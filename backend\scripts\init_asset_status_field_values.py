#!/usr/bin/env python3
"""
初始化资产状态字段值数据
将现有的硬编码资产状态选项迁移到字段值管理系统
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models.field_value import FieldValue
from app.models import Base
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 预设的资产状态选项
ASSET_STATUS_OPTIONS = [
    {
        'field_name': 'status',
        'field_value': '使用中',
        'description': '资产正在使用中'
    },
    {
        'field_name': 'status',
        'field_value': '闲置',
        'description': '资产处于闲置状态'
    },
    {
        'field_name': 'status',
        'field_value': '维修中',
        'description': '资产正在维修'
    },
    {
        'field_name': 'status',
        'field_value': '已报废',
        'description': '资产已报废处理'
    },
    {
        'field_name': 'status',
        'field_value': '已转移',
        'description': '资产已转移到其他部门或人员'
    },
    {
        'field_name': 'status',
        'field_value': '库存',
        'description': '资产在库存中'
    }
]

def init_asset_status_field_values():
    """初始化资产状态字段值"""
    db = SessionLocal()
    try:
        # 检查是否已存在status字段的数据
        existing_count = db.query(FieldValue).filter(
            FieldValue.field_name == 'status'
        ).count()
        
        if existing_count > 0:
            logger.info(f"已存在 {existing_count} 个status字段值，跳过初始化")
            return
        
        # 创建资产状态字段值
        logger.info("开始创建资产状态字段值...")
        
        for status_option in ASSET_STATUS_OPTIONS:
            field_value = FieldValue(
                field_name=status_option['field_name'],
                field_value=status_option['field_value'],
                description=status_option['description']
            )
            db.add(field_value)
            logger.info(f"创建字段值: {status_option['field_value']}")
        
        db.commit()
        logger.info("资产状态字段值初始化完成")
        
        # 验证创建结果
        created_count = db.query(FieldValue).filter(
            FieldValue.field_name == 'status'
        ).count()
        logger.info(f"成功创建 {created_count} 个资产状态字段值")
        
    except Exception as e:
        logger.error(f"初始化资产状态字段值失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    try:
        logger.info("开始初始化资产状态字段值...")
        init_asset_status_field_values()
        logger.info("初始化完成")
    except Exception as e:
        logger.error(f"脚本执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 