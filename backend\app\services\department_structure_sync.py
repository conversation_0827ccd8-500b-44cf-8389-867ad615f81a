"""
部门结构同步服务
从基础信息-人员信息独立同步部门结构到腾讯企业邮箱API
"""

import logging
import time
from typing import List, Dict, Optional, Set, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from collections import defaultdict

from app.schemas.department_sync import (
    DepartmentSyncRequest, DepartmentSyncResult, DepartmentSyncStats,
    DepartmentSyncOperation, DepartmentInfo, DepartmentHierarchy,
    DepartmentMapping, DepartmentSyncSource, DepartmentSyncMode
)
from app.services.email_api import TencentEmailAPIService
from app.crud.ecology_user import get_ecology_users
from app.crud.email import email_department
from app.schemas.email import EmailDepartmentCreate, EmailDepartmentUpdate
from app.models.ecology_user import EcologyUser

logger = logging.getLogger(__name__)


class DepartmentStructureSyncService:
    """部门结构同步服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        
        # 部门名称映射表，处理命名不一致问题
        self.department_name_mapping = {
            "重庆至信实业股份有限公司（重庆至信）": "重庆至信实业股份有限公司",
            "至信实业股份有限公司": "至信实业股份有限公司",
            "至信股份总部": "至信股份总部",
            # 可以根据需要添加更多映射
        }
        
        # 缓存已创建的部门，避免重复创建
        self.created_departments_cache: Dict[str, str] = {}  # {dept_name: tencent_dept_id}
        self.tencent_departments_cache: Dict[str, Dict] = {}  # {tencent_dept_id: dept_info}
        self.original_departments: List[DepartmentInfo] = []  # 存储原始部门信息
    
    def map_department_name(self, ecology_name: str) -> str:
        """将泛微数据中的部门名称映射到邮箱部门名称"""
        return self.department_name_mapping.get(ecology_name, ecology_name)

    def _get_mapped_department_name(self, dept_name: str, level: int, original_dept: Optional[DepartmentInfo]) -> str:
        """获取映射后的部门名称，对于非公司级别的部门考虑添加公司前缀"""
        # 先进行基本的名称映射
        mapped_name = self.map_department_name(dept_name)

        # 如果是公司级别（level=1），直接返回映射后的名称
        if level == 1:
            return mapped_name

        # 对于非公司级别的部门，检查是否需要添加公司前缀以避免重名
        if original_dept and original_dept.company_name:
            # 检查是否存在同名部门（来自不同公司）
            has_duplicate = False
            for dept in self.original_departments:
                if (dept.dept_name == dept_name and
                    dept.level == level and
                    dept.company_id != original_dept.company_id):
                    has_duplicate = True
                    break

            # 如果存在重名，添加公司前缀
            if has_duplicate:
                # 提取公司名称的主要部分，去掉括号内容
                company_name = original_dept.company_name
                if '（' in company_name:
                    company_prefix = company_name.split('（')[0]
                else:
                    company_prefix = company_name

                # 如果公司名太长，截取前10个字符
                if len(company_prefix) > 10:
                    company_prefix = company_prefix[:10]

                mapped_name = f"{company_prefix}-{mapped_name}"
                logger.info(f"检测到重名部门，添加公司前缀: {dept_name} -> {mapped_name}")

        return mapped_name
    
    async def sync_department_structure(self, request: DepartmentSyncRequest) -> DepartmentSyncResult:
        """同步部门结构"""
        start_time = time.time()
        result = DepartmentSyncResult(
            success=False,
            message="同步失败",
            stats=DepartmentSyncStats(),
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"开始部门结构同步: {request.model_dump()}")
            
            # 1. 获取需要同步的部门数据
            all_departments = await self._get_departments_from_ecology(request)
            if not all_departments:
                result.message = "未找到需要同步的部门数据"
                result.warnings.append("未找到需要同步的部门数据")
                return result

            # 保存原始部门信息供后续使用
            self.original_departments = all_departments

            # 记录无效部门统计（如果启用了部门有效性检查）
            if request.skip_empty_departments and hasattr(request, '_skipped_departments'):
                result.stats.invalid_departments = len(request._skipped_departments)
                if request._skipped_departments:
                    logger.info(f"跳过 {len(request._skipped_departments)} 个无效部门:")
                    for skipped in request._skipped_departments:
                        logger.info(f"  - {skipped['dept_name']} (ID: {skipped['dept_id']}): {skipped['reason']}")

            departments = all_departments
            result.stats.total_departments = len(departments)
            logger.info(f"获取到 {len(departments)} 个有效部门需要同步")
            
            # 2. 获取现有的腾讯企业邮箱部门信息（预览模式也需要加载以提供准确的预览结果）
            await self._load_tencent_departments()
            
            # 3. 构建部门层级关系
            hierarchy = self._build_department_hierarchy(departments)
            # 统计唯一公司数量
            unique_companies = set()
            for d in departments:
                if d.company_id:
                    unique_companies.add(d.company_id)
            result.stats.total_companies = len(unique_companies)
            
            # 4. 按层级顺序同步部门
            await self._sync_departments_by_hierarchy(hierarchy, request, result)
            
            # 5. 计算成功率和总结
            total_operations = result.stats.created_departments + result.stats.updated_departments + result.stats.skipped_departments + result.stats.failed_departments
            if total_operations > 0:
                result.stats.success_rate = (result.stats.created_departments + result.stats.updated_departments) / total_operations
            
            result.success = result.stats.failed_departments == 0

            # 构建结果消息
            base_msg = ""
            if request.dry_run:
                base_msg = f"预览完成！预计创建 {result.stats.created_departments} 个部门，更新 {result.stats.updated_departments} 个，跳过 {result.stats.skipped_departments} 个"
            else:
                base_msg = f"部门结构同步成功！创建 {result.stats.created_departments} 个，更新 {result.stats.updated_departments} 个，跳过 {result.stats.skipped_departments} 个"

            # 添加无效部门信息
            if result.stats.invalid_departments > 0:
                base_msg += f"，过滤无效部门 {result.stats.invalid_departments} 个"

            if result.success:
                result.message = base_msg
            else:
                result.message = f"部门结构同步完成，但有 {result.stats.failed_departments} 个部门失败。{base_msg}"
            
            logger.info(f"部门结构同步完成: {result.message}")
            
        except Exception as e:
            error_msg = f"部门结构同步异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            result.errors.append(error_msg)
            result.message = error_msg
        
        finally:
            end_time = time.time()
            result.duration = end_time - start_time
            result.end_time = datetime.now()
        
        return result
    
    async def _get_departments_from_ecology(self, request: DepartmentSyncRequest) -> List[DepartmentInfo]:
        """从泛微系统获取部门数据"""
        logger.info("正在从泛微系统获取部门数据...")

        try:
            # 获取所有用户数据（包含部门信息）
            ecology_users = get_ecology_users(self.db, skip=0, limit=100000)
            logger.info(f"从泛微系统获取到 {len(ecology_users)} 条用户记录")

            # 根据同步源筛选数据
            if request.source == DepartmentSyncSource.COMPANY and request.company_id:
                ecology_users = [u for u in ecology_users if u.company_id == request.company_id]
                logger.info(f"按公司ID {request.company_id} 筛选后剩余 {len(ecology_users)} 条记录")
            elif request.source == DepartmentSyncSource.DEPARTMENT and request.department_id:
                ecology_users = [u for u in ecology_users if u.dept_id == request.department_id or (u.dept_path and str(request.department_id) in u.dept_path.split(','))]
                logger.info(f"按部门ID {request.department_id} 筛选后剩余 {len(ecology_users)} 条记录")

            # 统计在职人员状态
            in_service_statuses = ['试用', '正式', '临时', '试用延期']
            active_users = [u for u in ecology_users if u.status in in_service_statuses]
            logger.info(f"在职人员数量: {len(active_users)}/{len(ecology_users)}")

            # 提取部门信息并去重
            departments_dict = {}
            dept_id_counter = 900000  # 从900000开始生成虚拟ID，避免与真实ID冲突

            # 统计每个部门的在职人员数量
            dept_active_user_count = {}

            for user in ecology_users:
                if not user.dept_hierarchy or not user.dept_name:
                    continue

                # 解析部门层级: "重庆至信实业股份有限公司（重庆至信） > 总经理办公室N0 > 设备设施部"
                hierarchy_parts = [part.strip() for part in user.dept_hierarchy.split(' > ') if part.strip()]
                if not hierarchy_parts:
                    continue

                # 统计在职人员数量（只统计叶子部门的直接人员）
                is_active_user = user.status in in_service_statuses
                leaf_dept_name = hierarchy_parts[-1]
                leaf_dept_key = f"{leaf_dept_name}#{len(hierarchy_parts)}#{user.company_id or 0}"

                if leaf_dept_key not in dept_active_user_count:
                    dept_active_user_count[leaf_dept_key] = 0
                if is_active_user:
                    dept_active_user_count[leaf_dept_key] += 1

                # 处理每一级部门
                for i, dept_name in enumerate(hierarchy_parts):
                    if not dept_name:
                        continue

                    # 生成唯一的部门标识（部门名称+层级+公司ID）
                    dept_key = f"{dept_name}#{i+1}#{user.company_id or 0}"

                    if dept_key not in departments_dict:
                        # 计算父部门ID（如果有的话）
                        parent_dept_id = None
                        if i > 0:
                            parent_dept_name = hierarchy_parts[i-1]
                            parent_key = f"{parent_dept_name}#{i}#{user.company_id or 0}"
                            if parent_key in departments_dict:
                                parent_dept_id = departments_dict[parent_key].dept_id

                        # 确定部门ID：叶子部门使用真实ID，中间部门生成虚拟ID
                        if i == len(hierarchy_parts) - 1:
                            # 叶子部门，使用真实部门ID
                            dept_id = user.dept_id
                        else:
                            # 中间部门，生成虚拟ID
                            dept_id_counter += 1
                            dept_id = dept_id_counter

                        dept_info = DepartmentInfo(
                            dept_id=dept_id,
                            dept_name=dept_name,
                            dept_hierarchy=user.dept_hierarchy,
                            company_id=user.company_id,
                            company_name=user.company_name,
                            parent_dept_id=parent_dept_id,
                            level=i + 1
                        )
                        departments_dict[dept_key] = dept_info

                        logger.debug(f"添加部门: {dept_name} (ID: {dept_id}, 层级: {i+1}, 父ID: {parent_dept_id})")

            departments = list(departments_dict.values())
            logger.info(f"提取到 {len(departments)} 个唯一部门")

            # 验证部门层级关系
            self._validate_department_hierarchy(departments)

            # 检查部门有效性并过滤无效部门
            if request.skip_empty_departments:
                valid_departments, skipped_departments = self._filter_valid_departments(departments, dept_active_user_count, request)
                logger.info(f"启用部门有效性检查，过滤后剩余 {len(valid_departments)} 个有效部门")
                # 将跳过的部门信息存储到request对象中，供后续统计使用
                request._skipped_departments = skipped_departments
                return valid_departments
            else:
                logger.info("未启用部门有效性检查，返回所有部门")
                request._skipped_departments = []
                return departments

        except Exception as e:
            logger.error(f"从泛微系统获取部门数据失败: {str(e)}", exc_info=True)
            raise

    def _filter_valid_departments(self, departments: List[DepartmentInfo], dept_active_user_count: Dict[str, int], request: DepartmentSyncRequest) -> tuple[List[DepartmentInfo], List[dict]]:
        """过滤有效的部门，排除空部门或无效部门"""
        logger.info("开始检查部门有效性...")

        valid_departments = []
        skipped_departments = []

        # 构建部门层级关系映射
        dept_children = {}
        for dept in departments:
            if dept.parent_dept_id:
                if dept.parent_dept_id not in dept_children:
                    dept_children[dept.parent_dept_id] = []
                dept_children[dept.parent_dept_id].append(dept.dept_id)

        def has_active_users_recursive(dept: DepartmentInfo) -> bool:
            """递归检查部门及其子部门是否有在职人员"""
            # 生成部门key用于查找在职人员数量
            dept_key = f"{dept.dept_name}#{dept.level}#{dept.company_id or 0}"

            # 检查当前部门是否有在职人员
            if dept_key in dept_active_user_count and dept_active_user_count[dept_key] > 0:
                return True

            # 检查子部门是否有在职人员
            if dept.dept_id in dept_children:
                for child_dept_id in dept_children[dept.dept_id]:
                    child_dept = next((d for d in departments if d.dept_id == child_dept_id), None)
                    if child_dept and has_active_users_recursive(child_dept):
                        return True

            return False

        for dept in departments:
            # 检查部门是否有效
            is_valid = True
            skip_reason = ""

            # 只有在启用跳过空部门选项时才进行检查
            if request.skip_empty_departments:
                # 检查是否有在职人员（包括子部门）
                if not has_active_users_recursive(dept):
                    is_valid = False
                    skip_reason = "部门及其子部门均无在职人员"

                # 检查部门名称是否包含弃用标识
                deprecated_keywords = ["已弃用", "停用", "废弃", "注销", "删除"]
                if any(keyword in dept.dept_name for keyword in deprecated_keywords):
                    is_valid = False
                    skip_reason = f"部门名称包含弃用标识: {dept.dept_name}"

            if is_valid:
                valid_departments.append(dept)
                logger.debug(f"部门有效: {dept.dept_name} (ID: {dept.dept_id})")
            else:
                skipped_departments.append({
                    'dept_id': dept.dept_id,
                    'dept_name': dept.dept_name,
                    'reason': skip_reason
                })
                logger.info(f"跳过无效部门: {dept.dept_name} (ID: {dept.dept_id}) - {skip_reason}")

        logger.info(f"部门有效性检查完成: 有效 {len(valid_departments)} 个，跳过 {len(skipped_departments)} 个")

        return valid_departments, skipped_departments

    def _validate_department_hierarchy(self, departments: List[DepartmentInfo]):
        """验证部门层级关系的完整性"""
        logger.info("验证部门层级关系...")

        dept_dict = {dept.dept_id: dept for dept in departments}
        orphaned_departments = []

        for dept in departments:
            if dept.parent_dept_id and dept.parent_dept_id not in dept_dict:
                orphaned_departments.append(dept)
                logger.warning(f"部门 {dept.dept_name} (ID: {dept.dept_id}) 的父部门 {dept.parent_dept_id} 不存在")

        if orphaned_departments:
            logger.warning(f"发现 {len(orphaned_departments)} 个孤立部门，将作为根部门处理")
            # 将孤立部门的父部门ID设为None，作为根部门处理
            for dept in orphaned_departments:
                dept.parent_dept_id = None
                dept.level = 1

        logger.info(f"部门层级验证完成，共 {len(departments)} 个部门")

    async def _load_tencent_departments(self):
        """加载腾讯企业邮箱现有部门信息"""
        try:
            logger.info("正在获取腾讯企业邮箱现有部门信息...")
            result = await self.api_service.get_department_list()
            
            if result.errcode == 0:
                departments = result.data.get("department", [])
                for dept in departments:
                    dept_id = str(dept.get("id", ""))
                    if dept_id:
                        self.tencent_departments_cache[dept_id] = dept
                logger.info(f"获取到 {len(departments)} 个腾讯企业邮箱部门")
            else:
                logger.warning(f"获取腾讯企业邮箱部门失败: {result.errmsg}")
                
        except Exception as e:
            logger.error(f"加载腾讯企业邮箱部门信息失败: {str(e)}")
    
    def _build_department_hierarchy(self, departments: List[DepartmentInfo]) -> List[DepartmentHierarchy]:
        """构建部门层级结构"""
        logger.info("正在构建部门层级结构...")
        
        # 按层级排序，确保父部门在子部门之前
        sorted_departments = sorted(departments, key=lambda d: (d.level, d.dept_name))
        
        hierarchy_list = []
        dept_mapping = {}  # {dept_id: DepartmentHierarchy}
        
        for dept in sorted_departments:
            hierarchy_item = DepartmentHierarchy(
                dept_id=dept.dept_id,
                dept_name=dept.dept_name,
                parent_id=dept.parent_dept_id,
                level=dept.level
            )
            
            dept_mapping[dept.dept_id] = hierarchy_item
            
            # 如果是根部门（公司级别），直接添加到结果列表
            if dept.level == 1:
                hierarchy_list.append(hierarchy_item)
            else:
                # 找到父部门并添加为子部门
                if dept.parent_dept_id and dept.parent_dept_id in dept_mapping:
                    parent = dept_mapping[dept.parent_dept_id]
                    parent.children.append(hierarchy_item)
                else:
                    # 如果找不到父部门，临时作为根部门
                    hierarchy_list.append(hierarchy_item)
        
        logger.info(f"构建完成，共 {len(hierarchy_list)} 个根部门")
        return hierarchy_list
    
    async def _sync_departments_by_hierarchy(
        self, 
        hierarchy: List[DepartmentHierarchy], 
        request: DepartmentSyncRequest, 
        result: DepartmentSyncResult
    ):
        """按层级顺序同步部门"""
        logger.info("开始按层级顺序同步部门...")
        
        # 为每个公司创建顶层部门
        company_departments = await self._create_company_departments(hierarchy, request)
        
        async def sync_department_recursive(dept: DepartmentHierarchy, parent_tencent_id: str = "1"):
            """递归同步部门"""
            operation = await self._sync_single_department(dept, parent_tencent_id, request)
            result.operations.append(operation)
            
            # 更新统计信息
            if operation.operation == "create":
                result.stats.created_departments += 1
            elif operation.operation == "update":
                result.stats.updated_departments += 1
            elif operation.operation == "skip":
                result.stats.skipped_departments += 1
            elif operation.operation == "error":
                result.stats.failed_departments += 1
                result.errors.append(operation.message)
            
            # 如果成功创建或找到了部门，继续同步子部门
            if operation.tencent_dept_id and operation.operation != "error":
                for child in dept.children:
                    await sync_department_recursive(child, operation.tencent_dept_id)
        
        # 同步所有根部门及其子部门
        for root_dept in hierarchy:
            # 获取公司对应的顶层部门ID，如果不存在则使用根部门ID
            company_id = self._get_company_id_for_department(root_dept)
            parent_tencent_id = company_departments.get(company_id, "1")
            await sync_department_recursive(root_dept, parent_tencent_id)
    
    async def _create_company_departments(
        self, 
        hierarchy: List[DepartmentHierarchy], 
        request: DepartmentSyncRequest
    ) -> Dict[Optional[int], str]:
        """为每个公司创建顶层部门"""
        logger.info("开始创建公司顶层部门...")
        company_departments = {}  # {company_id: tencent_dept_id}
        
        # 获取所有唯一的公司ID和公司名称
        companies = {}
        for root_dept in hierarchy:
            company_id = self._get_company_id_for_department(root_dept)
            company_name = self._get_company_name_for_department(root_dept)
            if company_id and company_name:
                companies[company_id] = company_name
        
        # 为每个公司创建顶层部门
        for company_id, company_name in companies.items():
            # 映射公司名称
            mapped_company_name = self.map_department_name(company_name)
            
            # 查找或创建公司部门
            company_dept_id = await self._find_or_create_company_department(
                mapped_company_name, request
            )
            
            if company_dept_id:
                company_departments[company_id] = company_dept_id
                logger.info(f"公司 {company_name} 的顶层部门ID: {company_dept_id}")
        
        return company_departments
    
    async def _find_or_create_company_department(
        self, 
        company_name: str, 
        request: DepartmentSyncRequest
    ) -> Optional[str]:
        """查找或创建公司部门"""
        # 检查缓存
        cache_key = f"{company_name}#1"  # 公司部门的父部门始终是根部门
        if cache_key in self.created_departments_cache:
            return self.created_departments_cache[cache_key]
        
        # 搜索部门是否已存在（干运行模式下跳过真实API调用）
        if request.dry_run:
            # 干运行模式下，只检查本地缓存，不调用API
            existing_dept_id = self._find_existing_department_in_cache(company_name, "1")
        else:
            existing_dept_id = await self._find_existing_department(company_name, "1")
        
        if existing_dept_id:
            # 部门已存在
            self.created_departments_cache[cache_key] = existing_dept_id
            return existing_dept_id
        else:
            # 创建新部门
            if request.dry_run:
                return "DRY_RUN"
            
            new_dept_id = await self._create_department(company_name, "1")
            if new_dept_id:
                self.created_departments_cache[cache_key] = new_dept_id
                
                # 创建本地数据库记录
                await self._create_local_department_record(new_dept_id, company_name, "1")
                
                logger.info(f"创建公司部门成功: {company_name} -> {new_dept_id}")
                return new_dept_id
            else:
                logger.error(f"创建公司部门失败: {company_name}")
                return None
    
    def _get_company_id_for_department(self, dept: DepartmentHierarchy) -> Optional[int]:
        """获取部门对应的公司ID"""
        # 从原始部门信息中获取公司ID
        for d in self.original_departments:
            if d.dept_id == dept.dept_id:
                return d.company_id
        return None
    
    def _get_company_name_for_department(self, dept: DepartmentHierarchy) -> Optional[str]:
        """获取部门对应的公司名称"""
        # 从原始部门信息中获取公司名称
        for d in self.original_departments:
            if d.dept_id == dept.dept_id:
                return d.company_name
        return None
    
    async def _sync_single_department(
        self,
        dept: DepartmentHierarchy,
        parent_tencent_id: str,
        request: DepartmentSyncRequest
    ) -> DepartmentSyncOperation:
        """同步单个部门"""
        # 从原始部门信息中获取完整信息
        original_dept = None
        for d in self.original_departments:
            if d.dept_id == dept.dept_id:
                original_dept = d
                break

        dept_info = DepartmentInfo(
            dept_id=dept.dept_id,
            dept_name=dept.dept_name,
            level=dept.level,
            company_id=original_dept.company_id if original_dept else None,
            company_name=original_dept.company_name if original_dept else None,
            dept_hierarchy=original_dept.dept_hierarchy if original_dept else None
        )

        # 映射部门名称，对于非公司级别的部门，考虑添加公司前缀以避免重名
        mapped_name = self._get_mapped_department_name(dept.dept_name, dept.level, original_dept)
        
        try:
            # 检查缓存
            cache_key = f"{mapped_name}#{parent_tencent_id}"
            if cache_key in self.created_departments_cache:
                return DepartmentSyncOperation(
                    dept_info=dept_info,
                    operation="skip",
                    tencent_dept_id=self.created_departments_cache[cache_key],
                    message=f"部门已存在（缓存命中）: {mapped_name}",
                    parent_tencent_id=parent_tencent_id
                )
            
            # 搜索部门是否已存在（干运行模式下跳过真实API调用）
            if request.dry_run:
                # 干运行模式下，只检查本地缓存，不调用API
                existing_dept_id = self._find_existing_department_in_cache(mapped_name, parent_tencent_id)
            else:
                existing_dept_id = await self._find_existing_department(mapped_name, parent_tencent_id)
            
            if existing_dept_id:
                # 部门已存在
                self.created_departments_cache[cache_key] = existing_dept_id
                
                if request.mode == DepartmentSyncMode.CREATE_UPDATE and request.overwrite_existing:
                    # 更新现有部门
                    if not request.dry_run:
                        update_result = await self._update_department(existing_dept_id, mapped_name, parent_tencent_id)
                        if update_result:
                            return DepartmentSyncOperation(
                                dept_info=dept_info,
                                operation="update",
                                tencent_dept_id=existing_dept_id,
                                message=f"更新部门成功: {mapped_name}",
                                parent_tencent_id=parent_tencent_id
                            )
                    else:
                        return DepartmentSyncOperation(
                            dept_info=dept_info,
                            operation="update",
                            tencent_dept_id="DRY_RUN",
                            message=f"[试运行] 将更新部门: {mapped_name}",
                            parent_tencent_id=parent_tencent_id
                        )
                
                return DepartmentSyncOperation(
                    dept_info=dept_info,
                    operation="skip",
                    tencent_dept_id=existing_dept_id if not request.dry_run else "DRY_RUN",
                    message=f"部门已存在: {mapped_name}",
                    parent_tencent_id=parent_tencent_id
                )
            
            else:
                # 创建新部门
                if request.dry_run:
                    return DepartmentSyncOperation(
                        dept_info=dept_info,
                        operation="create",
                        tencent_dept_id="DRY_RUN",
                        message=f"[试运行] 将创建部门: {mapped_name}",
                        parent_tencent_id=parent_tencent_id
                    )
                
                new_dept_id = await self._create_department(mapped_name, parent_tencent_id)
                if new_dept_id:
                    self.created_departments_cache[cache_key] = new_dept_id
                    
                    # 创建本地数据库记录
                    await self._create_local_department_record(new_dept_id, mapped_name, parent_tencent_id)
                    
                    return DepartmentSyncOperation(
                        dept_info=dept_info,
                        operation="create",
                        tencent_dept_id=new_dept_id,
                        message=f"创建部门成功: {mapped_name}",
                        parent_tencent_id=parent_tencent_id
                    )
                else:
                    return DepartmentSyncOperation(
                        dept_info=dept_info,
                        operation="error",
                        message=f"创建部门失败: {mapped_name}",
                        parent_tencent_id=parent_tencent_id
                    )
        
        except Exception as e:
            error_msg = f"同步部门异常 {mapped_name}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return DepartmentSyncOperation(
                dept_info=dept_info,
                operation="error",
                message=error_msg,
                parent_tencent_id=parent_tencent_id
            )
    
    def _find_existing_department_in_cache(self, dept_name: str, parent_id: str) -> Optional[str]:
        """在缓存中查找已存在的部门（用于干运行模式）"""
        try:
            # 只在缓存中查找
            for dept_id, dept_info in self.tencent_departments_cache.items():
                if (dept_info.get("name") == dept_name and 
                    str(dept_info.get("parentid", "1")) == parent_id):
                    return dept_id
            return None
        except Exception as e:
            logger.error(f"在缓存中查找部门失败 {dept_name}: {str(e)}")
            return None
    
    async def _find_existing_department(self, dept_name: str, parent_id: str) -> Optional[str]:
        """查找已存在的部门"""
        try:
            # 先在缓存中查找
            cached_result = self._find_existing_department_in_cache(dept_name, parent_id)
            if cached_result:
                return cached_result
            
            # 通过API搜索
            search_result = await self.api_service.search_department(dept_name, fuzzy=False)
            if search_result.errcode == 0 and search_result.data.get("department"):
                departments = search_result.data["department"]
                for dept in departments:
                    if (dept.get("name") == dept_name and 
                        str(dept.get("parentid", "1")) == parent_id):
                        return str(dept.get("id", ""))
            
            return None
            
        except Exception as e:
            logger.error(f"查找部门失败 {dept_name}: {str(e)}")
            return None
    
    async def _create_department(self, dept_name: str, parent_id: str) -> Optional[str]:
        """创建部门"""
        try:
            api_data = {
                "name": dept_name,
                "parentid": int(parent_id) if parent_id.isdigit() else 1,
                "order": 1
            }
            
            logger.info(f"正在创建部门: {dept_name}, 父部门ID: {parent_id}")
            result = await self.api_service.create_department(api_data)
            
            if result.errcode == 0:
                new_dept_id = str(result.data.get("id", ""))
                if new_dept_id:
                    logger.info(f"创建部门成功: {dept_name} -> {new_dept_id}")
                    return new_dept_id
                else:
                    # 如果响应中没有ID，尝试搜索获取
                    existing_id = await self._find_existing_department(dept_name, parent_id)
                    if existing_id:
                        logger.info(f"通过搜索找到新创建的部门: {dept_name} -> {existing_id}")
                        return existing_id
            else:
                logger.error(f"创建部门失败: {dept_name}, 错误: {result.errmsg} (错误码: {result.errcode})")
            
            return None
            
        except Exception as e:
            logger.error(f"创建部门异常 {dept_name}: {str(e)}", exc_info=True)
            return None
    
    async def _update_department(self, dept_id: str, dept_name: str, parent_id: str) -> bool:
        """更新部门"""
        try:
            api_data = {
                "id": int(dept_id),
                "name": dept_name,
                "parentid": int(parent_id) if parent_id.isdigit() else 1
            }
            
            result = await self.api_service.update_department(api_data)
            if result.errcode == 0:
                logger.info(f"更新部门成功: {dept_name}")
                return True
            else:
                logger.error(f"更新部门失败: {dept_name}, 错误: {result.errmsg}")
                return False
                
        except Exception as e:
            logger.error(f"更新部门异常 {dept_name}: {str(e)}")
            return False
    
    async def _create_local_department_record(self, dept_id: str, dept_name: str, parent_id: str):
        """创建本地部门记录"""
        try:
            # 检查本地是否已存在
            existing = email_department.get_by_dept_id(self.db, dept_id=dept_id)
            if existing:
                return
            
            # 创建本地记录
            dept_create = EmailDepartmentCreate(
                dept_id=dept_id,
                name=dept_name,
                parent_id=None if parent_id == "1" else parent_id,
                order=1
            )
            email_department.create(self.db, obj_in=dept_create)
            logger.debug(f"创建本地部门记录: {dept_name} (ID: {dept_id})")
            
        except Exception as e:
            logger.error(f"创建本地部门记录失败 {dept_name}: {str(e)}")
    
    async def preview_sync(self, request: DepartmentSyncRequest) -> DepartmentSyncResult:
        """预览同步结果（试运行模式）"""
        request.dry_run = True
        return await self.sync_department_structure(request) 