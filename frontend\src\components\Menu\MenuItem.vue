<template>
  <template v-if="!menuItem.hidden && hasMenuPermission(menuItem)">
    <el-sub-menu 
      v-if="menuItem.children?.length" 
      :index="menuItem.path"
    >
      <template #title>
        <el-icon v-if="menuItem.icon">
          <component :is="menuItem.icon" />
        </el-icon>
        <span>{{ menuItem.title }}</span>
      </template>
      <MenuItem 
        v-for="child in filterChildrenByPermission(menuItem.children)"
        :key="child.path"
        :menu-item="child"
      />
    </el-sub-menu>

    <el-menu-item 
      v-else 
      :index="menuItem.path"
    >
      <el-icon v-if="menuItem.icon">
        <component :is="menuItem.icon" />
      </el-icon>
      <template #title>{{ menuItem.title }}</template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import type { MenuItem } from '@/router/menus'
import { hasAnyPermission } from '@/utils/permission'
import { useUserStore } from '@/stores/user'

defineOptions({
  name: 'MenuItem'
})

defineProps<{
  menuItem: MenuItem
}>()

const userStore = useUserStore()

/**
 * 检查菜单是否有权限显示
 */
function hasMenuPermission(menu: MenuItem): boolean {
  // 没有权限要求的菜单，或超级管理员，直接显示
  if (!menu.permissions || !menu.permissions.length || userStore.isSuperUser) {
    return true
  }
  // 检查是否有菜单要求的任意一个权限
  return hasAnyPermission(menu.permissions)
}

/**
 * 过滤子菜单，只保留有权限的菜单项
 */
function filterChildrenByPermission(children: MenuItem[]): MenuItem[] {
  return children.filter(child => hasMenuPermission(child))
}
</script> 