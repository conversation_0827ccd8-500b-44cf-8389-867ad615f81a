// =============================================================================
// 移动端主题样式系统
// 统一Element Plus和Vant主题变量，只支持浅色主题
// =============================================================================

// 基础CSS变量定义
:root {
  // 主色调
  --theme-primary: #409eff;
  --theme-success: #67c23a;
  --theme-warning: #e6a23c;
  --theme-danger: #f56c6c;
  --theme-info: #909399;
  
  // 中性色
  --theme-white: #ffffff;
  --theme-black: #000000;
  
  // 文字颜色
  --theme-text-primary: #303133;
  --theme-text-regular: #606266;
  --theme-text-secondary: #909399;
  --theme-text-placeholder: #a8abb2;
  --theme-text-disabled: #c0c4cc;
  
  // 背景色
  --theme-bg-page: #f2f3f5;
  --theme-bg-card: #ffffff;
  --theme-bg-overlay: rgba(0, 0, 0, 0.7);
  
  // 边框颜色
  --theme-border-light: #e4e7ed;
  --theme-border-base: #dcdfe6;
  --theme-border-dark: #d4d7de;
  --theme-border-darker: #cdd0d6;
  
  // 圆角
  --theme-radius-small: 2px;
  --theme-radius-base: 4px;
  --theme-radius-large: 8px;
  --theme-radius-round: 50%;
  
  // 间距
  --theme-space-xs: 4px;
  --theme-space-sm: 8px;
  --theme-space-md: 16px;
  --theme-space-lg: 24px;
  --theme-space-xl: 32px;
  
  // 字体大小
  --theme-font-xs: 10px;
  --theme-font-sm: 12px;
  --theme-font-md: 14px;
  --theme-font-lg: 16px;
  --theme-font-xl: 18px;
  
  // 阴影
  --theme-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --theme-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --theme-shadow-dark: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 12px rgba(0, 0, 0, 0.05);
  
  // 动画时长
  --theme-duration-fast: 0.2s;
  --theme-duration-base: 0.3s;
  --theme-duration-slow: 0.5s;
  
  // z-index层级
  --theme-z-normal: 1;
  --theme-z-fixed: 10;
  --theme-z-overlay: 100;
  --theme-z-modal: 1000;
  --theme-z-toast: 2000;
}

// =============================================================================
// Element Plus主题变量映射
// =============================================================================
:root {
  // Element Plus 主色调
  --el-color-primary: var(--theme-primary);
  --el-color-success: var(--theme-success);
  --el-color-warning: var(--theme-warning);
  --el-color-danger: var(--theme-danger);
  --el-color-info: var(--theme-info);
  
  // Element Plus 中性色
  --el-color-white: var(--theme-white);
  --el-color-black: var(--theme-black);
  
  // Element Plus 文字色
  --el-text-color-primary: var(--theme-text-primary);
  --el-text-color-regular: var(--theme-text-regular);
  --el-text-color-secondary: var(--theme-text-secondary);
  --el-text-color-placeholder: var(--theme-text-placeholder);
  --el-text-color-disabled: var(--theme-text-disabled);
  
  // Element Plus 背景色
  --el-bg-color: var(--theme-bg-card);
  --el-bg-color-page: var(--theme-bg-page);
  
  // Element Plus 边框色
  --el-border-color: var(--theme-border-base);
  --el-border-color-light: var(--theme-border-light);
  --el-border-color-dark: var(--theme-border-dark);
  --el-border-color-darker: var(--theme-border-darker);
  
  // Element Plus 圆角
  --el-border-radius-small: var(--theme-radius-small);
  --el-border-radius-base: var(--theme-radius-base);
  --el-border-radius-large: var(--theme-radius-large);
  --el-border-radius-round: var(--theme-radius-round);
}

// =============================================================================
// Vant主题变量映射
// =============================================================================
:root {
  // Vant 主色调
  --van-primary-color: var(--theme-primary);
  --van-success-color: var(--theme-success);
  --van-warning-color: var(--theme-warning);
  --van-danger-color: var(--theme-danger);
  
  // Vant 中性色
  --van-white: var(--theme-white);
  --van-black: var(--theme-black);
  
  // Vant 文字色
  --van-text-color: var(--theme-text-primary);
  --van-text-color-2: var(--theme-text-regular);
  --van-text-color-3: var(--theme-text-secondary);
  
  // Vant 背景色
  --van-background: var(--theme-bg-page);
  --van-background-2: var(--theme-bg-card);
  
  // Vant 边框色
  --van-border-color: var(--theme-border-base);
  
  // Vant 圆角
  --van-radius-sm: var(--theme-radius-small);
  --van-radius-md: var(--theme-radius-base);
  --van-radius-lg: var(--theme-radius-large);
  
  // Vant 字体大小
  --van-font-size-xs: var(--theme-font-xs);
  --van-font-size-sm: var(--theme-font-sm);
  --van-font-size-md: var(--theme-font-md);
  --van-font-size-lg: var(--theme-font-lg);
  --van-font-size-xl: var(--theme-font-xl);
  
  // Vant 间距
  --van-padding-xs: var(--theme-space-xs);
  --van-padding-sm: var(--theme-space-sm);
  --van-padding-md: var(--theme-space-md);
  --van-padding-lg: var(--theme-space-lg);
  --van-padding-xl: var(--theme-space-xl);
}

// =============================================================================
// 紧凑模式
// =============================================================================
.compact-mode {
  // 减小间距
  --theme-space-xs: 2px;
  --theme-space-sm: 4px;
  --theme-space-md: 8px;
  --theme-space-lg: 12px;
  --theme-space-xl: 16px;
  
  // 减小字体大小
  --theme-font-xs: 9px;
  --theme-font-sm: 11px;
  --theme-font-md: 13px;
  --theme-font-lg: 15px;
  --theme-font-xl: 17px;
  
  // Element Plus 组件高度调整
  --el-component-size-small: 24px;
  --el-component-size: 28px;
  --el-component-size-large: 32px;
  
  // Vant 组件高度调整
  --van-cell-vertical-padding: 6px;
  --van-button-default-height: 36px;
  --van-nav-bar-height: 44px;
}

// =============================================================================
// 移动端特定样式
// =============================================================================

// 安全区域适配
.mobile-safe-area {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

// 1px边框解决方案
.mobile-hairline {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    border: 1px solid var(--theme-border-base);
    transform: scale(0.5);
    transform-origin: 0 0;
    pointer-events: none;
  }
}

// 触摸反馈
.mobile-touch-feedback {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  
  &:active {
    background-color: rgba(0, 0, 0, 0.05);
    transition: background-color var(--theme-duration-fast);
  }
}

// 滚动优化
.mobile-scroll-optimize {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// 移动端弹窗优化样式
.mobile-popup-optimize {
  // 确保弹窗内容区域可以正确滚动
  .van-popup__content {
    display: flex;
    flex-direction: column;
    max-height: 100%;
  }
  
  // 弹窗底部按钮区域确保可见
  .mobile-popup-footer {
    flex-shrink: 0;
    border-top: 1px solid var(--theme-border-light);
    background: var(--theme-bg-card);
  }
  
  // 弹窗主内容区域允许滚动
  .popup-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

// 移动端弹窗底部按钮修复 - 激进方案
.van-popup {
  // 在移动端确保弹窗不会超出可视区域
  @media screen and (max-width: 768px) {
    &[style*="height"] {
      // 如果已设置高度，确保不超过安全高度
      max-height: calc(100vh - 120px) !important;
      max-height: calc(100svh - 60px) !important;
    }
    
    // 底部弹窗特殊处理
    &[position="bottom"] {
      .van-popup__content {
        max-height: inherit;
        display: flex;
        flex-direction: column;
      }
      
      // 确保底部按钮区域始终可见
      .mobile-popup-footer {
        position: sticky;
        bottom: 0;
        z-index: 10;
        margin-top: auto;
      }
    }
  }
}

// 容器查询支持
@supports (container-type: inline-size) {
  .mobile-container {
    container-type: inline-size;
  }
  
  @container (min-width: 320px) {
    .mobile-responsive {
      --theme-space-md: 12px;
    }
  }
  
  @container (min-width: 375px) {
    .mobile-responsive {
      --theme-space-md: 16px;
    }
  }
  
  @container (min-width: 414px) {
    .mobile-responsive {
      --theme-space-md: 20px;
    }
  }
}

// 降级方案（不支持容器查询时使用媒体查询）
@supports not (container-type: inline-size) {
  @media (min-width: 320px) {
    .mobile-responsive {
      --theme-space-md: 12px;
    }
  }
  
  @media (min-width: 375px) {
    .mobile-responsive {
      --theme-space-md: 16px;
    }
  }
  
  @media (min-width: 414px) {
    .mobile-responsive {
      --theme-space-md: 20px;
    }
  }
}

// =============================================================================
// 主题切换动画
// =============================================================================
html {
  transition: color var(--theme-duration-base),
              background-color var(--theme-duration-base);
}

.theme-transition {
  transition: all var(--theme-duration-base) ease-in-out;
}

// =============================================================================
// 工具类
// =============================================================================

// 文字颜色
.text-primary { color: var(--theme-text-primary); }
.text-regular { color: var(--theme-text-regular); }
.text-secondary { color: var(--theme-text-secondary); }
.text-placeholder { color: var(--theme-text-placeholder); }
.text-disabled { color: var(--theme-text-disabled); }

// 背景色
.bg-page { background-color: var(--theme-bg-page); }
.bg-card { background-color: var(--theme-bg-card); }

// 边框
.border-light { border-color: var(--theme-border-light); }
.border-base { border-color: var(--theme-border-base); }
.border-dark { border-color: var(--theme-border-dark); }

// 圆角
.radius-small { border-radius: var(--theme-radius-small); }
.radius-base { border-radius: var(--theme-radius-base); }
.radius-large { border-radius: var(--theme-radius-large); }

// 间距
.space-xs { margin: var(--theme-space-xs); }
.space-sm { margin: var(--theme-space-sm); }
.space-md { margin: var(--theme-space-md); }
.space-lg { margin: var(--theme-space-lg); }
.space-xl { margin: var(--theme-space-xl); } 