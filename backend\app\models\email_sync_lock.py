from sqlalchemy import Column, String, Bo<PERSON>an, DateTime, Integer
from sqlalchemy.sql import func
from datetime import datetime, timedelta, timezone
from app.database import Base

class EmailSyncLock(Base):
    """邮箱同步锁表，用于防止邮箱相关操作的并发冲突"""
    __tablename__ = "email_sync_locks"

    # 锁的名称作为主键
    lock_name = Column(String, primary_key=True, index=True)
    # 锁定状态
    is_locked = Column(Boolean, default=False)
    # 锁定时间
    locked_at = Column(DateTime(timezone=True), nullable=True)
    # 最后更新时间
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    # 锁定者（可以是用户名或'system'）
    locked_by = Column(String, nullable=True)
    # 锁定操作类型
    operation_type = Column(String, nullable=True, comment="操作类型：cache_sync/extid_completion/personnel_sync/consistency_check")
    # 锁超时时间（秒），默认30分钟
    timeout_seconds = Column(Integer, default=1800)
    
    def is_expired(self) -> bool:
        """检查锁是否已过期"""
        if not self.is_locked or not self.locked_at:
            return False
        
        expired_time = self.locked_at + timedelta(seconds=self.timeout_seconds)
        return datetime.now(timezone.utc) > expired_time
    
    def time_remaining(self) -> int:
        """获取锁剩余时间（秒），如果已过期返回0"""
        if not self.is_locked or not self.locked_at:
            return 0
        
        expired_time = self.locked_at + timedelta(seconds=self.timeout_seconds)
        remaining = expired_time - datetime.now(timezone.utc)
        return max(0, int(remaining.total_seconds()))
    
    def __repr__(self):
        return f"<EmailSyncLock(lock_name='{self.lock_name}', is_locked={self.is_locked}, locked_by='{self.locked_by}', operation_type='{self.operation_type}', expires_in={self.time_remaining()}s)>" 