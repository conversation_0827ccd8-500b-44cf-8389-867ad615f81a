from typing import Optional, List
from pydantic import BaseModel

# 角色基类
class RoleBase(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    is_default: int = 0

# 创建角色
class RoleCreate(RoleBase):
    pass

# 更新角色
class RoleUpdate(RoleBase):
    code: Optional[str] = None
    name: Optional[str] = None
    is_default: Optional[int] = None

# 数据库中的角色基类
class RoleInDBBase(RoleBase):
    id: int

    class Config:
        from_attributes = True

# API返回的简化角色信息
class RoleBasic(RoleInDBBase):
    pass

# API返回的完整角色信息（包含权限）
class Role(RoleInDBBase):
    permissions: List["PermissionBasic"] = []

# 角色分配请求
class RoleAssign(BaseModel):
    user_id: int
    role_ids: List[int]

# 权限分配请求
class PermissionAssign(BaseModel):
    role_id: int
    permission_ids: List[int]

# 用于避免循环引用的简化权限模型
class PermissionBasic(BaseModel):
    id: int
    code: str
    name: str
    module: str

    class Config:
        from_attributes = True

from app.schemas.permission import Permission

# 包含完整权限信息的角色
class RoleWithPermissions(Role):
    permissions: List["Permission"] = []

    class Config:
        from_attributes = True

RoleWithPermissions.model_rebuild() 