"""merge_multiple_heads

Revision ID: 7bdbca0b6716
Revises: add_ldap_ip_range_support, bb3657ac3497
Create Date: 2025-06-24 09:27:52.948242

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7bdbca0b6716'
down_revision: Union[str, None] = ('add_ldap_ip_range_support', 'bb3657ac3497')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
