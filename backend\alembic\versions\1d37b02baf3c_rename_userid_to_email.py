"""rename_userid_to_email

Revision ID: 1d37b02baf3c
Revises: 5eb508c112c1
Create Date: 2025-05-24 12:52:53.184593

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d37b02baf3c'
down_revision: Union[str, None] = '5eb508c112c1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
