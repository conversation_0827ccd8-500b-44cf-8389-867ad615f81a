from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from ..database import Base

class ADSyncLog(Base):
    """AD同步日志表"""
    __tablename__ = "ad_sync_logs"

    id = Column(Integer, primary_key=True, index=True)
    operator = Column(String(100), comment="操作员用户名")
    source_type = Column(String(20), comment="同步来源类型：all/company/department")
    source_id = Column(Integer, nullable=True, comment="同步来源ID（公司ID或部门ID）")
    sync_time = Column(DateTime, nullable=False, comment="同步时间")
    total_users = Column(Integer, default=0, comment="总用户数")
    created_users = Column(Integer, default=0, comment="新创建用户数")
    skipped_users = Column(Integer, default=0, comment="跳过用户数")
    disabled_users = Column(Integer, default=0, comment="禁用账号数")
    moved_users = Column(Integer, default=0, comment="移动到新部门的用户数")
    updated_users = Column(Integer, default=0, comment="更新信息的用户数")
    created_ous = Column(JSON, nullable=True, comment="创建的组织单位详细信息（JSON格式）")
    renamed_ous = Column(JSON, nullable=True, comment="重命名的组织单位详细信息（JSON格式）")
    created_groups = Column(Integer, default=0, comment="创建的部门安全组数")
    updated_groups = Column(Integer, default=0, comment="更新的部门安全组数")
    added_to_groups = Column(Integer, default=0, comment="添加到安全组的用户数")
    removed_from_groups = Column(Integer, default=0, comment="从安全组移除的用户数")
    errors = Column(Text, nullable=True, comment="错误信息（JSON格式）")
    # 使用Text类型确保可以存储大量数据，包括所有新创建用户的密码信息
    details = Column(Text, nullable=True, comment="详细信息（JSON格式，包含新创建用户信息及密码）") 