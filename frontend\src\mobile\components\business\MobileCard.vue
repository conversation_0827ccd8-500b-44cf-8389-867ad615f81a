<template>
  <div class="mobile-card" :class="cardClasses" @click="handleClick">
    <!-- 卡片头部 -->
    <div v-if="$slots.header || title || extra" class="card-header">
      <slot name="header">
        <div class="header-title">
          <van-icon v-if="icon" :name="icon" :color="iconColor" />
          <span v-if="title">{{ title }}</span>
        </div>
        <div v-if="extra || $slots.extra" class="header-extra">
          <slot name="extra">{{ extra }}</slot>
        </div>
      </slot>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <slot>
        <!-- 默认内容布局 -->
        <div v-if="items.length > 0" class="content-list">
          <div
            v-for="(item, index) in items"
            :key="index"
            class="content-item"
            :class="{ 'is-link': item.isLink }"
            @click.stop="handleItemClick(item, index)"
          >
            <div class="item-left">
              <van-icon
                v-if="item.icon"
                :name="item.icon"
                :color="item.iconColor"
                class="item-icon"
              />
              <div class="item-content">
                <div class="item-title">{{ item.title }}</div>
                <div v-if="item.description" class="item-description">
                  {{ item.description }}
                </div>
              </div>
            </div>
            <div class="item-right">
              <span v-if="item.value" class="item-value" :class="item.valueClass">
                {{ item.value }}
              </span>
              <van-icon
                v-if="item.isLink"
                name="arrow"
                class="item-arrow"
              />
            </div>
          </div>
        </div>
      </slot>
    </div>
    
    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </div>
    
    <!-- 加载状态覆盖 -->
    <div v-if="loading" class="card-loading">
      <van-loading size="20" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface CardItem {
  title: string
  description?: string
  value?: string | number
  icon?: string
  iconColor?: string
  isLink?: boolean
  valueClass?: string
  onClick?: () => void
  [key: string]: any
}

interface Props {
  // 卡片标题
  title?: string
  // 卡片图标
  icon?: string
  // 图标颜色
  iconColor?: string
  // 额外内容
  extra?: string
  // 是否可点击
  clickable?: boolean
  // 是否显示边框
  border?: boolean
  // 是否显示阴影
  shadow?: boolean
  // 圆角大小
  radius?: 'small' | 'medium' | 'large'
  // 内边距大小
  padding?: 'small' | 'medium' | 'large'
  // 是否加载中
  loading?: boolean
  // 卡片项目数据
  items?: CardItem[]
  // 自定义类名
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  clickable: false,
  border: true,
  shadow: false,
  radius: 'medium',
  padding: 'medium',
  loading: false,
  items: () => []
})

const emit = defineEmits<{
  'click': [event: MouseEvent]
  'item-click': [item: CardItem, index: number]
}>()

// 卡片样式类
const cardClasses = computed(() => [
  {
    'is-clickable': props.clickable,
    'has-border': props.border,
    'has-shadow': props.shadow,
    'is-loading': props.loading,
    [`radius-${props.radius}`]: true,
    [`padding-${props.padding}`]: true
  },
  props.customClass
])

// 点击事件处理
const handleClick = (event: MouseEvent) => {
  if (props.clickable && !props.loading) {
    emit('click', event)
  }
}

// 项目点击事件处理
const handleItemClick = (item: CardItem, index: number) => {
  if (item.onClick) {
    item.onClick()
  }
  emit('item-click', item, index)
}
</script>

<style lang="scss" scoped>
.mobile-card {
  position: relative;
  background: var(--van-background);
  overflow: hidden;
  
  // 圆角样式
  &.radius-small {
    border-radius: var(--van-radius-sm);
  }
  
  &.radius-medium {
    border-radius: var(--van-radius-md);
  }
  
  &.radius-large {
    border-radius: var(--van-radius-lg);
  }
  
  // 内边距样式
  &.padding-small {
    --card-padding: var(--van-padding-sm);
  }
  
  &.padding-medium {
    --card-padding: var(--van-padding-md);
  }
  
  &.padding-large {
    --card-padding: var(--van-padding-lg);
  }
  
  // 边框样式
  &.has-border {
    border: 1px solid var(--van-border-color);
  }
  
  // 阴影样式
  &.has-shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // 可点击样式
  &.is-clickable {
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--van-background-2);
    }
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  // 加载状态
  &.is-loading {
    pointer-events: none;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--card-padding);
  border-bottom: 1px solid var(--van-border-color);
  
  .header-title {
    display: flex;
    align-items: center;
    gap: var(--van-padding-xs);
    font-size: var(--van-font-size-lg);
    font-weight: 500;
    color: var(--van-text-color);
  }
  
  .header-extra {
    color: var(--van-text-color-2);
    font-size: var(--van-font-size-sm);
  }
}

.card-content {
  padding: var(--card-padding);
}

.card-footer {
  padding: var(--card-padding);
  border-top: 1px solid var(--van-border-color);
  background: var(--van-background-2);
}

.content-list {
  .content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--van-padding-sm) 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid var(--van-border-color);
    }
    
    &.is-link {
      cursor: pointer;
      
      &:hover {
        background: var(--van-background-2);
      }
    }
  }
  
  .item-left {
    display: flex;
    align-items: center;
    flex: 1;
    gap: var(--van-padding-sm);
  }
  
  .item-icon {
    font-size: 18px;
  }
  
  .item-content {
    flex: 1;
  }
  
  .item-title {
    font-size: var(--van-font-size-md);
    color: var(--van-text-color);
    line-height: 1.4;
  }
  
  .item-description {
    font-size: var(--van-font-size-sm);
    color: var(--van-text-color-2);
    margin-top: 2px;
    line-height: 1.4;
  }
  
  .item-right {
    display: flex;
    align-items: center;
    gap: var(--van-padding-xs);
  }
  
  .item-value {
    font-size: var(--van-font-size-sm);
    color: var(--van-text-color-2);
    
    &.success {
      color: var(--van-success-color);
    }
    
    &.warning {
      color: var(--van-warning-color);
    }
    
    &.danger {
      color: var(--van-danger-color);
    }
    
    &.primary {
      color: var(--van-primary-color);
    }
  }
  
  .item-arrow {
    font-size: 12px;
    color: var(--van-text-color-3);
  }
}

.card-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  z-index: 1;
}


</style> 