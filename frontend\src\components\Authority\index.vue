<template>
  <template v-if="hasAuth">
    <slot></slot>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { hasPermission, hasAnyPermission, hasAllPermissions } from '@/utils/permission';
import { PropType } from 'vue';

defineOptions({
  name: 'Authority',
  inheritAttrs: false
})

const props = defineProps({
  // 需要的权限（字符串或字符串数组）
  permission: {
    type: [String, Array] as PropType<string | string[]>,
    default: null
  },
  // 需要的权限（字符串或字符串数组）- 兼容性属性
  value: {
    type: [String, Array] as PropType<string | string[]>,
    default: null
  },
  // 权限匹配模式：any-任意一个权限即可，all-必须有全部权限 
  match: {
    type: String as PropType<'any' | 'all'>,
    default: 'any',
    validator: (value: string) => ['any', 'all'].includes(value)
  }
})

const hasAuth = computed(() => {
  // 优先使用 value 属性，如果没有则使用 permission 属性
  const targetPermission = props.value || props.permission
  
  if (!targetPermission) {
    return true
  }
  
  if (typeof targetPermission === 'string') {
    return hasPermission(targetPermission)
  } else if (Array.isArray(targetPermission)) {
    return props.match === 'all' 
      ? hasAllPermissions(targetPermission as string[]) 
      : hasAnyPermission(targetPermission as string[])
  }
  
  return true
})
</script> 