# OPS平台Jenkins CI/CD部署指南

## 概述

本文档介绍如何使用Jenkins为OPS平台配置完整的CI/CD流程，包括代码构建、测试、Docker镜像构建和部署。

## 系统要求

### Jenkins服务器
- Jenkins 2.387+ LTS
- 至少4GB可用内存
- 至少50GB可用磁盘空间
- Java 11+

### Kubernetes集群
- Kubernetes 1.20+
- 启用RBAC
- 配置Jenkins ServiceAccount

### Harbor镜像仓库
- Harbor 2.0+
- 配置项目访问权限
- 启用镜像扫描

## 快速开始

### 1. 安装Jenkins

```bash
# 使用Docker安装Jenkins
docker run -d \
  --name jenkins \
  -p 8080:8080 \
  -p 50000:50000 \
  -v jenkins_home:/var/jenkins_home \
  -v /var/run/docker.sock:/var/run/docker.sock \
  jenkins/jenkins:lts
```

### 2. 安装必需插件

```bash
# 访问Jenkins插件管理页面
# http://your-jenkins:8080/pluginManager/

# 安装以下插件：
# - Pipeline
# - Git
# - Kubernetes
# - Docker Pipeline
# - Credentials
```

### 3. 配置凭据

在Jenkins中配置以下凭据：

| 凭据ID | 类型 | 描述 |
|---------|------|------|
| `gitea` | Username/Password | Gitea Git仓库访问 |
| `dev-harbor` | Username/Password | Harbor镜像仓库访问 |
| `kubeconfig` | Secret | Kubernetes集群配置 |

## Jenkinsfile详解

### 阶段1: 代码检出
```groovy
stage('Checkout Code') {
  agent any
  steps {
    git(
      url: 'https://git.zhixin.asia/221900264/OPS-Platform', 
      credentialsId: 'gitea', 
      branch: 'dev'
    )
  }
}
```

**功能**:
- 从Gitea仓库检出代码
- 使用配置的凭据进行认证
- 自动生成构建标签

### 阶段2: 前端构建
```groovy
stage('Frontend Build') {
  agent {
    kubernetes {
      yaml '''
        containers:
        - name: node
          image: node:22-alpine  # 与Dockerfile完全一致
      '''
    }
  }
}
```

**功能**:
- 使用Kubernetes Pod进行构建
- Node.js 18-alpine环境（与Dockerfile一致）
- 执行TypeScript类型检查
- 构建生产版本
- 生成构建产物压缩包

### 阶段3: 后端构建和测试
```groovy
stage('Backend Build & Test') {
  agent {
    kubernetes {
      yaml '''
        containers:
        - name: python
          image: python:3.11-slim  # 与Dockerfile完全一致
      '''
    }
  }
}
```

**功能**:
- Python 3.11-slim环境（与Dockerfile一致）
- 安装uv包管理器
- 安装项目依赖和开发依赖
- 代码质量检查（black, isort, flake8）
- 运行pytest测试套件
- 生成测试覆盖率报告

### 阶段4: Docker镜像构建和推送
```groovy
stage('Build & Push Docker Images') {
  agent {
    kubernetes {
      yaml '''
        containers:
        - name: docker
          image: docker:20.10
          securityContext:
            privileged: true
      '''
    }
  }
}
```

**功能**:
- 使用Docker-in-Docker构建镜像
- 构建前端和后端镜像
- 推送到Harbor镜像仓库
- 自动清理本地镜像

### 阶段5: 测试环境部署
```groovy
stage('Deploy to Test Environment') {
  agent any
  when {
    branch 'dev'
  }
}
```

**功能**:
- 仅在dev分支执行
- 部署到测试环境
- 支持Kubernetes部署

## 配置说明

### 环境变量
```groovy
environment {
  HARBOR_REGISTRY = 'harbor.zhixin.asia'
  FRONTEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/frontend"
  BACKEND_IMAGE = "${HARBOR_REGISTRY}/ops-platform/backend"
  NODE_ENV = 'production'
  PYTHON_ENV = 'production'
}

### 构建标签策略
```groovy
env.BUILD_TAG = "ops-platform-${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
```

**格式**: `ops-platform-{构建号}-{Git提交哈希}`
**示例**: `ops-platform-123-a1cc35d`

## 部署配置

### Kubernetes配置
```yaml
# jenkins-serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: jenkins
  namespace: jenkins
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: jenkins-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: jenkins
  namespace: jenkins
```

### Harbor配置
```bash
# 创建项目
harbor-cli project create --name ops-platform --public

# 配置权限
harbor-cli member add --project ops-platform --user jenkins --role developer

# 镜像地址格式
# 前端: harbor.zhixin.asia/ops-platform/frontend[:TAG]
# 后端: harbor.zhixin.asia/ops-platform/backend[:TAG]
```

## 监控和告警

### 构建监控
- 构建成功率统计
- 构建时间趋势分析
- 失败构建原因分析

### 通知配置
```groovy
post {
  failure {
    // 发送失败通知
    emailext(
      subject: "构建失败: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
      body: "构建失败，请查看日志: ${env.BUILD_URL}",
      to: "<EMAIL>"
    )
  }
}
```

## 故障排除

### 常见问题

#### 1. 前端构建失败
```bash
# 检查Node.js版本
node --version

# 清理依赖
rm -rf node_modules package-lock.json
npm install

# 检查构建脚本
npm run build
```

#### 2. 后端测试失败
```bash
# 检查Python版本
python --version

# 安装系统依赖
apt-get update && apt-get install -y gcc g++ libpq-dev libffi-dev

# 运行测试
uv run pytest tests/ -v
```

#### 3. Docker构建失败
```bash
# 检查Docker权限
docker ps

# 检查Harbor连接
docker login harbor.zhixin.asia

# 检查构建上下文
ls -la frontend/
ls -la backend/
```

#### 4. Kubernetes Pod启动失败
```bash
# 检查Pod状态
kubectl get pods -n jenkins

# 查看Pod日志
kubectl logs <pod-name> -n jenkins

# 检查资源配额
kubectl describe resourcequota -n jenkins
```

### 日志分析
```bash
# 查看Jenkins构建日志
tail -f /var/jenkins_home/jobs/ops-platform/builds/latest/log

# 查看Kubernetes事件
kubectl get events -n jenkins --sort-by='.lastTimestamp'

# 查看Docker构建日志
docker logs <container-id>
```

## 性能优化

### 构建优化
1. **并行构建**: 前端和后端可以并行构建
2. **缓存策略**: 使用Docker层缓存
3. **资源限制**: 合理配置Pod资源限制

### 部署优化
1. **蓝绿部署**: 支持零停机部署
2. **回滚策略**: 快速回滚到上一个版本
3. **健康检查**: 部署后自动健康检查

## 安全配置

### 凭据管理
- 使用Jenkins凭据存储敏感信息
- 定期轮换密码和密钥
- 限制凭据访问权限

### 网络安全
- 配置防火墙规则
- 使用HTTPS访问Jenkins
- 限制Docker socket访问

### 权限控制
- 配置Jenkins用户权限
- 使用RBAC控制Kubernetes访问
- 审计日志记录

## 扩展和集成

### CI/CD流水线扩展
1. **多环境部署**: 开发、测试、预生产、生产
2. **自动化测试**: 集成测试、性能测试、安全测试
3. **质量门禁**: 代码覆盖率、安全扫描、性能基准

### 工具集成
1. **代码质量**: SonarQube集成
2. **安全扫描**: Trivy、Clair集成
3. **监控告警**: Prometheus、Grafana集成

## 最佳实践

### 1. 版本管理
- 使用语义化版本号
- 标签策略清晰明确
- 支持快速回滚

### 2. 测试策略
- 单元测试覆盖率 > 80%
- 集成测试覆盖关键流程
- 自动化测试执行

### 3. 部署策略
- 蓝绿部署减少风险
- 健康检查确保可用性
- 监控告警及时响应

### 4. 安全策略
- 最小权限原则
- 定期安全扫描
- 漏洞及时修复

## 联系和支持

如果在Jenkins CI/CD配置过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查Jenkins和Kubernetes日志
3. 验证凭据和权限配置
4. 联系DevOps团队获取技术支持

---

**注意**: 生产环境部署前，请务必：
- 完成完整的测试流程
- 配置监控和告警
- 制定回滚和应急方案
- 进行安全审查和风险评估
