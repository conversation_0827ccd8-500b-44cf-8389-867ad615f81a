<template>
  <div class="mobile-email-members">
    <van-search v-model="searchValue" placeholder="搜索成员" />
    
    <van-list>
      <van-cell
        v-for="member in members"
        :key="member.id"
        :title="member.name"
        :label="member.department"
        :value="member.email"
      />
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchValue = ref('')
const members = ref([
  { id: 1, name: '张三', department: '技术部', email: 'zhang<PERSON>@company.com' },
  { id: 2, name: '李四', department: '销售部', email: '<EMAIL>' },
  { id: 3, name: '王五', department: '人事部', email: '<EMAIL>' }
])
</script>

<style lang="scss" scoped>
@use '@mobile/styles/variables.scss';

.mobile-email-members {
  background-color: #f7f8fa;
  min-height: 100vh;
}
</style> 