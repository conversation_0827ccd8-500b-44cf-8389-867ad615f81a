# 邮箱人员同步过滤配置整合任务

## 任务背景
邮箱管理模块中，人员同步功能存在配置重复问题：
- 独立的"过滤配置"对话框
- "人员同步配置"对话框中也包含相同的过滤字段
- 过滤配置实质上就是同步源筛选，与AD管理模块功能类似

## 整合方案
**方案1：简化整合方案（已选择）**
- 移除独立的"过滤配置"对话框
- 将过滤配置直接整合到"人员同步配置"对话框中
- 保持现有数据模型不变，只修改前端界面

## 执行计划

### 1. 前端界面整合
- **文件**: `frontend/src/views/email/SyncManagement.vue`
- **操作**: 
  - 移除过滤配置对话框（行1012-1109）
  - 优化人员同步配置对话框布局
  - 移除相关的过滤配置变量和方法
  - 整合保存逻辑

### 2. API调用优化
- **文件**: `frontend/src/api/email/personnel-sync.ts`
- **操作**: 检查并移除不必要的过滤配置API

### 3. 后端清理
- **文件**: `backend/app/schemas/email_personnel_sync.py`
- **操作**: 清理不必要的Schema定义
- **文件**: `backend/app/api/v1/email.py`
- **操作**: 清理不必要的API端点

### 4. 数据模型
- **文件**: `backend/app/models/email.py`
- **操作**: 保持不变，确保数据兼容性

## 预期结果
1. 用户界面更简洁，避免配置重复
2. 所有过滤功能通过统一的同步配置界面管理
3. 保持现有数据结构和功能完整性
4. 与AD管理模块的设计理念保持一致

## 执行时间
开始执行：$(date) 