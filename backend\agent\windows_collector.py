import wmi
import platform
import socket
import os
import winreg
import logging
import win32net
import win32netcon
import win32security
import win32api
import win32con
import win32com.client
import pythoncom
import time
from datetime import datetime
import shutil
import json

# 导入注册表管理器
try:
    from .registry_manager import RegistryManager, RegistryOperationType, RegistryValueType, RegistryRootKey
except ImportError:
    # 处理相对导入失败的情况
    try:
        from registry_manager import RegistryManager, RegistryOperationType, RegistryValueType, RegistryRootKey
    except ImportError as e:
        logging.warning(f"无法导入注册表管理器: {e}")
        RegistryManager = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


class WindowsCollector:
    """Windows系统信息采集器"""
    
    def __init__(self):
        """初始化WMI连接"""
        self.wmi_conn = None
        self.wmi_service = None
        self.wmi_service_conn = None
        
        # 初始化注册表管理器
        self.registry_manager = None
        if RegistryManager is not None:
            try:
                self.registry_manager = RegistryManager()
                logger.info("注册表管理器初始化成功")
            except Exception as e:
                logger.warning(f"注册表管理器初始化失败: {e}")
        
        try:
            # 尝试初始化wmi模块
            import wmi
            logger.info("成功导入wmi模块")
            
            # 确保先进行COM初始化
            try:
                # 强制初始化COM子系统
                import pythoncom
                pythoncom.CoInitialize()
                logger.info("COM子系统初始化成功")
                
                # 设置COM安全级别 - 确保在创建WMI连接前完成
                try:
                    # pythoncom.CoInitializeSecurity参数说明：
                    # - None: 默认安全描述符
                    # - -1: COM_RIGHTS_EXECUTE, 允许远程激活
                    # - 3: RPC_C_AUTHN_LEVEL_CALL, 默认验证等级 
                    # - 1: RPC_C_IMP_LEVEL_IDENTIFY, 模拟级别
                    # - None: 不做额外身份验证
                    # - 0: EOAC_NONE, 不需要额外功能
                    import win32com.client.dynamic
                    # 明确指定所有参数的类型和值，修复参数错误
                    pythoncom.CoInitializeSecurity(
                        None,        # pSecDesc
                        -1,          # cAuthSvc (COM_RIGHTS_EXECUTE)
                        None,        # asAuthSvc
                        None,        # dwAuthnLevel (使用默认值而非3)
                        None,        # dwImpLevel (使用默认值而非1)
                        None,        # pAuthList
                        0,           # dwCapabilities (EOAC_NONE)
                        None         # dwReserved (必须为None而非0)
                    )
                    logger.info("COM安全初始化成功")
                except Exception as sec_err:
                    logger.warning(f"COM安全初始化失败 (这可能影响WMI功能): {sec_err}")
                    # 继续尝试WMI连接，因为即使COM安全初始化失败，WMI连接可能仍然成功
            except Exception as com_err:
                logger.warning(f"COM初始化失败 (这可能影响WMI功能): {com_err}")
            
            # 多种方式尝试初始化WMI连接
            logger.info("开始尝试初始化WMI连接...")
            wmi_success = False
            
            # 方法1: 使用标准初始化 - 首先尝试最简单稳定的方式
            if not wmi_success:
                try:
                    # 确保每次尝试前重新初始化COM
                    pythoncom.CoInitialize()
                    # 直接使用最基本的WMI构造方式，不带任何特殊参数
                    self.wmi_conn = wmi.WMI()
                    if self.wmi_conn:
                        logger.info("使用标准方式初始化WMI连接成功")
                        wmi_success = True
                except Exception as err:
                    logger.warning(f"标准WMI初始化失败: {err}")
                    
            # 方法2: 使用带安全特权的初始化
            if not wmi_success:
                try:
                    pythoncom.CoInitialize()  # 再次确保COM已初始化
                    self.wmi_conn = wmi.WMI(privileges=["Security"])
                    if self.wmi_conn:
                        logger.info("使用安全特权初始化WMI连接成功")
                        wmi_success = True
                except Exception as err:
                    logger.warning(f"带安全特权的WMI初始化失败: {err}")
                    
            # 方法3: 使用显式Moniker字符串
            if not wmi_success:
                try:
                    pythoncom.CoInitialize()  # 再次确保COM已初始化
                    self.wmi_conn = wmi.WMI(moniker="winmgmts:")
                    if self.wmi_conn:
                        logger.info("使用基本Moniker初始化WMI连接成功")
                        wmi_success = True
                except Exception as err:
                    logger.warning(f"基本Moniker WMI初始化失败: {err}")
                    
            # 方法4: 使用impersonation级别
            if not wmi_success:
                try:
                    pythoncom.CoInitialize()  # 再次确保COM已初始化
                    self.wmi_conn = wmi.WMI(moniker="winmgmts:{impersonationLevel=impersonate}!//./root/cimv2")
                    if self.wmi_conn:
                        logger.info("使用impersonation级别初始化WMI连接成功")
                        wmi_success = True
                except Exception as err:
                    logger.warning(f"带impersonation级别的WMI初始化失败: {err}")
                    
            # 方法5: 使用本地连接
            if not wmi_success:
                try:
                    pythoncom.CoInitialize()  # 再次确保COM已初始化
                    self.wmi_conn = wmi.WMI(moniker="//./root/cimv2")
                    if self.wmi_conn:
                        logger.info("使用本地路径初始化WMI连接成功")
                        wmi_success = True
                except Exception as err:
                    logger.warning(f"本地路径WMI初始化失败: {err}")
            
            # 检查WMI连接是否成功建立
            if wmi_success:
                # 使用专用方法进行深度连接验证
                if self._verify_wmi_connection(self.wmi_conn):
                    # 初始化WMI服务连接（用于一些高级操作）
                    try:
                        self.wmi_service = win32com.client.Dispatch("WbemScripting.SWbemLocator")
                        self.wmi_service_conn = self.wmi_service.ConnectServer(".", "root\\cimv2")
                        logger.info("WMI服务连接初始化成功")
                    except Exception as service_err:
                        logger.warning(f"WMI服务连接初始化失败，某些高级功能可能不可用: {service_err}")
                else:
                    logger.warning("WMI连接验证失败，将释放连接并使用备用方法")
                    self.wmi_conn = None
                    wmi_success = False
            
            if not wmi_success:
                logger.error("所有WMI连接方法都失败，将使用备用方法收集信息")
                self.wmi_conn = None
                
        except ImportError as imp_err:
            logger.critical(f"导入wmi模块失败，WMI功能将不可用: {imp_err}")
            # 降级处理，不抛出异常
        except Exception as e:
            logger.critical(f"WMI初始化过程中发生未知错误: {e}")
            # 降级处理，不抛出异常
    
    def _safe_wmi_query(self, query_func, default_return=None, query_name=""):
        """安全执行WMI查询，确保每次查询前初始化COM环境"""
        if self.wmi_conn is None:
            logger.warning("WMI连接不可用，无法执行查询")
            return default_return
            
        try:
            # 创建一个线程执行查询，避免长时间阻塞
            import threading
            result = [default_return]
            query_completed = threading.Event()
            query_error = [None]
            
            def run_query():
                try:
                    # 在查询线程中完全重新初始化COM环境
                    import pythoncom
                    pythoncom.CoInitialize()
                    
                    try:
                        # 在新的COM上下文中为该线程设置安全级别
                        pythoncom.CoInitializeSecurity(
                            None,
                            -1,
                            None,
                            3,     # RPC_C_AUTHN_LEVEL_CALL
                            1,     # RPC_C_IMP_LEVEL_IDENTIFY
                            None,
                            0,     # EOAC_NONE
                            0      # dwReserved
                        )
                    except Exception as com_sec_err:
                        # 继续执行，但记录错误
                        logger.debug(f"在查询线程中设置COM安全级别失败: {com_sec_err}")
                    
                    # 在当前线程中创建新的WMI连接，使用默认参数以提高稳定性
                    import wmi
                    try:
                        # 首先尝试标准连接
                        thread_wmi = wmi.WMI()
                    except Exception:
                        # 如果失败，尝试带impersonation的连接
                        thread_wmi = wmi.WMI(moniker="winmgmts:{impersonationLevel=impersonate}!//./root/cimv2")
                    
                    # 保存原始连接引用
                    original_conn = self.wmi_conn
                    
                    try:
                        # 临时替换WMI连接为线程本地连接
                        self.wmi_conn = thread_wmi
                        
                        # 执行查询函数
                        result[0] = query_func()
                    finally:
                        # 始终恢复原始连接并释放资源
                        self.wmi_conn = original_conn
                        pythoncom.CoUninitialize()
                    
                    query_completed.set()
                except Exception as qe:
                    logger.error(f"WMI查询'{query_name}'执行错误: {qe}")
                    query_error[0] = qe
                    query_completed.set()
            
            query_thread = threading.Thread(target=run_query)
            query_thread.daemon = True
            query_thread.start()
            
            # 等待查询完成，最多10秒
            if not query_completed.wait(10):
                logger.warning(f"WMI查询超时: {query_name}")
                return default_return
                
            # 如果发生错误，记录错误
            if query_error[0]:
                logger.error(f"WMI查询失败（详细错误）: {query_error[0]}")
                
                # 查询失败后可以尝试备用方法，但不重试WMI
                # 因为重试可能会遇到相同的COM线程问题
                return default_return
            
            return result[0]
        except Exception as e:
            logger.error(f"WMI查询过程中发生错误: {e}")
            return default_return
    
    def _is_admin(self):
        """检查当前进程是否拥有管理员权限"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except:
            return False
    
    def collect_hardware_info(self):
        """采集硬件信息"""
        hw_info = {
            "cpu_model": "",
            "cpu_cores": 0,
            "memory_total": 0,
            "disks": [],
            "serial_number": "",
            "manufacturer": "",
            "model": ""
        }
        
        try:
            logger.info("开始采集硬件信息...")
            is_admin = self._is_admin()
            if not is_admin:
                logger.warning("当前进程不具备管理员权限，某些WMI查询可能失败")
            
            # 尝试获取CPU信息
            try:
                if is_admin:
                    # 专门为 Win32_Processor 创建一个独立的 WMI 连接
                    try:
                        logger.info("尝试使用专用连接获取CPU信息...")
                        import wmi
                        pythoncom.CoInitialize()
                        cpu_conn = wmi.WMI(moniker="winmgmts:{impersonationLevel=impersonate}!//./root/cimv2")
                        cpu_items = cpu_conn.Win32_Processor()
                        
                        if cpu_items and len(cpu_items) > 0:
                            cpu_info = cpu_items[0]
                            hw_info["cpu_model"] = cpu_info.Name
                            hw_info["cpu_cores"] = cpu_info.NumberOfCores
                            logger.info(f"通过专用连接成功获取CPU信息: {hw_info['cpu_model']}, {hw_info['cpu_cores']} cores")
                    except Exception as cpu_err:
                        logger.error(f"通过专用连接获取CPU信息失败: {cpu_err}")
                        
                        # 如果专用连接失败，尝试使用主WMI连接
                        if self.wmi_conn:
                            # 使用安全查询方法
                            def query_cpu():
                                cpu_items = self.wmi_conn.Win32_Processor()
                                if cpu_items and len(cpu_items) > 0:
                                    cpu_info = cpu_items[0]
                                    return cpu_info.Name, cpu_info.NumberOfCores
                                return None, None
                            
                            cpu_name, cpu_cores = self._safe_wmi_query(query_cpu, (None, None), "Win32_Processor")
                            if cpu_name:
                                hw_info["cpu_model"] = cpu_name
                            if cpu_cores:
                                hw_info["cpu_cores"] = cpu_cores
            except Exception as e:
                logger.error(f"获取CPU信息时发生错误: {str(e)}")
            
            # 如果WMI方式失败，使用备用方法
            if not hw_info["cpu_model"] or not hw_info["cpu_cores"]:
                try:
                    # 使用平台模块获取基础信息
                    hw_info["cpu_model"] = platform.processor()
                    hw_info["cpu_cores"] = os.cpu_count() or 0
                    logger.info(f"使用备用方法获取CPU信息: {hw_info['cpu_model']}, {hw_info['cpu_cores']} cores")
                    
                    # 尝试使用更多备用方法增强信息
                    if shutil.which("wmic"):
                        try:
                            import subprocess
                            cpu_model_proc = subprocess.run(
                                ["wmic", "cpu", "get", "name"], 
                                capture_output=True, text=True, check=True
                            )
                            if cpu_model_proc.stdout:
                                lines = cpu_model_proc.stdout.strip().split('\n')
                                if len(lines) > 1:
                                    hw_info["cpu_model"] = lines[1].strip()
                            
                            # WMIC 获取核心数
                            if not hw_info["cpu_cores"] or hw_info["cpu_cores"] == 0:
                                cpu_cores_proc = subprocess.run(
                                    ["wmic", "cpu", "get", "NumberOfCores"], 
                                    capture_output=True, text=True, check=True
                                )
                                if cpu_cores_proc.stdout:
                                    lines = cpu_cores_proc.stdout.strip().split('\n')
                                    if len(lines) > 1:
                                        try:
                                            hw_info["cpu_cores"] = int(lines[1].strip())
                                        except (ValueError, TypeError):
                                            pass
                            
                            logger.info(f"WMIC增强的CPU信息: {hw_info['cpu_model']}, {hw_info['cpu_cores']} cores")
                        except Exception as wmic_err:
                            logger.warning(f"使用WMIC获取CPU信息失败: {wmic_err}")
                    
                    # 尝试使用PowerShell
                    if shutil.which("powershell"):
                        try:
                            import subprocess
                            # 获取CPU信息
                            ps_result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance Win32_Processor | Select-Object Name,NumberOfCores | ConvertTo-Json)"],
                                capture_output=True, text=True, check=True
                            )
                            
                            if ps_result.stdout.strip():
                                import json
                                try:
                                    cpu_data = json.loads(ps_result.stdout)
                                    if isinstance(cpu_data, dict):
                                        if "Name" in cpu_data:
                                            hw_info["cpu_model"] = cpu_data["Name"]
                                        if "NumberOfCores" in cpu_data:
                                            hw_info["cpu_cores"] = cpu_data["NumberOfCores"]
                                    logger.info(f"PowerShell增强的CPU信息: {hw_info['cpu_model']}, {hw_info['cpu_cores']} cores")
                                except json.JSONDecodeError:
                                    logger.warning("无法解析PowerShell CPU信息输出")
                        except Exception as ps_err:
                            logger.warning(f"使用PowerShell获取CPU信息失败: {ps_err}")
                except Exception as alt_e:
                    logger.error(f"备用方法获取CPU信息也失败: {str(alt_e)}")
            
            # 获取内存信息
            if is_admin:
                try:
                    # 专门为内存信息创建一个临时连接
                    try:
                        logger.info("尝试使用专用连接获取内存信息...")
                        import wmi
                        pythoncom.CoInitialize()
                        # 这里使用不含Security参数的连接方式
                        memory_conn = wmi.WMI()
                        memory_items = memory_conn.Win32_ComputerSystem()
                        if memory_items and len(memory_items) > 0:
                            memory_info = memory_items[0]
                            hw_info["memory_total"] = int(memory_info.TotalPhysicalMemory) // 1024  # 转换为KB
                            logger.info(f"通过专用连接获取内存信息成功: {int(memory_info.TotalPhysicalMemory) // (1024*1024*1024)} GB")
                    except Exception as direct_err:
                        logger.warning(f"通过专用连接获取内存信息失败: {str(direct_err)}")
                        
                        # 尝试使用基本连接
                        try:
                            # 使用基本WMI实例，不指定权限
                            import wmi
                            pythoncom.CoInitialize()
                            simple_conn = wmi.WMI(moniker="winmgmts:")
                            memory_items = simple_conn.Win32_ComputerSystem()
                            if memory_items and len(memory_items) > 0:
                                memory_info = memory_items[0]
                                hw_info["memory_total"] = int(memory_info.TotalPhysicalMemory) // 1024  # 转换为KB
                                logger.info(f"通过基本连接获取内存信息成功: {int(memory_info.TotalPhysicalMemory) // (1024*1024*1024)} GB")
                        except Exception as basic_err:
                            logger.warning(f"通过基本连接获取内存信息失败: {str(basic_err)}")
                            
                            # 最后尝试常规WMI连接
                            if self.wmi_conn:
                                try:
                                    memory_items = self.wmi_conn.Win32_ComputerSystem()
                                    if memory_items and len(memory_items) > 0:
                                        memory_info = memory_items[0]
                                        hw_info["memory_total"] = int(memory_info.TotalPhysicalMemory) // 1024  # 转换为KB
                                except Exception as wmi_err:
                                    logger.error(f"通过Win32_ComputerSystem获取内存信息失败: {str(wmi_err)}")
                except Exception as e:
                    logger.error(f"获取内存信息时发生错误: {str(e)}")
            
            # 如果WMI方式获取内存失败，使用psutil
            if not hw_info["memory_total"]:
                try:
                    import psutil
                    memory = psutil.virtual_memory()
                    hw_info["memory_total"] = memory.total // 1024  # 转换为KB
                    logger.info(f"使用psutil获取内存信息: {memory.total / (1024 * 1024 * 1024):.2f} GB")
                except ImportError:
                    logger.warning("psutil模块未安装，无法获取内存信息")
                except Exception as e:
                    logger.error(f"使用psutil获取内存信息失败: {str(e)}")
                    
                    # 如果psutil也失败，尝试使用PowerShell
                    if shutil.which("powershell"):
                        try:
                            import subprocess
                            result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum).Sum"],
                                capture_output=True, text=True
                            )
                            if result.stdout.strip():
                                total_memory = int(result.stdout.strip())
                                hw_info["memory_total"] = total_memory // 1024  # 转换为KB
                                logger.info(f"使用PowerShell获取内存信息: {total_memory / (1024*1024*1024):.2f} GB")
                        except Exception as ps_err:
                            logger.error(f"使用PowerShell获取内存信息也失败: {str(ps_err)}")
                            
                            # 最后尝试使用wmic命令行
                            try:
                                result = subprocess.run(
                                    ["wmic", "computersystem", "get", "totalphysicalmemory"],
                                    capture_output=True, text=True
                                )
                                if result.stdout:
                                    lines = result.stdout.strip().split('\n')
                                    if len(lines) > 1:
                                        try:
                                            total_memory = int(lines[1].strip())
                                            hw_info["memory_total"] = total_memory // 1024  # 转换为KB
                                            logger.info(f"使用wmic命令获取内存信息: {total_memory / (1024*1024*1024):.2f} GB")
                                        except (ValueError, IndexError):
                                            pass
                            except Exception:
                                pass
            
            # 获取磁盘信息
            disks = []
            try:
                # 首先尝试使用psutil获取磁盘信息（不需要管理员权限）
                try:
                    import psutil
                    for partition in psutil.disk_partitions(all=False):
                        if partition.fstype:  # 忽略无文件系统的分区
                            try:
                                usage = psutil.disk_usage(partition.mountpoint)
                                disk_info = {
                                    "name": partition.device,
                                    "total_space": usage.total // 1024,  # 转换为KB
                                    "free_space": usage.free // 1024,    # 转换为KB
                                    "filesystem": partition.fstype,
                                    "mount_point": partition.mountpoint
                                }
                                disks.append(disk_info)
                            except (PermissionError, FileNotFoundError):
                                # 某些系统分区可能无法访问
                                pass
                    
                    if disks:
                        logger.info(f"使用psutil成功获取了 {len(disks)} 个磁盘信息")
                except ImportError:
                    logger.warning("psutil模块未安装，无法使用此方法获取磁盘信息")
                
                # 如果psutil方法失败或未获取到数据，尝试使用WMI
                if not disks and self.wmi_conn and is_admin:
                    wmi_disks = self.wmi_conn.Win32_LogicalDisk(DriveType=3)  # 3 = 本地磁盘
                    for disk in wmi_disks:
                        try:
                            disk_info = {
                                "name": disk.DeviceID,
                                "total_space": int(disk.Size) // 1024 if disk.Size else 0,  # 转换为KB
                                "free_space": int(disk.FreeSpace) // 1024 if disk.FreeSpace else 0,  # 转换为KB
                                "filesystem": disk.FileSystem or "",
                                "mount_point": disk.DeviceID
                            }
                            disks.append(disk_info)
                        except Exception as disk_err:
                            logger.error(f"处理磁盘 {disk.DeviceID} 信息时出错: {str(disk_err)}")
                    
                    if disks:
                        logger.info(f"使用WMI成功获取了 {len(disks)} 个磁盘信息")
                
                # 如果以上方法都失败，使用命令行
                if not disks:
                    import subprocess
                    try:
                        result = subprocess.run(["wmic", "logicaldisk", "where", "drivetype=3", "get", "deviceid,size,freespace,filesystem"], 
                                               capture_output=True, text=True, check=True)
                        lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                        for line in lines:
                            parts = line.strip().split()
                            if len(parts) >= 4:
                                try:
                                    disk_info = {
                                        "name": parts[0],
                                        "total_space": int(parts[1]) // 1024,  # 转换为KB
                                        "free_space": int(parts[2]) // 1024,  # 转换为KB
                                        "filesystem": parts[3],
                                        "mount_point": parts[0]
                                    }
                                    disks.append(disk_info)
                                except (ValueError, IndexError):
                                    pass
                        
                        if disks:
                            logger.info(f"使用命令行成功获取了 {len(disks)} 个磁盘信息")
                    except Exception as cmd_err:
                        logger.error(f"使用命令行获取磁盘信息失败: {str(cmd_err)}")
            except Exception as disk_e:
                logger.error(f"获取磁盘信息过程中发生错误: {str(disk_e)}")
                
            hw_info["disks"] = disks
            
            # 获取系统信息（制造商、型号、序列号）
            if is_admin:
                try:
                    if self.wmi_conn:
                        try:
                            # 尝试使用直接方式查询系统信息
                            try:
                                import wmi
                                pythoncom.CoInitialize()
                                temp_conn = wmi.WMI(moniker="winmgmts:{impersonationLevel=impersonate}!//./root/cimv2")
                                sys_items = temp_conn.Win32_ComputerSystemProduct()
                                if sys_items and len(sys_items) > 0:
                                    sys_info = sys_items[0]
                                    hw_info["serial_number"] = sys_info.IdentifyingNumber or ""
                                    hw_info["manufacturer"] = sys_info.Vendor or ""
                                    hw_info["model"] = sys_info.Name or ""
                                    logger.info(f"通过临时WMI连接获取系统信息成功: {hw_info['manufacturer']} {hw_info['model']}")
                            except Exception as temp_err:
                                logger.warning(f"通过临时WMI连接获取系统信息失败: {str(temp_err)}")
                                
                                # 尝试使用常规WMI连接
                                sys_items = self.wmi_conn.Win32_ComputerSystemProduct()
                                if sys_items and len(sys_items) > 0:
                                    sys_info = sys_items[0]
                                    hw_info["serial_number"] = sys_info.IdentifyingNumber or ""
                                    hw_info["manufacturer"] = sys_info.Vendor or ""
                                    hw_info["model"] = sys_info.Name or ""
                        except Exception as sys_err:
                            logger.error(f"通过Win32_ComputerSystemProduct获取系统信息失败: {str(sys_err)}")
                except Exception as e:
                    logger.error(f"获取系统信息时发生错误: {str(e)}")
            
            # 如果WMI方式失败，使用PowerShell作为备用方法
            if not hw_info["manufacturer"] or not hw_info["model"]:
                if shutil.which("powershell"):
                    try:
                        import subprocess
                        
                        # 获取制造商
                        if not hw_info["manufacturer"]:
                            result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance -ClassName Win32_ComputerSystem).Manufacturer"],
                                capture_output=True, text=True
                            )
                            if result.stdout.strip():
                                hw_info["manufacturer"] = result.stdout.strip()
                                logger.info(f"通过PowerShell获取制造商: {hw_info['manufacturer']}")
                        
                        # 获取型号
                        if not hw_info["model"]:
                            result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance -ClassName Win32_ComputerSystem).Model"],
                                capture_output=True, text=True
                            )
                            if result.stdout.strip():
                                hw_info["model"] = result.stdout.strip()
                                logger.info(f"通过PowerShell获取型号: {hw_info['model']}")
                        
                        # 获取序列号
                        if not hw_info["serial_number"]:
                            result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance -ClassName Win32_BIOS).SerialNumber"],
                                capture_output=True, text=True
                            )
                            if result.stdout.strip():
                                hw_info["serial_number"] = result.stdout.strip()
                                logger.info(f"通过PowerShell获取序列号: {hw_info['serial_number']}")
                                
                    except Exception as ps_err:
                        logger.error(f"使用PowerShell获取系统信息失败: {str(ps_err)}")
                        
                        # 尝试使用wmic命令行作为最后手段
                        try:
                            # 获取制造商
                            if not hw_info["manufacturer"]:
                                result = subprocess.run(
                                    ["wmic", "computersystem", "get", "manufacturer"],
                                    capture_output=True, text=True
                                )
                                if result.stdout:
                                    lines = result.stdout.strip().split('\n')
                                    if len(lines) > 1:
                                        hw_info["manufacturer"] = lines[1].strip()
                            
                            # 获取型号
                            if not hw_info["model"]:
                                result = subprocess.run(
                                    ["wmic", "computersystem", "get", "model"],
                                    capture_output=True, text=True
                                )
                                if result.stdout:
                                    lines = result.stdout.strip().split('\n')
                                    if len(lines) > 1:
                                        hw_info["model"] = lines[1].strip()
                            
                            # 获取序列号
                            if not hw_info["serial_number"]:
                                result = subprocess.run(
                                    ["wmic", "bios", "get", "serialnumber"],
                                    capture_output=True, text=True
                                )
                                if result.stdout:
                                    lines = result.stdout.strip().split('\n')
                                    if len(lines) > 1:
                                        hw_info["serial_number"] = lines[1].strip()
                        except Exception:
                            pass
            
            logger.info("硬件信息采集完成")
            return hw_info
        except Exception as e:
            logger.error(f"采集硬件信息过程中发生错误: {str(e)}")
            # 返回空结果而不是抛出异常
            return hw_info
    
    def collect_os_info(self):
        """采集操作系统信息"""
        os_info = {
            "name": "",
            "version": "",
            "build": "",
            "architecture": "",
            "install_date": "",
            "installed_updates": [],
            "security": {
                "firewall_enabled": False,
                "antivirus": "Unknown",
                "antivirus_enabled": False
            }
        }
        
        try:
            logger.info("开始采集操作系统信息...")
            is_admin = self._is_admin()
            
            # 获取基本操作系统信息
            try:
                if self.wmi_conn and is_admin:
                    # 使用安全查询方法获取操作系统信息，避免COM线程问题
                    def query_os_info():
                        os_items = self.wmi_conn.Win32_OperatingSystem()
                        if os_items and len(os_items) > 0:
                            os_item = os_items[0]
                            return {
                                "Caption": os_item.Caption,
                                "Version": os_item.Version,
                                "BuildNumber": os_item.BuildNumber,
                                "OSArchitecture": os_item.OSArchitecture,
                                "InstallDate": os_item.InstallDate
                            }
                        return None
                    
                    # 使用安全查询方法执行WMI查询
                    os_data = self._safe_wmi_query(query_os_info, None, "Win32_OperatingSystem")
                    
                    if os_data:
                        os_info["name"] = os_data["Caption"]
                        os_info["version"] = os_data["Version"]
                        os_info["build"] = os_data["BuildNumber"]
                        os_info["architecture"] = os_data["OSArchitecture"]
                        
                        # 转换WMI日期格式为标准格式
                        if os_data["InstallDate"]:
                            try:
                                # WMI日期格式: yyyymmddHHMMSS.mmmmmm+UUU
                                install_date = os_data["InstallDate"].split('.')[0]
                                formatted_date = f"{install_date[0:4]}-{install_date[4:6]}-{install_date[6:8]}"
                                os_info["install_date"] = formatted_date
                            except Exception as date_err:
                                logger.warning(f"转换安装日期时出错: {str(date_err)}")
            except Exception as e:
                logger.error(f"获取基本操作系统信息时发生错误: {str(e)}")
            
            # 如果WMI方式失败，使用platform模块
            if not os_info["name"] or not os_info["version"]:
                try:
                    system_name = platform.system()
                    if system_name == "Windows":
                        os_info["name"] = f"Windows {platform.release()}"
                    else:
                        os_info["name"] = system_name
                        
                    os_info["version"] = platform.version()
                    os_info["build"] = platform.release()
                    os_info["architecture"] = platform.machine()
                    logger.info(f"使用platform模块获取操作系统信息: {os_info['name']} {os_info['version']}")
                except Exception as platform_err:
                    logger.error(f"使用platform模块获取操作系统信息失败: {str(platform_err)}")
            
            # 尝试使用PowerShell获取更详细的信息
            if (not os_info["name"] or not os_info["install_date"]) and shutil.which("powershell"):
                try:
                    import subprocess
                    result = subprocess.run(
                        ["powershell", "-Command", "(Get-CimInstance Win32_OperatingSystem | Select-Object Caption,Version,InstallDate | ConvertTo-Json)"], 
                        capture_output=True, text=True, check=True
                    )
                    
                    if result.stdout.strip():
                        import json
                        try:
                            ps_data = json.loads(result.stdout)
                            if ps_data:
                                if not os_info["name"] and "Caption" in ps_data:
                                    os_info["name"] = ps_data["Caption"]
                                if not os_info["version"] and "Version" in ps_data:
                                    os_info["version"] = ps_data["Version"]
                                if not os_info["install_date"] and "InstallDate" in ps_data:
                                    # 转换WMI日期格式
                                    try:
                                        install_date = ps_data["InstallDate"].split("T")[0]
                                        os_info["install_date"] = install_date
                                    except:
                                        pass
                        except json.JSONDecodeError:
                            logger.warning("使用PowerShell获取操作系统信息失败: 无法解析JSON")
                except Exception as ps_err:
                    logger.warning(f"使用PowerShell获取操作系统信息失败: {str(ps_err)}")
            
            # 获取已安装更新
            installed_updates = []
            
            # 首先尝试使用PowerShell获取更新列表（更可靠且不需要管理员权限）
            if shutil.which("powershell"):
                try:
                    import subprocess
                    result = subprocess.run(
                        ["powershell", "-Command", "(Get-HotFix | Select-Object HotFixID | ConvertTo-Json)"], 
                        capture_output=True, text=True, check=True
                    )
                    
                    if result.stdout.strip():
                        import json
                        try:
                            ps_data = json.loads(result.stdout)
                            # 结果可能是单个更新或更新列表
                            if isinstance(ps_data, list):
                                for update in ps_data:
                                    if "HotFixID" in update:
                                        installed_updates.append(update["HotFixID"])
                            elif isinstance(ps_data, dict) and "HotFixID" in ps_data:
                                installed_updates.append(ps_data["HotFixID"])
                            logger.info(f"通过PowerShell获取到 {len(installed_updates)} 个已安装更新")
                        except json.JSONDecodeError:
                            logger.warning("解析PowerShell更新列表时出错: 无法解析JSON")
                except Exception as ps_err:
                    logger.warning(f"使用PowerShell获取更新列表失败: {str(ps_err)}")
            
            # 如果PowerShell方式失败，尝试使用WMI
            if not installed_updates and self.wmi_conn and is_admin:
                # 定义WMI查询函数
                def query_qfe():
                    updates = []
                    qfe_items = self.wmi_conn.Win32_QuickFixEngineering()
                    for qfe in qfe_items:
                        if qfe.HotFixID:
                            updates.append(qfe.HotFixID)
                    return updates
                
                # 使用安全查询方法
                wmi_updates = self._safe_wmi_query(query_qfe, [], "Win32_QuickFixEngineering")
                if wmi_updates:
                    installed_updates = wmi_updates
                    logger.info(f"通过WMI获取到 {len(installed_updates)} 个已安装更新")
            
            os_info["installed_updates"] = installed_updates
            
            # 获取安全信息
            security_info = self._check_security_status()
            os_info["security"] = security_info
            
            logger.info("操作系统信息采集完成")
            return os_info
        except Exception as e:
            logger.error(f"采集操作系统信息过程中发生错误: {str(e)}")
            return os_info
            
    def _check_security_status(self):
        """检查安全状态（防火墙和杀毒软件）"""
        security_info = {
            "firewall_enabled": False,
            "antivirus": "Unknown",
            "antivirus_enabled": False
        }
        
        # 检查防火墙状态
        try:
            # 首先使用PowerShell命令检查防火墙状态（不需要管理员权限）
            if shutil.which("powershell"):
                try:
                    import subprocess
                    result = subprocess.run(
                        ["powershell", "-Command", "(Get-NetFirewallProfile | Select-Object Name,Enabled | ConvertTo-Json)"],
                        capture_output=True, text=True, check=True
                    )
                    
                    if result.stdout.strip():
                        import json
                        try:
                            ps_data = json.loads(result.stdout)
                            # 如果至少有一个配置文件启用了防火墙，则认为防火墙已启用
                            if isinstance(ps_data, list):
                                for profile in ps_data:
                                    if profile.get("Enabled"):
                                        security_info["firewall_enabled"] = True
                                        break
                            elif isinstance(ps_data, dict):
                                security_info["firewall_enabled"] = ps_data.get("Enabled", False)
                                
                            logger.info(f"通过PowerShell确定防火墙状态: {'启用' if security_info['firewall_enabled'] else '禁用'}")
                            return security_info
                        except json.JSONDecodeError:
                            logger.warning("解析PowerShell防火墙输出时出错: 无法解析JSON")
                except Exception as ps_err:
                    logger.warning(f"使用PowerShell检查防火墙状态失败: {str(ps_err)}")
            
            # 尝试使用netsh命令（需要管理员权限）
            try:
                import subprocess
                result = subprocess.run(
                    ["netsh", "advfirewall", "show", "allprofiles", "state"], 
                    capture_output=True, text=True, check=True
                )
                
                # 检查输出中是否包含"ON"
                if "ON" in result.stdout:
                    security_info["firewall_enabled"] = True
                    logger.info("通过netsh命令确定防火墙已启用")
                else:
                    logger.info("通过netsh命令确定防火墙已禁用")
            except Exception as netsh_err:
                logger.warning(f"通过netsh命令无法确定防火墙状态: {str(netsh_err)}")
                
                # 尝试检查Windows防火墙服务状态
                try:
                    result = subprocess.run(
                        ["sc", "query", "MpsSvc"], 
                        capture_output=True, text=True, check=True
                    )
                    
                    # 检查输出是否包含"RUNNING"
                    if "RUNNING" in result.stdout:
                        security_info["firewall_enabled"] = True
                        logger.info("通过SC命令检测到防火墙服务正在运行")
                    else:
                        logger.info("通过SC命令检测到防火墙服务未运行")
                except Exception as sc_err:
                    logger.warning(f"通过SC命令检查防火墙服务状态失败: {str(sc_err)}")
        except Exception as e:
            logger.error(f"检查防火墙状态时发生错误: {str(e)}")
            
        # 检查杀毒软件状态
        try:
            # 使用PowerShell获取Windows Defender状态（不需要管理员权限）
            if shutil.which("powershell"):
                try:
                    import subprocess
                    result = subprocess.run(
                        ["powershell", "-Command", "(Get-MpComputerStatus | Select-Object AMRunningMode,RealTimeProtectionEnabled | ConvertTo-Json)"],
                        capture_output=True, text=True, check=True
                    )
                    
                    if result.stdout.strip():
                        import json
                        try:
                            ps_data = json.loads(result.stdout)
                            if isinstance(ps_data, dict):
                                # 检查Defender是否启用实时保护
                                is_enabled = ps_data.get("RealTimeProtectionEnabled", False)
                                security_info["antivirus"] = "Windows Defender"
                                security_info["antivirus_enabled"] = is_enabled
                                logger.info(f"通过PowerShell检测到Windows Defender, 状态: {'启用' if is_enabled else '禁用'}")
                                return security_info
                        except json.JSONDecodeError:
                            logger.warning("解析PowerShell防病毒输出时出错: 无法解析JSON")
                except Exception as ps_err:
                    logger.warning(f"使用PowerShell检查Windows Defender状态失败: {str(ps_err)}")
                    
                # 尝试使用更通用的WMI查询方法（不需要特定的Defender PowerShell模块）
                try:
                    result = subprocess.run(
                        ["powershell", "-Command", "(Get-CimInstance -Namespace root/SecurityCenter2 -ClassName AntivirusProduct | Select-Object displayName,productState | ConvertTo-Json)"],
                        capture_output=True, text=True, check=True
                    )
                    
                    if result.stdout.strip():
                        import json
                        try:
                            ps_data = json.loads(result.stdout)
                            if isinstance(ps_data, list) and len(ps_data) > 0:
                                # 使用第一个检测到的防病毒软件
                                av_info = ps_data[0]
                                security_info["antivirus"] = av_info.get("displayName", "Unknown")
                                
                                # 解析productState (位掩码)
                                # 一般来说，2表示已启用
                                product_state = av_info.get("productState", 0)
                                security_info["antivirus_enabled"] = (product_state & 0x1000) != 0
                                
                                logger.info(f"通过SecurityCenter2检测到杀毒软件: {security_info['antivirus']}, 状态: {'启用' if security_info['antivirus_enabled'] else '禁用'}")
                                return security_info
                            elif isinstance(ps_data, dict):
                                security_info["antivirus"] = ps_data.get("displayName", "Unknown")
                                product_state = ps_data.get("productState", 0)
                                security_info["antivirus_enabled"] = (product_state & 0x1000) != 0
                                
                                logger.info(f"通过SecurityCenter2检测到杀毒软件: {security_info['antivirus']}, 状态: {'启用' if security_info['antivirus_enabled'] else '禁用'}")
                                return security_info
                        except json.JSONDecodeError:
                            logger.warning("解析SecurityCenter2输出时出错: 无法解析JSON")
                except Exception as sc2_err:
                    logger.warning(f"通过SecurityCenter2检查杀毒软件状态失败: {str(sc2_err)}")
        except Exception as e:
            logger.error(f"检查杀毒软件状态时发生错误: {str(e)}")
            
        return security_info
    
    def collect_software_info(self):
        """采集已安装软件信息"""
        try:
            logger.info("开始采集已安装软件信息...")
            
            software_list = []
            
            # 从注册表获取已安装软件列表（64位）
            software_list.extend(self._get_software_from_registry(
                winreg.HKEY_LOCAL_MACHINE, 
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"
            ))
            
            # 32位软件在64位系统上的注册表位置
            if platform.machine().endswith('64'):
                software_list.extend(self._get_software_from_registry(
                    winreg.HKEY_LOCAL_MACHINE, 
                    r"SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
                ))
            
            logger.info(f"共采集到 {len(software_list)} 个已安装软件")
            return software_list
            
        except Exception as e:
            logger.error(f"采集软件信息时发生错误: {str(e)}")
            return []
    
    def _get_software_from_registry(self, hive, reg_path):
        """从注册表获取软件信息"""
        software_list = []
        try:
            registry_key = winreg.OpenKey(hive, reg_path)
            subkey_count = winreg.QueryInfoKey(registry_key)[0]
            
            for i in range(subkey_count):
                try:
                    subkey_name = winreg.EnumKey(registry_key, i)
                    subkey = winreg.OpenKey(registry_key, subkey_name)
                    
                    try:
                        name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                    except (WindowsError, IndexError, TypeError):
                        continue  # 跳过没有名称的项目
                    
                    try:
                        version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                    except (WindowsError, IndexError, TypeError):
                        version = ""
                        
                    try:
                        publisher = winreg.QueryValueEx(subkey, "Publisher")[0]
                    except (WindowsError, IndexError, TypeError):
                        publisher = ""
                        
                    try:
                        install_date_str = winreg.QueryValueEx(subkey, "InstallDate")[0]
                        if install_date_str and len(install_date_str) == 8:  # YYYYMMDD格式
                            install_date = f"{install_date_str[:4]}-{install_date_str[4:6]}-{install_date_str[6:8]}"
                        else:
                            install_date = ""
                    except (WindowsError, IndexError, TypeError):
                        install_date = ""
                        
                    try:
                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                    except (WindowsError, IndexError, TypeError):
                        install_location = ""
                        
                    try:
                        size_str = winreg.QueryValueEx(subkey, "EstimatedSize")[0]
                        # 验证size_str是否为有效数字
                        if size_str is not None:
                            # 转换为字符串并去除空白字符
                            size_str_clean = str(size_str).strip()
                            # 检查是否为空或者是否为纯数字
                            if size_str_clean and size_str_clean.isdigit():
                                size = int(size_str_clean) * 1024  # 转换为KB
                            else:
                                size = 0  # 空值或非数字时设为0
                        else:
                            size = 0  # None值时设为0
                    except (WindowsError, IndexError, TypeError, ValueError) as e:
                        # 记录具体的错误信息，便于调试
                        logger.debug(f"获取软件大小信息失败: {str(e)}")
                        size = 0
                    
                    # 创建软件信息对象
                    software = {
                        "name": name,
                        "version": version,
                        "publisher": publisher,
                        "install_date": install_date,
                        "size": size,
                        "install_location": install_location
                    }
                    
                    software_list.append(software)
                    
                except (WindowsError, IndexError) as e:
                    continue  # 跳过读取失败的项目
                finally:
                    try:
                        winreg.CloseKey(subkey)
                    except:
                        pass
                        
            winreg.CloseKey(registry_key)
        except WindowsError as e:
            logger.error(f"读取注册表时发生错误: {str(e)}")
        
        return software_list
    
    def collect_network_info(self):
        """采集网络信息"""
        network_info = {
            "interfaces": [],
            "hostname": socket.gethostname(),
            "domain": "",
            "dns_servers": [],
            "default_gateway": ""
        }
        
        try:
            logger.info("开始采集网络信息...")
            is_admin = self._is_admin()
            
            # 获取网络接口信息，首先尝试使用psutil（不需要管理员权限）
            try:
                import psutil
                network_interfaces = []
                for name, stats in psutil.net_if_addrs().items():
                    interface = {
                        "name": name,
                        "mac_address": "",
                        "ip_address": "",
                        "subnet_mask": "",
                        "dhcp_enabled": True,  # 默认假设启用DHCP
                        "is_connected": True   # 默认假设已连接
                    }
                    
                    # 获取MAC地址、IP地址和子网掩码
                    for addr in stats:
                        if addr.family == psutil.AF_LINK:  # MAC地址
                            interface["mac_address"] = addr.address
                        elif addr.family == socket.AF_INET:  # IPv4地址
                            interface["ip_address"] = addr.address
                            interface["subnet_mask"] = addr.netmask or ""
                    
                    # 检查接口是否连接 - 通过检查IPv4地址判断
                    interface["is_connected"] = bool(interface["ip_address"])
                    
                    # 添加到接口列表
                    if interface["mac_address"] or interface["ip_address"]:
                        network_interfaces.append(interface)
                
                logger.info(f"通过psutil成功获取了 {len(network_interfaces)} 个网络接口信息")
                network_info["interfaces"] = network_interfaces
            except ImportError:
                logger.warning("psutil模块未安装，无法使用此方法获取网络接口信息")
            except Exception as psutil_err:
                logger.error(f"使用psutil获取网络接口信息失败: {str(psutil_err)}")
            
            # 如果psutil方式失败，尝试使用WMI
            if not network_info["interfaces"] and self.wmi_conn and is_admin:
                try:
                    network_adapters = self.wmi_conn.Win32_NetworkAdapter(PhysicalAdapter=True)
                    nic_configs = {nic.SettingID: nic for nic in self.wmi_conn.Win32_NetworkAdapterConfiguration()}
                    
                    for adapter in network_adapters:
                        try:
                            interface = {
                                "name": adapter.Name,
                                "mac_address": adapter.MACAddress or "",
                                "ip_address": "",
                                "subnet_mask": "",
                                "dhcp_enabled": False,
                                "is_connected": adapter.NetConnectionStatus == 2  # 2表示已连接
                            }
                            
                            # 获取对应的配置信息
                            if adapter.GUID in nic_configs:
                                config = nic_configs[adapter.GUID]
                                if config.IPAddress and len(config.IPAddress) > 0:
                                    interface["ip_address"] = config.IPAddress[0]
                                if config.IPSubnet and len(config.IPSubnet) > 0:
                                    interface["subnet_mask"] = config.IPSubnet[0]
                                interface["dhcp_enabled"] = config.DHCPEnabled
                            
                            # 只添加有效的网络接口
                            if interface["mac_address"]:
                                network_info["interfaces"].append(interface)
                        except Exception as adapter_err:
                            logger.error(f"处理网络适配器 {adapter.Name} 信息时出错: {str(adapter_err)}")
                    
                    logger.info(f"通过WMI成功获取了 {len(network_info['interfaces'])} 个网络接口信息")
                except Exception as wmi_err:
                    logger.error(f"通过WMI获取网络接口信息失败: {str(wmi_err)}")
            
            # 获取DNS服务器和默认网关信息
            try:
                if shutil.which("powershell"):
                    # 使用PowerShell获取DNS服务器（不需要管理员权限）
                    try:
                        import subprocess
                        result = subprocess.run(
                            ["powershell", "-Command", "(Get-DnsClientServerAddress -AddressFamily IPv4 | Select-Object -ExpandProperty ServerAddresses) -join ','"], 
                            capture_output=True, text=True, check=True
                        )
                        
                        dns_servers = [server.strip() for server in result.stdout.split(',') if server.strip()]
                        if dns_servers:
                            # 移除重复的DNS服务器
                            network_info["dns_servers"] = list(dict.fromkeys(dns_servers))
                            logger.info(f"通过PowerShell获取到DNS服务器: {', '.join(network_info['dns_servers'])}")
                    except Exception as ps_dns_err:
                        logger.warning(f"使用PowerShell获取DNS服务器失败: {str(ps_dns_err)}")
                    
                    # 使用PowerShell获取默认网关（不需要管理员权限）
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", "(Get-NetRoute -DestinationPrefix '0.0.0.0/0' | Select-Object -ExpandProperty NextHop)"], 
                            capture_output=True, text=True, check=True
                        )
                        
                        gateway = result.stdout.strip()
                        if gateway and gateway != "0.0.0.0":
                            network_info["default_gateway"] = gateway
                            logger.info(f"通过PowerShell获取到默认网关: {gateway}")
                    except Exception as ps_gw_err:
                        logger.warning(f"使用PowerShell获取默认网关失败: {str(ps_gw_err)}")
            except Exception as dns_gw_err:
                logger.error(f"获取DNS和网关信息时出错: {str(dns_gw_err)}")
            
            # 如果DNS服务器列表为空，使用公共DNS作为备用
            if not network_info["dns_servers"]:
                network_info["dns_servers"] = ["*******", "*******"]  # Google公共DNS
                logger.info("无法获取真实DNS服务器，使用默认DNS服务器")
            
            # 获取计算机的域信息
            try:
                # 首先尝试从环境变量获取
                domain = os.environ.get("USERDOMAIN", "")
                if domain and domain != os.environ.get("COMPUTERNAME", ""):
                    network_info["domain"] = domain
                    logger.info(f"从环境变量获取到域信息: {domain}")
                
                # 尝试使用PowerShell获取域信息
                elif shutil.which("powershell"):
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", "[System.DirectoryServices.ActiveDirectory.Domain]::GetComputerDomain().Name"], 
                            capture_output=True, text=True
                        )
                        
                        domain = result.stdout.strip()
                        if domain and not result.stderr:
                            network_info["domain"] = domain
                            logger.info(f"通过PowerShell获取到域信息: {domain}")
                    except Exception:
                        # 如果上面的方法失败，可能是因为计算机不在域中
                        # 尝试使用更基本的方法
                        try:
                            result = subprocess.run(
                                ["powershell", "-Command", "(Get-CimInstance Win32_ComputerSystem).Domain"], 
                                capture_output=True, text=True
                            )
                            
                            domain = result.stdout.strip()
                            if domain and domain != "WORKGROUP":
                                network_info["domain"] = domain
                                logger.info(f"通过Win32_ComputerSystem获取到域信息: {domain}")
                        except Exception:
                            pass
            except Exception as domain_err:
                logger.warning(f"获取域信息失败: {str(domain_err)}")
            
            logger.info("网络信息采集完成")
            return network_info
        except Exception as e:
            logger.error(f"采集网络信息过程中发生错误: {str(e)}")
            return network_info
    
    def collect_user_info(self):
        """采集最后登录用户信息"""
        user_info = {
            "username": "",
            "full_name": "",
            "login_time": "",
            "domain": ""
        }
        
        logger.info("开始采集最后登录用户信息...")
        
        # 从环境变量获取当前用户信息
        try:
            current_user = os.environ.get('USERNAME', '')
            domain = os.environ.get('USERDOMAIN', '')
            
            user_info["username"] = current_user
            user_info["domain"] = domain
            
            logger.info(f"通过环境变量获取当前用户: {current_user}, 域: {domain}")
            
            # 如果用户名以$结尾，说明是计算机账户，尝试获取实际登录的用户
            if current_user.endswith('$'):
                # 尝试多种方法获取实际登录用户
                
                # 1. 尝试使用WMI获取登录用户
                try:
                    if self.wmi_conn:
                        # 定义WMI查询函数
                        def query_user():
                            logged_users = self.wmi_conn.Win32_ComputerSystem()
                            if logged_users and len(logged_users) > 0 and logged_users[0].UserName:
                                return logged_users[0].UserName
                            return None
                        
                        # 使用安全查询方法
                        user_domain = self._safe_wmi_query(query_user, None, "Win32_ComputerSystem")
                        
                        if user_domain:
                            parts = user_domain.split('\\', 1)
                            if len(parts) > 1:
                                user_info["domain"] = parts[0]
                                user_info["username"] = parts[1]
                            else:
                                user_info["username"] = user_domain
                            logger.info(f"通过WMI获取到登录用户: {user_info['username']}, 域: {user_info['domain']}")
                except Exception as e:
                    logger.warning(f"尝试获取WMI登录用户信息失败: {str(e)}")
                
                # 2. 尝试使用PowerShell获取登录用户信息
                if (user_info["username"].endswith('$') or not user_info["username"]) and shutil.which("powershell"):
                    try:
                        import subprocess
                        # 尝试获取登录用户信息
                        result = subprocess.run(
                            ["powershell", "-Command", "(Get-CimInstance Win32_ComputerSystem).UserName"], 
                            capture_output=True, text=True, check=True
                        )
                        
                        user_domain = result.stdout.strip()
                        if user_domain:
                            parts = user_domain.split('\\', 1)
                            if len(parts) > 1:
                                user_info["domain"] = parts[0]
                                user_info["username"] = parts[1]
                            else:
                                user_info["username"] = user_domain
                            logger.info(f"通过PowerShell获取到登录用户: {user_info['username']}, 域: {user_info['domain']}")
                    except Exception as ps_err:
                        logger.warning(f"通过PowerShell获取登录用户失败: {str(ps_err)}")
                        
                    # 如果上述方法都失败，尝试使用查询会话
                    if user_info["username"].endswith('$') or not user_info["username"]:
                        try:
                            result = subprocess.run(
                                ["powershell", "-Command", "(query user) -replace '\\s{2,}', ',' | ConvertFrom-Csv"], 
                                capture_output=True, text=True
                            )
                            
                            if result.stdout.strip() and not "errorlevel" in result.stdout.lower():
                                # 提取第一个用户的用户名
                                lines = result.stdout.strip().split('\n')
                                if len(lines) > 1:  # 跳过标题行
                                    user_line = lines[1].strip()
                                    username = user_line.split()[0]
                                    if username and not username.endswith('$'):
                                        user_info["username"] = username
                                        logger.info(f"通过query user命令获取到登录用户: {username}")
                        except Exception:
                            pass
                        
                # 3. 如果上述方法都失败，尝试获取当前活动用户
                if user_info["username"].endswith('$') or not user_info["username"]:
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI' -Name 'LastLoggedOnUser' | Select-Object -ExpandProperty LastLoggedOnUser"], 
                            capture_output=True, text=True
                        )
                        
                        last_user = result.stdout.strip()
                        if last_user:
                            parts = last_user.split('\\', 1)
                            if len(parts) > 1:
                                user_info["domain"] = parts[0]
                                user_info["username"] = parts[1]
                            else:
                                user_info["username"] = last_user
                            logger.info(f"通过注册表获取到最后登录用户: {user_info['username']}, 域: {user_info['domain']}")
                    except Exception:
                        pass
            
            # 尝试获取用户全名 - 要处理域用户名格式
            try:
                username = user_info["username"]
                # 去掉$符号后缀（如果存在）
                if username.endswith('$'):
                    username = username[:-1]
                    
                # 首先尝试通过win32net获取用户信息
                try:
                    # 设置超时较短以避免长时间等待
                    timeout_flag = False
                    def get_user_info():
                        nonlocal timeout_flag
                        try:
                            # 为避免长时间阻塞，设置超时时间
                            return win32net.NetUserGetInfo(domain if domain else None, username, 2)
                        except Exception as e:
                            logger.warning(f"通过win32net获取用户详细信息失败: {str(e)}")
                            timeout_flag = True
                            return None
                    
                    # 在单独线程中执行，避免长时间阻塞
                    import threading
                    result = [None]
                    t = threading.Thread(target=lambda: result.__setitem__(0, get_user_info()))
                    t.daemon = True
                    t.start()
                    t.join(3.0)  # 最多等待3秒
                    
                    if not timeout_flag and result[0]:
                        user_info_data = result[0]
                        user_info["full_name"] = user_info_data.get('full_name', '')
                        logger.info(f"获取到用户全名: {user_info['full_name']}")
                    else:
                        logger.warning("获取用户信息超时或失败，尝试备用方法")
                        raise Exception("获取用户信息超时或失败")
                        
                except Exception:
                    # 如果域用户获取失败，尝试本地用户
                    try:
                        if domain:
                            # 同样设置超时
                            result = [None]
                            t = threading.Thread(target=lambda: result.__setitem__(0, win32net.NetUserGetInfo(None, username, 2)))
                            t.daemon = True
                            t.start()
                            t.join(3.0)  # 最多等待3秒
                            
                            if result[0]:
                                user_info_data = result[0]
                                user_info["full_name"] = user_info_data.get('full_name', '')
                                logger.info(f"获取到本地用户全名: {user_info['full_name']}")
                            else:
                                raise Exception("获取本地用户信息超时或失败")
                    except Exception:
                        # 如果通过win32net无法获取，尝试使用PowerShell
                        if shutil.which("powershell"):
                            try:
                                import subprocess
                                # 首先尝试PowerShell获取用户信息（优先使用，成功率高）
                                cmd = f"try {{ (Get-LocalUser -Name '{username}' -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName) }} catch {{ Write-Output '' }}"
                                result = subprocess.run(["powershell", "-Command", cmd], capture_output=True, text=True, timeout=2)
                                
                                if result.stdout.strip():
                                    user_info["full_name"] = result.stdout.strip()
                                    logger.info(f"通过PowerShell Get-LocalUser获取到用户全名: {user_info['full_name']}")
                                    return
                                
                                # 如果本地用户不存在，尝试域用户
                                if domain:
                                    cmd = f"try {{ Get-ADUser -Identity '{username}' -Properties DisplayName | Select-Object -ExpandProperty DisplayName }} catch {{ Write-Output '' }}"
                                    result = subprocess.run(["powershell", "-Command", cmd], capture_output=True, text=True, timeout=2)
                                    
                                    if result.stdout.strip():
                                        user_info["full_name"] = result.stdout.strip()
                                        logger.info(f"通过PowerShell AD命令获取到用户全名: {user_info['full_name']}")
                                        return
                            except Exception as ps_err:
                                logger.debug(f"通过PowerShell获取用户信息失败: {str(ps_err)}")
                        
                        # 备用方式: 使用win32net（缩短超时时间）
                        try:
                            # 设置超时较短以避免长时间等待
                            timeout_flag = False
                            def get_user_info():
                                nonlocal timeout_flag
                                try:
                                    # 为避免长时间阻塞，设置超时时间
                                    return win32net.NetUserGetInfo(domain if domain else None, username, 2)
                                except Exception as e:
                                    logger.warning(f"通过win32net获取用户详细信息失败: {str(e)}")
                                    timeout_flag = True
                                    return None
                            
                            # 在单独线程中执行，避免长时间阻塞
                            import threading
                            result = [None]
                            t = threading.Thread(target=lambda: result.__setitem__(0, get_user_info()))
                            t.daemon = True
                            t.start()
                            t.join(1.5)  # 最多等待1.5秒，减少超时时间
                            
                            if not timeout_flag and result[0]:
                                user_info_data = result[0]
                                user_info["full_name"] = user_info_data.get('full_name', '')
                                logger.info(f"获取到本地用户全名: {user_info['full_name']}")
                            else:
                                raise Exception("获取本地用户信息超时或失败")
                        except Exception:
                            pass
            except Exception as e:
                logger.warning(f"获取用户全名失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取当前用户信息失败: {str(e)}")
        
        # 获取用户登录时间（尝试多种方法）
        try:
            # 尝试使用WMI获取最后登录时间
            if self.wmi_conn:
                try:
                    # 使用Win32_LogonSession类获取会话信息
                    def query_logon_session():
                        # 通过Win32_LoggedOnUser关联到LogonSession
                        logon_sessions = []
                        for user in self.wmi_conn.Win32_LoggedOnUser():
                            try:
                                antecedent = user.Antecedent
                                dependent = user.Dependent
                                # 解析用户名
                                user_name = antecedent.split('Name="')[1].split('"')[0]
                                # 解析域名
                                domain_name = antecedent.split('Domain="')[1].split('"')[0]
                                # 解析LogonId
                                logon_id = dependent.split('LogonId=')[1].split(',')[0]
                                
                                # 获取登录会话
                                for session in self.wmi_conn.Win32_LogonSession(["StartTime", "LogonId"]):
                                    if str(session.LogonId) == logon_id:
                                        if session.StartTime:
                                            # 返回用户名、域和登录时间
                                            logon_sessions.append({
                                                'user': user_name,
                                                'domain': domain_name,
                                                'start_time': session.StartTime
                                            })
                            except Exception:
                                continue
                        return logon_sessions
                    
                    # 使用安全查询方法
                    sessions = self._safe_wmi_query(query_logon_session, [], "Win32_LogonSession")
                    
                    if sessions:
                        # 找到当前用户的会话
                        found = False
                        for session in sessions:
                            if (session['user'].lower() == user_info["username"].lower() or 
                                session['user'].lower() == username.lower()):
                                try:
                                    # WMI日期格式: yyyymmddHHMMSS.mmmmmm+UUU
                                    start_time = session['start_time'].split('.')[0]
                                    # 转换为可读格式
                                    year = start_time[0:4]
                                    month = start_time[4:6]
                                    day = start_time[6:8]
                                    hour = start_time[8:10]
                                    minute = start_time[10:12]
                                    second = start_time[12:14]
                                    
                                    login_time = f"{year}年{month}月{day}日 {hour}:{minute}:{second}"
                                    user_info["login_time"] = login_time
                                    logger.info(f"通过WMI获取到登录时间: {login_time}")
                                    found = True
                                    break
                                except Exception as e:
                                    logger.warning(f"解析WMI登录时间出错: {str(e)}")
                        
                        # 如果没有找到精确匹配，使用第一个会话时间
                        if not found and sessions:
                            try:
                                # 使用第一个会话时间
                                start_time = sessions[0]['start_time'].split('.')[0]
                                year = start_time[0:4]
                                month = start_time[4:6]
                                day = start_time[6:8]
                                hour = start_time[8:10]
                                minute = start_time[10:12]
                                second = start_time[12:14]
                                
                                login_time = f"{year}年{month}月{day}日 {hour}:{minute}:{second}"
                                user_info["login_time"] = login_time
                                logger.info(f"使用WMI最近会话登录时间: {login_time}")
                            except Exception as e:
                                logger.warning(f"解析WMI最近会话登录时间出错: {str(e)}")
                except Exception as e:
                    logger.warning(f"通过WMI获取登录时间失败: {str(e)}")
            
            # 如果WMI方法失败，尝试使用PowerShell获取
            if not user_info["login_time"] and shutil.which("powershell"):
                try:
                    import subprocess
                    cmd = "(Get-CimInstance Win32_LogonSession | Sort-Object -Property StartTime -Descending | Select-Object -First 1 | Select-Object -ExpandProperty StartTime).ToString('yyyy年MM月dd日 HH:mm:ss')"
                    result = subprocess.run(["powershell", "-Command", cmd], capture_output=True, text=True, timeout=3)
                    
                    if result.stdout.strip():
                        user_info["login_time"] = result.stdout.strip()
                        logger.info(f"通过PowerShell获取到登录时间: {user_info['login_time']}")
                except Exception as ps_err:
                    logger.warning(f"使用PowerShell获取登录时间失败: {str(ps_err)}")
            
            # 如果之前的方法都失败，使用系统启动时间作为近似值
            if not user_info["login_time"]:
                try:
                    import psutil
                    boot_time = psutil.boot_time()
                    boot_datetime = datetime.fromtimestamp(boot_time)
                    user_info["login_time"] = boot_datetime.strftime("%Y年%m月%d日 %H:%M:%S")
                    logger.info(f"使用系统启动时间作为登录时间近似值: {user_info['login_time']}")
                except Exception as bt_err:
                    logger.warning(f"获取系统启动时间失败: {str(bt_err)}")
        except Exception as e:
            logger.warning(f"获取登录时间时发生错误: {str(e)}")
        
        logger.info("用户信息采集完成")
        return user_info

    def _verify_wmi_connection(self, wmi_connection):
        """验证WMI连接是否真正可用"""
        try:
            if wmi_connection is None:
                return False
                
            # 尝试执行最基本的WMI查询以验证连接
            try:
                # 确保COM已初始化
                pythoncom.CoInitialize()
                
                # 执行简单查询 - 获取操作系统实例数
                os_count = len(wmi_connection.Win32_OperatingSystem())
                logger.info(f"WMI连接验证成功: 找到 {os_count} 个操作系统实例")
                
                # 尝试获取BIOS信息，这是一个不需要特殊权限的类
                bios_instances = wmi_connection.Win32_BIOS()
                if bios_instances and len(bios_instances) > 0:
                    logger.info(f"WMI连接深度验证成功: 成功获取BIOS信息")
                    return True
                    
                # 如果BIOS查询成功，但没有实例，尝试再次验证
                comp_sys = wmi_connection.Win32_ComputerSystem()
                if comp_sys and len(comp_sys) > 0:
                    logger.info(f"WMI连接深度验证成功: 成功获取计算机系统信息")
                    return True
                    
                # 至少操作系统查询成功了
                return os_count > 0
                
            except Exception as query_err:
                logger.warning(f"WMI连接验证查询失败: {query_err}")
                return False
                
        except Exception as e:
            logger.error(f"WMI连接验证过程中发生错误: {e}")
            return False
    
    def handle_registry_operation(self, operation_data: dict) -> dict:
        """
        处理注册表操作请求
        
        Args:
            operation_data: 操作数据，包含操作类型、根键、路径等信息
            
        Returns:
            操作结果字典
        """
        if self.registry_manager is None:
            return {
                "success": False,
                "error": "注册表管理器未初始化",
                "message": "Registry manager not initialized"
            }
        
        try:
            # 解析操作参数
            operation = operation_data.get("operation")
            root_key_name = operation_data.get("root_key", "HKEY_LOCAL_MACHINE")
            key_path = operation_data.get("key_path", "")
            value_name = operation_data.get("value_name")
            value_data = operation_data.get("value_data")
            value_type_name = operation_data.get("value_type", "REG_SZ")
            create_backup = operation_data.get("create_backup", True)
            
            # 转换根键
            try:
                root_key = RegistryRootKey[root_key_name]
            except KeyError:
                return {
                    "success": False,
                    "error": f"无效的根键: {root_key_name}",
                    "message": f"Invalid root key: {root_key_name}"
                }
            
            # 转换值类型
            value_type = None
            if value_type_name:
                try:
                    value_type = RegistryValueType[value_type_name]
                except KeyError:
                    return {
                        "success": False,
                        "error": f"无效的值类型: {value_type_name}",
                        "message": f"Invalid value type: {value_type_name}"
                    }
            
            # 执行操作
            if operation == "REGISTRY_READ" or operation == "read_key":
                result = self.registry_manager.read_key(root_key, key_path)
                if result:
                    return {
                        "success": True,
                        "message": "读取成功",
                        "key_data": {
                            "name": result.name,
                            "full_path": result.full_path,
                            "sub_keys": result.sub_keys,
                            "values": [
                                {
                                    "name": v.name,
                                    "type": v.type.name,
                                    "data": self.registry_manager._convert_value_to_string(v.data, v.type),
                                    "size": v.size
                                }
                                for v in result.values
                            ],
                            "last_modified": result.last_modified,
                            "sub_key_count": result.sub_key_count,
                            "value_count": result.value_count
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": "读取注册表键失败",
                        "message": "Failed to read registry key"
                    }
            
            elif operation == "REGISTRY_WRITE" or operation == "write_value":
                if not value_name or value_data is None or not value_type:
                    return {
                        "success": False,
                        "error": "写入操作需要值名称、值数据和值类型",
                        "message": "Write operation requires value name, data and type"
                    }
                
                success = self.registry_manager.write_value(
                    root_key, key_path, value_name, str(value_data), 
                    value_type, create_backup
                )
                
                if success:
                    return {
                        "success": True,
                        "message": "写入成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "写入注册表值失败",
                        "message": "Failed to write registry value"
                    }
            
            elif operation == "REGISTRY_DELETE" or operation == "delete_value":
                if not value_name:
                    return {
                        "success": False,
                        "error": "删除值操作需要值名称",
                        "message": "Delete value operation requires value name"
                    }
                
                success = self.registry_manager.delete_value(
                    root_key, key_path, value_name, create_backup
                )
                
                if success:
                    return {
                        "success": True,
                        "message": "删除值成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "删除注册表值失败",
                        "message": "Failed to delete registry value"
                    }
            
            elif operation == "REGISTRY_CREATE_KEY" or operation == "create_key":
                success = self.registry_manager.create_key(root_key, key_path)
                
                if success:
                    return {
                        "success": True,
                        "message": "创建键成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "创建注册表键失败",
                        "message": "Failed to create registry key"
                    }
            
            elif operation == "REGISTRY_DELETE_KEY" or operation == "delete_key":
                success = self.registry_manager.delete_key(root_key, key_path, create_backup)
                
                if success:
                    return {
                        "success": True,
                        "message": "删除键成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "删除注册表键失败",
                        "message": "Failed to delete registry key"
                    }
            
            elif operation == "REGISTRY_ENUMERATE" or operation == "enumerate":
                # 这与read_key相同
                result = self.registry_manager.read_key(root_key, key_path)
                if result:
                    return {
                        "success": True,
                        "message": "枚举成功",
                        "key_data": {
                            "name": result.name,
                            "full_path": result.full_path,
                            "sub_keys": result.sub_keys,
                            "values": [
                                {
                                    "name": v.name,
                                    "type": v.type.name,
                                    "data": self.registry_manager._convert_value_to_string(v.data, v.type),
                                    "size": v.size
                                }
                                for v in result.values
                            ],
                            "last_modified": result.last_modified,
                            "sub_key_count": result.sub_key_count,
                            "value_count": result.value_count
                        }
                    }
                else:
                    return {
                        "success": False,
                        "error": "枚举注册表键失败",
                        "message": "Failed to enumerate registry key"
                    }
            
            elif operation == "REGISTRY_BACKUP" or operation == "backup":
                backup_reason = operation_data.get("backup_reason", "手动备份")
                backup = self.registry_manager.create_backup(root_key, key_path, backup_reason)
                
                if backup:
                    return {
                        "success": True,
                        "message": "备份成功",
                        "backup_id": backup.backup_id,
                        "backup_file": backup.file_path
                    }
                else:
                    return {
                        "success": False,
                        "error": "创建注册表备份失败",
                        "message": "Failed to create registry backup"
                    }
            
            elif operation == "REGISTRY_SEARCH" or operation == "search":
                search_pattern = operation_data.get("search_pattern", "")
                search_keys = operation_data.get("search_keys", True)
                search_values = operation_data.get("search_values", True)
                search_data = operation_data.get("search_data", True)
                max_depth = operation_data.get("max_depth", 5)
                max_results = operation_data.get("max_results", 50)
                
                results = self.registry_manager.search_registry(
                    root_key, key_path, search_pattern, search_keys,
                    search_values, search_data, max_depth, max_results
                )
                
                return {
                    "success": True,
                    "message": "搜索完成",
                    "search_results": [
                        {
                            "path": r["path"],
                            "match_type": r["match_type"],
                            "match_text": r["match_text"],
                            "value": {
                                "name": r["value"].name,
                                "type": r["value"].type.name,
                                "data": self.registry_manager._convert_value_to_string(r["value"].data, r["value"].type),
                                "size": r["value"].size
                            } if r["value"] else None
                        }
                        for r in results
                    ],
                    "total_results": len(results)
                }
            
            else:
                return {
                    "success": False,
                    "error": f"不支持的操作类型: {operation}",
                    "message": f"Unsupported operation: {operation}"
                }
        
        except Exception as e:
            logger.error(f"处理注册表操作时发生错误: {e}")
            return {
                "success": False,
                "error": f"操作执行失败: {str(e)}",
                "message": f"Operation failed: {str(e)}"
            }
    
    def get_registry_operation_log(self) -> list:
        """获取注册表操作日志"""
        if self.registry_manager is None:
            return []
        
        try:
            return self.registry_manager.get_operation_log()
        except Exception as e:
            logger.error(f"获取注册表操作日志失败: {e}")
            return []


if __name__ == "__main__":
    """测试信息采集功能"""
    try:
        collector = WindowsCollector()
        
        print("\n===== 硬件信息 =====")
        hardware_info = collector.collect_hardware_info()
        print(f"CPU: {hardware_info['cpu_model']} ({hardware_info['cpu_cores']} cores)")
        print(f"内存: {hardware_info['memory_total'] / 1024 / 1024:.2f} GB")
        print(f"厂商: {hardware_info['manufacturer']}")
        print(f"型号: {hardware_info['model']}")
        print(f"序列号: {hardware_info['serial_number']}")
        
        print("\n===== 操作系统信息 =====")
        os_info = collector.collect_os_info()
        print(f"系统: {os_info['name']} ({os_info['architecture']})")
        print(f"版本: {os_info['version']} (Build {os_info['build']})")
        print(f"防火墙: {'启用' if os_info['security']['firewall_enabled'] else '禁用'}")
        print(f"杀毒软件: {os_info['security']['antivirus']} ({'启用' if os_info['security']['antivirus_enabled'] else '禁用'})")
        
        print("\n===== 网络信息 =====")
        network_info = collector.collect_network_info()
        print(f"主机名: {network_info['hostname']}")
        print(f"域: {network_info['domain']}")
        print(f"默认网关: {network_info['default_gateway']}")
        print(f"DNS服务器: {', '.join(network_info['dns_servers'])}")
        
        print("\n===== 用户信息 =====")
        user_info = collector.collect_user_info()
        print(f"用户名: {user_info['username']}")
        print(f"全名: {user_info['full_name']}")
        print(f"登录时间: {user_info['login_time']}")
        print(f"域: {user_info['domain']}")
        
        print("\n===== 软件信息 =====")
        software_list = collector.collect_software_info()
        print(f"共 {len(software_list)} 个已安装软件")
        for i, software in enumerate(software_list[:5]):  # 仅显示前5个
            print(f"{i+1}. {software['name']} {software['version']} ({software['publisher']})")
        if len(software_list) > 5:
            print(f"... 还有 {len(software_list) - 5} 个软件")
            
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}") 