<template>
  <div class="custom-field-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <el-card class="form-section">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-form-item label="字段名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入字段名称，用于程序调用"
            :disabled="mode === 'edit'"
            maxlength="50"
            show-word-limit
          />
          <div class="form-help">
            字段名称用于程序调用，只能包含字母、数字和下划线，创建后不可修改
          </div>
        </el-form-item>

        <el-form-item label="字段标签" prop="label">
          <el-input
            v-model="formData.label"
            placeholder="请输入字段标签，用于界面显示"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="字段类型" prop="field_type">
          <el-select
            v-model="formData.field_type"
            placeholder="请选择字段类型"
            :disabled="mode === 'edit'"
            style="width: 100%"
            @change="handleFieldTypeChange"
          >
            <el-option
              v-for="option in FIELD_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <el-icon class="mr-2"><component :is="option.icon" /></el-icon>
              {{ option.label }}
            </el-option>
          </el-select>
          <div class="form-help">
            字段类型决定了数据的输入方式和存储格式，创建后不可修改
          </div>
        </el-form-item>

        <el-form-item label="适用范围" prop="applies_to">
          <el-select v-model="formData.applies_to" placeholder="请选择适用范围">
            <el-option
              v-for="option in APPLIES_TO_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="字段描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入字段描述，可选"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="是否必填" prop="is_required">
              <el-switch v-model="formData.is_required" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否启用" prop="is_active">
              <el-switch v-model="formData.is_active" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序顺序" prop="sort_order">
              <el-input-number
                v-model="formData.sort_order"
                :min="0"
                :max="9999"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="默认值" prop="default_value">
          <el-input
            v-model="formData.default_value"
            placeholder="请输入默认值，可选"
            maxlength="255"
          />
        </el-form-item>
      </el-card>

      <!-- 字段选项配置 -->
      <el-card v-if="needsOptions" class="form-section">
        <template #header>
          <span>字段选项配置</span>
        </template>

        <!-- 选择类型字段的选项配置 -->
        <template v-if="['select', 'radio', 'checkbox'].includes(formData.field_type)">
          <el-form-item label="选项列表" prop="options.choices">
            <div class="choices-config">
              <div
                v-for="(choice, index) in choices"
                :key="index"
                class="choice-item"
              >
                <el-input
                  v-model="choice.label"
                  placeholder="选项标签"
                  class="choice-label"
                />
                <el-input
                  v-model="choice.value"
                  placeholder="选项值"
                  class="choice-value"
                />
                <el-checkbox v-model="choice.disabled" class="choice-disabled">
                  禁用
                </el-checkbox>
                <el-button
                  type="danger"
                  size="small"
                  :disabled="choices.length <= 1"
                  @click="removeChoice(index)"
                >
                  删除
                </el-button>
              </div>
              <el-button type="primary" @click="addChoice">
                <el-icon><Plus /></el-icon>
                添加选项
              </el-button>
            </div>
          </el-form-item>

          <el-form-item v-if="formData.field_type === 'select'" label="多选模式">
            <el-switch v-model="formData.options.multiple" />
            <div class="form-help">
              开启后用户可以选择多个选项
            </div>
          </el-form-item>
        </template>

        <!-- 文件上传字段配置 -->
        <template v-if="formData.field_type === 'file'">
          <el-form-item label="接受文件类型">
            <el-select
              v-model="fileAcceptType"
              placeholder="请选择文件类型"
              @change="handleFileAcceptChange"
            >
              <el-option label="所有文件" value="*" />
              <el-option label="图片文件" value="image/*" />
              <el-option label="文档文件" value="document" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="fileAcceptType === 'custom'" label="自定义类型">
            <el-input
              v-model="formData.options.accept"
              placeholder="例如: .jpg,.png,.pdf"
            />
            <div class="form-help">
              多个类型用逗号分隔，例如：.jpg,.png,.pdf 或 image/*,application/pdf
            </div>
          </el-form-item>

          <el-form-item label="文件大小限制">
            <el-input-number
              v-model="fileSizeLimit"
              :min="1"
              :max="100"
              controls-position="right"
              style="width: 200px"
            />
            <el-select
              v-model="fileSizeUnit"
              style="width: 80px; margin-left: 8px"
              @change="updateFileSize"
            >
              <el-option label="KB" value="KB" />
              <el-option label="MB" value="MB" />
            </el-select>
          </el-form-item>

          <el-form-item label="多文件上传">
            <el-switch v-model="formData.options.multiple" />
          </el-form-item>
        </template>

        <!-- 文本域配置 -->
        <template v-if="formData.field_type === 'textarea'">
          <el-form-item label="显示行数">
            <el-input-number
              v-model="formData.options.rows"
              :min="2"
              :max="20"
              controls-position="right"
              style="width: 200px"
            />
          </el-form-item>
        </template>

        <!-- 数字字段配置 -->
        <template v-if="formData.field_type === 'number'">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="最小值">
                <el-input-number
                  v-model="formData.options.min"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大值">
                <el-input-number
                  v-model="formData.options.max"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="步长">
                <el-input-number
                  v-model="formData.options.step"
                  :min="0.01"
                  :step="0.01"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 通用配置 -->
        <el-form-item label="占位符">
          <el-input
            v-model="formData.options.placeholder"
            placeholder="输入框的占位提示文本"
          />
        </el-form-item>
      </el-card>

      <!-- 验证规则配置 -->
      <el-card class="form-section">
        <template #header>
          <span>验证规则配置</span>
        </template>

        <template v-if="['text', 'textarea'].includes(formData.field_type)">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="最小长度">
                <el-input-number
                  v-model="formData.validation_rules.min_length"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大长度">
                <el-input-number
                  v-model="formData.validation_rules.max_length"
                  :min="1"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <template v-if="formData.field_type === 'number'">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="最小值">
                <el-input-number
                  v-model="formData.validation_rules.min_value"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大值">
                <el-input-number
                  v-model="formData.validation_rules.max_value"
                  controls-position="right"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <el-form-item label="正则表达式">
          <el-input
            v-model="formData.validation_rules.pattern"
            placeholder="自定义验证正则表达式，可选"
          />
        </el-form-item>

        <el-form-item label="自定义错误信息">
          <el-input
            v-model="formData.validation_rules.custom_message"
            placeholder="验证失败时的错误提示，可选"
          />
        </el-form-item>
      </el-card>

      <!-- 表单操作 -->
      <div class="form-actions">
        <el-button @click="$emit('cancel')">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { customFieldUtils } from '@/api/custom_field'
import type {
  CustomField,
  CustomFieldCreate,
  CustomFieldUpdate,
  FieldOptions,
  ValidationRules
} from '@/types/custom_field'
import {
  FIELD_TYPE_OPTIONS,
  APPLIES_TO_OPTIONS,
  AppliesTo,
  FieldType
} from '@/types/custom_field'

interface Props {
  initialData?: CustomField | null
  mode: 'create' | 'edit'
}

interface Emits {
  (e: 'submit', data: CustomFieldCreate | CustomFieldUpdate): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用和状态
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<{
  name: string
  label: string
  field_type: string
  description?: string
  is_required: boolean
  default_value?: string
  options: FieldOptions
  validation_rules: ValidationRules
  sort_order: number
  is_active: boolean
  applies_to: AppliesTo
}>({
  name: '',
  label: '',
  field_type: '',
  description: '',
  is_required: false,
  default_value: '',
  options: {},
  validation_rules: {},
  sort_order: 0,
  is_active: true,
  applies_to: AppliesTo.ASSET
})

// 选择类型字段的选项配置
const choices = ref<Array<{ label: string; value: string; disabled: boolean }>>([
  { label: '选项1', value: 'option1', disabled: false },
  { label: '选项2', value: 'option2', disabled: false }
])

// 文件类型和大小配置
const fileAcceptType = ref('*')
const fileSizeLimit = ref(5)
const fileSizeUnit = ref('MB')

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    { min: 2, max: 50, message: '字段名称长度应在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段名称只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  label: [
    { required: true, message: '请输入字段标签', trigger: 'blur' },
    { min: 2, max: 100, message: '字段标签长度应在 2 到 100 个字符', trigger: 'blur' }
  ],
  field_type: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ],
  applies_to: [
    { required: true, message: '请选择适用范围', trigger: 'change' }
  ]
}

// 计算属性
const needsOptions = computed(() => {
  return ['select', 'radio', 'checkbox', 'file', 'textarea', 'number'].includes(formData.field_type)
})

// ============ 工具函数定义 ============

// 重置表单 - 移到前面定义
const resetForm = () => {
  formData.name = ''
  formData.label = ''
  formData.field_type = 'text'
  formData.description = ''
  formData.is_required = false
  formData.default_value = ''
  formData.options = {}
  formData.validation_rules = {}
  formData.sort_order = 0
  formData.is_active = true
  formData.applies_to = AppliesTo.BOTH
  
  choices.value = [{ label: '', value: '', disabled: false }]
  fileAcceptType.value = '*'
  fileSizeLimit.value = 5
  fileSizeUnit.value = 'MB'
}

// 文件配置处理
const updateFileSize = () => {
  const multiplier = fileSizeUnit.value === 'MB' ? 1048576 : 1024
  formData.options.max_size = fileSizeLimit.value * multiplier
}

// 处理文件字段初始化
const handleFileFieldInit = (options: any) => {
  if (options) {
    const accept = options.accept || '*'
    if (accept === '*') {
      fileAcceptType.value = '*'
    } else if (accept === 'image/*') {
      fileAcceptType.value = 'image/*'
    } else if (accept.includes('.doc') || accept.includes('.pdf')) {
      fileAcceptType.value = 'document'
    } else {
      fileAcceptType.value = 'custom'
    }
    
    // 处理文件大小
    const maxSize = options.max_size || 5242880 // 5MB
    if (maxSize >= 1048576) {
      fileSizeLimit.value = Math.round(maxSize / 1048576)
      fileSizeUnit.value = 'MB'
    } else {
      fileSizeLimit.value = Math.round(maxSize / 1024)
      fileSizeUnit.value = 'KB'
    }
  }
}

// 文件配置处理
const handleFileAcceptChange = (value: string) => {
  switch (value) {
    case '*':
      formData.options.accept = '*'
      break
    case 'image/*':
      formData.options.accept = 'image/*'
      break
    case 'document':
      formData.options.accept = '.doc,.docx,.pdf,.txt,.xls,.xlsx,.ppt,.pptx'
      break
    case 'custom':
      formData.options.accept = ''
      break
  }
}

// 选项管理
const addChoice = () => {
  choices.value.push({
    label: `选项${choices.value.length + 1}`,
    value: `option${choices.value.length + 1}`,
    disabled: false
  })
}

const removeChoice = (index: number) => {
  choices.value.splice(index, 1)
}

// ============ 事件处理函数 ============

// 字段类型变化处理
const handleFieldTypeChange = (fieldType: string) => {
  // 重置选项配置
  formData.options = customFieldUtils.getDefaultOptionsForType(fieldType)
  formData.validation_rules = customFieldUtils.getDefaultValidationForType(fieldType)

  // 重置选择选项
  if (['select', 'radio', 'checkbox'].includes(fieldType)) {
    choices.value = [
      { label: '选项1', value: 'option1', disabled: false },
      { label: '选项2', value: 'option2', disabled: false }
    ]
  }

  // 重置文件配置
  if (fieldType === FieldType.FILE) {
    fileAcceptType.value = 'image/*'
    fileSizeLimit.value = 5
    fileSizeUnit.value = 'MB'
    updateFileSize()
  }
}

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 处理选择类型字段的选项
    if (['select', 'radio', 'checkbox'].includes(formData.field_type)) {
      formData.options.choices = choices.value.filter(choice => 
        choice.label.trim() && choice.value.trim()
      )
      
      if (formData.options.choices.length === 0) {
        ElMessage.error('选择类型字段至少需要一个有效选项')
        return
      }
    }

    submitting.value = true
    
    const submitData = {
      name: formData.name,
      label: formData.label,
      field_type: formData.field_type as FieldType,
      description: formData.description,
      is_required: formData.is_required,
      default_value: formData.default_value,
      options: formData.options,
      validation_rules: formData.validation_rules,
      sort_order: formData.sort_order,
      is_active: formData.is_active,
      applies_to: formData.applies_to
    }

    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// ============ 监听器和生命周期 ============

// 监听文件大小变化
watch([fileSizeLimit, fileSizeUnit], () => {
  updateFileSize()
})

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    formData.name = newData.name
    formData.label = newData.label
    formData.field_type = newData.field_type
    formData.description = newData.description || ''
    formData.is_required = newData.is_required
    formData.default_value = newData.default_value || ''
    formData.options = newData.options ? { ...newData.options } : {}
    formData.validation_rules = newData.validation_rules ? { ...newData.validation_rules } : {}
    formData.sort_order = newData.sort_order
    formData.is_active = newData.is_active
    formData.applies_to = newData.applies_to

    // 处理选择类型字段的选项
    if (['select', 'radio', 'checkbox'].includes(newData.field_type) && newData.options?.choices) {
      choices.value = newData.options.choices.map(choice => ({
        label: choice.label,
        value: choice.value,
        disabled: choice.disabled || false
      }))
    }
    
    // 处理文件字段配置
    if (newData.field_type === 'file') {
      handleFileFieldInit(newData.options)
    }
  } else {
    resetForm()
  }
}, { immediate: true })

onMounted(() => {
  // 如果是创建模式，设置默认排序顺序
  if (props.mode === 'create') {
    formData.sort_order = Date.now() % 10000
  }
})
</script>

<style scoped>
.custom-field-form {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
}

.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.mr-2 {
  margin-right: 8px;
}

.choices-config {
  width: 100%;
}

.choice-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.choice-label {
  flex: 1;
}

.choice-value {
  flex: 1;
}

.choice-disabled {
  flex-shrink: 0;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style>