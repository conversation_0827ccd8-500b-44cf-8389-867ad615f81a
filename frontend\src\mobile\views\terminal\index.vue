<template>
  <div class="mobile-terminal-index">
    <!-- 功能菜单 -->
    <van-grid :column-num="2" :gutter="16">
      <van-grid-item
        icon="desktop-o"
        text="终端列表"
        @click="goToList"
      />
      <van-grid-item
        icon="plus"
        text="添加终端"
        @click="goToAdd"
      />
    </van-grid>
    
    <!-- 在线状态 -->
    <van-cell-group title="在线状态" inset>
      <van-cell title="在线终端" :value="statistics.onlineCount" />
      <van-cell title="离线终端" :value="statistics.offlineCount" />
      <van-cell title="总终端数" :value="statistics.totalCount" />
    </van-cell-group>
    
    <!-- 系统统计 -->
    <van-cell-group title="系统统计" inset>
      <van-cell title="Windows" :value="systemStats.windows" />
      <van-cell title="Linux" :value="systemStats.linux" />
      <van-cell title="macOS" :value="systemStats.macos" />
      <van-cell title="其他" :value="systemStats.other" />
    </van-cell-group>
    
    <!-- 快速操作 -->
    <van-cell-group title="快速操作" inset>
      <van-cell title="批量操作" is-link @click="goBatchOperation" />
      <van-cell title="命令历史" is-link @click="goCommandHistory" />
      <van-cell title="连接监控" is-link @click="goConnectionMonitor" />
    </van-cell-group>
    
    <!-- 最近活动 -->
    <van-cell-group title="最近活动" inset>
      <van-cell title="最后连接时间" :value="lastConnectionTime" />
      <van-cell title="活跃终端数" :value="activeTerminals" />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 统计数据
const statistics = ref({
  onlineCount: '--',
  offlineCount: '--',
  totalCount: '--'
})

// 系统统计
const systemStats = ref({
  windows: '--',
  linux: '--',
  macos: '--',
  other: '--'
})

// 活动信息
const lastConnectionTime = ref('--')
const activeTerminals = ref('--')

// 页面跳转
const goToList = () => {
  router.push('/m/terminal/list')
}

const goToAdd = () => {
  showToast('添加功能开发中')
}

const goBatchOperation = () => {
  showToast('批量操作开发中')
}

const goCommandHistory = () => {
  showToast('命令历史开发中')
}

const goConnectionMonitor = () => {
  showToast('连接监控开发中')
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 模拟加载统计数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    statistics.value = {
      onlineCount: '12',
      offlineCount: '18',
      totalCount: '30'
    }
    systemStats.value = {
      windows: '18',
      linux: '8',
      macos: '3',
      other: '1'
    }
    lastConnectionTime.value = '2024-01-15 15:45:00'
    activeTerminals.value = '8'
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.mobile-terminal-index {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .van-grid {
    margin-bottom: 16px;
  }
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}
</style> 