# 邮箱登录权限管理功能说明

## 功能概述

本功能基于腾讯企业邮箱API实现了对用户登录权限的精细化管理，允许管理员控制用户是否可以使用POP、IMAP等协议访问邮箱。

## 支持的权限类型

根据腾讯企业邮箱API文档，支持以下4种权限类型：

1. **强制启用安全登录** (type=1)
   - 强制用户使用微信动态码登录
   - 默认值：关闭 (0)

2. **IMAP/SMTP服务** (type=2)
   - 允许使用IMAP/SMTP协议访问邮箱
   - 默认值：开启 (1)

3. **POP/SMTP服务** (type=3)
   - 允许使用POP/SMTP协议访问邮箱
   - 默认值：开启 (1)

4. **安全登录** (type=4)
   - 启用微信动态码验证
   - 默认值：关闭 (0)

## 数据库字段

在 `email_members` 表中新增了以下字段：

```sql
force_secure_login INTEGER DEFAULT 0 COMMENT '强制启用安全登录 (0:关闭, 1:开启)'
imap_smtp_enabled INTEGER DEFAULT 1 COMMENT 'IMAP/SMTP服务 (0:关闭, 1:开启)'
pop_smtp_enabled INTEGER DEFAULT 1 COMMENT 'POP/SMTP服务 (0:关闭, 1:开启)'
secure_login_enabled INTEGER DEFAULT 0 COMMENT '是否启用安全登录 (0:关闭, 1:开启)'
```

## API接口

### 1. 获取成员登录权限

```http
GET /api/v1/email/members/{userid}/login-permissions
```

**路径参数：**
- `userid`: 成员邮箱地址（对应腾讯企业邮箱API的userid字段）

**响应示例：**
```json
{
  "errcode": 0,
  "errmsg": "ok",
  "option": [
    {"type": 1, "value": "0"},
    {"type": 2, "value": "1"},
    {"type": 3, "value": "1"},
    {"type": 4, "value": "0"}
  ]
}
```

### 2. 更新成员登录权限

```http
PUT /api/v1/email/members/{userid}/login-permissions
```

**路径参数：**
- `userid`: 成员邮箱地址（对应腾讯企业邮箱API的userid字段）

**请求体：**
```json
{
  "force_secure_login": 0,
  "imap_smtp_enabled": 1,
  "pop_smtp_enabled": 1,
  "secure_login_enabled": 0
}
```

### 3. 同步成员登录权限

```http
POST /api/v1/email/members/{userid}/sync-login-permissions
```

**路径参数：**
- `userid`: 成员邮箱地址（对应腾讯企业邮箱API的userid字段）

从腾讯API获取最新权限设置并更新本地数据库。

## 前端功能

### 1. 成员创建时的权限设置

在创建成员时，表单中包含登录权限设置选项：
- 强制安全登录：默认关闭
- IMAP/SMTP服务：默认开启
- POP/SMTP服务：默认开启
- 安全登录：默认关闭

### 2. 权限管理对话框

在成员列表中点击"权限"按钮，可以打开权限管理对话框：
- 显示当前成员信息
- 显示当前权限设置状态
- 支持修改权限设置
- 支持从腾讯API同步最新权限

### 3. 操作按钮

- **保存**：将权限设置同步到腾讯企业邮箱
- **同步权限**：从腾讯企业邮箱获取最新权限设置

## 使用流程

### 创建用户时设置权限

1. 在成员管理页面点击"新增成员"
2. 填写基本信息
3. 在"登录权限设置"部分配置权限
4. 点击"确定"创建成员

系统会自动：
- 调用腾讯API创建用户
- 设置指定的登录权限
- 保存到本地数据库

### 管理现有用户权限

1. 在成员列表中找到目标用户
2. 点击操作列的"权限"按钮
3. 在权限管理对话框中：
   - 查看当前权限设置
   - 修改权限设置
   - 点击"保存"应用更改
   - 或点击"同步权限"获取最新设置

## 技术实现

### 后端实现

1. **数据模型**：在 `EmailMember` 模型中添加权限字段
2. **API服务**：在 `TencentEmailAPIService` 中实现权限管理方法
3. **路由处理**：在 `email.py` 路由中添加权限管理端点
4. **数据库迁移**：通过Alembic迁移添加新字段

### 前端实现

1. **API接口**：在 `email.ts` 中添加权限管理接口
2. **UI组件**：在成员管理页面添加权限管理对话框
3. **表单处理**：支持权限设置的创建和编辑

## API变更说明

### v2.0.0 重要变更

为了符合腾讯企业邮箱API规范并解决extid为空的问题，我们对权限管理API进行了以下重要变更：

#### 路径参数变更
- **旧版本**: 使用 `extid` 作为路径参数
  ```
  GET /api/v1/email/members/{extid}/login-permissions
  PUT /api/v1/email/members/{extid}/login-permissions
  POST /api/v1/email/members/{extid}/sync-login-permissions
  ```

- **新版本**: 使用 `userid`（邮箱地址）作为路径参数
  ```
  GET /api/v1/email/members/{userid}/login-permissions
  PUT /api/v1/email/members/{userid}/login-permissions
  POST /api/v1/email/members/{userid}/sync-login-permissions
  ```

#### 变更原因
1. **符合腾讯API规范**: 腾讯企业邮箱API要求使用 `userid`（邮箱地址）作为用户标识
2. **解决extid为空问题**: 很多用户的 `extid` 字段为空，导致API调用失败
3. **提高兼容性**: 邮箱地址作为唯一标识更加可靠

#### 迁移指南
前端调用需要从传递 `row.extid` 改为传递 `row.email`：

```javascript
// 旧版本
await emailMemberApi.getLoginPermissions(row.extid)

// 新版本
await emailMemberApi.getLoginPermissions(row.email)
```

## 注意事项

1. **默认设置**：新用户默认开启POP和IMAP服务，符合常见使用需求
2. **权限同步**：本地数据库和腾讯API的权限设置可能不同步，建议定期同步
3. **用户标识**：所有权限相关API现在使用邮箱地址（userid）而不是extid作为用户标识
4. **错误处理**：如果用户邮箱地址不存在，API会返回404错误

## 测试

可以使用 `test_login_permissions.py` 脚本测试权限管理功能：

```bash
cd backend
python test_login_permissions.py
```

该脚本会：
1. 验证腾讯API连接
2. 获取测试用户的当前权限
3. 设置新的权限配置
4. 验证权限设置是否生效

## 相关文档

- [腾讯企业邮箱API文档](https://exmail.qq.com/qy_mng_logic/doc#10001)
- [功能设置API](https://service.rtxmail.net/api/283.html)
- 项目邮箱管理模块文档：`邮箱管理模块修改总结.md` 