import time
import uuid
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger('app')

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        # 添加request_id到日志上下文
        logger_adapter = logging.LoggerAdapter(
            logger,
            {'request_id': request_id}
        )
        
        # 记录请求开始
        logger_adapter.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                'method': request.method,
                'path': request.url.path,
                'query_params': str(request.query_params),
                'client_ip': request.client.host
            }
        )
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # 记录请求结束
            logger_adapter.info(
                f"Request completed: {response.status_code}",
                extra={
                    'status_code': response.status_code,
                    'duration': duration
                }
            )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger_adapter.error(
                f"Request failed: {str(e)}",
                exc_info=True,
                extra={'duration': duration}
            )
            raise 