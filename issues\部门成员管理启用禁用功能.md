# 部门与成员管理启用禁用功能

## 任务概述
为部门与成员管理系统的操作按钮增加可以禁用和启用的操作功能。

## 实施方案
参考用户管理模块的实现，新增专用的状态切换接口，权限控制更精细，语义更明确。

## 实施计划
1. 后端接口开发
   - 新增 `EmailMemberActiveUpdate` 数据模型
   - 新增 `PATCH /email/members/{userid}/active` API接口
   - 权限验证：`email:member:active`

2. 前端API接口开发  
   - 新增 `updateMemberStatus` API函数

3. 前端界面更新
   - 操作下拉菜单添加启用/禁用按钮
   - 实现状态切换处理函数
   - 添加确认对话框
   - 权限控制：`Authority` 组件

## 执行状态
✅ 后端数据模型添加完成
✅ 后端API接口添加完成  
✅ 前端API函数添加完成
✅ 前端界面功能添加完成
⏳ 等待验证测试

## 关键文件修改
- `backend/app/schemas/email.py` - 添加EmailMemberActiveUpdate模型
- `backend/app/api/v1/email.py` - 添加状态切换API接口
- `frontend/src/api/email/member.ts` - 添加updateMemberStatus函数
- `frontend/src/views/email/DepartmentMemberManagement.vue` - 添加启用/禁用按钮和逻辑

## 权限要求
新增权限：`email:member:active` - 成员状态切换权限 