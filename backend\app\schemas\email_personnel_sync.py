"""
人员信息与邮箱同步相关的数据模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field


class NameMatchResult(BaseModel):
    """姓名匹配结果"""
    person_id: int = Field(..., description="人员ID")
    person_name: str = Field(..., description="人员姓名")
    job_number: str = Field(..., description="工号")
    dept_name: Optional[str] = Field(None, description="部门名称")
    similarity: float = Field(..., description="相似度(0-1)")
    match_type: str = Field(..., description="匹配类型: exact/similar")
    status: Optional[str] = Field(None, description="人员状态: 试用/正式/临时/试用延期/解聘/离职/退休/无效")


class ExtidCompletionResult(BaseModel):
    """工号补全结果"""
    email_member_id: int = Field(..., description="邮箱成员ID")
    email: str = Field(..., description="邮箱地址")
    name: str = Field(..., description="邮箱成员姓名")
    current_extid: Optional[str] = Field(None, description="当前工号")
    matches: List[NameMatchResult] = Field(default_factory=list, description="匹配结果列表")
    auto_match: Optional[NameMatchResult] = Field(None, description="自动匹配结果")
    status: str = Field(..., description="状态: pending/matched/manual/skipped")


class ExtidCompletionStats(BaseModel):
    """工号补全统计信息"""
    total_members: int = Field(..., description="总成员数")
    has_extid: int = Field(..., description="已有工号的成员数")
    missing_extid: int = Field(..., description="缺少工号的成员数")
    completion_rate: float = Field(..., description="完成率(%)")


class ManualMatchRequest(BaseModel):
    """手动匹配请求"""
    email_member_id: int = Field(..., description="邮箱成员ID")
    person_id: int = Field(..., description="选择的人员ID")
    job_number: str = Field(..., description="工号")


class ExtidCompletionBatchRequest(BaseModel):
    """批量工号补全请求"""
    similarity_threshold: float = Field(default=0.8, description="相似度阈值")
    auto_confirm_exact_match: bool = Field(default=True, description="自动确认精确匹配")
    dry_run: bool = Field(default=False, description="是否为试运行")


class ExtidCompletionBatchResult(BaseModel):
    """批量工号补全结果"""
    total_processed: int = Field(..., description="处理总数")
    auto_matched: int = Field(..., description="自动匹配数")
    manual_required: int = Field(..., description="需要手动处理数")
    skipped: int = Field(..., description="跳过数")
    errors: int = Field(..., description="错误数")
    results: List[ExtidCompletionResult] = Field(default_factory=list, description="详细结果")
    error_messages: List[str] = Field(default_factory=list, description="错误信息")


class ExtidCompletionPaginatedResponse(BaseModel):
    """工号补全候选者分页响应"""
    items: List[ExtidCompletionResult] = Field(..., description="候选者列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


class ManualExtidRequest(BaseModel):
    """手动设置工号请求"""
    email_member_id: int = Field(..., description="邮箱成员ID")
    job_number: str = Field(..., description="工号")
    person_name: Optional[str] = Field(None, description="人员姓名（可选，用于验证）")


class DataBackupInfo(BaseModel):
    """数据备份信息"""
    backup_id: str = Field(..., description="备份ID")
    backup_time: datetime = Field(..., description="备份时间")
    table_name: str = Field(..., description="表名")
    record_count: int = Field(..., description="记录数量")
    backup_file_path: str = Field(..., description="备份文件路径")
    description: str = Field(..., description="备份描述")


class DataBackupRequest(BaseModel):
    """数据备份请求"""
    tables: List[str] = Field(..., description="要备份的表名列表")
    description: str = Field(..., description="备份描述")


class DataRestoreRequest(BaseModel):
    """数据恢复请求"""
    backup_id: str = Field(..., description="备份ID")
    confirm: bool = Field(default=False, description="确认恢复")


class SyncConfig(BaseModel):
    """同步配置"""
    enabled: bool = Field(default=False, description="是否启用同步")
    sync_time: Optional[str] = Field(None, description="同步时间(HH:MM格式)")
    sync_interval: int = Field(default=24, description="同步间隔(小时)")
    auto_create_users: bool = Field(default=True, description="自动创建用户")
    auto_update_users: bool = Field(default=True, description="自动更新用户")
    auto_disable_users: bool = Field(default=True, description="自动禁用离职用户")
    last_sync_time: Optional[str] = Field(None, description="最后同步时间")


# 人员邮箱同步相关Schema
class PersonnelChangeType(str, Enum):
    """人员变更类型"""
    CREATE = "create"
    UPDATE = "update"
    DISABLE = "disable"


class PersonnelChangeRecord(BaseModel):
    """人员变更记录"""
    change_type: PersonnelChangeType
    job_number: str
    personnel_data: Dict[str, Any]
    email_data: Optional[Dict[str, Any]] = None
    reason: str


class PersonnelChangeDetectionResult(BaseModel):
    """人员变更检测结果"""
    success: bool
    changes: List[PersonnelChangeRecord] = []
    total_personnel: Optional[int] = None
    total_email_members: Optional[int] = None
    since_time: Optional[str] = None
    error_message: Optional[str] = None


class PersonnelSyncOperationResult(BaseModel):
    """人员同步操作结果"""
    success: bool
    change_type: PersonnelChangeType
    job_number: str
    message: Optional[str] = None
    error_message: Optional[str] = None
    # 新增部门操作信息
    department_operation: Optional[str] = None  # 'created', 'updated', 'failed', 'cached'
    department_name: Optional[str] = None
    department_id: Optional[str] = None


class PersonnelSyncStats(BaseModel):
    """人员同步统计"""
    processed_count: int = 0
    created_count: int = 0
    updated_count: int = 0
    disabled_count: int = 0
    error_count: int = 0
    # 新增部门操作统计
    departments_created: int = 0
    departments_updated: int = 0
    departments_failed: int = 0


class PersonnelSyncResult(BaseModel):
    """人员同步结果"""
    success: bool
    sync_log_id: Optional[int] = None
    stats: Optional[PersonnelSyncStats] = None
    operation_results: List[PersonnelSyncOperationResult] = []
    duration: Optional[str] = None
    error_message: Optional[str] = None
    dry_run: bool = False


class PersonnelSyncRequest(BaseModel):
    """人员同步请求"""
    full_sync: bool = Field(default=False, description="是否全量同步")
    dry_run: bool = Field(default=False, description="是否试运行")
    since_time: Optional[str] = Field(None, description="增量同步起始时间")


class PersonnelSyncConfigUpdate(BaseModel):
    """人员同步配置更新"""
    enabled: Optional[bool] = None
    sync_time: Optional[str] = None
    sync_interval: Optional[int] = None
    auto_create_users: Optional[bool] = None
    auto_update_users: Optional[bool] = None
    auto_disable_users: Optional[bool] = None
    auto_create_departments: Optional[bool] = None
    
    # 过滤配置字段
    filter_enabled: Optional[bool] = None
    included_companies: Optional[List[str]] = None
    included_departments: Optional[List[str]] = None
    included_job_titles: Optional[List[str]] = None
    excluded_job_titles: Optional[List[str]] = None
    filter_logic: Optional[str] = Field(None, pattern="^(AND|OR)$", description="过滤逻辑(AND/OR)")


class PersonnelSyncConfigResponse(BaseModel):
    """人员同步配置响应"""
    id: int
    enabled: bool
    sync_time: Optional[str]
    sync_interval: int
    auto_create_users: bool
    auto_update_users: bool
    auto_disable_users: bool
    auto_create_departments: bool
    
    # 过滤配置字段
    filter_enabled: bool
    included_companies: Optional[List[str]]
    included_departments: Optional[List[str]]
    included_job_titles: Optional[List[str]]
    excluded_job_titles: Optional[List[str]]
    filter_logic: str
    
    last_sync_time: Optional[str]
    next_sync_time: Optional[str]
    sync_status: str
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PersonnelSyncLogResponse(BaseModel):
    """人员同步日志响应"""
    id: int
    sync_type: str
    sync_category: str
    sync_id: Optional[str]
    operator: Optional[str]
    status: str
    message: Optional[str]
    synced_count: int
    processed_count: int
    created_count: int
    updated_count: int
    disabled_count: int
    error_count: int
    total_count: int
    duration: Optional[str]
    details: Optional[Dict[str, Any]]
    error_message: Optional[str]
    start_time: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class SyncConfigUpdate(BaseModel):
    """同步配置更新"""
    enabled: Optional[bool] = Field(None, description="是否启用同步")
    sync_time: Optional[str] = Field(None, description="同步时间(HH:MM格式)")
    sync_interval: Optional[int] = Field(None, description="同步间隔(小时)")
    auto_create_users: Optional[bool] = Field(None, description="自动创建用户")
    auto_update_users: Optional[bool] = Field(None, description="自动更新用户")
    auto_disable_users: Optional[bool] = Field(None, description="自动禁用离职用户")


class PersonnelSyncLog(BaseModel):
    """人员同步日志"""
    id: int
    sync_type: str = Field(..., description="同步类型")
    status: str = Field(..., description="状态")
    message: Optional[str] = Field(None, description="消息")
    processed_count: int = Field(default=0, description="处理数量")
    success_count: int = Field(default=0, description="成功数量")
    error_count: int = Field(default=0, description="错误数量")
    duration: Optional[str] = Field(None, description="耗时")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class RecompletionStrategy(BaseModel):
    """重新补齐策略"""
    strategy_type: str = Field(..., description="策略类型: full_reset/smart_recompletion/selective")
    similarity_threshold: float = Field(default=0.8, description="相似度阈值")
    backup_before_operation: bool = Field(default=True, description="操作前是否备份")
    batch_size: int = Field(default=50, description="批处理大小")


class RecompletionRequest(BaseModel):
    """重新补齐请求"""
    strategy: RecompletionStrategy = Field(..., description="重新补齐策略")
    target_member_ids: Optional[List[int]] = Field(None, description="指定成员ID列表（选择性重新补齐时使用）")
    dry_run: bool = Field(default=False, description="是否为试运行")


class RecompletionCandidate(BaseModel):
    """重新补齐候选者"""
    email_member_id: int = Field(..., description="邮箱成员ID")
    email: str = Field(..., description="邮箱地址")
    name: str = Field(..., description="成员姓名")
    current_extid: str = Field(..., description="当前工号")
    current_match_confidence: float = Field(..., description="当前匹配可信度")
    reason_for_recompletion: str = Field(..., description="重新补齐原因")
    new_matches: List[NameMatchResult] = Field(default_factory=list, description="新的匹配结果")
    recommended_action: str = Field(..., description="推荐操作: keep/update/manual")


class RecompletionAnalysisResult(BaseModel):
    """重新补齐分析结果"""
    total_members_with_extid: int = Field(..., description="有工号的成员总数")
    candidates_for_recompletion: int = Field(..., description="需要重新补齐的候选者数量")
    high_confidence_matches: int = Field(..., description="高可信度匹配数量")
    low_confidence_matches: int = Field(..., description="低可信度匹配数量")
    no_match_found: int = Field(..., description="找不到匹配的数量")
    candidates: List[RecompletionCandidate] = Field(default_factory=list, description="候选者详情")


class RecompletionResult(BaseModel):
    """重新补齐执行结果"""
    total_processed: int = Field(..., description="处理总数")
    kept_unchanged: int = Field(..., description="保持不变数量")
    updated_extid: int = Field(..., description="更新工号数量")
    cleared_extid: int = Field(..., description="清除工号数量")
    manual_review_required: int = Field(..., description="需要手动审核数量")
    errors: int = Field(..., description="错误数量")
    operation_duration: str = Field(..., description="操作耗时")
    backup_id: Optional[str] = Field(None, description="备份ID")
    detailed_results: List[Dict[str, Any]] = Field(default_factory=list, description="详细结果")
    error_messages: List[str] = Field(default_factory=list, description="错误信息")


class FilterPreviewRequest(BaseModel):
    """过滤预览请求"""
    filter_enabled: bool = Field(default=False, description="是否启用过滤")
    included_companies: Optional[List[str]] = Field(None, description="包含的公司列表")
    included_departments: Optional[List[str]] = Field(None, description="包含的部门列表")
    included_job_titles: Optional[List[str]] = Field(None, description="包含的职位列表")
    excluded_job_titles: Optional[List[str]] = Field(None, description="排除的职位列表")
    filter_logic: str = Field(default="AND", pattern="^(AND|OR)$", description="过滤逻辑")


class FilterPreviewResult(BaseModel):
    """过滤预览结果"""
    total_personnel: int = Field(..., description="全部人员数量")
    filtered_personnel: int = Field(..., description="过滤后人员数量")
    reduction_rate: float = Field(..., description="减少比例(%)")
    department_breakdown: Dict[str, int] = Field(default_factory=dict, description="部门分布")
    job_title_breakdown: Dict[str, int] = Field(default_factory=dict, description="职位分布")
    sample_personnel: List[Dict[str, Any]] = Field(default_factory=list, description="样本人员(最多10个)")


class DepartmentInfo(BaseModel):
    """部门信息"""
    dept_name: str = Field(..., description="部门名称")
    personnel_count: int = Field(..., description="人员数量")


class JobTitleInfo(BaseModel):
    """职位信息"""
    job_title_name: str = Field(..., description="职位名称")
    personnel_count: int = Field(..., description="人员数量")
