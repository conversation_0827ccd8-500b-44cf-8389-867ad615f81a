import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import logging
import time
import win32timezone
from pathlib import Path

# 配置日志
log_dir = os.path.join(os.environ.get('PROGRAMDATA', 'C:\\ProgramData'), 'TerminalAgent', 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'terminal_agent_service.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def _get_agent_version():
    """获取Agent版本号
    
    优先级：
    1. 打包时注入的版本文件 (__version__.py)
    2. 默认配置文件 (default_config.json)
    3. 硬编码默认值 (1.0.0)
    """
    try:
        # 优先级1: 从打包时注入的版本文件读取
        try:
            import __version__
            version = getattr(__version__, '__version__', None) or getattr(__version__, 'VERSION', None)
            if version:
                logger.info(f"从版本文件读取版本号: {version}")
                return version
        except ImportError:
            pass
        
        # 优先级2: 从默认配置文件读取
        default_config = 'default_config.json'
        if os.path.exists(default_config):
            try:
                import json
                with open(default_config, 'r') as f:
                    default_data = json.load(f)
                version = default_data.get('agent_version')
                if version:
                    logger.info(f"从默认配置读取版本号: {version}")
                    return version
            except Exception as e:
                logger.warning(f"读取默认配置版本失败: {e}")
        
        # 优先级3: 硬编码默认值
        logger.info("使用硬编码默认版本号: 1.0.0")
        return "1.0.0"
        
    except Exception as e:
        logger.error(f"获取Agent版本号失败: {e}，使用默认版本")
        return "1.0.0"


class TerminalAgentService(win32serviceutil.ServiceFramework):
    """Windows服务实现，用于将Agent作为系统服务运行"""

    _svc_name_ = "TerminalAgent"
    _svc_display_name_ = "终端管理Agent"
    _svc_description_ = "提供终端信息采集和管理功能的Agent服务"
    _svc_type_ = win32service.SERVICE_WIN32_OWN_PROCESS  # 服务类型
    _svc_start_type_ = win32service.SERVICE_AUTO_START  # 自动启动

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        self.is_running = False
        self.agent = None

        # 设置工作目录
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(self.base_dir)

        logger.info(f"服务初始化完成，工作目录: {self.base_dir}")

    def _get_agent_version(self):
        """获取Agent版本号（实例方法）"""
        return _get_agent_version()

    def SvcStop(self):
        """服务停止时调用"""
        logger.info("收到停止服务请求")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False

        # 停止Agent
        if self.agent:
            logger.info("正在停止Agent...")
            try:
                self.agent.shutdown()
                logger.info("Agent已成功停止")
            except Exception as e:
                logger.error(f"停止Agent时发生错误: {str(e)}")

    def SvcDoRun(self):
        """服务启动时调用"""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        logger.info("开始启动服务")
        self.is_running = True
        self.main()

    def main(self):
        """服务主函数"""
        try:
            logger.info("正在启动服务...")

            # 创建状态文件，表明服务正在运行
            status_dir = os.path.join(os.environ.get('PROGRAMDATA', 'C:\\ProgramData'), 'TerminalAgent')
            os.makedirs(status_dir, exist_ok=True)
            status_file = os.path.join(status_dir, 'service_status.txt')

            with open(status_file, 'w') as f:
                f.write(f"Service started at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"PID: {os.getpid()}\n")
                f.write(f"Working directory: {self.base_dir}\n")

            logger.info(f"服务状态文件已创建: {status_file}")

            # 初始化Agent
            try:
                logger.info("正在初始化Agent...")
                from agent_client import TerminalAgent
                self.agent = TerminalAgent()

                config_dir = os.path.join(os.environ.get('PROGRAMDATA', 'C:\\ProgramData'), 'TerminalAgent')
                os.makedirs(config_dir, exist_ok=True)
                config_file = os.path.join(config_dir, 'agent_config.json')

                # 检查配置文件是否存在
                if not os.path.exists(config_file):
                    logger.warning(f"配置文件 {config_file} 不存在，将创建默认配置")
                    # 如果没有配置文件，尝试从安装目录复制默认配置
                    default_config = os.path.join(self.base_dir, 'default_config.json')
                    if os.path.exists(default_config):
                        import shutil
                        shutil.copy2(default_config, config_file)
                        logger.info(f"已复制默认配置文件到 {config_file}")

                # 检查配置文件中的版本号
                try:
                    import json
                    with open(config_file, 'r') as f:
                        config_data = json.load(f)

                    # 获取当前程序版本
                    current_version = self._get_agent_version()

                    # 更新配置文件中的版本号
                    if config_data.get('agent_version') != current_version:
                        logger.info(f"更新配置文件中的版本号从 {config_data.get('agent_version')} 到 {current_version}")
                        config_data['agent_version'] = current_version
                        with open(config_file, 'w') as f:
                            json.dump(config_data, f, indent=4)
                except Exception as e:
                    logger.error(f"检查或更新配置文件版本号时出错: {str(e)}")

                success = self.agent.initialize(config_file)
                if success:
                    logger.info("Agent初始化成功")
                else:
                    logger.error("Agent初始化失败")
            except Exception as e:
                logger.error(f"初始化Agent时发生错误: {str(e)}")
                self.agent = None

            # 主循环
            counter = 0
            while self.is_running:
                # 检查是否有停止事件
                if win32event.WaitForSingleObject(self.hWaitStop, 5000) == win32event.WAIT_OBJECT_0:
                    break

                # 每分钟记录一次心跳
                counter += 1
                if counter % 12 == 0:  # 每分钟(12 * 5秒)
                    logger.info(f"服务心跳 - 运行时间: {counter//12} 分钟")
                    with open(status_file, 'a') as f:
                        f.write(f"Heartbeat at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

            logger.info("服务退出主循环")

        except Exception as e:
            logger.error(f"服务运行过程中发生错误: {str(e)}")
            self.is_running = False


def install_service():
    """安装服务"""
    try:
        if len(sys.argv) == 1:
            servicemanager.Initialize()
            servicemanager.PrepareToHostSingle(TerminalAgentService)
            servicemanager.StartServiceCtrlDispatcher()
        else:
            win32serviceutil.HandleCommandLine(TerminalAgentService)
    except Exception as e:
        logger.error(f"处理服务命令时发生错误: {str(e)}")


if __name__ == '__main__':
    """
    服务安装与管理入口

    使用方法:
        安装服务:   python windows_service.py install
        启动服务:   python windows_service.py start
        停止服务:   python windows_service.py stop
        重启服务:   python windows_service.py restart
        删除服务:   python windows_service.py remove
        直接运行:   python windows_service.py debug
    """

    if len(sys.argv) > 1 and sys.argv[1] == 'debug':
        # 调试模式，直接运行不作为服务
        logger.info("以调试模式启动Agent")
        try:
            from agent_client import TerminalAgent
            agent = TerminalAgent()

            config_dir = os.path.join(os.environ.get('PROGRAMDATA', 'C:\\ProgramData'), 'TerminalAgent')
            os.makedirs(config_dir, exist_ok=True)
            config_file = os.path.join(config_dir, 'agent_config.json')

            # 检查配置文件是否存在
            if not os.path.exists(config_file):
                logger.warning(f"配置文件 {config_file} 不存在，将创建默认配置")
                # 如果没有配置文件，尝试从当前目录复制默认配置
                default_config = 'default_config.json'
                if os.path.exists(default_config):
                    import shutil
                    shutil.copy2(default_config, config_file)
                    logger.info(f"已复制默认配置文件到 {config_file}")

            # 检查配置文件中的版本号
            try:
                import json
                with open(config_file, 'r') as f:
                    config_data = json.load(f)

                # 获取当前程序版本
                current_version = _get_agent_version()

                # 更新配置文件中的版本号
                if config_data.get('agent_version') != current_version:
                    logger.info(f"更新配置文件中的版本号从 {config_data.get('agent_version')} 到 {current_version}")
                    config_data['agent_version'] = current_version
                    with open(config_file, 'w') as f:
                        json.dump(config_data, f, indent=4)
            except Exception as e:
                logger.error(f"检查或更新配置文件版本号时出错: {str(e)}")

            success = agent.initialize(config_file)
            if success:
                logger.info("Agent初始化成功，按Ctrl+C终止程序")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("收到终止信号，正在停止Agent...")
                    agent.shutdown()
                    logger.info("Agent已停止")
            else:
                logger.error("Agent初始化失败")
        except Exception as e:
            logger.error(f"调试模式运行时发生错误: {str(e)}")
    else:
        # 作为服务运行
        install_service()