from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

class AssetNumberRule(BaseModel):
    """资产编号规则"""
    prefix: str = Field(..., description="编号前缀")
    number_length: int = Field(8, description="数字长度")
    start_number: int = Field(1, description="起始数字")

class AssetSettingsBase(BaseModel):
    """资产设置基础模型"""
    company: str = Field(..., description="公司")
    asset_number_rule: AssetNumberRule = Field(..., description="资产编号规则")

class AssetSettingsCreate(AssetSettingsBase):
    """创建资产设置模型"""
    pass

class AssetSettingsUpdate(BaseModel):
    """更新资产设置模型"""
    company: Optional[str] = None
    asset_number_rule: Optional[AssetNumberRule] = None

class AssetSettingsResponse(AssetSettingsBase):
    """资产设置响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 为了兼容性，提供别名
AssetSettings = AssetSettingsResponse

# 资产类型相关模型
class AssetTypeBase(BaseModel):
    """资产类型基础模型"""
    name: str = Field(..., description="类型名称")
    description: Optional[str] = Field(None, description="描述")

class AssetTypeCreate(AssetTypeBase):
    """创建资产类型模型"""
    pass

class AssetTypeUpdate(AssetTypeBase):
    """更新资产类型模型"""
    name: Optional[str] = None
    description: Optional[str] = None

class AssetTypeResponse(AssetTypeBase):
    """资产类型响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 资产状态相关模型
class AssetStatusBase(BaseModel):
    """资产状态基础模型"""
    name: str = Field(..., description="状态名称")
    description: Optional[str] = Field(None, description="描述")

class AssetStatusCreate(AssetStatusBase):
    """创建资产状态模型"""
    pass

class AssetStatusUpdate(AssetStatusBase):
    """更新资产状态模型"""
    name: Optional[str] = None
    description: Optional[str] = None

class AssetStatusResponse(AssetStatusBase):
    """资产状态响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 资产品牌相关模型
class AssetBrandBase(BaseModel):
    """资产品牌基础模型"""
    name: str = Field(..., description="品牌名称")
    description: Optional[str] = Field(None, description="描述")

class AssetBrandCreate(AssetBrandBase):
    """创建资产品牌模型"""
    pass

class AssetBrandUpdate(AssetBrandBase):
    """更新资产品牌模型"""
    name: Optional[str] = None
    description: Optional[str] = None

class AssetBrandResponse(AssetBrandBase):
    """资产品牌响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 资产型号相关模型
class AssetModelBase(BaseModel):
    """资产型号基础模型"""
    name: str = Field(..., description="型号名称")
    brand_id: int = Field(..., description="品牌ID")
    description: Optional[str] = Field(None, description="描述")

class AssetModelCreate(AssetModelBase):
    """创建资产型号模型"""
    pass

class AssetModelUpdate(AssetModelBase):
    """更新资产型号模型"""
    name: Optional[str] = None
    brand_id: Optional[int] = None
    description: Optional[str] = None

class AssetModelResponse(AssetModelBase):
    """资产型号响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    brand: Optional[AssetBrandResponse] = None

    class Config:
        from_attributes = True

# 为了兼容性，添加别名
AssetType = AssetTypeResponse
AssetStatus = AssetStatusResponse
AssetBrand = AssetBrandResponse
AssetModel = AssetModelResponse 