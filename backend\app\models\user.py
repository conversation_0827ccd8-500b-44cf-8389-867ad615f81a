from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String
from sqlalchemy.orm import relationship
from app.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    is_builtin = Column(Boolean, default=False, comment="是否为内置账号")
    
    # roles关联在role.py中通过backref定义 