import request from '@/utils/request'

// 获取邮箱配置列表
export const getEmailConfigs = (params?: { skip?: number; limit?: number }) => {
  return request.get('/email/configs', { params })
}

// 创建邮箱配置
export const createEmailConfig = (data: {
  corp_id: string
  corp_secret: string
  app_name?: string
  app_key?: string
  api_base_url?: string
  is_active?: boolean
}) => {
  return request.post('/email/configs', data)
}

// 更新邮箱配置
export const updateEmailConfig = (id: number, data: {
  corp_id?: string
  corp_secret?: string
  app_name?: string
  app_key?: string
  api_base_url?: string
  is_active?: boolean
}) => {
  return request.put(`/email/configs/${id}`, data)
}

// 删除邮箱配置
export const deleteEmailConfig = (id: number) => {
  return request.delete(`/email/configs/${id}`)
}

// 测试邮箱配置连接
export const testEmailConfig = (id: number) => {
  return request.post(`/email/configs/${id}/test`)
} 