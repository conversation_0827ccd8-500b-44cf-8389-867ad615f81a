# 移动端盘点记录搜索功能改进

## 问题描述
移动端盘点任务页面原有"扫码盘点"和"手动盘点"两个独立按钮，用户希望改为搜索框形式，并在搜索框右侧添加扫码图标。

## 解决方案
将原有的两个独立按钮替换为集成的搜索功能：
- 使用 Vant Search 组件作为主要搜索界面
- 利用 `#action` 插槽在右侧添加扫码图标
- 保持原有的扫码和搜索逻辑，但通过搜索框统一操作

## 修改内容

### 1. 模板修改 (`frontend/src/mobile/views/asset/InventoryTask.vue`)
- **移除**: 原有的快速操作按钮区域 (第65-80行)
- **新增**: 搜索功能区域，包含带扫码图标的搜索框

```vue
<!-- 搜索功能 -->
<div v-if="task.status === 'in_progress'" class="search-section">
  <van-search
    v-model="searchKeyword"
    placeholder="搜索资产编号或名称"
    shape="round"
    show-action
    @search="handleSearch"
    @clear="handleClearSearch"
    @input="handleSearchInput"
  >
    <template #action>
      <div class="scan-action" @click="handleScanClick">
        <van-icon name="scan" size="20" color="#1989fa" />
      </div>
    </template>
  </van-search>
</div>
```

### 2. 脚本逻辑修改
- **新增响应式数据**: `searchKeyword` 用于搜索关键词绑定
- **新增方法**:
  - `handleSearch(value: string)`: 处理搜索确认
  - `handleClearSearch()`: 清除搜索内容
  - `handleSearchInput(value: string)`: 处理搜索输入
  - `handleScanClick()`: 扫码图标点击处理
- **修改方法**: `startScan()` 现在将扫码结果直接填入搜索框

### 3. 样式修改
- **移除**: `.quick-actions` 样式
- **新增**: `.search-section` 样式，包含扫码图标的交互效果

```scss
.search-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
  
  .scan-action {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s;
    
    &:active {
      background-color: #f2f3f5;
    }
  }
  
  :deep(.van-search) {
    padding: 0;
    background: transparent;
    
    .van-search__content {
      background: #f7f8fa;
      border-radius: 18px;
    }
    
    .van-field__control {
      font-size: 14px;
    }
  }
}
```

## 功能特点
1. **界面简洁**: 减少了按钮数量，界面更清爽
2. **操作统一**: 搜索和扫码功能集成在一个区域
3. **交互直观**: 扫码图标位置明显，用户容易理解
4. **功能保持**: 保留了原有的所有盘点功能

## 用户体验改进
- 减少了界面元素，降低认知负担
- 扫码结果直接显示在搜索框中，操作更连贯
- 符合移动端常见的搜索+功能图标设计模式

## 技术实现
- 使用 Vant Search 组件的 `#action` 插槽自定义右侧操作
- 保持现有的 API 调用和数据处理逻辑
- 响应式设计，适配不同屏幕尺寸

## 测试要点
- [ ] 搜索框输入功能正常
- [ ] 扫码图标点击触发扫码功能
- [ ] 扫码结果正确填入搜索框
- [ ] 清除搜索功能正常
- [ ] 样式在不同设备上显示正常
- [ ] 原有的盘点记录操作功能不受影响

## 完成状态
✅ 模板修改完成
✅ 脚本逻辑实现完成  
✅ 样式适配完成
✅ 问题记录文档完成

修改已完成，用户可以测试新的搜索+扫码集成功能。 