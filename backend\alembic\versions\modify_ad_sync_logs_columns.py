"""modify_ad_sync_logs_columns

Revision ID: modify_ad_sync_logs_columns
Revises: 350233421c24
Create Date: 2025-01-15 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'modify_ad_sync_logs_columns'
down_revision: Union[str, None] = '350233421c24'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """
    修改ad_sync_logs表的字段类型，将created_ous和renamed_ous从Integer改为JSON
    以支持存储详细的组织单元信息
    """
    # 使用原生SQL来处理类型转换，先将Integer转换为Text，再转换为JSON
    op.execute("ALTER TABLE ad_sync_logs ALTER COLUMN created_ous TYPE JSON USING created_ous::text::json")
    op.execute("ALTER TABLE ad_sync_logs ALTER COLUMN renamed_ous TYPE JSON USING renamed_ous::text::json")
    
    # 更新字段注释
    op.alter_column('ad_sync_logs', 'created_ous',
                   existing_type=postgresql.JSON(astext_type=sa.Text()),
                   comment='创建的组织单位详细信息（JSON格式）')
    
    op.alter_column('ad_sync_logs', 'renamed_ous',
                   existing_type=postgresql.JSON(astext_type=sa.Text()),
                   comment='重命名的组织单位详细信息（JSON格式）')


def downgrade() -> None:
    """
    回滚操作：将JSON字段改回Integer类型
    注意：这会导致数据丢失，仅用于紧急回滚
    """
    # 使用原生SQL来处理类型转换，从JSON转换回Integer
    op.execute("ALTER TABLE ad_sync_logs ALTER COLUMN renamed_ous TYPE INTEGER USING (renamed_ous::text)::integer")
    op.execute("ALTER TABLE ad_sync_logs ALTER COLUMN created_ous TYPE INTEGER USING (created_ous::text)::integer")
    
    # 恢复字段注释
    op.alter_column('ad_sync_logs', 'renamed_ous',
                   existing_type=sa.Integer(),
                   comment='重命名的组织单位数')
    
    op.alter_column('ad_sync_logs', 'created_ous',
                   existing_type=sa.Integer(),
                   comment='创建的组织单位数') 