// 泛微用户数据类型
export interface EcologyUser {
  id?: number
  user_id?: number
  dept_id: number
  dept_name: string
  dept_hierarchy: string
  level: number
  dept_path: string
  company_id?: number
  company_name?: string
  user_name?: string
  job_number?: string
  mobile?: string
  email?: string
  job_title?: number
  job_title_name?: string
  gender?: string
  status?: string
  created_at?: string
  updated_at?: string
  
  // 兼容原有API返回的字段名
  DeptID?: number
  DeptName?: string
  DeptHierarchy?: string
  Level?: number
  DeptPath?: string
  CompanyID?: number
  CompanyName?: string
  UserID?: number
  UserName?: string
  JobNumber?: string
  Mobile?: string
  Email?: string
  JobTitle?: number
  JobTitleName?: string
  Gender?: string
  Status?: string
}

// 同步配置类型
export interface SyncConfig {
  sync_hour: number
  sync_time: string
  last_sync_time: string | null
  next_sync_time: string | null
  sync_status: string
  error_message: string | null
} 