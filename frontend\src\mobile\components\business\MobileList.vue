<template>
  <div class="mobile-list">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :finished-text="finishedText"
        :error="error"
        :error-text="errorText"
        @load="onLoad"
      >
        <template #default>
          <slot :items="items" :loading="loading" :refreshing="refreshing">
            <van-cell
              v-for="item in items"
              :key="getItemKey(item)"
              :title="getItemTitle(item)"
              :value="getItemValue(item)"
              :label="getItemLabel(item)"
              :is-link="clickable"
              @click="handleItemClick(item)"
            />
          </slot>
        </template>
        
        <template #loading>
          <slot name="loading">
            <div class="loading-container">
              <van-loading size="16">加载中...</van-loading>
            </div>
          </slot>
        </template>
        
        <template #finished>
          <slot name="finished">
            <div class="finished-text">{{ finishedText }}</div>
          </slot>
        </template>
        
        <template #error>
          <slot name="error">
            <div class="error-container">
              <van-icon name="warning-o" size="16" />
              <span>{{ errorText }}</span>
              <van-button size="mini" @click="onLoad">重试</van-button>
            </div>
          </slot>
        </template>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  // 数据源
  items?: any[]
  // 是否正在加载
  loading?: boolean
  // 是否已加载完成
  finished?: boolean
  // 是否出错
  error?: boolean
  // 完成文本
  finishedText?: string
  // 错误文本
  errorText?: string
  // 是否可点击
  clickable?: boolean
  // 获取item的key
  itemKey?: string | ((item: any) => string | number)
  // 获取item的标题
  itemTitle?: string | ((item: any) => string)
  // 获取item的值
  itemValue?: string | ((item: any) => string)
  // 获取item的标签
  itemLabel?: string | ((item: any) => string)
  // 自动加载
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  loading: false,
  finished: false,
  error: false,
  finishedText: '没有更多了',
  errorText: '加载失败，点击重试',
  clickable: true,
  itemKey: 'id',
  itemTitle: 'title',
  itemValue: 'value',
  itemLabel: 'label',
  autoLoad: true
})

const emit = defineEmits<{
  'load': []
  'refresh': []
  'item-click': [item: any]
  'update:loading': [loading: boolean]
  'update:finished': [finished: boolean]
  'update:error': [error: boolean]
}>()

const refreshing = ref(false)
const internalLoading = ref(props.loading)
const internalFinished = ref(props.finished)
const internalError = ref(props.error)

// 计算属性
const loading = computed({
  get: () => props.loading ?? internalLoading.value,
  set: (value) => {
    internalLoading.value = value
    emit('update:loading', value)
  }
})

const finished = computed({
  get: () => props.finished ?? internalFinished.value,
  set: (value) => {
    internalFinished.value = value
    emit('update:finished', value)
  }
})

const error = computed({
  get: () => props.error ?? internalError.value,
  set: (value) => {
    internalError.value = value
    emit('update:error', value)
  }
})

// 获取item属性的通用方法
const getItemKey = (item: any): string | number => {
  if (typeof props.itemKey === 'function') {
    return props.itemKey(item)
  }
  return item[props.itemKey] || item.id || Math.random()
}

const getItemTitle = (item: any): string => {
  if (typeof props.itemTitle === 'function') {
    return props.itemTitle(item)
  }
  return item[props.itemTitle] || ''
}

const getItemValue = (item: any): string => {
  if (typeof props.itemValue === 'function') {
    return props.itemValue(item)
  }
  return item[props.itemValue] || ''
}

const getItemLabel = (item: any): string => {
  if (typeof props.itemLabel === 'function') {
    return props.itemLabel(item)
  }
  return item[props.itemLabel] || ''
}

// 事件处理
const onLoad = () => {
  error.value = false
  emit('load')
}

const onRefresh = () => {
  finished.value = false
  error.value = false
  emit('refresh')
  
  // 模拟刷新完成
  setTimeout(() => {
    refreshing.value = false
  }, 1000)
}

const handleItemClick = (item: any) => {
  if (props.clickable) {
    emit('item-click', item)
  }
}

// 监听外部状态变化
watch(() => props.loading, (newVal) => {
  internalLoading.value = newVal
})

watch(() => props.finished, (newVal) => {
  internalFinished.value = newVal
})

watch(() => props.error, (newVal) => {
  internalError.value = newVal
})
</script>

<style lang="scss" scoped>
.mobile-list {
  height: 100%;
  overflow: hidden;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--van-padding-md);
  color: var(--van-text-color-2);
}

.finished-text {
  text-align: center;
  padding: var(--van-padding-md);
  color: var(--van-text-color-2);
  font-size: var(--van-font-size-sm);
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: var(--van-padding-md);
  color: var(--van-text-color-2);
  gap: var(--van-padding-sm);
  
  span {
    font-size: var(--van-font-size-sm);
  }
}
</style> 