import request from '@/utils/request'

// 获取成员列表
export const getMembers = (params?: {
  page?: number
  size?: number
  department_id?: string
  search?: string
}) => {
  return request.get('/email/members', { params })
}

// 创建成员
export const createMember = (data: {
  extid: string
  email: string
  name: string
  department_id: string
  password: string
  position?: string
  mobile?: string
  tel?: string
  pop_smtp_enabled?: boolean
  imap_smtp_enabled?: boolean
  secure_login_enabled?: boolean
  force_secure_login?: boolean
}, options?: { sync_to_api?: boolean }) => {
  return request.post('/email/members', data, {
    params: options
  })
}

// 更新成员
export const updateMember = (userid: string, data: {
  email?: string
  name?: string
  department_id?: string
  password?: string
  position?: string
  mobile?: string
  tel?: string
  pop_smtp_enabled?: boolean
  imap_smtp_enabled?: boolean
  secure_login_enabled?: boolean
  force_secure_login?: boolean
}, options?: { sync_to_api?: boolean }) => {
  return request.put(`/email/members/${userid}`, data, {
    params: options
  })
}

// 删除成员
export const deleteMember = (userid: string, options?: { sync_to_api?: boolean }) => {
  return request.delete(`/email/members/${userid}`, {
    params: options
  })
}

// 获取成员详情
export const getMemberDetail = (userid: string) => {
  return request.get(`/email/members/${userid}`)
}

// 更新成员登录权限
export const updateMemberLoginPermissions = (userid: string, data: {
  force_secure_login?: number
  imap_smtp_enabled?: number
  pop_smtp_enabled?: number
  secure_login_enabled?: number
}, options?: { sync_to_api?: boolean }) => {
  return request.put(`/email/members/${userid}/login-permissions`, data, {
    params: options
  })
}

// 获取成员登录权限设置
export const getMemberLoginPermissions = (userid: string) => {
  return request.get(`/email/members/${userid}/login-permissions`)
}

// 同步成员登录权限设置（从腾讯API获取最新设置）
export const syncMemberLoginPermissions = (userid: string) => {
  return request.post(`/email/members/${userid}/sync-login-permissions`)
}

// 从API同步成员数据
export const syncMembersFromApi = (departmentId?: string) => {
  return request.post('/email/sync/members', {}, {
    params: departmentId ? { department_id: departmentId } : {},
    timeout: 600000  // 10分钟超时，邮箱成员同步是耗时操作
  })
}

// 批量获取成员登录权限设置
export const batchGetMemberLoginPermissions = (userids: string[]) => {
  return request.post('/email/members/batch-login-permissions', { userids })
}

// 导出成员数据
export const exportMembers = (params?: {
  search?: string
  department_id?: string
  format?: 'csv' | 'xlsx'
}) => {
  return request.get('/email/members/export', {
    params: {
      format: 'xlsx',
      ...params
    },
    responseType: 'blob'
  })
}

// 更新成员状态（启用/禁用）
export const updateMemberStatus = (userid: string, is_active: boolean) => {
  return request.patch(`/email/members/${userid}/active`, {
    is_active
  })
} 