from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from app.api.deps import get_db
from app.crud.field_value import field_value_crud
from app.schemas.field_value import (
    FieldValueCreate,
    FieldValueUpdate,
    FieldValueResponse
)
from pydantic import BaseModel

class FieldValueListResponse(BaseModel):
    data: List[FieldValueResponse]
    total: int

router = APIRouter()

@router.get("/", response_model=FieldValueListResponse)
def get_field_values(
    field_name: Optional[str] = None,
    keyword: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1),
    db: Session = Depends(get_db),
) -> FieldValueListResponse:
    """获取字段值列表"""
    field_values = field_value_crud.search(
        db,
        field_name=field_name,
        keyword=keyword,
        skip=skip,
        limit=limit
    )
    total = field_value_crud.get_total(
        db,
        field_name=field_name,
        keyword=keyword
    )
    return FieldValueListResponse(data=field_values, total=total)

@router.post("/", response_model=FieldValueResponse)
def create_field_value(
    field_value_in: FieldValueCreate,
    db: Session = Depends(get_db),
) -> FieldValueResponse:
    """创建字段值"""
    return field_value_crud.create(db, obj_in=field_value_in)

@router.get("/{field_value_id}", response_model=FieldValueResponse)
def get_field_value(
    field_value_id: int,
    db: Session = Depends(get_db),
) -> FieldValueResponse:
    """获取单个字段值"""
    return field_value_crud.get(db, id=field_value_id)

@router.put("/{field_value_id}", response_model=FieldValueResponse)
def update_field_value(
    field_value_id: int,
    field_value_in: FieldValueUpdate,
    db: Session = Depends(get_db),
) -> FieldValueResponse:
    """更新字段值"""
    field_value = field_value_crud.get(db, id=field_value_id)
    return field_value_crud.update(db, db_obj=field_value, obj_in=field_value_in)

@router.delete("/{field_value_id}", response_model=FieldValueResponse)
def delete_field_value(
    field_value_id: int,
    db: Session = Depends(get_db),
) -> FieldValueResponse:
    """删除字段值"""
    return field_value_crud.remove(db, id=field_value_id) 