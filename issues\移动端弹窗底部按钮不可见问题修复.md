# 移动端弹窗底部按钮不可见问题修复

## 问题描述
移动端弹窗中的底部按钮不可见，用户必须手动拖动页面到占满整个浏览器时，底部按钮才会出现。这严重影响了用户体验。

## 问题分析
1. **根本原因**: 弹窗使用百分比高度（如80%、70%等），这些百分比基于100vh计算
2. **100vh问题**: 在移动端浏览器中，100vh包含了地址栏高度，但地址栏在滚动时可能隐藏
3. **影响**: 底部按钮被隐藏在可视区域之外，需要手动拖拽才能看到

## 解决方案
使用现代CSS动态视口单位（dvh）+ 组件优化的组合方案：

### 方案1：使用dvh单位替换百分比高度
- 将弹窗高度从百分比改为dvh单位
- dvh会自动考虑浏览器chrome的变化
- 兼容性好（93.8%支持率）

### 方案2：优化MobilePopupFooter组件
- 增加更大的安全区域间距
- 优化按钮最小高度和间距
- 确保在所有设备上可见

## 修复步骤
1. 更新移动端样式变量添加dvh支持
2. 修复8个文件中的弹窗高度设置
3. 优化MobilePopupFooter组件样式
4. 添加弹窗容器样式优化
5. 测试验证修复效果

## 涉及文件
- `frontend/src/mobile/styles/variables.scss`
- `frontend/src/mobile/styles/theme.scss`
- `frontend/src/mobile/components/MobilePopupFooter.vue`
- `frontend/src/mobile/views/asset/AssetSettings.vue`
- `frontend/src/mobile/views/asset/InventoryTask.vue`
- `frontend/src/mobile/views/asset/InventoryList.vue`
- `frontend/src/mobile/views/asset/FieldValueManagement.vue`
- `frontend/src/mobile/components/MobilePersonnelSelector.vue`
- `frontend/src/mobile/components/MobileFieldValueSelector.vue`

## 预期结果
- 弹窗底部按钮在所有移动设备上完全可见
- 不需要手动拖拽就能看到和点击底部按钮
- 弹窗高度自动适应真实的可视区域

## 修复完成情况

### ✅ 已完成的修复
1. **更新样式变量** - 在`variables.scss`中添加了基于`dvh`单位的弹窗高度变量
2. **优化MobilePopupFooter组件** - 增加了更大的安全区域间距和按钮高度
3. **修复所有弹窗高度设置** - 将11个文件中的百分比高度改为`dvh`单位：
   - `AssetSettings.vue` (80% → 80dvh)
   - `InventoryTask.vue` (80% → 80dvh)
   - `InventoryList.vue` (2个弹窗: 70% → 70dvh)
   - `FieldValueManagement.vue` (60% → 60dvh)
   - `CustomFieldManagement.vue` (80% → 80dvh)
   - `AssetDetail.vue` (70% → 70dvh)
   - `Login.vue` (40% → 60dvh，提高了可用性)
   - `MobilePersonnelSelector.vue` (70% → 70dvh)
   - `MobileFieldValueSelector.vue` (2个弹窗: 70% → 70dvh, 50% → 60dvh)
4. **添加弹窗优化样式** - 在`theme.scss`中增加了移动端弹窗的通用优化样式

### 🔧 技术实现细节
- 使用`dvh`（动态视口高度）单位替代百分比，自动适应浏览器地址栏的显示/隐藏
- 增加了CSS变量系统，便于统一管理弹窗高度
- 优化了底部按钮的安全区域间距，从44px提升到48px
- 增加了底部内容边距，确保内容不被按钮遮挡

### 📱 兼容性
- `dvh`单位支持率达93.8%，覆盖绝大多数现代移动浏览器
- 保留了fallback值，确保在不支持的浏览器中有合理的显示效果 