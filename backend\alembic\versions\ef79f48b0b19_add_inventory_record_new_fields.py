"""add_inventory_record_new_fields

Revision ID: ef79f48b0b19
Revises: ad2d1f08372b
Create Date: 2024-12-18 18:22:26.680442

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ef79f48b0b19'
down_revision: Union[str, None] = 'ad2d1f08372b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('inventory_records', sa.Column('new_name', sa.String(length=100), nullable=True, comment='新资产名称'))
    op.add_column('inventory_records', sa.Column('new_specification', sa.String(length=200), nullable=True, comment='新规格型号'))
    op.add_column('inventory_records', sa.Column('new_status', sa.String(length=20), nullable=True, comment='新资产状态'))
    op.add_column('inventory_records', sa.Column('new_company', sa.String(length=100), nullable=True, comment='新公司'))
    op.add_column('inventory_records', sa.Column('new_remarks', sa.String(length=500), nullable=True, comment='新备注'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('inventory_records', 'new_remarks')
    op.drop_column('inventory_records', 'new_company')
    op.drop_column('inventory_records', 'new_status')
    op.drop_column('inventory_records', 'new_specification')
    op.drop_column('inventory_records', 'new_name')
    # ### end Alembic commands ###
