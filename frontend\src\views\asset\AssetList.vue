<template>
  <div class="asset-list">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Box /></el-icon>
        <h2 class="page-title">资产列表</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>资产管理</el-breadcrumb-item>
        <el-breadcrumb-item>资产列表</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索公司名/资产名称/资产编号/领用人/使用人"
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
            :prefix-icon="Search"
          >
          </el-input>
        </div>
        <div class="action-buttons">
          <el-dropdown trigger="click" v-if="hasAnyPermission(['asset:import', 'asset:view', 'asset:add'])">
            <el-button type="primary" plain class="menu-button">
              <el-icon><Connection /></el-icon>
              功能菜单
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showImportDialog()" v-if="hasPermission('asset:import')">
                  <el-icon><Upload /></el-icon>批量导入
                </el-dropdown-item>
                <el-dropdown-item @click="showColumnSettings()" v-if="hasPermission('asset:view')">
                  <el-icon><Setting /></el-icon>列设置
                </el-dropdown-item>
                <el-dropdown-item @click="showCreateModal()" v-if="hasPermission('asset:add')">
                  <el-icon><Plus /></el-icon>新增资产
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <el-table
        :data="assets"
        :loading="loading"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :key="tableKey"
        row-class-name="asset-table-row"
        header-row-class-name="asset-table-header"
        header-cell-class-name="table-header-cell"
        :default-sort="{ prop: 'asset_number', order: 'ascending' }"
      >
        <el-table-column type="selection" width="55" />
        <template v-for="column in columns" :key="column.prop">
          <el-table-column
            v-if="columnVisible(column.prop)"
            v-bind="column"
          >
            <template #header>
              <div class="column-header">
                <div class="column-title-wrapper">
                  <span class="column-title">
                    <el-tooltip
                      :content="column.label"
                      placement="top"
                      effect="dark"
                    >
                      {{ column.label }}
                    </el-tooltip>
                  </span>
                  <div class="column-icons">
                    <el-tooltip
                      :content="hasFilter(column.prop) ? '已设置筛选' : '点击筛选'"
                      placement="top"
                      v-if="column.filterable !== false"
                    >
                      <el-popover
                        placement="bottom"
                        :width="column.prop.includes('date') ? 360 : 240"
                        trigger="click"
                        popper-class="filter-popover"
                      >
                        <template #reference>
                          <el-button
                            class="icon-button"
                            :class="{ 'is-active': hasFilter(column.prop) }"
                            :type="hasFilter(column.prop) ? 'primary' : ''"
                            link
                          >
                            <el-icon><Filter /></el-icon>
                          </el-button>
                        </template>
                        <div class="filter-content">
                          <div class="filter-header">
                            <span class="filter-title">{{ column.label }}筛选</span>
                          </div>
                          <div class="filter-body">
                            <template v-if="column.prop === 'status'">
                              <el-select
                                v-model="searchForm.status"
                                placeholder="请选择状态"
                                clearable
                                class="filter-input"
                              >
                                <el-option
                                  v-for="option in statusOptions"
                                  :key="option.id"
                                  :label="option.field_value"
                                  :value="option.field_value"
                                />
                              </el-select>
                            </template>
                            <template v-else-if="column.prop === 'purchase_date' || column.prop === 'retirement_date'">
                              <el-date-picker
                                v-model="searchForm[column.prop === 'purchase_date' ? 'purchase_date_range' : 'retirement_date_range']"
                                type="daterange"
                                unlink-panels
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                value-format="YYYY-MM-DD"
                                class="filter-date-picker"
                                :shortcuts="[
                                  { text: '最近一周', value: () => getDateRange(7) },
                                  { text: '最近一月', value: () => getDateRange(30) },
                                  { text: '最近三月', value: () => getDateRange(90) },
                                  { text: '最近半年', value: () => getDateRange(180) },
                                  { text: '最近一年', value: () => getDateRange(365) }
                                ]"
                              />
                            </template>
                            <template v-else>
                              <el-select
                                v-model="searchForm[column.prop]"
                                :placeholder="'请选择' + column.label"
                                clearable
                                filterable
                                class="filter-input"
                              >
                                <el-option
                                  v-for="value in Array.from(filterOptions[column.prop as keyof typeof filterOptions] || new Set())"
                                  :key="value"
                                  :label="value"
                                  :value="value"
                                />
                              </el-select>
                            </template>
                          </div>
                          <div class="filter-footer">
                            <el-button @click="resetColumnFilter(column.prop)">重置</el-button>
                            <el-button type="primary" @click="handleSearch">确定</el-button>
                          </div>
                        </div>
                      </el-popover>
                    </el-tooltip>
                    <el-tooltip
                      :content="getSortTooltip(column.prop)"
                      placement="top"
                      v-if="column.prop !== 'created_at' && column.prop !== 'updated_at'"
                    >
                      <div
                        class="sort-icons"
                        @click="handleColumnSort(column.prop)"
                      >
                        <el-icon
                          class="sort-icon"
                          :class="{ 'is-active': currentSort.prop === column.prop && currentSort.order === 'ascending' }"
                        >
                          <CaretTop />
                        </el-icon>
                        <el-icon
                          class="sort-icon"
                          :class="{ 'is-active': currentSort.prop === column.prop && currentSort.order === 'descending' }"
                        >
                          <CaretBottom />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </template>
            <template #default="scope">
              <template v-if="column.slot === 'asset_number'">
                <Authority permission="asset:view">
                  <el-link
                    type="primary"
                    @click="showDetailDialog(scope.row)"
                  >
                    {{ scope.row.asset_number }}
                  </el-link>
                </Authority>
              </template>
              <template v-else-if="column.slot === 'status'">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
              <template v-else>
                {{ scope.row[column.prop] }}
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>

      <!-- 批量操作工具栏 -->
      <div class="batch-operation-toolbar" v-if="selectedAssets.length > 0">
        <div class="selected-count">
          已选择 {{ selectedAssets.length }} 个资产
        </div>
        <div class="batch-operations">
          <el-button-group>
            <Authority permission="asset:edit">
              <el-button
                type="primary"
                @click="batchStatusDialogVisible = true"
                :disabled="!selectedAssets.length"
              >
                <el-icon><Edit /></el-icon>批量修改状态
              </el-button>
            </Authority>
            <Authority permission="asset:export">
              <el-button
                type="primary"
                @click="exportDialogVisible = true"
                :disabled="!selectedAssets.length"
              >
                <el-icon><Download /></el-icon>批量导出
              </el-button>
            </Authority>
            <Authority permission="asset:view">
              <el-button
                type="primary"
                @click="batchDownloadQRCode"
                :disabled="!selectedAssets.length"
              >
                <el-icon><Picture /></el-icon>批量下载二维码
              </el-button>
            </Authority>
            <Authority permission="asset:view">
              <el-button
                type="primary"
                @click="batchPrintReceipts"
                :disabled="!selectedAssets.length"
              >
                <el-icon><Printer /></el-icon>批量打印验收单
              </el-button>
            </Authority>
            <Authority permission="asset:delete">
              <el-button
                type="danger"
                @click="batchDeleteAssets"
                :disabled="!selectedAssets.length"
              >
                <el-icon><Delete /></el-icon>批量删除
              </el-button>
            </Authority>
          </el-button-group>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <AssetForm
      :visible="formVisible"
      @update:visible="formVisible = $event"
      :mode="formMode"
      :initial-values="selectedAsset"
      @success="handleFormSuccess"
      v-if="(formMode === 'create' && hasPermission('asset:add')) || (formMode === 'edit' && hasPermission('asset:edit'))"
    />

    <!-- 批量修改状态的对话框 -->
    <el-dialog
      v-model="batchStatusDialogVisible"
      title="批量修改状态"
      width="400px"
      v-if="hasPermission('asset:edit')"
    >
      <el-form :model="batchStatusForm">
        <el-form-item label="资产状态">
          <el-select v-model="batchStatusForm.status" placeholder="请选择状态">
            <el-option
              v-for="option in statusOptions"
              :key="option.id"
              :label="option.field_value"
              :value="option.field_value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchStatusDialogVisible = false">取消</el-button>
          <Authority permission="asset:edit">
            <el-button type="primary" @click="confirmBatchChangeStatus">确定</el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 列设置对话框 -->
    <el-dialog
      v-model="columnSettingsVisible"
      title="列设置"
      width="500px"
      class="column-settings-dialog"
      @closed="handleColumnSettingsDialogClosed"
      :close-on-click-modal="false"
    >
      <div class="column-settings-content">
        <div class="column-settings-tip">
          <el-icon class="tip-icon"><InfoFilled /></el-icon>
          <span>拖动列表项可以调整显示顺序，勾选/取消勾选可控制列的显示/隐藏</span>
        </div>
        <el-scrollbar height="400px" class="column-scrollbar">
          <VueDraggable
            v-model="columnSettings"
            item-key="prop"
            handle=".drag-handle"
            ghost-class="ghost"
            @end="handleDragEnd"
          >
            <template #item="{ element }">
              <div class="column-item">
                <div class="column-drag">
                  <el-icon class="drag-handle"><Rank /></el-icon>
                </div>
                <Authority permission="asset:view">
                  <el-checkbox v-model="element.visible" @change="handleColumnVisibilityChange" class="column-checkbox">
                    {{ element.label }}
                  </el-checkbox>
                </Authority>
              </div>
            </template>
          </VueDraggable>
        </el-scrollbar>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <Authority permission="asset:view">
            <el-button @click="resetColumnSettings" type="warning">重置</el-button>
          </Authority>
          <el-button @click="columnSettingsVisible = false">取消</el-button>
          <Authority permission="asset:view">
            <el-button type="primary" @click="saveColumnSettings">确定</el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 导出选项对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出资产"
      width="400px"
      v-if="hasPermission('asset:export')"
    >
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="导出格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="csv">CSV</el-radio>
            <el-radio label="xlsx">Excel</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <Authority permission="asset:export">
            <el-button type="primary" @click="confirmExport">确认导出</el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入资产"
      width="650px"
      v-if="hasPermission('asset:import')"
      class="import-asset-dialog"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="import-dialog-content">
        <div class="import-steps">
          <div class="step-item">
            <div class="step-number">
              <span>1</span>
            </div>
            <div class="step-content">
              <h4>下载导入模板</h4>
              <p>请先下载导入模板，按照模板格式填写资产信息</p>
              <div class="template-buttons">
                <Authority permission="asset:import">
                  <el-button @click="handleDownloadTemplate('xlsx')" type="primary" plain>
                    <el-icon><Download /></el-icon>Excel模板
                  </el-button>
                </Authority>
                <Authority permission="asset:import">
                  <el-button @click="handleDownloadTemplate('csv')" plain>
                    <el-icon><Download /></el-icon>CSV模板
                  </el-button>
                </Authority>
              </div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">
              <span>2</span>
            </div>
            <div class="step-content">
              <h4>填写说明</h4>
              <ul class="import-instructions">
                <li>必填字段：公司名称、资产名称、资产编号、状态、领用人、使用人、入账日期</li>
                <li>日期格式：YYYY-MM-DD，例如：2024-01-01</li>
                <li>资产状态可选值：使用中、闲置、维修中、已报废</li>
                <li>资产编号必须唯一</li>
                <li>支持的文件格式：Excel(.xlsx)或CSV(.csv)</li>
              </ul>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">
              <span>3</span>
            </div>
            <div class="step-content">
              <h4>上传文件</h4>
              <p>请选择填写好的资产导入文件</p>
              <el-upload
                class="import-upload"
                :show-file-list="false"
                :before-upload="handleImport"
                accept=".csv,.xlsx"
                drag
              >
                <div class="upload-content">
                  <el-icon class="upload-icon"><Upload /></el-icon>
                  <div class="upload-text">
                    <span>点击或拖拽文件到此区域上传</span>
                    <p>支持 .xlsx 或 .csv 格式文件</p>
                  </div>
                </div>
              </el-upload>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="importDialogVisible = false">完成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 资产详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="资产详情"
      width="1000px"
      destroy-on-close
      class="asset-detail-dialog"
      v-if="hasPermission('asset:view')"
    >
      <div class="asset-detail-content">
        <el-tabs>
          <el-tab-pane label="基本信息">
            <div class="detail-main">
              <div class="detail-header">
                <div class="asset-title">
                  <span class="asset-number">{{ selectedAsset?.asset_number }}</span>
                  <el-tag :type="getStatusType(selectedAsset?.status)" size="small" class="status-tag">
                    {{ selectedAsset?.status }}
                  </el-tag>
                </div>
                <div class="asset-subtitle">{{ selectedAsset?.name }} - {{ selectedAsset?.company }}</div>
              </div>

              <el-divider />

              <div class="detail-content">
                <div class="detail-info">
                  <el-descriptions :column="2" border size="large" class="asset-descriptions">
                    <el-descriptions-item label="规格型号">
                      {{ selectedAsset?.specification }}
                    </el-descriptions-item>
                    <el-descriptions-item label="生产编号">
                      {{ selectedAsset?.production_number || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="价格">
                      {{ selectedAsset?.price ? `¥${selectedAsset.price.toFixed(2)}` : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="供应商">
                      {{ selectedAsset?.supplier || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="制造商">
                      {{ selectedAsset?.manufacturer || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="采购人">
                      {{ selectedAsset?.purchaser || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="领用人">
                      {{ selectedAsset?.custodian }}
                    </el-descriptions-item>
                    <el-descriptions-item label="领用人部门">
                      {{ selectedAsset?.custodian_department || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="使用人">
                      {{ selectedAsset?.user }}
                    </el-descriptions-item>
                    <el-descriptions-item label="使用人部门">
                      {{ selectedAsset?.user_department || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="存放位置">
                      {{ selectedAsset?.location || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="入账日期">
                      {{ selectedAsset?.purchase_date }}
                    </el-descriptions-item>
                    <el-descriptions-item label="销账日期">
                      {{ selectedAsset?.retirement_date || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="备注" :span="2">
                      {{ selectedAsset?.remarks || '-' }}
                    </el-descriptions-item>
                  </el-descriptions>

                  <div class="detail-footer">
                    <div class="time-info">
                      <div class="time-item">
                        <span class="label">创建时间：</span>
                        <span class="value">{{ selectedAsset?.created_at }}</span>
                      </div>
                      <div class="time-item">
                        <span class="label">更新时间：</span>
                        <span class="value">{{ selectedAsset?.updated_at }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="qrcode-section">
                  <h3>资产二维码</h3>
                  <div class="qrcode-container">
                    <QRCodeVue3
                      :value="getAssetQRCodeValue(selectedAsset)"
                      :size="200"
                      level="H"
                      render-as="canvas"
                    />
                  </div>
                  <Authority permission="asset:view">
                    <el-button type="primary" size="default" @click="downloadQRCode" class="download-btn">
                      <el-icon><Download /></el-icon>
                      下载二维码
                    </el-button>
                  </Authority>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <Authority permission="asset:view">
            <el-tab-pane label="变更记录">
              <div class="change-logs">
                <div class="change-logs-header">
                  <div class="change-logs-title">
                    <el-icon><InfoFilled /></el-icon>
                    <span>资产变更历史记录</span>
                  </div>
                </div>

                <el-empty
                  v-if="!changeLogs.length"
                  description="暂无变更记录"
                  :image-size="120"
                  class="empty-logs"
                />

                <el-timeline v-else>
                  <el-timeline-item
                    v-for="log in changeLogs"
                    :key="log.id"
                    :timestamp="formatDateTime(log.created_at, 'datetime')"
                    :type="getChangeLogType(log)"
                    placement="top"
                    :hollow="true"
                    :size="'large'"
                  >
                    <div class="change-log-item">
                      <div class="change-log-header">
                        <div class="field-name">{{ getFieldDisplayName(log.field) }}</div>
                        <div class="operator">变更类型: {{ log.change_type || '修改' }}</div>
                      </div>
                      <div class="change-content">
                        <template v-if="log.old_value || log.new_value">
                          <div class="change-values">
                            <div class="old-value" v-if="log.old_value">
                              <el-tag type="info" size="small" effect="plain">原值</el-tag>
                              <span class="value">{{ formatFieldValue(log.field, log.old_value) }}</span>
                            </div>
                            <div class="change-arrow" v-if="log.old_value && log.new_value">
                              <el-icon><ArrowRight /></el-icon>
                            </div>
                            <div class="new-value" v-if="log.new_value">
                              <el-tag type="success" size="small" effect="plain">新值</el-tag>
                              <span class="value">{{ formatFieldValue(log.field, log.new_value) }}</span>
                            </div>
                          </div>
                        </template>
                        <div v-else class="no-value">无变更内容</div>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>

                <div class="pagination" v-if="changeLogs.length">
                  <Authority permission="asset:view">
                    <el-pagination
                      v-model:current-page="changeLogPagination.current"
                      v-model:page-size="changeLogPagination.pageSize"
                      :total="changeLogPagination.total"
                      :page-sizes="[10, 20, 50]"
                      layout="total, sizes, prev, pager, next, jumper"
                      @size-change="handleChangeLogSizeChange"
                      @current-change="handleChangeLogCurrentChange"
                    />
                  </Authority>
                </div>
              </div>
            </el-tab-pane>
          </Authority>
        </el-tabs>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <Authority permission="asset:edit">
            <el-button type="primary" @click="handleEditFromDetail">编辑</el-button>
          </Authority>
          <Authority permission="asset:view">
            <el-button type="success" @click="showReceiptDialog(selectedAsset!)" v-if="selectedAsset">打印验收单</el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 资产验收单对话框 -->
    <el-dialog
      v-model="receiptDialogVisible"
      title="资产验收单预览"
      width="900px"
      append-to-body
      :destroy-on-close="false"
    >
      <AssetReceiptForm
        v-if="selectedAsset"
        ref="receiptFormRef"
        :asset="selectedAsset"
      />
      <template #footer>
        <div class="receipt-dialog-footer">
          <div class="receipt-tips">
            <el-alert
              type="info"
              :closable="false"
              show-icon
            >
              <p>提示：点击签名线可以添加电子签名。支持触摸屏和鼠标书写。</p>
            </el-alert>
          </div>
          <div class="receipt-buttons">
            <el-button @click="receiptDialogVisible = false">关闭</el-button>
            <Authority permission="asset:view">
              <el-button type="primary" @click="handlePrintReceipt">打印</el-button>
            </Authority>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Filter, CaretTop, CaretBottom, Search, Upload, Download, InfoFilled, Rank, Plus, ArrowDown, Connection, Box, ArrowRight, Edit, Picture, Printer, Delete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { assetApi } from '@/api/asset'
import type { Asset } from '@/types/asset'
import { fieldValueApi } from '@/api/field_value'
import type { FieldValue } from '@/types/field_value'
import AssetForm from './components/AssetForm.vue'
import AssetReceiptForm from './components/AssetReceiptForm.vue'
import { saveAs } from 'file-saver'
import QRCodeVue3 from 'qrcode.vue'
import type { AssetChangeLog } from '@/types/asset_change_log'
import VueDraggable from 'vuedraggable'
import { formatDateTime, fieldNameMap } from '@/utils/format'
import { debounce } from 'lodash-es'
import { hasPermission, hasAnyPermission } from '@/utils/permission'
import Authority from '@/components/Authority/index.vue'
import JSZip from 'jszip'
import QRCode from 'qrcode'

const assets = ref<Asset[]>([])
const loading = ref(false)
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const selectedAsset = ref<Asset | null>(null)
const selectedAssets = ref<Asset[]>([])
const tableKey = ref(0)

// 状态选项
const statusOptions = ref<FieldValue[]>([])

// 搜索表单（包含排序字段）
interface SearchForm {
  keyword: string
  company: string
  status: string
  category: string
  asset_number: string
  name: string
  custodian: string
  custodian_department: string
  user: string
  user_department: string
  location: string
  specification: string
  purchase_date_range: string[]
  retirement_date_range: string[]
  sort_by: string
  sort_order: string
  [key: string]: string | string[] // 添加索引签名
}

const searchForm = reactive<SearchForm>({
  keyword: '',
  company: '',
  status: '',
  category: '',
  asset_number: '',
  name: '',
  custodian: '',
  custodian_department: '',
  user: '',
  user_department: '',
  location: '',
  specification: '',
  purchase_date_range: [],
  retirement_date_range: [],
  sort_by: '',
  sort_order: ''
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 筛选选项数据
const filterOptions = reactive<Record<string, Set<string>>>({
  company: new Set(),
  name: new Set(),
  asset_number: new Set(),
  category: new Set(),
  custodian: new Set(),
  custodian_department: new Set(),
  user: new Set(),
  user_department: new Set(),
  location: new Set(),
  specification: new Set()
})

// 更新筛选选项
const updateFilterOptions = (data: Asset[]) => {
  data.forEach(item => {
    if (item.company) filterOptions.company.add(item.company)
    if (item.name) filterOptions.name.add(item.name)
    if (item.asset_number) filterOptions.asset_number.add(item.asset_number)
    if (item.category) filterOptions.category.add(item.category)
    if (item.custodian) filterOptions.custodian.add(item.custodian)
    if (item.custodian_department) filterOptions.custodian_department.add(item.custodian_department)
    if (item.user) filterOptions.user.add(item.user)
    if (item.user_department) filterOptions.user_department.add(item.user_department)
    if (item.location) filterOptions.location.add(item.location)
    if (item.specification) filterOptions.specification.add(item.specification)
  })
}

// 加载状态选项
const loadStatusOptions = async () => {
  try {
    const response = await fieldValueApi.getFieldValues({ field_name: 'status' })
    statusOptions.value = response.data.data
  } catch (error) {
    console.error('加载状态选项失败:', error)
  }
}

// 优化资产列表加载
const fetchAssets = async () => {
  loading.value = true
  try {
    const params: Record<string, any> = {
      keyword: searchForm.keyword,
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      company: searchForm.company,
      status: searchForm.status,
      category: searchForm.category,
      asset_number: searchForm.asset_number,
      name: searchForm.name,
      custodian: searchForm.custodian,
      custodian_department: searchForm.custodian_department,
      user: searchForm.user,
      user_department: searchForm.user_department,
      location: searchForm.location,
      sort_by: searchForm.sort_by,
      sort_order: searchForm.sort_order,
      purchase_date_start: searchForm.purchase_date_range?.[0] || undefined,
      purchase_date_end: searchForm.purchase_date_range?.[1] || undefined,
      retirement_date_start: searchForm.retirement_date_range?.[0] || undefined,
      retirement_date_end: searchForm.retirement_date_range?.[1] || undefined,
    }

    console.log('请求参数:', params)
    const response = await assetApi.getAssets(params)
    const formattedAssets = response.data.data.map((asset: Asset) => ({
      ...asset,
      purchase_date: asset.purchase_date ? dayjs(asset.purchase_date).format('YYYY-MM-DD') : '',
      retirement_date: asset.retirement_date ? dayjs(asset.retirement_date).format('YYYY-MM-DD') : '',
      created_at: asset.created_at ? dayjs(asset.created_at).format('YYYY-MM-DD HH:mm:ss') : '',
      updated_at: asset.updated_at ? dayjs(asset.updated_at).format('YYYY-MM-DD HH:mm:ss') : ''
    }))
    assets.value = formattedAssets
    pagination.total = response.data.total

    // 延迟更新筛选选项，避免阻塞主UI渲染
    setTimeout(() => {
      updateFilterOptions(formattedAssets)
    }, 100)
  } catch (error) {
    ElMessage.error('获取资产列表失败')
  } finally {
    loading.value = false
  }
}

// 使用防抖处理搜索，避免频繁请求
const debouncedSearch = debounce(() => {
  pagination.current = 1
  fetchAssets()
}, 300)

const handleSearch = () => {
  debouncedSearch()
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchAssets()
}

const handleCurrentChange = (val: number) => {
  pagination.current = val
  fetchAssets()
}

const showCreateModal = () => {
  // 检查用户是否有添加资产的权限
  if (!hasPermission('asset:add')) {
    ElMessage.warning('您没有添加资产的权限')
    return
  }

  formMode.value = 'create'
  selectedAsset.value = null
  formVisible.value = true
}

const showEditModal = (asset: Asset) => {
  // 检查用户是否有编辑资产的权限
  if (!hasPermission('asset:edit')) {
    ElMessage.warning('您没有编辑资产的权限')
    return
  }

  formMode.value = 'edit'
  selectedAsset.value = asset
  formVisible.value = true
}

const handleFormSuccess = () => {
  formVisible.value = false
  fetchAssets()
}

// 多选相关
const handleSelectionChange = (selection: Asset[]) => {
  selectedAssets.value = selection
}

// 批量修改状态相关
const batchStatusDialogVisible = ref(false)
const batchStatusForm = reactive({
  status: ''
})

const confirmBatchChangeStatus = async () => {
  if (!batchStatusForm.status) {
    ElMessage.warning('请选择状态')
    return
  }

  try {
    const promises = selectedAssets.value.map(asset =>
      assetApi.updateAsset(asset.id, { status: batchStatusForm.status })
    )
    await Promise.all(promises)
    ElMessage.success('批量修改成功')
    batchStatusDialogVisible.value = false
    batchStatusForm.status = ''
    fetchAssets()
  } catch (error) {
    ElMessage.error('批量修改失败')
  }
}

// 排序相关
interface SortState {
  prop: string
  order: 'ascending' | 'descending' | null
}

const currentSort = ref<SortState>({
  prop: '',
  order: null
})

// 处理排序变化
const handleSortChange = ({ prop, order }: SortState) => {
  currentSort.value = { prop, order }
  if (order === null) {
    searchForm.sort_by = ''
    searchForm.sort_order = ''
  } else {
    searchForm.sort_by = prop
    searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'
  }
  fetchAssets()
}

// 配置
interface ColumnConfig {
  prop: string
  label: string
  slot?: string
  width?: number | string
  sortable?: boolean | 'custom'
  filterable?: boolean
  visible: boolean
  tooltip?: string
}

interface ColumnSetting {
  prop: string
  label: string
  visible: boolean
}

// 列配置
const defaultColumns: ColumnConfig[] = [
  { prop: 'company', label: '公司', sortable: false, filterable: true, visible: true },
  { prop: 'asset_number', label: '资产编号', sortable: false, filterable: true, visible: true, slot: 'asset_number' },
  { prop: 'name', label: '资产名称', sortable: false, filterable: true, visible: true },
  { prop: 'category', label: '资产类别', sortable: false, filterable: true, visible: true },
  { prop: 'specification', label: '规格型号', sortable: false, filterable: true, visible: true },
  {
    prop: 'status',
    label: '资产状态',
    sortable: false,
    filterable: true,
    slot: 'status',
    visible: true
  },
  { prop: 'production_number', label: '生产编号', sortable: false, filterable: true, visible: true },
  { prop: 'price', label: '价格', sortable: false, filterable: true, visible: true },
  { prop: 'supplier', label: '供应商', sortable: false, filterable: true, visible: true },
  { prop: 'manufacturer', label: '制造商', sortable: false, filterable: true, visible: true },
  { prop: 'purchaser', label: '采购人', sortable: false, filterable: true, visible: true },
  { prop: 'custodian', label: '领用人', sortable: false, filterable: true, visible: true },
  { prop: 'custodian_department', label: '领用人部门', sortable: false, filterable: true, visible: true },
  { prop: 'user', label: '使用人', sortable: false, filterable: true, visible: true },
  { prop: 'user_department', label: '使用人部门', sortable: false, filterable: true, visible: true },
  { prop: 'location', label: '资产存放位置', sortable: false, filterable: true, visible: true },
  { prop: 'purchase_date', label: '入账日期', sortable: false, filterable: true, visible: true },
  { prop: 'retirement_date', label: '销账日期', sortable: false, filterable: true, visible: true },
  { prop: 'created_at', label: '创建时间', sortable: false, filterable: false, visible: false },
  { prop: 'updated_at', label: '更新时间', sortable: false, filterable: false, visible: false }
]

const columns = ref<ColumnConfig[]>(defaultColumns)

// 默认显示的列（不包含创建时间和更新时间）
const defaultVisibleColumns = [
  'company',
  'asset_number',
  'name',
  'category',
  'specification',
  'status',
  'custodian',
  'custodian_department',
  'user',
  'user_department',
  'location'
]

// 列设置相关
const columnSettingsVisible = ref(false)
const columnSettings = ref<ColumnSetting[]>([])

// 判断列是否有过滤条件
const hasFilter = (prop: string): boolean => {
  if (prop === 'purchase_date') {
    return searchForm.purchase_date_range?.length > 0
  }
  if (prop === 'retirement_date') {
    return searchForm.retirement_date_range?.length > 0
  }
  return !!searchForm[prop as keyof typeof searchForm]
}

// 重置单个列的过滤条件
const resetColumnFilter = (prop: string): void => {
  if (prop === 'purchase_date') {
    searchForm.purchase_date_range = []
  } else if (prop === 'retirement_date') {
    searchForm.retirement_date_range = []
  } else if (prop === 'keyword') {
    searchForm.keyword = ''
  } else if (prop in searchForm) {
    ;(searchForm[prop] as string) = ''
  }
  handleSearch()
}

// 初始化列设置
const initColumnSettings = () => {
  const savedSettings = localStorage.getItem('assetListColumnSettings')
  if (savedSettings) {
    try {
      const parsed = JSON.parse(savedSettings)
      // 使用保存的设置顺序，而不是默认顺序
      columnSettings.value = parsed.map((savedCol: ColumnSetting) => {
        const defaultColumn = defaultColumns.find((col: ColumnConfig) => col.prop === savedCol.prop)
        if (!defaultColumn) return null
        return {
          prop: savedCol.prop,
          label: defaultColumn.label,
          visible: savedCol.visible
        }
      }).filter((col: ColumnSetting | null): col is ColumnSetting => col !== null)

      // 检查是否有新增的列（在默认配置中存在但在保存的设置中不存在的列）
      const savedProps = new Set(parsed.map((col: ColumnSetting) => col.prop))
      const newColumns = defaultColumns
        .filter((col: ColumnConfig) => !savedProps.has(col.prop))
        .map((col: ColumnConfig) => ({
          prop: col.prop,
          label: col.label,
          visible: col.visible
        }))

      // 将新列添加到末尾
      columnSettings.value.push(...newColumns)

      // 立即更新列的显示顺序
      handleDragEnd()
    } catch (e) {
      console.error('解析列设置失败:', e)
      resetColumnSettings()
    }
  } else {
    // 直接调用重置函数，使用默认设置
    resetColumnSettings()
  }
}

// 重置列设置为默认值
const resetColumnSettings = () => {
  // 定义默认显示顺序
  const defaultOrder = [
    'company',
    'asset_number',
    'name',
    'category',
    'specification',
    'status',
    'custodian',
    'custodian_department',
    'user',
    'user_department',
    'location',
    'production_number',
    'price',
    'supplier',
    'manufacturer',
    'purchaser',
    'purchase_date',
    'retirement_date',
    'created_at',
    'updated_at'
  ]

  // 按照默认顺序重置列设置
  columnSettings.value = defaultOrder.map(prop => {
    const col = defaultColumns.find(col => col.prop === prop)
    if (!col) return null
    return {
      prop,
      label: col.label,
      visible: defaultVisibleColumns.includes(prop)
    }
  }).filter((col): col is ColumnSetting => col !== null)

  // 重置时也需要更新列的显示顺序
  handleDragEnd()

  // 清空筛选选项
  Object.keys(filterOptions).forEach(key => {
    filterOptions[key].clear()
  })

  // 保存重置后的设置到localStorage
  localStorage.setItem('assetListColumnSettings', JSON.stringify(columnSettings.value))

  // 增加tableKey强制刷新表格
  tableKey.value++

  // 重新获取数据
  nextTick(() => {
    fetchAssets()
  })
}

// 显示列设置对话框
const showColumnSettings = () => {
  columnSettingsVisible.value = true
  initColumnSettings()
}

// 处理拖拽结束
const handleDragEnd = () => {
  // 如果columnSettings为空，使用默认列配置
  if (!columnSettings.value || columnSettings.value.length === 0) {
    // 调用重置函数来设置默认顺序
    resetColumnSettings()
    return
  }

  // 更新列的顺序
  const newColumns = columnSettings.value.map((setting: ColumnSetting) => {
    const originalColumn = defaultColumns.find((col: ColumnConfig) => col.prop === setting.prop)
    if (!originalColumn) return null
    return {
      ...originalColumn,
      visible: setting.visible
    }
  }).filter((col): col is ColumnConfig => col !== null)

  columns.value = newColumns
}

// 处理列显示状态变化
const handleColumnVisibilityChange = () => {
  // 确保至少有一列是可见的
  const visibleColumns = columnSettings.value.filter((col: ColumnSetting) => col.visible)
  if (visibleColumns.length === 0) {
    ElMessage.warning('至少需要显示一列')
    columnSettings.value = columnSettings.value.map((col: ColumnSetting) => ({
      ...col,
      visible: true
    }))
  }
}

// 保存列设置
const saveColumnSettings = () => {
  localStorage.setItem('assetListColumnSettings', JSON.stringify(columnSettings.value))
  handleDragEnd() // 重用更新列顺序的逻辑
  columnSettingsVisible.value = false
  ElMessage.success('列设置已保存')
  // 重新获取数据，确保表格正确渲染
  nextTick(() => {
    tableKey.value++ // 增加tableKey强制刷新表格
    fetchAssets()
  })
}

// 判断列是否可见
const columnVisible = (prop: string): boolean => {
  if (!prop || !columns.value) return false
  const column = columns.value.find((col: ColumnConfig) => col.prop === prop)
  return column?.visible ?? false
}

// 在组件挂载初始化列设置
onMounted(() => {
  // 确保先初始化列设置
  initColumnSettings()
  // 加载状态选项
  loadStatusOptions()
  // 然后获取资产数据
  fetchAssets()
})

// 期范围计算函数
const getDateRange = (days: number) => {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * days)
  return [start, end]
}

// 处理列排序
const handleColumnSort = (prop: string) => {
  let order: 'ascending' | 'descending' | null = 'ascending'

  if (currentSort.value.prop === prop) {
    if (currentSort.value.order === 'ascending') {
      order = 'descending'
    } else if (currentSort.value.order === 'descending') {
      order = null
    }
  }

  handleSortChange({ prop, order })
}

// 获取排序提示文本
const getSortTooltip = (prop: string) => {
  if (currentSort.value.prop !== prop) {
    return '点击排序'
  }
  if (currentSort.value.order === 'ascending') {
    return '当前升序，点击切换为降序'
  }
  if (currentSort.value.order === 'descending') {
    return '当前降序，点击取消排序'
  }
  return '点击排序'
}

// 添加获取状态类型的方法
const getStatusType = (status: string | undefined): string => {
  switch (status) {
    case '闲置':
      return 'info'
    case '使用中':
      return 'success'
    case '维修中':
      return 'warning'
    case '已报废':
      return 'danger'
    case '库存':
      return 'primary'
    default:
      return 'info'
  }
}

// 导入相关
const importDialogVisible = ref(false)

const showImportDialog = () => {
  // 检查用户是否有导入权限
  if (hasPermission('asset:import')) {
    importDialogVisible.value = true
  } else {
    ElMessage.warning('您没有导入资产的权限')
  }
}

const handleImport = async (file: File) => {
  try {
    const response = await assetApi.importAssets(file)
    if (response.data.errors) {
      // 如果有部分导入失败
      ElMessage({
        message: response.data.message,
        type: 'warning',
        duration: 5000
      })
      // 显示详细错误信息
      ElMessageBox.alert(
        response.data.errors.join('\n'),
        '导入详情',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
    } else {
      ElMessage.success(response.data.message)
    }
    importDialogVisible.value = false
    fetchAssets()
    return false
  } catch (error: any) {
    const errorMessage = error.response?.data?.detail || '导入失败'
    // 如果错误信息包含换行符，使用 MessageBox 显示
    if (errorMessage.includes('\n')) {
      ElMessageBox.alert(
        errorMessage.replace(/\n/g, '<br>'),
        '导入失败',
        {
          type: 'error',
          dangerouslyUseHTMLString: true
        }
      )
    } else {
      ElMessage.error(errorMessage)
    }
    return false
  }
}

// 导出相关
const exportDialogVisible = ref(false)
const exportForm = reactive({
  format: 'xlsx'
})

const confirmExport = async () => {
  try {
    if (!selectedAssets.value.length) {
      ElMessage.warning('请选择要导出的资产')
      return
    }

    loading.value = true
    const response = await assetApi.exportAssets(
      exportForm.format as 'csv' | 'xlsx',
      selectedAssets.value.map(asset => asset.id)
    )

    // 检查响应类型
    if (!(response.data instanceof Blob)) {
      throw new Error('导出失败：服务器响应格式错误')
    }

    const blob = new Blob([response.data], {
      type: exportForm.format === 'csv'
        ? 'text/csv;charset=utf-8'
        : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 从响应头中获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = `资产清单_${dayjs().format('YYYYMMDD_HHmmss')}.${exportForm.format}`

    if (contentDisposition) {
      // 尝试获取 filename* 参数（UTF-8编码的文件名）
      const filenameUtf8Match = contentDisposition.match(/filename\*=utf-8''(.+)/)
      if (filenameUtf8Match) {
        try {
          filename = decodeURIComponent(filenameUtf8Match[1])
        } catch (e) {
          console.warn('Failed to decode UTF-8 filename:', e)
        }
      } else {
        // 回退到普通 filename
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
    }

    saveAs(blob, filename)
    exportDialogVisible.value = false
    ElMessage.success('导出成功')
  } catch (error: any) {
    console.error('Export error:', error)
    ElMessage.error(error.response?.data?.detail || '导出失败')
  } finally {
    loading.value = false
  }
}

// 下载模板
const handleDownloadTemplate = async (format: 'csv' | 'xlsx') => {
  try {
    loading.value = true
    const response = await assetApi.getImportTemplate(format)

    const blob = new Blob([response.data], {
      type: format === 'csv'
        ? 'text/csv;charset=utf-8'
        : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 从响应头中获取文件名
    const contentDisposition = response.headers['content-disposition']
    let filename = `资产导入模板.${format}`

    if (contentDisposition) {
      const filenameUtf8Match = contentDisposition.match(/filename\*=utf-8''(.+)/)
      if (filenameUtf8Match) {
        try {
          filename = decodeURIComponent(filenameUtf8Match[1])
        } catch (e) {
          console.warn('Failed to decode UTF-8 filename:', e)
        }
      } else {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
    }

    saveAs(blob, filename)
    ElMessage.success('模板下载成功')
  } catch (error: any) {
    console.error('Template download error:', error)
    ElMessage.error(error.response?.data?.detail || '模板下载失败')
  } finally {
    loading.value = false
  }
}

// 在 script 部分添加
const detailDialogVisible = ref(false)

// 显示详情对话框
const showDetailDialog = (asset: Asset) => {
  // 检查用户是否有查看权限
  if (!hasPermission('asset:view')) {
    ElMessage.warning('您没有查看资产详情的权限')
    return
  }

  selectedAsset.value = asset
  detailDialogVisible.value = true
}

// 从详情对话框中编辑
const handleEditFromDetail = () => {
  // 检查用户是否有编辑权限
  if (!hasPermission('asset:edit')) {
    ElMessage.warning('您没有编辑资产的权限')
    return
  }

  detailDialogVisible.value = false
  showEditModal(selectedAsset.value as Asset)
}

// 生成二维码内容
const getAssetQRCodeValue = (asset: Asset | null): string => {
  if (!asset) return ''
  return JSON.stringify({
    asset_number: asset.asset_number,
    name: asset.name,
    company: asset.company,
    specification: asset.specification,
    status: asset.status
  })
}

// 下载二维码
const downloadQRCode = () => {
  // 检查用户是否有查看资产的权限
  if (!hasPermission('asset:view')) {
    ElMessage.warning('您没有查看资产信息的权限')
    return
  }

  const canvas = document.querySelector('.qrcode-container canvas') as HTMLCanvasElement
  if (canvas && selectedAsset.value?.asset_number) {
    const link = document.createElement('a')
    link.download = `${selectedAsset.value.asset_number}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()
  }
}

// 变更记录相关
const changeLogs = ref<AssetChangeLog[]>([])
const changeLogPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 获取变更记录
const fetchChangeLogs = async () => {
  if (!selectedAsset.value?.id) return

  try {
    const response = await assetApi.getAssetChangeLogs(selectedAsset.value.id, {
      skip: (changeLogPagination.current - 1) * changeLogPagination.pageSize,
      limit: changeLogPagination.pageSize
    })
    changeLogs.value = response.data
    changeLogPagination.total = response.data.length // 后端返回总数，这里临时使用长度
  } catch (error) {
    ElMessage.error('获取变更记录失败')
  }
}

// 处理变更记录分页
const handleChangeLogSizeChange = (val: number) => {
  changeLogPagination.pageSize = val
  fetchChangeLogs()
}

const handleChangeLogCurrentChange = (val: number) => {
  changeLogPagination.current = val
  fetchChangeLogs()
}

// 获取字段显示名称
const getFieldDisplayName = (field: string): string => {
  return fieldNameMap[field] || field
}

// 监听详情对话框显示状态
watch(detailDialogVisible, (val) => {
  if (val && selectedAsset.value?.id) {
    fetchChangeLogs()
  } else {
    changeLogs.value = []
    changeLogPagination.current = 1
  }
})

// 在列设置对话框关闭时重新获取数据
const handleColumnSettingsDialogClosed = () => {
  tableKey.value++ // 增加tableKey强制刷新表格
  fetchAssets()
}

// 格式化字段值
const formatFieldValue = (field: string, value: string): string => {
  // 处理日期字段
  const dateFields = ['purchase_date', 'retirement_date', 'created_at', 'updated_at']
  if (dateFields.includes(field) && value) {
    try {
      return formatDateTime(value, 'date')
    } catch (e) {
      return value
    }
  }
  return value
}

// 打印验收单相关
const receiptDialogVisible = ref(false)
const receiptFormRef = ref<InstanceType<typeof AssetReceiptForm> | null>(null)

// 显示打印验收单对话框
const showReceiptDialog = (asset: Asset) => {
  selectedAsset.value = asset
  receiptDialogVisible.value = true
}

// 打印验收单
const handlePrintReceipt = () => {
  if (receiptFormRef.value) {
    receiptFormRef.value.printReceipt()
  }
}

// 新增的 getChangeLogType 方法
const getChangeLogType = (log: AssetChangeLog): string => {
  if (log.old_value && log.new_value) {
    return 'success'
  } else if (log.old_value || log.new_value) {
    return 'warning'
  } else {
    return 'info'
  }
}

// 批量下载二维码
const batchDownloadQRCode = async () => {
  // 检查用户是否有查看资产的权限
  if (!hasPermission('asset:view')) {
    ElMessage.warning('您没有查看资产信息的权限')
    return
  }

  if (!selectedAssets.value.length) {
    ElMessage.warning('请选择要下载二维码的资产')
    return
  }

  try {
    loading.value = true

    // 使用JSZip创建一个zip文件来包含所有二维码
    const zip = new JSZip()

    // 为每个选中的资产生成二维码
    for (const asset of selectedAssets.value) {
      // 生成二维码内容
      const qrValue = getAssetQRCodeValue(asset)

      // 使用qrcode库直接生成PNG图片数据
      try {
        // 生成二维码的DataURL
        const qrDataUrl = await QRCode.toDataURL(qrValue, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#ffffff'
          },
          errorCorrectionLevel: 'H'
        })

        // 将DataURL转换为base64数据
        const imgData = qrDataUrl.replace(/^data:image\/(png|jpg);base64,/, '')

        // 添加到zip文件
        zip.file(`${asset.asset_number || asset.id}.png`, imgData, {base64: true})
      } catch (qrError) {
        console.error(`生成资产 ${asset.asset_number} 的二维码失败:`, qrError)
      }
    }

    // 生成zip文件并下载
    const zipContent = await zip.generateAsync({type: 'blob'})
    saveAs(zipContent, `资产二维码_${dayjs().format('YYYYMMDDHHmmss')}.zip`)

    ElMessage.success(`成功打包 ${selectedAssets.value.length} 个资产的二维码`)
  } catch (error: any) {
    console.error('Batch QR code download error:', error)
    ElMessage.error('批量下载二维码失败: ' + (error.message || String(error)))
  } finally {
    loading.value = false
  }
}

// 批量删除资产
const batchDeleteAssets = async () => {
  // 检查用户是否有删除资产的权限
  if (!hasPermission('asset:delete')) {
    ElMessage.warning('您没有删除资产的权限')
    return
  }

  if (!selectedAssets.value.length) {
    ElMessage.warning('请选择要删除的资产')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedAssets.value.length} 个资产吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true
    const promises = selectedAssets.value.map(asset =>
      assetApi.deleteAsset(asset.id)
    )
    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    fetchAssets()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch delete error:', error)
      ElMessage.error(error.response?.data?.detail || '批量删除失败')
    }
  } finally {
    loading.value = false
  }
}

// 批量打印验收单
const batchPrintReceipts = async () => {
  // 检查用户是否有查看资产的权限
  if (!hasPermission('asset:view')) {
    ElMessage.warning('您没有查看资产信息的权限')
    return
  }

  if (!selectedAssets.value.length) {
    ElMessage.warning('请选择要打印验收单的资产')
    return
  }

  try {
    // 打开打印预览窗口
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      ElMessage.error('无法打开打印窗口，请检查是否被浏览器拦截')
      return
    }

    // 初始化打印窗口内容
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>资产验收单打印</title>
        <style>
          body { font-family: Arial, sans-serif; }
          .receipt { page-break-after: always; margin-bottom: 30px; }
          .receipt:last-child { page-break-after: avoid; }
          h2 { text-align: center; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          table, th, td { border: 1px solid #ddd; }
          th, td { padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .signature-line { margin-top: 50px; display: flex; justify-content: space-between; }
          .signature-box { border-bottom: 1px solid #000; width: 200px; height: 50px; }
          .signature-label { text-align: center; margin-top: 5px; }
        </style>
      </head>
      <body>
    `)

    // 为每个选中的资产生成验收单
    for (const asset of selectedAssets.value) {
      printWindow.document.write(`
        <div class="receipt">
          <h2>资产验收单</h2>
          <table>
            <tr>
              <th>资产编号</th>
              <td>${asset.asset_number || ''}</td>
              <th>资产名称</th>
              <td>${asset.name || ''}</td>
            </tr>
            <tr>
              <th>公司</th>
              <td>${asset.company || ''}</td>
              <th>规格型号</th>
              <td>${asset.specification || ''}</td>
            </tr>
            <tr>
              <th>生产编号</th>
              <td>${asset.production_number || ''}</td>
              <th>资产状态</th>
              <td>${asset.status || ''}</td>
            </tr>
            <tr>
              <th>价格</th>
              <td>${asset.price ? `¥${asset.price.toFixed(2)}` : ''}</td>
              <th>供应商</th>
              <td>${asset.supplier || ''}</td>
            </tr>
            <tr>
              <th>制造商</th>
              <td>${asset.manufacturer || ''}</td>
              <th>采购人</th>
              <td>${asset.purchaser || ''}</td>
            </tr>
            <tr>
              <th>领用人</th>
              <td>${asset.custodian || ''}</td>
              <th>领用人部门</th>
              <td>${asset.custodian_department || ''}</td>
            </tr>
            <tr>
              <th>使用人</th>
              <td>${asset.user || ''}</td>
              <th>使用人部门</th>
              <td>${asset.user_department || ''}</td>
            </tr>
            <tr>
              <th>存放位置</th>
              <td>${asset.location || ''}</td>
              <th>入账日期</th>
              <td>${asset.purchase_date || ''}</td>
            </tr>
            <tr>
              <th>备注</th>
              <td colspan="3">${asset.remarks || ''}</td>
            </tr>
          </table>

          <div class="signature-line">
            <div>
              <div class="signature-box"></div>
              <div class="signature-label">领用人签字</div>
            </div>
            <div>
              <div class="signature-box"></div>
              <div class="signature-label">管理员签字</div>
            </div>
            <div>
              <div class="signature-box"></div>
              <div class="signature-label">日期</div>
            </div>
          </div>
        </div>
      `)
    }

    printWindow.document.write(`
      </body>
      </html>
    `)

    printWindow.document.close()

    // 等待所有资源加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
    }

    ElMessage.success(`已准备打印 ${selectedAssets.value.length} 个资产的验收单`)
  } catch (error) {
    console.error('Batch print receipts error:', error)
    ElMessage.error('批量打印验收单失败')
  }
}
</script>

<style scoped>
.asset-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 320px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.asset-table-row {
  transition: all 0.3s;
  height: 56px;
}

.asset-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.asset-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.column-title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 8px;
}

.column-title {
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.column-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 批量操作工具栏样式 */
.batch-operation-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
}

.selected-count {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.batch-operations {
  display: flex;
  gap: 12px;
}

/* Keep other existing styles but make sure they don't conflict with the new ones */

.icon-button {
  padding: 2px;
  margin: 0;
}

.icon-button.is-active {
  color: #409EFF;
}

.sort-icons {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  margin-left: 4px;
}

.sort-icon {
  font-size: 10px;
  color: #c0c4cc;
}

.sort-icon.is-active {
  color: #409EFF;
}

/* Add any other styles needed from the asset list page */

/* 对话框通用样式 */
:deep(.asset-detail-dialog),
:deep(.import-asset-dialog),
:deep(.column-settings-dialog) {
  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeefb;
    padding: 16px 20px;
    margin-right: 0;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    border-top: 1px solid #eaeefb;
    padding: 12px 20px;
  }
}

.asset-detail-content {
  padding: 0;
}

.detail-main {
  padding: 20px;
}

.detail-header {
  padding-bottom: 16px;
}

.asset-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.asset-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-right: 10px;
}

.status-tag {
  text-transform: uppercase;
  font-weight: 500;
}

.asset-subtitle {
  font-size: 16px;
  color: #606266;
}

.detail-content {
  display: grid;
  grid-template-columns: 1fr 250px;
  gap: 30px;
  margin-top: 16px;
}

.detail-info {
  background-color: #fff;
  border-radius: 8px;
}

:deep(.el-descriptions) {
  width: 100%;

  .el-descriptions__header {
    margin-bottom: 12px;
  }

  .el-descriptions__title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .el-descriptions__body {
    width: 100%;
  }

  .el-descriptions__label {
    font-weight: 500;
    color: #606266;
    width: 120px;
    padding: 12px 10px;
  }

  .el-descriptions__content {
    color: #303133;
    padding: 12px 10px;
  }

  .el-descriptions-item__cell {
    padding-bottom: 12px;
  }
}

.detail-footer {
  margin-top: 20px;
  border-top: 1px dashed #eaeefb;
  padding-top: 16px;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #909399;
}

.time-item .label {
  width: 90px;
}

.qrcode-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #eaeefb;
}

.qrcode-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.qrcode-container {
  margin-bottom: 16px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.download-btn {
  width: 100%;
}

/* 变更记录表格样式 */
.change-logs {
  padding: 20px;
}

.change-logs-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eaeefb;
}

.change-logs-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.change-logs-title .el-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.empty-logs {
  padding: 40px 0;
}

:deep(.el-timeline) {
  padding: 6px 0 10px 0;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 18px;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 13px;
  margin-bottom: 8px;
}

.change-log-item {
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #eaeefb;
  transition: all 0.3s;
}

.change-log-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.change-log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #eaeefb;
}

.field-name {
  font-weight: 600;
  font-size: 15px;
  color: #333;
}

.operator {
  font-size: 13px;
  color: #909399;
}

.change-content {
  padding: 0 5px;
}

.change-values {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.old-value, .new-value {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
}

.old-value .value {
  color: #666;
}

.new-value .value {
  color: #303133;
  font-weight: 500;
}

.change-arrow {
  color: #909399;
  margin: 0 5px;
}

:deep(.el-timeline-item__node--large) {
  width: 14px;
  height: 14px;
}

:deep(.el-timeline-item__node--primary.is-hollow) {
  background-color: #fff;
}

.no-value {
  color: #909399;
  font-style: italic;
  padding: 5px 0;
}

.asset-descriptions {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav) {
  border-radius: 4px 4px 0 0;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  height: 3px;
  border-radius: 3px;
}

:deep(.el-tabs__content) {
  border: 1px solid #e4e7ed;
  border-top: none;
  border-radius: 0 0 4px 4px;
  background-color: #fff;
}

:deep(.el-tag) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: auto;
  padding: 2px 6px;
  font-weight: normal;
}

/* 资产验收单对话框样式 */
.receipt-dialog-footer {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.receipt-tips {
  width: 100%;
}

.receipt-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 导入对话框样式 */
.import-dialog-content {
  padding: 24px;
}

.import-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.3s ease;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
}

.step-number span {
  font-size: 16px;
}

.step-content {
  flex: 1;
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #eaeefb;
  transition: all 0.3s ease;
}

.step-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.step-content h4 {
  margin: 0 0 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 12px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.template-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.import-instructions {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.import-instructions li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.import-instructions li:last-child {
  margin-bottom: 0;
}

.import-upload {
  margin-top: 16px;
  width: 100%;
}

.import-upload :deep(.el-upload-dragger) {
  width: 100%;
  padding: 24px;
  border-radius: 8px;
  border: 1px dashed #c0c4cc;
  transition: all 0.3s ease;
}

.import-upload :deep(.el-upload-dragger:hover) {
  border-color: var(--el-color-primary);
  background-color: rgba(64, 158, 255, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-text span {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.upload-text p {
  margin: 8px 0 0;
  color: #909399;
  font-size: 14px;
}

/* 列设置对话框样式 */
.column-settings-content {
  padding: 24px;
}

.column-settings-tip {
  display: flex;
  align-items: center;
  background-color: #f0f9eb;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e1f3d8;
}

.tip-icon {
  font-size: 18px;
  color: #67c23a;
  margin-right: 10px;
}

.column-scrollbar {
  border: 1px solid #eaeefb;
  border-radius: 8px;
  background-color: #fff;
}

.column-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeefb;
  transition: all 0.3s ease;
  background-color: #fff;
}

.column-item:last-child {
  border-bottom: none;
}

.column-item:hover {
  background-color: #f9fafc;
}

.column-drag {
  margin-right: 12px;
  cursor: move;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.column-drag:hover {
  background-color: #ecf5ff;
}

.drag-handle {
  font-size: 16px;
  color: #909399;
  transition: all 0.3s ease;
}

.column-drag:hover .drag-handle {
  color: var(--el-color-primary);
}

.column-checkbox {
  flex: 1;
}

.column-checkbox :deep(.el-checkbox__label) {
  font-size: 14px;
  color: #303133;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb !important;
}
</style>
