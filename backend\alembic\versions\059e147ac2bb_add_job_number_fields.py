"""add_job_number_fields

Revision ID: 059e147ac2bb
Revises: 4e74cd204a44
Create Date: 2025-04-24 15:29:08.652169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '059e147ac2bb'
down_revision: Union[str, None] = '4e74cd204a44'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_ad_sync_locks_lock_name', table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    op.drop_index('ix_email_department_id', table_name='email_department')
    op.drop_table('email_department')
    op.drop_index('ix_email_sync_log_id', table_name='email_sync_log')
    op.drop_table('email_sync_log')
    op.drop_index('ix_email_config_id', table_name='email_config')
    op.drop_table('email_config')
    op.drop_index('ix_email_departments_id', table_name='email_departments')
    op.drop_table('email_departments')
    op.drop_index('ix_email_sync_logs_id', table_name='email_sync_logs')
    op.drop_table('email_sync_logs')
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.drop_index('ix_email_group_id', table_name='email_group')
    op.drop_table('email_group')
    op.drop_index('ix_email_users_id', table_name='email_users')
    op.drop_table('email_users')
    op.drop_index('ix_email_account_id', table_name='email_account')
    op.drop_table('email_account')
    op.add_column('assets', sa.Column('purchaser_job_number', sa.String(length=50), nullable=True, comment='采购人工号'))
    op.add_column('assets', sa.Column('custodian_job_number', sa.String(length=50), nullable=True, comment='领用人工号'))
    op.add_column('assets', sa.Column('user_job_number', sa.String(length=50), nullable=True, comment='使用人工号'))
    op.add_column('assets', sa.Column('inspector_job_number', sa.String(length=50), nullable=True, comment='验收人工号'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assets', 'inspector_job_number')
    op.drop_column('assets', 'user_job_number')
    op.drop_column('assets', 'custodian_job_number')
    op.drop_column('assets', 'purchaser_job_number')
    op.create_table('email_account',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('userid', sa.VARCHAR(length=100), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('department', sqlite.JSON(), nullable=True),
    sa.Column('position', sa.VARCHAR(length=100), nullable=True),
    sa.Column('mobile', sa.VARCHAR(length=100), nullable=True),
    sa.Column('email', sa.VARCHAR(length=100), nullable=False),
    sa.Column('password', sa.VARCHAR(length=100), nullable=True),
    sa.Column('status', sa.INTEGER(), nullable=True),
    sa.Column('is_synced', sa.BOOLEAN(), nullable=True),
    sa.Column('last_sync_time', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_account_id', 'email_account', ['id'], unique=False)
    op.create_table('email_users',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('userid', sa.VARCHAR(length=100), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('department', sa.VARCHAR(length=255), nullable=True),
    sa.Column('position', sa.VARCHAR(length=100), nullable=True),
    sa.Column('mobile', sa.VARCHAR(length=20), nullable=True),
    sa.Column('email', sa.VARCHAR(length=100), nullable=False),
    sa.Column('gender', sa.INTEGER(), nullable=True),
    sa.Column('status', sa.INTEGER(), nullable=True),
    sa.Column('enable_status', sa.INTEGER(), nullable=True),
    sa.Column('alias', sa.VARCHAR(length=100), nullable=True),
    sa.Column('password_status', sa.INTEGER(), nullable=True),
    sa.Column('synced_at', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('userid')
    )
    op.create_index('ix_email_users_id', 'email_users', ['id'], unique=False)
    op.create_table('email_group',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('groupid', sa.VARCHAR(length=100), nullable=False),
    sa.Column('groupname', sa.VARCHAR(length=100), nullable=False),
    sa.Column('userlist', sqlite.JSON(), nullable=True),
    sa.Column('groupmail', sa.VARCHAR(length=100), nullable=False),
    sa.Column('create_time', sa.DATETIME(), nullable=True),
    sa.Column('is_synced', sa.BOOLEAN(), nullable=True),
    sa.Column('last_sync_time', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_group_id', 'email_group', ['id'], unique=False)
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.create_table('email_sync_logs',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('sync_type', sa.VARCHAR(length=50), nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), nullable=False),
    sa.Column('message', sa.TEXT(), nullable=True),
    sa.Column('details', sa.TEXT(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_sync_logs_id', 'email_sync_logs', ['id'], unique=False)
    op.create_table('email_departments',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('dept_id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('parent_id', sa.INTEGER(), nullable=True),
    sa.Column('order', sa.INTEGER(), nullable=True),
    sa.Column('synced_at', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('dept_id')
    )
    op.create_index('ix_email_departments_id', 'email_departments', ['id'], unique=False)
    op.create_table('email_config',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('corp_id', sa.VARCHAR(length=100), nullable=False),
    sa.Column('secret', sa.VARCHAR(length=100), nullable=False),
    sa.Column('access_token', sa.VARCHAR(length=255), nullable=True),
    sa.Column('token_expires_at', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_config_id', 'email_config', ['id'], unique=False)
    op.create_table('email_sync_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('operation', sa.VARCHAR(length=50), nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), nullable=False),
    sa.Column('details', sa.TEXT(), nullable=True),
    sa.Column('operator', sa.VARCHAR(length=100), nullable=True),
    sa.Column('operation_time', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_sync_log_id', 'email_sync_log', ['id'], unique=False)
    op.create_table('email_department',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('dept_id', sa.INTEGER(), nullable=False),
    sa.Column('parentid', sa.INTEGER(), nullable=True),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('path', sa.VARCHAR(length=512), nullable=True),
    sa.Column('order', sa.INTEGER(), nullable=True),
    sa.Column('is_synced', sa.BOOLEAN(), nullable=True),
    sa.Column('last_sync_time', sa.DATETIME(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_email_department_id', 'email_department', ['id'], unique=False)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), nullable=True),
    sa.Column('locked_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index('ix_ad_sync_locks_lock_name', 'ad_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###
