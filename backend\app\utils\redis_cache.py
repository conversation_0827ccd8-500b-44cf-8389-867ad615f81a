import json
import pickle
import logging
from typing import Any, Optional, Union, List, Dict
import redis
import sys
from ..config import settings
from .redis_logger import log_redis_operation, stats_tracker
from ..core.cache_config import (
    CacheDataType, CacheStrategy, CacheConfigManager, CacheKeyManager,
    cache_config_manager, get_data_type_for_business, get_ttl_for_business
)

# 使用标准logger，依赖于应用级的日志配置
logger = logging.getLogger(__name__)

class RedisCache:
    """Redis缓存实现"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisCache, cls).__new__(cls)
            cls._instance._redis = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB,
                decode_responses=False,  # 不自动解码，以支持二进制序列化数据
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True
            )
            cls._instance._connected = False
            # 尝试连接Redis
            try:
                cls._instance._redis.ping()
                cls._instance._connected = True
                logger.info(f"Redis连接成功: {settings.REDIS_HOST}:{settings.REDIS_PORT}")
            except redis.exceptions.ConnectionError as e:
                logger.error(f"Redis连接失败: {str(e)}")
        return cls._instance
    
    @log_redis_operation("get")
    def get(self, key: str) -> Any:
        """获取缓存数据"""
        try:
            data = self._redis.get(key)
            if data:
                logger.debug(f"Redis缓存命中: {key}")
                return pickle.loads(data)
            logger.debug(f"Redis缓存未命中: {key}")
            return None
        except Exception as e:
            logger.error(f"Redis获取数据失败: {str(e)}, key={key}")
            return None
    
    @log_redis_operation("set")
    def set(self, key: str, value: Any, ttl: int = None, 
            data_type: Union[CacheDataType, str] = None,
            business_key: str = None) -> bool:
        """设置缓存数据
        
        Args:
            key: 缓存键
            value: 缓存值(任何可序列化的对象)
            ttl: 过期时间(秒)，如果不指定则根据数据类型自动选择
            data_type: 数据类型，用于自动选择TTL
            business_key: 业务键，用于自动选择TTL
        
        Returns:
            bool: 是否设置成功
        """
        # 智能TTL选择
        if ttl is None:
            ttl = self._get_smart_ttl(data_type, business_key)
            
        try:
            serialized = pickle.dumps(value)
            result = self._redis.setex(key, ttl, serialized)
            logger.debug(f"Redis缓存设置: {key}, TTL={ttl}秒, 数据类型={data_type or business_key or 'default'}")
            return result
        except Exception as e:
            logger.error(f"Redis设置数据失败: {str(e)}, key={key}")
            return False
    
    @log_redis_operation("delete")
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            result = bool(self._redis.delete(key))
            logger.debug(f"Redis缓存删除: {key}, 结果={result}")
            return result
        except Exception as e:
            logger.error(f"Redis删除数据失败: {str(e)}, key={key}")
            return False
    
    @log_redis_operation("clear_pattern")
    def clear_pattern(self, pattern: str) -> int:
        """按模式清除缓存
        
        Args:
            pattern: Redis键模式，如"user:*"
            
        Returns:
            int: 清除的键数量
        """
        try:
            keys = self._redis.keys(pattern)
            if keys:
                result = self._redis.delete(*keys)
                logger.info(f"Redis模式清除: {pattern}, 清除键数量={result}")
                return result
            logger.debug(f"Redis模式清除: {pattern}, 未找到匹配的键")
            return 0
        except Exception as e:
            logger.error(f"Redis清除模式缓存失败: {str(e)}, pattern={pattern}")
            return 0
    
    @log_redis_operation("clear_all")
    def clear_all(self) -> bool:
        """清除所有缓存数据"""
        try:
            result = self._redis.flushdb()
            logger.warning("Redis清除所有缓存数据")
            return result
        except Exception as e:
            logger.error(f"Redis清除所有数据失败: {str(e)}")
            return False
    
    def get_metrics(self) -> dict:
        """获取Redis监控信息"""
        try:
            info = self._redis.info()
            metrics = {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "total_keys": self._redis.dbsize(),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
            logger.debug(f"Redis监控信息: {metrics}")
            return metrics
        except Exception as e:
            logger.error(f"获取Redis监控信息失败: {str(e)}")
            return {}
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return stats_tracker.get_stats()
    
    def reset_stats(self) -> None:
        """重置统计数据"""
        stats_tracker.reset()
    
    def _get_smart_ttl(self, data_type: Union[CacheDataType, str] = None,
                      business_key: str = None) -> int:
        """智能TTL选择
        
        优先级：business_key > data_type > 默认值
        """
        if business_key:
            return get_ttl_for_business(business_key)
        elif data_type:
            return cache_config_manager.get_ttl(data_type)
        else:
            return settings.REDIS_TTL
    
    def set_with_business_key(self, key: str, value: Any, business_key: str) -> bool:
        """使用业务键设置缓存（自动选择TTL）"""
        return self.set(key, value, business_key=business_key)
    
    def set_with_data_type(self, key: str, value: Any, data_type: CacheDataType) -> bool:
        """使用数据类型设置缓存（自动选择TTL）"""
        return self.set(key, value, data_type=data_type)
    
    @log_redis_operation("mget")
    def get_multiple(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存数据
        
        Args:
            keys: 缓存键列表
            
        Returns:
            Dict[str, Any]: {key: value} 映射，不存在的键不会出现在结果中
        """
        if not keys:
            return {}
            
        try:
            values = self._redis.mget(keys)
            result = {}
            
            for key, value in zip(keys, values):
                if value is not None:
                    try:
                        result[key] = pickle.loads(value)
                        logger.debug(f"Redis批量获取命中: {key}")
                    except Exception as e:
                        logger.warning(f"Redis反序列化失败: {key}, {str(e)}")
                else:
                    logger.debug(f"Redis批量获取未命中: {key}")
            
            return result
        except Exception as e:
            logger.error(f"Redis批量获取失败: {str(e)}, keys={keys}")
            return {}
    
    @log_redis_operation("mset")
    def set_multiple(self, data: Dict[str, Any], ttl: int = None,
                    data_type: Union[CacheDataType, str] = None,
                    business_key: str = None) -> bool:
        """批量设置缓存数据
        
        Args:
            data: {key: value} 映射
            ttl: 过期时间（秒）
            data_type: 数据类型
            business_key: 业务键
            
        Returns:
            bool: 是否全部设置成功
        """
        if not data:
            return True
            
        # 智能TTL选择
        if ttl is None:
            ttl = self._get_smart_ttl(data_type, business_key)
        
        try:
            # 序列化所有数据
            serialized_data = {}
            for key, value in data.items():
                serialized_data[key] = pickle.dumps(value)
            
            # 使用pipeline批量操作
            pipe = self._redis.pipeline()
            
            # 批量设置
            pipe.mset(serialized_data)
            
            # 批量设置过期时间
            for key in data.keys():
                pipe.expire(key, ttl)
            
            # 执行所有操作
            results = pipe.execute()
            
            success = all(results)
            logger.debug(f"Redis批量设置: {len(data)}个键, TTL={ttl}秒, 成功={success}")
            return success
            
        except Exception as e:
            logger.error(f"Redis批量设置失败: {str(e)}, keys={list(data.keys())}")
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存键是否存在"""
        try:
            return bool(self._redis.exists(key))
        except Exception as e:
            logger.error(f"Redis检查键存在失败: {str(e)}, key={key}")
            return False
    
    def exists_multiple(self, keys: List[str]) -> Dict[str, bool]:
        """批量检查缓存键是否存在"""
        if not keys:
            return {}
            
        try:
            results = self._redis.exists(*keys)
            # Redis EXISTS 返回存在的键的数量，我们需要逐个检查
            exists_results = {}
            for key in keys:
                exists_results[key] = bool(self._redis.exists(key))
            
            return exists_results
        except Exception as e:
            logger.error(f"Redis批量检查键存在失败: {str(e)}, keys={keys}")
            return {key: False for key in keys}
    
    def get_ttl(self, key: str) -> int:
        """获取缓存键的剩余TTL
        
        Returns:
            int: 剩余秒数，-1表示永不过期，-2表示键不存在
        """
        try:
            return self._redis.ttl(key)
        except Exception as e:
            logger.error(f"Redis获取TTL失败: {str(e)}, key={key}")
            return -2
    
    def set_ttl(self, key: str, ttl: int) -> bool:
        """设置缓存键的TTL"""
        try:
            return bool(self._redis.expire(key, ttl))
        except Exception as e:
            logger.error(f"Redis设置TTL失败: {str(e)}, key={key}, ttl={ttl}")
            return False
    
    def get_cache_info(self, pattern: str = None) -> Dict[str, Any]:
        """获取缓存信息
        
        Args:
            pattern: 键模式，如"request_cache:*"
            
        Returns:
            缓存信息字典
        """
        try:
            if pattern:
                keys = self._redis.keys(pattern)
            else:
                keys = self._redis.keys("*")
            
            info = {
                "total_keys": len(keys),
                "keys_by_prefix": {},
                "memory_usage": "N/A",  # Redis版本>=4.0才支持MEMORY USAGE
                "server_info": self.get_metrics()
            }
            
            # 按前缀分组统计
            for key in keys:
                if isinstance(key, bytes):
                    key = key.decode('utf-8')
                prefix = key.split(':')[0] if ':' in key else 'no_prefix'
                info["keys_by_prefix"][prefix] = info["keys_by_prefix"].get(prefix, 0) + 1
            
            return info
            
        except Exception as e:
            logger.error(f"Redis获取缓存信息失败: {str(e)}")
            return {"error": str(e)} 