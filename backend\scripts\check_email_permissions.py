#!/usr/bin/env python3
"""
腾讯企业邮箱API权限检查脚本

此脚本用于诊断邮箱同步功能中的权限问题，特别是 602005 错误
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService
from app.models.email import EmailConfig
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def check_email_permissions():
    """检查邮箱API权限"""
    db = SessionLocal()
    
    try:
        logger.info("开始检查腾讯企业邮箱API权限...")
        
        # 1. 检查数据库中的邮箱配置
        configs = db.query(EmailConfig).filter(EmailConfig.is_active == True).all()
        logger.info(f"找到 {len(configs)} 个活跃的邮箱配置:")
        
        for config in configs:
            logger.info(f"  - ID: {config.id}, 应用名称: {config.app_name}, Corp ID: {config.corp_id}")
        
        # 2. 检查"功能设置"应用配置
        function_config = db.query(EmailConfig).filter(
            EmailConfig.app_name == "功能设置",
            EmailConfig.is_active == True
        ).first()
        
        if not function_config:
            logger.error("❌ 未找到'功能设置'应用配置")
            return False
        
        logger.info(f"✅ 找到'功能设置'应用配置: ID={function_config.id}")
        
        # 3. 测试访问token获取
        logger.info("测试access_token获取...")
        try:
            api_service = TencentEmailAPIService(db, app_name="功能设置")
            token = await api_service.get_access_token()
            logger.info(f"✅ 成功获取access_token: {token[:20]}...")
        except Exception as e:
            logger.error(f"❌ 获取access_token失败: {str(e)}")
            return False
        
        # 4. 测试部门列表API（基础权限测试）
        logger.info("测试部门列表API...")
        try:
            result = await api_service.get_department_list()
            if result.errcode == 0:
                logger.info("✅ 部门列表API调用成功")
            else:
                logger.error(f"❌ 部门列表API调用失败: {result.errmsg}")
                return False
        except Exception as e:
            logger.error(f"❌ 部门列表API调用异常: {str(e)}")
            return False
        
        # 5. 获取测试用户列表
        logger.info("获取测试用户列表...")
        try:
            result = await api_service.get_department_members_detail("1", fetch_child=1)
            if result.errcode == 0:
                members = result.data.get("userlist", [])
                logger.info(f"✅ 获取到 {len(members)} 个用户")
                
                # 选择前3个用户进行权限测试
                test_users = members[:3]
                if not test_users:
                    logger.warning("⚠️  没有用户可供测试")
                    return True
                
            else:
                logger.error(f"❌ 获取用户列表失败: {result.errmsg}")
                return False
        except Exception as e:
            logger.error(f"❌ 获取用户列表异常: {str(e)}")
            return False
        
        # 6. 测试用户权限获取API
        logger.info("测试用户权限获取API...")
        success_count = 0
        failed_count = 0
        
        for user in test_users:
            userid = user.get("userid")
            username = user.get("name", "未知")
            logger.info(f"测试用户: {username} ({userid})")
            
            try:
                result = await api_service.get_user_option(userid, [1, 2, 3, 4])
                if result.errcode == 0:
                    logger.info(f"  ✅ 成功获取用户权限")
                    success_count += 1
                else:
                    logger.error(f"  ❌ 获取用户权限失败: errcode={result.errcode}, errmsg={result.errmsg}")
                    failed_count += 1
                    
                    # 特别处理 602005 错误
                    if result.errcode == 602005:
                        logger.error(f"  🚫 权限错误 602005: '功能设置'应用缺少对用户 {userid} 的查看权限")
                        
            except Exception as e:
                logger.error(f"  ❌ 获取用户权限异常: {str(e)}")
                failed_count += 1
        
        # 7. 总结结果
        logger.info(f"\n=== 权限检查结果 ===")
        logger.info(f"测试用户数量: {len(test_users)}")
        logger.info(f"权限获取成功: {success_count}")
        logger.info(f"权限获取失败: {failed_count}")
        
        if failed_count > 0:
            logger.error(f"\n🚫 发现权限问题！")
            logger.error(f"解决方案:")
            logger.error(f"1. 登录腾讯企业邮箱管理后台")
            logger.error(f"2. 进入'管理工具' -> '第三方应用'")
            logger.error(f"3. 找到'功能设置'应用")
            logger.error(f"4. 检查并配置以下权限:")
            logger.error(f"   - 用户管理权限")
            logger.error(f"   - 查看指定成员权限")
            logger.error(f"   - 用户选项管理权限")
            logger.error(f"5. 确保'功能设置'应用有权访问所有需要同步的用户")
            return False
        else:
            logger.info(f"✅ 所有权限检查通过！")
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查过程中发生异常: {str(e)}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = asyncio.run(check_email_permissions())
    sys.exit(0 if success else 1) 