import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import App from './App.vue'
import router from './router/index'
import { createPinia } from 'pinia'
import { setupPermissionDirectives } from './directives/permission'
import 'element-plus/dist/index.css'
import 'vant/lib/index.css'
import './assets/styles.css'
import './assets/pagination-fix.css'
import './mobile/styles/index.scss'
import './mobile/styles/theme.scss'
import Authority from './components/Authority/index.vue'
// 导入移动端视口工具
import { initializeViewportUtils } from './mobile/utils/viewport'

// 初始化应用
const initializeApp = async () => {
  const app = createApp(App)

  // 注册所有图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  // 设置Pinia状态管理
  const pinia = createPinia()
  app.use(pinia)

  // 预初始化用户状态
  const { useUserStore } = await import('./stores/user')
  const userStore = useUserStore()
  
  // 如果有token，预加载用户信息（不阻塞应用启动）
  if (userStore.token) {
    userStore.initializeAuth().catch(() => {
      // 初始化失败不影响应用启动，路由守卫会处理
    })
  }

  // 设置插件
  app.use(router)
  app.use(ElementPlus, {
    locale: zhCn,
  })

  // 注册全局组件
  app.component('Authority', Authority)

  // 注册自定义指令
  setupPermissionDirectives(app)

  // 初始化移动端视口工具
  initializeViewportUtils()

  app.mount('#app')
}

// 启动应用
initializeApp().catch(error => {
  console.error('[Main] 应用初始化失败:', error)
}) 