from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, JSON
from sqlalchemy.sql import func
from ..database import Base

class LdapConfig(Base):
    __tablename__ = "ldap_config"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="配置名称")
    server = Column(String(255), nullable=False, comment="LDAP服务器地址")
    port = Column(Integer, default=389, comment="LDAP服务器端口")
    use_ssl = Column(Boolean, default=False, comment="是否使用SSL")
    base_dn = Column(String(500), nullable=False, comment="Base DN")
    bind_dn = Column(String(500), nullable=True, comment="绑定用户DN")
    bind_password = Column(String(255), nullable=True, comment="绑定用户密码")
    user_search_base = Column(String(500), nullable=True, comment="用户搜索Base DN")
    user_search_filter = Column(String(200), default="(sAMAccountName={username})", comment="用户搜索过滤器")
    user_name_attr = Column(String(50), default="sAMAccountName", comment="用户名属性")
    user_email_attr = Column(String(50), default="mail", comment="邮箱属性")
    user_display_name_attr = Column(String(50), default="displayName", comment="显示名称属性")
    auto_create_user = Column(Boolean, default=True, comment="自动创建用户")
    default_role = Column(String(50), default="user", comment="默认角色")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否为默认配置")
    description = Column(Text, nullable=True, comment="配置描述")
    ip_ranges = Column(JSON, nullable=True, comment="IP网段配置列表，支持CIDR和范围格式")
    priority = Column(Integer, default=1, comment="匹配优先级，数字越小优先级越高")
    auto_select_enabled = Column(Boolean, default=True, comment="是否启用基于IP的自动选择")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间") 