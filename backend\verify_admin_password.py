#!/usr/bin/env python3
from app.database import SessionLocal
from app.models.user import User
from app.core.security import verify_password

def verify_admin_password():
    db = SessionLocal()
    try:
        admin = db.query(User).filter(User.username == 'admin').first()
        if not admin:
            print('❌ admin用户不存在')
            return
        
        print(f'✅ 找到admin用户: {admin.username} / {admin.email}')
        
        # 测试常见密码
        passwords = ['123456', 'admin', 'admin123', 'password']
        
        for pwd in passwords:
            is_valid = verify_password(pwd, admin.hashed_password)
            print(f'   密码 "{pwd}": {"✅ 正确" if is_valid else "❌ 错误"}')
            if is_valid:
                print(f'   👉 正确密码是: {pwd}')
                break
                
    finally:
        db.close()

if __name__ == '__main__':
    verify_admin_password() 