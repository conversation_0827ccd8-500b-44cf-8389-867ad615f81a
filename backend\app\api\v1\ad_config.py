from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ... import models, schemas
from ...database import get_db
from ...services import ad_config as ad_config_service
from ...utils import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/config")
async def get_ad_config(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取AD配置"""
    try:
        config = await ad_config_service.get_ad_config(db)
        # 返回所有字段
        return {
            "server": config.server,
            "domain": config.domain,
            "username": config.username,
            "search_base": config.search_base,
            "use_ssl": config.use_ssl,
            "port": config.port,
            "id": config.id
        }
    except Exception as e:
        logger.error(f"获取AD配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/config", response_model=schemas.ADConfigResponse)
async def update_config(
    config: schemas.ADConfigCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新AD配置"""
    return await ad_config_service.update_ad_config(db, config)

@router.post("/config/test")
async def test_config(
    config: schemas.ADConfigCreate = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """测试AD配置"""
    try:
        if not config:
            db_config = await ad_config_service.get_ad_config(db)
            config = schemas.ADConfigCreate(
                server=db_config.server,
                domain=db_config.domain,
                username=db_config.username,
                password=db_config.password,
                search_base=db_config.search_base,
                use_ssl=db_config.use_ssl,
                port=db_config.port
            )
        return await ad_config_service.test_config(config)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        ) 