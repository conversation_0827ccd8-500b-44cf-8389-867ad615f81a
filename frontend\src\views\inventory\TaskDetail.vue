<template>
  <div class="inventory-task-detail">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Document /></el-icon>
        <h2 class="page-title">盘点任务详情</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/inventory/tasks' }">资产管理</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/inventory/tasks' }">资产盘点</el-breadcrumb-item>
        <el-breadcrumb-item>盘点任务详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover" v-loading="loading">
      <div class="toolbar">
        <div class="task-info">
          <el-button @click="router.back()" class="back-btn">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <div class="title-container">
            <h3 class="task-title">{{ task?.name }}</h3>
            <el-tag :type="getStatusType(task?.status)">
              {{ getStatusText(task?.status) }}
            </el-tag>
          </div>
        </div>
        <div class="action-buttons">
          <el-dropdown trigger="click" @command="handleExport">
            <el-button type="default" :loading="exporting">
              <Authority permission="inventory:export">
                <el-icon><Download /></el-icon>
                {{ exporting ? '导出中...' : '导出数据' }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </Authority>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="excel">导出Excel</el-dropdown-item>
                <el-dropdown-item command="csv">导出CSV</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            v-if="task?.status === 'pending'"
            type="primary"
            @click="handleStart"
          >
            <Authority permission="inventory:edit">
              开始盘点
            </Authority>
          </el-button>
          <el-button
            v-if="task?.status === 'in_progress'"
            type="success"
            @click="handleComplete"
          >
            <Authority permission="inventory:edit">
              完成盘点
            </Authority>
          </el-button>
        </div>
      </div>

      <!-- 任务信息 -->
      <el-descriptions :column="3" border>
        <el-descriptions-item label="任务描述" :span="3">
          {{ task?.description || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="开始日期">
          {{ task?.start_date }}
        </el-descriptions-item>
        <el-descriptions-item label="结束日期">
          {{ task?.end_date }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">
          {{ task?.created_by }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 盘点进度 -->
      <div class="progress-section">
        <h3>盘点进度</h3>
        <div class="progress-cards">
          <el-card shadow="hover" class="progress-card">
            <template #header>
              <div class="progress-card-header">
                <span>总资产数</span>
              </div>
            </template>
            <div class="progress-card-content">
              <span class="number">{{ statistics.total }}</span>
              <span class="label">个</span>
            </div>
          </el-card>

          <el-card shadow="hover" class="progress-card">
            <template #header>
              <div class="progress-card-header">
                <span>已盘点</span>
              </div>
            </template>
            <div class="progress-card-content">
              <span class="number">{{ statistics.checked }}</span>
              <span class="label">个</span>
            </div>
          </el-card>

          <el-card shadow="hover" class="progress-card">
            <template #header>
              <div class="progress-card-header">
                <span>待盘点</span>
              </div>
            </template>
            <div class="progress-card-content">
              <span class="number">{{ statistics.pending }}</span>
              <span class="label">个</span>
            </div>
          </el-card>

          <el-card shadow="hover" class="progress-card">
            <template #header>
              <div class="progress-card-header">
                <span>异常</span>
              </div>
            </template>
            <div class="progress-card-content">
              <span class="number">{{ statistics.abnormal }}</span>
              <span class="label">个</span>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 资产列表 -->
      <div class="asset-list-section">
        <div class="section-header">
          <h3>资产列表</h3>
          <div class="filters">
            <el-select
              v-model="searchForm.status"
              placeholder="盘点状态"
              clearable
              @change="handleSearch"
            >
              <el-option label="待盘点" value="pending" />
              <el-option label="正常" value="normal" />
              <el-option label="异常" value="abnormal" />
              <el-option label="丢失" value="missing" />
              <el-option label="信息变更" value="info_changed" />
            </el-select>
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索资产编号/名称"
              class="search-input"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <el-table
          :data="records"
          v-loading="tableLoading"
          style="width: 100%"
          :cell-style="{ padding: '8px 0' }"
          header-row-class-name="table-header"
          header-cell-class-name="table-header-cell"
        >
          <el-table-column prop="asset.asset_number" label="资产编号" width="150">
            <template #header>
              <div class="column-header">资产编号</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.name" label="资产名称" min-width="200">
            <template #header>
              <div class="column-header">资产名称</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.specification" label="规格型号" width="150">
            <template #header>
              <div class="column-header">规格型号</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.company" label="公司" width="150">
            <template #header>
              <div class="column-header">公司</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.custodian" label="领用人" width="120">
            <template #header>
              <div class="column-header">领用人</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.custodian_department" label="领用人部门" width="150">
            <template #header>
              <div class="column-header">领用人部门</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.user" label="使用人" width="120">
            <template #header>
              <div class="column-header">使用人</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.user_department" label="使用人部门" width="150">
            <template #header>
              <div class="column-header">使用人部门</div>
            </template>
          </el-table-column>
          <el-table-column prop="asset.location" label="存放位置" width="150">
            <template #header>
              <div class="column-header">存放位置</div>
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="盘点状态" width="120">
            <template #header>
              <div class="column-header">盘点状态</div>
            </template>
            <template #default="{ row }">
              <el-tag :type="getRecordStatusType(row.status)">
                {{ getRecordStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="checked_by" label="盘点人" width="120">
            <template #header>
              <div class="column-header">盘点人</div>
            </template>
          </el-table-column>
          <el-table-column prop="checked_at" label="盘点时间" width="180">
            <template #header>
              <div class="column-header">盘点时间</div>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100" fixed="right">
            <template #header>
              <div class="column-header">操作</div>
            </template>
            <template #default="{ row }">
              <el-dropdown v-if="task?.status !== 'completed'" trigger="click">
                <el-button type="primary" link>
                  操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <Authority permission="inventory:edit">
                      <el-dropdown-item @click="showCheckDialog(row)">
                        <el-icon><Edit /></el-icon>{{ row.status === 'pending' ? '盘点' : '修改' }}
                      </el-dropdown-item>
                    </Authority>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <Authority permission="inventory:view">
            <el-pagination
              v-model:current-page="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </Authority>
        </div>
      </div>
    </el-card>

    <!-- 盘点对话框 -->
    <el-dialog
      v-model="checkDialogVisible"
      :title="selectedRecord?.status === 'pending' ? '资产盘点' : '修改盘点结果'"
      width="500px"
    >
      <el-form
        ref="checkFormRef"
        :model="checkForm"
        :rules="checkRules"
        label-width="100px"
        @submit.prevent
      >
        <el-form-item label="资产编号">
          <span>{{ selectedRecord?.asset.asset_number }}</span>
        </el-form-item>
        
        <el-form-item label="资产名称">
          <span>{{ selectedRecord?.asset.name }}</span>
        </el-form-item>
        
        <el-form-item label="盘点状态" prop="status">
          <el-select v-model="checkForm.status" placeholder="请选择盘点状态">
            <el-option label="正常" value="normal" />
            <el-option label="异常" value="abnormal" />
            <el-option label="丢失" value="missing" />
            <el-option label="信息变更" value="info_changed" />
          </el-select>
        </el-form-item>
        
        <template v-if="checkForm.status === 'info_changed'">
          <el-form-item label="新领用人" prop="new_custodian">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_custodian"
                placeholder="请选择新领用人"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.personnel"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_custodian && !fieldValueOptions.personnel.some(option => option.field_value === checkForm.new_custodian)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('personnel', checkForm.new_custodian)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新领用人部门" prop="new_custodian_department">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_custodian_department"
                placeholder="请选择新领用人部门"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.department"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_custodian_department && !fieldValueOptions.department.some(option => option.field_value === checkForm.new_custodian_department)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('department', checkForm.new_custodian_department)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新使用人" prop="new_user">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_user"
                placeholder="请选择新使用人"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.personnel"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_user && !fieldValueOptions.personnel.some(option => option.field_value === checkForm.new_user)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('personnel', checkForm.new_user)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新使用人部门" prop="new_user_department">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_user_department"
                placeholder="请选择新使用人部门"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.department"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_user_department && !fieldValueOptions.department.some(option => option.field_value === checkForm.new_user_department)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('department', checkForm.new_user_department)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新生产编号" prop="new_production_number">
            <el-input
              v-model="checkForm.new_production_number"
              placeholder="请输入新生产编号"
              :maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="新价格" prop="new_price">
            <el-input-number
              v-model="checkForm.new_price"
              placeholder="请输入新价格"
              :precision="2"
              :step="0.01"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="新供应商" prop="new_supplier">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_supplier"
                placeholder="请选择新供应商"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.supplier"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_supplier && !fieldValueOptions.supplier.some(option => option.field_value === checkForm.new_supplier)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('supplier', checkForm.new_supplier)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新制造商" prop="new_manufacturer">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_manufacturer"
                placeholder="请选择新制造商"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.manufacturer"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_manufacturer && !fieldValueOptions.manufacturer.some(option => option.field_value === checkForm.new_manufacturer)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('manufacturer', checkForm.new_manufacturer)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新采购人" prop="new_purchaser">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_purchaser"
                placeholder="请选择新采购人"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.personnel"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_purchaser && !fieldValueOptions.personnel.some(option => option.field_value === checkForm.new_purchaser)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('personnel', checkForm.new_purchaser)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>

          <el-form-item label="新存放位置" prop="new_location">
            <div class="select-with-icon">
              <el-select
                v-model="checkForm.new_location"
                placeholder="请选择新存放位置"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.location"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <Authority permission="inventory:edit">
                <el-button
                  v-if="checkForm.new_location && !fieldValueOptions.location.some(option => option.field_value === checkForm.new_location)"
                  type="primary"
                  link
                  class="add-icon"
                  @click="handleQuickAdd('location', checkForm.new_location)"
                >
                  <el-icon><Plus /></el-icon>
                </el-button>
              </Authority>
            </div>
          </el-form-item>
        </template>
        
        <!-- 自定义字段区域 -->
        <template v-if="customFields.length > 0">
          <el-divider content-position="left">
            <span style="color: var(--el-color-primary); font-weight: 500;">自定义字段</span>
          </el-divider>
          
          <DynamicForm
            ref="customFieldFormRef"
            applies-to="inventory_record"
            :initial-data="customFieldData"
            :entity-id="selectedRecord?.id"
            :task-id="selectedRecord?.task_id"
            :asset-id="selectedRecord?.asset_id"
            :mode="'edit'"
            :label-width="'120px'"
            :show-actions="false"
            @change="handleCustomFieldChange"
            @ready="handleCustomFieldsReady"
          />
        </template>
        
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="checkForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="checkDialogVisible = false">取消</el-button>
        <Authority permission="inventory:edit">
          <el-button type="primary" @click="handleCheck" :loading="submitting">
            确定
          </el-button>
        </Authority>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, FormInstance } from 'element-plus'
import { inventoryApi } from '@/api/inventory'
import { fieldValueApi } from '@/api/field_value'
import { customFieldApi } from '@/api/custom_field'
import { ArrowLeft, Search, Edit, Document, ArrowDown, Download } from '@element-plus/icons-vue'
import type { InventoryTask, InventoryTaskStatus, InventoryRecord, InventoryRecordStatus } from '@/types/inventory'
import type { CustomField, InventoryRecordCustomFieldValue } from '@/types/custom_field'
import DynamicForm from '@/components/DynamicForm/index.vue'
import Authority from '@/components/Authority/index.vue'

const router = useRouter()
const route = useRoute()

// 任务数据
const task = ref<InventoryTask>()
const loading = ref(false)

// 统计数据
const statistics = reactive({
  total: 0,
  checked: 0,
  pending: 0,
  normal: 0,
  abnormal: 0,
  missing: 0,
  info_changed: 0
})

// 表格数据
const records = ref<InventoryRecord[]>([])
const tableLoading = ref(false)

// 搜索表单
interface SearchForm {
  keyword: string
  status: string
}

const searchForm = reactive<SearchForm>({
  keyword: '',
  status: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 字段值选项
interface FieldValueOption {
  id: number
  field_value: string
}

interface FieldValueOptions {
  personnel: FieldValueOption[]
  department: FieldValueOption[]
  location: FieldValueOption[]
  supplier: FieldValueOption[]
  manufacturer: FieldValueOption[]
}

const fieldValueOptions = reactive<FieldValueOptions>({
  personnel: [],
  department: [],
  location: [],
  supplier: [],
  manufacturer: []
})

// 自定义字段相关状态
const customFields = ref<CustomField[]>([])
const customFieldData = reactive<Record<string, any>>({})
const inventoryRecordCustomFieldValues = ref<InventoryRecordCustomFieldValue[]>([])
const loadingCustomFields = ref(false)

// 加载字段选项
const loadFieldValueOptions = async () => {
  try {
    type FieldName = keyof FieldValueOptions
    const fields: FieldName[] = [
      'personnel',
      'department',
      'location',
      'supplier',
      'manufacturer'
    ]
    
    const promises = fields.map(field => fieldValueApi.getFieldValues({ field_name: field as string }))
    const results = await Promise.all(promises)
    fields.forEach((field, index) => {
      fieldValueOptions[field] = results[index].data.data
    })
  } catch (error) {
    console.error('加载字段选项失败:', error)
    ElMessage.error('加载字段选项失败')
  }
}

// 加载自定义字段
const loadCustomFields = async () => {
  try {
    loadingCustomFields.value = true
    const response = await customFieldApi.getActiveCustomFields({ applies_to: 'inventory_record' })
    customFields.value = response.data
  } catch (error) {
    console.error('加载自定义字段失败:', error)
    // 自定义字段加载失败不应影响基础功能
  } finally {
    loadingCustomFields.value = false
  }
}

// 加载盘点记录的自定义字段值
const loadInventoryRecordCustomFieldValues = async (record: InventoryRecord) => {
  try {
    let response
    if (record.id != null) {
      // 实际盘点记录
      response = await customFieldApi.getInventoryRecordCustomFieldValues(record.id)
    } else {
      // 虚拟盘点记录
      response = await customFieldApi.getInventoryVirtualRecordCustomFieldValues(record.task_id, record.asset_id)
    }
    
    inventoryRecordCustomFieldValues.value = response.data
    
    // 初始化自定义字段数据
    Object.keys(customFieldData).forEach(key => delete customFieldData[key])
    
    // 预填充现有值
    inventoryRecordCustomFieldValues.value.forEach(value => {
      if (value.custom_field) {
        customFieldData[value.custom_field.name] = value.value
      }
    })
  } catch (error) {
    console.error('加载自定义字段值失败:', error)
    // 清空自定义字段数据
    Object.keys(customFieldData).forEach(key => delete customFieldData[key])
  }
}

// 保存自定义字段值
const saveCustomFieldValues = async (record: InventoryRecord) => {
  if (customFields.value.length === 0) return

  try {
    const values = customFields.value.map(field => ({
      custom_field_id: field.id,
      value: customFieldData[field.name] || ''
    }))

    if (record.id != null) {
      await customFieldApi.batchSetInventoryRecordCustomFieldValues(record.id, { values })
    } else {
      await customFieldApi.batchSetInventoryVirtualRecordCustomFieldValues(record.task_id, record.asset_id, { values })
    }
  } catch (error) {
    console.error('保存自定义字段值失败:', error)
    ElMessage.warning('保存自定义字段值失败，但盘点记录已保存')
  }
}

// 快速添加字段值
const handleQuickAdd = async (field: keyof FieldValueOptions, value: string) => {
  try {
    const response = await fieldValueApi.createFieldValue({
      field_name: field as string,
      field_value: value
    })
    fieldValueOptions[field].push(response.data)
    ElMessage.success('添加成功')
  } catch (error) {
    console.error('添加字段值失败:', error)
    ElMessage.error('添加失败')
  }
}

// 获取任务详情
const fetchTask = async () => {
  loading.value = true
  try {
    const response = await inventoryApi.getInventoryTask(Number(route.params.id))
    task.value = response.data
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取任务详情失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  if (!route.params.id) return
  
  try {
    const response = await inventoryApi.getInventoryTaskStatistics(Number(route.params.id))
    const stats = response.data
    statistics.total = stats.total
    statistics.checked = stats.checked
    statistics.pending = stats.pending
    statistics.normal = stats.normal
    statistics.abnormal = stats.abnormal
    statistics.missing = stats.missing
    statistics.info_changed = stats.info_changed
  } catch (error: any) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取盘点记录列表
const fetchRecords = async () => {
  if (!task.value) return
  
  tableLoading.value = true
  try {
    const params: any = {
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    }

    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }
    if (searchForm.status) {
      params.status = searchForm.status
    }

    const response = await inventoryApi.getInventoryRecords(task.value.id, params)
    records.value = response.data.data
    pagination.total = response.data.total
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '获取盘点记录失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchRecords()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchRecords()
}

const handleCurrentChange = (val: number) => {
  pagination.current = val
  fetchRecords()
}

// 开始盘点
const handleStart = async () => {
  if (!task.value) return
  
  try {
    await inventoryApi.updateInventoryTask(task.value.id, {
      status: 'in_progress'
    })
    ElMessage.success('已开始盘点')
    fetchTask()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 完成盘点
const handleComplete = async () => {
  if (!task.value) return
  
  try {
    await inventoryApi.completeInventoryTask(task.value.id)
    ElMessage.success('盘点任务已完成')
    fetchTask()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 导出功能
const exporting = ref(false)

const handleExport = async (format: 'excel' | 'csv') => {
  if (!task.value || exporting.value) return
  
  try {
    exporting.value = true
    ElMessage.info(`正在生成${format === 'excel' ? 'Excel' : 'CSV'}文件，请稍候...`)
    
    const response = await inventoryApi.exportInventoryTask(task.value.id, format)
    
    // 创建下载链接
    const blob = new Blob([response.data], {
      type: format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv;charset=utf-8'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '')
    const extension = format === 'excel' ? 'xlsx' : 'csv'
    link.download = `盘点任务_${task.value.name}_${timestamp}.${extension}`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error(error.response?.data?.detail || '导出失败')
  } finally {
    exporting.value = false
  }
}

// 盘点对话框
const checkDialogVisible = ref(false)
const selectedRecord = ref<InventoryRecord>()
const checkFormRef = ref<FormInstance>()
const submitting = ref(false)

interface CheckForm {
  status: InventoryRecordStatus
  remarks: string
  new_name?: string
  new_specification?: string
  new_status?: string
  new_company?: string
  new_custodian?: string
  new_custodian_department?: string
  new_user?: string
  new_user_department?: string
  new_location?: string
  new_production_number: string
  new_price?: number
  new_supplier?: string
  new_manufacturer?: string
  new_purchaser?: string
}

const checkForm = reactive<CheckForm>({
  status: 'normal',
  remarks: '',
  new_name: '',
  new_specification: '',
  new_status: '',
  new_company: '',
  new_custodian: '',
  new_custodian_department: '',
  new_user: '',
  new_user_department: '',
  new_location: '',
  new_production_number: '',
  new_price: undefined,
  new_supplier: '',
  new_manufacturer: '',
  new_purchaser: ''
})

const checkRules = {
  status: [
    { required: true, message: '请选择盘点状态', trigger: 'change' }
  ],
  new_name: [
    { required: false, message: '请输入新的资产名称', trigger: 'blur' }
  ],
  new_specification: [
    { required: false, message: '请输入新的规格型号', trigger: 'blur' }
  ],
  new_status: [
    { required: false, message: '请输入新的资产状态', trigger: 'blur' }
  ],
  new_company: [
    { required: false, message: '请输入新的所属公司', trigger: 'blur' }
  ],
  new_custodian: [
    { required: false, message: '请输入新的领用人', trigger: 'blur' }
  ],
  new_custodian_department: [
    { required: false, message: '请输入新的领用人部门', trigger: 'blur' }
  ],
  new_user: [
    { required: false, message: '请输入新的使用人', trigger: 'blur' }
  ],
  new_user_department: [
    { required: false, message: '请输入新的使用人部门', trigger: 'blur' }
  ],
  new_location: [
    { required: false, message: '请输入新的资产存放位置', trigger: 'blur' }
  ],
  new_production_number: [
    { required: false, message: '请输入新的生产编号', trigger: 'blur' }
  ],
  new_price: [
    { required: false, message: '请输入新的价格', trigger: 'blur' }
  ],
  new_supplier: [
    { required: false, message: '请选择新的供应商', trigger: 'change' }
  ],
  new_manufacturer: [
    { required: false, message: '请选择新的制造商', trigger: 'change' }
  ],
  new_purchaser: [
    { required: false, message: '请选择新的采购人', trigger: 'change' }
  ]
}

// 显示盘点对话框
const showCheckDialog = async (record: InventoryRecord) => {
  selectedRecord.value = record
  checkForm.status = record.status === 'pending' ? 'normal' : record.status
  checkForm.remarks = record.remarks || ''
  checkForm.new_name = record.new_name || ''
  checkForm.new_specification = record.new_specification || ''
  checkForm.new_status = record.new_status || ''
  checkForm.new_company = record.new_company || ''
  checkForm.new_custodian = record.new_custodian || ''
  checkForm.new_custodian_department = record.new_custodian_department || ''
  checkForm.new_user = record.new_user || ''
  checkForm.new_user_department = record.new_user_department || ''
  checkForm.new_location = record.new_location || ''
  checkForm.new_production_number = record.new_production_number || ''
  checkForm.new_price = record.new_price
  checkForm.new_supplier = record.new_supplier || ''
  checkForm.new_manufacturer = record.new_manufacturer || ''
  checkForm.new_purchaser = record.new_purchaser || ''
  
  // 加载自定义字段值
  await loadInventoryRecordCustomFieldValues(record)
  
  checkDialogVisible.value = true
}

// 提交盘点
const handleCheck = async () => {
  if (!checkFormRef.value || !selectedRecord.value) return
  
  await checkFormRef.value.validate(async (valid: boolean) => {
    if (valid && selectedRecord.value) {
      // 如果是信息变更状态，检查是否至少填写了一项变更信息
      if (checkForm.status === 'info_changed') {
        const hasChanges = [
          checkForm.new_name,
          checkForm.new_specification,
          checkForm.new_status,
          checkForm.new_company,
          checkForm.new_custodian,
          checkForm.new_custodian_department,
          checkForm.new_user,
          checkForm.new_user_department,
          checkForm.new_location,
          checkForm.new_production_number,
          checkForm.new_price !== undefined ? String(checkForm.new_price) : '',
          checkForm.new_supplier,
          checkForm.new_manufacturer,
          checkForm.new_purchaser
        ].some(value => typeof value === 'string' && value.trim() !== '')

        if (!hasChanges) {
          ElMessage.warning('信息变更状态下至少需要填写一项变更信息')
          return
        }
      }

      submitting.value = true
      try {
        const updateData: any = {
          status: checkForm.status,
          remarks: checkForm.remarks || undefined,
          checked_by: 'admin' // TODO: 从用户状态获取
        }

        if (checkForm.status === 'info_changed') {
          // 只提交已填写的字段
          if (typeof checkForm.new_name === 'string' && checkForm.new_name.trim()) updateData.new_name = checkForm.new_name
          if (typeof checkForm.new_specification === 'string' && checkForm.new_specification.trim()) updateData.new_specification = checkForm.new_specification
          if (typeof checkForm.new_status === 'string' && checkForm.new_status.trim()) updateData.new_status = checkForm.new_status
          if (typeof checkForm.new_company === 'string' && checkForm.new_company.trim()) updateData.new_company = checkForm.new_company
          if (typeof checkForm.new_custodian === 'string' && checkForm.new_custodian.trim()) updateData.new_custodian = checkForm.new_custodian
          if (typeof checkForm.new_custodian_department === 'string' && checkForm.new_custodian_department.trim()) updateData.new_custodian_department = checkForm.new_custodian_department
          if (typeof checkForm.new_user === 'string' && checkForm.new_user.trim()) updateData.new_user = checkForm.new_user
          if (typeof checkForm.new_user_department === 'string' && checkForm.new_user_department.trim()) updateData.new_user_department = checkForm.new_user_department
          if (typeof checkForm.new_location === 'string' && checkForm.new_location.trim()) updateData.new_location = checkForm.new_location
          if (typeof checkForm.new_production_number === 'string' && checkForm.new_production_number.trim()) updateData.new_production_number = checkForm.new_production_number
          if (checkForm.new_price !== undefined) updateData.new_price = checkForm.new_price
          if (typeof checkForm.new_supplier === 'string' && checkForm.new_supplier.trim()) updateData.new_supplier = checkForm.new_supplier
          if (typeof checkForm.new_manufacturer === 'string' && checkForm.new_manufacturer.trim()) updateData.new_manufacturer = checkForm.new_manufacturer
          if (typeof checkForm.new_purchaser === 'string' && checkForm.new_purchaser.trim()) updateData.new_purchaser = checkForm.new_purchaser
        }

        // 如果是虚拟记录（没有ID），使用新的API接口
        if (selectedRecord.value.id != null) {
          await inventoryApi.updateInventoryRecord(selectedRecord.value.id, updateData)
        } else {
          await inventoryApi.updateInventoryRecordByAsset(selectedRecord.value.task_id, selectedRecord.value.asset_id, updateData)
        }
        
        // 保存自定义字段值
        await saveCustomFieldValues(selectedRecord.value)
        
        ElMessage.success('盘点成功')
        checkDialogVisible.value = false
        fetchStatistics()
        fetchRecords()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 获取任务状态类型
const getStatusType = (status?: InventoryTaskStatus): string => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'in_progress':
      return 'warning'
    case 'completed':
      return 'success'
    default:
      return 'info'
  }
}

// 获取任务状态文本
const getStatusText = (status?: InventoryTaskStatus): string => {
  switch (status) {
    case 'pending':
      return '待盘点'
    case 'in_progress':
      return '盘点中'
    case 'completed':
      return '已完成'
    default:
      return '未知'
  }
}

// 获取记录状态类型
const getRecordStatusType = (status: InventoryRecordStatus): string => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'normal':
      return 'success'
    case 'abnormal':
      return 'warning'
    case 'missing':
      return 'danger'
    case 'info_changed':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取记录状态文本
const getRecordStatusText = (status: InventoryRecordStatus): string => {
  switch (status) {
    case 'pending':
      return '待盘点'
    case 'normal':
      return '正常'
    case 'abnormal':
      return '异常'
    case 'missing':
      return '丢失'
    case 'info_changed':
      return '信息变更'
    default:
      return '未知'
  }
}

// 自定义字段处理
const handleCustomFieldChange = (field: string, value: any, allData: Record<string, any>) => {
  customFieldData.value = allData
}

const handleCustomFieldsReady = (fields: CustomField[]) => {
  customFields.value = fields
}

// 初始加载
onMounted(async () => {
  await fetchTask()
  await fetchStatistics()
  await fetchRecords()
  await loadFieldValueOptions()
  await loadCustomFields()
})
</script>

<style scoped>
.inventory-task-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-info {
  display: flex;
  align-items: center;
}

.back-btn {
  margin-right: 16px;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 表格样式统一 */
.table-header {
  background-color: var(--el-fill-color-light) !important;
}

.table-header-cell {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.progress-section {
  margin-top: 24px;
}

.progress-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.progress-card {
  text-align: center;
}

.progress-card-header {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.progress-card-content {
  padding: 16px 0;
}

.progress-card-content .number {
  font-size: 24px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.progress-card-content .label {
  margin-left: 4px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.asset-list-section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.filters {
  display: flex;
  gap: 16px;
}

.search-input {
  width: 300px;
}

.select-with-icon {
  position: relative;
  width: 100%;

  .add-icon {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    padding: 4px;
    font-size: 16px;
  }
}
</style>