import request from '@/utils/request'
import type { Asset, AssetCreate, AssetUpdate } from '@/types/asset'
import type { AssetChangeLog } from '@/types/asset_change_log'

export const assetApi = {
  // 获取资产列表
  getAssets: (params: {
    skip?: number
    limit?: number
    keyword?: string
    search_field?: string
    company?: string
    status?: string
    category?: string
    asset_number?: string
    name?: string
    custodian?: string
    custodian_department?: string
    user?: string
    user_department?: string
    location?: string
    purchase_date_start?: string
    purchase_date_end?: string
    retirement_date_start?: string
    retirement_date_end?: string
    sort_by?: string
    sort_order?: string
  }) => {
    return request.get<{ data: Asset[], total: number }>('/assets', { params })
  },

  // 获取单个资产
  getAsset: (id: number) => {
    return request.get<Asset>(`/assets/${id}`)
  },

  // 创建资产
  createAsset: (data: AssetCreate) => {
    return request.post<Asset>('/assets', data)
  },

  // 更新资产
  updateAsset: (id: number, data: AssetUpdate) => {
    return request.put<Asset>(`/assets/${id}`, data)
  },

  // 删除资产
  deleteAsset: (id: number) => {
    return request.delete(`/assets/${id}`)
  },

  // 批量导入资产
  importAssets: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/assets/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 批量导出资产
  exportAssets: (format: 'csv' | 'xlsx', assetIds?: number[]) => {
    return request.post<Blob>('/assets/export', {
      format,
      asset_ids: assetIds
    }, {
      responseType: 'blob'
    })
  },

  // 获取导入模板
  getImportTemplate: (format: 'csv' | 'xlsx') => {
    return request.get<Blob>(`/assets/import/template?format=${format}`, {
      responseType: 'blob'
    })
  },

  // 获取资产变更记录
  getAssetChangeLogs: (assetId: number, params: { skip?: number; limit?: number }) => {
    return request.get<AssetChangeLog[]>(`/assets/${assetId}/change-logs`, { params })
  }
}
