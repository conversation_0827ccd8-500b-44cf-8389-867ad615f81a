<template>
  <div class="role-management">
    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">角色列表</span>
        </div>
        <div class="action-buttons">
          <Authority permission="system:role:add">
            <el-button type="primary" @click="handleAddRole">
              <el-icon><Plus /></el-icon> 添加角色
            </el-button>
          </Authority>
        </div>
      </div>

      <el-table
        :data="paginatedRoles"
        border
        style="width: 100%"
        row-class-name="role-table-row"
        header-row-class-name="role-table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column prop="code" label="角色代码" width="180">
          <template #header>
            <div class="column-header">角色代码</div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="角色名称" width="150">
          <template #header>
            <div class="column-header">角色名称</div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述">
          <template #header>
            <div class="column-header">描述</div>
          </template>
        </el-table-column>
        <el-table-column label="默认角色" width="100">
          <template #header>
            <div class="column-header">默认角色</div>
          </template>
          <template #default="scope">
            <el-tag v-if="scope.row.is_default" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="scope">
            <el-dropdown trigger="click">
              <el-button type="primary" size="small">
                操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <Authority permission="system:role:edit">
                    <el-dropdown-item @click="handleEditRole(scope.row)">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="system:permission:assign">
                    <el-dropdown-item @click="handleAssignPermissions(scope.row)">
                      <el-icon><Setting /></el-icon>权限设置
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="system:role:delete">
                    <el-dropdown-item
                      @click="handleDeleteRole(scope.row)"
                      :disabled="scope.row.is_default === 1"
                      class="text-danger"
                    >
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="roleList.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 角色表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="formType === 'add' ? '添加角色' : '编辑角色'" width="500px">
      <el-form :model="roleForm" :rules="rules" ref="roleFormRef" label-width="100px">
        <el-form-item label="角色代码" prop="code">
          <el-input v-model="roleForm.code" :disabled="formType === 'edit'" />
        </el-form-item>
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="roleForm.description" />
        </el-form-item>
        <el-form-item label="默认角色" prop="is_default">
          <el-switch
            v-model="roleForm.is_default"
            :active-value="1"
            :inactive-value="0"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRoleForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 权限设置对话框 -->
    <el-dialog v-model="permissionDialogVisible" title="权限设置" width="700px">
      <div v-if="currentRole">
        <h3>{{ currentRole.name }} - 权限设置</h3>

        <el-tabs v-model="activePermModule" @tab-change="handlePermModuleChange">
          <el-tab-pane
            v-for="module in permissionModules"
            :key="module"
            :label="getModuleLabel(module)"
            :name="module"
          >
            <el-scrollbar height="300px">
              <el-checkbox-group v-model="selectedPermissions">
                <el-row :gutter="20">
                  <el-col :span="8" v-for="perm in getPermissionsByModule(module)" :key="perm.id">
                    <el-checkbox :label="perm.id">{{ perm.name }}</el-checkbox>
                  </el-col>
                </el-row>
              </el-checkbox-group>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePermissions">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { systemApi } from '@/api/system'
import request from '@/utils/request'
import { Plus, Edit, Setting, Delete, ArrowDown } from '@element-plus/icons-vue'

interface Role {
  id: number
  code: string
  name: string
  description: string
  is_default: number
  permissions: Permission[]
}

interface Permission {
  id: number
  code: string
  name: string
  description: string
  module: string
}

const roleList = ref<Role[]>([])
const permissionList = ref<Permission[]>([])
const permissionModules = ref<string[]>([])
const activePermModule = ref('')
const selectedPermissions = ref<number[]>([])
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const formType = ref<'add' | 'edit'>('add')
const roleFormRef = ref<FormInstance>()
const currentRole = ref<Role | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)

const roleForm = reactive({
  id: 0,
  code: '',
  name: '',
  description: '',
  is_default: 0
})

const rules = reactive<FormRules>({
  code: [
    { required: true, message: '请输入角色代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '角色代码只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ]
})

// 模块名称映射
const moduleLabels: Record<string, string> = {
  'system': '系统管理',
  'asset': '资产管理',
  'inventory': '盘点管理',
  'ad': 'AD管理',
  'ecology': '泛微管理',
  'basic-info': '基础信息',
  'terminal': '终端管理'
}

const getModuleLabel = (moduleCode: string) => {
  return moduleLabels[moduleCode] || moduleCode
}

// 按模块获取权限
const getPermissionsByModule = (module: string) => {
  return permissionList.value.filter(perm => perm.module === module)
}

// 计算分页后的角色列表
const paginatedRoles = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return roleList.value.slice(startIndex, startIndex + pageSize.value)
})

// 加载角色列表
const loadRoles = async () => {
  try {
    console.log('[RoleManagement] 开始加载角色列表')
    const response = await systemApi.getRoles()
    roleList.value = response.data
  } catch (error) {
    console.error('[RoleManagement] 加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  }
}

// 加载权限列表
const loadPermissions = async () => {
  try {
    console.log('[RoleManagement] 开始加载权限列表')
    const response = await systemApi.getPermissions()
    permissionList.value = response.data

    // 提取模块
    const modules = new Set<string>()
    permissionList.value.forEach(perm => {
      modules.add(perm.module)
    })
    permissionModules.value = Array.from(modules)

    if (permissionModules.value.length > 0) {
      activePermModule.value = permissionModules.value[0]
    }
  } catch (error) {
    console.error('[RoleManagement] 加载权限列表失败:', error)
    ElMessage.error('加载权限列表失败')
  }
}

// 添加角色
const handleAddRole = () => {
  formType.value = 'add'
  roleForm.id = 0
  roleForm.code = ''
  roleForm.name = ''
  roleForm.description = ''
  roleForm.is_default = 0
  dialogVisible.value = true
}

// 编辑角色
const handleEditRole = (row: Role) => {
  formType.value = 'edit'
  roleForm.id = row.id
  roleForm.code = row.code
  roleForm.name = row.name
  roleForm.description = row.description || ''
  roleForm.is_default = row.is_default
  dialogVisible.value = true
}

// 删除角色
const handleDeleteRole = (row: Role) => {
  if (row.is_default === 1) {
    ElMessage.warning('默认角色不能删除')
    return
  }

  ElMessageBox.confirm(
    `确定要删除角色 "${row.name}" 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await request({
        url: `/system/roles/${row.id}`,
        method: 'delete',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
        }
      })
      ElMessage.success('删除成功')
      loadRoles()
    } catch (error) {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 分配权限
const handleAssignPermissions = async (row: Role) => {
  currentRole.value = row

  // 先加载该角色的权限
  try {
    // 使用systemApi的方法
    const response = await systemApi.getRoleById(row.id)
    const roleDetail = response.data

    // 设置已选权限
    selectedPermissions.value = roleDetail.permissions.map((p: Permission) => p.id)
    permissionDialogVisible.value = true
  } catch (error) {
    console.error('加载角色权限失败:', error)
    ElMessage.error('加载角色权限失败')
  }
}

// 保存权限设置
const savePermissions = async () => {
  if (!currentRole.value) return

  try {
    // 使用systemApi的方法
    await systemApi.assignPermissions(currentRole.value.id, selectedPermissions.value)

    ElMessage.success('权限设置已保存')
    permissionDialogVisible.value = false
    loadRoles()
  } catch (error) {
    console.error('保存权限失败:', error)
    ElMessage.error('保存权限失败')
  }
}

// 提交角色表单
const submitRoleForm = async () => {
  if (!roleFormRef.value) return

  await roleFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formType.value === 'add') {
          // 使用带有授权头的请求
          await request({
            url: '/system/roles/',
            method: 'post',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
            },
            data: roleForm
          })
          ElMessage.success('添加成功')
        } else {
          // 使用带有授权头的请求
          await request({
            url: `/system/roles/${roleForm.id}`,
            method: 'put',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
            },
            data: roleForm
          })
          ElMessage.success('修改成功')
        }
        dialogVisible.value = false
        loadRoles()
      } catch (error) {
        console.error('提交角色表单失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 处理权限模块变化
const handlePermModuleChange = (tab: string) => {
  console.log('切换到权限模块', tab)
  // activePermModule 的值会自动更新，不需要额外设置
}

// 处理分页变化
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
}

onMounted(() => {
  loadRoles()
  loadPermissions()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-form {
  flex: 1;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
}

.action-buttons {
  margin-left: 16px;
  display: flex;
  gap: 8px;
}

/* 表格样式统一 */
.table-header {
  background-color: var(--el-fill-color-light) !important;
}

.table-header-cell {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.operation-buttons .el-button {
  min-width: 72px;
}

.button-group {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  gap: 5px;
}

.action-btn {
  flex: 1;
  min-width: 80px;
  margin: 0 !important;
}

.button-column {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.operation-btn {
  width: 100%;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.text-danger {
  color: var(--el-color-danger);
}
</style>