import os
import sys
import shutil
import tempfile
import subprocess
import argparse
import json
from pathlib import Path
import platform
import logging
import tomllib

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

# 依赖列表
DEPENDENCIES = [
    'grpcio',
    'grpcio-tools',
    'protobuf',
    'wmi',
    'pywin32',
    'psutil',
    'requests'
]

# 需要包含的文件
REQUIRED_FILES = [
    'agent_client.py',
    'windows_collector.py',
    'windows_service.py',
    'registry_manager.py',  # 添加注册表管理器
    'terminal_pb/__init__.py',
    'terminal_pb/terminal_pb2.py',
    'terminal_pb/terminal_pb2_grpc.py',
    # 添加其他必要文件
]

def get_version_from_pyproject():
    """从pyproject.toml读取版本号"""
    try:
        # 查找pyproject.toml文件
        script_dir = Path(__file__).parent.absolute()
        pyproject_path = script_dir.parent / "pyproject.toml"
        
        if not pyproject_path.exists():
            logger.warning(f"未找到pyproject.toml文件: {pyproject_path}")
            return "1.0.0"
        
        with open(pyproject_path, 'rb') as f:
            pyproject_data = tomllib.load(f)
        
        version = pyproject_data.get('project', {}).get('version', '1.0.0')
        logger.info(f"从pyproject.toml读取版本号: {version}")
        return version
    except Exception as e:
        logger.warning(f"读取pyproject.toml版本失败: {e}，使用默认版本1.0.0")
        return "1.0.0"

def parse_arguments():
    """解析命令行参数"""
    # 获取默认版本号
    default_version = get_version_from_pyproject()
    
    parser = argparse.ArgumentParser(description='构建终端管理Agent安装包')
    parser.add_argument('--output', '-o', type=str, default='dist', help='输出目录')
    parser.add_argument('--name', '-n', type=str, default='TerminalAgent', help='安装包名称')
    parser.add_argument('--version', '-v', type=str, default=default_version, help='Agent版本')
    parser.add_argument('--server', '-s', type=str, default='localhost:50051', help='服务器地址')
    parser.add_argument('--tls', action='store_true', help='使用TLS加密')
    parser.add_argument('--cert', '-c', type=str, help='证书文件路径')
    parser.add_argument('--icon', '-i', type=str, help='图标文件路径')
    return parser.parse_args()

def check_dependencies():
    """检查构建依赖"""
    logger.info("检查构建依赖...")

    # 检查Python版本
    python_version = platform.python_version_tuple()
    if int(python_version[0]) < 3 or (int(python_version[0]) == 3 and int(python_version[1]) < 6):
        logger.error("需要Python 3.6或更高版本")
        return False

    # 检查uv是否可用
    try:
        subprocess.run(['uv', '--version'], check=True, capture_output=True)
        logger.info("uv包管理器可用")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("uv包管理器不可用，请先安装uv")
        return False

    # 检查必要的库
    try:
        # 使用uv安装pyinstaller
        subprocess.run(['uv', 'add', 'pyinstaller'], check=True)
        
        # 检查其他依赖
        missing_deps = []
        for dep in DEPENDENCIES:
            try:
                __import__(dep.split('==')[0].strip())
            except ImportError:
                missing_deps.append(dep)

        if missing_deps:
            logger.warning(f"缺少以下依赖: {', '.join(missing_deps)}")
            logger.info("正在使用uv安装缺失的依赖...")
            for dep in missing_deps:
                subprocess.run(['uv', 'add', dep], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"安装依赖失败: {e}")
        return False

    logger.info("所有依赖已满足")
    return True

def prepare_files(temp_dir, args):
    """准备构建所需的文件"""
    logger.info("准备构建文件...")

    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent.absolute()

    # 创建构建目录
    build_dir = Path(temp_dir) / "build"
    os.makedirs(build_dir, exist_ok=True)

    # 复制必要文件
    for file in REQUIRED_FILES:
        source_file = script_dir / file
        target_file = build_dir / file

        # 确保目标目录存在
        os.makedirs(os.path.dirname(target_file), exist_ok=True)

        if source_file.exists():
            shutil.copy2(source_file, target_file)
            logger.info(f"已复制文件: {file}")
        else:
            logger.warning(f"文件 {file} 不存在，将被跳过")

    # 确保terminal_pb是个包
    init_file = build_dir / "terminal_pb" / "__init__.py"
    if not os.path.exists(init_file):
        os.makedirs(os.path.dirname(init_file), exist_ok=True)
        with open(init_file, 'w') as f:
            f.write("# Terminal Agent Proto Package\nfrom . import terminal_pb2\nfrom . import terminal_pb2_grpc")

    # 创建版本文件，供Agent运行时读取
    version_file = build_dir / "__version__.py"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(f'"""Agent版本信息"""\n')
        f.write(f'__version__ = "{args.version}"\n')
        f.write(f'VERSION = "{args.version}"\n')
    
    logger.info(f"已创建版本文件: {version_file}, 版本号: {args.version}")

    # 生成Agent配置
    agent_config = {
        "server_address": args.server,
        "use_tls": args.tls,
        "cert_file": args.cert if args.cert else "",
        "heartbeat_interval": 300,
        "collection_interval": 86400,
        "agent_version": args.version,
        "api_server_address": "http://" + args.server.split(":")[0] + ":8000",
        "version_check_interval": 86400,
        "auto_upgrade": False,
        "unique_id": ""
    }

    with open(build_dir / "default_config.json", 'w') as f:
        json.dump(agent_config, f, indent=4)

    # 复制证书文件(如果有)
    if args.cert and os.path.exists(args.cert):
        cert_filename = os.path.basename(args.cert)
        shutil.copy2(args.cert, build_dir / cert_filename)

    # 创建安装脚本
    create_install_script(build_dir, args)

    # 创建卸载脚本
    create_uninstall_script(build_dir)

    return build_dir

def create_install_script(build_dir, args):
    """创建Windows安装脚本"""
    install_script = """@echo off
echo 正在安装终端管理Agent...

:: 检查管理员权限
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误：需要管理员权限才能安装。请右键点击此脚本，选择"以管理员身份运行"。
    pause
    exit /b 1
)

:: 设置安装目录
set INSTALL_DIR=%ProgramFiles%\\TerminalAgent
set CONFIG_DIR=%ProgramData%\\TerminalAgent
set LOGS_DIR=%ProgramData%\\TerminalAgent\\logs

:: 创建目录
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%CONFIG_DIR%" mkdir "%CONFIG_DIR%"
if not exist "%LOGS_DIR%" mkdir "%LOGS_DIR%"

:: 停止现有服务
sc query TerminalAgent >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo 正在停止已存在的TerminalAgent服务...
    net stop TerminalAgent >nul 2>&1
    timeout /t 2
    sc delete TerminalAgent >nul 2>&1
    timeout /t 2
)

:: 复制文件
echo 正在复制文件...
:: 获取当前脚本所在目录
set SCRIPT_DIR=%~dp0

:: 只复制必要的Agent文件，避免复制系统DLL和其他不必要文件
if exist "%SCRIPT_DIR%TerminalAgent.exe" (
    copy "%SCRIPT_DIR%TerminalAgent.exe" "%INSTALL_DIR%\\TerminalAgent.exe"
    echo 已复制 TerminalAgent.exe
) else (
    echo 错误：找不到 TerminalAgent.exe 文件
    pause
    exit /b 1
)

if exist "%SCRIPT_DIR%default_config.json" (
    copy "%SCRIPT_DIR%default_config.json" "%INSTALL_DIR%\\default_config.json"
    echo 已复制 default_config.json
) else (
    echo 错误：找不到 default_config.json 文件
    pause
    exit /b 1
)

if exist "%SCRIPT_DIR%README.txt" copy "%SCRIPT_DIR%README.txt" "%INSTALL_DIR%\\README.txt"
echo 文件复制完成

:: 复制默认配置
echo 正在处理配置文件...
if not exist "%CONFIG_DIR%\\agent_config.json" (
    echo 创建新的配置文件...
    copy "%INSTALL_DIR%\\default_config.json" "%CONFIG_DIR%\\agent_config.json"
    echo 配置文件创建成功
) else (
    :: 备份原有配置
    echo 备份原有配置文件...
    copy "%CONFIG_DIR%\\agent_config.json" "%CONFIG_DIR%\\agent_config.json.bak"

    :: 更新配置文件中的版本号
    echo 更新配置文件中的版本号...
    powershell -Command "$config = Get-Content -Raw -Path '%CONFIG_DIR%\\agent_config.json' | ConvertFrom-Json; $config.agent_version = '{args.version}'; $config | ConvertTo-Json -Depth 10 | Set-Content -Path '%CONFIG_DIR%\\agent_config.json'"

    :: 检查是否成功更新
    powershell -Command "$config = Get-Content -Raw -Path '%CONFIG_DIR%\\agent_config.json' | ConvertFrom-Json; if ($config.agent_version -eq '{args.version}') {{ Write-Host '版本号更新成功' }} else {{ Write-Host '版本号更新失败，尝试备用方法' }}"

    :: 备用方法：直接复制默认配置并保留原有的unique_id
    powershell -Command "$oldConfig = Get-Content -Raw -Path '%CONFIG_DIR%\\agent_config.json.bak' | ConvertFrom-Json; $newConfig = Get-Content -Raw -Path '%INSTALL_DIR%\\default_config.json' | ConvertFrom-Json; $newConfig.unique_id = $oldConfig.unique_id; $newConfig.terminal_id = $oldConfig.terminal_id; $newConfig | ConvertTo-Json -Depth 10 | Set-Content -Path '%CONFIG_DIR%\\agent_config.json'"

    echo 配置文件处理完成
)

:: 添加到PATH (可选)
setx PATH "%PATH%;%INSTALL_DIR%" /M

:: 注册并启动服务 - 使用可执行文件安装方式，这是最可靠的方式
echo 正在注册服务...
echo 使用PyWin32服务安装方式...
"%INSTALL_DIR%\\TerminalAgent.exe" install

if %ERRORLEVEL% equ 0 (
    echo 服务安装成功，正在启动服务...
    net start TerminalAgent
    echo 服务启动完成
) else (
    echo 服务安装失败，错误代码: %ERRORLEVEL%
    echo 尝试使用sc命令创建服务...

    :: 使用SC命令创建服务
    sc create TerminalAgent binPath= "%INSTALL_DIR%\\TerminalAgent.exe" start= auto type= own obj= LocalSystem DisplayName= "终端管理Agent"

    if %ERRORLEVEL% equ 0 (
        echo SC命令创建服务成功
        echo 正在设置服务描述...
        sc description TerminalAgent "提供终端信息采集和管理功能的Agent服务"

        echo 正在启动服务...
        net start TerminalAgent
    ) else (
        echo 服务创建失败，请检查权限或手动安装
    )
)

echo 安装完成!
echo 服务名称: TerminalAgent
echo 安装目录: %INSTALL_DIR%
echo 配置目录: %CONFIG_DIR%
echo 日志目录: %LOGS_DIR%
pause
"""

    with open(build_dir / "install.bat", 'w') as f:
        f.write(install_script)

def create_uninstall_script(build_dir):
    """创建Windows卸载脚本"""
    uninstall_script = """@echo off
echo 正在卸载终端管理Agent...

:: 设置日志文件
set UNINSTALL_LOG=%TEMP%\\TerminalAgent_uninstall.log
echo 卸载开始时间: %date% %time% > "%UNINSTALL_LOG%"

:: 检查管理员权限
NET SESSION >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误：需要管理员权限才能卸载。请右键点击此脚本，选择"以管理员身份运行"。 >> "%UNINSTALL_LOG%"
    echo 错误：需要管理员权限才能卸载。请右键点击此脚本，选择"以管理员身份运行"。
    pause
    exit /b 1
)

:: 设置安装目录
set INSTALL_DIR=%ProgramFiles%\\TerminalAgent
set CONFIG_DIR=%ProgramData%\\TerminalAgent
set SERVICE_NAME=TerminalAgent

echo 安装目录: %INSTALL_DIR% >> "%UNINSTALL_LOG%"
echo 配置目录: %CONFIG_DIR% >> "%UNINSTALL_LOG%"

:: 停止服务
echo 正在停止服务...
echo 尝试停止服务... >> "%UNINSTALL_LOG%"
net stop %SERVICE_NAME% >> "%UNINSTALL_LOG%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo 警告: 服务停止失败或服务不存在，继续卸载过程... >> "%UNINSTALL_LOG%"
    echo 警告: 服务停止失败或服务不存在，继续卸载过程...
)
timeout /t 2 > nul

:: 删除服务
echo 正在删除服务...
echo 尝试删除服务... >> "%UNINSTALL_LOG%"
sc delete %SERVICE_NAME% >> "%UNINSTALL_LOG%" 2>&1
if %ERRORLEVEL% neq 0 (
    echo 警告: 服务删除失败，继续卸载过程... >> "%UNINSTALL_LOG%"
    echo 警告: 服务删除失败，继续卸载过程...
)
timeout /t 2 > nul

:: 确保进程已关闭
echo 确保进程已关闭...
echo 尝试终止相关进程... >> "%UNINSTALL_LOG%"
taskkill /f /im TerminalAgent.exe >> "%UNINSTALL_LOG%" 2>&1
timeout /t 2 > nul

:: 删除文件
echo 正在删除文件...
echo 删除安装目录: %INSTALL_DIR% >> "%UNINSTALL_LOG%"
if exist "%INSTALL_DIR%" (
    rmdir /s /q "%INSTALL_DIR%" >> "%UNINSTALL_LOG%" 2>&1
    if %ERRORLEVEL% neq 0 (
        echo 警告: 无法完全删除安装目录，可能有文件正在使用... >> "%UNINSTALL_LOG%"
        echo 警告: 无法完全删除安装目录，可能有文件正在使用...
    )
)

:: 删除开始菜单项
echo 删除开始菜单快捷方式... >> "%UNINSTALL_LOG%"
if exist "%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs\\TerminalAgent" (
    rmdir /s /q "%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs\\TerminalAgent" >> "%UNINSTALL_LOG%" 2>&1
)

:: 询问是否删除配置和日志
echo 是否删除配置和日志文件? (Y/N)
set /p DELETE_CONFIG=

if /i "%DELETE_CONFIG%"=="Y" (
    echo 用户选择删除配置和日志文件 >> "%UNINSTALL_LOG%"
    if exist "%CONFIG_DIR%" (
        echo 删除配置目录: %CONFIG_DIR% >> "%UNINSTALL_LOG%"
        rmdir /s /q "%CONFIG_DIR%" >> "%UNINSTALL_LOG%" 2>&1
        if %ERRORLEVEL% neq 0 (
            echo 警告: 无法完全删除配置目录 >> "%UNINSTALL_LOG%"
            echo 警告: 无法完全删除配置目录
        ) else (
            echo 配置和日志文件已删除。
        )
    )
) else (
    echo 用户选择保留配置和日志文件 >> "%UNINSTALL_LOG%"
    echo 配置和日志文件已保留: %CONFIG_DIR%
)

:: 清理注册表
echo 清理注册表... >> "%UNINSTALL_LOG%"
reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\TerminalAgent" /f >> "%UNINSTALL_LOG%" 2>&1
reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\\TerminalAgent.exe" /f >> "%UNINSTALL_LOG%" 2>&1

:: 清理临时文件
echo 清理临时文件... >> "%UNINSTALL_LOG%"
if exist "%TEMP%\\TerminalAgent" (
    rmdir /s /q "%TEMP%\\TerminalAgent" >> "%UNINSTALL_LOG%" 2>&1
)

echo 卸载完成时间: %date% %time% >> "%UNINSTALL_LOG%"
echo 卸载日志保存在: %UNINSTALL_LOG%
echo 卸载完成!
echo 请按任意键退出...
pause > nul
"""

    # 使用二进制模式写入，确保Windows换行符处理正确
    with open(build_dir / "uninstall.bat", 'wb') as f:
        f.write(uninstall_script.replace('\n', '\r\n').encode('utf-8'))

def build_exe(build_dir, args):
    """使用PyInstaller构建可执行文件"""
    logger.info("开始构建可执行文件...")

    # 准备PyInstaller命令
    pyinstaller_cmd = [
        'uv', 'run', 'pyinstaller',
        '--noconfirm',
        '--clean',
        '--name', args.name,
        '--onefile',  # 单文件模式
        '--add-data', f"{build_dir / 'default_config.json'};.",
        '--hidden-import', 'wmi',
        '--hidden-import', 'win32com.client',
        '--hidden-import', 'win32service',
        '--hidden-import', 'win32serviceutil',
        '--hidden-import', 'win32event',
        '--hidden-import', 'servicemanager',
        '--hidden-import', 'terminal_pb',
        '--hidden-import', 'terminal_pb.terminal_pb2',
        '--hidden-import', 'terminal_pb.terminal_pb2_grpc',
        '--hidden-import', 'registry_manager',  # 添加注册表管理器
    ]

    # 将terminal_pb目录作为模块添加到打包内容中
    os.makedirs(build_dir / 'terminal_pb', exist_ok=True)

    # 添加图标(如果有)
    if args.icon and os.path.exists(args.icon):
        pyinstaller_cmd.extend(['--icon', args.icon])

    # 添加主脚本
    pyinstaller_cmd.append(str(build_dir / 'windows_service.py'))

    # 运行PyInstaller
    try:
        subprocess.run(pyinstaller_cmd, check=True, cwd=str(build_dir))
        logger.info("可执行文件构建成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"构建可执行文件失败: {e}")
        return False

def create_final_package(build_dir, dist_dir, args):
    """创建最终安装包"""
    logger.info("正在创建最终安装包...")

    # 创建输出目录
    output_dir = Path(args.output)
    os.makedirs(output_dir, exist_ok=True)

    # 创建最终安装包目录
    package_name = f"{args.name}-{args.version}"
    package_dir = output_dir / package_name
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)

    # 复制可执行文件
    shutil.copy2(dist_dir / f"{args.name}.exe", package_dir / f"{args.name}.exe")

    # 复制安装和卸载脚本
    shutil.copy2(build_dir / "install.bat", package_dir / "install.bat")
    shutil.copy2(build_dir / "uninstall.bat", package_dir / "uninstall.bat")

    # 复制默认配置
    shutil.copy2(build_dir / "default_config.json", package_dir / "default_config.json")

    # 复制证书文件(如果有)
    if args.cert and os.path.exists(args.cert):
        cert_filename = os.path.basename(args.cert)
        shutil.copy2(args.cert, package_dir / cert_filename)

    # 创建自述文件
    readme_content = f"""# 终端管理Agent

版本: {args.version}
服务器地址: {args.server}

## 安装说明

1. 右键点击 install.bat 脚本，选择"以管理员身份运行"
2. 系统将自动安装Agent并注册为Windows服务
3. 安装完成后，服务将自动启动

## 配置文件

配置文件位置: %ProgramData%\\TerminalAgent\\agent_config.json

## 日志文件

日志文件位置: %ProgramData%\\TerminalAgent\\logs\\

## 卸载说明

1. 右键点击 uninstall.bat 脚本，选择"以管理员身份运行"
2. 系统将自动停止服务并卸载Agent
"""

    with open(package_dir / "README.txt", 'w') as f:
        f.write(readme_content)

    # 创建ZIP压缩包
    zip_filename = f"{package_name}.zip"
    shutil.make_archive(output_dir / package_name, 'zip', output_dir, package_name)
    logger.info(f"安装包已创建: {output_dir / zip_filename}")

    return output_dir / zip_filename

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 检查依赖
    if not check_dependencies():
        return 1

    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # 准备文件
            build_dir = prepare_files(temp_dir, args)

            # 构建可执行文件
            if not build_exe(build_dir, args):
                return 1

            # 创建最终安装包
            dist_dir = build_dir / "dist"
            package_path = create_final_package(build_dir, dist_dir, args)

            logger.info(f"构建成功! 安装包位置: {package_path}")
            return 0

        except Exception as e:
            logger.error(f"构建过程中发生错误: {e}")
            return 1

if __name__ == "__main__":
    sys.exit(main())