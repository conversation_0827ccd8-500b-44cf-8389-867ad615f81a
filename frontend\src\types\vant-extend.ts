import type { UploaderFileListItem } from 'vant'

// 扩展Vant文件上传组件类型，支持自定义状态
export interface ExtendedFileListItem extends Omit<UploaderFileListItem, 'status'> {
  // 扩展状态支持，包含ready状态
  status?: '' | 'uploading' | 'done' | 'failed' | 'ready'
  // 扩展属性
  name?: string
  size?: number
  selected?: boolean
}

// Tag组件类型定义（与Vant官方保持一致）
export type TagType = 'primary' | 'success' | 'danger' | 'warning' | 'default'
export type TagSize = 'large' | 'medium'  // 注意：Vant只支持large和medium两种尺寸 