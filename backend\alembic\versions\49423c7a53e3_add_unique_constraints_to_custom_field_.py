"""add_unique_constraints_to_custom_field_values

Revision ID: 49423c7a53e3
Revises: 459dacb946da
Create Date: 2025-07-01 01:18:39.625304

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '49423c7a53e3'
down_revision: Union[str, None] = '459dacb946da'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 为资产自定义字段值表添加唯一约束
    op.create_unique_constraint(
        'uk_asset_custom_field', 
        'asset_custom_field_values', 
        ['asset_id', 'custom_field_id']
    )
    
    # 为盘点记录自定义字段值表创建部分唯一索引
    # 对于有inventory_record_id的记录
    op.execute("""
        CREATE UNIQUE INDEX uk_inventory_custom_field_record 
        ON inventory_record_custom_field_values (inventory_record_id, custom_field_id) 
        WHERE inventory_record_id IS NOT NULL
    """)
    
    # 对于虚拟记录（task_id + asset_id组合）
    op.execute("""
        CREATE UNIQUE INDEX uk_inventory_custom_field_virtual 
        ON inventory_record_custom_field_values (task_id, asset_id, custom_field_id) 
        WHERE inventory_record_id IS NULL AND task_id IS NOT NULL AND asset_id IS NOT NULL
    """)


def downgrade() -> None:
    # 删除索引和约束
    op.execute("DROP INDEX IF EXISTS uk_inventory_custom_field_virtual")
    op.execute("DROP INDEX IF EXISTS uk_inventory_custom_field_record")
    op.drop_constraint('uk_asset_custom_field', 'asset_custom_field_values', type_='unique')
