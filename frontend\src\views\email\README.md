# 邮箱管理模块

基于腾讯企业邮箱API的前端管理界面，提供完整的企业邮箱管理功能。

## 功能概述

### 1. 管理首页 (`/email`)
- 功能导航卡片
- 统计数据展示
- 快速操作入口
- **权限控制**: 各功能模块卡片和快速操作按钮都有对应的权限控制

### 2. 邮箱配置 (`/email/config`)
- 企业邮箱API配置管理
- 支持多个配置项
- 连接测试功能
- 配置的启用/禁用
- **权限控制**: 新增、编辑、测试连接功能均有权限控制

### 3. 部门管理 (`/email/departments`)
- 树形结构展示部门层级
- 部门信息的增删改查
- 与腾讯企业邮箱API同步
- 支持拖拽排序
- **权限控制**: 完整的CRUD和同步权限控制

### 4. 成员管理 (`/email/members`)
- 成员信息的完整管理
- 按部门筛选
- 批量操作支持
- 成员详情查看
- 登录权限管理
- **权限控制**: 完整的CRUD、同步、导出、权限管理等功能权限控制

### 5. 群组管理 (`/email/groups`)
- 邮件群组管理
- 群组成员管理
- 群组描述编辑
- 群组状态控制
- **权限控制**: 完整的CRUD和同步权限控制

### 6. 标签管理 (`/email/tags`)
- 标签的创建和管理
- 标签状态控制
- 快速搜索功能
- **权限控制**: 完整的CRUD和同步权限控制

### 7. 同步管理 (`/email/sync`)
- 统一的数据同步界面
- 分模块同步功能
- 全量同步支持
- 同步日志查看
- 自动同步配置
- **权限控制**: 各同步功能和配置功能均有权限控制

## 技术特性

### 前端技术栈
- **Vue 3** + **TypeScript** - 现代化前端框架
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理

### 设计特点
- **响应式设计** - 适配不同屏幕尺寸
- **权限控制** - 基于角色的访问控制，全模块覆盖
- **统一样式** - 与系统其他模块保持一致
- **用户体验** - 直观的操作界面和反馈

### 代码特性
- **TypeScript** - 类型安全
- **组合式API** - Vue 3最佳实践
- **模块化** - 清晰的代码结构
- **可维护性** - 易于扩展和维护

## 权限配置

模块使用以下权限标识：

```typescript
// 基础权限
'email:view'                 // 邮箱管理查看权限

// 配置管理
'email:config:view'          // 配置查看
'email:config:create'        // 配置创建
'email:config:update'        // 配置更新
'email:config:delete'        // 配置删除

// 部门管理
'email:department:view'      // 部门查看
'email:department:create'    // 部门创建
'email:department:update'    // 部门更新
'email:department:delete'    // 部门删除
'email:department:sync'      // 部门同步

// 成员管理
'email:member:view'          // 成员查看
'email:member:create'        // 成员创建
'email:member:update'        // 成员更新
'email:member:delete'        // 成员删除
'email:member:sync'          // 成员同步
'email:member:export'        // 成员导出
'email:member:permissions'   // 成员权限管理

// 群组管理
'email:group:view'           // 群组查看
'email:group:create'         // 群组创建
'email:group:update'         // 群组更新
'email:group:delete'         // 群组删除
'email:group:sync'           // 群组同步

// 标签管理
'email:tag:view'             // 标签查看
'email:tag:create'           // 标签创建
'email:tag:update'           // 标签更新
'email:tag:delete'           // 标签删除
'email:tag:sync'             // 标签同步

// 同步管理
'email:sync:view'            // 同步查看
'email:sync:all'             // 全量同步
'email:sync:config'          // 同步配置
```

## 权限控制实现

### 路由级权限
每个页面路由都配置了对应的权限要求：

```typescript
{
  path: 'email/config',
  meta: {
    permissions: ['email:config:view']
  }
}
```

### 组件级权限
页面内的功能按钮都使用 `Authority` 组件进行权限控制：

```vue
<Authority permission="email:config:create">
  <el-button @click="handleCreate">新增配置</el-button>
</Authority>
```

### 特性
- **完整覆盖**: 所有功能操作都有对应的权限控制
- **精细粒度**: 支持到具体操作的权限控制
- **用户体验**: 无权限时功能按钮不显示，避免用户困惑
- **统一标准**: 权限码命名遵循统一规范

## 安装和使用

1. 确保后端权限数据已正确初始化
2. 为用户分配对应的邮箱管理相关权限
3. 登录系统即可访问有权限的邮箱管理功能

## 开发指南

### 添加新功能权限控制

1. 在后端 `create_default_permissions.py` 中添加权限定义
2. 在前端组件中使用 `Authority` 组件包装功能按钮
3. 在路由配置中添加页面权限要求
4. 更新本文档的权限列表

### 权限命名规范

权限码格式：`模块:资源:操作`
- 模块：`email`
- 资源：`config`、`department`、`member`、`group`、`tag`、`sync`
- 操作：`view`、`create`、`update`、`delete`、`sync`、`export` 等

## 注意事项

1. 权限控制基于后端权限系统，确保前后端权限码一致
2. 新增权限需要重新初始化权限数据或手动添加
3. 超级管理员默认拥有所有权限
4. 权限控制仅影响前端界面显示，后端API仍需独立进行权限验证

## API接口

### 配置管理
- `GET /api/v1/email/configs` - 获取配置列表
- `POST /api/v1/email/configs` - 创建配置
- `PUT /api/v1/email/configs/{id}` - 更新配置
- `DELETE /api/v1/email/configs/{id}` - 删除配置
- `POST /api/v1/email/configs/{id}/test` - 测试连接

### 部门管理
- `GET /api/v1/email/departments` - 获取部门列表
- `GET /api/v1/email/departments/tree` - 获取部门树
- `POST /api/v1/email/departments` - 创建部门
- `PUT /api/v1/email/departments/{id}` - 更新部门
- `DELETE /api/v1/email/departments/{id}` - 删除部门
- `POST /api/v1/email/sync/departments` - 同步部门

### 成员管理
- `GET /api/v1/email/members` - 获取成员列表
- `POST /api/v1/email/members` - 创建成员
- `PUT /api/v1/email/members/{id}` - 更新成员
- `DELETE /api/v1/email/members/{id}` - 删除成员
- `POST /api/v1/email/sync/members` - 同步成员

### 群组管理
- `GET /api/v1/email/groups` - 获取群组列表
- `POST /api/v1/email/groups` - 创建群组
- `PUT /api/v1/email/groups/{id}` - 更新群组
- `DELETE /api/v1/email/groups/{id}` - 删除群组
- `POST /api/v1/email/sync/groups` - 同步群组

### 标签管理
- `GET /api/v1/email/tags` - 获取标签列表
- `POST /api/v1/email/tags` - 创建标签
- `PUT /api/v1/email/tags/{id}` - 更新标签
- `DELETE /api/v1/email/tags/{id}` - 删除标签
- `POST /api/v1/email/sync/tags` - 同步标签

## 使用说明

### 1. 初始配置
1. 进入"邮箱配置"页面
2. 添加腾讯企业邮箱的API配置
3. 测试连接确保配置正确
4. 启用配置

### 2. 数据同步
1. 进入"同步管理"页面
2. 选择需要同步的模块
3. 执行同步操作
4. 查看同步日志

### 3. 日常管理
- 使用各个管理页面进行日常的增删改查操作
- 定期执行数据同步保持数据一致性
- 根据需要配置自动同步策略

## 文件结构

```
frontend/src/views/email/
├── index.vue                 # 管理首页
├── EmailConfig.vue          # 邮箱配置
├── DepartmentManagement.vue # 部门管理
├── MemberManagement.vue     # 成员管理
├── GroupManagement.vue      # 群组管理
├── TagManagement.vue        # 标签管理
├── SyncManagement.vue       # 同步管理
└── README.md               # 说明文档

frontend/src/api/
└── email.ts                # API接口定义

frontend/src/utils/
└── date.ts                 # 日期工具函数
```

## 注意事项

1. **权限控制**: 确保用户具有相应的权限才能访问对应功能
2. **数据同步**: 建议定期执行数据同步，保持与腾讯企业邮箱的数据一致性
3. **错误处理**: 所有操作都包含错误处理和用户友好的提示信息
4. **性能优化**: 大量数据时使用分页加载，避免性能问题

## 扩展说明

如需扩展功能，可以：
1. 在对应的Vue组件中添加新的功能
2. 在API文件中添加新的接口调用
3. 在路由配置中添加新的页面路由
4. 在菜单配置中添加新的菜单项 