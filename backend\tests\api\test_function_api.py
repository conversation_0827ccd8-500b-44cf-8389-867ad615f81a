#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试腾讯企业邮箱 功能设置API接口
"""

import asyncio
import httpx
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService

async def test_function_api():
    """测试功能设置API接口"""
    print("=== 测试腾讯企业邮箱 功能设置API接口 ===\n")
    
    db = SessionLocal()
    
    try:
        # 1. 初始化API服务
        print("1. 初始化API服务...")
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        print(f"✅ 成功创建API服务")
        print(f"   应用名称: {api_service.app_name}")
        print(f"   基础URL: {api_service.base_url}")
        
        # 2. 获取访问令牌
        print("\n2. 获取访问令牌...")
        try:
            access_token = await api_service.get_access_token()
            print(f"✅ 成功获取访问令牌: {access_token[:20]}...")
        except Exception as e:
            print(f"❌ 获取访问令牌失败: {str(e)}")
            return
        
        # 3. 测试 service/get_function 接口
        print("\n3. 测试 service/get_function 接口...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 测试获取不同类型的功能属性
            function_types = ["1", "2", "3", "4"]  # 对应不同的功能类型
            
            for func_type in function_types:
                get_url = f"{api_service.base_url}/service/get_function?access_token={access_token}"
                get_data = {"type": func_type}
                
                print(f"   测试类型 {func_type}:")
                print(f"   请求URL: {get_url}")
                print(f"   请求参数: {json.dumps(get_data, ensure_ascii=False)}")
                
                get_response = await client.post(get_url, json=get_data)
                print(f"   响应状态码: {get_response.status_code}")
                print(f"   响应内容: {get_response.text}")
                
                if get_response.status_code == 200:
                    get_result = get_response.json()
                    errcode = get_result.get("errcode")
                    if errcode == 0:
                        print(f"   ✅ 类型 {func_type} 获取成功!")
                    else:
                        print(f"   ❌ 类型 {func_type} 错误: {errcode} - {get_result.get('errmsg')}")
                else:
                    print(f"   ❌ HTTP错误: {get_response.status_code}")
                print()
        
        # 4. 测试 service/set_function 接口
        print("\n4. 测试 service/set_function 接口...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 尝试设置IMAP/SMTP服务
            set_url = f"{api_service.base_url}/service/set_function?access_token={access_token}"
            set_data = {
                "type": "2",  # IMAP/SMTP服务
                "value": "1"  # 开启
            }
            
            print(f"   请求URL: {set_url}")
            print(f"   请求参数: {json.dumps(set_data, ensure_ascii=False)}")
            
            set_response = await client.post(set_url, json=set_data)
            print(f"   响应状态码: {set_response.status_code}")
            print(f"   响应内容: {set_response.text}")
            
            if set_response.status_code == 200:
                set_result = set_response.json()
                set_errcode = set_result.get("errcode")
                if set_errcode == 0:
                    print("   ✅ service/set_function 接口调用成功!")
                else:
                    print(f"   ❌ service/set_function 错误: {set_errcode} - {set_result.get('errmsg')}")
            else:
                print(f"   ❌ HTTP错误: {set_response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    finally:
        db.close()

async def test_user_specific_settings():
    """测试用户特定的设置方式"""
    print("\n=== 测试用户特定设置方式 ===\n")
    
    db = SessionLocal()
    
    try:
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        access_token = await api_service.get_access_token()
        
        # 测试通过user/update接口设置用户属性
        print("测试 user/update 接口...")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            update_url = f"{api_service.base_url}/user/update?access_token={access_token}"
            
            # 尝试通过extattr设置用户扩展属性
            update_data = {
                "userid": "<EMAIL>",
                "extattr": [
                    {"name": "imap_smtp_enabled", "value": "1"},
                    {"name": "pop_smtp_enabled", "value": "1"}
                ]
            }
            
            print(f"   请求URL: {update_url}")
            print(f"   请求参数: {json.dumps(update_data, ensure_ascii=False)}")
            
            update_response = await client.post(update_url, json=update_data)
            print(f"   响应状态码: {update_response.status_code}")
            print(f"   响应内容: {update_response.text}")
            
            if update_response.status_code == 200:
                result = update_response.json()
                errcode = result.get("errcode")
                if errcode == 0:
                    print("   ✅ user/update 接口调用成功!")
                elif errcode == 602005:
                    print("   ❌ 权限不足，无法通过此接口设置用户属性")
                else:
                    print(f"   ❌ 错误: {errcode} - {result.get('errmsg')}")
                    
    except Exception as e:
        print(f"❌ 用户设置测试失败: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    print("开始测试腾讯企业邮箱 功能设置API接口...\n")
    asyncio.run(test_function_api())
    asyncio.run(test_user_specific_settings())
    print("\n=== 测试完成 ===") 