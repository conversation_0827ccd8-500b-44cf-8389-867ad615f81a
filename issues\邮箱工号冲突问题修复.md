# 邮箱工号冲突问题修复

## 问题描述

**错误现象：** 工号补全过程中出现 `UNIQUE constraint failed: email_members.extid` 错误

**错误位置：** `backend/app/services/email_extid_completion.py` 的 `_update_member_extid` 方法

**具体日志：**
```
[0] 2025-06-06 00:22:33,317 - app.services.email_extid_completion - ERROR - 更新成员工号时出错: 
(sqlite3.IntegrityError) UNIQUE constraint failed: email_members.extid
[0] [SQL: UPDATE email_members SET extid=?, updated_at=CURRENT_TIMESTAMP WHERE email_members.id = ?]
[0] [parameters: ('212400419', 990)]
```

## 问题根因分析

1. **数据库约束：** `email_members.extid` 字段设有 `UNIQUE` 约束
2. **业务逻辑缺陷：** `_update_member_extid` 方法缺少工号冲突检查
3. **时序问题：** 腾讯API更新成功，但本地数据库更新因唯一约束失败
4. **工号冲突：** 工号 `212400419` 已被其他成员使用，但系统尝试重复分配

## 修复方案

**策略：** 在更新前添加工号冲突检查逻辑

**修改内容：**
1. 在 `_update_member_extid` 方法中添加更新前冲突检查
2. 在腾讯API更新成功后，再次检查冲突（防止并发问题）
3. 增强错误日志，提供详细的冲突信息

## 代码修改

### 文件：`backend/app/services/email_extid_completion.py`

**修改位置：** `_update_member_extid` 方法（约第568-618行）

**核心改动：**
```python
# 检查工号是否已被其他成员使用（如果工号不为空）
if job_number and job_number.strip():
    existing_member = self.db.query(EmailMember).filter(
        EmailMember.extid == job_number.strip(),
        EmailMember.id != member_id
    ).first()
    
    if existing_member:
        logger.error(f"工号 {job_number} 已被其他成员使用: ID={existing_member.id}, "
                   f"邮箱={existing_member.email}, 姓名={existing_member.name}")
        return False
```

## 修复效果

1. **避免数据库约束错误：** 提前检查并阻止冲突的工号分配
2. **保证数据一致性：** 防止腾讯API与本地数据库不一致
3. **提供详细错误信息：** 明确指出冲突的具体成员信息
4. **处理并发场景：** 双重检查机制防止并发冲突

## 相关技术要点

- **数据库约束：** SQLite 的 UNIQUE 约束处理
- **事务管理：** 数据库回滚机制
- **并发控制：** 双重检查模式应用
- **API同步：** 第三方API与本地数据一致性

## 完成时间

2025-01-06 