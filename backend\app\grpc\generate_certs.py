#!/usr/bin/env python
"""
证书生成工具

用于为终端管理模块的gRPC服务器和客户端生成TLS证书。
支持生成自签名证书和客户端证书。
"""

import os
import argparse
import logging
import subprocess
from pathlib import Path
import shutil
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


def check_openssl():
    """检查OpenSSL是否可用"""
    try:
        result = subprocess.run(['openssl', 'version'], 
                              capture_output=True, text=True, check=True)
        logger.info(f"OpenSSL版本: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        logger.error("调用OpenSSL失败")
        return False
    except FileNotFoundError:
        logger.error("未找到OpenSSL，请先安装OpenSSL")
        return False


def generate_ca_cert(cert_dir):
    """生成CA根证书"""
    ca_key = cert_dir / "ca.key"
    ca_crt = cert_dir / "ca.crt"
    
    logger.info("生成CA根证书...")
    
    # 生成CA私钥
    subprocess.run([
        'openssl', 'genrsa',
        '-out', str(ca_key),
        '2048'
    ], check=True)
    
    # 生成CA自签名证书
    subprocess.run([
        'openssl', 'req', '-new', '-x509',
        '-key', str(ca_key),
        '-out', str(ca_crt),
        '-days', '3650',
        '-subj', '/C=CN/ST=Shanghai/L=Shanghai/O=OPS-Platform/OU=CA/CN=Terminal Management CA'
    ], check=True)
    
    logger.info(f"CA根证书生成完成: {ca_crt}")
    return ca_key, ca_crt


def generate_server_cert(cert_dir, ca_key, ca_crt):
    """生成服务器证书"""
    server_key = cert_dir / "server.key"
    server_csr = cert_dir / "server.csr"
    server_crt = cert_dir / "server.crt"
    server_cnf = cert_dir / "server.cnf"
    
    logger.info("生成服务器证书...")
    
    # 创建服务器配置文件，添加SAN扩展
    with open(server_cnf, 'w') as f:
        f.write("""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Shanghai
L = Shanghai
O = OPS-Platform
OU = Server
CN = TerminalManagementServer

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = TerminalManagementServer
IP.1 = 127.0.0.1
""")
    
    # 生成服务器私钥
    subprocess.run([
        'openssl', 'genrsa',
        '-out', str(server_key),
        '2048'
    ], check=True)
    
    # 生成服务器CSR
    subprocess.run([
        'openssl', 'req', '-new',
        '-key', str(server_key),
        '-out', str(server_csr),
        '-config', str(server_cnf)
    ], check=True)
    
    # 使用CA签名服务器证书
    subprocess.run([
        'openssl', 'x509', '-req',
        '-in', str(server_csr),
        '-CA', str(ca_crt),
        '-CAkey', str(ca_key),
        '-CAcreateserial',
        '-out', str(server_crt),
        '-days', '365',
        '-extensions', 'v3_req',
        '-extfile', str(server_cnf)
    ], check=True)
    
    # 删除CSR文件
    os.remove(server_csr)
    
    logger.info(f"服务器证书生成完成: {server_crt}")
    return server_key, server_crt


def generate_client_cert(cert_dir, ca_key, ca_crt, client_name="client"):
    """生成客户端证书"""
    client_key = cert_dir / f"{client_name}.key"
    client_csr = cert_dir / f"{client_name}.csr"
    client_crt = cert_dir / f"{client_name}.crt"
    client_cnf = cert_dir / f"{client_name}.cnf"
    
    logger.info(f"生成客户端证书: {client_name}...")
    
    # 创建客户端配置文件
    with open(client_cnf, 'w') as f:
        f.write(f"""[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Shanghai
L = Shanghai
O = OPS-Platform
OU = Client
CN = {client_name}

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = clientAuth
""")
    
    # 生成客户端私钥
    subprocess.run([
        'openssl', 'genrsa',
        '-out', str(client_key),
        '2048'
    ], check=True)
    
    # 生成客户端CSR
    subprocess.run([
        'openssl', 'req', '-new',
        '-key', str(client_key),
        '-out', str(client_csr),
        '-config', str(client_cnf)
    ], check=True)
    
    # 使用CA签名客户端证书
    subprocess.run([
        'openssl', 'x509', '-req',
        '-in', str(client_csr),
        '-CA', str(ca_crt),
        '-CAkey', str(ca_key),
        '-CAcreateserial',
        '-out', str(client_crt),
        '-days', '365',
        '-extensions', 'v3_req',
        '-extfile', str(client_cnf)
    ], check=True)
    
    # 删除CSR文件
    os.remove(client_csr)
    
    logger.info(f"客户端证书生成完成: {client_crt}")
    return client_key, client_crt


def create_client_package(cert_dir, output_dir, client_name):
    """创建客户端证书包"""
    client_key = cert_dir / f"{client_name}.key"
    client_crt = cert_dir / f"{client_name}.crt"
    ca_crt = cert_dir / "ca.crt"
    
    package_dir = output_dir / f"{client_name}_certs"
    os.makedirs(package_dir, exist_ok=True)
    
    # 复制证书文件
    shutil.copy2(client_key, package_dir / f"{client_name}.key")
    shutil.copy2(client_crt, package_dir / f"{client_name}.crt")
    shutil.copy2(ca_crt, package_dir / "ca.crt")
    
    # 添加说明文件
    with open(package_dir / "README.txt", 'w') as f:
        f.write(f"""终端管理 - 客户端证书包 ({client_name})

此证书包包含以下文件:
- {client_name}.key: 客户端私钥文件
- {client_name}.crt: 客户端证书文件
- ca.crt: CA根证书

使用说明:
1. 将这些文件复制到终端Agent所在的机器上
2. 在Agent配置中启用TLS并指定证书文件路径
3. 重启Agent使证书生效

注意:
- 保持私钥文件的安全，不要泄露
- 证书有效期为1年，到期后需要更新
""")
    
    # 创建ZIP包
    zip_file = output_dir / f"{client_name}_certs.zip"
    if os.path.exists(zip_file):
        os.remove(zip_file)
        
    shutil.make_archive(str(output_dir / f"{client_name}_certs"), 'zip', 
                       str(output_dir), f"{client_name}_certs")
    
    # 删除临时目录
    shutil.rmtree(package_dir)
    
    logger.info(f"客户端证书包已创建: {zip_file}")
    return zip_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="终端管理TLS证书生成工具")
    parser.add_argument('--output', '-o', type=str, default='certs',
                      help='证书输出目录')
    parser.add_argument('--client', '-c', type=str, default='agent',
                      help='客户端证书名称')
    parser.add_argument('--force', '-f', action='store_true',
                      help='强制重新生成已存在的证书')
    args = parser.parse_args()
    
    # 检查OpenSSL
    if not check_openssl():
        return 1
    
    # 准备目录
    cert_dir = Path(args.output)
    os.makedirs(cert_dir, exist_ok=True)
    
    # 检查现有证书
    ca_exists = (cert_dir / "ca.crt").exists() and (cert_dir / "ca.key").exists()
    server_exists = (cert_dir / "server.crt").exists() and (cert_dir / "server.key").exists()
    client_exists = (cert_dir / f"{args.client}.crt").exists() and (cert_dir / f"{args.client}.key").exists()
    
    if ca_exists and server_exists and client_exists and not args.force:
        logger.info("所有证书已存在，如需重新生成请使用 --force 参数")
        return 0
    
    try:
        # 生成CA证书
        if not ca_exists or args.force:
            ca_key, ca_crt = generate_ca_cert(cert_dir)
        else:
            ca_key, ca_crt = cert_dir / "ca.key", cert_dir / "ca.crt"
            logger.info("使用现有CA证书")
        
        # 生成服务器证书
        if not server_exists or args.force:
            server_key, server_crt = generate_server_cert(cert_dir, ca_key, ca_crt)
        else:
            server_key, server_crt = cert_dir / "server.key", cert_dir / "server.crt"
            logger.info("使用现有服务器证书")
        
        # 生成客户端证书
        if not client_exists or args.force:
            client_key, client_crt = generate_client_cert(cert_dir, ca_key, ca_crt, args.client)
        else:
            client_key, client_crt = cert_dir / f"{args.client}.key", cert_dir / f"{args.client}.crt"
            logger.info("使用现有客户端证书")
        
        # 创建客户端证书包
        output_dir = Path(args.output) / "packages"
        os.makedirs(output_dir, exist_ok=True)
        client_package = create_client_package(cert_dir, output_dir, args.client)
        
        logger.info("证书生成完成！")
        logger.info(f"证书目录: {cert_dir}")
        logger.info(f"客户端证书包: {client_package}")
        
        return 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"执行OpenSSL命令时发生错误: {e}")
        return 1
    except Exception as e:
        logger.error(f"生成证书时发生错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 