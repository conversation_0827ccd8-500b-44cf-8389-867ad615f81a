# WebSocket 终端状态 404 错误修复

## 问题描述

终端列表页面出现 WebSocket 连接错误：
```
INFO: 127.0.0.1:8606 - "GET /ws/terminal-status HTTP/1.1" 404 Not Found
```

用户反馈在访问终端列表页面时，WebSocket 实时状态更新功能无法正常工作。

## 问题分析

通过代码检查发现问题的根本原因：

### 1. 缺少 WebSocket 支持库
- **错误信息**：`No supported WebSocket library detected`
- **原因**：`uvicorn==0.27.0` 缺少 WebSocket 支持库
- **解决方案**：升级为 `uvicorn[standard]==0.27.0`

### 2. Redis 缓存中间件干扰
- **问题**：Redis 缓存中间件拦截了 WebSocket 路径请求
- **原因**：中间件的 `exclude_paths` 列表未包含 WebSocket 路径
- **解决方案**：在排除列表中添加 `/ws/` 前缀

## 修复方案

### 步骤 1：安装 WebSocket 支持库

**文件**：`backend/pyproject.toml`

```diff
dependencies = [
    "fastapi==0.113.0",
-   "uvicorn==0.27.0",
+   "uvicorn[standard]==0.27.0",
    ...
]
```

### 步骤 2：排除 WebSocket 路径

**文件**：`backend/app/middleware/redis_cache_logging.py`

```diff
# 不缓存的路径列表
self.exclude_paths = [
    "/api/v1/auth/login",
    "/api/v1/system/monitoring",
    ...
    "/api/v1/email-personnel-sync/extid-completion/candidates",
+   "/ws/",  # 排除所有 WebSocket 路径，避免中间件干扰 WebSocket 协议升级
]
```

### 步骤 3：同步依赖

```bash
cd backend
uv sync
```

## 验证结果

修复后的成功日志：
```
INFO: ('127.0.0.1', 9305) - "WebSocket /ws/terminal-status" [accepted]
INFO: WebSocket连接已建立，当前连接数: 1
INFO: connection open
```

## 技术要点

### WebSocket vs HTTP 协议
- **WebSocket 升级**：需要 HTTP 协议升级 (Upgrade: websocket)
- **中间件影响**：HTTP 中间件可能干扰 WebSocket 协议升级
- **Uvicorn 支持**：标准版本包含 `websockets` 库支持

### 中间件设计模式
- **路径排除**：关键基础设施路径应排除在缓存之外
- **协议识别**：区分 HTTP 和 WebSocket 请求的处理方式
- **性能考虑**：实时通信路径不应被缓存

## 相关功能

此修复影响以下功能：
- ✅ 终端在线状态实时更新
- ✅ 终端列表 WebSocket 连接状态指示器
- ✅ 终端状态变化的实时推送

## 测试建议

1. **功能测试**：访问终端列表页面，确认状态指示器显示"实时连接"
2. **状态推送**：模拟终端上线/下线，验证状态实时更新
3. **连接稳定性**：长时间保持页面打开，确认连接稳定

## 防范措施

1. **依赖管理**：使用 `uvicorn[standard]` 确保完整功能支持
2. **中间件审查**：新增中间件时考虑对 WebSocket 的影响
3. **路径管理**：维护中间件排除路径的完整性

## 相关文档

- [终端在线状态实时更新WebSocket实现.md](./终端在线状态实时更新WebSocket实现.md)
- [backend/app/api/v1/websocket.py](../backend/app/api/v1/websocket.py)
- [frontend/src/utils/websocket.ts](../frontend/src/utils/websocket.ts) 