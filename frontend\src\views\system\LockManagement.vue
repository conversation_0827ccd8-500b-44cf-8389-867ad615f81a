<template>
  <div class="lock-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Lock /></el-icon>
        <h2 class="page-title">同步锁管理</h2>
      </div>
      <div class="header-actions">
        <el-button 
          :icon="Refresh" 
          @click="refreshData"
          :loading="loading"
          circle
          title="刷新数据"
        />
        <el-switch
          v-model="autoRefresh"
          @change="toggleAutoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
        />
      </div>
    </div>

    <!-- 状态概览卡片 -->
    <div class="status-cards">
      <el-card class="status-card">
        <div class="card-content">
          <div class="card-icon total">
            <el-icon><DataBoard /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-value">{{ globalStatus?.summary.total_locks || 0 }}</div>
            <div class="card-label">总锁数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="status-card">
        <div class="card-content">
          <div class="card-icon active">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-value">{{ globalStatus?.summary.total_active || 0 }}</div>
            <div class="card-label">活跃锁</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="status-card">
        <div class="card-content">
          <div class="card-icon expired">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-value">{{ globalStatus?.summary.total_expired || 0 }}</div>
            <div class="card-label">过期锁</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="status-card">
        <div class="card-content">
          <div class="card-icon service">
            <el-icon><Cpu /></el-icon>
          </div>
          <div class="card-info">
            <div class="card-value">
              <el-tag :type="globalStatus?.service_running ? 'success' : 'danger'" size="small">
                {{ globalStatus?.service_running ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            <div class="card-label">清理服务</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-bar">
      <el-button 
        type="primary" 
        :icon="Delete" 
        @click="cleanupExpiredLocks"
        :loading="cleanupLoading"
        :disabled="!hasExpiredLocks"
      >
        清理过期锁
      </el-button>
      <el-button 
        type="danger" 
        :icon="Close" 
        @click="confirmForceCleanup"
        :loading="forceCleanupLoading"
        plain
      >
        强制清理所有锁
      </el-button>
      <div class="last-update">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>

    <!-- AD同步锁表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><Connection /></el-icon>
            AD同步锁 ({{ globalStatus?.ad_locks.total || 0 }})
          </span>
          <el-tag v-if="globalStatus?.ad_locks.expired" type="danger" size="small">
            {{ globalStatus.ad_locks.expired }} 个过期
          </el-tag>
        </div>
      </template>
      
      <el-table 
        :data="globalStatus?.ad_locks.locks || []" 
        stripe
        :loading="loading"
        empty-text="暂无AD同步锁"
      >
        <el-table-column prop="lock_name" label="锁名称" min-width="180">
          <template #default="{ row }">
            <el-text class="lock-name">{{ row.lock_name }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="lockUtils.getLockStatusDisplay(row).type"
              size="small"
            >
              {{ lockUtils.getLockStatusDisplay(row).text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="locked_by" label="锁定者" width="120">
          <template #default="{ row }">
            <el-text>{{ row.locked_by || '-' }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="锁定时间" width="180">
          <template #default="{ row }">
            <el-text>{{ lockUtils.formatTime(row.locked_at) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="剩余时间" width="120">
          <template #default="{ row }">
            <el-text 
              :type="row.is_expired ? 'danger' : 'primary'"
              :class="{ 'expired-time': row.is_expired }"
            >
              {{ lockUtils.formatTimeRemaining(row.time_remaining) }}
            </el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="超时设置" width="100">
          <template #default="{ row }">
            <el-text>{{ Math.floor(row.timeout_seconds / 60) }}分钟</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.is_locked"
              type="danger"
              size="small"
              :icon="Close"
              @click="confirmForceRelease('ad', row.lock_name)"
              plain
            >
              强制释放
            </el-button>
            <el-text v-else type="info">无需操作</el-text>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 邮箱同步锁表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><Message /></el-icon>
            邮箱同步锁 ({{ globalStatus?.email_locks.total || 0 }})
          </span>
          <el-tag v-if="globalStatus?.email_locks.expired" type="danger" size="small">
            {{ globalStatus.email_locks.expired }} 个过期
          </el-tag>
        </div>
      </template>
      
      <el-table 
        :data="globalStatus?.email_locks.locks || []" 
        stripe
        :loading="loading"
        empty-text="暂无邮箱同步锁"
      >
        <el-table-column prop="lock_name" label="锁名称" min-width="180">
          <template #default="{ row }">
            <el-text class="lock-name">{{ row.lock_name }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="lockUtils.getLockStatusDisplay(row).type"
              size="small"
            >
              {{ lockUtils.getLockStatusDisplay(row).text }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作类型" width="120">
          <template #default="{ row }">
            <el-text>{{ lockUtils.getOperationTypeDisplay(row.operation_type) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="locked_by" label="锁定者" width="120">
          <template #default="{ row }">
            <el-text>{{ row.locked_by || '-' }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="锁定时间" width="180">
          <template #default="{ row }">
            <el-text>{{ lockUtils.formatTime(row.locked_at) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="剩余时间" width="120">
          <template #default="{ row }">
            <el-text 
              :type="row.is_expired ? 'danger' : 'primary'"
              :class="{ 'expired-time': row.is_expired }"
            >
              {{ lockUtils.formatTimeRemaining(row.time_remaining) }}
            </el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="超时设置" width="100">
          <template #default="{ row }">
            <el-text>{{ Math.floor(row.timeout_seconds / 60) }}分钟</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.is_locked"
              type="danger"
              size="small"
              :icon="Close"
              @click="confirmForceRelease('email', row.lock_name)"
              plain
            >
              强制释放
            </el-button>
            <el-text v-else type="info">无需操作</el-text>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Lock, Refresh, DataBoard, Warning, CircleClose, Cpu, 
  Delete, Close, Connection, Message 
} from '@element-plus/icons-vue'
import { locksApi, lockUtils, type GlobalLockStatus, type CleanupResult, type ApiResponse } from '@/api/locks'

// 响应式数据
const loading = ref(false)
const cleanupLoading = ref(false)
const forceCleanupLoading = ref(false)
const autoRefresh = ref(true)
const globalStatus = ref<GlobalLockStatus | null>(null)
const lastUpdateTime = ref('')
const refreshTimer = ref<number | null>(null)

// 计算属性
const hasExpiredLocks = computed(() => {
  return (globalStatus.value?.summary.total_expired || 0) > 0
})

// 获取锁状态数据
const fetchLockStatus = async () => {
  try {
    loading.value = true
    console.log('[LockManagement] 开始获取锁状态')

    // 检查token
    const token = localStorage.getItem('token')
    console.log('[LockManagement] 当前token:', token ? `${token.substring(0, 20)}...` : '无token')

    const response = await locksApi.getAllLocksStatus()
    console.log('[LockManagement] API响应:', response)

    // 直接使用返回的数据，后端返回的是GlobalLockStatus而不是包装的ApiResponse
    const globalLockStatus = response.data as GlobalLockStatus
    console.log('[LockManagement] 解析后的数据:', globalLockStatus)

    globalStatus.value = globalLockStatus
    lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
    console.log('[LockManagement] 锁状态更新成功')
  } catch (error: any) {
    console.error('[LockManagement] 获取锁状态失败:', error)
    console.error('[LockManagement] 错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    })
    
    // 更好的错误信息处理
    let errorMessage = '网络错误'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }
    
    ElMessage.error('获取锁状态失败: ' + errorMessage)
  } finally {
    loading.value = false
  }
}

// 手动刷新数据
const refreshData = () => {
  fetchLockStatus()
}

// 切换自动刷新
const toggleAutoRefresh = (enabled: boolean) => {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 启动自动刷新
const startAutoRefresh = () => {
  if (refreshTimer.value) return
  
  refreshTimer.value = window.setInterval(() => {
    fetchLockStatus()
  }, 30000) // 30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 清理过期锁
const cleanupExpiredLocks = async () => {
  try {
    cleanupLoading.value = true
    const response = await locksApi.cleanupExpiredLocks()

    // 直接使用返回的数据
    const result = response.data as CleanupResult
    ElMessage.success(`清理完成，共清理 ${result.total_cleaned} 个过期锁`)
    await fetchLockStatus() // 刷新数据
  } catch (error: any) {
    console.error('清理过期锁失败:', error)
    ElMessage.error('清理失败: ' + (error.message || '网络错误'))
  } finally {
    cleanupLoading.value = false
  }
}

// 确认强制清理所有锁
const confirmForceCleanup = () => {
  ElMessageBox.confirm(
    '此操作将强制释放所有同步锁，可能会影响正在进行的同步任务。确认继续吗？',
    '危险操作确认',
    {
      confirmButtonText: '确认强制清理',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    forceCleanupAllLocks()
  }).catch(() => {
    // 用户取消操作
  })
}

// 强制清理所有锁
const forceCleanupAllLocks = async () => {
  try {
    forceCleanupLoading.value = true
    const response = await locksApi.forceCleanupAllLocks()

    // 直接使用返回的数据
    const result = response.data as CleanupResult
    ElMessage.success(`强制清理完成，共释放 ${result.total_released} 个锁`)
    await fetchLockStatus() // 刷新数据
  } catch (error: any) {
    console.error('强制清理失败:', error)
    ElMessage.error('强制清理失败: ' + (error.message || '网络错误'))
  } finally {
    forceCleanupLoading.value = false
  }
}

// 确认强制释放指定锁
const confirmForceRelease = (lockType: 'ad' | 'email', lockName: string) => {
  ElMessageBox.confirm(
    `确认要强制释放 ${lockType === 'ad' ? 'AD' : '邮箱'}同步锁 "${lockName}" 吗？`,
    '强制释放确认',
    {
      confirmButtonText: '确认释放',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    forceReleaseLock(lockType, lockName)
  }).catch(() => {
    // 用户取消操作
  })
}

// 强制释放指定锁
const forceReleaseLock = async (lockType: 'ad' | 'email', lockName: string) => {
  try {
    const apiCall = lockType === 'ad'
      ? locksApi.forceReleaseAdLock
      : locksApi.forceReleaseEmailLock

    const response = await apiCall(lockName)

    // 处理axios响应格式
    const apiResponse = response.data || response

    if (apiResponse.success) {
      ElMessage.success(`已强制释放${lockType === 'ad' ? 'AD' : '邮箱'}锁: ${lockName}`)
      await fetchLockStatus() // 刷新数据
    } else {
      ElMessage.error('强制释放失败: ' + (apiResponse.message || 'Unknown error'))
    }
  } catch (error: any) {
    console.error('强制释放锁失败:', error)
    ElMessage.error('强制释放失败: ' + (error.message || '网络错误'))
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchLockStatus()
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.lock-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 状态卡片样式 */
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.status-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.expired {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #f56c6c;
}

.card-icon.service {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #409eff;
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: #666;
}

/* 操作栏样式 */
.action-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.last-update {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 表格样式增强 */
.lock-name {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
}

.expired-time {
  font-weight: bold;
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .last-update {
    margin-left: 0;
    text-align: center;
  }
}
</style> 