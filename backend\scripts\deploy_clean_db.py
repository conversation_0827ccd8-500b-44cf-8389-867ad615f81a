#!/usr/bin/env python3
"""
OPS Platform 标准化数据库部署脚本
用于生产环境的一键部署
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine
from sqlalchemy import text
import logging
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_environment():
    """检查部署环境"""
    logger.info("🔍 检查部署环境...")
    
    try:
        # 检查数据库连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            logger.info(f"✅ PostgreSQL连接成功: {version.split(',')[0]}")
        
        # 检查必要的依赖
        try:
            import psycopg2
            logger.info("✅ psycopg2驱动可用")
        except ImportError:
            logger.error("❌ psycopg2驱动缺失，请运行: uv add psycopg2-binary")
            return False
        
        try:
            import alembic
            logger.info("✅ Alembic可用")
        except ImportError:
            logger.error("❌ Alembic缺失，请运行: uv add alembic")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False

def deploy_database(force=False):
    """部署数据库"""
    if not check_environment():
        return False
    
    if not force:
        logger.info("⚠️ 这将完全重建数据库，所有现有数据将丢失！")
        response = input("确定要继续吗？(yes/no): ")
        if response.lower() != 'yes':
            logger.info("部署已取消")
            return False
    
    # 执行重建脚本
    logger.info("🚀 开始执行数据库重建...")
    
    try:
        from scripts.rebuild_database import main as rebuild_main
        result = rebuild_main()
        
        if result == 0:
            logger.info("✅ 数据库部署成功！")
            return True
        else:
            logger.error("❌ 数据库部署失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 部署过程中发生错误: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPS Platform标准化数据库部署")
    parser.add_argument('--force', action='store_true', help='强制部署，不询问确认')
    args = parser.parse_args()
    
    logger.info("🚀 OPS Platform 数据库标准化部署")
    logger.info("=" * 50)
    
    if deploy_database(force=args.force):
        logger.info("🎉 部署完成！")
        logger.info("💡 下一步:")
        logger.info("   1. 启动应用: uv run python run.py")
        logger.info("   2. 访问前端: http://localhost:5173")
        logger.info("   3. 访问API: http://localhost:8000")
        logger.info("   4. 管理员登录: admin / admin123")
        return 0
    else:
        logger.error("❌ 部署失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 