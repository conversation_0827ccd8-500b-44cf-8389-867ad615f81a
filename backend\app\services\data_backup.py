"""
数据备份服务
用于在执行数据修改操作前备份重要数据，并提供恢复功能
"""

import logging
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.email import EmailMember
from app.models.ecology_user import EcologyUser
from app.schemas.email_personnel_sync import DataBackupInfo, DataBackupRequest, DataRestoreRequest

logger = logging.getLogger(__name__)

class DataBackupService:
    """数据备份服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.backup_dir = Path("data_backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def _generate_backup_id(self) -> str:
        """生成备份ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"backup_{timestamp}_{unique_id}"
    
    def _get_backup_file_path(self, backup_id: str, table_name: str) -> Path:
        """获取备份文件路径"""
        return self.backup_dir / f"{backup_id}_{table_name}.json"
    
    def _get_backup_info_file_path(self, backup_id: str) -> Path:
        """获取备份信息文件路径"""
        return self.backup_dir / f"{backup_id}_info.json"
    
    def backup_table_data(self, table_name: str, backup_id: str) -> DataBackupInfo:
        """备份指定表的数据"""
        try:
            backup_time = datetime.now()
            
            # 根据表名获取对应的模型类
            model_class = None
            if table_name == "email_members":
                model_class = EmailMember
            elif table_name == "ecology_users":
                model_class = EcologyUser
            else:
                raise ValueError(f"不支持的表名: {table_name}")
            
            # 查询所有数据
            records = self.db.query(model_class).all()
            record_count = len(records)
            
            # 将数据转换为字典格式
            data_list = []
            for record in records:
                record_dict = {}
                for column in record.__table__.columns:
                    value = getattr(record, column.name)
                    # 处理日期时间类型
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    record_dict[column.name] = value
                data_list.append(record_dict)
            
            # 保存到文件
            backup_file_path = self._get_backup_file_path(backup_id, table_name)
            with open(backup_file_path, 'w', encoding='utf-8') as f:
                json.dump(data_list, f, ensure_ascii=False, indent=2)
            
            # 创建备份信息
            backup_info = DataBackupInfo(
                backup_id=backup_id,
                backup_time=backup_time,
                table_name=table_name,
                record_count=record_count,
                backup_file_path=str(backup_file_path),
                description=f"表 {table_name} 的数据备份"
            )
            
            logger.info(f"成功备份表 {table_name}，共 {record_count} 条记录")
            return backup_info
            
        except Exception as e:
            logger.error(f"备份表 {table_name} 失败: {str(e)}", exc_info=True)
            raise
    
    def create_backup(self, request: DataBackupRequest) -> List[DataBackupInfo]:
        """创建数据备份"""
        backup_id = self._generate_backup_id()
        backup_infos = []
        
        logger.info(f"开始创建备份，备份ID: {backup_id}")
        
        try:
            # 备份每个指定的表
            for table_name in request.tables:
                backup_info = self.backup_table_data(table_name, backup_id)
                backup_infos.append(backup_info)
            
            # 保存备份信息到文件
            backup_summary = {
                "backup_id": backup_id,
                "backup_time": datetime.now().isoformat(),
                "description": request.description,
                "tables": [
                    {
                        "backup_id": info.backup_id,
                        "backup_time": info.backup_time.isoformat(),
                        "table_name": info.table_name,
                        "record_count": info.record_count,
                        "backup_file_path": info.backup_file_path,
                        "description": info.description
                    } for info in backup_infos
                ]
            }
            
            info_file_path = self._get_backup_info_file_path(backup_id)
            with open(info_file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"备份创建完成，备份ID: {backup_id}，共备份 {len(backup_infos)} 个表")
            return backup_infos
            
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}", exc_info=True)
            # 清理已创建的备份文件
            self._cleanup_backup_files(backup_id)
            raise
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        backups = []
        
        try:
            # 查找所有备份信息文件
            for info_file in self.backup_dir.glob("*_info.json"):
                try:
                    with open(info_file, 'r', encoding='utf-8') as f:
                        backup_info = json.load(f)
                    backups.append(backup_info)
                except Exception as e:
                    logger.warning(f"读取备份信息文件 {info_file} 失败: {str(e)}")
            
            # 按备份时间降序排列
            backups.sort(key=lambda x: x.get('backup_time', ''), reverse=True)
            
        except Exception as e:
            logger.error(f"列出备份失败: {str(e)}", exc_info=True)
        
        return backups
    
    def get_backup_info(self, backup_id: str) -> Optional[Dict[str, Any]]:
        """获取指定备份的信息"""
        try:
            info_file_path = self._get_backup_info_file_path(backup_id)
            if not info_file_path.exists():
                return None
            
            with open(info_file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            logger.error(f"获取备份信息失败: {str(e)}", exc_info=True)
            return None
    
    def _cleanup_backup_files(self, backup_id: str):
        """清理备份文件"""
        try:
            # 删除数据文件
            for backup_file in self.backup_dir.glob(f"{backup_id}_*.json"):
                backup_file.unlink()
            logger.info(f"已清理备份 {backup_id} 的文件")
        except Exception as e:
            logger.warning(f"清理备份文件失败: {str(e)}")
    
    def delete_backup(self, backup_id: str) -> bool:
        """删除指定的备份"""
        try:
            backup_info = self.get_backup_info(backup_id)
            if not backup_info:
                logger.warning(f"备份 {backup_id} 不存在")
                return False
            
            self._cleanup_backup_files(backup_id)
            logger.info(f"成功删除备份 {backup_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除备份失败: {str(e)}", exc_info=True)
            return False

    def restore_table_data(self, backup_id: str, table_name: str) -> bool:
        """恢复指定表的数据"""
        try:
            # 获取备份文件路径
            backup_file_path = self._get_backup_file_path(backup_id, table_name)
            if not backup_file_path.exists():
                logger.error(f"备份文件不存在: {backup_file_path}")
                return False

            # 读取备份数据
            with open(backup_file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # 根据表名获取对应的模型类
            model_class = None
            if table_name == "email_members":
                model_class = EmailMember
            elif table_name == "ecology_users":
                model_class = EcologyUser
            else:
                raise ValueError(f"不支持的表名: {table_name}")

            logger.info(f"开始恢复表 {table_name}，共 {len(backup_data)} 条记录")

            # 清空现有数据
            self.db.query(model_class).delete()

            # 恢复数据
            restored_count = 0
            for record_data in backup_data:
                try:
                    # 处理日期时间字段
                    for key, value in record_data.items():
                        if isinstance(value, str) and 'T' in value and ':' in value:
                            try:
                                record_data[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            except:
                                pass  # 如果不是日期时间格式，保持原值

                    # 创建新记录
                    new_record = model_class(**record_data)
                    self.db.add(new_record)
                    restored_count += 1

                except Exception as e:
                    logger.warning(f"恢复记录失败: {str(e)}, 数据: {record_data}")

            # 提交事务
            self.db.commit()

            logger.info(f"成功恢复表 {table_name}，共 {restored_count} 条记录")
            return True

        except Exception as e:
            logger.error(f"恢复表 {table_name} 失败: {str(e)}", exc_info=True)
            self.db.rollback()
            return False

    def restore_backup(self, request: DataRestoreRequest) -> bool:
        """恢复备份数据"""
        if not request.confirm:
            logger.warning("恢复操作需要确认")
            return False

        try:
            # 获取备份信息
            backup_info = self.get_backup_info(request.backup_id)
            if not backup_info:
                logger.error(f"备份 {request.backup_id} 不存在")
                return False

            logger.info(f"开始恢复备份 {request.backup_id}")

            # 恢复每个表的数据
            success_count = 0
            total_tables = len(backup_info['tables'])

            for table_info in backup_info['tables']:
                table_name = table_info['table_name']
                if self.restore_table_data(request.backup_id, table_name):
                    success_count += 1
                else:
                    logger.error(f"恢复表 {table_name} 失败")

            if success_count == total_tables:
                logger.info(f"备份 {request.backup_id} 恢复完成，成功恢复 {success_count}/{total_tables} 个表")
                return True
            else:
                logger.error(f"备份 {request.backup_id} 恢复部分失败，成功恢复 {success_count}/{total_tables} 个表")
                return False

        except Exception as e:
            logger.error(f"恢复备份失败: {str(e)}", exc_info=True)
            return False

    def get_backup_size(self, backup_id: str) -> Dict[str, Any]:
        """获取备份大小信息"""
        try:
            backup_info = self.get_backup_info(backup_id)
            if not backup_info:
                return {}

            total_size = 0
            file_sizes = {}

            for table_info in backup_info['tables']:
                table_name = table_info['table_name']
                backup_file_path = self._get_backup_file_path(backup_id, table_name)
                if backup_file_path.exists():
                    file_size = backup_file_path.stat().st_size
                    file_sizes[table_name] = file_size
                    total_size += file_size

            # 添加信息文件大小
            info_file_path = self._get_backup_info_file_path(backup_id)
            if info_file_path.exists():
                info_file_size = info_file_path.stat().st_size
                file_sizes['info'] = info_file_size
                total_size += info_file_size

            return {
                "backup_id": backup_id,
                "total_size": total_size,
                "total_size_mb": round(total_size / 1024 / 1024, 2),
                "file_sizes": file_sizes
            }

        except Exception as e:
            logger.error(f"获取备份大小失败: {str(e)}", exc_info=True)
            return {}
