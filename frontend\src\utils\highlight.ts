/**
 * 在文本中高亮搜索关键词
 * @param text 原始文本
 * @param keyword 搜索关键词
 * @param className 高亮样式类名
 * @returns 包含高亮标记的HTML字符串
 */
export function highlightKeyword(
  text: string, 
  keyword: string, 
  className: string = 'highlight'
): string {
  if (!text || !keyword) {
    return text || ''
  }

  // 转义特殊字符
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  
  // 创建正则表达式，忽略大小写
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  
  // 替换匹配的文本
  return text.replace(regex, `<span class="${className}">$1</span>`)
}

/**
 * 检查文本是否包含搜索关键词
 * @param text 文本
 * @param keyword 关键词
 * @returns 是否包含关键词
 */
export function containsKeyword(text: string, keyword: string): boolean {
  if (!text || !keyword) {
    return false
  }
  
  return text.toLowerCase().includes(keyword.toLowerCase())
}

/**
 * 获取包含关键词的文本片段
 * @param text 原始文本
 * @param keyword 关键词
 * @param maxLength 最大长度
 * @returns 包含关键词的文本片段
 */
export function getKeywordSnippet(
  text: string, 
  keyword: string, 
  maxLength: number = 100
): string {
  if (!text || !keyword) {
    return text?.substring(0, maxLength) || ''
  }

  const lowerText = text.toLowerCase()
  const lowerKeyword = keyword.toLowerCase()
  const keywordIndex = lowerText.indexOf(lowerKeyword)
  
  if (keywordIndex === -1) {
    return text.substring(0, maxLength)
  }

  // 计算片段的开始和结束位置
  const halfLength = Math.floor((maxLength - keyword.length) / 2)
  let start = Math.max(0, keywordIndex - halfLength)
  let end = Math.min(text.length, keywordIndex + keyword.length + halfLength)
  
  // 调整开始位置，确保不会截断单词
  if (start > 0) {
    const spaceIndex = text.lastIndexOf(' ', start + 10)
    if (spaceIndex > start) {
      start = spaceIndex + 1
    }
  }
  
  // 调整结束位置，确保不会截断单词
  if (end < text.length) {
    const spaceIndex = text.indexOf(' ', end - 10)
    if (spaceIndex > end - 10 && spaceIndex !== -1) {
      end = spaceIndex
    }
  }

  let snippet = text.substring(start, end)
  
  // 添加省略号
  if (start > 0) {
    snippet = '...' + snippet
  }
  if (end < text.length) {
    snippet = snippet + '...'
  }
  
  return snippet
} 