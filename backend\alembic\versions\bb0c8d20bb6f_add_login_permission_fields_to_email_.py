"""add_login_permission_fields_to_email_members

Revision ID: bb0c8d20bb6f
Revises: 1d37b02baf3c
Create Date: 2025-05-26 14:00:05.789715

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bb0c8d20bb6f'
down_revision: Union[str, None] = '1d37b02baf3c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加登录权限管理字段到email_members表
    op.add_column('email_members', 
                  sa.Column('force_secure_login', sa.Integer(), nullable=True, default=0, 
                           comment='强制启用安全登录 (0:关闭, 1:开启)'))
    op.add_column('email_members', 
                  sa.Column('imap_smtp_enabled', sa.Integer(), nullable=True, default=1, 
                           comment='IMAP/SMTP服务 (0:关闭, 1:开启)'))
    op.add_column('email_members', 
                  sa.Column('pop_smtp_enabled', sa.Integer(), nullable=True, default=1, 
                           comment='POP/SMTP服务 (0:关闭, 1:开启)'))
    op.add_column('email_members', 
                  sa.Column('secure_login_enabled', sa.Integer(), nullable=True, default=0, 
                           comment='是否启用安全登录 (0:关闭, 1:开启)'))


def downgrade() -> None:
    # 删除登录权限管理字段
    op.drop_column('email_members', 'force_secure_login')
    op.drop_column('email_members', 'imap_smtp_enabled')
    op.drop_column('email_members', 'pop_smtp_enabled')
    op.drop_column('email_members', 'secure_login_enabled')
