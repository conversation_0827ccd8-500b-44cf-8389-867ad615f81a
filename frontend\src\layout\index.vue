<template>
  <el-container class="layout-container">
    <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
      <div class="logo" :class="{ 'logo-collapse': isCollapse }">
        {{ isCollapse ? '运' : '运维管理平台' }}
      </div>
      <el-menu
        :router="true"
        :default-active="$route.path"
        class="menu"
        :collapse="isCollapse"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <MenuItem
          v-for="menu in menus"
          :key="menu.path"
          :menu-item="menu"
        />
      </el-menu>
    </el-aside>
    
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <el-icon 
            class="trigger" 
            @click="toggleSidebar"
          >
            <Expand v-if="isCollapse"/>
            <Fold v-else/>
          </el-icon>
        </div>
        <div class="header-right">
          <el-dropdown 
            trigger="click" 
            @command="handleCommand"
            @visible-change="handleVisibleChange"
          >
            <div class="user-info">
              <el-icon class="user-icon"><User /></el-icon>
              <div class="user-detail">
                <span class="username">{{ userStore.userInfo?.username || '未知用户' }}</span>
                <span class="user-role">{{ userStore.userInfo?.is_superuser ? '超级管理员' : '普通用户' }}</span>
              </div>
              <el-icon class="arrow-icon" :class="{ 'is-active': dropdownVisible }">
                <ArrowDown />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  用户信息
                </el-dropdown-item>
                <el-dropdown-item divided command="logout" class="dropdown-danger">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-main class="main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import MenuItem from '@/components/Menu/MenuItem.vue'
import { menus } from '@/router/menus'
import { Expand, Fold, SwitchButton, User, ArrowDown } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const isCollapse = ref(false)
const dropdownVisible = ref(false)

onMounted(async () => {
  if (import.meta.env.DEV) {
    console.log('[Layout] 布局组件已挂载，用户信息:', userStore.userInfo?.username)
  }
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      userStore.logout()
      router.push('/login')
    } catch {
      // 用户取消操作
    }
  } else if (command === 'profile') {
    router.push('/user/profile')
  }
}

const handleVisibleChange = (visible) => {
  dropdownVisible.value = visible
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  transition: all 0.3s;
}

.logo-collapse {
  font-size: 24px;
}

.menu {
  border: none;
  height: calc(100vh - 60px); /* 减去logo高度 */
  position: relative;
}

.menu:not(.el-menu--collapse) {
  width: 220px;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 20px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #409EFF;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 12px;
  height: 60px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f6f6f6;
}

.user-icon {
  font-size: 20px;
  padding: 4px;
  border-radius: 50%;
  background: #f0f2f5;
  color: #666;
}

.user-detail {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.user-role {
  font-size: 12px;
  color: #999;
}

.logout-button {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #f56c6c;
  transition: all 0.3s;
}

.logout-button:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.el-divider--vertical {
  height: 20px;
  margin: 0 12px;
}

.main {
  background: #f0f2f5;
  padding: 20px;
}

:deep(.el-menu-item) {
  &.is-active {
    background-color: #263445 !important;
  }
  
  &:hover {
    background-color: #263445 !important;
  }
}

.menu-bottom {
  display: none;
}

.arrow-icon {
  font-size: 12px;
  color: #909399;
  transition: transform 0.3s ease;
}

.arrow-icon.is-active {
  transform: rotate(180deg);
}

.user-info:hover .arrow-icon {
  transform: none;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 4px;
}

:deep(.dropdown-danger) {
  color: #f56c6c;
}

:deep(.dropdown-danger:hover) {
  background-color: #fef0f0;
  color: #f56c6c;
}
</style>
