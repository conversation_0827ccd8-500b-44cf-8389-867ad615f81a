"""add_column_add_users_to_dept_groups

Revision ID: 16badf81ccbc
Revises: 871e78634ea5
Create Date: 2025-03-21 09:09:32.660362

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '16badf81ccbc'
down_revision: Union[str, None] = '871e78634ea5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.add_column('ad_sync_config', sa.Column('add_users_to_dept_groups', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ad_sync_config', 'add_users_to_dept_groups')
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    # ### end Alembic commands ###
