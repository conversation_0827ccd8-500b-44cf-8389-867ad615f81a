from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Column, Integer, JSON
from sqlalchemy.orm import Mapped, mapped_column

from app.models import Base

class AssetSettings(Base):
    """资产管理设置模型
    
    ⚠️  CRITICAL TABLE - DO NOT DELETE ⚠️
    这是核心业务表，包含资产管理的重要配置信息。
    在任何迁移操作中都不应删除此表！
    """
    __tablename__ = "asset_settings"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    company: Mapped[str] = mapped_column(String(100), unique=True, comment="公司")
    asset_number_rule: Mapped[dict] = mapped_column(JSON, comment="资产编号规则")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    ) 