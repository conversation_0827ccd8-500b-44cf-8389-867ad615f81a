from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.sql import func
from app.database import Base

class InitializationLock(Base):
    """初始化锁表，用于确保某些初始化操作只执行一次"""
    __tablename__ = "initialization_locks"

    # 锁的名称作为主键
    lock_name = Column(String, primary_key=True, index=True)
    # 是否已初始化
    is_initialized = Column(Boolean, default=False)
    # 初始化时间
    initialized_at = Column(DateTime(timezone=True), nullable=True)
    # 最后更新时间
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<InitializationLock(lock_name='{self.lock_name}', is_initialized={self.is_initialized})>" 