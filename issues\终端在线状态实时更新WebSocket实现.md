# 终端在线状态实时更新WebSocket实现

## 问题描述
终端列表页面的在线状态不是实时更新的，只有在手动刷新或搜索时才会更新。用户无法及时了解终端的真实在线状态。

## 现状分析
1. **后端与终端通信**：使用gRPC，终端每300秒发送心跳
2. **前端与后端通信**：仅使用HTTP REST API，无实时推送
3. **数据更新时机**：前端只在页面加载、手动刷新、搜索/分页时获取数据

## 解决方案
实现WebSocket实时推送机制，当终端状态发生变化时立即推送到前端。

## 实施计划

### 第一步：后端WebSocket支持
- [x] 在FastAPI中添加WebSocket端点
- [x] 创建终端状态变化广播机制  
- [x] 在gRPC心跳处理中触发WebSocket推送

### 第二步：前端WebSocket客户端
- [x] 创建WebSocket连接管理类
- [x] 在终端列表页面建立WebSocket连接
- [x] 接收实时状态更新并刷新界面

### 第三步：状态同步优化
- [x] 只推送变化的终端状态
- [x] 添加连接断线重连机制
- [x] 优化用户体验（避免频繁刷新）

## 技术方案
- 后端：FastAPI WebSocket + 消息广播
- 前端：原生WebSocket API + Vue3响应式更新
- 触发机制：gRPC心跳处理完成后广播状态变化

## 预期效果
- 终端上线/离线状态实时反映在前端界面
- 减少用户手动刷新操作
- 提升用户体验和系统的实时性

## 实施完成状态

### 已完成功能
1. **后端WebSocket服务**
   - 创建了 `/ws/terminal-status` WebSocket端点
   - 实现了连接管理和消息广播机制
   - 集成到FastAPI主应用中

2. **gRPC心跳广播**
   - 修改了gRPC心跳处理逻辑
   - 在终端状态更新时触发WebSocket广播
   - 处理了异步事件循环兼容性问题

3. **前端WebSocket客户端**
   - 创建了完整的WebSocket客户端类
   - 实现了自动重连机制
   - 添加了连接状态监控

4. **前端界面集成**
   - 在终端列表页面集成了WebSocket连接
   - 实现了实时状态更新
   - 添加了连接状态指示器

### 关键文件修改
- `backend/app/api/v1/websocket.py` - 新建WebSocket API
- `backend/app/main.py` - 注册WebSocket路由
- `backend/app/grpc/server.py` - 心跳处理添加广播
- `frontend/src/utils/websocket.ts` - 新建WebSocket客户端
- `frontend/src/views/terminal/List.vue` - 集成实时更新

### 技术特性
- 支持自动重连（最多5次）
- 心跳保活机制（30秒间隔）
- 页面可见性检测重连
- 网络状态监控
- 错误处理和日志记录

## 测试验证
实现完成后需要验证：
1. WebSocket连接是否正常建立
2. 终端心跳是否触发实时更新
3. 断线重连机制是否工作
4. 界面状态是否实时同步 