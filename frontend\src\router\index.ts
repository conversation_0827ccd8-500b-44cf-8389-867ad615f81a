import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw, NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '../stores/user.js'
import { hasAnyPermission } from '@/utils/permission'
import { mobileRoutes } from '@mobile/router/index'
import { usePlatform } from '@/composables/usePlatform'
import { ElMessage } from 'element-plus'

const routes: Array<RouteRecordRaw> = [
  // Loading页面独立路由
  {
    path: '/loading',
    name: 'Loading',
    component: () => import('../views/Loading.vue'),
    meta: { requiresAuth: true }
  },
  
  // 移动端路由重定向 (向后兼容)
  {
    path: '/mobile/:pathMatch(.*)*',
    redirect: to => {
      const targetPath = to.params.pathMatch
      return `/m/${targetPath}`
    }
  },
  
  // 添加移动端路由
  ...mobileRoutes,
  
  // 独立的登录和测试页面
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/device-test',
    name: 'DeviceTest',
    component: () => import('../views/DeviceTest.vue'),
    meta: { requiresAuth: false }
  },
  
  // 桌面端主布局路由 - 使用根级路径
  {
    path: '/',
    name: 'Layout',
    component: () => import('../layout/index.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('../views/dashboard/index.vue'),
        meta: { title: '仪表盘', requiresAuth: true }
      },
      {
        path: 'user/profile',
        name: 'UserProfile',
        component: () => import('../views/user/profile.vue'),
        meta: { 
          requiresAuth: true,
          title: '用户信息'
        }
      },
      {
        path: 'basic-info/personnel',
        name: 'PersonnelInfo',
        component: () => import('../views/basic-info/PersonnelInfo.vue'),
        meta: { 
          requiresAuth: true,
          title: '人员信息',
          permissions: ['basic-info:personnel:view']
        }
      },
      {
        path: 'ad',
        name: 'ADManagement',
        component: () => import('../views/ad/index.vue'),
        meta: { 
          requiresAuth: true,
          title: 'AD管理',
          permissions: ['ad:view']
        }
      },
      {
        path: 'ad/config',
        name: 'ADConfig',
        component: () => import('../views/ad/ADConfig.vue'),
        meta: {
          requiresAuth: true,
          title: 'AD配置',
          permissions: ['ad:config']
        }
      },
      {
        path: 'ad/sync-config',
        name: 'ADSyncConfig',
        component: () => import('../views/ad/ADSyncConfig.vue'),
        meta: {
          requiresAuth: true,
          title: 'AD人员同步',
          permissions: ['ad:sync']
        }
      },
      {
        path: 'email',
        name: 'EmailManagement',
        component: () => import('../views/email/index.vue'),
        meta: {
          requiresAuth: true,
          title: '邮箱管理',
          permissions: ['email:view']
        }
      },
      {
        path: 'email/config',
        name: 'EmailConfig',
        component: () => import('../views/email/EmailConfig.vue'),
        meta: {
          requiresAuth: true,
          title: '邮箱配置',
          permissions: ['email:config:view']
        }
      },
      {
        path: 'email/departments-members',
        name: 'EmailDepartmentsMembers',
        component: () => import('../views/email/DepartmentMemberManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '部门与成员管理',
          permissions: ['email:department:view', 'email:member:view']
        }
      },
      {
        path: 'email/groups',
        name: 'EmailGroups',
        component: () => import('../views/email/GroupManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '群组管理',
          permissions: ['email:group:view']
        }
      },
      {
        path: 'email/tags',
        name: 'EmailTags',
        component: () => import('../views/email/TagManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '标签管理',
          permissions: ['email:tag:view']
        }
      },
      {
        path: 'email/sync',
        name: 'EmailSync',
        component: () => import('../views/email/SyncManagement.vue'),
        meta: {
          requiresAuth: true,
          title: '同步管理',
          permissions: ['email:sync:view', 'email:member:edit']
        }
      },
      {
        path: 'email/creation-requests',
        name: 'EmailCreationRequests',
        component: () => import('../views/email/CreationRequests.vue'),
        meta: {
          requiresAuth: true,
          title: '邮箱申请管理',
          permissions: ['email:request:view']
        }
      },
      {
        path: 'asset/list',
        name: 'AssetList',
        component: () => import('../views/asset/AssetList.vue'),
        meta: {
          requiresAuth: true,
          title: '资产列表',
          permissions: ['asset:view']
        }
      },
      {
        path: 'asset/settings',
        name: 'AssetSettings',
        component: () => import('../views/asset/AssetSettings.vue'),
        meta: {
          requiresAuth: true,
          title: '资产设置',
          permissions: ['asset:field:manage']
        }
      },
      {
        path: 'asset/custom-fields',
        name: 'CustomFieldManagement',
        component: () => import('@/views/system/CustomFieldManagement.vue'),
        meta: {
          title: '自定义字段管理',
          icon: 'Document',
          requiresAuth: true,
          permissions: ['asset:field:manage']
        }
      },
      {
        path: 'inventory',
        name: 'Inventory',
        meta: {
          title: '资产盘点',
          icon: 'Tickets',
          requiresAuth: true,
          permissions: ['inventory:view']
        },
        children: [
          {
            path: 'tasks',
            name: 'inventory-tasks',
            component: () => import('../views/inventory/TaskList.vue'),
            meta: {
              title: '盘点任务',
              requiresAuth: true,
              permissions: ['inventory:view']
            }
          },
          {
            path: 'tasks/:id',
            name: 'inventory-task-detail',
            component: () => import('../views/inventory/TaskDetail.vue'),
            meta: {
              title: '盘点任务详情',
              hidden: true,
              requiresAuth: true,
              permissions: ['inventory:view']
            }
          }
        ]
      },
      {
        path: 'field-values',
        name: 'FieldValues',
        component: () => import('@/views/field-value/index.vue'),
        meta: {
          title: '字段值管理',
          icon: 'el-icon-document',
          permissions: ['asset:field:manage']
        }
      },
      {
        path: 'system/settings',
        name: 'SystemSettings',
        component: () => import('@/views/system/SystemSettings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          requiresAuth: true,
          permissions: ['system:view']
        }
      },
      {
        path: 'system/users',
        name: 'UserManagement',
        component: () => import('@/views/system/UserManagement.vue'),
        meta: {
          title: '用户管理',
          icon: 'User',
          requiresAuth: true,
          permissions: ['system:view']
                }
      },
      {
        path: 'system/ldap-config',
        name: 'LdapConfig',
        component: () => import('@/views/system/LdapConfig.vue'),
        meta: {
          title: 'LDAP配置',
          icon: 'Connection',
          requiresAuth: true,
          permissions: ['ldap:config:view']
        }
      },
      {
        path: 'system/lock-management',
        name: 'LockManagement',
        component: () => import('@/views/system/LockManagement.vue'),
        meta: {
          title: '同步锁管理',
          icon: 'Lock',
          requiresAuth: true,
          permissions: ['system:lock:manage']
        }
      },
      // 终端管理相关路由
      {
        path: 'terminal/overview',
        name: 'TerminalOverview',
        component: () => import('@/views/terminal/Overview.vue'),
        meta: {
          title: '终端概况',
          requiresAuth: true,
          permissions: ['terminal:view']
        }
      },
      {
        path: 'terminal/list',
        name: 'TerminalList',
        component: () => import('@/views/terminal/List.vue'),
        meta: {
          title: '终端列表',
          requiresAuth: true,
          permissions: ['terminal:view']
        }
      },
      {
        path: 'terminal/software',
        name: 'SoftwareManagement',
        component: () => import('@/views/terminal/Software.vue'),
        meta: {
          title: '软件管理',
          requiresAuth: true,
          permissions: ['terminal:view']
        }
      },
      {
        path: 'terminal/software/:name',
        name: 'SoftwareDetail',
        component: () => import('@/views/terminal/SoftwareDetail.vue'),
        meta: {
          title: '软件详情',
          requiresAuth: true,
          permissions: ['terminal:view']
        }
      },
      {
        path: 'terminal/detail/:id',
        name: 'TerminalDetail',
        component: () => import('@/views/terminal/Detail.vue'),
        meta: {
          title: '终端详情',
          hidden: true,
          requiresAuth: true,
          permissions: ['terminal:view']
        }
      },
      {
        path: 'terminal/agent',
        name: 'AgentManagement',
        component: () => import('@/views/terminal/Agent.vue'),
        meta: {
          title: 'Agent管理',
          requiresAuth: true,
          permissions: ['terminal:agent:manage']
        }
      },
      {
        path: 'terminal/command-whitelist',
        name: 'CommandWhitelist',
        component: () => import('@/views/terminal/CommandWhitelist.vue'),
        meta: {
          title: '命令白名单',
          requiresAuth: true,
          permissions: ['terminal:command:manage']
        }
      },
      {
        path: '403',
        name: 'Forbidden',
        component: () => import('@/views/error/403.vue'),
        meta: {
          title: '无权访问',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 高性能同步路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  const { shouldUseMobile } = usePlatform()

  // 1. 公共路由直接通过
  const publicRoutes = ['/login', '/m/login', '/loading', '/device-test']
  if (publicRoutes.includes(to.path)) {
    return next()
  }

  // 2. 根路径重定向
  if (to.path === '/') {
    if (!userStore.isLoggedIn) {
      const loginPath = shouldUseMobile.value ? '/m/login' : '/login'
      return next(loginPath)
    } else {
      const homePath = shouldUseMobile.value ? '/m/apps' : '/dashboard'
      return next(homePath)
    }
  }

  // 3. 登录状态检查
  if (!userStore.isLoggedIn) {
    const loginPath = shouldUseMobile.value ? '/m/login' : '/login'
    return next(loginPath)
  }

  // 3.1 如果已登录但用户信息缺失，后台异步获取（不阻塞路由）
  if (!userStore.userInfo?.id && !userStore.isLoading) {
    userStore.getUserInfo().catch(() => {
      // 获取失败不影响路由导航
    })
  }

  // 4. 权限验证（仅对有权限要求的页面）
  if (to.meta.permissions && Array.isArray(to.meta.permissions)) {
    const permissions = to.meta.permissions as string[]
    
    // 如果用户信息存在，进行权限检查
    if (userStore.userInfo?.id) {
      const hasRequiredPermission = permissions.some(permission => 
        userStore.hasPermission(permission)
      )
      if (!hasRequiredPermission) {
        return next('/403')
      }
    } else if (!userStore.isLoading) {
      // 仅在有权限要求且用户信息缺失时才跳转到Loading页面
      const loadingPath = `/loading?redirect=${encodeURIComponent(to.fullPath)}`
      return next(loadingPath)
    }
    // 如果正在加载中，允许继续访问
  }

  // 5. 平台路由处理
  const isMobileRoute = to.path.startsWith('/m/')
  
  if (shouldUseMobile.value && !isMobileRoute) {
    const mobilePath = to.path === '/' ? '/m/apps' : `/m${to.path}`
    return next(mobilePath)
  }
  
  if (!shouldUseMobile.value && isMobileRoute) {
    const desktopPath = to.path === '/m/apps' ? '/dashboard' : to.path.replace(/^\/m/, '') || '/dashboard'
    return next(desktopPath)
  }

  // 6. 所有检查通过，允许导航
  next()
})

export default router