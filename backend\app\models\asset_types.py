from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Column, Integer, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models import Base

class AssetType(Base):
    """资产类型模型"""
    __tablename__ = "asset_types"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, comment="类型名称")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="描述")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

class AssetStatus(Base):
    """资产状态模型"""
    __tablename__ = "asset_statuses"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, comment="状态名称")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="描述")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

class AssetBrand(Base):
    """资产品牌模型"""
    __tablename__ = "asset_brands"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, comment="品牌名称")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="描述")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

class AssetModel(Base):
    """资产型号模型"""
    __tablename__ = "asset_models"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), comment="型号名称")
    brand_id: Mapped[int] = mapped_column(Integer, ForeignKey("asset_brands.id"), comment="品牌ID")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="描述")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )
    
    # 关联关系
    brand = relationship("AssetBrand") 