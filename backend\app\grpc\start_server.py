import logging
import os
import sys
import traceback

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# 首先编译 proto 文件
from app.grpc.generate_pb import generate_proto_code
from app.grpc.server import serve

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别以获取更详细信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    try:
        # 编译 proto 文件
        logger.info("开始编译 proto 文件...")
        generate_proto_code()
        
        # 查看Python路径
        logger.debug("Python路径: %s", sys.path)
        
        # 启动 gRPC 服务器
        logger.info("正在启动 gRPC 服务器...")
        serve()
    except ImportError as e:
        logger.error(f"导入错误: {str(e)}")
        logger.error(f"详细信息: {traceback.format_exc()}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动 gRPC 服务器失败: {str(e)}")
        logger.error(f"详细信息: {traceback.format_exc()}")
        sys.exit(1) 