# 移动端盘点搜索功能实施

## 任务概述
改进移动端资产盘点页面的搜索功能，使其能够正确按照资产编号、资产名称等信息进行准确搜索。

## 问题分析
1. **原有搜索功能问题**：
   - 使用简单的`van-search`组件，缺少搜索字段选择
   - `handleSearchInput`函数只打印日志，没有实际调用API
   - `getInventoryRecords` API支持`keyword`参数，但没有传递搜索关键词
   - 缺少搜索结果高亮显示

2. **参考实现**：
   - 移动端资产列表使用`SimpleSearch`组件
   - 支持多字段搜索（资产编号、名称、类别等）
   - 实现了防抖搜索和关键词高亮
   - 正确传递搜索参数到API

## 实施方案
使用`SimpleSearch`组件替换现有搜索实现，复用资产列表的成熟搜索功能。

## 具体修改

### 1. 组件导入和搜索状态
```typescript
// 新增导入
import SimpleSearch from '@mobile/components/business/SimpleSearch.vue'
import { highlightKeyword } from '@/utils/highlight'

// 搜索相关状态
const searchValue = ref('')
const searchField = ref('asset_number')
```

### 2. 搜索组件替换
```vue
<!-- 原有实现 -->
<van-search
  v-model="searchKeyword"
  placeholder="搜索资产编号或名称"
  shape="round"
  show-action
  @search="handleSearch"
  @clear="handleClearSearch"
  @input="handleSearchInput"
>
  <template #action>
    <div class="scan-action" @click="handleScanClick">
      <van-icon name="scan" size="20" color="#1989fa" />
    </div>
  </template>
</van-search>

<!-- 新实现 -->
<simple-search
  v-model="searchValue"
  v-model:search-field="searchField"
  @search="handleSearch"
  @clear="handleSearchClear"
/>
```

### 3. 搜索逻辑实现
```typescript
// 处理搜索
const handleSearch = async (value: string, field: string) => {
  searchValue.value = value
  searchField.value = field
  // 重置页码并刷新数据
  recordsPagination.current = 1
  await loadRecords(true)
}

// 处理搜索清空
const handleSearchClear = async () => {
  searchValue.value = ''
  searchField.value = 'asset_number' // 重置为默认搜索字段
  recordsPagination.current = 1
  await loadRecords(true)
}

// 高亮搜索关键词
const highlightText = (text: string) => {
  if (!searchValue.value || !text) {
    return text
  }
  return highlightKeyword(text, searchValue.value, 'search-highlight')
}
```

### 4. API调用优化
```typescript
// 在loadRecords函数中添加keyword参数
const params = {
  skip: (recordsPagination.current - 1) * recordsPagination.pageSize,
  limit: recordsPagination.pageSize,
  status: activeRecordStatus.value === 'all' ? undefined : activeRecordStatus.value,
  keyword: searchValue.value || undefined  // 新增搜索关键词
}
```

### 5. 搜索结果高亮
```vue
<!-- 资产名称高亮 -->
<span class="asset-name" v-html="highlightText(record.asset.name)"></span>

<!-- 资产编号高亮 -->
<span v-html="highlightText(record.asset.asset_number)"></span>

<!-- 保管人高亮 -->
<span v-html="highlightText(record.asset.custodian)"></span>

<!-- 位置高亮 -->
<span v-html="highlightText(record.asset.location)"></span>
```

### 6. 空状态优化
```vue
<van-empty
  v-if="!recordsLoading && recordsList.length === 0"
  image="search"
  :description="searchValue ? '未找到相关记录' : '暂无盘点记录'"
/>
```

### 7. 搜索高亮样式
```scss
// 搜索高亮样式
:deep(.search-highlight) {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
}
```

## 功能特性

### 1. 多字段搜索支持
- 资产编号 (asset_number)
- 资产名称 (name)
- 资产类别 (category)
- 规格型号 (specification)
- 姓名 (person_name)
- 工号 (job_number)
- 部门 (department)

### 2. 搜索体验优化
- **防抖搜索**：避免频繁API调用
- **实时搜索**：输入时自动搜索
- **关键词高亮**：搜索结果中高亮显示匹配关键词
- **字段选择**：支持选择不同搜索字段
- **搜索清空**：一键清空搜索条件

### 3. 与现有功能集成
- **状态筛选**：搜索与状态筛选协同工作
- **分页加载**：搜索结果支持分页加载
- **扫码集成**：扫码结果自动填入搜索框

## 技术实现细节

### 1. 组件复用
- 复用`SimpleSearch`组件，保持界面一致性
- 利用已验证的搜索逻辑，减少开发风险

### 2. API集成
- 利用现有`inventoryApi.getInventoryRecords`的`keyword`参数
- 保持后端API不变，仅优化前端调用

### 3. 性能优化
- 搜索时重置分页状态，避免数据混乱
- 使用防抖机制，减少不必要的API调用

## 测试要点

### 1. 搜索功能测试
- [ ] 按资产编号搜索
- [ ] 按资产名称搜索
- [ ] 按保管人搜索
- [ ] 按位置搜索
- [ ] 搜索字段切换
- [ ] 搜索清空功能

### 2. 界面交互测试
- [ ] 搜索结果高亮显示
- [ ] 空状态提示
- [ ] 搜索与筛选协同
- [ ] 分页加载正常

### 3. 性能测试
- [ ] 搜索响应速度
- [ ] 防抖机制生效
- [ ] 内存使用正常

## 完成状态
✅ **已完成** - 移动端盘点搜索功能已成功实施

## 后续优化建议
1. 考虑添加搜索历史记录
2. 支持模糊搜索和精确搜索切换
3. 添加搜索结果统计信息
4. 优化大数据量下的搜索性能 