<template>
  <div class="asset-receipt-form" ref="receiptFormRef">
    <div class="receipt-header">
      <h1>资产验收单</h1>
      <div class="receipt-date">填单日期：{{ currentDate }}</div>
    </div>
    
    <table class="receipt-table">
      <tbody>
        <tr>
          <td class="label">资产名称：</td>
          <td class="value">{{ asset.name }}</td>
          <td class="label">固定资产编号：</td>
          <td class="value">{{ asset.asset_number }}</td>
        </tr>
        <tr>
          <td class="label">资产规格：</td>
          <td class="value">{{ asset.specification }}</td>
          <td class="label">生产编号：</td>
          <td class="value">{{ asset.production_number }}</td>
        </tr>
        <tr>
          <td class="label">价格：</td>
          <td class="value">{{ formatPrice(asset.price) }}</td>
          <td class="label">供应商：</td>
          <td class="value">{{ asset.supplier }}</td>
        </tr>
        <tr>
          <td class="label">制造商：</td>
          <td class="value">{{ asset.manufacturer }}</td>
          <td class="label">使用部门：</td>
          <td class="value">{{ asset.user_department }}</td>
        </tr>
        <tr>
          <td class="label">使用人：</td>
          <td class="value">{{ asset.user }}</td>
          <td class="label">领用人：</td>
          <td class="value">{{ asset.custodian }}</td>
        </tr>
        <tr>
          <td class="label">采购人：</td>
          <td class="value">{{ asset.purchaser }}</td>
          <td class="label">验收人：</td>
          <td class="value">{{ asset.inspector }}</td>
        </tr>
        <tr>
          <td class="label">备注：</td>
          <td class="value" colspan="3">{{ asset.remarks }}</td>
        </tr>
      </tbody>
    </table>
    
    <div class="receipt-footer">
      <div class="signature-area">
        <div class="signature-item">
          <span>领用人签字：</span>
          <div v-if="!signatures.custodian" class="signature-line" @click="startSignature('custodian')">
            <div class="signature-hint">点击此处签名</div>
          </div>
          <img v-else :src="signatures.custodian" class="signature-image" @click="startSignature('custodian')" />
        </div>
        <div class="signature-item">
          <span>验收人签字：</span>
          <div v-if="!signatures.inspector" class="signature-line" @click="startSignature('inspector')">
            <div class="signature-hint">点击此处签名</div>
          </div>
          <img v-else :src="signatures.inspector" class="signature-image" @click="startSignature('inspector')" />
        </div>
        <div class="signature-item">
          <span>采购人签字：</span>
          <div v-if="!signatures.purchaser" class="signature-line" @click="startSignature('purchaser')">
            <div class="signature-hint">点击此处签名</div>
          </div>
          <img v-else :src="signatures.purchaser" class="signature-image" @click="startSignature('purchaser')" />
        </div>
      </div>
    </div>
  </div>

  <!-- 签名对话框 -->
  <el-dialog
    v-model="signatureDialogVisible"
    title="请在下方区域签名"
    width="400px"
    :close-on-click-modal="false"
    append-to-body
    @closed="handleDialogClose"
  >
    <div class="signature-pad-container">
      <div class="signature-pad-wrapper">
        <canvas ref="signatureCanvas" class="signature-pad"></canvas>
      </div>
      <div class="signature-tips">提示：请在上方区域手写签名</div>
    </div>
    <div class="signature-actions">
      <el-button @click="clearSignature">清除</el-button>
      <el-button type="primary" @click="saveSignature" :disabled="!isSignatureActive">保存</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import type { Asset } from '@/types/asset'
import dayjs from 'dayjs'

const props = defineProps<{
  asset: Asset
}>()

const receiptFormRef = ref<HTMLElement | null>(null)
const signatureCanvas = ref<HTMLCanvasElement | null>(null)
const signatureContext = ref<CanvasRenderingContext2D | null>(null)
const signatureDialogVisible = ref(false)
const currentSignatureType = ref<'custodian' | 'inspector' | 'purchaser' | null>(null)
const isSignatureActive = ref(false)
const isPainting = ref(false)
const lastPos = reactive({ x: 0, y: 0 })

// 存储签名图像的URL
const signatures = reactive<{
  custodian: string | null,
  inspector: string | null,
  purchaser: string | null
}>({
  custodian: null,
  inspector: null,
  purchaser: null
})

const currentDate = computed(() => {
  return dayjs().format('YYYY/MM/DD')
})

// 格式化价格
const formatPrice = (price?: number) => {
  if (price === undefined || price === null) return '/'
  return `¥ ${price.toFixed(2)}`
}

// 开始签名
const startSignature = (type: 'custodian' | 'inspector' | 'purchaser') => {
  currentSignatureType.value = type
  signatureDialogVisible.value = true
  
  // 在下一个 tick 后初始化签名板
  setTimeout(() => {
    if (signatureCanvas.value) {
      const canvas = signatureCanvas.value
      const context = canvas.getContext('2d')
      
      if (context) {
        // 设置画布大小
        canvas.width = canvas.offsetWidth
        canvas.height = canvas.offsetHeight
        
        // 设置画笔样式
        context.lineWidth = 2
        context.lineCap = 'round'
        context.lineJoin = 'round'
        context.strokeStyle = '#000'
        
        // 清空画布
        context.clearRect(0, 0, canvas.width, canvas.height)
        
        signatureContext.value = context
        
        // 初始化事件监听
        initSignatureEvents()
      }
    }
    isSignatureActive.value = false
  }, 100)
}

// 初始化签名事件监听
const initSignatureEvents = () => {
  const canvas = signatureCanvas.value
  if (!canvas) return
  
  // 先移除所有已存在的事件监听，避免重复绑定
  removeSignatureEvents()
  
  // 重新添加事件监听
  canvas.addEventListener('mousedown', handleStart)
  canvas.addEventListener('mousemove', handleMove)
  canvas.addEventListener('mouseup', handleEnd)
  canvas.addEventListener('mouseleave', handleEnd)
  
  // 触摸设备支持
  canvas.addEventListener('touchstart', handleStart)
  canvas.addEventListener('touchmove', handleMove)
  canvas.addEventListener('touchend', handleEnd)
}

// 移除签名事件监听
const removeSignatureEvents = () => {
  const canvas = signatureCanvas.value
  if (!canvas) return

  canvas.removeEventListener('mousedown', handleStart)
  canvas.removeEventListener('mousemove', handleMove)
  canvas.removeEventListener('mouseup', handleEnd)
  canvas.removeEventListener('mouseleave', handleEnd)
  canvas.removeEventListener('touchstart', handleStart)
  canvas.removeEventListener('touchmove', handleMove)
  canvas.removeEventListener('touchend', handleEnd)
}

// 对话框关闭时清理事件
const handleDialogClose = () => {
  removeSignatureEvents()
  currentSignatureType.value = null
}

// 鼠标/触摸事件处理
const handleStart = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  isPainting.value = true
  isSignatureActive.value = true
  
  // 获取起始位置
  const position = getPointerPosition(e)
  if (position && signatureContext.value) {
    lastPos.x = position.x
    lastPos.y = position.y
  }
}

const handleMove = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  if (!isPainting.value || !signatureContext.value) return
  
  const position = getPointerPosition(e)
  if (!position) return
  
  // 绘制线条
  const ctx = signatureContext.value
  ctx.beginPath()
  ctx.moveTo(lastPos.x, lastPos.y)
  ctx.lineTo(position.x, position.y)
  ctx.stroke()
  
  // 更新位置
  lastPos.x = position.x
  lastPos.y = position.y
}

const handleEnd = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  isPainting.value = false
}

// 获取指针位置
const getPointerPosition = (e: MouseEvent | TouchEvent) => {
  if (!signatureCanvas.value) return null
  
  const canvas = signatureCanvas.value
  const rect = canvas.getBoundingClientRect()
  
  let clientX: number
  let clientY: number
  
  if ('touches' in e) {
    // 触摸事件
    if (e.touches.length === 0) return null
    clientX = e.touches[0].clientX
    clientY = e.touches[0].clientY
  } else {
    // 鼠标事件
    clientX = e.clientX
    clientY = e.clientY
  }
  
  return {
    x: clientX - rect.left,
    y: clientY - rect.top
  }
}

// 清除签名
const clearSignature = () => {
  if (signatureCanvas.value && signatureContext.value) {
    signatureContext.value.clearRect(0, 0, signatureCanvas.value.width, signatureCanvas.value.height)
    isSignatureActive.value = false
  }
}

// 保存签名
const saveSignature = () => {
  if (!signatureCanvas.value || !currentSignatureType.value) return
  
  // 将画布内容转换为图像URL
  const dataUrl = signatureCanvas.value.toDataURL('image/png')
  
  // 保存到对应的签名变量
  signatures[currentSignatureType.value] = dataUrl
  
  // 关闭对话框
  signatureDialogVisible.value = false
  currentSignatureType.value = null
}

// 打印方法
const printReceipt = () => {
  const printWindow = window.open('', '_blank')
  if (!printWindow) {
    console.error('无法打开打印窗口')
    return
  }
  
  // 获取当前组件的HTML
  const content = receiptFormRef.value?.innerHTML || ''
  
  // 构建HTML内容
  let htmlContent = '<!DOCTYPE html><html><head>'
  htmlContent += '<title>资产验收单 - ' + props.asset.name + '</title>'
  htmlContent += '<style>'
  htmlContent += 'body { font-family: "SimSun", "宋体", sans-serif; padding: 10px; margin: 0; }'
  htmlContent += '.receipt-header { text-align: center; margin-bottom: 15px; }'
  htmlContent += '.receipt-header h1 { font-size: 22px; margin: 0 0 8px 0; font-weight: bold; }'
  htmlContent += '.receipt-date { text-align: right; margin-bottom: 15px; font-size: 14px; }'
  htmlContent += '.receipt-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }'
  htmlContent += '.receipt-table td { border: 1px solid #000; padding: 6px 8px; height: 30px; font-size: 14px; }'
  htmlContent += '.label { width: 20%; font-weight: bold; background-color: #f5f5f5; }'
  htmlContent += '.value { width: 30%; }'
  htmlContent += '.receipt-footer { margin-top: 25px; }'
  htmlContent += '.signature-area { display: flex; justify-content: space-between; align-items: center; gap: 20px; }'
  htmlContent += '.signature-item { flex: 1; font-size: 14px; display: flex; align-items: center; margin-right: 0; }'
  htmlContent += '.signature-item span { white-space: nowrap; margin-right: 8px; }'
  htmlContent += '.signature-line { flex: 1; border-bottom: 1px solid #000; height: 20px; cursor: pointer; position: relative; background-color: #f5f5f5; min-width: 120px; }'
  htmlContent += '.signature-hint { position: absolute; top: 0; left: 0; width: 100%; font-size: 12px; color: #909399; text-align: center; }'
  htmlContent += '.signature-image { flex: 1; max-width: none; max-height: 50px; cursor: pointer; object-fit: contain; }'
  htmlContent += '@media print { '
  htmlContent += '  @page { size: A5 landscape; margin: 0.5cm; }'
  htmlContent += '  .signature-hint { display: none; }'
  htmlContent += '  .signature-line { background-color: transparent; }'
  htmlContent += '}'
  htmlContent += '</style>'
  htmlContent += '</head><body>'
  htmlContent += '<div class="asset-receipt-form">' + content + '</div>'
  htmlContent += '<script>'
  htmlContent += 'window.onload = function() {'
  htmlContent += '  window.print();'
  htmlContent += '  setTimeout(function() { window.close(); }, 500);'
  htmlContent += '};'
  htmlContent += '</' + 'script>'
  htmlContent += '</body></html>'
  
  printWindow.document.write(htmlContent)
  printWindow.document.close()
}

// 在组件挂载时不再初始化签名事件，改为在打开对话框时初始化
onMounted(() => {
  // 不再需要初始化，改为在对话框打开时初始化
})

// 暴露打印方法
defineExpose({
  printReceipt
})
</script>

<style scoped>
.asset-receipt-form {
  font-family: "SimSun", "宋体", sans-serif;
  padding: 15px;
  background-color: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 100%;
  height: auto;
  margin: 0 auto;
}

.receipt-header {
  text-align: center;
  margin-bottom: 15px;
}

.receipt-header h1 {
  font-size: 22px;
  margin: 0 0 8px 0;
  font-weight: bold;
}

.receipt-date {
  text-align: right;
  margin-bottom: 15px;
  font-size: 14px;
}

.receipt-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.receipt-table td {
  border: 1px solid #000;
  padding: 6px 8px;
  height: 30px;
  font-size: 14px;
}

.label {
  width: 20%;
  font-weight: bold;
  background-color: #f5f5f5;
}

.value {
  width: 30%;
}

.receipt-footer {
  margin-top: 25px;
}

.signature-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.signature-item {
  flex: 1;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-right: 0;
}

.signature-item span {
  white-space: nowrap;
  margin-right: 8px;
}

.signature-container {
  display: none; /* 移除不需要的容器 */
}

.signature-line {
  flex: 1;
  border-bottom: 1px solid #000;
  height: 20px;
  cursor: pointer;
  position: relative;
  background-color: #f5f5f5;
  min-width: 120px;
}

.signature-hint {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.signature-image {
  flex: 1;
  max-width: none;
  max-height: 50px;
  cursor: pointer;
  object-fit: contain;
}

.signature-pad-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.signature-pad-wrapper {
  width: 100%;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
}

.signature-pad {
  width: 100%;
  height: 200px;
  touch-action: none;
  cursor: crosshair;
}

.signature-tips {
  margin-top: 8px;
  color: #909399;
  font-size: 14px;
}

.signature-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 