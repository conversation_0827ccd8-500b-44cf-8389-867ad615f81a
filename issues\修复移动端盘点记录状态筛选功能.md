# 修复移动端盘点记录状态筛选功能

## 问题描述
移动端资产盘点记录页面的状态筛选选项卡功能失效，点击任何选项都会显示所有资产记录，无法按状态进行筛选。

## 问题分析
通过代码检查发现问题根源：
1. **前端代码正确**：移动端正确传递了 `status` 参数给后端API
2. **CRUD层正确**：`get_multi_by_task` 方法已正确实现状态筛选逻辑
3. **API层缺失**：后端API的 `list_inventory_records` 函数缺少 `status` 参数接收

## 修复方案
### 1. 修复前端van-tabs组件绑定
**文件**: `frontend/src/mobile/views/asset/InventoryTask.vue`
**问题**: Vant 4.x版本中，van-tabs的v-model应该使用`v-model:active`
**修改**: 
```vue
<!-- 修改前 -->
<van-tabs v-model="activeRecordStatus" @change="handleRecordStatusChange">

<!-- 修改后 -->
<van-tabs v-model:active="activeRecordStatus" @change="handleRecordStatusChange">
```

### 2. 修复后端API参数接收
**文件**: `backend/app/api/v1/inventory.py`
**修改**: 在 `list_inventory_records` 函数中添加 `status` 参数

```python
@router.get("/tasks/{task_id}/records", response_model=InventoryRecordListResponse)
def list_inventory_records(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:record:view"])),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,  # 新增参数
) -> InventoryRecordListResponse:
    """获取盘点记录列表"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    filters = {}
    if keyword:
        filters["keyword"] = keyword
    if status:
        filters["status"] = status  # 传递状态参数

    # ... 其余代码保持不变
```

### 2. 优化前端默认状态
**文件**: `frontend/src/mobile/views/asset/InventoryTask.vue`
**修改**: 将默认筛选状态从"全部"改为"待盘点"

```typescript
// 修改前
const activeRecordStatus = ref<string>('all')

// 修改后
const activeRecordStatus = ref<string>('pending')
```

## 技术细节
### 后端筛选逻辑
CRUD层的筛选实现（已存在且正确）：
```python
def get_multi_by_task(self, db: Session, *, task_id: int, skip: int = 0, limit: int = 100, filters: Dict[str, Any] = None):
    query = db.query(self.model).options(joinedload(self.model.asset)).filter(self.model.task_id == task_id)
    
    if filters:
        for field, value in filters.items():
            if value is not None:
                if field == "status" and value:  # 状态筛选
                    query = query.filter(self.model.status == value)
    
    return query.offset(skip).limit(limit).all()
```

### 前端状态映射
```typescript
const recordStatusOptions = [
  { text: '正常', value: 'normal' },
  { text: '异常', value: 'abnormal' },
  { text: '缺失', value: 'missing' },
  { text: '信息变更', value: 'info_changed' }
]
```

## 验证方法
1. 启动后端服务
2. 打开移动端盘点任务页面
3. 切换到"盘点记录"标签页
4. 点击不同状态的筛选选项卡
5. 验证只显示对应状态的记录

## 相关文件
- `backend/app/api/v1/inventory.py` - 后端API接口
- `backend/app/crud/inventory.py` - 数据访问层
- `frontend/src/mobile/views/asset/InventoryTask.vue` - 移动端页面
- `frontend/src/api/inventory.ts` - 前端API客户端

## 修复结果
- ✅ 状态筛选功能正常工作
- ✅ 默认显示待盘点记录，符合用户预期
- ✅ 所有筛选选项都能正确过滤数据
- ✅ 搜索功能与状态筛选可同时使用

## 注意事项
- 此修复不影响其他功能
- 保持了向后兼容性
- 优化了用户体验（默认显示待盘点） 