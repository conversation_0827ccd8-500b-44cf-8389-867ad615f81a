# 移动端盘点任务页面样式和功能优化

## 问题描述
移动端盘点任务页面存在以下问题：
1. 搜索框旁边的选项下拉框没有和搜索框对齐
2. 资产信息显示拥挤，布局不协调
3. 分类功能失效，点击异常或其他状态都显示全部资产

## 解决方案

### 1. 修复分类筛选功能
**文件**: `frontend/src/mobile/views/asset/InventoryTask.vue`
**问题**: `handleRecordStatusChange`函数中缺少重置分页状态
**修复**: 添加分页重置逻辑
```javascript
const handleRecordStatusChange = () => {
  recordsPagination.current = 1
  recordsList.value = []
  recordsFinished.value = false
  loadRecords(true)
}
```

### 2. 优化搜索区域布局
**文件**: `frontend/src/mobile/components/business/SimpleSearch.vue`
**改进内容**:
- 统一容器高度为40px，使用`align-items: center`精确对齐
- 深度控制Vant组件内部元素，确保dropdown-menu和search完全对齐
- 统一圆角为8px，内边距为12px
- 添加box-sizing: border-box确保尺寸一致性
- 使用flex布局精确控制所有内部元素的垂直居中

### 3. 优化资产信息卡片样式
**文件**: `frontend/src/mobile/views/asset/InventoryTask.vue`
**改进内容**:
- 增加卡片内容间距(16px -> 12px)
- 优化信息项布局，增加最小宽度和换行处理
- 美化备注区域，添加背景色和左边框
- 调整按钮尺寸和间距
- 统一圆角为12px，增强视觉层次

### 4. 整体样式协调
- 统一颜色规范 (#323233, #646566, #969799)
- 标准化间距 (8px, 12px, 16px, 20px)
- 优化标签和按钮样式
- 增强搜索高亮效果

## 技术细节

### 样式优化要点
1. **搜索区域**: 使用flex布局，center对齐，统一高度40px，深度控制Vant组件内部元素
2. **卡片布局**: 增加内边距，优化信息层次，使用CSS Grid布局
3. **交互反馈**: 增强按钮和标签的视觉效果
4. **响应式**: 保持移动端友好的触摸区域大小，使用box-sizing确保尺寸一致性

### 功能修复要点
1. **状态筛选**: 确保切换状态时正确重置分页
2. **数据加载**: 保持loadRecords函数的参数传递正确
3. **用户体验**: 优化加载状态和空数据提示

## 测试验证
- [x] 分类筛选功能正常工作
- [x] 搜索框和下拉框对齐
- [x] 资产信息布局协调
- [x] 移动端触摸体验良好
- [x] 样式在不同设备上表现一致

## 影响范围
- 移动端盘点任务详情页面
- SimpleSearch通用搜索组件
- 不影响桌面端功能

## 后续优化建议
1. 考虑添加骨架屏提升加载体验
2. 优化长文本的截断和展开功能
3. 增加更多的状态指示器 