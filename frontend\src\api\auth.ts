import request from '@/utils/request'
import type { LoginData, TokenData } from '../types/auth'

export const authApi = {
  // 用户登录
  login: (data: LoginData) => {
    return request.post<TokenData>('/api/v1/auth/login', data)
  },

  // 测试认证
  testAuth: () => {
    return request.get('/api/v1/auth/test-auth')
  },

  // 刷新token
  refreshToken: () => {
    return request.post<TokenData>('/api/v1/auth/refresh-token')
  },

  // 登出
  logout: () => {
    return request.post('/api/v1/auth/logout')
  }
} 