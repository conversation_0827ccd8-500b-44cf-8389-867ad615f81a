from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.database import get_db
from app.api import deps
from app.crud import command_whitelist as crud
from app.schemas import command_whitelist as schemas
from app.models.command_whitelist import CommandCategory, CommandWhitelist
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


# 命令分类管理
@router.get("/categories", response_model=List[schemas.CommandCategoryResponse])
def get_command_categories(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = Query(None, description="是否启用"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """获取命令分类列表"""
    categories = crud.get_command_categories(db, skip=skip, limit=limit, is_active=is_active)
    return categories


@router.post("/categories", response_model=schemas.CommandCategoryResponse)
def create_command_category(
    category: schemas.CommandCategoryCreate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """创建命令分类"""
    # 检查名称是否已存在
    existing_category = crud.get_command_category_by_name(db, category.name)
    if existing_category:
        raise HTTPException(status_code=400, detail="分类名称已存在")
    
    return crud.create_command_category(db, category)


@router.get("/categories/{category_id}", response_model=schemas.CommandCategoryResponse)
def get_command_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """获取单个命令分类"""
    category = crud.get_command_category(db, category_id)
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return category


@router.put("/categories/{category_id}", response_model=schemas.CommandCategoryResponse)
def update_command_category(
    category_id: int,
    category_update: schemas.CommandCategoryUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """更新命令分类"""
    category = crud.update_command_category(db, category_id, category_update)
    if not category:
        raise HTTPException(status_code=404, detail="分类不存在")
    return category


@router.delete("/categories/{category_id}")
def delete_command_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """删除命令分类"""
    success = crud.delete_command_category(db, category_id)
    if not success:
        raise HTTPException(status_code=404, detail="分类不存在")
    return {"message": "分类删除成功"}


# 命令白名单管理
@router.get("/", response_model=List[schemas.CommandWhitelistResponse])
def get_command_whitelist(
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = Query(None, description="分类ID"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    security_level: Optional[str] = Query(None, description="安全级别"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """获取命令白名单列表"""
    commands = crud.get_command_whitelist(
        db, 
        skip=skip, 
        limit=limit,
        category_id=category_id,
        is_active=is_active,
        security_level=security_level,
        search=search
    )
    return commands


# 命令模板和验证 - 移到动态路由之前
@router.get("/templates", response_model=schemas.CommandTemplateResponse)
def get_command_templates(
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """获取命令模板（按分类组织）"""
    templates_dict = crud.get_command_templates(db)
    
    categories = []
    for category_name, commands in templates_dict.items():
        categories.append(schemas.CommandTemplate(
            category=category_name,
            commands=commands
        ))
    
    return schemas.CommandTemplateResponse(categories=categories)


@router.get("/user/available", response_model=List[schemas.CommandWhitelistResponse])
def get_user_available_commands(
    db: Session = Depends(get_db),
    current_user = Depends(deps.get_current_active_user)
):
    """获取当前用户可用的命令列表"""
    # 获取用户权限
    user_permissions = getattr(current_user, 'permissions', [])
    
    # 根据用户权限筛选可用命令
    available_commands = crud.get_user_available_commands(db, user_permissions)
    return available_commands


@router.post("/validate", response_model=schemas.CommandValidationResponse)
def validate_command(
    request: schemas.CommandValidationRequest,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:send"]))
):
    """验证命令是否安全和合规"""
    result = crud.validate_command(db, request.command)
    return schemas.CommandValidationResponse(**result)


@router.post("/", response_model=schemas.CommandWhitelistResponse)
def create_command_whitelist(
    command: schemas.CommandWhitelistCreate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """创建命令白名单项"""
    # 验证分类是否存在
    category = crud.get_command_category(db, command.category_id)
    if not category:
        raise HTTPException(status_code=400, detail="分类不存在")
    
    return crud.create_command_whitelist(db, command)


@router.get("/{command_id}", response_model=schemas.CommandWhitelistResponse)
def get_command_whitelist_item(
    command_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """获取单个命令白名单项"""
    command = crud.get_command_whitelist_item(db, command_id)
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")
    return command


@router.put("/{command_id}", response_model=schemas.CommandWhitelistResponse)
def update_command_whitelist(
    command_id: int,
    command_update: schemas.CommandWhitelistUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """更新命令白名单项"""
    command = crud.update_command_whitelist(db, command_id, command_update)
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")
    return command


@router.delete("/{command_id}")
def delete_command_whitelist(
    command_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """删除命令白名单项"""
    success = crud.delete_command_whitelist(db, command_id)
    if not success:
        raise HTTPException(status_code=404, detail="命令不存在")
    return {"message": "命令删除成功"}


# 批量操作
@router.post("/batch/import")
def batch_import_commands(
    commands_data: List[Dict[str, Any]],
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """批量导入命令"""
    created_count = 0
    errors = []
    
    for i, command_data in enumerate(commands_data):
        try:
            # 验证分类是否存在
            if "category_name" in command_data:
                category = crud.get_command_category_by_name(db, command_data["category_name"])
                if category:
                    command_data["category_id"] = category.id
                    del command_data["category_name"]
            
            command_create = schemas.CommandWhitelistCreate(**command_data)
            crud.create_command_whitelist(db, command_create)
            created_count += 1
            
        except Exception as e:
            errors.append(f"第{i+1}条记录错误: {str(e)}")
    
    return {
        "message": f"批量导入完成",
        "created_count": created_count,
        "total_count": len(commands_data),
        "errors": errors
    }


@router.post("/batch/default")
def initialize_default_commands(
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:manage"]))
):
    """初始化默认命令白名单"""
    
    # 默认分类和命令数据
    default_data = [
        {
            "category": {
                "name": "系统信息",
                "description": "系统信息查询命令",
                "required_permission": "terminal:command:basic"
            },
            "commands": [
                {
                    "name": "系统信息查询",
                    "command": "systeminfo",
                    "description": "显示计算机和操作系统的配置信息",
                    "example": "systeminfo",
                    "timeout": 30,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                },
                {
                    "name": "当前用户信息",
                    "command": "whoami",
                    "description": "显示当前登录用户的用户名",
                    "example": "whoami",
                    "timeout": 10,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                },
                {
                    "name": "主机名查询",
                    "command": "hostname",
                    "description": "显示计算机的主机名",
                    "example": "hostname",
                    "timeout": 10,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                }
            ]
        },
        {
            "category": {
                "name": "网络信息",
                "description": "网络配置和连接信息查询",
                "required_permission": "terminal:command:basic"
            },
            "commands": [
                {
                    "name": "IP配置查询",
                    "command": "ipconfig",
                    "description": "显示网络适配器的IP配置信息",
                    "example": "ipconfig /all",
                    "timeout": 20,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                },
                {
                    "name": "网络连通性测试",
                    "command": "ping",
                    "description": "测试与目标主机的网络连通性",
                    "example": "ping *******",
                    "timeout": 30,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                },
                {
                    "name": "DNS解析查询",
                    "command": "nslookup",
                    "description": "查询DNS记录信息",
                    "example": "nslookup google.com",
                    "timeout": 30,
                    "admin_required": False,
                    "security_level": "PUBLIC"
                }
            ]
        },
        {
            "category": {
                "name": "进程服务",
                "description": "进程和服务管理命令",
                "required_permission": "terminal:command:operator"
            },
            "commands": [
                {
                    "name": "进程列表",
                    "command": "tasklist",
                    "description": "显示正在运行的进程列表",
                    "example": "tasklist",
                    "timeout": 30,
                    "admin_required": False,
                    "security_level": "OPERATOR"
                },
                {
                    "name": "服务查询",
                    "command": "sc query",
                    "description": "查询Windows服务状态",
                    "example": "sc query",
                    "timeout": 30,
                    "admin_required": False,
                    "security_level": "OPERATOR"
                }
            ]
        }
    ]
    
    created_categories = 0
    created_commands = 0
    
    for category_data in default_data:
        # 创建或获取分类
        existing_category = crud.get_command_category_by_name(db, category_data["category"]["name"])
        if not existing_category:
            category_create = schemas.CommandCategoryCreate(**category_data["category"])
            category = crud.create_command_category(db, category_create)
            created_categories += 1
        else:
            category = existing_category
        
        # 创建命令
        for command_data in category_data["commands"]:
            command_data["category_id"] = category.id
            command_create = schemas.CommandWhitelistCreate(**command_data)
            crud.create_command_whitelist(db, command_create)
            created_commands += 1
    
    return {
        "message": "默认命令初始化完成",
        "created_categories": created_categories,
        "created_commands": created_commands
    } 