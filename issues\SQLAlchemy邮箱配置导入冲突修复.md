# SQLAlchemy 邮箱配置导入冲突修复

## 问题描述

在邮箱配置API中出现SQLAlchemy错误：
```
sqlalchemy.exc.ArgumentError: Column expression, FROM clause, or other columns clause element expected, got <class 'app.schemas.email.EmailConfig'>.
```

错误发生在 `backend/app/api/v1/email.py` 第109行：
```python
existing = db.query(EmailConfig).filter(EmailConfig.app_name == config_in.app_name).first()
```

## 问题原因

在 `email.py` 文件中同时导入了Schema和Model的 `EmailConfig` 类，导致名称冲突：

```python
from app.schemas.email import EmailConfig  # Schema类
```

数据库查询时误用了Schema类而非Model类。

## 解决方案

**修改导入语句**：
- 将Model类导入为 `EmailConfigModel`
- 保持Schema类名称为 `EmailConfig`

**修改前**：
```python
from app.schemas.email import EmailConfig, ...
```

**修改后**：
```python
from app.models.email import EmailConfig as EmailConfigModel
from app.schemas.email import EmailConfig, ...
```

**修改数据库查询**：
```python
# 修改前
existing = db.query(EmailConfig).filter(EmailConfig.app_name == config_in.app_name).first()

# 修改后
existing = db.query(EmailConfigModel).filter(EmailConfigModel.app_name == config_in.app_name).first()
```

## 修复文件

- `backend/app/api/v1/email.py`

## 验证

- ✅ 语法检查通过：`python -m py_compile app/api/v1/email.py`
- ✅ 导入语句正确分离Schema和Model类
- ✅ 数据库查询使用正确的Model类

## 总结

通过使用别名导入解决了Schema和Model类的命名冲突，确保数据库操作使用正确的SQLAlchemy模型类。 