from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from fastapi.responses import StreamingResponse
from urllib.parse import quote
import pandas as pd
import io
import os
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from app.api.deps import get_db, check_permissions
from app.crud.inventory import inventory_task_crud, inventory_record_crud
from app.crud.asset import asset_crud
from app.crud.custom_field import custom_field_crud, inventory_record_custom_field_value_crud
from app.schemas.inventory import (
    InventoryTaskCreate,
    InventoryTaskUpdate,
    InventoryTaskResponse,
    InventoryTaskListResponse,
    InventoryRecordCreate,
    InventoryRecordUpdate,
    InventoryRecordResponse,
    InventoryRecordListResponse
)

router = APIRouter()

@router.post("/tasks", response_model=InventoryTaskResponse)
def create_inventory_task(
    task: InventoryTaskCreate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:add"])),
) -> InventoryTaskResponse:
    """创建盘点任务"""
    # 验证日期
    if task.start_date > task.end_date:
        raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")
    if task.start_date < datetime.now().replace(hour=0, minute=0, second=0, microsecond=0):
        raise HTTPException(status_code=400, detail="开始日期不能早于今天")

    # 创建任务（不再预创建盘点记录，采用动态查询模式）
    db_task = inventory_task_crud.create(db, obj_in=task)

    return db_task

@router.get("/tasks", response_model=InventoryTaskListResponse)
def list_inventory_tasks(
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"])),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
    start_date_start: Optional[str] = None,
    start_date_end: Optional[str] = None,
    end_date_start: Optional[str] = None,
    end_date_end: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> InventoryTaskListResponse:
    """获取盘点任务列表"""
    filters = {}
    if keyword:
        filters["keyword"] = keyword
    if status:
        filters["status"] = status
    if start_date_start:
        filters["start_date_start"] = start_date_start
    if start_date_end:
        filters["start_date_end"] = start_date_end
    if end_date_start:
        filters["end_date_start"] = end_date_start
    if end_date_end:
        filters["end_date_end"] = end_date_end

    total = inventory_task_crud.get_count(db, filters=filters)
    items = inventory_task_crud.get_multi(
        db,
        skip=skip,
        limit=limit,
        filters=filters,
        sort_by=sort_by,
        sort_order=sort_order
    )

    return {
        "data": items,
        "total": total
    }

@router.get("/tasks/{task_id}", response_model=InventoryTaskResponse)
def get_inventory_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"])),
) -> InventoryTaskResponse:
    """获取盘点任务详情"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.put("/tasks/{task_id}", response_model=InventoryTaskResponse)
def update_inventory_task(
    task_id: int,
    task_update: InventoryTaskUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:edit"])),
) -> InventoryTaskResponse:
    """更新盘点任务"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 验证日期
    start_date = task_update.start_date or task.start_date
    end_date = task_update.end_date or task.end_date
    if start_date > end_date:
        raise HTTPException(status_code=400, detail="开始日期不能晚于结束日期")

    return inventory_task_crud.update(db, db_obj=task, obj_in=task_update)

@router.delete("/tasks/{task_id}")
def delete_inventory_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:delete"])),
) -> dict:
    """删除盘点任务"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    inventory_task_crud.remove(db, id=task_id)
    return {"message": "Task deleted successfully"}

@router.get("/tasks/{task_id}/records", response_model=InventoryRecordListResponse)
def list_inventory_records(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:record:view"])),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    status: Optional[str] = None,
) -> InventoryRecordListResponse:
    """获取盘点记录列表"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    filters = {}
    if keyword:
        filters["keyword"] = keyword
    if status:
        filters["status"] = status

    records = inventory_record_crud.get_multi_by_task(
        db,
        task_id=task_id,
        skip=skip,
        limit=limit,
        filters=filters
    )

    total = inventory_record_crud.get_count_by_task(
        db,
        task_id=task_id,
        filters=filters
    )

    return InventoryRecordListResponse(
        data=records,
        total=total
    )

@router.put("/records/{record_id}", response_model=InventoryRecordResponse)
def update_inventory_record(
    record_id: int,
    record_update: InventoryRecordUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:record:edit"])),
) -> InventoryRecordResponse:
    """更新盘点记录"""
    record = inventory_record_crud.get(db, id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="Record not found")

    # 检查任务状态
    task = inventory_task_crud.get(db, id=record.task_id)
    if task.status == "completed":
        raise HTTPException(status_code=400, detail="已完成的盘点任务不能修改")

    return inventory_record_crud.update(db, db_obj=record, obj_in=record_update)

@router.put("/tasks/{task_id}/records/{asset_id}", response_model=InventoryRecordResponse)
def update_inventory_record_by_asset(
    task_id: int,
    asset_id: int,
    record_update: InventoryRecordUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:record:edit"])),
) -> InventoryRecordResponse:
    """通过任务ID和资产ID更新盘点记录（支持虚拟记录）"""
    # 检查任务状态
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    if task.status == "completed":
        raise HTTPException(status_code=400, detail="已完成的盘点任务不能修改")

    # 检查资产是否存在
    asset = asset_crud.get(db, id=asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")

    return inventory_record_crud.update_or_create_record(
        db, task_id=task_id, asset_id=asset_id, obj_in=record_update
    )

@router.get("/tasks/{task_id}/statistics")
def get_inventory_task_statistics(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"])),
) -> dict:
    """获取盘点任务统计信息"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取所有盘点记录进行统计（不分页）
    all_records = inventory_record_crud.get_multi_by_task(db, task_id=task_id, skip=0, limit=1000000)
    
    # 统计各种状态的数量
    total = len(all_records)
    pending = len([r for r in all_records if r.status == "pending"])
    normal = len([r for r in all_records if r.status == "normal"])
    abnormal = len([r for r in all_records if r.status == "abnormal"])
    missing = len([r for r in all_records if r.status == "missing"])
    info_changed = len([r for r in all_records if r.status == "info_changed"])
    checked = total - pending  # 已盘点 = 总数 - 待盘点

    return {
        "total": total,
        "pending": pending,
        "normal": normal,
        "abnormal": abnormal,
        "missing": missing,
        "info_changed": info_changed,
        "checked": checked
    }

@router.post("/tasks/{task_id}/complete")
def complete_inventory_task(
    task_id: int,
    request: dict = Body(default={}),
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:edit"])),
) -> dict:
    """完成盘点任务"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查是否所有记录都已盘点（包括动态查询的虚拟记录）
    records = inventory_record_crud.get_multi_by_task(db, task_id=task_id, skip=0, limit=1000000)
    pending_records = [r for r in records if r.status == "pending"]
    if pending_records:
        raise HTTPException(
            status_code=400, 
            detail=f"还有 {len(pending_records)} 个未盘点的资产，请完成所有资产盘点后再提交"
        )

    # 检查是否有信息变更记录
    info_change_records = inventory_record_crud.get_info_changes_by_task(db, task_id=task_id)
    auto_apply_changes = request.get("auto_apply_changes", False)
    
    if info_change_records and not auto_apply_changes:
        # 如果有信息变更且用户没有选择自动应用，返回提示信息
        return {
            "status": "pending_changes",
            "message": f"发现 {len(info_change_records)} 个资产信息变更记录，请先处理这些变更或选择自动应用",
            "changes_count": len(info_change_records),
            "info_changes_url": f"/api/v1/inventory/tasks/{task_id}/info-changes"
        }
    
    # 如果用户选择自动应用变更，则先应用变更
    if info_change_records and auto_apply_changes:
        apply_result = inventory_record_crud.apply_info_changes_to_assets(db, task_id=task_id)
        if apply_result["failed_count"] > 0:
            raise HTTPException(
                status_code=400,
                detail=f"应用信息变更时发生错误：{apply_result['failed_count']} 个记录失败"
            )

    # 更新任务状态
    inventory_task_crud.update(
        db,
        db_obj=task,
        obj_in={"status": "completed"}
    )

    response = {"message": "Task completed successfully"}
    
    # 如果自动应用了变更，返回应用结果
    if info_change_records and auto_apply_changes:
        response["applied_changes"] = apply_result["applied_count"]
    
    return response

@router.get("/tasks/{task_id}/export")
async def export_inventory_task(
    task_id: int,
    format: str = Query("xlsx", regex="^(csv|xlsx|excel)$"),
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:export"])),
):
    """导出盘点任务数据（包含自定义字段和图片）"""
    try:
        # 统一格式参数处理
        if format == 'excel':
            format = 'xlsx'
        
        # 检查任务是否存在
        task = inventory_task_crud.get(db, id=task_id)
        if not task:
            raise HTTPException(status_code=404, detail="盘点任务不存在")

        # 获取所有盘点记录
        records = inventory_record_crud.get_multi_by_task(db, task_id=task_id, skip=0, limit=1000000)
        if not records:
            raise HTTPException(status_code=400, detail="没有盘点记录可导出")

        # 获取盘点相关的自定义字段
        custom_fields = custom_field_crud.get_active_fields(db, applies_to="inventory")

        # 构建导出数据
        export_data = []
        for record in records:
            # 基础盘点记录数据
            row_data = {
                '任务名称': task.name,
                '资产编号': record.asset.asset_number if record.asset else '',
                '资产名称': record.asset.name if record.asset else '',
                '资产类别': record.asset.category if record.asset else '',
                '规格型号': record.asset.specification if record.asset else '',
                '原领用人': record.asset.custodian if record.asset else '',
                '原使用人': record.asset.user if record.asset else '',
                '原存放位置': record.asset.location if record.asset else '',
                '盘点状态': get_status_text(record.status),
                '盘点备注': record.remarks or '',
                '盘点人': record.checked_by or '',
                '盘点时间': record.checked_at.strftime('%Y-%m-%d %H:%M:%S') if record.checked_at else '',
            }

            # 添加变更信息字段
            if record.status == 'info_changed':
                row_data.update({
                    '新资产名称': record.new_name or '',
                    '新规格型号': record.new_specification or '',
                    '新资产状态': record.new_status or '',
                    '新领用人': record.new_custodian or '',
                    '新使用人': record.new_user or '',
                    '新公司': record.new_company or '',
                    '新存放位置': record.new_location or '',
                    '新生产编号': record.new_production_number or '',
                    '新价格': record.new_price or '',
                    '新供应商': record.new_supplier or '',
                    '新制造商': record.new_manufacturer or '',
                    '新采购人': record.new_purchaser or '',
                })

            # 获取自定义字段值
            if record.id:
                # 实际盘点记录
                custom_field_values = inventory_record_custom_field_value_crud.get_by_record(
                    db, inventory_record_id=record.id
                )
            else:
                # 虚拟记录（未盘点状态）
                custom_field_values = inventory_record_custom_field_value_crud.get_by_record(
                    db, task_id=task_id, asset_id=record.asset_id
                )

            # 添加自定义字段数据
            custom_field_dict = {cfv.custom_field.name: cfv.value for cfv in custom_field_values if cfv.custom_field}
            for field in custom_fields:
                field_value = custom_field_dict.get(field.name, '')
                
                # 处理图片字段
                if field.field_type == 'file' and field_value:
                    if format == 'csv':
                        # CSV格式提供完整URL
                        if not field_value.startswith('http'):
                            # 构建完整的文件URL  
                            base_url = "http://localhost:8000"  # 可以从配置读取
                            field_value = base_url + field_value
                
                row_data[field.label] = field_value

            export_data.append(row_data)

        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        # 创建内存文件对象
        output = io.BytesIO()
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_ext = 'xlsx' if format == 'xlsx' else 'csv'
        filename_display = f'盘点任务_{task.name}_{timestamp}.{file_ext}'
        
        if format == 'csv':
            # 导出CSV - 直接写入BytesIO，避免编码问题
            csv_content = df.to_csv(index=False)
            output.write(csv_content.encode('utf-8-sig'))
            media_type = 'text/csv; charset=utf-8'
        else:
            # 导出Excel（简化版，不嵌入图片）
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='盘点记录')
                worksheet = writer.sheets['盘点记录']
                
                # 设置样式
                header_font = Font(name='微软雅黑', size=11, bold=True)
                header_fill = PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                thin_border = Border(
                    left=Side(style='thin', color='D9D9D9'),
                    right=Side(style='thin', color='D9D9D9'),
                    top=Side(style='thin', color='D9D9D9'),
                    bottom=Side(style='thin', color='D9D9D9')
                )
                
                # 应用表头样式
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = thin_border
                
                # 设置列宽
                for i, column in enumerate(df.columns):
                    col_letter = get_column_letter(i + 1)
                    max_length = max(
                        len(str(column)),
                        df[column].astype(str).str.len().max() if not df.empty else 0
                    )
                    worksheet.column_dimensions[col_letter].width = min(max_length + 2, 50)
            
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        
        output.seek(0)
        
        # 编码文件名，确保中文文件名正确处理
        encoded_filename = quote(filename_display, safe='')
        
        return StreamingResponse(
            io.BytesIO(output.getvalue()),
            media_type=media_type,
            headers={
                'Content-Disposition': f'attachment; filename*=utf-8\'\'{encoded_filename}',
                'Access-Control-Expose-Headers': 'Content-Disposition'
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        print(f"导出盘点任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.get("/tasks/{task_id}/info-changes")
def get_inventory_info_changes(
    task_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"])),
) -> dict:
    """获取盘点任务中的信息变更预览"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取所有信息变更记录
    change_records = inventory_record_crud.get_info_changes_by_task(db, task_id=task_id)
    
    changes = []
    for record in change_records:
        change_info = {
            "record_id": record.id,
            "asset_id": record.asset_id,
            "asset_number": record.asset.asset_number if record.asset else "",
            "asset_name": record.asset.name if record.asset else "",
            "checked_by": record.checked_by,
            "checked_at": record.checked_at,
            "changes": {}
        }
        
        # 字段映射和显示名称
        field_mappings = {
            "new_name": {"field": "name", "label": "资产名称"},
            "new_specification": {"field": "specification", "label": "规格型号"},
            "new_status": {"field": "status", "label": "资产状态"},
            "new_custodian": {"field": "custodian", "label": "领用人"},
            "new_custodian_department": {"field": "custodian_department", "label": "领用人部门"},
            "new_user": {"field": "user", "label": "使用人"},
            "new_user_department": {"field": "user_department", "label": "使用人部门"},
            "new_location": {"field": "location", "label": "存放位置"},
            "new_company": {"field": "company", "label": "公司"},
            "new_remarks": {"field": "remarks", "label": "备注"},
            "new_production_number": {"field": "production_number", "label": "生产编号"},
            "new_price": {"field": "price", "label": "价格"},
            "new_supplier": {"field": "supplier", "label": "供应商"},
            "new_manufacturer": {"field": "manufacturer", "label": "制造商"},
            "new_purchaser": {"field": "purchaser", "label": "采购人"}
        }
        
        # 检查每个字段的变更
        for new_field, mapping in field_mappings.items():
            new_value = getattr(record, new_field, None)
            if new_value:  # 有新值的字段
                old_value = getattr(record.asset, mapping["field"], None) if record.asset else None
                change_info["changes"][mapping["field"]] = {
                    "label": mapping["label"],
                    "old_value": old_value,
                    "new_value": new_value
                }
        
        if change_info["changes"]:  # 只包含有实际变更的记录
            changes.append(change_info)
    
    return {
        "task_id": task_id,
        "task_name": task.name,
        "total_changes": len(changes),
        "changes": changes
    }

@router.post("/tasks/{task_id}/apply-info-changes")
def apply_inventory_info_changes(
    task_id: int,
    request: dict = Body(...),
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:edit"])),
) -> dict:
    """应用盘点任务中的信息变更到资产表"""
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取要应用的记录ID列表（可选）
    selected_record_ids = request.get("record_ids", None)
    
    # 应用变更
    result = inventory_record_crud.apply_info_changes_to_assets(
        db, 
        task_id=task_id, 
        selected_record_ids=selected_record_ids
    )
    
    return {
        "message": "信息变更应用完成",
        "applied_count": result["applied_count"],
        "failed_count": result["failed_count"],
        "failed_records": result["failed_records"]
    }

def get_status_text(status: str) -> str:
    """获取盘点状态的中文文本"""
    status_map = {
        'pending': '待盘点',
        'normal': '正常',
        'abnormal': '异常',
        'missing': '缺失',
        'info_changed': '信息变更'
    }
    return status_map.get(status, status)