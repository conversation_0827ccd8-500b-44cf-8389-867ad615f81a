<template>
  <div class="terminal-detail">
    <div class="page-header">
      <div class="header-left">
        <el-button class="back-button" @click="goBack" type="primary" text>
          <el-icon><Back /></el-icon>
          返回列表
        </el-button>
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/terminal/list' }">终端列表</el-breadcrumb-item>
          <el-breadcrumb-item>终端详情</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <Authority permission="terminal:command:send">
          <el-button v-if="terminalDetail.online_status" type="primary" size="small" @click="sendCollectInfoCommand">
            <el-icon><Refresh /></el-icon> 刷新信息
          </el-button>
        </Authority>
      </div>
    </div>

    <div class="terminal-header-card">
      <div class="terminal-info-main">
        <div class="terminal-icon">
          <el-avatar :size="64" :icon="Monitor" class="terminal-avatar" />
        </div>
        <div class="terminal-info-content">
          <div class="terminal-title">
            <h2 class="hostname">{{ terminalDetail.hostname || '加载中...' }}</h2>
            <div class="status-badges">
              <el-tag v-if="terminalDetail.online_status" type="success" effect="dark" class="status-tag">
                <el-icon><Connection /></el-icon> 在线
              </el-tag>
              <el-tag v-else type="danger" effect="dark" class="status-tag">
                <el-icon><Mute /></el-icon> 离线
              </el-tag>
              <el-tag v-if="terminalDetail.agent_version" type="info" effect="plain" class="version-tag">
                <el-icon><Box /></el-icon> v{{ terminalDetail.agent_version }}
              </el-tag>
            </div>
          </div>
          <div class="terminal-meta">
            <div class="meta-item" v-if="terminalDetail.ip_address">
              <el-icon><Connection /></el-icon>
              <span>{{ terminalDetail.ip_address }}</span>
            </div>
            <div class="meta-item" v-if="terminalDetail.mac_address">
              <el-icon><Monitor /></el-icon>
              <span>{{ terminalDetail.mac_address }}</span>
            </div>
            <div class="meta-item" v-if="terminalDetail.last_heartbeat_time">
              <el-icon><Timer /></el-icon>
              <span>最后心跳: {{ getTimeAgo(terminalDetail.last_heartbeat_time) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="terminal-os-info" v-if="terminalDetail.os_info">
        <div class="os-icon-container">
          <el-icon class="os-logo"><component :is="getOsIcon(terminalDetail.os_info.name)" /></el-icon>
        </div>
        <div class="os-details">
          <div class="os-name">{{ terminalDetail.os_info?.name || '-' }}</div>
          <div class="os-version">{{ terminalDetail.os_info?.version || '-' }}</div>
        </div>
      </div>
    </div>

    <div v-loading="loading" class="terminal-content">
      <el-tabs v-model="activeTab" class="detail-tabs" type="card" @tab-change="handleTabChange">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <div class="info-summary">
            <div class="summary-cards">
              <div class="summary-card system-card">
                <div class="card-header">
                  <el-icon class="card-icon"><Platform /></el-icon>
                  <span>系统信息</span>
                </div>
                <div class="card-content">
                  <div class="info-item">
                    <span class="label">操作系统:</span>
                    <span class="value">
                      {{ terminalDetail.os_info?.name || '-' }} {{ terminalDetail.os_info?.version ? `(${terminalDetail.os_info.version})` : '' }}
                    </span>
                  </div>
                  <div class="info-item">
                    <span class="label">架构:</span>
                    <span class="value">{{ terminalDetail.os_info?.architecture || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">安装日期:</span>
                    <span class="value">{{ terminalDetail.os_info?.install_date || '-' }}</span>
                  </div>
                </div>
              </div>

              <div class="summary-card hardware-card">
                <div class="card-header">
                  <el-icon class="card-icon"><Cpu /></el-icon>
                  <span>硬件信息</span>
                </div>
                <div class="card-content">
                  <div class="info-item">
                    <span class="label">CPU:</span>
                    <span class="value">{{ terminalDetail.hardware_info?.cpu_model || '-' }} ({{ terminalDetail.hardware_info?.cpu_cores || '0' }}核)</span>
                  </div>
                  <div class="info-item">
                    <span class="label">内存:</span>
                    <span class="value">{{ formatSize(terminalDetail.hardware_info?.memory_total, 'memory') }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">制造商:</span>
                    <span class="value">{{ terminalDetail.hardware_info?.manufacturer || '-' }}</span>
                  </div>
                </div>
              </div>

              <div class="summary-card network-card">
                <div class="card-header">
                  <el-icon class="card-icon"><Connection /></el-icon>
                  <span>网络信息</span>
                </div>
                <div class="card-content">
                  <div class="info-item">
                    <span class="label">IP地址:</span>
                    <span class="value">{{ terminalDetail.ip_address || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">MAC地址:</span>
                    <span class="value">{{ terminalDetail.mac_address || '-' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">网关:</span>
                    <span class="value">{{ terminalDetail.network_info?.default_gateway || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="section-divider">
            <div class="divider-title">
              <el-icon><InfoFilled /></el-icon>
              <span>详细信息</span>
            </div>
          </div>

          <!-- 基本信息 -->
          <el-collapse accordion class="detail-collapse">
            <el-collapse-item name="1">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Document /></el-icon>
                  <span>基本信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" class="custom-descriptions">
                <el-descriptions-item label="主机名">
                  <span class="important-text">{{ terminalDetail.hostname }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="唯一标识">
                  <el-tooltip :content="terminalDetail.unique_id" placement="top">
                    <span class="ellipsis-text">{{ terminalDetail.unique_id }}</span>
                  </el-tooltip>
                </el-descriptions-item>
                <el-descriptions-item label="IP地址">
                  <el-tag size="small" effect="plain">{{ terminalDetail.ip_address || '-' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="MAC地址">
                  <el-tag size="small" effect="plain">{{ terminalDetail.mac_address }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="Agent版本">
                  <el-tag type="info" size="small" effect="dark">{{ terminalDetail.agent_version || '-' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="在线状态">
                  <el-tag :type="terminalDetail.online_status ? 'success' : 'danger'" size="small" effect="dark">
                    <el-icon v-if="terminalDetail.online_status"><Connection /></el-icon>
                    <el-icon v-else><Mute /></el-icon>
                    {{ terminalDetail.online_status ? '在线' : '离线' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="最后心跳时间">
                  <div class="time-info">
                    <span>{{ terminalDetail.last_heartbeat_time ? formatTime(terminalDetail.last_heartbeat_time) : '-' }}</span>
                    <el-tag v-if="terminalDetail.last_heartbeat_time" size="small" type="info" effect="plain" class="time-ago">
                      {{ getTimeAgo(terminalDetail.last_heartbeat_time) }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="最后在线时间">
                  <div class="time-info">
                    <span>{{ terminalDetail.last_online_time ? formatTime(terminalDetail.last_online_time) : '-' }}</span>
                    <el-tag v-if="terminalDetail.last_online_time" size="small" type="info" effect="plain" class="time-ago">
                      {{ getTimeAgo(terminalDetail.last_online_time) }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="最后离线时间">
                  <div class="time-info">
                    <span>{{ terminalDetail.last_offline_time ? formatTime(terminalDetail.last_offline_time) : '-' }}</span>
                    <el-tag v-if="terminalDetail.last_offline_time" size="small" type="info" effect="plain" class="time-ago">
                      {{ getTimeAgo(terminalDetail.last_offline_time) }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="注册时间">
                  <div class="time-info">
                    <span>{{ formatTime(terminalDetail.registration_time) }}</span>
                    <el-tag v-if="terminalDetail.registration_time" size="small" type="info" effect="plain" class="time-ago">
                      {{ getTimeAgo(terminalDetail.registration_time) }}
                    </el-tag>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="心跳间隔">
                  <el-tag type="success" size="small" effect="plain">{{ terminalDetail.heartbeat_interval }}秒</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="信息采集间隔">
                  <el-tag type="success" size="small" effect="plain">{{ terminalDetail.collection_interval }}秒</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 操作系统信息 -->
            <el-collapse-item v-if="terminalDetail.os_info" name="2">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Platform /></el-icon>
                  <span>操作系统信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" class="custom-descriptions">
                <el-descriptions-item label="系统名称">
                  <div class="os-item">
                    <el-icon class="os-icon">
                      <component :is="getOsIcon(terminalDetail.os_info.name)" />
                    </el-icon>
                    <span>{{ terminalDetail.os_info.name || '-' }}</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="系统版本">
                  <el-tag size="small" effect="plain">{{ terminalDetail.os_info.version || '-' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="构建版本">{{ terminalDetail.os_info.build || '-' }}</el-descriptions-item>
                <el-descriptions-item label="系统架构">{{ terminalDetail.os_info.architecture || '-' }}</el-descriptions-item>
                <el-descriptions-item label="安装日期">{{ terminalDetail.os_info.install_date || '-' }}</el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  {{ terminalDetail.os_info.update_time ? formatTime(terminalDetail.os_info.update_time) : '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 安全信息 -->
            <el-collapse-item v-if="terminalDetail.os_info?.security_info" name="3">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Lock /></el-icon>
                  <span>安全信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" class="custom-descriptions">
                <el-descriptions-item label="防火墙状态">
                  <div class="security-status">
                    <el-tag :type="terminalDetail.os_info.security_info.firewall_enabled ? 'success' : 'danger'" size="small" effect="dark">
                      <el-icon v-if="terminalDetail.os_info.security_info.firewall_enabled"><SuccessFilled /></el-icon>
                      <el-icon v-else><CloseBold /></el-icon>
                      {{ terminalDetail.os_info.security_info.firewall_enabled ? '已启用' : '未启用' }}
                    </el-tag>
                    <div v-if="!terminalDetail.os_info.security_info.firewall_enabled" class="security-warning">
                      <el-icon><Warning /></el-icon>
                      <span>建议启用防火墙以增强系统安全性</span>
                    </div>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="杀毒软件">
                  <span>{{ terminalDetail.os_info.security_info.antivirus || '-' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="杀毒软件状态">
                  <div class="security-status">
                    <el-tag :type="terminalDetail.os_info.security_info.antivirus_enabled ? 'success' : 'danger'" size="small" effect="dark">
                      <el-icon v-if="terminalDetail.os_info.security_info.antivirus_enabled"><SuccessFilled /></el-icon>
                      <el-icon v-else><CloseBold /></el-icon>
                      {{ terminalDetail.os_info.security_info.antivirus_enabled ? '已启用' : '未启用' }}
                    </el-tag>
                    <div v-if="!terminalDetail.os_info.security_info.antivirus_enabled && terminalDetail.os_info.security_info.antivirus" class="security-warning">
                      <el-icon><Warning /></el-icon>
                      <span>建议启用杀毒软件以增强系统安全性</span>
                    </div>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 硬件信息 -->
            <el-collapse-item v-if="terminalDetail.hardware_info" name="4">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Cpu /></el-icon>
                  <span>硬件信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" class="custom-descriptions">
                <el-descriptions-item label="CPU型号">{{ terminalDetail.hardware_info.cpu_model || '-' }}</el-descriptions-item>
                <el-descriptions-item label="CPU核心数">
                  <el-tag size="small" type="info" effect="plain">{{ terminalDetail.hardware_info.cpu_cores || '-' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="内存总量">
                  <el-tag size="small" type="success" effect="plain">{{ formatSize(terminalDetail.hardware_info.memory_total, 'memory') }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="序列号">{{ terminalDetail.hardware_info.serial_number || '-' }}</el-descriptions-item>
                <el-descriptions-item label="制造商">{{ terminalDetail.hardware_info.manufacturer || '-' }}</el-descriptions-item>
                <el-descriptions-item label="型号">{{ terminalDetail.hardware_info.model || '-' }}</el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  {{ terminalDetail.hardware_info.update_time ? formatTime(terminalDetail.hardware_info.update_time) : '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>

            <!-- 磁盘信息 -->
            <el-collapse-item v-if="terminalDetail.hardware_info?.disks && terminalDetail.hardware_info.disks.length > 0" name="5">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Box /></el-icon>
                  <span>磁盘信息</span>
                </div>
              </template>
              <div class="disk-cards">
                <el-card v-for="disk in terminalDetail.hardware_info.disks" :key="disk.name" class="disk-card" shadow="hover">
                  <template #header>
                    <div class="disk-header">
                      <span class="disk-name">{{ disk.name }}</span>
                      <span class="disk-type">{{ disk.filesystem || '未知文件系统' }}</span>
                    </div>
                  </template>
                  <div class="disk-info">
                    <div class="disk-size-info">
                      <div class="disk-total">
                        <span class="info-label">总容量:</span>
                        <span class="info-value">{{ formatSize(disk.total_space, 'disk') }}</span>
                      </div>
                      <div class="disk-free">
                        <span class="info-label">可用容量:</span>
                        <span class="info-value">{{ formatSize(disk.free_space, 'disk') }}</span>
                      </div>
                    </div>
                    <div class="disk-usage-container">
                      <el-progress
                        :percentage="calculateUsagePercentage(disk)"
                        :color="getDiskProgressColor(disk)"
                        :format="() => calculateUsagePercentage(disk) + '%'"
                        stripe
                      />
                    </div>
                    <div class="disk-mount" v-if="disk.mount_point">
                      <span class="info-label">挂载点:</span>
                      <el-tag size="small" effect="plain">{{ disk.mount_point }}</el-tag>
                    </div>
                  </div>
                </el-card>
              </div>
            </el-collapse-item>

            <!-- 网络信息 -->
            <el-collapse-item v-if="terminalDetail.network_info" name="6">
              <template #title>
                <div class="collapse-header">
                  <el-icon class="collapse-icon"><Connection /></el-icon>
                  <span>网络信息</span>
                </div>
              </template>
              <el-descriptions :column="2" border size="small" class="custom-descriptions">
                <el-descriptions-item label="主机名">{{ terminalDetail.network_info.hostname || '-' }}</el-descriptions-item>
                <el-descriptions-item label="域">{{ terminalDetail.network_info.domain || '-' }}</el-descriptions-item>
                <el-descriptions-item label="默认网关">
                  <el-tag size="small" effect="plain">{{ terminalDetail.network_info.default_gateway || '-' }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="DNS服务器">
                  <div class="dns-servers">
                    <el-tag
                      v-for="(dns, index) in terminalDetail.network_info.dns_servers"
                      :key="index"
                      size="small"
                      effect="plain"
                      class="dns-tag"
                    >
                      {{ dns }}
                    </el-tag>
                    <span v-if="!terminalDetail.network_info.dns_servers || terminalDetail.network_info.dns_servers.length === 0">-</span>
                  </div>
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  {{ terminalDetail.network_info.update_time ? formatTime(terminalDetail.network_info.update_time) : '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>

          <!-- 网络接口 -->
          <div v-if="terminalDetail.network_info?.interfaces && terminalDetail.network_info.interfaces.length > 0" class="network-interfaces">
            <div class="section-header">
              <el-icon class="section-icon"><Monitor /></el-icon>
              <span>网络接口</span>
            </div>
            <el-table
              :data="terminalDetail.network_info.interfaces"
              border
              stripe
              style="width: 100%"
              class="custom-table"
            >
              <el-table-column prop="name" label="接口名称">
                <template #default="{ row }">
                  <div class="network-interface-name">
                    <el-icon :class="{ 'connected-icon': row.is_connected }"><Monitor /></el-icon>
                    <span>{{ row.name }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="mac_address" label="MAC地址">
                <template #default="{ row }">
                  <el-tag size="small" effect="plain">{{ row.mac_address }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="ip_address" label="IP地址">
                <template #default="{ row }">
                  <el-tag size="small" effect="plain" type="success">{{ row.ip_address }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="subnet_mask" label="子网掩码" />
              <el-table-column prop="dhcp_enabled" label="DHCP">
                <template #default="{ row }">
                  <el-tag :type="row.dhcp_enabled ? 'success' : 'info'" size="small">
                    {{ row.dhcp_enabled ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="is_connected" label="连接状态">
                <template #default="{ row }">
                  <el-tag :type="row.is_connected ? 'success' : 'danger'" size="small" effect="dark">
                    <el-icon v-if="row.is_connected"><Connection /></el-icon>
                    <el-icon v-else><Mute /></el-icon>
                    {{ row.is_connected ? '已连接' : '未连接' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 当前登录用户 -->
          <div v-if="terminalDetail.last_login_user" class="login-user-section">
            <div class="section-header">
              <el-icon class="section-icon"><User /></el-icon>
              <span>当前登录用户</span>
            </div>
            <el-card shadow="hover" class="user-card">
              <div class="user-info">
                <div class="user-avatar">
                  <el-avatar :size="60" :icon="User" />
                </div>
                <div class="user-details">
                  <div class="user-name">
                    <span class="username">{{ terminalDetail.last_login_user.username }}</span>
                    <span v-if="terminalDetail.last_login_user.full_name" class="fullname">
                      ({{ terminalDetail.last_login_user.full_name }})
                    </span>
                  </div>
                  <div class="user-meta">
                    <div class="meta-item" v-if="terminalDetail.last_login_user.domain">
                      <el-icon><HomeFilled /></el-icon>
                      <span>{{ terminalDetail.last_login_user.domain }}</span>
                    </div>
                    <div class="meta-item" v-if="terminalDetail.last_login_user.login_time">
                      <el-icon><Timer /></el-icon>
                      <span>登录时间: {{ formatTime(terminalDetail.last_login_user.login_time) }}</span>
                    </div>
                    <div class="meta-item" v-if="terminalDetail.last_login_user.update_time">
                      <el-icon><Calendar /></el-icon>
                      <span>更新时间: {{ formatTime(terminalDetail.last_login_user.update_time) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 软件列表 -->
        <el-tab-pane label="软件列表" name="software">
          <div class="action-bar software-action-bar">
            <div class="search-area">
              <el-input
                v-model="softwareSearchText"
                placeholder="搜索软件名称"
                clearable
                class="search-input"
                @input="filterSoftware"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-tag type="info" effect="plain" class="search-count">
                共 {{ filteredSoftwareList.length }} 个软件
              </el-tag>
            </div>

            <div class="right-actions">
              <el-button type="primary" size="small" :disabled="!terminalDetail.online_status" @click="refreshSoftwareList">
                <el-icon><Refresh /></el-icon> 刷新软件列表
              </el-button>
            </div>
          </div>

          <el-table
            v-loading="softwareLoading"
            :data="currentPageSoftware"
            style="width: 100%"
            border
            highlight-current-row
            class="mt-20 software-table"
            :empty-text="softwareLoading ? '加载中...' : '暂无软件数据'"
          >
            <el-table-column prop="name" label="软件名称" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="software-name">
                  <el-icon><Document /></el-icon>
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="version" label="版本" min-width="120">
              <template #default="{ row }">
                <el-tag effect="plain" size="small">{{ row.version || '未知' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="publisher" label="发布商" min-width="150" show-overflow-tooltip />
            <el-table-column prop="install_date" label="安装日期" min-width="150">
              <template #default="{ row }">
                <span>{{ row.install_date || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="size" label="大小" min-width="120">
              <template #default="{ row }">
                <el-tag effect="plain" size="small" type="success">{{ row.size ? formatSize(row.size, 'software') : '-' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="install_location" label="安装位置" min-width="200" show-overflow-tooltip />
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-tooltip content="卸载软件" placement="top">
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    :disabled="!terminalDetail.online_status"
                    @click="uninstallSoftware(row)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <!-- 添加分页控件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="softwarePagination.currentPage"
              v-model:page-size="softwarePagination.pageSize"
              :page-sizes="softwarePagination.pageSizes"
              layout="total, sizes, prev, pager, next, jumper"
              :total="softwarePagination.total"
              @size-change="handleSoftwareSizeChange"
              @current-change="handleSoftwareCurrentChange"
              background
            />
          </div>
        </el-tab-pane>

        <!-- 命令历史 -->
        <el-tab-pane label="命令历史" name="commands">
          <div class="action-bar command-action-bar">
            <div class="filter-area">
              <el-select
                v-model="commandStatusFilter"
                placeholder="命令状态"
                clearable
                @change="filterCommands"
                class="status-filter"
              >
                <el-option label="全部" value="" />
                <el-option label="等待发送" value="PENDING" />
                <el-option label="已发送" value="SENT" />
                <el-option label="已执行" value="EXECUTED" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="执行失败" value="FAILED" />
                <el-option label="执行超时" value="TIMEOUT" />
              </el-select>

              <el-tag type="info" effect="plain" class="command-count">
                共 {{ commandsList.length }} 条命令
              </el-tag>
            </div>

            <div class="button-group">
              <Authority permission="terminal:command:send">
                <el-button type="primary" :disabled="!terminalDetail.online_status" @click="sendCollectInfoCommand">
                  <el-icon><Promotion /></el-icon> 采集信息
                </el-button>
              </Authority>
              <Authority permission="terminal:command:send">
                <el-button type="success" :disabled="!terminalDetail.online_status" @click="showCustomCommandDialog">
                  <el-icon><Monitor /></el-icon> 发送自定义命令
                </el-button>
              </Authority>
              <Authority permission="terminal:agent:manage">
                <el-button type="warning" :disabled="!terminalDetail.online_status" @click="showUpgradeAgentDialog">
                  <el-icon><Upload /></el-icon> 升级Agent
                </el-button>
              </Authority>
            </div>
          </div>

          <el-timeline class="command-timeline">
            <el-timeline-item
              v-for="command in commandsList"
              :key="command.id"
              :type="getCommandStatusType(command.status)"
              :hollow="command.status?.toUpperCase() === 'PENDING' || command.status?.toUpperCase() === 'SENT'"
              :timestamp="formatTime(command.create_time)"
              placement="top"
            >
              <el-card shadow="hover" :class="['command-card', 'type-' + getCommandStatusType(command.status)]">
                <div class="command-header">
                  <div class="command-type">
                    <el-tag :type="getCommandTypeTag(command.type)" effect="dark" size="small">
                      {{ getCommandTypeText(command.type) }}
                    </el-tag>
                    <el-tag :type="getCommandStatusTag(command.status)" effect="plain" size="small" class="ml-10">
                      {{ getCommandStatusText(command.status) }}
                    </el-tag>
                  </div>
                  <div class="command-actions">
                    <el-tooltip content="查看结果" placement="top" :disabled="command.status?.toUpperCase() !== 'EXECUTED' && command.status?.toUpperCase() !== 'COMPLETED' && command.status?.toUpperCase() !== 'FAILED'">
                      <el-button
                        type="primary"
                        size="small"
                        circle
                        :disabled="command.status?.toUpperCase() !== 'EXECUTED' && command.status?.toUpperCase() !== 'COMPLETED' && command.status?.toUpperCase() !== 'FAILED'"
                        @click="viewCommandResult(command)"
                      >
                        <el-icon><View /></el-icon>
                      </el-button>
                    </el-tooltip>
                  </div>
                </div>
                <div class="command-content">
                  <div class="content-preview">{{ command.content }}</div>
                  <div class="command-meta">
                    <div class="meta-item" v-if="command.execute_time">
                      <el-icon><Timer /></el-icon>
                      <span>执行时间: {{ formatTime(command.execute_time) }}</span>
                    </div>
                    <div class="meta-item" v-if="command.execution_duration">
                      <el-icon><Stopwatch /></el-icon>
                      <span>耗时: {{ formatDuration(command.execution_duration) }}</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>

            <el-empty v-if="commandsList.length === 0" description="暂无命令历史" />
          </el-timeline>
          
          <!-- 命令历史分页 -->
          <div v-if="commandsPagination.total > 0" class="pagination-container">
            <el-pagination
              v-model:current-page="commandsPagination.currentPage"
              v-model:page-size="commandsPagination.pageSize"
              :page-sizes="commandsPagination.pageSizes"
              :total="commandsPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleCommandsSizeChange"
              @current-change="handleCommandsCurrentChange"
            />
          </div>
        </el-tab-pane>

        <!-- 注册表管理 -->
        <el-tab-pane v-if="terminalDetail.online_status" label="注册表管理" name="registry">
          <Authority permission="terminal:registry:read">
            <RegistryBrowser 
              :terminal-id="terminalId" 
              :is-active="activeTab === 'registry'"
              :has-been-activated="registryTabActivated"
              @registry-operation="handleRegistryOperation"
            />
          </Authority>
          <div v-if="!terminalDetail.online_status" class="offline-placeholder">
            <el-empty description="终端离线，无法进行注册表操作" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 自定义命令对话框 -->
    <el-dialog
      v-model="customCommandDialog.visible"
      title="发送自定义命令"
      width="700px"
      destroy-on-close
      class="custom-dialog"
    >
      <div class="dialog-body">
        <el-form ref="customCommandFormRef" :model="customCommandDialog.form" label-width="120px">
          <el-form-item label="命令模板" prop="template">
            <el-select
              v-model="customCommandDialog.selectedTemplate"
              placeholder="选择命令模板（可选）"
              style="width: 100%"
              clearable
              filterable
              @change="handleTemplateSelect"
            >
              <el-option-group
                v-for="category in commandTemplates"
                :key="category.category"
                :label="category.category"
              >
                <el-option
                  v-for="cmd in category.commands"
                  :key="cmd.id"
                  :label="`${cmd.command} - ${cmd.description}`"
                  :value="cmd.id"
                >
                  <div class="template-option">
                    <div class="command-text">{{ cmd.command }}</div>
                    <div class="command-desc">{{ cmd.description }}</div>
                    <el-tag :type="getSecurityLevelType(cmd.security_level)" size="small">
                      {{ cmd.security_level }}
                    </el-tag>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="命令内容" prop="content" required>
            <el-input
              v-model="customCommandDialog.form.content"
              type="textarea"
              placeholder="请输入命令内容，或从上方模板中选择"
              rows="4"
              class="command-textarea"
              @input="validateCommand"
            />
            <div class="command-validation" v-if="commandValidation">
              <el-alert
                :title="commandValidation.message"
                :type="commandValidation.is_valid ? 'success' : 'error'"
                :closable="false"
                style="margin-top: 10px"
              />
            </div>
          </el-form-item>
          <el-form-item label="超时时间(秒)" prop="timeout">
            <el-input-number v-model="customCommandDialog.form.timeout" :min="60" :max="86400" />
          </el-form-item>
          <el-form-item label="目标终端">
            <div class="target-terminal">
              <el-tag :type="terminalDetail.online_status ? 'success' : 'danger'" effect="dark">
                {{ terminalDetail.hostname }}
              </el-tag>
              <el-tag v-if="!terminalDetail.online_status" type="warning" effect="plain" class="ml-10">
                <el-icon><Warning /></el-icon> 终端当前离线，命令将在终端上线后执行
              </el-tag>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="customCommandDialog.visible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="sendCustomCommand" 
            :loading="customCommandDialog.loading"
            :disabled="!isCommandValid"
          >
            <el-icon><Promotion /></el-icon> 发送命令
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 命令结果对话框 -->
    <el-dialog
      v-model="resultDialog.visible"
      :title="resultDialog.title"
      width="700px"
      destroy-on-close
      class="custom-dialog result-dialog-container"
    >
      <div v-loading="resultDialog.loading" class="result-dialog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="命令类型">
            <el-tag :type="getCommandTypeTag(resultDialog.command.type)">
              {{ getCommandTypeText(resultDialog.command.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getCommandStatusTag(resultDialog.command.status)">
              {{ getCommandStatusText(resultDialog.command.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(resultDialog.command.create_time) }}</el-descriptions-item>
          <el-descriptions-item label="发送时间">
            {{ resultDialog.command.sent_time ? formatTime(resultDialog.command.sent_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="执行时间">
            {{ resultDialog.command.execute_time ? formatTime(resultDialog.command.execute_time) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">
            {{ formatDuration(resultDialog.command.execution_duration) }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="command-content-box mt-20">
          <div class="content-header">命令内容</div>
          <div class="content-body">
            <pre class="command-text">{{ resultDialog.command.content }}</pre>
          </div>
        </div>

        <div v-if="resultDialog.command.result" class="result-box mt-20">
          <div class="content-header success-header">执行结果</div>
          <div class="content-body">
            <pre class="result-text">{{ resultDialog.command.result }}</pre>
          </div>
        </div>

        <div v-if="resultDialog.command.error" class="error-box mt-20">
          <div class="content-header error-header">错误信息</div>
          <div class="content-body">
            <pre class="error-text">{{ resultDialog.command.error }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 升级Agent对话框 -->
    <el-dialog
      v-model="upgradeAgentDialog.visible"
      title="升级Agent"
      width="550px"
      destroy-on-close
      class="custom-dialog"
    >
      <div class="dialog-body">
        <el-form ref="upgradeAgentFormRef" :model="upgradeAgentDialog.form" label-width="100px">
          <el-form-item label="目标版本" prop="version" required>
            <el-select
              v-model="upgradeAgentDialog.form.version"
              placeholder="请选择目标版本"
              filterable
              clearable
              class="version-select"
              :loading="upgradeAgentDialog.versionsLoading"
            >
              <el-option
                v-for="version in upgradeAgentDialog.versions"
                :key="version.id"
                :label="version.version"
                :value="version.id"
              >
                <div class="version-option">
                  <span>{{ version.version }}</span>
                  <el-tag size="small" type="success" v-if="version.is_current">当前版本</el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="目标终端">
            <div class="target-terminal">
              <el-tag :type="terminalDetail.online_status ? 'success' : 'danger'" effect="dark">
                {{ terminalDetail.hostname }}
              </el-tag>
              <el-tag v-if="!terminalDetail.online_status" type="warning" effect="plain" class="ml-10">
                <el-icon><Warning /></el-icon> 终端当前离线，命令将在终端上线后执行
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="当前版本">
            <el-tag type="info" effect="dark">{{ terminalDetail.agent_version || '-' }}</el-tag>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="upgradeAgentDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="sendUpgradeAgentCommand" :loading="upgradeAgentDialog.loading">
            <el-icon><Upload /></el-icon> 发送升级命令
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Search,
  Refresh,
  Platform,
  Connection,
  Mute,
  Document,
  Promotion,
  Monitor,
  View,
  Timer,
  Stopwatch,
  Warning,
  Cpu,
  Box,
  Lock,
  User,
  HomeFilled,
  Calendar,
  InfoFilled,
  SuccessFilled,
  CloseBold,
  ChromeFilled,
  Apple,
  Delete,
  Upload,
  Back
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { terminalApi } from '@/api/terminal'
import { commandWhitelistApi } from '@/api/command-whitelist'
import RegistryBrowser from './components/RegistryBrowser.vue'
import Authority from '@/components/Authority/index.vue'
import type { TerminalDetail, Software, TerminalCommand } from '@/types/terminal'
import type { CommandTemplate, CommandWhitelist, CommandValidationResponse } from '@/types/command'

const route = useRoute()
const router = useRouter()
const terminalId = ref(route.params.id as string)

// 数据加载
const loading = ref(true)
const softwareLoading = ref(false)
const commandsLoading = ref(false)
const activeTab = ref('basic')
const registryTabActivated = ref(false)
const terminalDetail = ref<TerminalDetail>({} as TerminalDetail)
const softwareList = ref<Software[]>([])
const filteredSoftwareList = ref<Software[]>([])
const commandsList = ref<TerminalCommand[]>([])
const softwareSearchText = ref('')
const commandStatusFilter = ref('')
const softwarePagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

// 命令历史分页
const commandsPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

// 自定义命令对话框
const customCommandDialog = reactive({
  visible: false,
  loading: false,
  selectedTemplate: null as number | null,
  form: {
    content: '',
    timeout: 3600
  }
})

// 命令模板和验证
const commandTemplates = ref<CommandTemplate[]>([])
const commandValidation = ref<CommandValidationResponse | null>(null)

// 结果对话框
const resultDialog = reactive({
  visible: false,
  loading: false,
  title: '命令执行结果',
  command: {} as TerminalCommand
})

// 升级Agent对话框
const upgradeAgentDialog = reactive({
  visible: false,
  loading: false,
  versionsLoading: false,
  versions: [] as any[],
  form: {
    version: ''
  }
})

// 加载终端详情
const fetchTerminalDetail = async () => {
  loading.value = true
  try {
    const response = await terminalApi.getTerminalDetail(terminalId.value)
    terminalDetail.value = response.data
  } catch (error) {
    console.error('获取终端详情失败:', error)
    ElMessage.error('获取终端详情失败')
  } finally {
    loading.value = false
  }
}

// 加载软件列表
const fetchSoftwareList = async () => {
  softwareLoading.value = true
  try {
    const response = await terminalApi.getTerminalSoftware(terminalId.value)
    softwareList.value = response.data
    filteredSoftwareList.value = [...softwareList.value]
    softwarePagination.total = filteredSoftwareList.value.length
  } catch (error) {
    console.error('获取软件列表失败:', error)
    ElMessage.error('获取软件列表失败')
  } finally {
    softwareLoading.value = false
  }
}

// 加载命令历史
const fetchCommandsList = async () => {
  commandsLoading.value = true
  try {
    const params = {
      status: commandStatusFilter.value,
      skip: (commandsPagination.currentPage - 1) * commandsPagination.pageSize,
      limit: commandsPagination.pageSize
    }
    const response = await terminalApi.getTerminalCommands(terminalId.value, params)
    
    // 检查响应数据格式
    if (Array.isArray(response.data)) {
      commandsList.value = response.data
      // 如果是数组，说明后端没有分页，我们需要自己处理分页
      commandsPagination.total = response.data.length
    } else if (response.data && typeof response.data === 'object') {
      // 如果是对象，可能包含items和total字段
      commandsList.value = (response.data as any).items || response.data
      commandsPagination.total = (response.data as any).total || 0
    } else {
      commandsList.value = []
      commandsPagination.total = 0
    }
  } catch (error) {
    console.error('获取命令历史失败:', error)
    ElMessage.error('获取命令历史失败')
  } finally {
    commandsLoading.value = false
  }
}

// 过滤软件
const filterSoftware = () => {
  if (!softwareSearchText.value) {
    filteredSoftwareList.value = [...softwareList.value]
  } else {
    const searchText = softwareSearchText.value.toLowerCase()
    filteredSoftwareList.value = softwareList.value.filter(software =>
      software.name.toLowerCase().includes(searchText) ||
      (software.publisher && software.publisher.toLowerCase().includes(searchText))
    )
  }
  softwarePagination.total = filteredSoftwareList.value.length
  softwarePagination.currentPage = 1 // 重置为第一页
}

// 获取当前页显示的软件列表
const currentPageSoftware = computed(() => {
  const start = (softwarePagination.currentPage - 1) * softwarePagination.pageSize
  const end = start + softwarePagination.pageSize
  return filteredSoftwareList.value.slice(start, end)
})

// 添加分页事件处理函数
const handleSoftwareSizeChange = (val: number) => {
  softwarePagination.pageSize = val
}

const handleSoftwareCurrentChange = (val: number) => {
  softwarePagination.currentPage = val
}

// 命令历史分页事件处理函数
const handleCommandsSizeChange = (val: number) => {
  commandsPagination.pageSize = val
  commandsPagination.currentPage = 1 // 重置为第一页
  fetchCommandsList()
}

const handleCommandsCurrentChange = (val: number) => {
  commandsPagination.currentPage = val
  fetchCommandsList()
}

// 过滤命令
const filterCommands = () => {
  commandsPagination.currentPage = 1 // 重置为第一页
  fetchCommandsList()
}

// 发送采集信息命令
const sendCollectInfoCommand = async () => {
  try {
    await terminalApi.sendCollectInfoCommand(terminalId.value)
    ElMessage.success('已发送采集信息命令')
    fetchCommandsList()
  } catch (error) {
    console.error('发送采集信息命令失败:', error)
    ElMessage.error('发送采集信息命令失败')
  }
}

// 显示自定义命令对话框
const showCustomCommandDialog = async () => {
  customCommandDialog.visible = true
  customCommandDialog.selectedTemplate = null
  customCommandDialog.form = {
    content: '',
    timeout: 3600
  }
  commandValidation.value = null
  
  // 加载命令模板
  await fetchCommandTemplates()
}

// 获取命令模板
const fetchCommandTemplates = async () => {
  try {
    const response = await commandWhitelistApi.getTemplates()
    commandTemplates.value = response.data.categories
  } catch (error) {
    console.error('获取命令模板失败:', error)
  }
}

// 处理模板选择
const handleTemplateSelect = (templateId: number | null) => {
  if (!templateId) {
    return
  }
  
  // 查找选中的命令模板
  let selectedCommand: CommandWhitelist | null = null
  for (const category of commandTemplates.value) {
    const found = category.commands.find(cmd => cmd.id === templateId)
    if (found) {
      selectedCommand = found
      break
    }
  }
  
  if (selectedCommand) {
    customCommandDialog.form.content = selectedCommand.command
    validateCommand()
  }
}

// 验证命令
const validateCommand = async () => {
  const content = customCommandDialog.form.content.trim()
  if (!content) {
    commandValidation.value = null
    return
  }
  
  try {
    const response = await commandWhitelistApi.validateCommand({ command: content })
    commandValidation.value = response.data
  } catch (error) {
    console.error('验证命令失败:', error)
    commandValidation.value = {
      is_valid: false,
      message: '命令验证失败'
    }
  }
}

// 计算命令是否有效
const isCommandValid = computed(() => {
  const content = customCommandDialog.form.content.trim()
  return content && (!commandValidation.value || commandValidation.value.is_valid)
})

// 获取安全级别标签类型
const getSecurityLevelType = (level: string) => {
  switch (level) {
    case 'PUBLIC': return 'success'
    case 'OPERATOR': return 'warning'
    case 'ADMIN': return 'danger'
    default: return 'info'
  }
}

// 发送自定义命令
const sendCustomCommand = async () => {
  if (!customCommandDialog.form.content) {
    ElMessage.warning('请输入命令内容')
    return
  }

  customCommandDialog.loading = true
  try {
    const data = {
      terminal_id: terminalId.value,
      type: 'CUSTOM_COMMAND',
      content: customCommandDialog.form.content,
      timeout: customCommandDialog.form.timeout
    }

    await terminalApi.createTerminalCommand(data)
    ElMessage.success('命令发送成功')
    customCommandDialog.visible = false
    fetchCommandsList()
  } catch (error) {
    console.error('发送命令失败:', error)
    ElMessage.error('发送命令失败')
  } finally {
    customCommandDialog.loading = false
  }
}

// 查看命令结果
const viewCommandResult = (command: TerminalCommand) => {
  resultDialog.command = command
  resultDialog.visible = true
}

// 显示升级Agent对话框
const showUpgradeAgentDialog = async () => {
  upgradeAgentDialog.visible = true
  upgradeAgentDialog.form.version = ''

  // 加载Agent版本列表
  await fetchAgentVersions()
}

// 获取Agent版本列表
const fetchAgentVersions = async () => {
  upgradeAgentDialog.versionsLoading = true
  try {
    // 获取终端平台类型
    let platform = 'windows'
    if (terminalDetail.value.os_info && terminalDetail.value.os_info.name) {
      const osName = terminalDetail.value.os_info.name.toLowerCase()
      if (osName.includes('linux')) {
        platform = 'linux'
      } else if (osName.includes('mac') || osName.includes('darwin')) {
        platform = 'macos'
      }
    }

    const response = await terminalApi.getAgentVersions({ platform })
    upgradeAgentDialog.versions = response.data
  } catch (error) {
    console.error('获取Agent版本列表失败:', error)
    ElMessage.error('获取Agent版本列表失败')
  } finally {
    upgradeAgentDialog.versionsLoading = false
  }
}

// 发送升级Agent命令
const sendUpgradeAgentCommand = async () => {
  if (!upgradeAgentDialog.form.version) {
    ElMessage.warning('请选择目标版本')
    return
  }

  upgradeAgentDialog.loading = true
  try {
    // 获取选中的版本信息
    const selectedVersion = upgradeAgentDialog.versions.find(v => v.id === upgradeAgentDialog.form.version)
    if (!selectedVersion) {
      throw new Error('无法找到选中的版本信息')
    }

    // 发送升级命令
    await terminalApi.sendUpgradeAgentCommand(terminalId.value, {
      version: selectedVersion.version,
      download_url: selectedVersion.download_url
    })

    ElMessage.success('已发送Agent升级命令')
    upgradeAgentDialog.visible = false
    fetchCommandsList()
  } catch (error) {
    console.error('发送升级命令失败:', error)
    ElMessage.error('发送升级命令失败')
  } finally {
    upgradeAgentDialog.loading = false
  }
}

// 获取命令类型文本
const getCommandTypeText = (type: string) => {
  switch (type) {
    case 'COLLECT_INFO': return '采集信息'
    case 'UPGRADE_AGENT': return '升级Agent'
    case 'CUSTOM_COMMAND': return '自定义命令'
    default: return type
  }
}

// 获取命令类型标签样式
const getCommandTypeTag = (type: string) => {
  switch (type) {
    case 'COLLECT_INFO': return 'primary'
    case 'UPGRADE_AGENT': return 'warning'
    case 'CUSTOM_COMMAND': return 'success'
    default: return 'info'
  }
}

// 获取命令状态文本
const getCommandStatusText = (status: string) => {
  // 转换为大写处理，确保大小写兼容
  const upperStatus = status?.toUpperCase() || ''
  
  switch (upperStatus) {
    case 'PENDING': return '等待发送'
    case 'SENT': return '已发送'
    case 'EXECUTED': return '已执行'
    case 'COMPLETED': return '已完成'
    case 'FAILED': return '执行失败'
    case 'TIMEOUT': return '执行超时'
    default: return status
  }
}

// 获取命令状态标签样式
const getCommandStatusTag = (status: string) => {
  const upperStatus = status?.toUpperCase() || ''
  
  switch (upperStatus) {
    case 'PENDING': return 'info'
    case 'SENT': return 'warning'
    case 'EXECUTED': return 'success'
    case 'COMPLETED': return 'success'
    case 'FAILED': return 'danger'
    case 'TIMEOUT': return 'danger'
    default: return 'info'
  }
}

// 格式化时间
const formatTime = (time: string | undefined) => {
  if (!time) return '-'

  try {
    // 检查中文日期格式：YYYY年MM月DD日 HH:mm:ss
    const chinesePattern = /^(\d{4})年(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/
    if (chinesePattern.test(time)) {
      const match = time.match(chinesePattern)
      if (match) {
        const [, year, month, day, hour, minute, second] = match
        return `${year}/${month.padStart(2, '0')}/${day.padStart(2, '0')} ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}`
      }
    }

    // 检查日期格式是否为 YYYY-MM-DD HH:MM:SS
    const datePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/
    if (datePattern.test(time)) {
      // 将 YYYY-MM-DD HH:MM:SS 格式转换为 YYYY/MM/DD HH:MM:SS
      const [datePart, timePart] = time.split(' ')
      const [year, month, day] = datePart.split('-')
      return `${year}/${month}/${day} ${timePart}`
    }

    // 如果不是预期的格式，尝试使用 Date 处理
    const date = new Date(time)

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期格式:', time)
      return time
    }

    // 格式化日期时间
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const second = String(date.getSeconds()).padStart(2, '0')

    return `${year}/${month}/${day} ${hour}:${minute}:${second}`
  } catch (error) {
    console.error('格式化时间错误:', error)
    return time // 出错时返回原始字符串
  }
}

// 格式化执行耗时
const formatDuration = (duration?: number) => {
  if (!duration) return '-'
  
  // 如果小于1000毫秒，显示毫秒
  if (duration < 1000) {
    return `${duration}毫秒`
  }
  
  // 如果小于60秒，显示秒（保留1位小数）
  if (duration < 60000) {
    const seconds = (duration / 1000).toFixed(1)
    return `${seconds}秒`
  }
  
  // 如果大于等于60秒，显示分钟和秒
  const minutes = Math.floor(duration / 60000)
  const remainingSeconds = Math.floor((duration % 60000) / 1000)
  
  if (remainingSeconds === 0) {
    return `${minutes}分钟`
  } else {
    return `${minutes}分${remainingSeconds}秒`
  }
}

// 格式化文件大小
const formatSize = (size?: number, sourceType: 'memory' | 'disk' | 'software' = 'software') => {
  if (!size) return '-'

  // 根据数据来源使用不同的单位转换逻辑
  if (sourceType === 'memory' || sourceType === 'disk') {
    // 内存和磁盘大小是以KB为单位
    if (size < 1024) {
      return size + ' KB'
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' MB'
    } else {
      return (size / (1024 * 1024)).toFixed(2) + ' GB'
    }
  } else {
    // 软件大小实际单位可能是B(字节)
    if (size < 1024) {
      return size + ' B'
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB'
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB'
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
    }
  }
}

// 返回列表页
const goBack = () => {
  router.push('/terminal/list')
}

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  if (tabName === 'software' && softwareList.value.length === 0) {
    fetchSoftwareList()
  } else if (tabName === 'commands' && commandsList.value.length === 0) {
    fetchCommandsList()
  } else if (tabName === 'registry' && !registryTabActivated.value) {
    registryTabActivated.value = true
  }
}

// 监听标签页切换
watch(activeTab, (newValue) => {
  handleTabChange(newValue)
})

// 刷新软件列表
const refreshSoftwareList = async () => {
  await fetchSoftwareList()
  ElMessage.success('软件列表已刷新')
}

// 获取命令状态类型（用于timeline）
const getCommandStatusType = (status: string) => {
  const upperStatus = status?.toUpperCase() || ''
  
  switch (upperStatus) {
    case 'EXECUTED': return 'success'
    case 'COMPLETED': return 'success'
    case 'FAILED': return 'danger'
    case 'TIMEOUT': return 'warning'
    case 'PENDING': return 'info'
    case 'SENT': return 'primary'
    default: return 'info'
  }
}

// 根据操作系统名称获取图标
const getOsIcon = (osName: string | undefined) => {
  if (!osName) return Platform

  const lowerOsName = osName?.toLowerCase() || ''
  if (lowerOsName.includes('windows')) return ChromeFilled
  if (lowerOsName.includes('mac') || lowerOsName.includes('darwin')) return Apple
  if (lowerOsName.includes('linux') || lowerOsName.includes('ubuntu') || lowerOsName.includes('centos')) return Platform

  return Platform
}

// 获取相对时间描述
const getTimeAgo = (time: string | undefined) => {
  if (!time) return '未知'

  const now = new Date()
  const past = new Date(time)
  const diff = Math.floor((now.getTime() - past.getTime()) / 1000)

  if (diff < 60) return `${diff}秒前`
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
  if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`
  if (diff < 2592000) return `${Math.floor(diff / 86400)}天前`
  if (diff < 31536000) return `${Math.floor(diff / 2592000)}个月前`
  return `${Math.floor(diff / 31536000)}年前`
}

// 计算磁盘使用百分比
const calculateUsagePercentage = (disk: any) => {
  if (!disk.total_space || !disk.free_space) return 0
  const used = disk.total_space - disk.free_space
  return Math.round((used / disk.total_space) * 100)
}

// 获取磁盘进度条颜色
const getDiskProgressColor = (disk: any) => {
  const percentage = calculateUsagePercentage(disk)
  if (percentage > 90) return '#F56C6C'
  if (percentage > 70) return '#E6A23C'
  return '#67C23A'
}

// 卸载软件
const uninstallSoftware = async (software: Software) => {
  ElMessageBox.confirm(
    `确定要从终端 ${terminalDetail.value.hostname} 卸载软件 "${software.name} ${software.version || ''}" 吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await terminalApi.sendUninstallSoftwareCommand(terminalId.value, {
        name: software.name,
        version: software.version
      })
      ElMessage.success('已发送卸载命令，正在处理中')
      fetchCommandsList() // 更新命令列表
    } catch (error) {
      console.error('发送卸载命令失败:', error)
      ElMessage.error('发送卸载命令失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 处理注册表操作
const handleRegistryOperation = (data: any) => {
  console.log('注册表操作:', data)
  // 这里可以处理来自RegistryBrowser组件的注册表操作事件
  // 例如记录操作日志、刷新相关数据等
}

onMounted(() => {
  fetchTerminalDetail()
})
</script>

<style scoped>
.terminal-detail {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 16px;
  font-size: 14px;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 10px;
}

/* 终端头部卡片样式 */
.terminal-header-card {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.terminal-info-main {
  display: flex;
  gap: 20px;
  flex: 1;
}

.terminal-avatar {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  font-size: 32px;
}

.terminal-info-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.terminal-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.hostname {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.status-badges {
  display: flex;
  gap: 10px;
  align-items: center;
}

.status-tag {
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  font-weight: 500;
}

.version-tag {
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
}

.terminal-meta {
  display: flex;
  gap: 24px;
  color: #606266;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.terminal-os-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-left: 24px;
  border-left: 1px solid #ebeef5;
}

.os-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.os-logo {
  font-size: 40px;
  color: #409eff;
}

.os-details {
  display: flex;
  flex-direction: column;
}

.os-name {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.os-version {
  font-size: 14px;
  color: #606266;
}

/* 内容区域样式 */
.terminal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.detail-tabs {
  margin-bottom: 20px;
}

.detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.detail-tabs :deep(.el-tabs__item) {
  font-size: 15px;
  height: 50px;
  line-height: 50px;
}

.detail-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

.info-summary {
  margin-bottom: 30px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.summary-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, #409eff, #67c23a);
  border-radius: 4px 0 0 4px;
}

.system-card::before {
  background: linear-gradient(to bottom, #409eff, #79bbff);
}

.hardware-card::before {
  background: linear-gradient(to bottom, #67c23a, #95d475);
}

.network-card::before {
  background: linear-gradient(to bottom, #e6a23c, #f3d19e);
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.card-icon {
  font-size: 20px;
  color: #409eff;
}

.system-card .card-icon {
  color: #409eff;
}

.hardware-card .card-icon {
  color: #67c23a;
}

.network-card .card-icon {
  color: #e6a23c;
}

.card-content {
  padding: 0;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item .label {
  color: #606266;
  width: 90px;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
  font-weight: 500;
}

.section-divider {
  display: flex;
  align-items: center;
  margin: 30px 0 20px;
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 1px;
  background-color: #ebeef5;
  z-index: 1;
}

.divider-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  background-color: #fff;
  padding: 0 12px 0 0;
  position: relative;
  z-index: 2;
}

.mt-20 {
  margin-top: 20px;
}

.ml-10 {
  margin-left: 10px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.software-action-bar {
  border-left: 4px solid #409eff;
}

.command-action-bar {
  border-left: 4px solid #67c23a;
}

.search-area, .filter-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input, .status-filter {
  width: 320px;
}

.search-count, .command-count {
  height: 32px;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
}

.software-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.software-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.software-table :deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

.software-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.command-timeline {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.command-timeline :deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
}

.command-timeline :deep(.el-timeline-item__tail) {
  left: 7px;
}

.command-timeline :deep(.el-timeline-item__wrapper) {
  padding-left: 30px;
}

.command-timeline :deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: #909399;
}

.command-timeline :deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
}

.command-timeline :deep(.el-timeline-item__tail) {
  left: 7px;
}

.command-timeline :deep(.el-timeline-item__wrapper) {
  padding-left: 30px;
}

.command-timeline :deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: #909399;
}

.command-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  transition: all 0.3s;
  border-left: 3px solid #dcdfe6;
}

.command-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

/* 根据命令类型设置不同的边框颜色 */
.command-card.type-primary {
  border-left-color: #409eff;
}

.command-card.type-success {
  border-left-color: #67c23a;
}

.command-card.type-warning {
  border-left-color: #e6a23c;
}

.command-card.type-danger {
  border-left-color: #f56c6c;
}

.command-card.type-info {
  border-left-color: #909399;
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.command-type {
  display: flex;
  align-items: center;
}

.command-content {
  background-color: #f9fafc;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #ebeef5;
}

.content-preview {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  max-height: 80px;
  overflow-y: auto;
  color: #303133;
  line-height: 1.5;
  padding: 5px;
}

.command-meta {
  display: flex;
  margin-top: 10px;
  gap: 15px;
  color: #909399;
  font-size: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dialog-body {
  padding: 10px 0;
}

.command-textarea {
  font-family: monospace;
}

.target-terminal {
  display: flex;
  align-items: center;
}

.result-dialog {
  max-height: 70vh;
  overflow-y: auto;
}

.command-content-box,
.result-box,
.error-box {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: hidden;
}

.content-header {
  padding: 8px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  font-weight: bold;
}

.success-header {
  background-color: #f0f9eb;
  color: #67c23a;
}

.error-header {
  background-color: #fef0f0;
  color: #f56c6c;
}

.content-body {
  padding: 15px;
  background-color: #fff;
}

.command-text,
.result-text,
.error-text {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 13px;
  margin: 0;
}

.result-text {
  max-height: 300px;
  overflow-y: auto;
}

.error-text {
  color: #F56C6C;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.detail-collapse {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.detail-collapse :deep(.el-collapse-item__header) {
  font-size: 15px;
  padding: 12px 15px;
  background-color: #f9fafc;
  border-radius: 4px;
  transition: all 0.3s;
}

.detail-collapse :deep(.el-collapse-item__header:hover) {
  background-color: #f0f7ff;
}

.detail-collapse :deep(.el-collapse-item__content) {
  padding: 20px 15px;
}

.detail-collapse :deep(.el-collapse-item__arrow) {
  font-size: 16px;
  font-weight: bold;
}

.collapse-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.collapse-icon {
  font-size: 18px;
  color: #409EFF;
}

.custom-descriptions {
  margin-top: 15px;
}

.custom-descriptions :deep(.el-descriptions__label) {
  width: 120px;
  font-weight: 600;
  color: #606266;
  background-color: #f9fafc;
}

.custom-descriptions :deep(.el-descriptions__content) {
  padding: 12px 15px;
}

.custom-descriptions :deep(.el-descriptions__body) {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.important-text {
  font-weight: bold;
  color: #303133;
}

.ellipsis-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-ago {
  font-size: 12px;
}

.os-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.os-icon {
  font-size: 16px;
}

.security-status {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.security-warning {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #E6A23C;
  font-size: 12px;
}

.disk-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.disk-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.disk-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.disk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.disk-name {
  font-weight: bold;
  font-size: 16px;
}

.disk-type {
  color: #909399;
  font-size: 13px;
}

.disk-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.disk-size-info {
  display: flex;
  justify-content: space-between;
}

.disk-total, .disk-free {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-label {
  color: #909399;
  font-size: 13px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.disk-usage-container {
  margin: 5px 0;
}

.disk-mount {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dns-servers {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.dns-tag {
  margin-right: 5px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 16px;
  margin: 25px 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
  color: #303133;
}

.section-icon {
  font-size: 18px;
  color: #409EFF;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.custom-table :deep(th.el-table__cell) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
}

.network-interface-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connected-icon {
  color: #67C23A;
}

.login-user-section {
  margin-top: 20px;
}

.user-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.user-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  gap: 20px;
}

.user-avatar {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.user-details {
  flex: 1;
}

.user-name {
  margin-bottom: 10px;
}

.username {
  font-size: 18px;
  font-weight: bold;
  margin-right: 8px;
}

.fullname {
  color: #606266;
}

.user-meta {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

.pagination-container {
  margin-top: 25px;
  text-align: right;
  padding: 10px;
  background-color: #f9fafc;
  border-radius: 8px;
}

.version-select {
  width: 100%;
}

.version-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 对话框样式 */
.custom-dialog :deep(.el-dialog__header) {
  padding: 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
  background-color: #f9fafc;
}

.custom-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
}

.custom-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.custom-dialog :deep(.el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f9fafc;
}

.custom-dialog :deep(.el-button) {
  padding: 9px 20px;
}

/* 命令模板选择器样式 */
.template-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
  padding: 5px 0;
}

.command-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.command-desc {
  font-size: 12px;
  color: #909399;
}

.command-validation {
  margin-top: 10px;
}

.command-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
</style>