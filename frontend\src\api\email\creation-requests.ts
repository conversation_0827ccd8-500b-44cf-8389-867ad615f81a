import request from '@/utils/request'

// 邮箱申请相关接口

/**
 * 获取邮箱创建申请列表
 */
export function getCreationRequests(params?: {
  status?: string
  skip?: number
  limit?: number
}) {
  return request.get('/email-creation-requests/requests', { params })
}

/**
 * 获取申请统计信息
 */
export function getRequestsStats() {
  return request.get('/email-creation-requests/requests/stats')
}

/**
 * 获取申请详情
 */
export function getCreationRequestDetail(requestId: number) {
  return request.get(`/email-creation-requests/requests/${requestId}`)
}

/**
 * 审批申请（批准或拒绝）
 */
export function approveCreationRequest(requestId: number, data: {
  action: 'approve' | 'reject'
  notes?: string
}) {
  return request.post(`/email-creation-requests/requests/${requestId}/approve`, data)
}

/**
 * 删除申请
 */
export function deleteCreationRequest(requestId: number) {
  return request.delete(`/email-creation-requests/requests/${requestId}`)
}

/**
 * 批量审批申请
 */
export function batchApproveRequests(data: {
  request_ids: number[]
  action: 'approve' | 'reject'
  notes?: string
}) {
  return request.post('/email-creation-requests/requests/batch-approve', data)
} 