# 修复人员邮箱同步中的"userid not found"错误

## 问题背景

在人员邮箱同步过程中，当系统尝试更新用户信息时，腾讯企业邮箱API返回错误：
- 错误码：60111
- 错误信息：`userid not found`
- 发生场景：`_update_email_user` 方法中调用 `update_member` API时

## 问题分析

**根本原因：**
- 用户在本地数据库中存在但在腾讯企业邮箱系统中不存在
- 可能是由于：用户被外部删除、从未创建或数据同步问题

**现有代码问题：**
- `_update_email_user` 方法没有处理用户不存在的情况
- `_disable_email_user` 方法已经正确处理了60111错误

## 解决方案

采用**容错处理**方案：当更新用户遇到"用户不存在"错误时，自动触发用户创建流程。

### 修复内容

1. **修改文件：** `backend/app/services/personnel_email_sync.py`
2. **修改方法：** `_update_email_user`
3. **主要改动：**
   - 在腾讯API更新失败后检查错误码60111
   - 当用户不存在时，自动构建创建用户的数据
   - 调用创建API，使用现有邮箱地址（不重新生成）
   - 创建或更新本地邮箱成员记录
   - 返回详细的操作结果和日志

### 核心逻辑

```python
if result.errcode == 60111:  # 用户不存在
    logger.warning(f"用户 {email_data['email']} 在腾讯企业邮箱中不存在，尝试自动创建")
    
    # 构建创建用户的API数据
    create_api_data = {
        "userid": email_data["email"],  # 使用现有邮箱地址
        "name": personnel_data["user_name"],
        "extid": job_number,
        "department": [dept_id],
        "position": personnel_data.get("job_title_name") or "",
        "mobile": personnel_data.get("mobile") or "",
        "password": "TempPass123!"
    }
    
    # 调用创建API
    create_result = await self.api_service.create_member(create_api_data)
    
    # 处理结果和更新本地记录
```

## 修复效果

1. **自动恢复数据一致性**：当检测到用户不存在时自动创建
2. **提高系统容错性**：避免因单个用户问题导致整个同步流程失败
3. **详细日志记录**：记录自动修复操作，便于监控和调试
4. **保持现有邮箱地址**：使用已分配的邮箱地址，避免重复分配

## 测试验证

- ✅ 确保60111错误被正确捕获和处理
- ✅ 验证自动创建用户的API调用正确
- ✅ 检查本地数据库记录的创建/更新
- ✅ 确认日志输出的完整性和准确性

## 相关代码文件

- `backend/app/services/personnel_email_sync.py` - 主要修复文件
- `backend/app/services/email_api.py` - 腾讯API服务
- `backend/app/crud/email.py` - 邮箱数据操作

## 额外改进：已实现用户禁用功能

在修复过程中，我们发现腾讯企业邮箱API的 `user/update` 接口支持 `enable` 参数来禁用/启用用户。

### 新增功能：

1. **添加了禁用/启用方法**：
```python
async def disable_member(self, userid: str):
    """禁用成员"""
    return await self.request("POST", "user/update", {
        "userid": userid,
        "enable": 0
    })

async def enable_member(self, userid: str):
    """启用成员"""
    return await self.request("POST", "user/update", {
        "userid": userid,
        "enable": 1
    })
```

2. **更新离职处理方式**：
- **原来**：删除用户账号（不可逆）
- **现在**：禁用用户账号（可逆，保留数据）

3. **优势**：
- ✅ **可逆操作**：可随时重新启用
- ✅ **数据安全**：保留邮件数据
- ✅ **管理友好**：避免误删重要信息

## 日期

2025-01-12 