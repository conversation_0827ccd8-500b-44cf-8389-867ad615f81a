import request from '@/utils/request'
import type { InventoryTask, InventoryTaskCreate, InventoryTaskUpdate, InventoryRecord, InventoryRecordUpdate } from '@/types/inventory'

export const inventoryApi = {
  // 获取盘点任务列表
  getInventoryTasks: (params: {
    skip?: number
    limit?: number
    keyword?: string
    status?: string
    start_date_start?: string
    start_date_end?: string
    end_date_start?: string
    end_date_end?: string
    sort_by?: string
    sort_order?: string
  }) => {
    return request.get<{ data: InventoryTask[], total: number }>('/inventory/tasks', { params })
  },

  // 获取单个盘点任务
  getInventoryTask: (id: number) => {
    return request.get<InventoryTask>(`/inventory/tasks/${id}`)
  },

  // 创建盘点任务
  createInventoryTask: (data: InventoryTaskCreate) => {
    return request.post<InventoryTask>('/inventory/tasks', data)
  },

  // 更新盘点任务
  updateInventoryTask: (id: number, data: InventoryTaskUpdate) => {
    return request.put<InventoryTask>(`/inventory/tasks/${id}`, data)
  },

  // 删除盘点任务
  deleteInventoryTask: (id: number) => {
    return request.delete(`/inventory/tasks/${id}`)
  },

  // 导出盘点任务
  exportInventoryTask: (id: number, format: 'excel' | 'csv' = 'excel') => {
    return request.get(`/inventory/tasks/${id}/export`, {
      params: { format },
      responseType: 'blob'
    })
  },

  // 获取盘点记录列表
  getInventoryRecords: (taskId: number, params: {
    skip?: number
    limit?: number
    status?: string
    keyword?: string
  }) => {
    return request.get<{ data: InventoryRecord[], total: number }>(`/inventory/tasks/${taskId}/records`, { params })
  },

  // 更新盘点记录
  updateInventoryRecord: (id: number, data: InventoryRecordUpdate) => {
    return request.put<InventoryRecord>(`/inventory/records/${id}`, data)
  },

  // 通过任务ID和资产ID更新盘点记录（支持虚拟记录）
  updateInventoryRecordByAsset: (taskId: number, assetId: number, data: InventoryRecordUpdate) => {
    return request.put<InventoryRecord>(`/inventory/tasks/${taskId}/records/${assetId}`, data)
  },

  // 完成盘点任务
  completeInventoryTask: (id: number) => {
    return request.post(`/inventory/tasks/${id}/complete`)
  },

  // 完成盘点任务（支持自动应用变更）
  completeInventoryTaskWithChanges: (id: number, data?: {
    auto_apply_changes?: boolean
  }) => {
    return request.post(`/inventory/tasks/${id}/complete`, data || {})
  },

  // 获取盘点任务统计信息
  getInventoryTaskStatistics: (id: number) => {
    return request.get<{
      total: number
      pending: number
      normal: number
      abnormal: number
      missing: number
      info_changed: number
      checked: number
    }>(`/inventory/tasks/${id}/statistics`)
  },

  // 获取盘点任务的信息变更预览
  getInventoryInfoChanges: (taskId: number) => {
    return request.get<{
      task_id: number
      task_name: string
      total_changes: number
      changes: Array<{
        record_id: number
        asset_id: number
        asset_number: string
        asset_name: string
        checked_by: string
        checked_at: string
        changes: Record<string, {
          label: string
          old_value: any
          new_value: any
        }>
      }>
    }>(`/inventory/tasks/${taskId}/info-changes`)
  },

  // 应用盘点任务的信息变更
  applyInventoryInfoChanges: (taskId: number, data: {
    record_ids?: number[]
  }) => {
    return request.post<{
      message: string
      applied_count: number
      failed_count: number
      failed_records: Array<{
        record_id: number
        asset_number: string
        error: string
      }>
    }>(`/inventory/tasks/${taskId}/apply-info-changes`, data)
  }
} 