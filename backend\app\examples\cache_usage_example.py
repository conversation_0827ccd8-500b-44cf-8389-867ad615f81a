"""
Redis缓存系统使用示例

展示新的分层TTL缓存系统的使用方法
"""
from app.utils.redis_cache import RedisCache
from app.core.cache_config import CacheDataType, CacheKeyManager
import logging

logger = logging.getLogger(__name__)

# 创建缓存实例
cache = RedisCache()

def example_basic_usage():
    """基础使用示例"""
    
    # 1. 传统方式（兼容现有代码）
    cache.set("simple_key", {"data": "value"}, ttl=300)
    result = cache.get("simple_key")
    
    # 2. 使用数据类型自动选择TTL
    cache.set_with_data_type("user_info:123", {"name": "张三"}, CacheDataType.SEMI_STATIC)  # 15分钟
    cache.set_with_data_type("sync_status", {"status": "running"}, CacheDataType.DYNAMIC)  # 2分钟
    cache.set_with_data_type("online_users", ["user1", "user2"], CacheDataType.REALTIME)  # 30秒
    
    # 3. 使用业务键自动选择TTL
    cache.set_with_business_key("user_list", [{"id": 1}, {"id": 2}], "users")  # 15分钟
    cache.set_with_business_key("email_sync_log", {"last_sync": "2025-01-21"}, "email_sync_logs")  # 2分钟

def example_batch_operations():
    """批量操作示例"""
    
    # 批量设置
    data = {
        "user:1": {"name": "用户1"},
        "user:2": {"name": "用户2"},
        "user:3": {"name": "用户3"}
    }
    cache.set_multiple(data, business_key="users")
    
    # 批量获取
    keys = ["user:1", "user:2", "user:3"]
    results = cache.get_multiple(keys)
    
    # 检查键是否存在
    exists_results = cache.exists_multiple(keys)

def example_cache_key_management():
    """缓存键管理示例"""
    
    # 构建标准化缓存键
    request_key = CacheKeyManager.build_request_key(
        module="email",
        operation="members",
        identifier="department_1",
        user="admin"
    )
    # 结果: "request_cache:email:members:department_1:user:admin"
    
    data_key = CacheKeyManager.build_data_key(
        module="ad",
        operation="users",
        identifier="ou_1"
    )
    # 结果: "data_cache:ad:users:ou_1"
    
    # 获取清除模式
    pattern = CacheKeyManager.get_pattern("request_cache", "email")
    # 结果: "request_cache:email:*"

def example_cache_invalidation():
    """缓存失效示例"""
    
    # 1. 单个键删除
    cache.delete("specific_key")
    
    # 2. 模式匹配删除
    count = cache.clear_pattern("request_cache:email:members:*")
    logger.info(f"清除了 {count} 个邮箱成员相关缓存")
    
    # 3. 清除所有缓存
    cache.clear_all()

def example_ttl_management():
    """TTL管理示例"""
    
    key = "test_key"
    cache.set(key, "test_value", ttl=300)
    
    # 获取剩余TTL
    remaining_ttl = cache.get_ttl(key)
    print(f"剩余TTL: {remaining_ttl}秒")
    
    # 重新设置TTL
    cache.set_ttl(key, 600)
    
    # 检查键是否存在
    if cache.exists(key):
        print("键存在")

def example_monitoring():
    """监控示例"""
    
    # 获取缓存信息
    cache_info = cache.get_cache_info("request_cache:*")
    print(f"请求缓存键总数: {cache_info['total_keys']}")
    print(f"按前缀分布: {cache_info['keys_by_prefix']}")
    
    # 获取Redis服务器指标
    metrics = cache.get_metrics()
    print(f"Redis连接数: {metrics.get('connected_clients', 0)}")
    print(f"内存使用: {metrics.get('used_memory_human', 'N/A')}")

def example_api_usage():
    """在API中的使用示例"""
    
    def get_users_api(page: int = 1, size: int = 20):
        """用户列表API示例"""
        
        # 构建缓存键
        cache_key = CacheKeyManager.build_request_key(
            module="user",
            operation="list",
            identifier=f"page_{page}_size_{size}"
        )
        
        # 尝试从缓存获取
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.info(f"从缓存获取用户列表: {cache_key}")
            return cached_result
        
        # 模拟数据库查询
        users = [{"id": i, "name": f"用户{i}"} for i in range((page-1)*size + 1, page*size + 1)]
        
        # 使用业务键缓存结果（自动选择15分钟TTL）
        cache.set_with_business_key(cache_key, users, "users")
        
        logger.info(f"查询数据库并缓存用户列表: {cache_key}")
        return users
    
    def update_user_api(user_id: int, user_data: dict):
        """用户更新API示例"""
        
        # 模拟更新数据库
        # update_user_in_db(user_id, user_data)
        
        # 清除相关缓存
        patterns = [
            "request_cache:user:list:*",  # 用户列表缓存
            f"request_cache:user:detail:{user_id}*",  # 特定用户详情缓存
            "request_cache:user:permissions:*"  # 用户权限相关缓存
        ]
        
        for pattern in patterns:
            count = cache.clear_pattern(pattern)
            logger.info(f"清除缓存模式 {pattern}: {count} 个键")

if __name__ == "__main__":
    # 运行示例
    print("=== Redis缓存系统使用示例 ===")
    
    print("\n1. 基础使用")
    example_basic_usage()
    
    print("\n2. 批量操作")
    example_batch_operations()
    
    print("\n3. 缓存键管理")
    example_cache_key_management()
    
    print("\n4. 缓存失效")
    example_cache_invalidation()
    
    print("\n5. TTL管理")
    example_ttl_management()
    
    print("\n6. 监控功能")
    example_monitoring()
    
    print("\n7. API使用示例")
    example_api_usage() 