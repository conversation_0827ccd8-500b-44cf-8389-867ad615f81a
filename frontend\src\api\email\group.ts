import request from '@/utils/request'

// 获取群组列表
export const getGroups = (params?: { 
  page?: number
  size?: number
  search?: string 
}) => {
  return request.get('/email/groups', { params })
}

// 获取群组详情
export const getGroup = (groupid: string) => {
  return request.get(`/email/groups/${groupid}`)
}

// 创建群组
export const createGroup = (data: {
  groupid: string
  groupname: string
  userlist?: string
  groupdesc?: string
}, options?: { sync_to_api?: boolean }) => {
  return request.post('/email/groups', data, {
    params: options
  })
}

// 更新群组
export const updateGroup = (groupid: string, data: {
  groupname?: string
  userlist?: string
  groupdesc?: string
}, options?: { sync_to_api?: boolean }) => {
  return request.put(`/email/groups/${groupid}`, data, {
    params: options
  })
}

// 删除群组
export const deleteGroup = (groupid: string, options?: { sync_to_api?: boolean }) => {
  return request.delete(`/email/groups/${groupid}`, {
    params: options
  })
}

// 从API同步群组数据
export const syncGroupsFromApi = () => {
  return request.post('/email/sync/groups')
} 