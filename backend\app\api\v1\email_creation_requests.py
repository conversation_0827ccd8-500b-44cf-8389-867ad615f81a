"""
邮箱创建申请管理API接口
提供申请列表、审批、拒绝等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime

from app.database import get_db
from app.utils import get_current_user
from app.models.user import User
from app.models.email import EmailCreationRequest, EmailMember
from app.api import deps
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.schemas.email_personnel_sync import PersonnelChangeRecord, PersonnelChangeType
from pydantic import BaseModel

router = APIRouter()


class EmailCreationRequestResponse(BaseModel):
    """邮箱创建申请响应"""
    id: int
    job_number: str
    user_name: str
    dept_name: Optional[str]
    job_title_name: Optional[str]
    mobile: Optional[str]
    status: str
    reason: Optional[str]
    requested_by: str
    approved_by: Optional[str]
    approved_at: Optional[datetime]
    created_email: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ApprovalRequest(BaseModel):
    """审批请求"""
    action: str  # approve 或 reject
    notes: Optional[str] = None

class BatchApprovalRequest(BaseModel):
    """批量审批请求"""
    request_ids: List[int]
    action: str  # approve 或 reject
    notes: Optional[str] = None


class PaginatedRequestsResponse(BaseModel):
    """分页申请响应"""
    data: List[EmailCreationRequestResponse]
    total: int
    page: int
    size: int
    pages: int

@router.get("/requests", response_model=PaginatedRequestsResponse)
async def get_creation_requests(
    status: Optional[str] = Query(None, description="申请状态过滤"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取邮箱创建申请列表"""
    try:
        base_query = db.query(EmailCreationRequest)
        
        if status:
            base_query = base_query.filter(EmailCreationRequest.status == status)
        
        # 获取总数
        total = base_query.count()
        
        # 获取分页数据
        requests = base_query.order_by(EmailCreationRequest.created_at.desc()).offset(skip).limit(limit).all()
        
        # 计算分页信息
        page = (skip // limit) + 1
        pages = (total + limit - 1) // limit
        
        return PaginatedRequestsResponse(
            data=requests,
            total=total,
            page=page,
            size=limit,
            pages=pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取申请列表失败: {str(e)}"
        )


@router.get("/requests/stats")
async def get_requests_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取申请统计信息"""
    try:
        stats = {}

        # 各状态的申请数量
        for status_name in ["pending", "approved", "rejected", "created"]:
            count = db.query(EmailCreationRequest).filter(EmailCreationRequest.status == status_name).count()
            stats[f"{status_name}_count"] = count

        # 总申请数
        stats["total_requests"] = db.query(EmailCreationRequest).count()

        # 当前邮箱使用情况
        active_email_count = db.query(EmailMember).filter(EmailMember.is_active == True).count()
        stats["active_email_count"] = active_email_count
        stats["email_quota"] = 1000
        stats["available_quota"] = 1000 - active_email_count

        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.get("/requests/{request_id}", response_model=EmailCreationRequestResponse)
async def get_creation_request_detail(
    request_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取邮箱创建申请详情"""
    try:
        request = db.query(EmailCreationRequest).filter(EmailCreationRequest.id == request_id).first()

        if not request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="申请记录不存在"
            )

        return request

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取申请详情失败: {str(e)}"
        )


@router.post("/requests/{request_id}/approve")
async def approve_creation_request(
    request_id: int,
    approval: ApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """审批邮箱创建申请"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        request = db.query(EmailCreationRequest).filter(EmailCreationRequest.id == request_id).first()
        
        if not request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="申请记录不存在"
            )
        
        if request.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"申请状态为 {request.status}，无法审批"
            )
        
        if approval.action == "approve":
            # 检查邮箱名额限制
            active_email_count = db.query(EmailMember).filter(EmailMember.is_active == True).count()
            if active_email_count >= 1000:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱名额已满（1000个），无法创建新账号"
                )
            
            # 检查工号是否已经有邮箱
            existing_member = db.query(EmailMember).filter(EmailMember.extid == request.job_number).first()
            if existing_member:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="该工号已存在邮箱账号"
                )
            
            # 批准申请
            request.status = "approved"
            request.approved_by = current_user.username
            request.approved_at = datetime.now()
            request.notes = approval.notes
            
            # 创建邮箱账号
            sync_service = PersonnelEmailSyncService(db)
            
            # 构造变更记录
            personnel_data = {
                "user_name": request.user_name,
                "job_number": request.job_number,
                "dept_name": request.dept_name,
                "job_title_name": request.job_title_name,
                "mobile": request.mobile
            }
            
            change_record = PersonnelChangeRecord(
                change_type=PersonnelChangeType.CREATE,
                job_number=request.job_number,
                personnel_data=personnel_data,
                email_data=None,
                reason="管理员审批通过"
            )
            
            # 执行创建操作
            result = await sync_service._create_email_user(change_record, dry_run=False)
            
            if result.success:
                request.status = "created"
                request.created_email = sync_service._generate_email_address(request.user_name, request.job_number)
                db.commit()
                
                return {
                    "message": "申请审批通过，邮箱账号创建成功",
                    "created_email": request.created_email
                }
            else:
                request.status = "approved"  # 保持审批状态，但创建失败
                request.notes = f"{approval.notes or ''}\n创建失败: {result.error_message}"
                db.commit()
                
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"邮箱账号创建失败: {result.error_message}"
                )
        
        elif approval.action == "reject":
            # 拒绝申请
            request.status = "rejected"
            request.approved_by = current_user.username
            request.approved_at = datetime.now()
            request.notes = approval.notes
            db.commit()
            
            return {"message": "申请已拒绝"}
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的审批操作，只支持 approve 或 reject"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"审批失败: {str(e)}"
        )





@router.delete("/requests/{request_id}")
async def delete_creation_request(
    request_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """删除邮箱创建申请"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        request = db.query(EmailCreationRequest).filter(EmailCreationRequest.id == request_id).first()
        
        if not request:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="申请记录不存在"
            )
        
        if request.status == "created":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="已创建的申请不能删除"
            )
        
        db.delete(request)
        db.commit()
        
        return {"message": "申请记录已删除"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除申请失败: {str(e)}"
        )


@router.post("/requests/batch-approve")
async def batch_approve_requests(
    approval: BatchApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """批量审批邮箱创建申请"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        if not approval.request_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请提供要操作的申请ID列表"
            )
        
        if approval.action not in ["approve", "reject"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的审批操作，只支持 approve 或 reject"
            )
        
        # 获取所有申请
        requests = db.query(EmailCreationRequest).filter(
            EmailCreationRequest.id.in_(approval.request_ids)
        ).all()
        
        if not requests:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到指定的申请记录"
            )
        
        # 检查申请状态
        pending_requests = [req for req in requests if req.status == "pending"]
        if len(pending_requests) != len(requests):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能审批状态为 pending 的申请"
            )
        
        success_count = 0
        failed_count = 0
        failed_details = []
        
        for request in pending_requests:
            try:
                if approval.action == "approve":
                    # 检查邮箱名额限制
                    active_email_count = db.query(EmailMember).filter(EmailMember.is_active == True).count()
                    if active_email_count >= 1000:
                        failed_count += 1
                        failed_details.append(f"申请ID {request.id}: 邮箱名额已满")
                        continue
                    
                    # 检查工号是否已经有邮箱
                    existing_member = db.query(EmailMember).filter(EmailMember.extid == request.job_number).first()
                    if existing_member:
                        failed_count += 1
                        failed_details.append(f"申请ID {request.id}: 工号已存在邮箱账号")
                        continue
                    
                    # 批准申请
                    request.status = "approved"
                    request.approved_by = current_user.username
                    request.approved_at = datetime.now()
                    request.notes = approval.notes
                    
                    # 创建邮箱账号
                    sync_service = PersonnelEmailSyncService(db)
                    
                    # 构造变更记录
                    personnel_data = {
                        "user_name": request.user_name,
                        "job_number": request.job_number,
                        "dept_name": request.dept_name,
                        "job_title_name": request.job_title_name,
                        "mobile": request.mobile
                    }
                    
                    change_record = PersonnelChangeRecord(
                        change_type=PersonnelChangeType.CREATE,
                        job_number=request.job_number,
                        personnel_data=personnel_data,
                        email_data=None,
                        reason="管理员批量审批通过"
                    )
                    
                    # 执行创建操作
                    result = await sync_service._create_email_user(change_record, dry_run=False)
                    
                    if result.success:
                        request.status = "created"
                        request.created_email = sync_service._generate_email_address(request.user_name, request.job_number)
                        success_count += 1
                    else:
                        request.status = "approved"  # 保持审批状态，但创建失败
                        request.notes = f"{approval.notes or ''}\n创建失败: {result.error_message}"
                        failed_count += 1
                        failed_details.append(f"申请ID {request.id}: 邮箱创建失败 - {result.error_message}")
                
                elif approval.action == "reject":
                    # 拒绝申请
                    request.status = "rejected"
                    request.approved_by = current_user.username
                    request.approved_at = datetime.now()
                    request.notes = approval.notes
                    success_count += 1
                
                db.commit()
                
            except Exception as e:
                failed_count += 1
                failed_details.append(f"申请ID {request.id}: {str(e)}")
                continue
        
        result_message = f"批量{approval.action}完成: 成功 {success_count} 个，失败 {failed_count} 个"
        if failed_details:
            result_message += f"\n失败详情: {'; '.join(failed_details)}"
        
        return {
            "message": result_message,
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_details": failed_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量审批失败: {str(e)}"
        )
