#!/usr/bin/env python3
"""
快速测试后端服务器状态
"""
import requests
import sys

def test_server():
    """测试服务器连接"""
    base_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000", 
        "http://**************:8000"
    ]
    
    for url in base_urls:
        try:
            print(f"测试 {url}...")
            response = requests.get(url, timeout=5)
            print(f"✅ {url} - 状态码: {response.status_code}")
            print(f"   响应: {response.text[:100]}...")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"❌ {url} - 连接超时")
        except Exception as e:
            print(f"❌ {url} - 错误: {e}")
        print()
    
    # 测试API端点
    api_url = "http://**************:8000/api/v1/auth/ldap-configs"
    try:
        print(f"测试API端点: {api_url}")
        response = requests.get(api_url, timeout=5)
        print(f"✅ API测试 - 状态码: {response.status_code}")
        if response.status_code == 401:
            print("   (401未授权是正常的，说明API正在运行)")
    except Exception as e:
        print(f"❌ API测试失败: {e}")

if __name__ == "__main__":
    test_server() 