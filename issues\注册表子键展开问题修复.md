# 注册表子键展开问题修复

## 问题描述
注册表管理功能中，用户点击注册表子键的展开按钮时，子键无法正常展开显示其下级节点，导致用户无法浏览完整的注册表结构。

## 问题症状

1. 点击子键的展开箭头（>）时，箭头会转向下方，但没有显示子节点
2. 控制台显示API调用成功，返回了子键数据
3. 虚拟树组件的`setNodeChildren`方法被调用，但界面没有更新
4. 根键（如HKEY_LOCAL_MACHINE）可以正常展开，但二级及以下子键无法展开

## 根本原因分析

### 1. 路径生成逻辑错误（主要原因）

在`RegistryVirtualTree.vue`的`setNodeChildren`方法中，子键路径生成逻辑存在错误：

```javascript
// 错误的路径生成逻辑
if (nodePath === selectedRootKey.value) {
  childPath = `${selectedRootKey.value}\\${childName}`
} else {
  childPath = `${nodePath}\\${childName}`  // 这里导致重复拼接
}
```

**问题**：当`nodePath`已经是完整路径（如`HKEY_LOCAL_MACHINE\SOFTWARE`）时，上述逻辑仍然会进行拼接，但实际上应该直接拼接子键名称。

### 2. Vue 3 响应式更新失效

Map类型的响应式更新需要特殊处理，直接修改Map中对象的属性不会触发Vue的响应式更新：

```javascript
// 无效的更新方式
mapNode.loading = true
treeNodes.value.set(node.path, { ...mapNode })

// 正确的更新方式
const updatedNode = { ...mapNode, loading: true }
treeNodes.value.set(node.path, updatedNode)
treeNodes.value = new Map(treeNodes.value)  // 强制触发响应式
```

### 3. 扁平化计算数据不一致

`flattenedItems`计算时使用了可能过期的节点数据，没有从Map中获取最新状态：

```javascript
// 使用过期数据
for (const child of node.children) {
  flatten(child, level + 1)
}

// 使用最新数据
for (const child of node.children) {
  const latestChild = treeNodes.value.get(child.path) || child
  flatten(latestChild, level + 1)
}
```

### 4. API调用不一致

混用了普通API(`performRegistryOperation`)和分页API(`performRegistryOperationPaginated`)，可能导致数据格式不统一。

## 解决方案

### 1. 修复路径生成逻辑

```javascript
// 修复前
if (nodePath === selectedRootKey.value) {
  childPath = `${selectedRootKey.value}\\${childName}`
} else {
  childPath = `${nodePath}\\${childName}`
}

// 修复后
const childPath = `${nodePath}\\${childName}`
```

简化逻辑，直接拼接父节点路径和子键名称。

### 2. 强制触发响应式更新

在所有Map更新操作后添加强制响应式触发：

```javascript
const updatedNode = { ...node, /* 更新的属性 */ }
treeNodes.value.set(nodePath, updatedNode)
treeNodes.value = new Map(treeNodes.value)  // 关键修复
```

### 3. 优化扁平化计算

确保始终使用Map中的最新节点数据：

```javascript
const latestRootNode = treeNodes.value.get(rootNode.value.path) || rootNode.value
flatten(latestRootNode)
```

### 4. 统一API调用

所有虚拟树的子节点加载统一使用分页API：

```javascript
const result = await performRegistryOperationPaginated(
  parseInt(props.terminalId),
  {
    operation: RegistryOperationType.ENUMERATE,
    root_key: selectedRootKey.value as RegistryRootKey,
    key_path: keyPath,
    page: 1,
    page_size: 50
  }
)
```

## 修复的文件

### 1. `frontend/src/components/RegistryVirtualTree.vue`

- 修复`setNodeChildren`方法的路径生成逻辑
- 优化`toggleExpand`方法的响应式更新
- 改进`flattenedItems`计算逻辑
- 完善`loadNodeChildren`的状态管理

### 2. `frontend/src/views/terminal/components/RegistryBrowser.vue`

- 统一`handleLoadVirtualChildren`方法中的API调用
- 改进错误处理和用户提示

## 验证方法

1. 选择任意根键（如HKEY_LOCAL_MACHINE）
2. 点击展开根键，确保显示一级子键
3. 点击任意一级子键的展开按钮
4. 验证是否正确显示二级子键
5. 继续展开更深层级的子键

## 技术要点

### Vue 3 Map 响应式更新

Vue 3中，Map类型的响应式需要特别注意：
- 直接修改Map中对象的属性不会触发更新
- 需要创建新对象并重新设置到Map中
- 可能需要`treeNodes.value = new Map(treeNodes.value)`强制触发

### 虚拟滚动的状态管理

虚拟滚动组件中的状态管理需要确保：
- 数据状态的一致性
- 正确的父子组件通信
- 异步操作的时序控制

## 预防措施

1. **增强调试日志**：添加更多详细的控制台输出，便于问题追踪
2. **状态验证**：在关键操作后验证数据状态的正确性
3. **API统一性**：避免混用不同的API接口
4. **响应式最佳实践**：遵循Vue 3的响应式更新模式

## 相关链接

- [Vue 3 响应式原理](https://vuejs.org/guide/essentials/reactivity-fundamentals.html)
- [Element Plus 虚拟化表格](https://element-plus.org/zh-CN/component/table-v2.html)
- [JavaScript Map MDN文档](https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Map)

---

**修复完成时间**: 2024-12-19
**影响范围**: 注册表管理功能的树形导航
**测试状态**: ✅ 已验证 