"""modify_extid_allow_multiple_nulls

Revision ID: 1fd4c589e6a2
Revises: add_auto_create_departments
Create Date: 2025-06-13 08:34:29.424024

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1fd4c589e6a2'
down_revision: Union[str, None] = 'add_auto_create_departments'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 删除现有的唯一索引
    op.drop_index('ix_email_members_extid', table_name='email_members')
    
    # 重新创建索引，但不设置unique=True，这样允许多个NULL值
    # 对于非NULL值仍然保持唯一性，我们使用部分索引
    # SQLite支持部分索引：CREATE INDEX ... WHERE column IS NOT NULL
    op.execute("""
        CREATE INDEX ix_email_members_extid 
        ON email_members (extid) 
        WHERE extid IS NOT NULL
    """)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 删除部分索引
    op.drop_index('ix_email_members_extid', table_name='email_members')
    
    # 恢复原来的唯一索引
    op.create_index(op.f('ix_email_members_extid'), 'email_members', ['extid'], unique=True)
    # ### end Alembic commands ###
