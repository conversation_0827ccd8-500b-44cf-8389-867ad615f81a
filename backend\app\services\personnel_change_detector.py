"""
人员信息变更检测服务
用于检测人员信息的新增、修改、离职状态变更
支持增量同步和全量同步
"""

import logging
from typing import List, Dict, Optional, Tuple, Set
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember, EmailSyncLog
from app.schemas.email_personnel_sync import (
    PersonnelChangeType,
    PersonnelChangeRecord,
    PersonnelChangeDetectionResult
)

logger = logging.getLogger(__name__)


class PersonnelChangeDetector:
    """人员信息变更检测器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.filter_config = None
    
    def set_filter_config(self, filter_config):
        """设置过滤配置"""
        self.filter_config = filter_config
        logger.info(f"设置过滤配置: {filter_config}")
    
    def _build_personnel_filter_query(self):
        """构建人员过滤查询条件"""
        # 基础查询条件
        base_conditions = [
            EcologyUser.job_number.isnot(None),
            EcologyUser.job_number != "",
            EcologyUser.user_name.isnot(None),
            EcologyUser.user_name != ""
        ]
        
        # 如果没有启用过滤或没有过滤配置，返回基础条件
        if not self.filter_config or not getattr(self.filter_config, 'filter_enabled', False):
            return and_(*base_conditions)
        
        # 收集过滤条件
        filter_conditions = []
        
        # 公司过滤
        if hasattr(self.filter_config, 'included_companies') and self.filter_config.included_companies:
            company_conditions = []
            for company in self.filter_config.included_companies:
                company_conditions.append(EcologyUser.company_name.contains(company.strip()))
            if company_conditions:
                filter_conditions.append(or_(*company_conditions))
        
        # 部门过滤
        if hasattr(self.filter_config, 'included_departments') and self.filter_config.included_departments:
            dept_conditions = []
            for dept in self.filter_config.included_departments:
                dept_conditions.append(EcologyUser.dept_name.contains(dept.strip()))
            if dept_conditions:
                filter_conditions.append(or_(*dept_conditions))
        
        # 职位包含过滤
        if hasattr(self.filter_config, 'included_job_titles') and self.filter_config.included_job_titles:
            job_include_conditions = []
            for job_title in self.filter_config.included_job_titles:
                job_include_conditions.append(EcologyUser.job_title_name.contains(job_title.strip()))
            if job_include_conditions:
                filter_conditions.append(or_(*job_include_conditions))
        
        # 职位排除过滤
        if hasattr(self.filter_config, 'excluded_job_titles') and self.filter_config.excluded_job_titles:
            job_exclude_conditions = []
            for job_title in self.filter_config.excluded_job_titles:
                job_exclude_conditions.append(~EcologyUser.job_title_name.contains(job_title.strip()))
            if job_exclude_conditions:
                filter_conditions.append(and_(*job_exclude_conditions))
        
        # 组合过滤条件
        if filter_conditions:
            filter_logic = getattr(self.filter_config, 'filter_logic', 'AND')
            if filter_logic == 'OR':
                final_filter = or_(*filter_conditions)
            else:  # 默认使用AND逻辑
                final_filter = and_(*filter_conditions)
            
            # 将基础条件和过滤条件结合
            return and_(*base_conditions, final_filter)
        else:
            return and_(*base_conditions)
    
    def _apply_filter_to_personnel_list(self, personnel_list):
        """对人员列表应用过滤逻辑（用于增量同步的额外过滤）"""
        if not self.filter_config or not getattr(self.filter_config, 'filter_enabled', False):
            return personnel_list
        
        filtered_list = []
        
        for person in personnel_list:
            should_include = True
            
            # 应用公司过滤
            if hasattr(self.filter_config, 'included_companies') and self.filter_config.included_companies:
                company_match = False
                for company in self.filter_config.included_companies:
                    if person.company_name and company.strip() in person.company_name:
                        company_match = True
                        break
                if not company_match:
                    should_include = False
            
            # 应用部门过滤
            if should_include and hasattr(self.filter_config, 'included_departments') and self.filter_config.included_departments:
                dept_match = False
                for dept in self.filter_config.included_departments:
                    if person.dept_name and dept.strip() in person.dept_name:
                        dept_match = True
                        break
                if not dept_match:
                    should_include = False
            
            # 应用职位包含过滤
            if should_include and hasattr(self.filter_config, 'included_job_titles') and self.filter_config.included_job_titles:
                job_include_match = False
                for job_title in self.filter_config.included_job_titles:
                    if person.job_title_name and job_title.strip() in person.job_title_name:
                        job_include_match = True
                        break
                if not job_include_match:
                    should_include = False
            
            # 应用职位排除过滤
            if should_include and hasattr(self.filter_config, 'excluded_job_titles') and self.filter_config.excluded_job_titles:
                for job_title in self.filter_config.excluded_job_titles:
                    if person.job_title_name and job_title.strip() in person.job_title_name:
                        should_include = False
                        break
            
            if should_include:
                filtered_list.append(person)
        
        logger.info(f"过滤前人员数量: {len(personnel_list)}, 过滤后人员数量: {len(filtered_list)}")
        return filtered_list
    
    def detect_changes(self, 
                      full_sync: bool = False,
                      since_time: Optional[datetime] = None) -> PersonnelChangeDetectionResult:
        """
        检测人员信息变更
        
        Args:
            full_sync: 是否全量同步
            since_time: 增量同步的起始时间
            
        Returns:
            PersonnelChangeDetectionResult: 变更检测结果
        """
        logger.info(f"开始检测人员信息变更，全量同步: {full_sync}, 起始时间: {since_time}")
        
        try:
            if full_sync:
                return self._detect_full_changes()
            else:
                return self._detect_incremental_changes(since_time)
                
        except Exception as e:
            logger.error(f"检测人员信息变更失败: {str(e)}", exc_info=True)
            return PersonnelChangeDetectionResult(
                success=False,
                error_message=f"检测变更失败: {str(e)}",
                changes=[]
            )
    
    def _detect_full_changes(self) -> PersonnelChangeDetectionResult:
        """检测全量变更"""
        logger.info("执行全量变更检测")
        
        changes = []
        
        # 获取所有有工号的人员信息
        personnel_list = self.db.query(EcologyUser).filter(
            self._build_personnel_filter_query()
        ).all()
        
        logger.info(f"找到 {len(personnel_list)} 条有效人员信息")
        
        # 获取所有邮箱成员的工号集合
        existing_extids = set()
        email_members = self.db.query(EmailMember).filter(
            and_(
                EmailMember.extid.isnot(None),
                EmailMember.extid != ""
            )
        ).all()
        
        for member in email_members:
            existing_extids.add(member.extid)
        
        logger.info(f"现有邮箱成员工号数量: {len(existing_extids)}")
        
        # 检测变更
        for person in personnel_list:
            job_number = person.job_number.strip() if person.job_number else ""
            if not job_number:
                continue
                
            # 查找对应的邮箱成员
            email_member = self.db.query(EmailMember).filter(
                EmailMember.extid == job_number
            ).first()
            
            if not email_member:
                # 检测到需要创建邮箱的用户，但不自动创建，需要管理员审批
                # 只有在职员工才考虑创建邮箱
                if not self._is_departed(person):
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.CREATE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=None,
                        reason=f"在职员工缺少邮箱账号，需要管理员审批创建（状态：{person.status}）"
                    ))
            else:
                # 检查是否需要更新
                if self._need_update(person, email_member):
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.UPDATE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=self._convert_email_member_to_dict(email_member),
                        reason="人员信息与邮箱信息不一致"
                    ))
                
                # 检查离职状态
                if self._is_departed(person) and email_member.is_active:
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.DISABLE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=self._convert_email_member_to_dict(email_member),
                        reason=f"人员状态为离职: {person.status}"
                    ))
        
        logger.info(f"检测到 {len(changes)} 项变更")
        
        return PersonnelChangeDetectionResult(
            success=True,
            changes=changes,
            total_personnel=len(personnel_list),
            total_email_members=len(existing_extids)
        )
    
    def _detect_incremental_changes(self, since_time: Optional[datetime]) -> PersonnelChangeDetectionResult:
        """检测增量变更"""
        if not since_time:
            # 如果没有指定时间，使用最后一次同步时间
            last_sync_log = self.db.query(EmailSyncLog).filter(
                EmailSyncLog.sync_category == "personnel",
                EmailSyncLog.status == "success"
            ).order_by(EmailSyncLog.completed_at.desc()).first()
            
            if last_sync_log and last_sync_log.completed_at:
                since_time = last_sync_log.completed_at
            else:
                # 如果没有成功的同步记录，回退到全量同步
                logger.warning("没有找到上次同步时间，执行全量同步")
                return self._detect_full_changes()
        
        logger.info(f"执行增量变更检测，起始时间: {since_time}")
        
        changes = []
        
        # 获取指定时间后更新的人员信息
        updated_personnel = self.db.query(EcologyUser).filter(
            and_(
                EcologyUser.updated_at >= since_time,
                self._build_personnel_filter_query()
            )
        ).all()
        
        logger.info(f"找到 {len(updated_personnel)} 条更新的人员信息")
        
        for person in updated_personnel:
            job_number = person.job_number.strip() if person.job_number else ""
            if not job_number:
                continue
                
            # 查找对应的邮箱成员
            email_member = self.db.query(EmailMember).filter(
                EmailMember.extid == job_number
            ).first()
            
            if not email_member:
                # 检测到需要创建邮箱的用户，但不自动创建，需要管理员审批
                # 只有在职员工才考虑创建邮箱
                if not self._is_departed(person):
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.CREATE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=None,
                        reason=f"在职员工缺少邮箱账号，需要管理员审批创建（状态：{person.status}）"
                    ))
            else:
                # 检查是否需要更新
                if self._need_update(person, email_member):
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.UPDATE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=self._convert_email_member_to_dict(email_member),
                        reason="人员信息与邮箱信息不一致"
                    ))
                
                # 检查离职状态
                if self._is_departed(person) and email_member.is_active:
                    changes.append(PersonnelChangeRecord(
                        change_type=PersonnelChangeType.DISABLE,
                        job_number=job_number,
                        personnel_data=self._convert_personnel_to_dict(person),
                        email_data=self._convert_email_member_to_dict(email_member),
                        reason=f"人员状态为离职: {person.status}"
                    ))
        
        logger.info(f"检测到 {len(changes)} 项变更")
        
        return PersonnelChangeDetectionResult(
            success=True,
            changes=changes,
            total_personnel=len(updated_personnel),
            since_time=since_time.isoformat() if since_time else None
        )
    
    def _need_update(self, person: EcologyUser, email_member: EmailMember) -> bool:
        """检查是否需要更新邮箱用户信息"""
        # 检查姓名
        if person.user_name != email_member.name:
            return True
            
        # 检查手机号
        if person.mobile != email_member.mobile:
            return True
            
        # 检查职位
        if person.job_title_name != email_member.position:
            return True
            
        return False
    
    def _is_departed(self, person: EcologyUser) -> bool:
        """检查人员是否已离职"""
        if not person.status:
            return False
            
        # 离职状态包括：解聘、离职、退休、无效
        departed_statuses = ["解聘", "离职", "退休", "无效"]
        return person.status in departed_statuses
    
    def _convert_personnel_to_dict(self, person: EcologyUser) -> Dict:
        """将人员信息转换为字典"""
        return {
            "user_id": person.user_id,
            "user_name": person.user_name,
            "job_number": person.job_number,
            "dept_name": person.dept_name,
            "job_title_name": person.job_title_name,
            "mobile": person.mobile,
            "email": person.email,
            "status": person.status,
            "updated_at": person.updated_at.isoformat() if person.updated_at else None
        }
    
    def _convert_email_member_to_dict(self, member: EmailMember) -> Dict:
        """将邮箱成员信息转换为字典"""
        return {
            "id": member.id,
            "extid": member.extid,
            "email": member.email,
            "name": member.name,
            "position": member.position,
            "mobile": member.mobile,
            "is_active": member.is_active,
            "updated_at": member.updated_at.isoformat() if member.updated_at else None
        }
