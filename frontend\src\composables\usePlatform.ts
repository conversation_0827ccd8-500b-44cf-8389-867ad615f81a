import { computed, ref } from 'vue'
import { useDevice } from './useDevice'

// 全局单例状态
let platformInstance: ReturnType<typeof createPlatformDetection> | null = null

// 创建平台检测实例
function createPlatformDetection() {
  const { isMobile, isTablet, isDesktop } = useDevice()
  
  // 平台类型
  const platform = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  // 是否应该使用移动端组件
  const shouldUseMobile = computed(() => {
    return isMobile.value || isTablet.value
  })

  return {
    platform,
    shouldUseMobile,
    isMobile,
    isTablet,
    isDesktop
  }
}

export const usePlatform = () => {
  // 单例模式：只创建一次平台检测实例
  if (!platformInstance) {
    platformInstance = createPlatformDetection()
  }

  return {
    platform: platformInstance.platform,
    shouldUseMobile: platformInstance.shouldUseMobile,
    isMobile: platformInstance.isMobile,
    isTablet: platformInstance.isTablet,
    isDesktop: platformInstance.isDesktop
  }
} 