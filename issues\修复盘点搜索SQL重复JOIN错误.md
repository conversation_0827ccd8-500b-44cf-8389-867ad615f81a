# 修复盘点搜索SQL重复JOIN错误

## 问题描述
用户在使用移动端资产盘点搜索功能时遇到SQL错误：
```
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.InvalidColumnReference) table name "assets" specified more than once
```

## 错误分析

### SQL错误详情
```sql
FROM inventory_records JOIN assets ON assets.id = inventory_records.asset_id 
JOIN assets ON assets.id = inventory_records.asset_id LEFT OUTER JOIN assets AS assets_1 ON assets_1.id = inventory_records.asset_id
```

### 问题根因
在`backend/app/crud/inventory.py`的`CRUDInventoryRecord.get_multi_by_task`方法中存在重复的`join(Asset)`操作：

1. **第一次JOIN**：搜索关键词时 `query.join(Asset)`
2. **第二次JOIN**：排序时 `query.join(Asset)`
3. **第三次JOIN**：`joinedload(self.model.asset)` eager loading

这导致SQL查询中出现多个重复的JOIN子句，引发PostgreSQL错误。

## 修复方案

### 解决思路
使用`asset_joined`标记变量来跟踪Asset表是否已经JOIN，避免重复JOIN操作。

### 具体修改

#### 修改文件：`backend/app/crud/inventory.py`

**原始代码问题**：
```python
def get_multi_by_task(self, ...):
    query = db.query(self.model).options(joinedload(self.model.asset)).filter(...)
    
    if filters:
        if field == "keyword":
            query = query.join(Asset).filter(...)  # 第一次JOIN
    
    # 默认按资产编号排序
    query = query.join(Asset).order_by(Asset.asset_number)  # 第二次JOIN - 重复！
```

**修复后代码**：
```python
def get_multi_by_task(self, ...):
    query = db.query(self.model).options(joinedload(self.model.asset)).filter(...)
    
    # 标记是否已经JOIN了Asset表
    asset_joined = False
    
    if filters:
        if field == "keyword":
            if not asset_joined:
                query = query.join(Asset)
                asset_joined = True
            query = query.filter(...)
    
    # 默认按资产编号排序，如果还没有JOIN Asset表则进行JOIN
    if not asset_joined:
        query = query.join(Asset)
    query = query.order_by(Asset.asset_number)
```

## 修复效果

### 解决的问题
1. ✅ 消除SQL重复JOIN错误
2. ✅ 保持搜索功能完整性
3. ✅ 保持排序功能正常
4. ✅ 维持查询性能

### 功能验证
- 盘点记录搜索功能正常工作
- 按资产编号、名称、保管人等字段搜索
- 搜索结果正确排序
- 分页功能正常

## 技术细节

### JOIN策略优化
1. **单次JOIN**：每个查询中Asset表只JOIN一次
2. **条件JOIN**：根据是否需要搜索决定是否JOIN
3. **标记追踪**：使用`asset_joined`变量避免重复
4. **性能保持**：不影响原有查询性能

### 兼容性保证
- 保持API接口不变
- 保持查询结果一致
- 保持错误处理逻辑
- 保持分页和排序功能

## 测试建议

### 功能测试
1. 测试无搜索条件的记录列表
2. 测试各种搜索关键词
3. 测试搜索+状态筛选组合
4. 测试分页功能
5. 测试排序功能

### 性能测试
1. 验证查询执行时间
2. 检查SQL执行计划
3. 监控数据库连接使用

## 相关文件
- `backend/app/crud/inventory.py` - 主要修复文件
- `frontend/src/mobile/views/asset/InventoryTask.vue` - 前端搜索界面
- `frontend/src/api/inventory.ts` - API调用接口

## 任务状态
- [x] 问题分析完成
- [x] 修复方案确定
- [x] 代码修改完成
- [ ] 功能测试验证
- [ ] 性能测试验证
- [ ] 部署到生产环境 