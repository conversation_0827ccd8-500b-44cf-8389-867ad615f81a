"""add_email_sync_lock_table

Revision ID: add_email_sync_lock_table
Revises: 1fd4c589e6a2
Create Date: 2025-06-17 17:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_email_sync_lock_table'
down_revision: Union[str, None] = '1fd4c589e6a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_sync_locks',
    sa.Column('lock_name', sa.String(), nullable=False),
    sa.Column('is_locked', sa.Bo<PERSON>an(), nullable=True),
    sa.Column('locked_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.String(), nullable=True),
    sa.Column('operation_type', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index(op.f('ix_email_sync_locks_lock_name'), 'email_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_email_sync_locks_lock_name'), table_name='email_sync_locks')
    op.drop_table('email_sync_locks')
    # ### end Alembic commands ### 