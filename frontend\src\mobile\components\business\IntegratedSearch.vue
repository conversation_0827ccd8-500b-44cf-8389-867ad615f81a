<template>
  <div class="integrated-search">
    <!-- 整合搜索容器 -->
    <div class="search-container">
      <!-- 搜索类型标签 -->
      <div 
        class="search-type-tag" 
        @click="showTypeSelector = true"
      >
        <van-icon :name="getCurrentFieldIcon()" class="tag-field-icon" />
        <span class="tag-text">{{ getCurrentFieldLabel() }}</span>
        <van-icon name="arrow-down" class="tag-arrow-icon" />
      </div>
      
      <!-- 搜索输入框 -->
      <van-search
        v-model="searchValue"
        :placeholder="getPlaceholder()"
        clearable
        @search="handleSearch"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="handleClear"
        class="search-input"
        show-action
        action-text=""
      />
    </div>
    
    <!-- 搜索类型选择器 -->
    <van-action-sheet
      v-model:show="showTypeSelector"
      title="选择搜索类型"
      :actions="searchFieldActions"
      @select="handleFieldSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'

interface Props {
  modelValue?: string
  searchField?: string
  placeholder?: string
  debounceTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  searchField: 'asset_number',
  placeholder: '',
  debounceTime: 300
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:searchField': [field: string]
  'search': [value: string, field: string]
  'input': [value: string, field: string]
  'focus': []
  'blur': []
  'clear': []
}>()

// 响应式数据
const searchValue = ref(props.modelValue)
const searchField = ref(props.searchField)
const debounceTimer = ref<NodeJS.Timeout | null>(null)
const showTypeSelector = ref(false)

// 搜索字段选项
const searchFieldOptions = [
  { text: '资产编号', value: 'asset_number', color: '#1989fa', icon: 'qr' },
  { text: '资产名称', value: 'name', color: '#07c160', icon: 'label-o' },
  { text: '资产类别', value: 'category', color: '#ff976a', icon: 'apps-o' },
  { text: '规格型号', value: 'specification', color: '#7232dd', icon: 'description' },
  { text: '姓名', value: 'person_name', color: '#ee0a24', icon: 'contact' },
  { text: '工号', value: 'job_number', color: '#ff8f1f', icon: 'id-card' },
  { text: '部门', value: 'department', color: '#323233', icon: 'cluster-o' }
]

// 转换为action-sheet格式的选项
const searchFieldActions = computed(() => {
  return searchFieldOptions.map(option => ({
    name: option.text,
    value: option.value,
    color: option.color
  }))
})

// 获取字段标签
const getFieldLabel = (field: string) => {
  const option = searchFieldOptions.find(opt => opt.value === field)
  return option ? option.text : field
}

// 获取当前字段标签
const getCurrentFieldLabel = () => {
  return getFieldLabel(searchField.value)
}

// 获取当前字段颜色
const getCurrentFieldColor = () => {
  const option = searchFieldOptions.find(opt => opt.value === searchField.value)
  return option ? option.color : '#1989fa'
}

// 获取当前字段图标
const getCurrentFieldIcon = () => {
  const option = searchFieldOptions.find(opt => opt.value === searchField.value)
  return option ? option.icon : 'search'
}

// 获取占位符文本
const getPlaceholder = () => {
  if (props.placeholder) return props.placeholder
  
  const fieldLabel = getFieldLabel(searchField.value)
  return `请输入${fieldLabel}...`
}

// 监听搜索值变化
watch(searchValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听搜索字段变化
watch(searchField, (newVal) => {
  emit('update:searchField', newVal)
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  searchValue.value = newVal
})

// 监听外部搜索字段变化
watch(() => props.searchField, (newVal) => {
  searchField.value = newVal
})

// 处理字段选择
const handleFieldSelect = (action: any) => {
  searchField.value = action.value
  showTypeSelector.value = false
  
  // 如果有搜索内容，重新搜索
  if (searchValue.value.trim()) {
    handleSearch(searchValue.value.trim())
  }
}

// 处理输入事件（实时搜索）
const handleInput = (value: string) => {
  emit('input', value, searchField.value)
  
  // 清除之前的定时器
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value)
  }
  
  // 设置防抖
  debounceTimer.value = setTimeout(() => {
    if (value.trim()) {
      handleSearch(value.trim())
    } else {
      // 如果搜索框为空，触发搜索以显示所有结果
      emit('search', '', searchField.value)
    }
  }, props.debounceTime)
}

// 处理搜索事件
const handleSearch = (value: string) => {
  const trimmedValue = value.trim()
  emit('search', trimmedValue, searchField.value)
}

// 处理焦点事件
const handleFocus = () => {
  emit('focus')
}

// 处理失焦事件
const handleBlur = () => {
  emit('blur')
}

// 处理清空事件
const handleClear = () => {
  emit('clear')
  emit('search', '', searchField.value) // 清空时显示所有结果
}
</script>

<style lang="scss" scoped>
.integrated-search {
  background: var(--van-background);
}

.search-container {
  display: flex;
  align-items: center;
  background: #f7f8fa;
  border-radius: 8px;
  border: 1px solid #ebedf0;
  height: 40px;
  overflow: hidden;
  box-sizing: border-box;
}

.search-type-tag {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 100%;
  background: v-bind(getCurrentFieldColor());
  color: white;
  cursor: pointer;
  flex-shrink: 0;
  user-select: none;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 60%;
    background: rgba(255, 255, 255, 0.3);
  }
  
  .tag-field-icon {
    font-size: 14px;
    margin-right: 6px;
  }
  
  .tag-text {
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    margin-right: 6px;
  }
  
  .tag-arrow-icon {
    font-size: 12px;
    transition: transform 0.2s ease;
  }
  
  &:active {
    opacity: 0.8;
  }
}

.search-input {
  flex: 1;
  height: 100%;
  
  :deep(.van-search) {
    padding: 0;
    background: transparent;
    height: 100%;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-search__content) {
    background: transparent;
    border-radius: 0;
    height: 100%;
    border: none;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin: 0;
    padding: 0 12px;
  }
  
  :deep(.van-field) {
    background: transparent;
    padding: 0;
    height: 100%;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-field__body) {
    height: 100%;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-field__control) {
    height: 100%;
    line-height: 40px;
    font-size: 14px;
    padding: 0;
    border: none;
    background: transparent;
    color: var(--van-text-color);
    
    &::placeholder {
      color: var(--van-text-color-3);
    }
  }
  
  :deep(.van-field__left-icon) {
    margin-right: 8px;
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--van-text-color-3);
  }
  
  :deep(.van-field__right-icon) {
    margin-left: 8px;
    display: flex;
    align-items: center;
    height: 100%;
    color: var(--van-text-color-3);
  }
  
  :deep(.van-search__action) {
    display: none; // 隐藏默认搜索按钮
  }
}

// 响应式适配
@media (max-width: 480px) {
  .search-type-tag {
    padding: 0 8px;
    
    .tag-text {
      font-size: 12px;
    }
  }
  
  .search-input {
    :deep(.van-search__content) {
      padding: 0 8px;
    }
    
    :deep(.van-field__control) {
      font-size: 13px;
    }
  }
}

// 动画效果
.search-type-tag {
  transition: all 0.2s ease;
  
  &:hover {
    .tag-arrow-icon {
      transform: rotate(180deg);
    }
  }
}

// Action Sheet 自定义样式
:deep(.van-action-sheet) {
  .van-action-sheet__item {
    font-weight: 500;
    
    &:active {
      background: var(--van-active-color);
    }
  }
}
</style> 