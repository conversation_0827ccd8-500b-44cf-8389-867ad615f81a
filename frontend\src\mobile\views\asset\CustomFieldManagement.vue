<template>
  <div class="mobile-custom-field-management">
    <!-- 导航栏 -->
    <van-nav-bar
      title="自定义字段管理"
      left-arrow
      @click-left="$router.go(-1)"
    >
      <template #right>
        <van-icon name="plus" @click="handleAdd" />
      </template>
    </van-nav-bar>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <van-search
        v-model="searchForm.keyword"
        placeholder="搜索字段名称或标签"
        @search="handleSearch"
        @clear="handleSearch"
        show-action
        @cancel="handleSearch"
      />
      
      <van-row :gutter="12" class="filter-row">
        <van-col :span="12">
          <van-field
            v-model="fieldTypeDisplay"
            is-link
            readonly
            label="字段类型"
            placeholder="全部类型"
            @click="showFieldTypePicker = true"
          />
        </van-col>
        <van-col :span="12">
          <van-field
            v-model="appliesToDisplay"
            is-link
            readonly
            label="适用范围"
            placeholder="全部范围"
            @click="showAppliesToPicker = true"
          />
        </van-col>
      </van-row>
    </div>

    <!-- 字段列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多数据了"
        @load="onLoad"
      >
        <div v-if="fieldList.length === 0 && !loading" class="empty-state">
          <van-empty description="暂无自定义字段">
            <van-button type="primary" @click="handleAdd">
              创建第一个字段
            </van-button>
          </van-empty>
        </div>
        
        <van-swipe-cell
          v-for="item in fieldList"
          :key="item.id"
          :before-close="(params: any) => beforeClose(params, item)"
        >
          <van-cell
            :title="item.label"
            :label="`${item.name} | ${getFieldTypeLabel(item.field_type)}`"
            @click="handleEdit(item)"
          >
            <template #value>
              <div class="field-info">
                <van-tag 
                  :type="getFieldTypeColor(item.field_type)" 
                  size="medium"
                >
                  {{ getFieldTypeLabel(item.field_type) }}
                </van-tag>
                <van-tag 
                  :type="getAppliesToColor(item.applies_to)" 
                  size="medium"
                  class="ml-1"
                >
                  {{ getAppliesToLabel(item.applies_to) }}
                </van-tag>
                <div class="field-status">
                  <van-switch
                    v-model="item.is_active"
                    size="small"
                    @click.stop
                    @change="(value: boolean) => handleToggleStatus(item, value)"
                  />
                </div>
              </div>
            </template>
          </van-cell>
          
          <template #right>
            <van-button 
              square 
              type="danger" 
              text="删除"
              @click="confirmDelete(item)"
            />
          </template>
        </van-swipe-cell>
      </van-list>
    </van-pull-refresh>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAdd"
    />

    <!-- 字段类型选择器 -->
    <van-popup v-model:show="showFieldTypePicker" position="bottom">
      <van-picker
        :columns="fieldTypeOptions"
        @confirm="onFieldTypeConfirm"
        @cancel="showFieldTypePicker = false"
      />
    </van-popup>

    <!-- 适用范围选择器 -->
    <van-popup v-model:show="showAppliesToPicker" position="bottom">
      <van-picker
        :columns="appliesToOptions"
        @confirm="onAppliesToConfirm"
        @cancel="showAppliesToPicker = false"
      />
    </van-popup>

    <!-- 字段编辑表单弹窗 -->
    <van-popup 
      v-model:show="showForm" 
      position="bottom" 
      :style="{ height: 'var(--mobile-popup-max-height, 68svh)' }"
      round
    >
      <div class="form-container">
        <van-nav-bar
          :title="formMode === 'create' ? '新增字段' : '编辑字段'"
          left-text="取消"
          @click-left="showForm = false"
        />
        
        <van-form ref="formRef" @submit="handleSubmit">
          <van-cell-group inset>
            <van-field
              v-model="formData.name"
              name="name"
              label="字段名称"
              placeholder="请输入字段名称（英文）"
              :rules="[
                { required: true, message: '请输入字段名称' },
                { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '只能包含字母、数字和下划线，且以字母或下划线开头' }
              ]"
              :disabled="formMode === 'edit'"
            />
            
            <van-field
              v-model="formData.label"
              name="label"
              label="显示标签"
              placeholder="请输入显示标签（中文）"
              :rules="[{ required: true, message: '请输入显示标签' }]"
            />
            
            <van-field
              v-model="fieldTypeFormDisplay"
              is-link
              readonly
              name="field_type"
              label="字段类型"
              placeholder="请选择字段类型"
              :rules="[{ required: true, message: '请选择字段类型' }]"
              @click="showFormFieldTypePicker = true"
            />
            
            <van-field
              v-model="appliesToFormDisplay"
              is-link
              readonly
              name="applies_to"
              label="适用范围"
              placeholder="请选择适用范围"
              :rules="[{ required: true, message: '请选择适用范围' }]"
              @click="showFormAppliesToPicker = true"
            />
            
            <van-field
              v-model="formData.description"
              name="description"
              label="字段描述"
              type="textarea"
              placeholder="请输入字段描述（可选）"
              :rows="3"
              autosize
              show-word-limit
              :maxlength="200"
            />
            
            <van-field
              v-model="formData.default_value"
              name="default_value"
              label="默认值"
              placeholder="请输入默认值（可选）"
            />
            
            <!-- 选择类型字段的选项配置 -->
            <template v-if="formData.field_type === 'select' || formData.field_type === 'radio' || formData.field_type === 'checkbox'">
              <van-divider>选项配置</van-divider>
              <div class="options-section">
                <div
                  v-for="(option, index) in formData.options"
                  :key="index"
                  class="option-item"
                >
                  <van-field
                    v-model="option.value"
                    :name="`option_value_${index}`"
                    label="选项值"
                    placeholder="选项值"
                    :rules="[{ required: true, message: '请输入选项值' }]"
                  />
                  <van-field
                    v-model="option.label"
                    :name="`option_label_${index}`"
                    label="显示文本"
                    placeholder="显示文本"
                    :rules="[{ required: true, message: '请输入显示文本' }]"
                  />
                  <van-button
                    type="danger"
                    size="small"
                    @click="removeOption(index)"
                  >
                    删除
                  </van-button>
                </div>
                <van-button
                  type="primary"
                  size="small"
                  @click="addOption"
                >
                  添加选项
                </van-button>
              </div>
            </template>
            
            <van-field name="is_required">
              <template #input>
                <van-switch v-model="formData.is_required" />
              </template>
              <template #label>
                <span>必填字段</span>
              </template>
            </van-field>
            
            <van-field name="is_active">
              <template #input>
                <van-switch v-model="formData.is_active" />
              </template>
              <template #label>
                <span>启用状态</span>
              </template>
            </van-field>
          </van-cell-group>
        </van-form>
        
        <MobilePopupFooter :buttons="formFooterButtons" />
      </div>
    </van-popup>

    <!-- 表单内字段类型选择器 -->
    <van-popup v-model:show="showFormFieldTypePicker" position="bottom">
      <van-picker
        :columns="fieldTypeOptions"
        @confirm="onFormFieldTypeConfirm"
        @cancel="showFormFieldTypePicker = false"
      />
    </van-popup>

    <!-- 表单内适用范围选择器 -->
    <van-popup v-model:show="showFormAppliesToPicker" position="bottom">
      <van-picker
        :columns="appliesToOptions"
        @confirm="onFormAppliesToConfirm"
        @cancel="showFormAppliesToPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { customFieldApi } from '@/api/custom_field'
import type { CustomField, CustomFieldCreate, CustomFieldUpdate } from '@/types/custom_field'
import { FIELD_TYPE_OPTIONS, APPLIES_TO_OPTIONS } from '@/types/custom_field'
import type { TagType, TagSize } from '@/types/vant-extend'
import MobilePopupFooter from '@/mobile/components/MobilePopupFooter.vue'

interface SearchForm {
  keyword: string
  field_type: string
  applies_to: string
}

interface FormData {
  name: string
  label: string
  field_type: string
  applies_to: string
  description: string
  default_value: string
  is_required: boolean
  is_active: boolean
  options: Array<{ value: string; label: string }>
}

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const fieldList = ref<CustomField[]>([])
const currentPage = ref(1)
const pageSize = 20

// 搜索和筛选
const searchForm = reactive<SearchForm>({
  keyword: '',
  field_type: '',
  applies_to: ''
})

// 弹窗状态
const showFieldTypePicker = ref(false)
const showAppliesToPicker = ref(false)
const showForm = ref(false)
const showFormFieldTypePicker = ref(false)
const showFormAppliesToPicker = ref(false)

// 表单数据
const formMode = ref<'create' | 'edit'>('create')
const formRef = ref()
const currentField = ref<CustomField | null>(null)
const formData = reactive<FormData>({
  name: '',
  label: '',
  field_type: '',
  applies_to: '',
  description: '',
  default_value: '',
  is_required: false,
  is_active: true,
  options: []
})

// 选择器选项
const fieldTypeOptions = FIELD_TYPE_OPTIONS.map(item => ({
  text: item.label,
  value: item.value
}))

const appliesToOptions = [
  { text: '全部范围', value: '' },
  ...APPLIES_TO_OPTIONS.map(item => ({
    text: item.label,
    value: item.value
  }))
]

// 显示值计算
const fieldTypeDisplay = computed(() => {
  if (!searchForm.field_type) return ''
  const option = FIELD_TYPE_OPTIONS.find(item => item.value === searchForm.field_type)
  return option?.label || ''
})

const appliesToDisplay = computed(() => {
  if (!searchForm.applies_to) return ''
  const option = APPLIES_TO_OPTIONS.find(item => item.value === searchForm.applies_to)
  return option?.label || ''
})

const fieldTypeFormDisplay = computed(() => {
  if (!formData.field_type) return ''
  const option = FIELD_TYPE_OPTIONS.find(item => item.value === formData.field_type)
  return option?.label || ''
})

const appliesToFormDisplay = computed(() => {
  if (!formData.applies_to) return ''
  const option = APPLIES_TO_OPTIONS.find(item => item.value === formData.applies_to)
  return option?.label || ''
})

// 工具函数
const getFieldTypeLabel = (type: string) => {
  const option = FIELD_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

const getFieldTypeColor = (type: string): TagType => {
  const colors: Record<string, TagType> = {
    text: 'default',
    textarea: 'primary',
    number: 'warning',
    date: 'success',
    datetime: 'success',
    select: 'primary',
    radio: 'primary',
    checkbox: 'primary',
    file: 'danger'
  }
  return colors[type] || 'default'
}

const getAppliesToLabel = (appliesTo: string) => {
  const option = APPLIES_TO_OPTIONS.find(opt => opt.value === appliesTo)
  return option?.label || appliesTo
}

const getAppliesToColor = (appliesTo: string): TagType => {
  const colors: Record<string, TagType> = {
    asset: 'success',
    inventory_record: 'warning',
    both: 'primary'
  }
  return colors[appliesTo] || 'default'
}

// 数据加载
const fetchData = async (reset = false) => {
  try {
    // 如果是重置，初始化分页状态
    if (reset) {
      currentPage.value = 1
      fieldList.value = []
      finished.value = false
    }

    const response = await customFieldApi.getCustomFields({
      skip: (currentPage.value - 1) * pageSize,
      limit: pageSize,
      keyword: searchForm.keyword || undefined,
      field_type: searchForm.field_type || undefined,
      applies_to: searchForm.applies_to || undefined
    })

    const newFields = response.data?.data || []
    
    if (reset) {
      fieldList.value = newFields
    } else {
      // 检查是否有重复数据，避免重复添加
      const existingIds = new Set(fieldList.value.map(field => field.id))
      const uniqueNewFields = newFields.filter(field => !existingIds.has(field.id))
      fieldList.value.push(...uniqueNewFields)
    }

    // 如果返回的数据少于页面大小，说明没有更多数据了
    if (newFields.length < pageSize) {
      finished.value = true
    }

    // 只有成功加载数据后才增加页码
    currentPage.value++
  } catch (error) {
    console.error('获取字段列表失败:', error)
    showToast('获取数据失败')
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchData(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  // 如果正在刷新或已经完成加载，不执行
  if (refreshing.value || finished.value) return
  
  loading.value = true
  await fetchData(false)
  loading.value = false
}

// 搜索处理
const handleSearch = () => {
  fetchData(true)
}

// 筛选器确认
const onFieldTypeConfirm = ({ selectedOptions }: any) => {
  searchForm.field_type = selectedOptions[0].value
  showFieldTypePicker.value = false
  fetchData(true)
}

const onAppliesToConfirm = ({ selectedOptions }: any) => {
  searchForm.applies_to = selectedOptions[0].value
  showAppliesToPicker.value = false
  fetchData(true)
}

// 表单操作
const handleAdd = () => {
  formMode.value = 'create'
  resetForm()
  showForm.value = true
}

const handleEdit = (field: CustomField) => {
  formMode.value = 'edit'
  currentField.value = field
  fillForm(field)
  showForm.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    name: '',
    label: '',
    field_type: '',
    applies_to: '',
    description: '',
    default_value: '',
    is_required: false,
    is_active: true,
    options: []
  })
}

const fillForm = (field: CustomField) => {
  Object.assign(formData, {
    name: field.name,
    label: field.label,
    field_type: field.field_type,
    applies_to: field.applies_to,
    description: field.description || '',
    default_value: field.default_value || '',
    is_required: field.is_required,
    is_active: field.is_active,
    options: field.options?.choices || []
  })
}

// 表单内选择器确认
const onFormFieldTypeConfirm = ({ selectedOptions }: any) => {
  formData.field_type = selectedOptions[0].value
  showFormFieldTypePicker.value = false
  
  // 如果不是选择类型，清空选项配置
  if (!['select', 'radio', 'checkbox'].includes(formData.field_type)) {
    formData.options = []
  }
}

const onFormAppliesToConfirm = ({ selectedOptions }: any) => {
  formData.applies_to = selectedOptions[0].value
  showFormAppliesToPicker.value = false
}

// 选项管理
const addOption = () => {
  formData.options.push({ value: '', label: '' })
}

const removeOption = (index: number) => {
  formData.options.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    const submitData: any = {
      name: formData.name,
      label: formData.label,
      field_type: formData.field_type,
      applies_to: formData.applies_to,
      description: formData.description,
      default_value: formData.default_value,
      is_required: formData.is_required,
      is_active: formData.is_active
    }

    // 如果是选择类型字段，添加选项配置
    if (['select', 'radio', 'checkbox'].includes(formData.field_type)) {
      submitData.options = {
        choices: formData.options
      }
    }

    if (formMode.value === 'create') {
      await customFieldApi.createCustomField(submitData as CustomFieldCreate)
      showToast('创建成功')
    } else {
      await customFieldApi.updateCustomField(currentField.value!.id, submitData as CustomFieldUpdate)
      showToast('更新成功')
    }
    
    showForm.value = false
    fetchData(true)
  } catch (error) {
    console.error('提交失败:', error)
    showToast('提交失败')
  }
}

// 切换状态
const handleToggleStatus = async (field: CustomField, newStatus: boolean) => {
  try {
    await customFieldApi.updateCustomField(field.id, { is_active: newStatus })
    field.is_active = newStatus
    showToast(newStatus ? '已启用' : '已禁用')
  } catch (error) {
    console.error('切换状态失败:', error)
    showToast('操作失败')
    // 恢复原状态
    field.is_active = !newStatus
  }
}

// 删除确认
const confirmDelete = (field: CustomField) => {
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除字段"${field.label}"吗？删除后无法恢复。`
  }).then(() => {
    handleDelete(field)
  }).catch(() => {
    // 用户取消
  })
}

// 删除字段
const handleDelete = async (field: CustomField) => {
  try {
    await customFieldApi.deleteCustomField(field.id)
    showToast('删除成功')
    fetchData(true)
  } catch (error) {
    console.error('删除失败:', error)
    showToast('删除失败')
  }
}

// 滑动删除前确认
const beforeClose = (params: any, field: CustomField): boolean | Promise<boolean> => {
  const { position } = params
  if (position === 'right') {
    return new Promise<boolean>((resolve) => {
      showConfirmDialog({
        title: '确认删除',
        message: `确定要删除字段"${field.label}"吗？`
      })
        .then(() => {
          handleDelete(field)
          resolve(true)
        })
        .catch(() => resolve(false))
    })
  }
  return true
}

// 表单底部按钮配置
const formFooterButtons = computed(() => [
  {
    text: '保存',
    type: 'primary' as const,
    onClick: handleSubmit
  }
])

onMounted(async () => {
  // 初始化时加载第一页数据
  loading.value = true
  await fetchData(true)
  loading.value = false
})
</script>

<style scoped>
.mobile-custom-field-management {
  height: 100vh;
  background-color: #f5f5f5;
}

.search-section {
  background: white;
  padding-bottom: 8px;
}

.filter-row {
  padding: 0 16px 12px;
}

.field-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.field-status {
  margin-top: 4px;
}

.ml-1 {
  margin-left: 4px;
}

.form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.options-section {
  padding: 16px;
}

.option-item {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  
  .van-field {
    margin-bottom: 8px;
  }
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}
</style> 