from sqlalchemy import Column, Integer, String, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from app.database import Base
import datetime

class EcologyUser(Base):
    __tablename__ = "ecology_users"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    dept_id = Column(Integer, index=True)
    dept_name = Column(String, index=True)
    dept_hierarchy = Column(String)
    level = Column(Integer)
    dept_path = Column(String)
    company_id = Column(Integer, index=True)
    company_name = Column(String, index=True)
    user_name = Column(String, index=True)
    job_number = Column(String, index=True)
    mobile = Column(String)
    email = Column(String)
    job_title = Column(Integer)
    job_title_name = Column(String)
    gender = Column(String)
    status = Column(String)
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

class SyncConfig(Base):
    __tablename__ = "ecology_sync_config"
    
    id = Column(Integer, primary_key=True, index=True)
    sync_hour = Column(Integer, default=8)  # 同步时间（小时），默认为早上8点
    sync_time = Column(String, default="08:00")  # 同步时间，格式为 HH:MM
    last_sync_time = Column(DateTime, default=None)
    next_sync_time = Column(DateTime, default=None)
    sync_status = Column(String, default="未同步")  # 同步状态：未同步、同步中、同步成功、同步失败
    error_message = Column(String, default=None)  # 错误信息
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now()) 