import request from '@/utils/request'

// 工号补全相关接口

/**
 * 获取工号补全统计信息
 */
export function getExtidCompletionStats() {
  return request({
    url: '/email-personnel-sync/extid-completion/stats',
    method: 'get',
    timeout: 60000  // 1分钟超时
  })
}

/**
 * 获取工号补全候选者列表（分页）
 */
export function getExtidCompletionCandidates(params: {
  similarity_threshold?: number
  page?: number
  page_size?: number
  search?: string
  status_filter?: string
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/candidates',
    method: 'get',
    params,
    timeout: 180000  // 3分钟超时，数据量大时需要更长时间
  })
}

/**
 * 执行自动工号补全
 */
export function executeAutoExtidCompletion(data: {
  similarity_threshold?: number
  auto_confirm_exact_match?: boolean
  dry_run?: boolean
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/auto-execute',
    method: 'post',
    data,
    timeout: 300000  // 5分钟超时，执行补全可能需要很长时间
  })
}

/**
 * 手动确认工号匹配
 */
export function manualConfirmExtidMatch(data: {
  email_member_id: number
  person_id: number
  job_number: string
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/manual-confirm',
    method: 'post',
    data,
    timeout: 60000  // 1分钟超时
  })
}

/**
 * 手动设置工号
 */
export function manualSetExtid(data: {
  email_member_id: number
  job_number: string
  person_name?: string
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/manual-set',
    method: 'post',
    data,
    timeout: 60000  // 1分钟超时
  })
}

/**
 * 清除工号补全缓存
 */
export function clearCompletionCache() {
  return request({
    url: '/email-personnel-sync/extid-completion/clear-cache',
    method: 'post',
    timeout: 30000  // 30秒超时
  })
}

/**
 * 分析重新补齐候选者
 */
export function analyzeRecompletionCandidates(data: {
  strategy_type: string
  similarity_threshold?: number
  backup_before_operation?: boolean
  batch_size?: number
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/analyze-recompletion',
    method: 'post',
    data,
    timeout: 120000  // 2分钟超时
  })
}

/**
 * 执行重新补齐操作
 */
export function executeRecompletion(data: {
  strategy: {
    strategy_type: string
    similarity_threshold?: number
    backup_before_operation?: boolean
    batch_size?: number
  }
  target_member_ids?: number[]
  dry_run?: boolean
}) {
  return request({
    url: '/email-personnel-sync/extid-completion/execute-recompletion',
    method: 'post',
    data,
    timeout: 600000  // 10分钟超时，重新补齐可能需要很长时间
  })
}

// 数据备份相关接口

/**
 * 创建数据备份
 */
export function createDataBackup(data: {
  tables: string[]
  description: string
}) {
  return request({
    url: '/email-personnel-sync/backup/create',
    method: 'post',
    data
  })
}

/**
 * 获取备份列表
 */
export function getBackupList() {
  return request({
    url: '/email-personnel-sync/backup/list',
    method: 'get'
  })
}

/**
 * 获取备份详细信息
 */
export function getBackupInfo(backupId: string) {
  return request({
    url: `/email-personnel-sync/backup/${backupId}`,
    method: 'get'
  })
}

/**
 * 恢复数据备份
 */
export function restoreDataBackup(backupId: string, data: {
  backup_id: string
  confirm: boolean
}) {
  return request({
    url: `/email-personnel-sync/backup/${backupId}/restore`,
    method: 'post',
    data
  })
}

/**
 * 删除数据备份
 */
export function deleteDataBackup(backupId: string) {
  return request({
    url: `/email-personnel-sync/backup/${backupId}`,
    method: 'delete'
  })
}

// 类型定义
export interface ExtidCompletionStats {
  total_members: number
  has_extid: number
  missing_extid: number
  completion_rate: number
}

export interface NameMatchResult {
  person_id: number
  person_name: string
  job_number: string
  dept_name?: string
  similarity: number
  match_type: string
}

export interface ExtidCompletionResult {
  email_member_id: number
  email: string
  name: string
  current_extid?: string
  matches: NameMatchResult[]
  auto_match?: NameMatchResult
  status: string
}

export interface ExtidCompletionBatchResult {
  total_processed: number
  auto_matched: number
  manual_required: number
  skipped: number
  errors: number
  results: ExtidCompletionResult[]
  error_messages: string[]
}

export interface ExtidCompletionPaginatedResponse {
  items: ExtidCompletionResult[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface DataBackupInfo {
  backup_id: string
  backup_time: string
  table_name: string
  record_count: number
  backup_file_path: string
  description: string
}

// 重新补齐相关类型定义
export interface RecompletionStrategy {
  strategy_type: string
  similarity_threshold: number
  backup_before_operation: boolean
  batch_size: number
}

export interface RecompletionCandidate {
  email_member_id: number
  email: string
  name: string
  current_extid: string
  current_match_confidence: number
  reason_for_recompletion: string
  new_matches: NameMatchResult[]
  recommended_action: string
}

export interface RecompletionAnalysisResult {
  total_members_with_extid: number
  candidates_for_recompletion: number
  high_confidence_matches: number
  low_confidence_matches: number
  no_match_found: number
  candidates: RecompletionCandidate[]
}

export interface RecompletionResult {
  total_processed: number
  kept_unchanged: number
  updated_extid: number
  cleared_extid: number
  manual_review_required: number
  errors: number
  operation_duration: string
  backup_id?: string
  detailed_results: any[]
  error_messages: string[]
}

// 人员信息同步到腾讯邮箱相关接口

/**
 * 手动触发人员信息同步到腾讯邮箱
 */
export function triggerPersonnelSync(data: {
  full_sync?: boolean
  dry_run?: boolean
  since_time?: string
}) {
  return request({
    url: '/personnel-email-sync/sync/trigger',
    method: 'post',
    data,
    timeout: 300000  // 5分钟超时，同步可能需要很长时间
  })
}

/**
 * 获取人员同步状态
 */
export function getPersonnelSyncStatus() {
  return request({
    url: '/personnel-email-sync/sync/status',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 获取人员同步日志
 */
export function getPersonnelSyncLogs(params: {
  page?: number
  page_size?: number
  sync_type?: string
}) {
  return request({
    url: '/personnel-email-sync/sync/logs',
    method: 'get',
    params,
    timeout: 60000
  })
}

/**
 * 检查数据一致性
 */
export function checkDataConsistency() {
  return request({
    url: '/personnel-email-sync/sync/check-consistency',
    method: 'post',
    timeout: 120000  // 2分钟超时
  })
}

// 人员同步相关类型定义
export interface PersonnelSyncRequest {
  full_sync: boolean
  dry_run: boolean
  since_time?: string
}

export interface PersonnelSyncStats {
  processed_count: number
  created_count: number
  updated_count: number
  disabled_count: number
  error_count: number
  departments_created: number
  departments_updated: number
  departments_failed: number
}

export interface PersonnelSyncResult {
  success: boolean
  sync_log_id?: number
  stats?: PersonnelSyncStats
  operation_results: any[]
  duration?: string
  error_message?: string
  dry_run: boolean
}

// 过滤功能相关接口

/**
 * 获取可用的公司列表
 */
export function getAvailableCompanies() {
  return request({
    url: '/personnel-email-sync/filter/companies',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 获取可用的部门列表
 */
export function getAvailableDepartments() {
  return request({
    url: '/personnel-email-sync/filter/departments',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 获取可用的职位列表
 */
export function getAvailableJobTitles() {
  return request({
    url: '/personnel-email-sync/filter/job-titles',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 预览过滤结果
 */
export function previewFilterResults(data: FilterPreviewRequest) {
  return request({
    url: '/personnel-email-sync/filter/preview',
    method: 'post',
    data,
    timeout: 60000
  })
}

/**
 * 获取当前过滤统计信息
 */
export function getFilterStats() {
  return request({
    url: '/personnel-email-sync/filter/stats',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 获取人员同步配置
 */
export function getPersonnelSyncConfig() {
  return request({
    url: '/personnel-email-sync/sync/config',
    method: 'get',
    timeout: 30000
  })
}

/**
 * 更新人员同步配置
 */
export function updatePersonnelSyncConfig(data: PersonnelSyncConfigUpdate) {
  return request({
    url: '/personnel-email-sync/sync/config',
    method: 'put',
    data,
    timeout: 30000
  })
}

// 过滤功能相关类型定义
export interface FilterPreviewRequest {
  filter_enabled: boolean
  included_companies?: string[]
  included_departments?: string[]
  included_job_titles?: string[]
  excluded_job_titles?: string[]
  filter_logic: 'AND' | 'OR'
}

export interface FilterPreviewResult {
  total_personnel: number
  filtered_personnel: number
  reduction_rate: number
  department_breakdown: Record<string, number>
  job_title_breakdown: Record<string, number>
  sample_personnel: Array<{
    job_number: string
    user_name: string
    dept_name: string
    job_title_name: string
    status: string
  }>
}

export interface PersonnelSyncConfigUpdate {
  enabled?: boolean
  sync_time?: string
  sync_interval?: number
  auto_create_users?: boolean
  auto_update_users?: boolean
  auto_disable_users?: boolean
  auto_create_departments?: boolean
  filter_enabled?: boolean
  included_companies?: string[]
  included_departments?: string[]
  included_job_titles?: string[]
  excluded_job_titles?: string[]
  filter_logic?: 'AND' | 'OR'
}

export interface PersonnelSyncConfigResponse {
  id: number
  enabled: boolean
  sync_time?: string
  sync_interval: number
  auto_create_users: boolean
  auto_update_users: boolean
  auto_disable_users: boolean
  auto_create_departments: boolean
  filter_enabled: boolean
  included_companies?: string[]
  included_departments?: string[]
  included_job_titles?: string[]
  excluded_job_titles?: string[]
  filter_logic: string
  last_sync_time?: string
  next_sync_time?: string
  sync_status: string
  error_message?: string
  created_at: string
  updated_at: string
}

/**
 * 根据公司名获取该公司下的部门列表
 */
export function getDepartmentsByCompany(companyName: string) {
  return request({
    url: `/personnel-email-sync/filter/departments/by-company/${encodeURIComponent(companyName)}`,
    method: 'get',
    timeout: 30000
  })
}
