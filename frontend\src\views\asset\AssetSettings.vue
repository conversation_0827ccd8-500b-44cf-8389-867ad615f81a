<template>
  <div class="asset-settings">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Setting /></el-icon>
        <h2 class="page-title">资产编号设置</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>资产管理</el-breadcrumb-item>
        <el-breadcrumb-item>资产设置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <el-card class="settings-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">资产编号规则设置</span>
        </div>
        <div class="action-buttons">
          <Authority permission="asset:edit">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增设置
            </el-button>
          </Authority>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="settings"
        style="width: 100%"
        border
        row-class-name="settings-table-row"
        header-row-class-name="settings-table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column prop="company" label="公司" width="180">
          <template #header>
            <div class="column-header">公司</div>
          </template>
        </el-table-column>
        <el-table-column label="资产编号规则" min-width="400">
          <template #header>
            <div class="column-header">资产编号规则</div>
          </template>
          <template #default="{ row }">
            <div class="rule-info">
              <div class="rule-item">
                <span class="label">编号前缀：</span>
                <span class="value">{{ row.asset_number_rule.prefix }}</span>
              </div>
              <div class="rule-item">
                <span class="label">数字长度：</span>
                <span class="value">{{ row.asset_number_rule.number_length }}</span>
              </div>
              <div class="rule-item">
                <span class="label">起始数字：</span>
                <span class="value">{{ row.asset_number_rule.start_number }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="示例" width="180">
          <template #header>
            <div class="column-header">示例</div>
          </template>
          <template #default="{ row }">
            {{ generateExample(row.asset_number_rule) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <Authority permission="asset:edit">
                    <el-dropdown-item @click="handleEdit(row)">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="asset:edit">
                    <el-dropdown-item divided @click="handleDelete(row)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        destroy-on-close
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="公司" prop="company">
            <el-input
              v-model="form.company"
              :disabled="dialogMode === 'edit'"
              placeholder="请输入公司名称"
            />
          </el-form-item>
          
          <el-form-item label="编号前缀" prop="asset_number_rule.prefix">
            <el-input
              v-model="form.asset_number_rule.prefix"
              placeholder="请输入编号前缀，如：GD"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="数字长度" prop="asset_number_rule.number_length">
            <el-input-number
              v-model="form.asset_number_rule.number_length"
              :min="1"
              :max="12"
              placeholder="请输入数字长度"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="起始数字" prop="asset_number_rule.start_number">
            <el-input-number
              v-model="form.asset_number_rule.start_number"
              :min="0"
              :max="999999999999"
              placeholder="请输入起始数字"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="示例">
            <div class="example-preview">
              {{ generateExample(form.asset_number_rule) }}
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <Authority permission="asset:edit">
              <el-button type="primary" @click="handleSubmit">
                确定
              </el-button>
            </Authority>
          </span>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Plus, Edit, Delete, ArrowDown, Setting } from '@element-plus/icons-vue'
import { assetSettingsApi } from '@/api/asset_settings'
import type { AssetSettings, AssetNumberRule } from '@/types/asset_settings'
import Authority from '@/components/Authority/index.vue'

const loading = ref(false)
const settings = ref<AssetSettings[]>([])
const dialogVisible = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const formRef = ref<FormInstance>()

const dialogTitle = computed(() => dialogMode.value === 'create' ? '新增设置' : '编辑设置')

const defaultForm = {
  company: '',
  asset_number_rule: {
    prefix: '',
    number_length: 8,
    start_number: 1
  }
}

const form = reactive({
  company: '',
  asset_number_rule: {
    prefix: '',
    number_length: 8,
    start_number: 1
  }
})

const rules = {
  company: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  'asset_number_rule.prefix': [
    { required: true, message: '请输入编号前缀', trigger: 'blur' }
  ],
  'asset_number_rule.number_length': [
    { required: true, message: '请输入数字长度', trigger: 'blur' }
  ],
  'asset_number_rule.start_number': [
    { required: true, message: '请输入起始数字', trigger: 'blur' }
  ]
}

// 生成示例编号
const generateExample = (rule: AssetNumberRule) => {
  const { prefix, number_length, start_number } = rule
  if (!prefix || !number_length) return '-'
  return `${prefix}${String(start_number).padStart(number_length, '0')}`
}

// 获取设置列表
const fetchSettings = async () => {
  loading.value = true
  try {
    const response = await assetSettingsApi.getAssetSettings({})
    settings.value = response.data
  } catch (error) {
    ElMessage.error('获取设置列表失败')
  } finally {
    loading.value = false
  }
}

// 新增设置
const handleAdd = () => {
  dialogMode.value = 'create'
  Object.assign(form, defaultForm)
  dialogVisible.value = true
}

// 编辑设置
const handleEdit = (row: AssetSettings) => {
  dialogMode.value = 'edit'
  Object.assign(form, row)
  dialogVisible.value = true
}

// 删除设置
const handleDelete = async (row: AssetSettings) => {
  try {
    await ElMessageBox.confirm('确定要删除该设置吗？', '提示', {
      type: 'warning'
    })
    await assetSettingsApi.deleteAssetSettings(row.company)
    ElMessage.success('删除成功')
    fetchSettings()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    if (dialogMode.value === 'create') {
      await assetSettingsApi.createAssetSettings(form)
      ElMessage.success('创建成功')
    } else {
      await assetSettingsApi.updateAssetSettings(form.company, {
        asset_number_rule: form.asset_number_rule
      })
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    fetchSettings()
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}

// 初始化
fetchSettings()
</script>

<style scoped>
.asset-settings {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.settings-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.settings-table-row {
  transition: all 0.3s;
  height: 56px;
}

.settings-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.settings-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.rule-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.rule-item {
  display: flex;
  align-items: center;
}

.rule-item .label {
  font-weight: bold;
  width: 90px;
  color: #606266;
}

.rule-item .value {
  font-family: monospace;
}

.example-preview {
  font-family: monospace;
  font-size: 16px;
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  color: #409EFF;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 