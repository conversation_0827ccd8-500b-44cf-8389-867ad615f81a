"""extend_email_sync_log_for_personnel

Revision ID: 52246107d9e3
Revises: 732657688b92
Create Date: 2025-06-12 08:54:21.444997

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '52246107d9e3'
down_revision: Union[str, None] = '732657688b92'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 只扩展 email_sync_logs 表结构
    op.add_column('email_sync_logs', sa.Column('sync_category', sa.String(length=20), nullable=True, comment='同步分类(email/personnel)', server_default='email'))
    op.add_column('email_sync_logs', sa.Column('sync_id', sa.String(length=50), nullable=True, comment='同步ID'))
    op.add_column('email_sync_logs', sa.Column('operator', sa.String(length=100), nullable=True, comment='操作员'))
    op.add_column('email_sync_logs', sa.Column('created_count', sa.Integer(), nullable=True, comment='创建数量', server_default='0'))
    op.add_column('email_sync_logs', sa.Column('disabled_count', sa.Integer(), nullable=True, comment='禁用数量', server_default='0'))
    op.add_column('email_sync_logs', sa.Column('error_count', sa.Integer(), nullable=True, comment='错误数量', server_default='0'))
    op.add_column('email_sync_logs', sa.Column('processed_count', sa.Integer(), nullable=True, comment='处理数量', server_default='0'))
    op.add_column('email_sync_logs', sa.Column('start_time', sa.DateTime(timezone=True), nullable=True, comment='开始时间'))
    op.add_column('email_sync_logs', sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment='完成时间'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('email_sync_logs', 'completed_at')
    op.drop_column('email_sync_logs', 'start_time')
    op.drop_column('email_sync_logs', 'processed_count')
    op.drop_column('email_sync_logs', 'error_count')
    op.drop_column('email_sync_logs', 'disabled_count')
    op.drop_column('email_sync_logs', 'created_count')
    op.drop_column('email_sync_logs', 'operator')
    op.drop_column('email_sync_logs', 'sync_id')
    op.drop_column('email_sync_logs', 'sync_category')
    # ### end Alembic commands ###
