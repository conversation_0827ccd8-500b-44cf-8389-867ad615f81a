from app.database import SessionLocal
from app.crud.permission import permission_crud
from app.crud.role import role_crud

def check_permissions_and_roles():
    db = SessionLocal()
    try:
        permissions = permission_crud.get_multi(db)
        roles = role_crud.get_multi(db)
        
        print(f"Total permissions: {len(permissions)}")
        print("Permission modules:")
        modules = permission_crud.get_modules(db)
        for module in modules:
            module_permissions = permission_crud.get_by_module(db, module=module)
            print(f"  - {module}: {len(module_permissions)} permissions")
        
        print("\nTotal roles:", len(roles))
        for role in roles:
            print(f"  - {role.name} (code: {role.code}): {len(role.permissions)} permissions")
            
    except Exception as e:
        print(f"Error checking permissions: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_permissions_and_roles() 