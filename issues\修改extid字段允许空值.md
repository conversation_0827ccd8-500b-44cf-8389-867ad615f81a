# 修改extid字段允许空值

## 任务背景
用户反馈希望空工号的成员extid能为空（NULL），而不是使用邮箱地址作为extid。

## 当前问题
- `EmailMember` 模型中 `extid` 字段有 `unique=True` 约束
- SQLite 的 UNIQUE 约束不允许多个 NULL 值共存
- 当前逻辑：空工号时使用邮箱地址作为 extid

## 解决方案
1. 创建数据库迁移，修改 extid 字段约束允许多个 NULL 值
2. 修改同步代码逻辑，空工号时设置 extid 为 NULL
3. 更新相关文档

## 执行计划
1. ✅ 创建数据库迁移文件
2. ✅ 修改同步代码逻辑  
3. ✅ 执行数据库迁移
4. ✅ 更新问题文档

## 实施详情

### 1. 数据库迁移文件
- 文件：`backend/alembic/versions/1fd4c589e6a2_modify_extid_allow_multiple_nulls.py`
- 操作：删除原有UNIQUE索引，创建部分索引（只对非NULL值保持唯一性）

### 2. 代码修改
- 文件：`backend/app/api/v1/email.py` 第1349-1359行
- 修改：空工号时设置extid为None而不是邮箱地址
- 优化：只对非空extid进行重复检查

### 3. 文档更新
- 更新了原问题文档的解决方案和示例
- 记录了新的处理逻辑和效果

## 预期效果
- 有工号的成员：正常使用原工号
- 空工号的成员：extid 为 NULL
- 不再出现 UNIQUE 约束冲突错误

## 创建时间
2025-01-27 