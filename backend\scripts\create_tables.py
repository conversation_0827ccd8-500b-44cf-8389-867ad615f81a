import sys
import os
sys.path.insert(0, 'backend')

from sqlalchemy import create_engine, text
from backend.app.config import Settings
from backend.app.models.email import PersonnelSyncConfig, PersonnelSyncLog
from backend.app.database import Base
import logging

logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

try:
    settings = Settings()
    engine = create_engine(settings.DATABASE_URL)
    
    logger.info('开始创建人员邮箱同步相关表...')
    
    # 创建表
    Base.metadata.create_all(bind=engine, tables=[
        PersonnelSyncConfig.__table__,
        PersonnelSyncLog.__table__
    ])
    
    logger.info('人员邮箱同步相关表创建完成')
    
    # 检查表是否创建成功
    with engine.connect() as conn:
        # 检查 personnel_sync_config 表
        result = conn.execute(text('SELECT name FROM sqlite_master WHERE type="table" AND name="personnel_sync_config"'))
        if result.fetchone():
            logger.info('✓ personnel_sync_config 表创建成功')
        else:
            logger.error('✗ personnel_sync_config 表创建失败')
        
        # 检查 personnel_sync_logs 表
        result = conn.execute(text('SELECT name FROM sqlite_master WHERE type="table" AND name="personnel_sync_logs"'))
        if result.fetchone():
            logger.info('✓ personnel_sync_logs 表创建成功')
        else:
            logger.error('✗ personnel_sync_logs 表创建失败')
    
    print('人员邮箱同步相关表创建成功！')
    
except Exception as e:
    logger.error(f'创建人员邮箱同步相关表失败: {str(e)}')
    print('人员邮箱同步相关表创建失败！')
    import traceback
    traceback.print_exc()
