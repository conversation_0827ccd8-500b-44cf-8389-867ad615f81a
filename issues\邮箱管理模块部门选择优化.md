# 邮箱管理模块部门选择优化

## 任务描述
用户反馈邮箱管理模块中"从基础信息同步部门结构"功能的部门选择需要手动输入ID，这是不合理的，需要改为下拉选择器。

## 问题分析
1. **当前问题**: 部门选择使用 `el-input-number` 手动输入ID
2. **用户体验**: 用户需要知道具体的部门ID才能选择，非常不友好
3. **参考方案**: AD管理模块已经实现了部门下拉选择器

## 修改内容

### 1. 前端界面修改
**文件**: `frontend/src/views/email/SyncManagement.vue`

#### 状态管理
- 添加部门列表状态: `departments`
- 添加部门加载状态: `departmentsLoading`
- 添加公司列表状态: `companies`
- 添加公司加载状态: `companiesLoading`

#### 方法实现
- 添加 `fetchDepartments()` 方法获取部门列表
- 添加 `fetchCompanies()` 方法获取公司列表
- 修改 `showDepartmentStructureSyncDialog()` 方法，在显示对话框时加载数据

#### 界面组件
- 将部门ID的 `el-input-number` 改为 `el-select` 下拉选择器
- 将公司ID的 `el-input-number` 改为 `el-select` 下拉选择器
- 添加 `filterable` 搜索功能
- 添加 `loading` 加载状态
- 添加 `clearable` 清空功能

### 2. 功能特性
- **部门选择**: 显示格式为 "部门名称"
- **公司选择**: 显示格式为 "公司名称"
- **搜索过滤**: 支持按名称搜索
- **加载状态**: 显示数据加载进度
- **错误处理**: 获取数据失败时显示错误提示

### 3. API集成
- 使用 `ecologyApi.getDepartments()` 获取部门列表
- 使用 `ecologyApi.getCompanies()` 获取公司列表
- 复用现有的ecology API接口

## 修改前后对比

### 修改前
```vue
<el-form-item v-if="departmentSyncRequest.source === 'department'" label="部门ID">
  <el-input-number 
    v-model="departmentSyncRequest.department_id" 
    :min="1" 
    placeholder="请输入部门ID"
  />
</el-form-item>
```

### 修改后
```vue
<el-form-item v-if="departmentSyncRequest.source === 'department'" label="选择部门">
  <el-select
    v-model="departmentSyncRequest.department_id"
    placeholder="请选择部门"
    filterable
    :loading="departmentsLoading"
    clearable
  >
    <el-option
      v-for="dept in departments"
      :key="dept.id"
      :label="dept.name"
      :value="dept.id"
    />
  </el-select>
</el-form-item>
```

## 测试要点
1. **功能测试**
   - [x] 部门下拉选择器正常显示
   - [x] 公司下拉选择器正常显示
   - [x] 搜索过滤功能正常
   - [x] 选择后正确填充ID值
   - [x] 清空功能正常

2. **用户体验测试**
   - [x] 加载状态显示正常
   - [x] 错误提示友好
   - [x] 与AD管理模块体验一致

3. **数据验证**
   - [x] 部门列表数据正确
   - [x] 公司列表数据正确
   - [x] 选择的值正确传递给后端

## 完成状态
- [x] 前端界面修改完成
- [x] 状态管理实现完成
- [x] API集成完成
- [x] 功能测试通过

## 总结
通过将手动输入ID改为下拉选择器，大大提升了用户体验：
1. 用户无需记忆部门ID，直接通过名称选择
2. 界面简洁，只显示名称，不显示技术细节
3. 支持搜索过滤，快速定位目标部门
4. 与系统其他模块保持一致的操作体验
5. 减少了用户输入错误的可能性 