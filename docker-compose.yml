version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ops_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ops_platform}
      POSTGRES_USER: ${POSTGRES_USER:-ops_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-ops_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ops_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ops_user} -d ${POSTGRES_DB:-ops_platform}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ops_redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ops_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ops_backend
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-ops_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-ops_password}
      - POSTGRES_DB=${POSTGRES_DB:-ops_platform}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-ops_user}:${POSTGRES_PASSWORD:-ops_password}@postgres:5432/${POSTGRES_DB:-ops_platform}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/0
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    ports:
      - "8000:8000"
    networks:
      - ops_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ops_frontend
    ports:
      - "80:80"
    networks:
      - ops_network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 数据库迁移服务
  migration:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ops_migration
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=${POSTGRES_USER:-ops_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-ops_password}
      - POSTGRES_DB=${POSTGRES_DB:-ops_platform}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-ops_user}:${POSTGRES_PASSWORD:-ops_password}@postgres:5432/${POSTGRES_DB:-ops_platform}
    command: ["alembic", "upgrade", "head"]
    networks:
      - ops_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: "no"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  ops_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
