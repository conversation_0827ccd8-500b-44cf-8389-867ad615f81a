#!/usr/bin/env python3
"""
修复EmailSyncLog表中的无效JSON数据
将空字符串或无效JSON替换为null
"""

import sys
import os
from sqlalchemy import text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal

def fix_json_data():
    """修复无效的JSON数据"""
    db = SessionLocal()
    
    try:
        print("开始修复EmailSyncLog表中的无效JSON数据...")
        
        # 查询所有记录，检查details字段
        result = db.execute(text("SELECT id, details FROM email_sync_logs WHERE details IS NOT NULL AND details != ''"))
        records = result.fetchall()
        
        print(f"找到 {len(records)} 条包含details数据的记录")
        
        fixed_count = 0
        
        for record in records:
            record_id, details = record
            
            # 检查是否为有效JSON
            if details and details.strip():
                try:
                    import json
                    json.loads(details)
                    # 如果能解析，说明是有效JSON，跳过
                    continue
                except (json.JSONDecodeError, ValueError):
                    # 无效JSON，需要修复
                    print(f"修复记录ID {record_id} 的无效JSON数据")
                    db.execute(text("UPDATE email_sync_logs SET details = NULL WHERE id = :id"), {"id": record_id})
                    fixed_count += 1
            else:
                # 空字符串，设置为NULL
                print(f"修复记录ID {record_id} 的空字符串")
                db.execute(text("UPDATE email_sync_logs SET details = NULL WHERE id = :id"), {"id": record_id})
                fixed_count += 1
        
        # 提交更改
        db.commit()
        print(f"成功修复 {fixed_count} 条记录")
        
        # 验证修复结果
        result = db.execute(text("SELECT COUNT(*) FROM email_sync_logs WHERE details IS NOT NULL AND details != ''"))
        remaining_count = result.scalar()
        print(f"修复后还有 {remaining_count} 条包含details数据的记录")
        
    except Exception as e:
        print(f"修复过程中发生错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    print("EmailSyncLog JSON数据修复工具")
    print("=" * 50)
    
    try:
        fix_json_data()
        print("\n修复完成！")
        
    except Exception as e:
        print(f"修复失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 