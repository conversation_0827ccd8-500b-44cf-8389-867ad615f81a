export interface AppItem {
  id: string
  name: string
  icon: string
  color: string
  path: string
  permission?: string
  description?: string
}

// 主要应用模块配置 - 只显示大模块
export const appItems: AppItem[] = [
  {
    id: 'dashboard',
    name: '仪表盘',
    icon: 'chart-trending-o',
    color: '#1989fa',
    path: '/m/dashboard',
    description: '系统数据概览'
  },
  {
    id: 'ad',
    name: 'AD管理',
    icon: 'contact',
    color: '#409eff',
    path: '/m/ad',
    permission: 'ad:view',
    description: 'Active Directory域管理'
  },
  {
    id: 'email',
    name: '邮箱管理',
    icon: 'envelop-o',
    color: '#67c23a',
    path: '/m/email',
    permission: 'email:view',
    description: '企业邮箱账号管理'
  },
  {
    id: 'asset',
    name: '资产管理',
    icon: 'gift-card-o',
    color: '#e6a23c',
    path: '/m/asset',
    permission: 'asset:view',
    description: '设备资产管理'
  },
  {
    id: 'terminal',
    name: '终端管理',
    icon: 'desktop-o',
    color: '#f56c6c',
    path: '/m/terminal',
    permission: 'terminal:view',
    description: '远程终端管理'
  },
  {
    id: 'system',
    name: '系统管理',
    icon: 'setting',
    color: '#909399',
    path: '/m/system',
    permission: 'system:view',
    description: '系统设置管理'
  }
]

// 获取用户有权限的应用
export const getPermittedApps = (userPermissions: string[] = []): AppItem[] => {
  return appItems.filter(app => {
    if (!app.permission) return true
    return userPermissions.includes(app.permission)
  })
} 