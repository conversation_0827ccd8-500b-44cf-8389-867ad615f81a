from typing import Any, Dict, Optional, Union, List

from sqlalchemy.orm import Session, joinedload

from app.core.security import get_password_hash, verify_password
from app.crud.base import CRUDBase
from app.models.user import User
from app.models.role import Role
from app.schemas.user import UserC<PERSON>, UserUpdate

class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()

    def get_by_username(self, db: Session, *, username: str) -> Optional[User]:
        return db.query(User).filter(User.username == username).first()

    def create(self, db: Session, *, obj_in: UserCreate) -> User:
        db_obj = User(
            email=obj_in.email,
            username=obj_in.username,
            hashed_password=get_password_hash(obj_in.password),
            is_superuser=obj_in.is_superuser,
            is_active=obj_in.is_active
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 如果是超级管理员，自动分配超级管理员角色
        if obj_in.is_superuser:
            from app.crud.role import role_crud
            super_admin_role = role_crud.get_by_code(db, code="super_admin")
            if super_admin_role:
                if db_obj.roles is None:
                    db_obj.roles = []
                db_obj.roles.append(super_admin_role)
                db.add(db_obj)
                db.commit()
                db.refresh(db_obj)
        
        return db_obj

    def update(
        self, db: Session, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        if update_data.get("password"):
            hashed_password = get_password_hash(update_data["password"])
            del update_data["password"]
            update_data["hashed_password"] = hashed_password
            
        # 检查是否更新了is_superuser字段为True
        is_superuser_updated = "is_superuser" in update_data and update_data["is_superuser"] == True
        
        # 先更新用户基本信息
        updated_user = super().update(db, db_obj=db_obj, obj_in=update_data)
        
        # 如果设置为超级管理员，自动分配超级管理员角色
        if is_superuser_updated:
            from app.crud.role import role_crud
            super_admin_role = role_crud.get_by_code(db, code="super_admin")
            
            if super_admin_role:
                # 检查用户是否已有此角色
                has_role = False
                for role in updated_user.roles:
                    if role.code == "super_admin":
                        has_role = True
                        break
                
                # 如果没有此角色，添加
                if not has_role:
                    if updated_user.roles is None:
                        updated_user.roles = []
                    updated_user.roles.append(super_admin_role)
                    db.add(updated_user)
                    db.commit()
                    db.refresh(updated_user)
        
        return updated_user

    def authenticate(self, db: Session, *, username: str, password: str) -> Optional[User]:
        user = self.get_by_username(db, username=username)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user

    def is_active(self, user: User) -> bool:
        return user.is_active

    def is_superuser(self, user: User) -> bool:
        return user.is_superuser

    def get_with_roles(self, db: Session, *, id: int) -> Optional[User]:
        """获取用户及其所有角色和权限"""
        return db.query(User).filter(User.id == id).options(
            joinedload(User.roles).joinedload(Role.permissions)
        ).first()

    def get_multi_with_roles(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表及其所有角色信息"""
        return db.query(User).options(
            joinedload(User.roles).joinedload(Role.permissions)
        ).offset(skip).limit(limit).all()

    def update_superuser(self, db: Session, *, db_obj: User, is_superuser: bool) -> User:
        """更新用户的超级管理员状态"""
        db_obj.is_superuser = is_superuser
        
        # 如果设置为超级管理员，自动分配超级管理员角色
        if is_superuser:
            # 查找超级管理员角色
            from app.crud.role import role_crud
            super_admin_role = role_crud.get_by_code(db, code="super_admin")
            
            if super_admin_role:
                # 检查用户是否已有此角色
                has_role = False
                for role in db_obj.roles:
                    if role.code == "super_admin":
                        has_role = True
                        break
                
                # 如果没有此角色，添加
                if not has_role:
                    if db_obj.roles is None:
                        db_obj.roles = []
                    db_obj.roles.append(super_admin_role)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
        
    def update_active(self, db: Session, *, db_obj: User, is_active: bool) -> User:
        """更新用户的激活状态"""
        db_obj.is_active = is_active
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

user_crud = CRUDUser(User) 