"""
第三阶段测试配置
定义测试参数、阈值和配置选项
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
import os

@dataclass
class PerformanceThresholds:
    """性能测试阈值"""
    # 查询性能阈值（秒）
    stats_query_max_time: float = 5.0
    pagination_query_max_time: float = 3.0
    large_page_query_max_time: float = 5.0
    consistency_check_max_time: float = 10.0
    sync_operation_max_time: float = 30.0
    
    # 并发性能阈值
    concurrent_query_max_time: float = 15.0
    avg_concurrent_stats_time: float = 3.0
    avg_concurrent_matches_time: float = 3.0
    
    # 内存使用阈值（MB）
    max_memory_increase: float = 100.0
    
    # 数据库连接性能阈值
    avg_connection_time: float = 0.1
    max_connection_time: float = 0.5
    
    # 吞吐量阈值（条/秒）
    min_sync_throughput: float = 10.0
    min_query_throughput: float = 100.0

@dataclass
class TestDataConfig:
    """测试数据配置"""
    # 大数据量测试配置
    large_test_data_size: int = 1000
    performance_test_batch_size: int = 100
    
    # 并发测试配置
    concurrent_tasks_count: int = 10
    concurrent_stats_tasks: int = 5
    concurrent_matches_tasks: int = 5
    
    # 测试数据前缀
    functionality_test_prefix: str = "TEST_"
    consistency_test_prefix: str = "TEST_"
    performance_test_prefix: str = "PERF_"
    exception_test_prefix: str = "EXC_"
    
    # 分页测试配置
    default_page_size: int = 50
    large_page_size: int = 100
    max_page_size: int = 1000

@dataclass
class MonitoringConfig:
    """监控配置"""
    # 缓存配置
    metrics_cache_ttl: int = 300  # 5分钟
    max_cache_size: int = 100
    
    # 告警配置
    max_alerts_count: int = 1000
    alert_cleanup_threshold: int = 500
    
    # 健康检查阈值
    min_sync_success_rate: float = 90.0
    critical_sync_success_rate: float = 80.0
    max_hours_since_last_sync: int = 24
    critical_hours_since_last_sync: int = 48
    max_errors_24h: int = 10
    critical_errors_24h: int = 50
    min_data_consistency_rate: float = 85.0
    critical_data_consistency_rate: float = 70.0
    
    # 监控间隔
    monitoring_interval: int = 60  # 秒
    
    # 性能趋势分析天数
    performance_trend_days: int = 30
    sync_history_days: int = 7

@dataclass
class OptimizationConfig:
    """优化配置"""
    # 批处理配置
    default_batch_size: int = 100
    max_batch_size: int = 500
    min_batch_size: int = 10
    
    # 并行处理配置
    max_workers: int = 4
    enable_parallel_processing: bool = True
    
    # 缓存配置
    enable_caching: bool = True
    cache_ttl: int = 300
    
    # 性能基准测试配置
    benchmark_iterations: int = 3
    benchmark_warmup_iterations: int = 1

@dataclass
class TestEnvironmentConfig:
    """测试环境配置"""
    # 数据库配置
    test_database_url: Optional[str] = None
    use_test_database: bool = False
    
    # API配置
    api_base_url: str = "http://localhost:8000"
    api_timeout: int = 30
    
    # 测试超时配置
    test_timeout: int = 300  # 5分钟
    performance_test_timeout: int = 600  # 10分钟
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "stage3_tests.log"
    
    # 报告配置
    generate_html_report: bool = True
    generate_json_report: bool = True
    report_directory: str = "test_reports"

class Stage3TestConfig:
    """第三阶段测试总配置"""
    
    def __init__(self):
        self.performance = PerformanceThresholds()
        self.test_data = TestDataConfig()
        self.monitoring = MonitoringConfig()
        self.optimization = OptimizationConfig()
        self.environment = TestEnvironmentConfig()
        
        # 从环境变量加载配置
        self._load_from_environment()
    
    def _load_from_environment(self):
        """从环境变量加载配置"""
        # 性能阈值配置
        if os.getenv("STATS_QUERY_MAX_TIME"):
            self.performance.stats_query_max_time = float(os.getenv("STATS_QUERY_MAX_TIME"))
        
        if os.getenv("SYNC_OPERATION_MAX_TIME"):
            self.performance.sync_operation_max_time = float(os.getenv("SYNC_OPERATION_MAX_TIME"))
        
        # 测试数据配置
        if os.getenv("LARGE_TEST_DATA_SIZE"):
            self.test_data.large_test_data_size = int(os.getenv("LARGE_TEST_DATA_SIZE"))
        
        if os.getenv("CONCURRENT_TASKS_COUNT"):
            self.test_data.concurrent_tasks_count = int(os.getenv("CONCURRENT_TASKS_COUNT"))
        
        # 环境配置
        if os.getenv("TEST_API_BASE_URL"):
            self.environment.api_base_url = os.getenv("TEST_API_BASE_URL")
        
        if os.getenv("TEST_DATABASE_URL"):
            self.environment.test_database_url = os.getenv("TEST_DATABASE_URL")
            self.environment.use_test_database = True
        
        if os.getenv("TEST_TIMEOUT"):
            self.environment.test_timeout = int(os.getenv("TEST_TIMEOUT"))
        
        if os.getenv("LOG_LEVEL"):
            self.environment.log_level = os.getenv("LOG_LEVEL")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            "performance": {
                "stats_query_max_time": self.performance.stats_query_max_time,
                "pagination_query_max_time": self.performance.pagination_query_max_time,
                "consistency_check_max_time": self.performance.consistency_check_max_time,
                "sync_operation_max_time": self.performance.sync_operation_max_time,
                "concurrent_query_max_time": self.performance.concurrent_query_max_time,
                "max_memory_increase": self.performance.max_memory_increase,
                "min_sync_throughput": self.performance.min_sync_throughput
            },
            "test_data": {
                "large_test_data_size": self.test_data.large_test_data_size,
                "concurrent_tasks_count": self.test_data.concurrent_tasks_count,
                "default_page_size": self.test_data.default_page_size,
                "max_page_size": self.test_data.max_page_size
            },
            "monitoring": {
                "metrics_cache_ttl": self.monitoring.metrics_cache_ttl,
                "min_sync_success_rate": self.monitoring.min_sync_success_rate,
                "max_hours_since_last_sync": self.monitoring.max_hours_since_last_sync,
                "max_errors_24h": self.monitoring.max_errors_24h,
                "min_data_consistency_rate": self.monitoring.min_data_consistency_rate
            },
            "optimization": {
                "default_batch_size": self.optimization.default_batch_size,
                "max_workers": self.optimization.max_workers,
                "enable_parallel_processing": self.optimization.enable_parallel_processing,
                "enable_caching": self.optimization.enable_caching
            },
            "environment": {
                "api_base_url": self.environment.api_base_url,
                "test_timeout": self.environment.test_timeout,
                "log_level": self.environment.log_level,
                "generate_html_report": self.environment.generate_html_report
            }
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": []
        }
        
        # 验证性能阈值
        if self.performance.stats_query_max_time <= 0:
            validation_results["errors"].append("stats_query_max_time必须大于0")
            validation_results["valid"] = False
        
        if self.performance.sync_operation_max_time <= 0:
            validation_results["errors"].append("sync_operation_max_time必须大于0")
            validation_results["valid"] = False
        
        # 验证测试数据配置
        if self.test_data.large_test_data_size <= 0:
            validation_results["errors"].append("large_test_data_size必须大于0")
            validation_results["valid"] = False
        
        if self.test_data.large_test_data_size > 10000:
            validation_results["warnings"].append("large_test_data_size过大可能影响测试性能")
        
        if self.test_data.concurrent_tasks_count <= 0:
            validation_results["errors"].append("concurrent_tasks_count必须大于0")
            validation_results["valid"] = False
        
        if self.test_data.concurrent_tasks_count > 20:
            validation_results["warnings"].append("concurrent_tasks_count过大可能导致资源竞争")
        
        # 验证监控配置
        if self.monitoring.min_sync_success_rate < 0 or self.monitoring.min_sync_success_rate > 100:
            validation_results["errors"].append("min_sync_success_rate必须在0-100之间")
            validation_results["valid"] = False
        
        # 验证优化配置
        if self.optimization.default_batch_size <= 0:
            validation_results["errors"].append("default_batch_size必须大于0")
            validation_results["valid"] = False
        
        if self.optimization.max_workers <= 0:
            validation_results["errors"].append("max_workers必须大于0")
            validation_results["valid"] = False
        
        return validation_results

# 全局配置实例
config = Stage3TestConfig()

# 配置验证
validation_result = config.validate_config()
if not validation_result["valid"]:
    raise ValueError(f"配置验证失败: {validation_result['errors']}")

if validation_result["warnings"]:
    import logging
    logger = logging.getLogger(__name__)
    for warning in validation_result["warnings"]:
        logger.warning(f"配置警告: {warning}")

if __name__ == "__main__":
    # 打印当前配置
    import json
    print("第三阶段测试配置:")
    print(json.dumps(config.get_config_dict(), indent=2, ensure_ascii=False))
