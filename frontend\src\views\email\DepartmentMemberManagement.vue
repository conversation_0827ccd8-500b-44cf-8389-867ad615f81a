<template>
  <div class="department-member-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><OfficeBuilding /></el-icon>
        <h2 class="page-title">部门与成员管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>部门与成员管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-row :gutter="12" class="h-full">
      <!-- 左侧：部门树 -->
      <el-col :span="6" class="h-full">
        <div class="aside-container">
          <div class="aside-header">
            <span class="aside-title">部门结构</span>
          </div>
          <div class="aside-content">
            <el-tree
              ref="deptTreeRef"
              :data="departmentTree"
              :props="treeProps"
              node-key="dept_id"
              :expand-on-click-node="false"
              :highlight-current="true"
              @node-click="handleDeptNodeClick"
              v-loading="treeLoading"
            >
              <template #default="{ data }">
                <div class="tree-node">
                  <span class="node-label">{{ data.name }}</span>
                  <div class="node-actions">
                    <el-dropdown trigger="click" @click.stop>
                      <el-button type="primary" size="small" text class="node-menu-btn">
                        <el-icon><MoreFilled /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <Authority permission="email:department:create">
                            <el-dropdown-item @click="handleCreateSubDepartment(data)">
                              <el-icon><Plus /></el-icon>添加子部门
                            </el-dropdown-item>
                          </Authority>
                          <Authority permission="email:department:update">
                            <el-dropdown-item @click="handleEditDepartment(data)">
                              <el-icon><Edit /></el-icon>编辑部门
                            </el-dropdown-item>
                          </Authority>
                          <Authority permission="email:department:delete">
                            <el-dropdown-item @click="handleDeleteDepartment(data)" divided>
                              <el-icon><Delete /></el-icon>删除部门
                            </el-dropdown-item>
                          </Authority>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
            </el-tree>
          </div>
        </div>
      </el-col>

      <!-- 右侧：成员管理 -->
      <el-col :span="18" class="h-full">
        <div class="main-container">
          <div class="main-header">
            <div class="header-left">
              <div class="tab-with-dept">
                <el-tag v-if="selectedDepartment" type="info" class="current-dept" size="large">
                  当前部门: {{ selectedDepartment.name }}
                </el-tag>
                <span v-else class="no-dept-selected">请选择部门查看成员</span>
              </div>
            </div>
            <div class="header-center">
            </div>
            <div class="header-right">
              <!-- 搜索框 -->
              <el-input
                v-if="selectedDepartment"
                v-model="memberSearchForm.search"
                placeholder="搜索成员姓名、邮箱"
                class="search-input"
                clearable
                @keyup.enter="handleMemberSearch"
                :prefix-icon="Search"
              />
              <el-dropdown v-if="selectedDepartment" trigger="click">
                <el-button type="primary" plain class="menu-button">
                  <el-icon class="menu-icon"><UserFilled /></el-icon>
                  功能菜单
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <Authority permission="email:member:create">
                      <el-dropdown-item @click="handleCreateMember">
                        <el-icon><Plus /></el-icon>新增成员
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="email:member:sync">
                      <el-dropdown-item @click="handleSyncMembers" :disabled="memberSyncing">
                        <el-icon><Refresh /></el-icon>同步成员
                        <el-icon v-if="memberSyncing" class="is-loading"><Loading /></el-icon>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="email:department:sync">
                      <el-dropdown-item @click="syncDepartments" :disabled="deptSyncing" divided>
                        <el-icon><Refresh /></el-icon>同步部门
                        <el-icon v-if="deptSyncing" class="is-loading"><Loading /></el-icon>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="email:department:create">
                      <el-dropdown-item @click="handleCreateDepartment">
                        <el-icon><Plus /></el-icon>新增部门
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="email:member:export">
                      <el-dropdown-item @click="handleExportMembers" divided>
                        <el-icon><Download /></el-icon>导出成员
                      </el-dropdown-item>
                    </Authority>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="main-content">
            <!-- 成员表格 -->
            <div v-if="selectedDepartment">
              <div class="table-container">
                <el-table
                  v-loading="memberLoading"
                  :data="memberList"
                  style="width: 100%"
                >
                  <el-table-column prop="extid" label="工号" width="120">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleViewMember(row)">
                        {{ row.extid }}
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="email" label="邮箱地址" width="200">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleViewMember(row)">
                        {{ row.email }}
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name" label="姓名" width="120">
                    <template #default="{ row }">
                      <el-button type="primary" link @click="handleViewMember(row)">
                        {{ row.name }}
                      </el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="position" label="职位" width="120" />
                  <el-table-column prop="mobile" label="手机号" width="130" />
                  <el-table-column prop="tel" label="电话" width="120" />
                  <el-table-column prop="is_active" label="状态" width="80">
                    <template #default="{ row }">
                      <el-tag :type="row.is_active ? 'success' : 'danger'">
                        {{ row.is_active ? '启用' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" fixed="right" align="center">
                    <template #default="{ row }">
                      <el-dropdown trigger="click">
                        <el-button type="primary" link>
                          操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <Authority permission="email:member:view">
                              <el-dropdown-item @click="handleViewMember(row)">
                                <el-icon><View /></el-icon>查看详情
                              </el-dropdown-item>
                            </Authority>
                            <Authority permission="email:member:update">
                              <el-dropdown-item @click="handleEditMember(row)">
                                <el-icon><Edit /></el-icon>编辑成员
                              </el-dropdown-item>
                            </Authority>
                            <Authority permission="email:member:active">
                              <el-dropdown-item @click="handleToggleMemberStatus(row)" divided>
                                <el-icon v-if="row.is_active"><Close /></el-icon>
                                <el-icon v-else><Check /></el-icon>
                                {{ row.is_active ? '禁用成员' : '启用成员' }}
                              </el-dropdown-item>
                            </Authority>
                            <Authority permission="email:member:delete">
                              <el-dropdown-item @click="handleDeleteMember(row)" divided style="color: var(--el-color-danger)">
                                <el-icon><Delete /></el-icon>删除成员
                              </el-dropdown-item>
                            </Authority>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="pagination-container">
                <Authority permission="email:member:view">
                  <el-pagination
                    v-model:current-page="memberPagination.page"
                    v-model:page-size="memberPagination.size"
                    :total="memberPagination.total"
                    :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleMemberSizeChange"
                    @current-change="handleMemberCurrentChange"
                  />
                </Authority>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
              <el-empty description="请从左侧选择部门查看成员" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 部门表单对话框 -->
    <el-dialog 
      v-model="deptDialogVisible" 
      :title="deptDialogTitle" 
      width="500px"
      destroy-on-close
    >
      <el-form 
        ref="deptFormRef" 
        :model="deptForm" 
        :rules="deptFormRules"
        label-width="100px"
      >
        <el-form-item label="部门ID" prop="dept_id">
          <el-input 
            v-model="deptForm.dept_id" 
            placeholder="请输入部门ID"
            :disabled="isDeptEditMode"
          />
          <div class="form-tip">部门的唯一标识，创建后不可修改</div>
        </el-form-item>
        
        <el-form-item label="部门名称" prop="name">
          <el-input 
            v-model="deptForm.name" 
            placeholder="请输入部门名称"
          />
        </el-form-item>
        
        <el-form-item label="上级部门" prop="parent_id">
          <el-tree-select
            v-model="deptForm.parent_id"
            :data="departmentTree"
            :props="treeSelectProps"
            node-key="dept_id"
            placeholder="请选择上级部门"
            clearable
            check-strictly
          />
        </el-form-item>
        
        <el-form-item label="排序" prop="order">
          <el-input-number 
            v-model="deptForm.order" 
            :min="0"
            :max="999999"
            placeholder="排序值"
          />
        </el-form-item>
        
        <el-form-item label="同步到API">
          <el-switch 
            v-model="deptSyncToApi"
            :active-text="'是'"
            :inactive-text="'否'"
          />
          <div class="form-tip">是否同步到企业邮箱API</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deptDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDeptForm" :loading="deptSaving">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员表单对话框 -->
    <el-dialog
      v-model="memberDialogVisible"
      :title="memberDialogTitle"
      width="600px"
      @close="handleMemberDialogClose"
    >
      <el-form
        ref="memberFormRef"
        :model="memberForm"
        :rules="memberFormRules"
        label-width="120px"
      >
        <el-form-item label="工号" prop="extid">
          <el-input
            v-model="memberForm.extid"
            :placeholder="isMemberEditMode ? '工号可选，留空保持不变' : '请输入工号'"
          />
        </el-form-item>
        <el-form-item label="邮箱地址" prop="email">
          <el-input
            v-model="memberForm.email"
            placeholder="请输入邮箱地址"
            :disabled="isMemberEditMode"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="memberForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isMemberEditMode">
          <el-input 
            v-model="memberForm.password"
            placeholder="请输入密码"
            type="password"
            show-password
          />
          <div class="form-tip">创建成员时必须设置密码</div>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-else>
          <el-input 
            v-model="memberForm.password"
            placeholder="留空则不修改密码"
            type="password"
            show-password
          />
          <div class="form-tip">如不修改密码请留空</div>
        </el-form-item>
        <el-form-item label="所属部门" prop="department_id">
          <el-tree-select
            v-model="memberForm.department_id"
            :data="departmentTree"
            :props="treeSelectProps"
            node-key="dept_id"
            placeholder="请选择部门"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="职位" prop="position">
          <el-input v-model="memberForm.position" placeholder="请输入职位" />
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="memberForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="电话" prop="tel">
          <el-input v-model="memberForm.tel" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="登录权限">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-checkbox v-model="memberForm.pop_smtp_enabled">POP/SMTP服务</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="memberForm.imap_smtp_enabled">IMAP/SMTP服务</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="memberForm.secure_login_enabled">安全登录</el-checkbox>
            </el-col>
            <el-col :span="12">
              <el-checkbox v-model="memberForm.force_secure_login">强制安全登录</el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="同步到API">
          <el-switch 
            v-model="memberSyncToApi"
            :active-text="'是'"
            :inactive-text="'否'"
          />
          <div class="form-tip">是否同步到企业邮箱API</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="memberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveMemberForm" :loading="memberSaving">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成员详情对话框 -->
    <el-dialog 
      v-model="memberDetailDialogVisible" 
      title="成员详情" 
      width="700px"
      @close="handleMemberDetailDialogClose"
    >
      <div v-if="currentMember">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基本信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="工号">{{ currentMember.extid }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ currentMember.email }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ currentMember.name }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ currentMember.department_name }}</el-descriptions-item>
            <el-descriptions-item label="职位">{{ currentMember.position || '-' }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ currentMember.mobile || '-' }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ currentMember.tel || '-' }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="currentMember.is_active ? 'success' : 'danger'">
                {{ currentMember.is_active ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">
              {{ formatDateTime(currentMember.created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 登录权限信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
          <template #header>
            <div class="card-header">
              <span class="card-title">登录权限设置</span>
              <el-button 
                type="primary" 
                size="small" 
                @click="refreshMemberPermissions"
                :loading="permissionsLoading"
              >
                <el-icon><Refresh /></el-icon>
                刷新权限
              </el-button>
            </div>
          </template>
          
          <div v-loading="permissionsLoading">
            <el-descriptions :column="1" border v-if="memberPermissions">
              <el-descriptions-item label="POP/SMTP 登录">
                <el-tag :type="memberPermissions.pop_smtp_enabled ? 'success' : 'danger'">
                  {{ memberPermissions.pop_smtp_enabled ? '已启用' : '已禁用' }}
                </el-tag>
                <span class="permission-desc">允许使用 POP3/SMTP 协议收发邮件</span>
              </el-descriptions-item>
              
              <el-descriptions-item label="IMAP/SMTP 登录">
                <el-tag :type="memberPermissions.imap_smtp_enabled ? 'success' : 'danger'">
                  {{ memberPermissions.imap_smtp_enabled ? '已启用' : '已禁用' }}
                </el-tag>
                <span class="permission-desc">允许使用 IMAP/SMTP 协议收发邮件</span>
              </el-descriptions-item>
              
              <el-descriptions-item label="安全登录">
                <el-tag :type="memberPermissions.secure_login_enabled ? 'success' : 'danger'">
                  {{ memberPermissions.secure_login_enabled ? '已启用' : '已禁用' }}
                </el-tag>
                <span class="permission-desc">启用后需要通过安全验证才能登录</span>
              </el-descriptions-item>
              
              <el-descriptions-item label="强制安全登录">
                <el-tag :type="memberPermissions.force_secure_login ? 'warning' : 'info'">
                  {{ memberPermissions.force_secure_login ? '已强制' : '未强制' }}
                </el-tag>
                <span class="permission-desc">强制要求所有登录都必须通过安全验证</span>
              </el-descriptions-item>
            </el-descriptions>
            
            <el-empty 
              v-else-if="!permissionsLoading" 
              description="暂无权限信息" 
              :image-size="80"
            />
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog 
      v-model="exportDialogVisible" 
      title="导出成员数据" 
      width="400px"
      destroy-on-close
    >
      <div class="export-form">
        <div class="export-info">
          <p>
            <strong>导出范围：</strong>
            <span v-if="selectedDepartment">{{ selectedDepartment.name }} 部门</span>
            <span v-else>全部部门</span>
          </p>
          <p v-if="memberSearchForm.search">
            <strong>搜索条件：</strong>{{ memberSearchForm.search }}
          </p>
        </div>
        
        <el-form label-width="80px">
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportForm.format">
              <el-radio value="xlsx">Excel格式 (.xlsx)</el-radio>
              <el-radio value="csv">CSV格式 (.csv)</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmExport"
            :loading="exporting"
          >
            <el-icon><Download /></el-icon>
            {{ exporting ? '导出中...' : '确认导出' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  OfficeBuilding, 
  Refresh, 
  Plus, 
  Edit, 
  Delete, 
  Search, 
  UserFilled,
  Setting,
  ArrowDown,
  MoreFilled,
  Loading,
  Download,
  View,
  Close,
  Check
} from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'
import { formatDateTime } from '@/utils/date'
import { 
  getDepartmentTree, 
  createDepartment, 
  updateDepartment, 
  deleteDepartment, 
  syncDepartmentsFromApi 
} from '@/api/email/department'
import { 
  getMembers, 
  createMember, 
  updateMember, 
  deleteMember, 
  getMemberDetail,
  updateMemberLoginPermissions,
  getMemberLoginPermissions,
  syncMembersFromApi,
  exportMembers,
  updateMemberStatus
} from '@/api/email/member'
import type { 
  Department, 
  Member, 
  DepartmentForm, 
  MemberForm, 
  MemberPermissions,
  MemberSearchParams
} from '@/types/email'

// 部门相关状态
const treeLoading = ref(false)
const deptSyncing = ref(false)
const departmentTree = ref<Department[]>([])
const selectedDepartment = ref<Department | null>(null)

// 成员相关状态
const memberLoading = ref(false)
const memberSyncing = ref(false)
const memberList = ref<Member[]>([])
const currentMember = ref<Member | null>(null)
const memberPermissions = ref<MemberPermissions | null>(null)
const permissionsLoading = ref(false)

// 导出相关状态
const exportDialogVisible = ref(false)
const exporting = ref(false)
const exportForm = reactive({
  format: 'xlsx' as 'xlsx' | 'csv'
})

// 分页
const memberPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 搜索
const memberSearchForm = reactive<MemberSearchParams>({
  search: ''
})

// 部门对话框
const deptDialogVisible = ref(false)
const isDeptEditMode = ref(false)
const deptSaving = ref(false)
const deptSyncToApi = ref(true)
const deptFormRef = ref<FormInstance>()

const deptForm = reactive<DepartmentForm>({
  dept_id: '',
  name: '',
  parent_id: '',
  order: 0
})

// 成员对话框
const memberDialogVisible = ref(false)
const memberDetailDialogVisible = ref(false)
const isMemberEditMode = ref(false)
const memberSaving = ref(false)
const memberSyncToApi = ref(true)
const memberFormRef = ref<FormInstance>()

const memberForm = reactive<MemberForm>({
  extid: '',
  email: '',
  name: '',
  password: '',
  department_id: '',
  position: '',
  mobile: '',
  tel: '',
  pop_smtp_enabled: true,
  imap_smtp_enabled: true,
  secure_login_enabled: false,
  force_secure_login: false
})



// 树形控件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

const treeSelectProps = {
  children: 'children',
  label: 'name',
  value: 'dept_id'
}

// 计算属性
const deptDialogTitle = computed(() => {
  return isDeptEditMode.value ? '编辑部门' : '新增部门'
})

const memberDialogTitle = computed(() => {
  return isMemberEditMode.value ? '编辑成员' : '新增成员'
})

// 表单验证规则
const deptFormRules: FormRules = {
  dept_id: [
    { required: true, message: '请输入部门ID', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' }
  ]
}

// 动态计算表单验证规则
const memberFormRules = computed(() => {
  const rules: any = {
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    department_id: [
      { required: true, message: '请选择所属部门', trigger: 'change' }
    ]
  }
  
  // 工号验证规则：新增模式下必填，编辑模式下可选
  if (isMemberEditMode.value) {
    // 编辑模式：工号可选
    rules.extid = []
  } else {
    // 新增模式：工号必填
    rules.extid = [
      { required: true, message: '请输入工号', trigger: 'blur' }
    ]
  }
  
  // 密码验证规则：新增模式下必填，编辑模式下可选
  if (isMemberEditMode.value) {
    // 编辑模式：密码可选，但如果填写了则需要满足长度要求
    rules.password = [
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ]
  } else {
    // 新增模式：密码必填
    rules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ]
  }
  
  return rules
})

// 生命周期
onMounted(() => {
  loadDepartmentTree()
})

// 加载部门树
const loadDepartmentTree = async () => {
  try {
    treeLoading.value = true
    const response = await getDepartmentTree()
    departmentTree.value = response.data.tree
  } catch (error) {
    ElMessage.error('加载部门树失败')
    console.error(error)
  } finally {
    treeLoading.value = false
  }
}

// 同步部门
const syncDepartments = async () => {
  // 创建加载实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步部门数据，请耐心等待...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    deptSyncing.value = true
    
    // 调用API同步部门
    const response = await syncDepartmentsFromApi()
    
    // 显示成功信息，包含同步数量和耗时
    if (response && response.data) {
      const { message, synced_count, total_count, elapsed_time } = response.data
      ElMessage.success(message || `部门同步成功，共同步 ${synced_count}/${total_count} 个部门，耗时 ${elapsed_time}`)
    } else {
      ElMessage.success('部门同步成功')
    }
    
    // 重新加载部门树
    await loadDepartmentTree()
  } catch (error: any) {
    ElMessage.error('部门同步失败: ' + (error.message || '未知错误'))
    console.error(error)
  } finally {
    // 确保在任何情况下都关闭加载动画
    loadingInstance.close()
    deptSyncing.value = false
  }
}

// 点击部门节点
const handleDeptNodeClick = (data: any) => {
  selectedDepartment.value = data
  loadMembers()
}

// 加载成员列表
const loadMembers = async () => {
  if (!selectedDepartment.value) return
  
  try {
    memberLoading.value = true
    const params = {
      page: memberPagination.page,
      size: memberPagination.size,
      department_id: selectedDepartment.value.dept_id,
      search: memberSearchForm.search || undefined
    }
    
    const response = await getMembers(params)
    memberList.value = response.data.items
    memberPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载成员列表失败')
    console.error(error)
  } finally {
    memberLoading.value = false
  }
}

// 成员搜索
const handleMemberSearch = () => {
  memberPagination.page = 1
  loadMembers()
}

// 分页处理
const handleMemberSizeChange = (size: number) => {
  memberPagination.size = size
  memberPagination.page = 1
  loadMembers()
}

const handleMemberCurrentChange = (page: number) => {
  memberPagination.page = page
  loadMembers()
}

// 部门管理方法
const handleCreateDepartment = () => {
  isDeptEditMode.value = false
  deptSyncToApi.value = true
  Object.assign(deptForm, {
    dept_id: '',
    name: '',
    parent_id: selectedDepartment.value?.dept_id || '',
    order: 0
  })
  deptDialogVisible.value = true
}

const handleCreateSubDepartment = (data: any) => {
  isDeptEditMode.value = false
  deptSyncToApi.value = true
  Object.assign(deptForm, {
    dept_id: '',
    name: '',
    parent_id: data.dept_id,
    order: 0
  })
  deptDialogVisible.value = true
}

const handleEditDepartment = (data: any) => {
  isDeptEditMode.value = true
  deptSyncToApi.value = true
  Object.assign(deptForm, {
    dept_id: data.dept_id,
    name: data.name,
    parent_id: data.parent_id || '',
    order: data.order || 0
  })
  deptDialogVisible.value = true
}

const handleDeleteDepartment = async (data: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门 "${data.name}" 吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    await deleteDepartment(data.dept_id, { sync_to_api: true })
    ElMessage.success('删除成功')
    await loadDepartmentTree()
    
    // 如果删除的是当前选中的部门，清空选择
    if (selectedDepartment.value?.dept_id === data.dept_id) {
      selectedDepartment.value = null
      memberList.value = []
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const saveDeptForm = async () => {
  if (!deptFormRef.value) return
  
  try {
    await deptFormRef.value.validate()
    deptSaving.value = true
    
    const formData: any = { ...deptForm }
    if (!formData.parent_id) {
      formData.parent_id = undefined
    }
    
    if (isDeptEditMode.value) {
      await updateDepartment(deptForm.dept_id, formData, { sync_to_api: deptSyncToApi.value })
      ElMessage.success('更新成功')
    } else {
      await createDepartment(formData, { sync_to_api: deptSyncToApi.value })
      ElMessage.success('创建成功')
    }
    
    deptDialogVisible.value = false
    await loadDepartmentTree()
  } catch (error) {
    ElMessage.error(isDeptEditMode.value ? '更新失败' : '创建失败')
    console.error(error)
  } finally {
    deptSaving.value = false
  }
}

// 成员管理方法
const handleCreateMember = () => {
  isMemberEditMode.value = false
  memberSyncToApi.value = true
  Object.assign(memberForm, {
    extid: '',
    email: '',
    name: '',
    password: '',
    department_id: selectedDepartment.value?.dept_id || '',
    position: '',
    mobile: '',
    tel: '',
    pop_smtp_enabled: true,
    imap_smtp_enabled: true,
    secure_login_enabled: false,
    force_secure_login: false
  })
  memberDialogVisible.value = true
}

const handleEditMember = async (row: any) => {
  try {
    isMemberEditMode.value = true
    memberSyncToApi.value = true
    
    // 先设置基本信息
    Object.assign(memberForm, {
      extid: row.extid,
      email: row.email,
      name: row.name,
      password: '',
      department_id: row.department_id,
      position: row.position || '',
      mobile: row.mobile || '',
      tel: row.tel || '',
      pop_smtp_enabled: false,
      imap_smtp_enabled: false,
      secure_login_enabled: false,
      force_secure_login: false
    })
    
    // 显示对话框
    memberDialogVisible.value = true
    
    // 从API获取最新的权限状态
    try {
      const response = await getMemberLoginPermissions(row.email)
      const apiData = response.data
      
      console.log('编辑成员时API返回的权限数据:', apiData)
      
      // 解析API返回的option数组格式
      if (apiData && apiData.option && Array.isArray(apiData.option)) {
        apiData.option.forEach((option: any) => {
          const type = option.type
          const value = option.value === '1' || option.value === 1
          
          switch (type) {
            case 1:
              memberForm.force_secure_login = value
              break
            case 2:
              memberForm.imap_smtp_enabled = value
              break
            case 3:
              memberForm.pop_smtp_enabled = value
              break
            case 4:
              memberForm.secure_login_enabled = value
              break
          }
        })
      }
      
      console.log('编辑成员时解析后的权限数据:', {
        pop_smtp_enabled: memberForm.pop_smtp_enabled,
        imap_smtp_enabled: memberForm.imap_smtp_enabled,
        secure_login_enabled: memberForm.secure_login_enabled,
        force_secure_login: memberForm.force_secure_login
      })
    } catch (error) {
      console.warn('获取最新权限失败，使用本地数据:', error)
      // 如果API调用失败，使用本地数据作为备选
      Object.assign(memberForm, {
        pop_smtp_enabled: Boolean(row.pop_smtp_enabled),
        imap_smtp_enabled: Boolean(row.imap_smtp_enabled),
        secure_login_enabled: Boolean(row.secure_login_enabled),
        force_secure_login: Boolean(row.force_secure_login)
      })
    }
  } catch (error) {
    ElMessage.error('打开编辑对话框失败')
    console.error(error)
  }
}

const handleViewMember = async (row: any) => {
  try {
    const response = await getMemberDetail(row.email)
    currentMember.value = response.data
    memberDetailDialogVisible.value = true
    
    // 同时加载成员权限信息
    await loadMemberPermissions(row.email)
  } catch (error) {
    ElMessage.error('获取成员详情失败')
    console.error(error)
  }
}

// 加载成员权限信息
const loadMemberPermissions = async (email: string) => {
  try {
    permissionsLoading.value = true
    const response = await getMemberLoginPermissions(email)
    
    // 解析API返回的权限数据
    if (response.data && response.data.option && Array.isArray(response.data.option)) {
      const permissions = {
        pop_smtp_enabled: false,
        imap_smtp_enabled: false,
        secure_login_enabled: false,
        force_secure_login: false
      }
      
      response.data.option.forEach((option: any) => {
        const type = option.type
        const value = option.value === '1' || option.value === 1
        
        switch (type) {
          case 1:
            permissions.force_secure_login = value
            break
          case 2:
            permissions.imap_smtp_enabled = value
            break
          case 3:
            permissions.pop_smtp_enabled = value
            break
          case 4:
            permissions.secure_login_enabled = value
            break
        }
      })
      
      memberPermissions.value = permissions
    } else {
      memberPermissions.value = null
    }
  } catch (error) {
    console.warn('获取成员权限失败:', error)
    memberPermissions.value = null
  } finally {
    permissionsLoading.value = false
  }
}

// 导出成员
const handleExportMembers = () => {
  exportForm.format = 'xlsx'
  exportDialogVisible.value = true
}

// 确认导出
const confirmExport = async () => {
  try {
    exporting.value = true
    
    const params: any = {
      format: exportForm.format
    }
    
    // 添加搜索条件
    if (memberSearchForm.search) {
      params.search = memberSearchForm.search
    }
    
    // 添加部门过滤
    if (selectedDepartment.value) {
      params.department_id = selectedDepartment.value.dept_id
    }
    
    const response = await exportMembers(params)
    
    // 创建下载链接
    const blob = new Blob([response.data], {
      type: exportForm.format === 'xlsx' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv;charset=utf-8'
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
    const deptName = selectedDepartment.value ? `_${selectedDepartment.value.name}` : ''
    link.download = `邮箱成员${deptName}_${timestamp}.${exportForm.format}`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    exportDialogVisible.value = false
  } catch (error) {
    ElMessage.error('导出失败')
    console.error(error)
  } finally {
    exporting.value = false
  }
}

// 刷新成员权限
const refreshMemberPermissions = async () => {
  if (currentMember.value) {
    await loadMemberPermissions(currentMember.value.email)
  }
}

const handleDeleteMember = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除成员 "${row.name}" 吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    await deleteMember(row.email, { sync_to_api: true })
    ElMessage.success('删除成功')
    await loadMembers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

const handleMemberDialogClose = () => {
  memberFormRef.value?.resetFields()
}

const handleMemberDetailDialogClose = () => {
  currentMember.value = null
  memberPermissions.value = null
}

const saveMemberForm = async () => {
  if (!memberFormRef.value) return
  
  try {
    await memberFormRef.value.validate()
    memberSaving.value = true
    
    const formData: any = { ...memberForm }
    if (isMemberEditMode.value && !formData.password) {
      formData.password = undefined
    }
    
    // 转换权限字段为后端期望的整数格式
    if (formData.pop_smtp_enabled !== undefined) {
      formData.pop_smtp_enabled = formData.pop_smtp_enabled ? 1 : 0
    }
    if (formData.imap_smtp_enabled !== undefined) {
      formData.imap_smtp_enabled = formData.imap_smtp_enabled ? 1 : 0
    }
    if (formData.secure_login_enabled !== undefined) {
      formData.secure_login_enabled = formData.secure_login_enabled ? 1 : 0
    }
    if (formData.force_secure_login !== undefined) {
      formData.force_secure_login = formData.force_secure_login ? 1 : 0
    }
    
    console.log('发送的成员数据:', formData)
    
    if (isMemberEditMode.value) {
      await updateMember(memberForm.email, formData, { sync_to_api: memberSyncToApi.value })
      ElMessage.success('更新成功')
    } else {
      await createMember(formData, { sync_to_api: memberSyncToApi.value })
      ElMessage.success('创建成功')
    }
    
    memberDialogVisible.value = false
    await loadMembers()
  } catch (error) {
    ElMessage.error(isMemberEditMode.value ? '更新失败' : '创建失败')
    console.error(error)
  } finally {
    memberSaving.value = false
  }
}

const handleSyncMembers = async () => {
  if (!selectedDepartment.value) return
  
  // 创建加载实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步成员数据，请耐心等待...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    memberSyncing.value = true
    
    // 调用API同步成员
    const response = await syncMembersFromApi(selectedDepartment.value.dept_id)
    
    // 显示成功信息，包含同步数量和耗时
    if (response && response.data) {
      const { message } = response.data
      ElMessage.success(message || '成员同步成功')
    } else {
      ElMessage.success('成员同步成功')
    }
    
    // 重新加载成员列表
    await loadMembers()
  } catch (error: any) {
    ElMessage.error('成员同步失败: ' + (error.message || '未知错误'))
    console.error(error)
  } finally {
    // 确保在任何情况下都关闭加载动画
    loadingInstance.close()
    memberSyncing.value = false
  }
}

const handleToggleMemberStatus = async (row: any) => {
  try {
    // 使用确认对话框
    await ElMessageBox.confirm(
      `确定要${row.is_active ? '禁用' : '启用'}成员 "${row.name}" 吗？`,
      '状态切换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 调用状态切换API
    await updateMemberStatus(row.email, !row.is_active)
    ElMessage.success(`成员已${row.is_active ? '禁用' : '启用'}`)
    await loadMembers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('更新成员状态失败')
      console.error(error)
    }
  }
}

</script>

<style scoped>
.department-member-management {
  padding: 20px;
  height: calc(100vh - 100px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.tree-card,
.member-card {
  height: calc(100vh - 200px);
  overflow: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
  padding-right: 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card .card-title {
  font-weight: 500;
  font-size: 16px;
  color: #303133;
}

.permission-desc {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}

.permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.member-info {
  font-size: 14px;
  color: #606266;
}

/* 新增样式 - 与AD模块保持一致 */
.h-full {
  height: 100%;
}

.menu-button {
  display: inline-flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 14px;
  color: var(--el-button-text-color);
  border-color: var(--el-button-border-color);
  transition: all 0.3s;
}

.menu-button:hover {
  background-color: var(--el-color-primary) !important;
  color: white !important;
  border-color: var(--el-color-primary) !important;
}

.menu-button:hover .el-icon--right {
  color: white !important;
}

.menu-button:hover .menu-icon,
.menu-button:hover .el-icon--right {
  color: white !important;
}

.menu-icon {
  margin-right: 5px;
  font-size: 16px;
}

.menu-button .el-icon--right {
  margin-left: 5px;
  margin-right: 0;
  font-size: 12px;
}

.node-menu-btn {
  padding: 4px;
  width: 24px;
  height: 24px;
}

.node-menu-btn:hover {
  background-color: var(--el-color-primary);
  color: white;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 14px;
}

/* 布局样式 */
.aside-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.aside-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.aside-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.aside-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.main-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.main-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.header-left {
  display: flex;
  align-items: center;
  min-width: 200px;
  flex-shrink: 0;
  gap: 5px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 20px;
  overflow: hidden;
  min-width: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.tab-with-dept {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.current-dept {
  font-weight: normal;
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 0;
}

.no-dept-selected {
  color: #909399;
  font-size: 14px;
}

.search-input {
  width: 250px;
  margin-right: 10px;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.table-container {
  position: relative;
  min-height: 200px;
  padding: 0 16px;
}

.pagination-container {
  padding: 12px 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-placeholder {
  padding: 20px;
}

.toolbar {
  padding: 12px 16px;
}

/* 确保在小屏幕上也能正确显示 */
@media (max-width: 768px) {
  .main-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
  }

  .header-center {
    justify-content: flex-start;
    margin: 0;
  }

  .header-left {
    flex-wrap: wrap;
    gap: 10px;
  }

  .tab-with-dept {
    flex-wrap: wrap;
    gap: 5px;
  }

  .current-dept {
    margin-top: 5px;
    margin-left: 0;
    border-radius: 4px;
    width: fit-content;
  }
}

:deep(.el-form-item__label) {
  font-size: 14px;
  white-space: nowrap;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 导出对话框样式 */
.export-form {
  margin-bottom: 20px;
}

.export-info {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.export-info p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.export-info p:last-child {
  margin-bottom: 0;
}

.export-info strong {
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 