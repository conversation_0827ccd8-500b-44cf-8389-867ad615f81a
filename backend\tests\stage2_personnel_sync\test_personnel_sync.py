"""
测试人员邮箱同步功能
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_sync_config(token):
    """测试同步配置接口"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试获取同步配置 ===")
    response = requests.get(f"{BASE_URL}/api/v1/personnel-email-sync/sync/config", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        config = response.json()
        print(f"同步配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    return response.status_code == 200

def test_sync_status(token):
    """测试同步状态接口"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试获取同步状态 ===")
    response = requests.get(f"{BASE_URL}/api/v1/personnel-email-sync/sync/status", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        status = response.json()
        print(f"同步状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    return response.status_code == 200

def test_check_consistency(token):
    """测试数据一致性检查"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试数据一致性检查 ===")
    response = requests.post(f"{BASE_URL}/api/v1/personnel-email-sync/sync/check-consistency", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"一致性检查结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    return response.status_code == 200

def test_dry_run_sync(token):
    """测试试运行同步"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试试运行同步 ===")
    sync_data = {
        "full_sync": True,
        "dry_run": True
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/personnel-email-sync/sync/trigger", 
                           headers=headers, json=sync_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"试运行结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    return response.status_code == 200

def test_sync_logs(token):
    """测试同步日志接口"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试获取同步日志 ===")
    response = requests.get(f"{BASE_URL}/api/v1/personnel-email-sync/sync/logs?limit=5", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        logs = response.json()
        print(f"同步日志: {json.dumps(logs, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    return response.status_code == 200

def main():
    """主测试函数"""
    print("开始测试人员邮箱同步功能...")
    
    # 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        sys.exit(1)
    
    print(f"登录成功，获取到token: {token[:20]}...")
    
    # 测试各个接口
    tests = [
        ("同步配置", test_sync_config),
        ("同步状态", test_sync_status),
        ("数据一致性检查", test_check_consistency),
        ("试运行同步", test_dry_run_sync),
        ("同步日志", test_sync_logs)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            success = test_func(token)
            results[test_name] = "✓ 通过" if success else "✗ 失败"
        except Exception as e:
            results[test_name] = f"✗ 异常: {str(e)}"
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 检查是否所有测试都通过
    all_passed = all("✓" in result for result in results.values())
    if all_passed:
        print("\n🎉 所有测试都通过了！人员邮箱同步功能基本正常。")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
