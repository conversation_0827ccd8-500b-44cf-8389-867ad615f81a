from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, ForeignKey, Text, DateTime, BigInteger, JSON
from sqlalchemy.orm import relationship
import datetime
from app.database import Base
import uuid

def generate_uuid():
    return str(uuid.uuid4())

class Terminal(Base):
    """终端设备模型"""
    __tablename__ = "terminals"

    id = Column(String(36), primary_key=True, default=generate_uuid, index=True)
    hostname = Column(String(255), nullable=False)
    unique_id = Column(String(255), unique=True, nullable=False, index=True)
    mac_address = Column(String(50), nullable=False)
    ip_address = Column(String(50))
    agent_version = Column(String(50))
    online_status = Column(Boolean, default=False)
    last_online_time = Column(DateTime, nullable=True)
    last_offline_time = Column(DateTime, nullable=True)
    last_heartbeat_time = Column(DateTime, nullable=True)
    registration_time = Column(DateTime, default=datetime.datetime.utcnow)
    heartbeat_interval = Column(Integer, default=300)  # 心跳间隔，单位秒
    collection_interval = Column(Integer, default=86400)  # 信息采集间隔，单位秒

    # 关联
    hardware_info = relationship("HardwareInfo", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    os_info = relationship("OSInfo", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    software_list = relationship("Software", back_populates="terminal", cascade="all, delete-orphan")
    network_info = relationship("NetworkInfo", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    last_login_user = relationship("UserLoginInfo", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    commands = relationship("TerminalCommand", back_populates="terminal", cascade="all, delete-orphan") 