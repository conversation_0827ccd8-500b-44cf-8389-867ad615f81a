"""add_email_sync_logs_table

Revision ID: 732657688b92
Revises: 6b5e6c94a564
Create Date: 2025-06-03 16:55:36.948879

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '732657688b92'
down_revision: Union[str, None] = '6b5e6c94a564'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_sync_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('sync_type', sa.String(length=50), nullable=False, comment='同步类型(departments/members/groups/tags/full)'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='状态(success/failed)'),
    sa.Column('message', sa.Text(), nullable=True, comment='同步消息'),
    sa.Column('synced_count', sa.Integer(), nullable=True, comment='同步数量'),
    sa.Column('updated_count', sa.Integer(), nullable=True, comment='更新数量'),
    sa.Column('total_count', sa.Integer(), nullable=True, comment='总数量'),
    sa.Column('duration', sa.String(length=20), nullable=True, comment='耗时'),
    sa.Column('details', sa.Text(), nullable=True, comment='详细信息'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_sync_logs_id'), 'email_sync_logs', ['id'], unique=False)
    op.drop_index('ix_ad_sync_locks_lock_name', table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), nullable=True),
    sa.Column('locked_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index('ix_ad_sync_locks_lock_name', 'ad_sync_locks', ['lock_name'], unique=False)
    op.drop_index(op.f('ix_email_sync_logs_id'), table_name='email_sync_logs')
    op.drop_table('email_sync_logs')
    # ### end Alembic commands ###
