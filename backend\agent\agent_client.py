# pyright: ignore
# 忽略所有Pylance警告，因为脚本中包含Shell脚本字符串，会导致误报

import sys
import os
import time
import logging
import threading
import json
import uuid
import platform
import socket
import asyncio
import grpc
from datetime import datetime
import subprocess
import shutil
import tempfile
import requests
import queue
import random

# 配置日志
log_dir = os.path.join(os.environ.get('PROGRAMDATA', 'C:\\ProgramData') if platform.system() == 'Windows' else '/var/log', 'TerminalAgent', 'logs')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'terminal_agent.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 检测操作系统类型，导入对应的采集器
if platform.system() == 'Windows':
    try:
        from windows_collector import WindowsCollector
        logger.info("已加载Windows信息采集器")
    except ImportError as e:
        logger.error(f"导入Windows采集器失败: {str(e)}")
        WindowsCollector = None
else:
    logger.warning(f"不支持的操作系统: {platform.system()}")
    WindowsCollector = None

# 导入生成的gRPC代码
try:
    # 尝试直接导入
    from terminal_pb import terminal_pb2, terminal_pb2_grpc
    logger.info("成功导入gRPC协议定义")
except ImportError as e:
    logger.error(f"导入gRPC协议定义失败: {str(e)}")

    # 尝试多种导入路径
    import_success = False

    # 尝试1: 当前目录
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        from terminal_pb import terminal_pb2, terminal_pb2_grpc
        logger.info("成功从当前目录导入gRPC协议定义")
        import_success = True
    except ImportError:
        pass

    # 尝试2: 直接导入文件
    if not import_success:
        try:
            terminal_pb_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "terminal_pb")
            if terminal_pb_dir not in sys.path:
                sys.path.insert(0, terminal_pb_dir)
            import terminal_pb2, terminal_pb2_grpc
            logger.info("成功从terminal_pb目录直接导入协议定义")
            import_success = True
        except ImportError:
            pass

    # 尝试3: 内联创建模块
    if not import_success:
        try:
            # 如果是打包版本，检查是否存在内嵌文件
            pb_files_dir = os.path.join(getattr(sys, '_MEIPASS', os.path.dirname(os.path.abspath(__file__))), "terminal_pb")
            if os.path.exists(pb_files_dir):
                if pb_files_dir not in sys.path:
                    sys.path.insert(0, pb_files_dir)
                import terminal_pb2, terminal_pb2_grpc
                logger.info("成功从打包目录导入协议定义")
                import_success = True
        except (ImportError, AttributeError):
            pass

    # 尝试4: 检查是否存在直接的.py文件
    if not import_success:
        try:
            module_dir = os.path.dirname(os.path.abspath(__file__))
            if os.path.exists(os.path.join(module_dir, "terminal_pb2.py")) and os.path.exists(os.path.join(module_dir, "terminal_pb2_grpc.py")):
                sys.path.insert(0, module_dir)
                import terminal_pb2, terminal_pb2_grpc
                logger.info("成功从当前目录直接导入.py文件")
                import_success = True
        except ImportError:
            pass

    # 如果所有方法都失败
    if not import_success:
        logger.critical("无法导入gRPC协议定义，Agent将无法与服务器通信")
        terminal_pb2 = None
        terminal_pb2_grpc = None

# Agent配置
class AgentConfig:
    """Agent配置"""
    def __init__(self):
        self.server_address = "localhost:50051"  # 服务器地址
        self.api_server_address = "http://localhost:8000"  # API服务器地址（用于版本检查等）
        self.terminal_id = ""  # 终端ID，由服务器分配
        self.unique_id = self._generate_unique_id()  # 生成唯一标识
        self.heartbeat_interval = 300  # 心跳间隔，默认300秒（5分钟）
        self.collection_interval = 86400  # 信息采集间隔，默认24小时
        self.version_check_interval = 86400  # 版本检查间隔，默认24小时
        self.auto_upgrade = False  # 是否自动升级
        self.upgrade_window_start = "02:00"  # 升级窗口开始时间
        self.upgrade_window_end = "06:00"  # 升级窗口结束时间
        self.upgrade_retry_max = 3  # 升级重试次数
        self.upgrade_backup_enabled = True  # 是否启用升级备份
        self.agent_version = self._get_agent_version()  # Agent版本
        self.use_tls = False  # 是否使用TLS加密
        self.cert_file = ""  # 客户端证书文件

    def _generate_unique_id(self):
        """生成唯一标识"""
        # 基于硬件信息生成唯一标识
        # 在实际应用中，可以使用主板序列号、BIOS序列号等生成更稳定的唯一标识
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                      for elements in range(0, 2*6, 2)][::-1])
        return f"{platform.node()}-{mac}"

    def _get_agent_version(self):
        """获取Agent版本号
        
        优先级：
        1. 打包时注入的版本文件 (__version__.py)
        2. 默认配置文件 (default_config.json)
        3. 硬编码默认值 (1.0.0)
        """
        try:
            # 优先级1: 从打包时注入的版本文件读取
            try:
                import __version__
                version = getattr(__version__, '__version__', None) or getattr(__version__, 'VERSION', None)
                if version:
                    logger.info(f"从版本文件读取版本号: {version}")
                    return version
            except ImportError:
                pass
            
            # 优先级2: 从默认配置文件读取
            default_config = 'default_config.json'
            if os.path.exists(default_config):
                try:
                    with open(default_config, 'r') as f:
                        default_data = json.load(f)
                    version = default_data.get('agent_version')
                    if version:
                        logger.info(f"从默认配置读取版本号: {version}")
                        return version
                except Exception as e:
                    logger.warning(f"读取默认配置版本失败: {e}")
            
            # 优先级3: 硬编码默认值
            logger.info("使用硬编码默认版本号: 1.0.0")
            return "1.0.0"
            
        except Exception as e:
            logger.error(f"获取Agent版本号失败: {e}，使用默认版本")
            return "1.0.0"

    def load_config(self, config_file):
        """从配置文件加载配置"""
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    self.server_address = config.get("server_address", self.server_address)
                    self.api_server_address = config.get("api_server_address", self.api_server_address)
                    self.terminal_id = config.get("terminal_id", "")
                    self.heartbeat_interval = config.get("heartbeat_interval", self.heartbeat_interval)
                    self.collection_interval = config.get("collection_interval", self.collection_interval)
                    self.version_check_interval = config.get("version_check_interval", self.version_check_interval)
                    self.auto_upgrade = config.get("auto_upgrade", self.auto_upgrade)
                    self.agent_version = config.get("agent_version", self.agent_version)
                    self.use_tls = config.get("use_tls", self.use_tls)
                    self.cert_file = config.get("cert_file", self.cert_file)
                    self.unique_id = config.get("unique_id", self.unique_id)
                logger.info(f"从 {config_file} 加载配置成功")
                logger.info(f"当前版本号: {self.agent_version}")
            else:
                logger.warning(f"配置文件 {config_file} 不存在，使用默认配置")

                # 尝试从当前目录或安装目录找到默认配置
                default_config = 'default_config.json'
                if os.path.exists(default_config):
                    try:
                        with open(default_config, 'r') as f:
                            default_data = json.load(f)
                        self.agent_version = default_data.get('agent_version', self.agent_version)
                        logger.info(f"从默认配置加载版本号: {self.agent_version}")
                    except Exception as e:
                        logger.error(f"加载默认配置失败: {str(e)}")


        except Exception as e:
            logger.error(f"加载配置失败: {str(e)}")

    def save_config(self, config_file):
        """保存配置到文件 - 智能配置保存：避免保存临时ID"""
        try:
            # 检查terminal_id是否为临时ID
            is_temporary_id = (
                self.terminal_id and (
                    self.terminal_id.startswith("local-") or 
                    self.terminal_id.startswith("temp-")
                )
            )
            
            # 如果当前ID是临时ID，则不保存它，保持配置文件中的原有ID
            terminal_id_to_save = self.terminal_id
            if is_temporary_id:
                logger.info(f"检测到临时ID {self.terminal_id}，将不保存到配置文件")
                # 尝试从现有配置文件读取原有的正式ID
                try:
                    if os.path.exists(config_file):
                        with open(config_file, 'r') as f:
                            existing_config = json.load(f)
                            existing_id = existing_config.get("terminal_id", "")
                            if existing_id and not existing_id.startswith(("local-", "temp-")):
                                terminal_id_to_save = existing_id
                                logger.info(f"保持配置文件中的原有正式ID: {existing_id}")
                            else:
                                terminal_id_to_save = ""  # 不保存临时ID
                                logger.info("配置文件中没有有效的正式ID，不保存临时ID")
                except Exception as e:
                    logger.warning(f"读取现有配置失败: {str(e)}，不保存临时ID")
                    terminal_id_to_save = ""

            config = {
                "server_address": self.server_address,
                "api_server_address": self.api_server_address,
                "terminal_id": terminal_id_to_save,
                "unique_id": self.unique_id,
                "heartbeat_interval": self.heartbeat_interval,
                "collection_interval": self.collection_interval,
                "version_check_interval": self.version_check_interval,
                "auto_upgrade": self.auto_upgrade,
                "agent_version": self.agent_version,
                "use_tls": self.use_tls,
                "cert_file": self.cert_file
            }

            # 确保目录存在
            config_dir = os.path.dirname(config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir, exist_ok=True)

            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)

            if is_temporary_id:
                logger.info(f"配置已保存到 {config_file}（临时ID未保存）")
            else:
                logger.info(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")
            return False


class TerminalAgent:
    """终端Agent客户端实现"""
    def __init__(self, config_file=None):
        """初始化Agent"""
        # 记录启动时间
        self._start_time = time.time()

        # 加载配置
        self.config = AgentConfig()
        if config_file:
            self.load_config(config_file)

        self.last_collection_time = 0  # 上次信息采集时间戳
        self.running = True  # 运行状态
        self.channel = None  # gRPC通道
        self.stub = None     # gRPC存根
        self.collector = None  # 信息采集器

        # 命令执行锁，防止多个命令同时执行
        self.command_lock = threading.Lock()
        self.executing_commands = set()  # 记录正在执行的命令ID

        # 后台线程列表
        self.threads = {}
        self.shutdown_event = threading.Event()

        # 心跳状态
        self.last_heartbeat_time = 0
        self.heartbeat_failures = 0
        self.heartbeat_max_failures = 5  # 最大连续失败次数
        self.heartbeat_backoff_factor = 1.5  # 退避因子
        self.heartbeat_retry_interval = 300  # 初始重试间隔（秒），与心跳间隔相同
        self.heartbeat_lock = threading.Lock()  # 心跳锁，防止多个心跳同时执行

        # 创建日志目录
        if not os.path.exists('logs'):
            os.makedirs('logs')

        # 初始化信息采集器
        self._initialize_collector()

        # 命令处理器映射
        self.command_handlers = None

        # 初始化命令处理器
        self._initialize_command_handlers()

        # 启动本地gRPC服务，用于接收命令通知
        self._start_notification_service()

        # 初始化完成
        logger.info("Agent初始化完成")

    def initialize(self, config_file):
        """加载配置并连接到服务器"""
        if config_file:
            self.load_config(config_file)

        # 确保命令处理器已初始化
        if self.command_handlers is None:
            self._initialize_command_handlers()

        # 连接服务器
        connection_success = self.connect_to_server()

        # 启动后台线程
        self._start_background_threads()

        # 智能注册处理
        if not self.config.terminal_id:
            # 没有terminal_id，需要注册
            logger.info("没有终端ID，尝试注册新终端...")
            success = self.register()
            return success
        else:
            # 已有terminal_id，检查是否为正式ID
            is_formal_id = (
                not self.config.terminal_id.startswith("local-") and
                not self.config.terminal_id.startswith("temp-") and
                self.config.terminal_id.strip() != ""
            )
            
            if is_formal_id:
                # 使用已有的正式终端ID，这是重连场景
                logger.info(f"使用已有的正式终端ID: {self.config.terminal_id}")
                if connection_success:
                    logger.info("连接成功，终端已准备就绪")
                    return True
                else:
                    logger.warning("连接失败，但保持现有ID，将在后台尝试重连")
                    return True  # 返回True让Agent继续运行，等待重连
            else:
                # 临时ID，尝试重新注册
                logger.info(f"检测到临时终端ID: {self.config.terminal_id}，尝试重新注册...")
                if connection_success:
                    success = self.register()
                    return success
                else:
                    logger.warning("连接失败，将保持临时ID等待服务器恢复")
                    return True  # 让Agent继续运行，等待连接恢复

    def connect_to_server(self):
        """
        连接到服务器

        Returns:
            bool: 是否成功连接
        """
        try:
            # 检查服务器地址格式
            if not self.config.server_address or ":" not in self.config.server_address:
                logger.error(f"服务器地址格式无效: {self.config.server_address}")
                logger.info("服务器地址应为 host:port 格式")
                return False

            # 解析服务器地址
            host, port = self.config.server_address.split(":")
            try:
                port = int(port)
                if not (0 < port < 65536):
                    logger.error(f"端口号无效: {port}")
                    return False
            except ValueError:
                logger.error(f"端口号不是有效的数字: {port}")
                return False

            logger.info(f"正在连接到服务器: {self.config.server_address}")

            # 创建通道选项
            options = [
                ('grpc.max_receive_message_length', 100 * 1024 * 1024),  # 100MB
                ('grpc.max_send_message_length', 100 * 1024 * 1024),     # 100MB
                ('grpc.keepalive_time_ms', 30000),                       # 30秒发送一次keepalive ping
                ('grpc.keepalive_timeout_ms', 10000),                    # 10秒没有响应则认为连接断开
                ('grpc.keepalive_permit_without_calls', True),           # 允许在没有调用的情况下发送keepalive
                ('grpc.http2.max_pings_without_data', 0),                # 允许无限的ping帧
                ('grpc.http2.min_time_between_pings_ms', 10000),         # ping之间的最小时间
                ('grpc.http2.min_ping_interval_without_data_ms', 5000),  # 无数据时ping间隔
            ]

            if self.config.use_tls:
                # 使用TLS加密
                logger.info("使用TLS加密连接")
                cert_data = None
                if self.config.cert_file and os.path.exists(self.config.cert_file):
                    try:
                        with open(self.config.cert_file, 'rb') as f:
                            cert_data = f.read()
                        logger.info(f"已加载TLS证书: {self.config.cert_file}")
                    except Exception as e:
                        logger.error(f"读取证书文件失败: {str(e)}")
                        return False
                else:
                    logger.warning(f"TLS证书文件不存在: {self.config.cert_file}")
                    logger.warning("将使用不安全的TLS连接")

                try:
                    credentials = grpc.ssl_channel_credentials(root_certificates=cert_data)
                    self.channel = grpc.secure_channel(self.config.server_address, credentials, options=options)
                    logger.info(f"已创建TLS加密通道")
                except Exception as e:
                    logger.error(f"创建TLS通道失败: {str(e)}")
                    return False
            else:
                # 使用非加密连接
                try:
                    logger.info("使用非加密连接")
                    self.channel = grpc.insecure_channel(self.config.server_address, options=options)
                    logger.info(f"已创建非加密通道")
                except Exception as e:
                    logger.error(f"创建非加密通道失败: {str(e)}")
                    return False

            # 创建gRPC存根
            try:
                self.stub = terminal_pb2_grpc.TerminalManagementStub(self.channel)
                logger.debug("已创建gRPC存根")
            except Exception as e:
                logger.error(f"创建gRPC存根失败: {str(e)}")
                # 关闭创建的通道
                if self.channel:
                    try:
                        self.channel.close()
                    except:
                        pass
                self.channel = None
                return False

            # 测试连接
            try:
                # 尝试进行简单的连接测试，使用2秒超时
                logger.info("正在测试连接...")
                grpc.channel_ready_future(self.channel).result(timeout=2)
                logger.info(f"已成功连接到服务器: {self.config.server_address}")
                return True
            except grpc.FutureTimeoutError:
                logger.warning(f"连接服务器超时: {self.config.server_address}")
                return False
            except Exception as e:
                logger.error(f"连接测试失败: {str(e)}")
                return False

        except Exception as e:
            logger.error(f"创建与服务器的连接失败: {str(e)}")
            return False

    def register(self):
        """
        向服务器注册终端 - 智能注册：区分首次注册和重连场景
        """
        try:
            logger.info("向服务器注册终端...")

            # 检查是否已有有效的正式terminal_id
            has_existing_formal_id = (
                self.config.terminal_id and 
                not self.config.terminal_id.startswith("local-") and
                not self.config.terminal_id.startswith("temp-") and
                self.config.terminal_id.strip() != ""
            )
            
            if has_existing_formal_id:
                # 已有正式ID，这是重连场景，不应该重新注册
                logger.info(f"检测到已有正式终端ID: {self.config.terminal_id}")
                logger.info("这是重连场景，将保持现有ID等待服务器恢复，不生成新的临时ID")
                
                if not self.stub:
                    logger.warning("gRPC存根未初始化，但保持现有ID等待连接恢复")
                    return False
                
                # 尝试用现有ID验证连接，但不生成新ID
                try:
                    # 可以尝试一个简单的连接测试，但失败时不修改ID
                    logger.debug("尝试验证现有ID的有效性...")
                    # 这里不做实际注册，只是标记为重连模式
                    return False  # 返回False表示需要等待服务器恢复，但不生成临时ID
                except Exception as e:
                    logger.info(f"现有ID验证失败，这是正常情况（服务器可能仍离线）: {str(e)}")
                    return False

            if not self.stub:
                logger.error("gRPC存根未初始化，无法注册终端")
                return False

            # 首次注册流程：没有正式ID或只有临时ID
            logger.info("执行首次注册流程...")

            # 获取主机信息
            hostname = platform.node()
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                                  for elements in range(0, 2*6, 2)][::-1])
            ip_address = socket.gethostbyname(socket.gethostname())
            os_info = f"{platform.system()} {platform.version()}"

            # 创建注册请求
            request = terminal_pb2.RegisterRequest(
                hostname=hostname,
                mac_address=mac_address,
                ip_address=ip_address,
                agent_version=self.config.agent_version,
                os_info=os_info,
                unique_id=self.config.unique_id
            )

            # 设置RPC调用超时时间
            try:
                # 调用远程注册接口
                response = self.stub.RegisterTerminal(request, timeout=10)  # 10秒超时

                if response.success:
                    logger.info(f"终端注册成功，分配的终端ID: {response.terminal_id}")
                    # 更新配置
                    logger.info(f"注册前心跳间隔值: {self.config.heartbeat_interval}秒")
                    logger.info(f"服务器返回心跳间隔值: {response.heartbeat_interval}秒")
                    self.config.terminal_id = response.terminal_id
                    self.config.heartbeat_interval = response.heartbeat_interval
                    self.config.collection_interval = response.collection_interval
                    logger.info(f"服务器配置的心跳间隔为: {response.heartbeat_interval}秒")
                    logger.info(f"更新后的心跳间隔为: {self.config.heartbeat_interval}秒")
                    logger.info(f"服务器配置的信息采集间隔为: {response.collection_interval}秒")
                    return True
                else:
                    logger.error(f"终端注册失败: {response.message}")
                    return False
            except grpc.RpcError as rpc_err:
                logger.error(f"注册RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                
                # 只在首次注册失败时才生成临时ID
                if not has_existing_formal_id:
                    logger.warning("首次注册失败，生成临时ID等待服务器恢复")
                    self.config.terminal_id = f"local-{uuid.uuid4()}"
                else:
                    logger.info("重连时注册失败，保持原有ID不变")
                
                return False

        except Exception as e:
            logger.error(f"注册过程中发生错误: {str(e)}")
            return False

    def send_heartbeat(self):
        """
        发送心跳

        Returns:
            bool: 心跳是否成功
        """
        start_time = time.time()
        try:
            if not self.stub:
                logger.error("gRPC存根未初始化，无法发送心跳")
                # 尝试重新连接
                reconnect_success = self._try_reconnect()
                if not reconnect_success:
                    logger.error("重新连接服务器失败，心跳将在下次尝试")
                return False

            # 创建心跳请求
            request = terminal_pb2.HeartbeatRequest(
                terminal_id=self.config.terminal_id,
                timestamp=int(time.time())
            )

            try:
                # 调用远程心跳接口
                logger.debug(f"向服务器 {self.config.server_address} 发送心跳请求，终端ID: {self.config.terminal_id}")
                response = self.stub.Heartbeat(request, timeout=5)  # 5秒超时

                if response.success:
                    elapsed = time.time() - start_time
                    logger.debug(f"心跳发送成功, 耗时: {elapsed:.2f}秒")
                    # 检查是否有待执行的命令
                    if response.has_command:
                        logger.info("服务器有待执行的命令，开始接收命令...")
                        # 使用单独的线程处理命令接收，避免阻塞心跳线程
                        cmd_thread = threading.Thread(
                            target=self.receive_commands,
                            name="CommandReceiver",
                            daemon=True
                        )
                        cmd_thread.start()
                    return True
                else:
                    logger.warning(f"心跳发送失败: 服务器返回失败响应")
                    return False

            except grpc.RpcError as rpc_err:
                if rpc_err.code() == grpc.StatusCode.UNIMPLEMENTED:
                    logger.error(f"服务器不支持心跳方法，请检查gRPC接口定义是否匹配")
                    # 这是接口不匹配问题，不需要重连
                elif rpc_err.code() == grpc.StatusCode.UNAVAILABLE:
                    logger.error(f"服务器不可用: {rpc_err.details()}")
                    self._try_reconnect()
                elif rpc_err.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                    logger.error(f"心跳请求超时")
                    # 服务器可能繁忙，不一定需要立即重连
                else:
                    logger.error(f"心跳RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                    self._try_reconnect()
                return False

        except Exception as e:
            logger.error(f"发送心跳时发生错误: {str(e)}")
            self._try_reconnect()
            return False

    def _try_reconnect(self, max_attempts=3, retry_interval=2):
        """
        尝试重新连接服务器

        Args:
            max_attempts (int): 最大重试次数
            retry_interval (int): 初始重试间隔（秒）

        Returns:
            bool: 是否成功重连
        """
        try:
            logger.info(f"尝试重新连接服务器 {self.config.server_address}...")

            # 关闭现有连接
            if self.channel:
                try:
                    self.channel.close()
                except Exception as e:
                    logger.warning(f"关闭旧连接时发生错误: {str(e)}")
                finally:
                    # 无论是否成功关闭，都清除现有引用
                    self.channel = None
                    self.stub = None

            # 重试连接
            for attempt in range(1, max_attempts + 1):
                logger.info(f"连接尝试 {attempt}/{max_attempts}...")

                try:
                    connected = self.connect_to_server()
                    if connected:
                        logger.info(f"服务器重连成功")
                        return True

                    if attempt < max_attempts:
                        wait_time = retry_interval * (2 ** (attempt - 1))  # 指数退避
                        logger.info(f"连接失败，将在{wait_time}秒后重试...")
                        time.sleep(wait_time)
                except Exception as e:
                    logger.error(f"连接尝试 {attempt} 出错: {str(e)}")
                    if attempt < max_attempts:
                        wait_time = retry_interval * (2 ** (attempt - 1))
                        logger.info(f"连接错误，将在{wait_time}秒后重试...")
                        time.sleep(wait_time)

            logger.error(f"在{max_attempts}次尝试后仍无法连接到服务器")
            return False

        except Exception as e:
            logger.error(f"重新连接服务器过程中发生未预期错误: {str(e)}")
            return False

    def collect_and_report_info(self):
        """
        采集并上报终端信息
        """
        try:
            if not self.collector:
                logger.error("信息采集器未初始化，无法采集信息")
                return False

            if not self.stub:
                logger.error("gRPC存根未初始化，无法上报信息")
                self._try_reconnect()
                return False

            logger.info("开始采集终端信息...")

            # 采集硬件信息
            hw_info = self.collector.collect_hardware_info()

            # 转换为gRPC格式
            disks_proto = []
            for disk in hw_info.get("disks", []):
                disk_proto = terminal_pb2.DiskInfo(
                    name=disk.get("name", ""),
                    total_space=disk.get("total_space", 0),
                    free_space=disk.get("free_space", 0),
                    filesystem=disk.get("filesystem", ""),
                    mount_point=disk.get("mount_point", "")
                )
                disks_proto.append(disk_proto)

            hardware_info = terminal_pb2.HardwareInfo(
                cpu_model=hw_info.get("cpu_model", ""),
                cpu_cores=hw_info.get("cpu_cores", 0),
                memory_total=hw_info.get("memory_total", 0),
                disks=disks_proto,
                serial_number=hw_info.get("serial_number", ""),
                manufacturer=hw_info.get("manufacturer", ""),
                model=hw_info.get("model", "")
            )

            # 采集操作系统信息
            os_info_dict = self.collector.collect_os_info()

            # 转换为gRPC格式
            security_info = terminal_pb2.SecurityInfo(
                firewall_enabled=os_info_dict.get("security", {}).get("firewall_enabled", False),
                antivirus=os_info_dict.get("security", {}).get("antivirus", "Unknown"),
                antivirus_enabled=os_info_dict.get("security", {}).get("antivirus_enabled", False)
            )

            os_info = terminal_pb2.OSInfo(
                name=os_info_dict.get("name", ""),
                version=os_info_dict.get("version", ""),
                build=os_info_dict.get("build", ""),
                architecture=os_info_dict.get("architecture", ""),
                install_date=os_info_dict.get("install_date", ""),
                installed_updates=os_info_dict.get("installed_updates", []),
                security=security_info
            )

            # 采集已安装软件
            software_dict_list = self.collector.collect_software_info()

            # 转换为gRPC格式
            installed_software = []
            for sw in software_dict_list:
                sw_proto = terminal_pb2.Software(
                    name=sw.get("name", ""),
                    version=sw.get("version", ""),
                    publisher=sw.get("publisher", ""),
                    install_date=sw.get("install_date", ""),
                    size=sw.get("size", 0),
                    install_location=sw.get("install_location", "")
                )
                installed_software.append(sw_proto)

            # 采集网络信息
            network_dict = self.collector.collect_network_info()

            # 转换为gRPC格式
            interfaces_proto = []
            for iface in network_dict.get("interfaces", []):
                iface_proto = terminal_pb2.NetworkInterface(
                    name=iface.get("name", ""),
                    mac_address=iface.get("mac_address", ""),
                    ip_address=iface.get("ip_address", ""),
                    subnet_mask=iface.get("subnet_mask", ""),
                    dhcp_enabled=iface.get("dhcp_enabled", False),
                    is_connected=iface.get("is_connected", False)
                )
                interfaces_proto.append(iface_proto)

            network_info = terminal_pb2.NetworkInfo(
                interfaces=interfaces_proto,
                hostname=network_dict.get("hostname", ""),
                domain=network_dict.get("domain", ""),
                dns_servers=network_dict.get("dns_servers", []),
                default_gateway=network_dict.get("default_gateway", "")
            )

            # 采集最后登录用户信息
            user_dict = self.collector.collect_user_info()

            # 转换为gRPC格式
            last_login_user = terminal_pb2.UserInfo(
                username=user_dict.get("username", ""),
                full_name=user_dict.get("full_name", ""),
                login_time=user_dict.get("login_time", ""),
                domain=user_dict.get("domain", "")
            )

            # 创建完整的信息上报请求
            request = terminal_pb2.TerminalInfoReport(
                terminal_id=self.config.terminal_id,
                hardware=hardware_info,
                os=os_info,
                installed_software=installed_software,
                network=network_info,
                last_login_user=last_login_user,
                timestamp=int(time.time())
            )

            try:
                # 调用远程信息上报接口
                response = self.stub.ReportTerminalInfo(request, timeout=30)  # 30秒超时

                if response.success:
                    logger.info("终端信息上报成功")
                    # 更新最后采集时间
                    self.last_collection_time = time.time()
                    return True
                else:
                    logger.warning(f"终端信息上报失败: {response.message}")
                    return False
            except grpc.RpcError as rpc_err:
                if rpc_err.code() == grpc.StatusCode.UNIMPLEMENTED:
                    logger.error("服务器不支持上报信息方法，请检查gRPC接口定义是否匹配")
                    # 这是接口不匹配问题，不需要重连
                else:
                    logger.error(f"信息上报RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                    self._try_reconnect()
                return False

        except Exception as e:
            logger.error(f"采集和上报信息时发生错误: {str(e)}")
            return False

    def receive_commands(self):
        """
        接收服务器下发的命令
        """
        try:
            if not self.stub:
                logger.error("gRPC存根未初始化，无法接收命令")
                self._try_reconnect()
                return False

            # 创建接收命令请求
            request = terminal_pb2.CommandRequest(
                terminal_id=self.config.terminal_id
            )

            logger.info("开始接收命令...")

            try:
                # 调用远程接收命令接口 - 流式RPC
                for command in self.stub.ReceiveCommands(request, timeout=15):
                    # 将gRPC command对象转换为字典，便于处理
                    # 获取命令类型名称，处理整数值情况
                    if hasattr(command.type, 'name'):
                        type_name = command.type.name
                    else:
                        # 手动将整数映射到枚举名称
                        type_map = {
                            0: "COLLECT_INFO",
                            1: "UPGRADE_AGENT",
                            2: "CUSTOM_COMMAND",
                            3: "UNINSTALL_SOFTWARE"
                        }
                        type_name = type_map.get(command.type, f"UNKNOWN_{command.type}")

                    cmd_dict = {
                        "command_id": command.command_id,
                        "type": type_name,  # 使用类型名称
                        "content": command.content,
                        "create_time": command.create_time,
                        "timeout": command.timeout
                    }
                    self._execute_command(cmd_dict)

                return True
            except grpc.RpcError as rpc_err:
                if rpc_err.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                    # 超时通常意味着没有命令
                    logger.debug("接收命令超时，可能没有待执行的命令")
                    return True
                elif rpc_err.code() == grpc.StatusCode.UNIMPLEMENTED:
                    logger.error("服务器不支持接收命令方法，请检查gRPC接口定义是否匹配")
                    # 这是接口不匹配问题，不需要重连
                else:
                    logger.error(f"接收命令RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                    self._try_reconnect()
                return False

        except Exception as e:
            logger.error(f"接收命令时发生错误: {str(e)}")
            return False

    def _execute_command(self, command):
        """
        执行命令
        """
        command_id = command.get("command_id", "")
        command_type = command.get("type", "")
        content = command.get("content", "")

        logger.info(f"收到命令: ID={command_id}, 类型={command_type}")

        execution_time = int(time.time())
        start_time = time.time()

        # 判断是否是重量级命令（需要锁定）
        is_heavyweight = False
        lock_acquired = False

        try:
            # 检查命令类型是否有效
            if not command_type:
                error_msg = "命令类型为空"
                logger.warning(error_msg)
                self._report_command_result(
                    command_id,
                    False,
                    "",
                    error_msg,
                    execution_time,
                    int((time.time() - start_time) * 1000)
                )
                return False

            # 查找处理函数 - 修复命令类型处理逻辑
            handler = None
            if isinstance(command_type, int):
                # 如果命令类型是整数（枚举值），直接查找
                handler = self.command_handlers.get(command_type)
            else:
                # 将命令类型转换为大写，以便与字典匹配
                command_type_upper = str(command_type).upper()

                # 创建命令类型到枚举值的映射
                command_type_map = {
                    "COLLECT_INFO": 0,
                    "UPGRADE_AGENT": 1,
                    "CUSTOM_COMMAND": 2,
                    "UNINSTALL_SOFTWARE": 3,
                    "REGISTRY_OPERATION": 4,
                    "STATUS_QUERY": 99
                }

                # 尝试通过名称查找枚举值
                if command_type_upper in command_type_map:
                    enum_value = command_type_map[command_type_upper]
                    handler = self.command_handlers.get(enum_value)
                else:
                    # 尝试通过字符串匹配
                    for k, v in self.command_handlers.items():
                        # 尝试处理枚举值和字符串的情况
                        if (isinstance(k, int) and str(k) == command_type_upper) or \
                           (hasattr(k, 'name') and k.name.upper() == command_type_upper):
                            handler = v
                            break

            if not handler:
                success = False
                output = ""
                error = f"不支持的命令类型: {command_type}"
                logger.warning(error)

                # 上报执行结果
                self._report_command_result(
                    command_id,
                    success,
                    output,
                    error,
                    execution_time,
                    int((time.time() - start_time) * 1000)
                )
                return False

            # 检查是否是重量级命令（需要锁定）
            if isinstance(command_type, int):
                is_heavyweight = command_type in [1, 3]  # UPGRADE_AGENT, UNINSTALL_SOFTWARE
            else:
                command_type_str = str(command_type).upper()
                is_heavyweight = "UPGRADE" in command_type_str or "UNINSTALL" in command_type_str

            # 如果是重量级命令，尝试获取锁
            if is_heavyweight and hasattr(self, 'command_lock'):
                try:
                    logger.debug(f"命令 {command_id} 尝试获取执行锁")
                    # 非阻塞方式获取锁
                    lock_acquired = self.command_lock.acquire(blocking=False)

                    if not lock_acquired:
                        logger.warning(f"命令 {command_id} 无法获取执行锁，已有其他命令在执行")
                        # 上报无法执行的结果
                        self._report_command_result(
                            command_id,
                            False,
                            "",
                            "已有其他重要命令正在执行，请稍后重试",
                            execution_time,
                            int((time.time() - start_time) * 1000)
                        )
                        return False

                    # 记录正在执行的命令
                    if hasattr(self, 'executing_commands'):
                        self.executing_commands.add(command_id)

                    logger.debug(f"命令 {command_id} 已获取执行锁")
                except Exception as e:
                    logger.error(f"获取命令锁时出错: {str(e)}")
                    # 继续执行，不因锁问题中断命令

            # 执行命令
            success, output, error = handler(content, command_id)

            execution_duration = int((time.time() - start_time) * 1000)  # 毫秒

            # 上报执行结果
            self._report_command_result(
                command_id,
                success,
                output,
                error,
                execution_time,
                execution_duration
            )

            return success

        except Exception as e:
            error_msg = f"执行命令时发生错误: {str(e)}"
            logger.error(error_msg)

            execution_duration = int((time.time() - start_time) * 1000)  # 毫秒

            # 上报执行失败结果
            self._report_command_result(
                command_id,
                False,
                "",
                error_msg,
                execution_time,
                execution_duration
            )

            return False

        finally:
            # 清理锁资源
            if is_heavyweight and lock_acquired:
                try:
                    if hasattr(self, 'executing_commands') and command_id in self.executing_commands:
                        self.executing_commands.remove(command_id)

                    if hasattr(self, 'command_lock'):
                        self.command_lock.release()
                        logger.debug(f"命令 {command_id} 已释放执行锁")
                except Exception as e:
                    logger.error(f"释放命令锁时出错: {str(e)}")

    def _report_command_result(self, command_id, success, output, error, execution_time, execution_duration):
        """
        向服务器上报命令执行结果

        Args:
            command_id (str): 命令ID
            success (bool): 执行是否成功
            output (str): 命令输出内容
            error (str): 错误信息
            execution_time (int): 执行时间戳
            execution_duration (int): 执行耗时（毫秒）

        Returns:
            bool: 上报是否成功
        """
        try:
            # 验证参数
            if not command_id:
                logger.error("无法上报命令结果：命令ID为空")
                return False

            if not isinstance(success, bool):
                logger.warning(f"命令成功状态类型错误，应为布尔值，实际为: {type(success).__name__}")
                success = bool(success)  # 尝试转换为布尔值

            # 确保output和error为字符串
            output = str(output) if output is not None else ""
            error = str(error) if error is not None else ""

            # 检查连接状态
            if not self.stub:
                logger.error("gRPC存根未初始化，无法上报命令执行结果")
                return False

            # 创建上报请求
            try:
                request = terminal_pb2.CommandResult(
                    command_id=command_id,
                    terminal_id=self.config.terminal_id,
                    success=success,
                    output=output,
                    error=error,
                    execution_time=execution_time,
                    execution_duration=execution_duration
                )
            except Exception as req_err:
                logger.error(f"创建命令结果请求对象失败: {str(req_err)}")
                return False

            try:
                # 调用远程接口上报结果
                logger.debug(f"上报命令 {command_id} 执行结果: 成功={success}")
                response = self.stub.ReportCommandResult(request, timeout=5)  # 5秒超时

                # 服务器正确接收了结果
                if response and hasattr(response, 'received') and response.received:
                    logger.info(f"命令执行结果上报成功: {command_id}")
                    return True
                else:
                    # 只有真正的错误才记录警告
                    msg = getattr(response, 'message', '未知错误') if response else '服务器无响应'
                    logger.warning(f"命令执行结果上报失败: {msg}")
                    return False
            except grpc.RpcError as rpc_err:
                logger.error(f"上报命令结果RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                # 尝试重连
                if str(rpc_err.code()) in ['UNAVAILABLE', 'DEADLINE_EXCEEDED', 'CANCELLED']:
                    logger.info("尝试重新连接服务器...")
                    self._try_reconnect()
                return False
            except Exception as call_err:
                logger.error(f"调用上报命令结果接口时出错: {str(call_err)}")
                return False

        except Exception as e:
            logger.error(f"上报命令执行结果时发生错误: {e.__class__.__name__}: {str(e)}")
            import traceback
            logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    def _report_notification_port(self):
        """
        向服务器报告通知服务端口
        """
        try:
            # 等待终端ID分配和连接建立
            max_wait = 60  # 最多等待60秒
            wait_step = 2
            for _ in range(max_wait // wait_step):
                if self.config.terminal_id and self.stub:
                    break
                time.sleep(wait_step)

            if not self.config.terminal_id or not self.stub:
                logger.warning("无法上报通知服务端口：终端ID未分配或连接未建立")
                return

            # TODO: 添加上报端口的接口调用，这需要在服务器端添加相应的接口
            # 暂时在日志中记录，供参考
            logger.info(f"终端 {self.config.terminal_id} 的通知服务端口为 {self.notification_port}")

        except Exception as e:
            logger.error(f"上报通知服务端口失败: {str(e)}")

    def load_config(self, config_file):
        """
        加载配置文件
        """
        try:
            # 检查配置文件路径
            if not config_file:
                logger.warning("未指定配置文件路径，使用默认配置")
                return

            # 检查文件是否存在
            if not os.path.exists(config_file):
                logger.warning(f"配置文件 {config_file} 不存在，尝试创建默认配置")
                try:
                    # 确保目录存在
                    config_dir = os.path.dirname(os.path.abspath(config_file))
                    if config_dir and not os.path.exists(config_dir):
                        os.makedirs(config_dir, exist_ok=True)

                    # 保存默认配置
                    self.config.save_config(config_file)
                    logger.info(f"已创建默认配置文件: {config_file}")
                except Exception as e:
                    logger.error(f"无法创建默认配置文件: {str(e)}")
                return

            # 检查文件访问权限
            if not os.access(config_file, os.R_OK):
                logger.error(f"没有权限读取配置文件: {config_file}")
                return

            # 使用AgentConfig的方法加载配置
            self.config.load_config(config_file)
            logger.info(f"已加载配置，服务器地址: {self.config.server_address}")
            logger.info(f"当前版本号: {self.config.agent_version}")

            # 检查是否需要更新版本号
            try:
                # 尝试从当前目录或安装目录找到默认配置
                default_config = 'default_config.json'
                if os.path.exists(default_config):
                    with open(default_config, 'r') as f:
                        default_data = json.load(f)
                    default_version = default_data.get('agent_version', '')

                    # 如果默认配置中的版本号与当前版本号不同，更新当前版本号
                    if default_version and default_version != self.config.agent_version:
                        logger.info(f"更新版本号从 {self.config.agent_version} 到 {default_version}")
                        self.config.agent_version = default_version
            except Exception as e:
                logger.error(f"检查默认配置版本号时出错: {str(e)}")

            if not self.config.unique_id:
                self.config.unique_id = self.config._generate_unique_id()
                logger.info(f"生成终端唯一标识: {self.config.unique_id}")

            # 保存配置，确保unique_id和版本号被持久化
            self.config.save_config(config_file)
            logger.info(f"配置已保存，版本号: {self.config.agent_version}")
        except PermissionError:
            logger.error(f"权限错误：无法访问配置文件 {config_file}")
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_file} 格式错误，无法解析JSON")
            # 尝试备份并创建新配置
            try:
                backup_file = f"{config_file}.bak.{int(time.time())}"
                shutil.copy2(config_file, backup_file)
                logger.info(f"已备份损坏的配置文件到: {backup_file}")
                self.config.save_config(config_file)
                logger.info(f"已创建新的默认配置文件: {config_file}")
            except Exception as sub_e:
                logger.error(f"备份或创建配置文件时出错: {str(sub_e)}")
        except Exception as e:
            logger.error(f"加载配置时发生未知错误: {str(e)}")
            # 继续使用默认配置

    def _start_background_threads(self):
        """启动后台线程"""
        try:
            logger.info("正在启动后台线程...")

            # 创建并启动心跳线程
            heartbeat_thread = threading.Thread(
                target=self._heartbeat_worker,
                name="HeartbeatThread",
                daemon=True  # 设置为daemon线程，这样主线程退出时，线程会自动终止
            )
            self.threads['heartbeat'] = heartbeat_thread
            heartbeat_thread.start()
            logger.info("心跳线程已启动")

            # 创建并启动信息采集线程
            collection_thread = threading.Thread(
                target=self._collection_worker,
                name="CollectionThread",
                daemon=True
            )
            self.threads['collection'] = collection_thread
            collection_thread.start()
            logger.info("信息采集线程已启动")

            # 启动健康监控线程
            health_check_thread = threading.Thread(
                target=self._health_check_worker,
                name="HealthCheckThread",
                daemon=True
            )
            self.threads['health'] = health_check_thread
            health_check_thread.start()
            logger.info("健康监控线程已启动")

            return True
        except Exception as e:
            logger.error(f"启动后台线程时发生错误: {str(e)}")
            return False

    def _heartbeat_worker(self):
        """心跳工作线程"""
        logger.info("心跳线程开始运行")
        logger.info(f"当前配置的心跳间隔为: {self.config.heartbeat_interval}秒")
        logger.info(f"当前设置的重试间隔为: {self.heartbeat_retry_interval}秒")

        while not self.shutdown_event.is_set() and self.running:
            try:
                # 防止多个心跳同时执行
                if not self.heartbeat_lock.acquire(blocking=False):
                    logger.debug("另一个心跳操作正在进行，跳过本次心跳")
                    time.sleep(1)
                    continue

                try:
                    current_time = time.time()
                    # 检查是否到达发送心跳的时间
                    time_since_last = current_time - self.last_heartbeat_time
                    logger.debug(f"距离上次心跳已经过去 {int(time_since_last)}秒，配置的心跳间隔为 {self.config.heartbeat_interval}秒")
                    if time_since_last >= self.config.heartbeat_interval:
                        logger.debug(f"发送心跳, 间隔: {int(time_since_last)}秒")
                        success = self.send_heartbeat()

                        if success:
                            self.last_heartbeat_time = current_time
                            if self.heartbeat_failures > 0:
                                logger.info(f"心跳恢复正常，之前连续失败 {self.heartbeat_failures} 次")
                            self.heartbeat_failures = 0
                            # 不再重置为60秒，而是保持原值不变，确保下次心跳间隔使用配置的heartbeat_interval
                        else:
                            self.heartbeat_failures += 1
                            logger.warning(f"心跳失败，当前连续失败次数: {self.heartbeat_failures}")

                            # 使用指数退避策略计算下次重试间隔
                            if self.heartbeat_failures <= self.heartbeat_max_failures:
                                # 每次失败后，延长等待时间，但不超过心跳间隔的一半
                                backoff = min(
                                    self.heartbeat_retry_interval * (self.heartbeat_backoff_factor ** (self.heartbeat_failures - 1)),
                                    self.config.heartbeat_interval / 2
                                )
                                logger.info(f"将在 {int(backoff)} 秒后重试心跳")
                                # 更新最后心跳时间，但减去退避时间，以便在退避时间后重试
                                self.last_heartbeat_time = current_time - self.config.heartbeat_interval + backoff
                            else:
                                # 连续失败次数过多，尝试重新注册
                                logger.error(f"心跳连续失败 {self.heartbeat_failures} 次，尝试重新注册")
                                if self.register():
                                    logger.info("重新注册成功")
                                    self.heartbeat_failures = 0
                                    # 不再设置为60秒
                                    self.last_heartbeat_time = current_time
                                else:
                                    logger.error("重新注册失败")
                                    # 设置较长的重试间隔避免频繁重试
                                    self.last_heartbeat_time = current_time - self.config.heartbeat_interval + 300

                finally:
                    self.heartbeat_lock.release()

                # 睡眠时间较短，以便能够及时响应关闭事件
                for _ in range(10):  # 分10次小睡，每次100毫秒
                    if self.shutdown_event.is_set() or not self.running:
                        break
                    time.sleep(0.1)

            except Exception as e:
                logger.error(f"心跳线程出现异常: {str(e)}")
                # 出现异常，短暂休息后继续
                time.sleep(5)

        logger.info("心跳线程已退出")

    def _collection_worker(self):
        """信息采集工作线程"""
        logger.info("信息采集线程开始运行")

        # 首次启动时延迟采集，避免与其他初始化任务冲突
        initial_delay = random.randint(60, 180)  # 随机延迟1-3分钟
        logger.info(f"信息采集将在 {initial_delay} 秒后开始")
        time.sleep(initial_delay)

        # 版本检查相关变量
        last_version_check_time = 0

        while not self.shutdown_event.is_set() and self.running:
            try:
                current_time = time.time()
                # 检查是否到达采集时间
                if self.last_collection_time == 0 or (current_time - self.last_collection_time) >= self.config.collection_interval:
                    logger.info("开始定期信息采集...")
                    success = self.collect_and_report_info()
                    if success:
                        logger.info("信息采集和上报成功")
                        self.last_collection_time = current_time
                    else:
                        logger.warning("信息采集或上报失败，将在稍后重试")
                        # 失败后设置稍短的重试间隔
                        self.last_collection_time = current_time - self.config.collection_interval + 1800  # 30分钟后重试

                # 检查是否需要检查版本更新
                if last_version_check_time == 0 or (current_time - last_version_check_time) >= self.config.version_check_interval:
                    logger.info("开始检查Agent版本更新...")
                    check_result = self._check_for_updates()
                    if check_result == 1:
                        logger.info("版本检查完成，发现新版本并已启动升级")
                    elif check_result == 0:
                        logger.info("版本检查完成，当前已是最新版本")
                    else:
                        logger.warning("版本检查失败")
                    last_version_check_time = current_time

                # 每30秒检查一次
                for _ in range(30):
                    if self.shutdown_event.is_set() or not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"信息采集线程出现异常: {str(e)}")
                # 出现异常，等待一段时间后继续
                time.sleep(300)  # 5分钟

        logger.info("信息采集线程已退出")

    def _check_for_updates(self):
        """
        检查是否有新版本可用

        Returns:
            int: 1=发现新版本并已启动升级，0=当前已是最新版本，-1=检查失败
        """
        try:
            # 获取当前平台
            platform_name = platform.system().lower()
            if platform_name == "darwin":
                platform_name = "macos"
            elif platform_name == "windows":
                platform_name = "windows"
            else:
                platform_name = "linux"

            # 构建API请求URL
            url = f"{self.config.api_server_address}/api/v1/terminal/agent/current-version?platform={platform_name}"

            # 准备请求头，包含Agent系统Token
            headers = {
                "Authorization": f"Bearer ops-agent-system-token-2024",
                "Content-Type": "application/json"
            }

            # 发送请求
            logger.info(f"请求当前版本信息: {url}")
            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                version_info = response.json()
                current_version = version_info.get("version")

                # 比较版本
                if self._is_newer_version(current_version, self.config.agent_version):
                    logger.info(f"发现新版本: {current_version}，当前版本: {self.config.agent_version}")

                    # 如果配置为自动升级，则执行升级
                    if self.config.auto_upgrade:
                        download_url = version_info.get("download_url")
                        if download_url:
                            # 检查升级窗口
                            if not self._is_in_upgrade_window():
                                logger.info(f"发现新版本: {current_version}，但当前不在升级窗口内，将等待下次检查")
                                return 1  # 发现新版本但等待升级窗口
                            
                            # 如果下载URL是相对路径，添加API服务器地址
                            if download_url.startswith("/"):
                                download_url = f"{self.config.api_server_address}{download_url}"

                            # 创建唯一的命令ID
                            command_id = str(uuid.uuid4())

                            # 执行升级
                            upgrade_thread = threading.Thread(
                                target=self._perform_agent_upgrade,
                                args=(download_url, current_version, command_id),
                                name="AgentUpgradeThread",
                                daemon=True
                            )
                            upgrade_thread.start()
                            return 1  # 发现新版本并已启动升级
                        else:
                            logger.warning("无法获取下载URL")
                            return -1  # 检查失败
                    else:
                        logger.info("发现新版本，但未启用自动升级")
                        return 1  # 发现新版本，但未升级
                else:
                    logger.debug(f"当前已是最新版本: {self.config.agent_version}")
                    return 0  # 当前已是最新版本
            elif response.status_code == 404:
                logger.info(f"未找到平台 {platform_name} 的当前版本信息")
                return 0  # 当前已是最新版本
            else:
                logger.warning(f"获取版本信息失败，状态码: {response.status_code}, 响应: {response.text}")
                return -1  # 检查失败

        except Exception as e:
            logger.error(f"检查更新时出错: {str(e)}")
            return -1  # 检查失败

    def _is_newer_version(self, version1, version2):
        """
        比较版本号，如果version1比version2新，返回True
        支持语义化版本号(SemVer)格式：major.minor.patch[-prerelease][+build]
        """
        try:
            # 如果版本号相同，直接返回False
            if version1 == version2:
                return False
            
            # 解析版本号
            v1_parsed = self._parse_version(version1)
            v2_parsed = self._parse_version(version2)
            
            # 比较主版本号
            if v1_parsed['major'] != v2_parsed['major']:
                return v1_parsed['major'] > v2_parsed['major']
            
            # 比较次版本号
            if v1_parsed['minor'] != v2_parsed['minor']:
                return v1_parsed['minor'] > v2_parsed['minor']
            
            # 比较修订版本号
            if v1_parsed['patch'] != v2_parsed['patch']:
                return v1_parsed['patch'] > v2_parsed['patch']
            
            # 比较预发布版本
            # 没有预发布标识的版本比有预发布标识的版本新
            if not v1_parsed['prerelease'] and v2_parsed['prerelease']:
                return True
            if v1_parsed['prerelease'] and not v2_parsed['prerelease']:
                return False
            
            # 都有预发布标识时，按字典序比较
            if v1_parsed['prerelease'] and v2_parsed['prerelease']:
                return v1_parsed['prerelease'] > v2_parsed['prerelease']
            
            return False  # 版本完全相同
            
        except Exception as e:
            logger.error(f"比较版本号时出错: {str(e)}, version1={version1}, version2={version2}")
            # 出错时使用简单的字符串比较作为fallback
            return version1 > version2

    def _parse_version(self, version_str):
        """
        解析版本号字符串
        返回包含major, minor, patch, prerelease, build的字典
        """
        try:
            # 移除'v'前缀（如果存在）
            version_str = version_str.lstrip('v')
            
            # 分离构建元数据
            if '+' in version_str:
                version_str, build = version_str.split('+', 1)
            else:
                build = None
            
            # 分离预发布标识
            if '-' in version_str:
                main_version, prerelease = version_str.split('-', 1)
            else:
                main_version = version_str
                prerelease = None
            
            # 解析主版本号
            parts = main_version.split('.')
            major = int(parts[0]) if len(parts) > 0 and parts[0].isdigit() else 0
            minor = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 0
            patch = int(parts[2]) if len(parts) > 2 and parts[2].isdigit() else 0
            
            return {
                'major': major,
                'minor': minor,
                'patch': patch,
                'prerelease': prerelease,
                'build': build
            }
        except Exception as e:
            logger.warning(f"解析版本号失败: {version_str}, 错误: {str(e)}")
            # 返回默认值
            return {
                'major': 0,
                'minor': 0,
                'patch': 0,
                'prerelease': None,
                'build': None
            }

    def _health_check_worker(self):
        """健康监控线程"""
        logger.info("健康监控线程开始运行")

        while not self.shutdown_event.is_set() and self.running:
            try:
                # 检查心跳线程是否正常运行
                heartbeat_thread = self.threads.get('heartbeat')
                if heartbeat_thread and not heartbeat_thread.is_alive():
                    logger.error("检测到心跳线程已停止，尝试重启")
                    logger.info(f"重启前的心跳间隔配置为: {self.config.heartbeat_interval}秒")
                    # 创建新的心跳线程
                    new_thread = threading.Thread(
                        target=self._heartbeat_worker,
                        name="HeartbeatThread",
                        daemon=True
                    )
                    self.threads['heartbeat'] = new_thread
                    new_thread.start()
                    logger.info("心跳线程已重新启动")
                    logger.info(f"重启后的心跳间隔配置为: {self.config.heartbeat_interval}秒")

                # 检查信息采集线程是否正常运行
                collection_thread = self.threads.get('collection')
                if collection_thread and not collection_thread.is_alive():
                    logger.error("检测到信息采集线程已停止，尝试重启")
                    # 创建新的信息采集线程
                    new_thread = threading.Thread(
                        target=self._collection_worker,
                        name="CollectionThread",
                        daemon=True
                    )
                    self.threads['collection'] = new_thread
                    new_thread.start()
                    logger.info("信息采集线程已重新启动")

                # 每60秒检查一次线程状态，这不影响心跳间隔
                for _ in range(60):
                    if self.shutdown_event.is_set() or not self.running:
                        break
                    time.sleep(1)

            except Exception as e:
                logger.error(f"健康监控线程出现异常: {str(e)}")
                time.sleep(60)

        logger.info("健康监控线程已退出")

    def shutdown(self):
        """关闭Agent，清理资源"""
        try:
            logger.info("开始关闭Agent...")

            # 设置关闭标志
            self.running = False
            self.shutdown_event.set()

            # 等待线程结束
            logger.info("正在等待后台线程结束...")
            for name, thread in self.threads.items():
                if thread.is_alive():
                    logger.info(f"等待{name}线程结束...")
                    thread.join(timeout=10)  # 最多等待10秒
                    if thread.is_alive():
                        logger.warning(f"{name}线程未能正常结束")

            # 关闭gRPC通道
            if self.channel:
                try:
                    logger.info("正在关闭gRPC通道...")
                    self.channel.close()
                except Exception as e:
                    logger.error(f"关闭gRPC通道时出错: {str(e)}")

            logger.info("Agent已关闭")
            return True
        except Exception as e:
            logger.error(f"关闭Agent时出错: {str(e)}")
            return False

    def get_status(self):
        """
        获取代理的当前状态

        Returns:
            dict: 包含代理状态信息的字典
        """
        try:
            status = {
                "agent_running": self.running,
                "terminal_id": self.config.terminal_id,
                "server_address": self.config.server_address,
                "connection_state": "connected" if self.channel and self.stub else "disconnected",
                "last_heartbeat_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                                    time.localtime(self.last_heartbeat_time)) if self.last_heartbeat_time > 0 else "never",
                "heartbeat_failures": self.heartbeat_failures,
                "last_collection_time": time.strftime('%Y-%m-%d %H:%M:%S',
                                                    time.localtime(self.last_collection_time)) if self.last_collection_time > 0 else "never",
                "threads": {}
            }

            # 检查线程状态
            for name, thread in self.threads.items():
                if thread:
                    status["threads"][name] = {
                        "alive": thread.is_alive(),
                        "name": thread.name,
                        "daemon": thread.daemon
                    }

            # 添加配置信息
            status["config"] = {
                "heartbeat_interval": self.config.heartbeat_interval,
                "collection_interval": self.config.collection_interval,
                "agent_version": self.config.agent_version,
                "use_tls": self.config.use_tls
            }

            # 添加运行时间信息
            uptime = int(time.time() - self._start_time) if hasattr(self, '_start_time') else 0
            status["uptime_seconds"] = uptime
            status["uptime_formatted"] = self._format_uptime(uptime)

            return status
        except Exception as e:
            logger.error(f"获取状态信息时出错: {str(e)}")
            return {"error": str(e)}

    def _format_uptime(self, seconds):
        """格式化运行时间"""
        days = seconds // 86400
        seconds %= 86400
        hours = seconds // 3600
        seconds %= 3600
        minutes = seconds // 60
        seconds %= 60

        parts = []
        if days > 0:
            parts.append(f"{days}天")
        if hours > 0 or days > 0:
            parts.append(f"{hours}小时")
        if minutes > 0 or hours > 0 or days > 0:
            parts.append(f"{minutes}分钟")
        parts.append(f"{seconds}秒")

        return "".join(parts)

    def _initialize_collector(self):
        """初始化信息采集器"""
        try:
            if platform.system() == 'Windows' and WindowsCollector is not None:
                logger.info("正在初始化Windows信息采集器...")
                self.collector = WindowsCollector()
                logger.info("Windows信息采集器初始化完成")
            else:
                logger.warning(f"不支持的操作系统或采集器不可用: {platform.system()}")
                self.collector = None
        except Exception as e:
            logger.error(f"初始化信息采集器时发生错误: {str(e)}")
            self.collector = None

    def _initialize_command_handlers(self):
        """初始化命令处理器映射"""
        # 这里按命令类型注册不同的处理函数
        if not hasattr(terminal_pb2, 'Command') or not hasattr(terminal_pb2.Command, 'CommandType'):
            logger.error("gRPC协议定义加载失败，无法初始化命令处理器")
            self.command_handlers = {}
            return

        # 初始化命令处理器映射
        command_type = terminal_pb2.Command.CommandType

        self.command_handlers = {
            command_type.COLLECT_INFO: self._handle_collect_info_command,
            command_type.UPGRADE_AGENT: self._handle_upgrade_agent_command,
            command_type.CUSTOM_COMMAND: self._handle_custom_command,
            command_type.UNINSTALL_SOFTWARE: self._handle_uninstall_software_command,
            command_type.REGISTRY_OPERATION: self._handle_registry_operation_command,
            # 添加新的命令类型: 状态查询
            99: self._handle_status_query_command  # 假设99是STATUS_QUERY命令类型
        }

        logger.info(f"已初始化{len(self.command_handlers)}个命令处理器")

    def _handle_status_query_command(self, content, command_id):
        """
        处理状态查询命令

        Args:
            content (str): 命令内容，可包含特定查询参数
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理状态查询命令: {command_id}")

            # 获取状态信息
            status = self.get_status()

            # 转换为格式化的文本
            output = json.dumps(status, indent=2, ensure_ascii=False)

            return True, output, ""
        except Exception as e:
            error_msg = f"处理状态查询命令时出错: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def _handle_collect_info_command(self, content, command_id):
        """
        处理信息采集命令

        Args:
            content (str): 命令内容，可包含特定查询参数
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理信息采集命令: {command_id}")

            # 检查采集器是否可用
            if self.collector is None:
                error_msg = "信息采集器未初始化或不可用"
                logger.error(error_msg)
                return False, "", error_msg

            # 执行信息采集
            result = self.collect_and_report_info()

            if result:
                return True, "信息采集并上报成功", ""
            else:
                return False, "", "信息采集或上报失败"
        except Exception as e:
            error_msg = f"处理信息采集命令时出错: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def _handle_upgrade_agent_command(self, content, command_id):
        """
        处理Agent升级命令

        Args:
            content (str): 命令内容，包含升级URL等信息
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理Agent升级命令: {command_id}")

            # 解析命令内容
            try:
                params = json.loads(content)
                # 兼容不同的参数名称
                upgrade_url = params.get("upgrade_url") or params.get("url")
                version = params.get("version", "未知版本")

                if not upgrade_url:
                    return False, "", "缺少升级URL参数"

                # 如果下载URL是相对路径，添加API服务器地址
                if upgrade_url.startswith("/"):
                    upgrade_url = f"{self.config.api_server_address}{upgrade_url}"
            except json.JSONDecodeError:
                return False, "", "无效的命令参数格式"

            logger.info(f"准备从 {upgrade_url} 升级到 {version}")

            # 创建单独的线程执行升级，避免阻塞命令处理
            upgrade_thread = threading.Thread(
                target=self._perform_agent_upgrade,
                args=(upgrade_url, version, command_id),
                name="AgentUpgradeThread",
                daemon=True
            )
            upgrade_thread.start()

            # 立即返回成功，实际升级在后台线程中进行
            return True, f"Agent正在后台升级到{version}，请稍后查看结果", ""
        except Exception as e:
            error_msg = f"处理Agent升级命令时出错: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def _perform_agent_upgrade(self, upgrade_url, version, command_id):
        """
        执行Agent升级的实际逻辑

        Args:
            upgrade_url (str): 升级包下载地址
            version (str): 目标版本号
            command_id (str): 命令ID，用于上报进度
        """
        upgrade_start_time = time.time()
        temp_dir = None
        
        try:
            # 记录升级开始
            logger.info(f"开始Agent升级: {self.config.agent_version} -> {version}")
            self._report_progress(command_id, 5, f"开始升级Agent到版本 {version}")

            # 1. 预检查
            self._report_progress(command_id, 8, "执行升级前检查...")
            if not self._pre_upgrade_check(version):
                raise Exception("升级前检查失败")

            # 2. 创建临时目录用于下载和解压
            temp_dir = tempfile.mkdtemp(prefix="agent_upgrade_")
            logger.info(f"创建临时目录: {temp_dir}")
            self._report_progress(command_id, 10, "已创建临时目录")

            try:
                # 3. 下载升级包
                self._report_progress(command_id, 15, "开始下载升级包...")
                download_path = self._download_upgrade_package(upgrade_url, version, temp_dir, command_id)
                
                # 4. 验证升级包
                self._report_progress(command_id, 45, "验证升级包完整性...")
                if not self._validate_upgrade_package(download_path, version):
                    raise Exception("升级包验证失败")

                # 5. 创建备份
                self._report_progress(command_id, 50, "创建当前版本备份...")
                backup_path = self._create_backup(version)
                logger.info(f"已创建备份: {backup_path}")

                # 6. 准备升级脚本
                self._report_progress(command_id, 55, "准备升级脚本...")
                upgrade_script_path = self._prepare_upgrade_script(temp_dir, download_path, version)

                # 7. 执行升级
                self._report_progress(command_id, 60, "开始执行升级...")
                self._execute_upgrade_script(upgrade_script_path, command_id)

                # 8. 升级完成
                upgrade_duration = time.time() - upgrade_start_time
                logger.info(f"升级过程已启动，耗时: {upgrade_duration:.2f}秒")
                self._report_progress(command_id, 100, f"升级到版本 {version} 完成，服务正在重启...")
                
                return True

            except Exception as e:
                # 升级过程中的错误处理
                error_msg = f"升级过程中出错: {str(e)}"
                logger.error(error_msg)
                self._report_progress(command_id, -1, f"升级失败: {error_msg}")

                # 尝试回滚
                self._attempt_rollback(command_id, error_msg)
                
                # 上报命令执行结果
                try:
                    upgrade_duration = time.time() - upgrade_start_time
                    self._report_command_result(command_id, False, "", error_msg, 0, upgrade_duration)
                except Exception as report_error:
                    logger.error(f"上报升级失败结果时出错: {str(report_error)}")

                return False
                
        except Exception as e:
            # 顶层异常处理
            error_msg = f"执行升级过程时出现严重错误: {str(e)}"
            logger.error(error_msg)
            
            try:
                self._report_progress(command_id, -1, error_msg)
                upgrade_duration = time.time() - upgrade_start_time
                self._report_command_result(command_id, False, "", error_msg, 0, upgrade_duration)
            except Exception as report_error:
                logger.error(f"上报升级严重错误时出错: {str(report_error)}")
            
            return False
            
        finally:
            # 清理临时目录
            if temp_dir:
                try:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                    logger.debug(f"已清理临时目录: {temp_dir}")
                except Exception as e:
                    logger.warning(f"清理临时目录时出错: {str(e)}")

    def _pre_upgrade_check(self, target_version):
        """升级前检查"""
        try:
            # 检查磁盘空间
            if not self._check_disk_space():
                logger.error("磁盘空间不足")
                return False
            
            # 检查网络连接
            if not self._check_network_connectivity():
                logger.error("网络连接检查失败")
                return False
                
            # 检查版本格式
            if not self._validate_version_format(target_version):
                logger.error(f"目标版本格式无效: {target_version}")
                return False
                
            # 检查是否为降级（可选）
            if self._is_newer_version(self.config.agent_version, target_version):
                logger.warning(f"检测到版本降级: {self.config.agent_version} -> {target_version}")
                
            return True
        except Exception as e:
            logger.error(f"升级前检查出错: {str(e)}")
            return False

    def _download_upgrade_package(self, upgrade_url, version, temp_dir, command_id):
        """下载升级包"""
        try:
            # 确定文件扩展名
            if platform.system() == 'Windows':
                file_ext = '.exe'
            elif upgrade_url.endswith('.deb'):
                file_ext = '.deb'
            elif upgrade_url.endswith('.rpm'):
                file_ext = '.rpm'
            elif upgrade_url.endswith('.pkg'):
                file_ext = '.pkg'
            else:
                file_ext = '.tar.gz'
            
            download_path = os.path.join(temp_dir, f"agent_upgrade_{version}{file_ext}")
            
            # 下载文件
            success = self._download_file(upgrade_url, download_path)
            if not success:
                raise Exception("下载升级包失败")

            self._report_progress(command_id, 40, "升级包下载完成")
            logger.info(f"升级包下载完成: {download_path}")
            return download_path
            
        except Exception as e:
            logger.error(f"下载升级包失败: {str(e)}")
            raise

    def _validate_upgrade_package(self, package_path, version):
        """验证升级包"""
        try:
            # 检查文件存在性
            if not os.path.exists(package_path):
                logger.error(f"升级包文件不存在: {package_path}")
                return False
                
            # 检查文件大小
            file_size = os.path.getsize(package_path)
            if file_size == 0:
                logger.error("升级包为空文件")
                return False
            elif file_size < 1024 * 1024:  # 小于1MB
                logger.warning(f"升级包文件较小: {file_size} bytes")
                
            # 检查文件魔数（简单验证）
            with open(package_path, 'rb') as f:
                header = f.read(16)
                if len(header) < 2:
                    logger.error("无法读取文件头")
                    return False
                    
            logger.info(f"升级包验证通过: {package_path}, 大小: {file_size} bytes")
            return True
            
        except Exception as e:
            logger.error(f"验证升级包时出错: {str(e)}")
            return False

    def _create_backup(self, target_version):
        """创建当前版本备份"""
        try:
            backup_dir = os.path.join(tempfile.gettempdir(), f"agent_backup_{int(time.time())}")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份当前可执行文件
            current_exe = sys.executable
            if os.path.exists(current_exe):
                backup_exe = os.path.join(backup_dir, os.path.basename(current_exe))
                shutil.copy2(current_exe, backup_exe)
                
            # 备份配置文件
            config_file = getattr(self, 'config_file', 'agent_config.json')
            if os.path.exists(config_file):
                backup_config = os.path.join(backup_dir, os.path.basename(config_file))
                shutil.copy2(config_file, backup_config)
                
            logger.info(f"已创建备份目录: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            logger.warning(f"创建备份失败: {str(e)}")
            return None

    def _execute_upgrade_script(self, script_path, command_id):
        """执行升级脚本"""
        try:
            logger.info(f"执行升级脚本: {script_path}")
            
            if platform.system() == 'Windows':
                # Windows升级脚本执行
                is_powershell = 'powershell' in os.environ.get('PSModulePath', '').lower()
                
                if is_powershell:
                    cmd = f'Start-Process -FilePath "{script_path}" -WindowStyle Hidden'
                    subprocess.Popen(['powershell', '-Command', cmd], shell=True, close_fds=True)
                else:
                    cmd = f'start "Agent升级" /b cmd /c "{script_path}"'
                    subprocess.Popen(cmd, shell=True, close_fds=True)
            else:
                # Unix系统升级脚本执行
                cmd = f'nohup bash "{script_path}" > /dev/null 2>&1 &'
                subprocess.Popen(cmd, shell=True, close_fds=True)
            
            self._report_progress(command_id, 80, "升级脚本已启动，服务即将重启...")
            time.sleep(3)  # 给脚本一点启动时间
            
        except Exception as e:
            logger.error(f"执行升级脚本失败: {str(e)}")
            raise

    def _attempt_rollback(self, command_id, error_msg):
        """尝试回滚到之前版本"""
        try:
            logger.info("尝试回滚到之前版本...")
            self._report_progress(command_id, -2, "升级失败，尝试回滚...")
            
            # 这里可以实现回滚逻辑
            # 例如恢复备份文件、重启服务等
            
        except Exception as e:
            logger.error(f"回滚失败: {str(e)}")

    def _check_disk_space(self, required_mb=100):
        """检查磁盘可用空间"""
        try:
            if platform.system() == 'Windows':
                import shutil
                free_bytes = shutil.disk_usage('.').free
            else:
                import os
                stat = os.statvfs('.')
                free_bytes = stat.f_bavail * stat.f_frsize
                
            free_mb = free_bytes / (1024 * 1024)
            logger.debug(f"可用磁盘空间: {free_mb:.2f} MB")
            return free_mb >= required_mb
            
        except Exception as e:
            logger.warning(f"检查磁盘空间失败: {str(e)}")
            return True  # 检查失败时假设空间足够

    def _check_network_connectivity(self):
        """检查网络连接"""
        try:
            # 简单ping测试
            import socket
            socket.create_connection(("*******", 53), timeout=3)
            return True
        except Exception:
            logger.warning("网络连接检查失败")
            return True  # 网络检查失败时不阻止升级

    def _validate_version_format(self, version):
        """验证版本号格式"""
        try:
            # 基本格式检查
            if not version or not isinstance(version, str):
                return False
            
            # 移除v前缀
            version = version.lstrip('v')
            
            # 检查是否包含数字
            import re
            if not re.search(r'\d', version):
                return False
                
            return True
        except Exception:
            return False

    def _is_in_upgrade_window(self):
        """检查当前时间是否在升级窗口内"""
        try:
            from datetime import datetime, time
            
            # 获取当前时间
            now = datetime.now().time()
            
            # 解析升级窗口时间
            start_time = datetime.strptime(self.config.upgrade_window_start, "%H:%M").time()
            end_time = datetime.strptime(self.config.upgrade_window_end, "%H:%M").time()
            
            # 检查是否在升级窗口内
            if start_time <= end_time:
                # 正常情况：开始时间 <= 结束时间（如 02:00 到 06:00）
                return start_time <= now <= end_time
            else:
                # 跨午夜情况：开始时间 > 结束时间（如 22:00 到 06:00）
                return now >= start_time or now <= end_time
                
        except Exception as e:
            logger.warning(f"检查升级窗口时出错: {str(e)}")
            return True  # 出错时允许升级

    def _load_upgrade_config_from_file(self, config):
        """从配置中加载升级相关配置"""
        try:
            # 升级窗口配置
            if "upgrade_window_start" in config:
                self.config.upgrade_window_start = config["upgrade_window_start"]
            if "upgrade_window_end" in config:
                self.config.upgrade_window_end = config["upgrade_window_end"]
                
            # 升级重试配置
            if "upgrade_retry_max" in config:
                self.config.upgrade_retry_max = config["upgrade_retry_max"]
                
            # 升级备份配置
            if "upgrade_backup_enabled" in config:
                self.config.upgrade_backup_enabled = config["upgrade_backup_enabled"]
                
            logger.debug(f"已加载升级配置: 窗口={self.config.upgrade_window_start}-{self.config.upgrade_window_end}, "
                        f"重试={self.config.upgrade_retry_max}, 备份={self.config.upgrade_backup_enabled}")
                        
        except Exception as e:
            logger.warning(f"加载升级配置时出错: {str(e)}")

    def _should_enable_auto_upgrade(self):
        """根据配置判断是否应该启用自动升级"""
        try:
            # 检查基本配置
            if not self.config.auto_upgrade:
                return False
            
            # 检查系统资源
            if not self._check_disk_space(200):  # 至少需要200MB空间
                logger.warning("磁盘空间不足，禁用自动升级")
                return False
                
            # 检查网络连接
            if not self._check_network_connectivity():
                logger.warning("网络连接不稳定，禁用自动升级")
                return False
                
            return True
            
        except Exception as e:
            logger.warning(f"检查自动升级条件时出错: {str(e)}")
            return False

    def _download_file(self, url, destination):
        """
        下载文件到指定位置

        Args:
            url (str): 下载地址
            destination (str): 保存路径

        Returns:
            bool: 是否下载成功
        """
        try:
            logger.info(f"开始下载文件: {url} -> {destination}")

            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(destination), exist_ok=True)

            # 下载文件
            response = requests.get(url, stream=True, timeout=60)
            response.raise_for_status()  # 如果响应状态不是200，将引发异常

            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            logger.info(f"文件大小: {total_size} 字节")

            # 写入文件
            with open(destination, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        # 每下载10%记录一次日志
                        if total_size > 0 and downloaded % (total_size // 10) < 8192:
                            percent = (downloaded / total_size) * 100
                            logger.info(f"下载进度: {percent:.1f}%")

            logger.info(f"文件下载完成: {destination}")
            return True

        except Exception as e:
            logger.error(f"下载文件时出错: {str(e)}")
            return False

    def _prepare_upgrade_script(self, temp_dir, installer_path, version):
        """
        准备升级脚本

        Args:
            temp_dir (str): 临时目录
            installer_path (str): 安装包路径
            version (str): 版本号

        Returns:
            str: 升级脚本路径
        """
        try:
            # 确定脚本路径
            if platform.system() == 'Windows':
                script_path = os.path.join(temp_dir, "upgrade.bat")

                # 检测是否在PowerShell环境中
                is_powershell = 'powershell' in os.environ.get('PSModulePath', '').lower() or 'powershell' in os.environ.get('SHELL', '').lower()

                # 获取配置文件路径
                config_file_path = os.path.join(os.path.dirname(sys.executable), "agent_config.json")

                if is_powershell:
                    # PowerShell脚本内容
                    script_content = f"""# PowerShell升级脚本
Write-Host "开始升级Agent到版本 {version}..."

# 等待几秒，确保主程序已经退出
Start-Sleep -Seconds 3

# 停止服务
Write-Host "正在停止TerminalAgent服务..."
Stop-Service -Name TerminalAgent -Force

# 再次等待，确保服务完全停止
Start-Sleep -Seconds 5

# 运行安装程序
Write-Host "正在安装新版本..."
Start-Process -FilePath "{installer_path}" -ArgumentList "/VERYSILENT", "/NORESTART" -Wait

# 等待安装完成
Start-Sleep -Seconds 10

# 更新配置文件中的版本号
Write-Host "正在更新配置文件中的版本号..."
$configPath = "{config_file_path}"
if (Test-Path $configPath) {{
    $config = Get-Content -Raw -Path $configPath | ConvertFrom-Json
    $config.agent_version = "{version}"
    $config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath
    Write-Host "配置文件已更新"
}} else {{
    Write-Host "未找到配置文件: $configPath"
}}

# 启动服务
Write-Host "正在启动服务..."
Start-Service -Name TerminalAgent

Write-Host "升级完成！"
"""
                    # 保存为.ps1文件
                    script_path = os.path.join(temp_dir, "upgrade.ps1")
                else:
                    # Windows批处理脚本内容
                    script_content = f"""@echo off
echo 开始升级Agent到版本 {version}...

:: 等待几秒，确保主程序已经退出
timeout /t 3 /nobreak > nul

:: 停止服务
echo 正在停止TerminalAgent服务...
net stop TerminalAgent

:: 再次等待，确保服务完全停止
timeout /t 5 /nobreak > nul

:: 运行安装程序
echo 正在安装新版本...
"{installer_path}" /VERYSILENT /NORESTART

:: 等待安装完成
timeout /t 10 /nobreak > nul

:: 更新配置文件中的版本号
echo 正在更新配置文件中的版本号...
set CONFIG_FILE="{config_file_path}"

if exist %CONFIG_FILE% (
    :: 使用PowerShell更新JSON文件
    powershell -Command "$config = Get-Content -Raw -Path '{config_file_path}' | ConvertFrom-Json; $config.agent_version = '{version}'; $config | ConvertTo-Json -Depth 10 | Set-Content -Path '{config_file_path}'"
    echo 配置文件已更新
) else (
    echo 未找到配置文件: %CONFIG_FILE%
)

:: 启动服务
echo 正在启动服务...
net start TerminalAgent

echo 升级完成！
"""
                    # 保存为.bat文件
                    script_path = os.path.join(temp_dir, "upgrade.bat")
            else:
                # 对于非Windows系统，创建跨平台shell脚本
                current_platform = platform.system().lower()
                
                if current_platform == "darwin":  # macOS
                    script_path = os.path.join(temp_dir, "upgrade_macos.sh")
                    script_content = self._create_macos_upgrade_script(installer_path, version, config_file_path)
                else:  # Linux
                    script_path = os.path.join(temp_dir, "upgrade_linux.sh")
                    script_content = self._create_linux_upgrade_script(installer_path, version, config_file_path)

            # 写入脚本文件
            with open(script_path, 'w') as f:
                f.write(script_content)

            # 设置执行权限
            if platform.system() != 'Windows':
                os.chmod(script_path, 0o755)

            logger.info(f"升级脚本已创建: {script_path}")
            return script_path

        except Exception as e:
            logger.error(f"准备升级脚本时出错: {str(e)}")
            raise

    def _create_linux_upgrade_script(self, installer_path, version, config_file_path):
        """创建Linux升级脚本"""
        return f"""#!/bin/bash
echo "开始升级Agent到版本 {version}..."

# 升级日志文件
LOG_FILE="/tmp/agent_upgrade_{version}.log"
exec > >(tee -a "$LOG_FILE") 2>&1

# 等待几秒，确保主程序已经退出
sleep 3

# 检测服务管理器类型并停止服务
echo "正在停止TerminalAgent服务..."
if command -v systemctl >/dev/null 2>&1; then
    # systemd系统
    sudo systemctl stop TerminalAgent
    SERVICE_MGR="systemd"
elif command -v service >/dev/null 2>&1; then
    # SysV init系统
    sudo service TerminalAgent stop
    SERVICE_MGR="sysv"
elif command -v /etc/init.d/TerminalAgent >/dev/null 2>&1; then
    # 直接调用init脚本
    sudo /etc/init.d/TerminalAgent stop
    SERVICE_MGR="init"
else
    echo "警告: 无法检测到服务管理器，尝试通过PID停止服务"
    # 尝试通过进程名停止
    sudo pkill -f TerminalAgent
    SERVICE_MGR="manual"
fi

# 等待服务完全停止
sleep 5

# 备份当前版本
BACKUP_DIR="/tmp/agent_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
if [ -f "/usr/local/bin/TerminalAgent" ]; then
    cp "/usr/local/bin/TerminalAgent" "$BACKUP_DIR/"
    echo "已备份当前版本到: $BACKUP_DIR"
fi

# 运行安装程序
echo "正在安装新版本..."
chmod +x "{installer_path}"

if [[ "{installer_path}" == *.deb ]]; then
    # Debian/Ubuntu包
    sudo dpkg -i "{installer_path}"
    sudo apt-get install -f  # 修复依赖
elif [[ "{installer_path}" == *.rpm ]]; then
    # RedHat/CentOS包
    sudo rpm -Uvh "{installer_path}"
elif [[ "{installer_path}" == *.tar.gz ]]; then
    # 源码包安装
    tar -xzf "{installer_path}" -C /tmp/
    cd /tmp/TerminalAgent-{version}/
    sudo ./install.sh
else
    # 通用安装脚本
    "{installer_path}" --silent
fi

# 等待安装完成
sleep 10

# 更新配置文件中的版本号
echo "正在更新配置文件中的版本号..."
CONFIG_FILE="{config_file_path}"

if [ -f "$CONFIG_FILE" ]; then
    # 备份配置文件
    cp "$CONFIG_FILE" "${{CONFIG_FILE}}.backup"
    
    # 使用Python更新JSON文件（优先python3）
    if command -v python3 >/dev/null 2>&1; then
        python3 -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    config['agent_version'] = '{version}'
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, indent=4)
    print('配置文件已更新')
except Exception as e:
    print(f'更新配置文件失败: {{e}}')
"
    elif command -v python >/dev/null 2>&1; then
        python -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    config['agent_version'] = '{version}'
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, indent=4)
    print('配置文件已更新')
except Exception as e:
    print('更新配置文件失败: ' + str(e))
"
    else
        echo "未找到Python，跳过配置文件更新"
    fi
else
    echo "未找到配置文件: $CONFIG_FILE"
fi

# 启动服务
echo "正在启动服务..."
case $SERVICE_MGR in
    "systemd")
        sudo systemctl start TerminalAgent
        sudo systemctl enable TerminalAgent
        ;;
    "sysv")
        sudo service TerminalAgent start
        ;;
    "init")
        sudo /etc/init.d/TerminalAgent start
        ;;
    "manual")
        echo "请手动启动TerminalAgent服务"
        ;;
esac

# 验证服务状态
sleep 3
if command -v systemctl >/dev/null 2>&1; then
    systemctl is-active TerminalAgent && echo "服务启动成功" || echo "服务启动失败"
else
    pgrep -f TerminalAgent && echo "服务启动成功" || echo "服务启动失败"
fi

echo "升级完成！日志文件: $LOG_FILE"
"""

    def _create_macos_upgrade_script(self, installer_path, version, config_file_path):
        """创建macOS升级脚本"""
        return f"""#!/bin/bash
echo "开始升级Agent到版本 {version}..."

# 升级日志文件
LOG_FILE="/tmp/agent_upgrade_{version}.log"
exec > >(tee -a "$LOG_FILE") 2>&1

# 等待几秒，确保主程序已经退出
sleep 3

# 停止launchd服务
echo "正在停止TerminalAgent服务..."
PLIST_PATH="/Library/LaunchDaemons/com.ops.terminalagent.plist"
if [ -f "$PLIST_PATH" ]; then
    sudo launchctl unload "$PLIST_PATH"
    echo "已停止launchd服务"
else
    # 尝试通过进程名停止
    sudo pkill -f TerminalAgent
    echo "通过进程名停止服务"
fi

# 等待服务完全停止
sleep 5

# 备份当前版本
BACKUP_DIR="/tmp/agent_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"
if [ -f "/usr/local/bin/TerminalAgent" ]; then
    cp "/usr/local/bin/TerminalAgent" "$BACKUP_DIR/"
    echo "已备份当前版本到: $BACKUP_DIR"
fi

# 运行安装程序
echo "正在安装新版本..."
chmod +x "{installer_path}"

if [[ "{installer_path}" == *.pkg ]]; then
    # macOS包安装
    sudo installer -pkg "{installer_path}" -target /
elif [[ "{installer_path}" == *.dmg ]]; then
    # 挂载DMG文件
    MOUNT_POINT="/Volumes/TerminalAgent"
    hdiutil attach "{installer_path}"
    if [ -f "$MOUNT_POINT/TerminalAgent.pkg" ]; then
        sudo installer -pkg "$MOUNT_POINT/TerminalAgent.pkg" -target /
    fi
    hdiutil detach "$MOUNT_POINT"
else
    # 通用安装脚本
    "{installer_path}" --silent
fi

# 等待安装完成
sleep 10

# 更新配置文件中的版本号
echo "正在更新配置文件中的版本号..."
CONFIG_FILE="{config_file_path}"

if [ -f "$CONFIG_FILE" ]; then
    # 备份配置文件
    cp "$CONFIG_FILE" "${{CONFIG_FILE}}.backup"
    
    # 使用Python更新JSON文件
    if command -v python3 >/dev/null 2>&1; then
        python3 -c "
import json
try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    config['agent_version'] = '{version}'
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, indent=4)
    print('配置文件已更新')
except Exception as e:
    print(f'更新配置文件失败: {{e}}')
"
    else
        echo "未找到Python，跳过配置文件更新"
    fi
else
    echo "未找到配置文件: $CONFIG_FILE"
fi

# 启动服务
echo "正在启动服务..."
if [ -f "$PLIST_PATH" ]; then
    sudo launchctl load "$PLIST_PATH"
    echo "已启动launchd服务"
else
    echo "未找到launchd配置文件，请手动启动服务"
fi

# 验证服务状态
sleep 3
if pgrep -f TerminalAgent >/dev/null; then
    echo "服务启动成功"
else
    echo "服务启动失败"
fi

echo "升级完成！日志文件: $LOG_FILE"
"""

    def _handle_custom_command(self, content, command_id):
        """
        处理自定义命令 - 安全执行白名单命令

        Args:
            content (str): 命令内容，必须是白名单中的命令
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理自定义命令: {command_id}")

            # 检查是否为空命令
            if not content or not content.strip():
                return False, "", "命令内容为空"

            # 提取命令内容
            command = content.strip()
            logger.info(f"准备执行安全命令: {command}")

            # 验证命令安全性
            if not self._validate_safe_command(command):
                error_msg = f"命令不在安全白名单中或包含危险操作: {command}"
                logger.warning(error_msg)
                return False, "", error_msg

            # 执行安全命令
            return self._execute_safe_command(command)

        except Exception as e:
            error_msg = f"处理自定义命令时出错: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def _validate_safe_command(self, command):
        """
        验证命令是否安全

        Args:
            command (str): 要验证的命令

        Returns:
            bool: 命令是否安全
        """
        try:
            # 基础安全检查 - 危险命令模式
            dangerous_patterns = [
                r'\bformat\b',
                r'\bdel\s+',
                r'\brd\s+',
                r'\brmdir\b',
                r'\bshutdown\b',
                r'\breboot\b',
                r'\brestart\b',
                r'>\s*nul',
                r'&\s*&',
                r'\|\s*\|',
                r';\s*;',
                r'`.*`',
                r'\$\(',
                r'%.*%',
                r'\bnet\s+user\b',
                r'\breg\s+delete\b',
                r'\breg\s+add\b.*\\windows\\',
                r'\bschtasks\b.*\/create\b',
                r'\bwmic\b.*call.*terminate',
                r'\btaskkill\b.*\/f\b',
                r'\bmkdir\b.*\\windows\\',
                r'\bcopy\b.*\\windows\\',
                r'\bmove\b.*\\windows\\'
            ]

            import re
            for pattern in dangerous_patterns:
                if re.search(pattern, command, re.IGNORECASE):
                    logger.warning(f"命令包含危险模式 {pattern}: {command}")
                    return False

            # 长度检查
            if len(command) > 500:
                logger.warning(f"命令长度超过限制: {len(command)}")
                return False

            # 基础白名单命令 - 在没有服务器连接时使用本地白名单
            safe_commands = [
                'systeminfo', 'whoami', 'hostname', 'ver',
                'ipconfig', 'ping', 'nslookup', 'netstat', 'arp',
                'tasklist', 'sc query', 'wmic computersystem',
                'wmic logicaldisk', 'wmic service', 'wmic process',
                'dir', 'echo', 'time', 'date',
                'gpresult', 'route print', 'tracert'
            ]

            # 检查命令是否以安全命令开头
            command_lower = command.lower()
            for safe_cmd in safe_commands:
                if command_lower.startswith(safe_cmd.lower()):
                    # 额外检查参数安全性
                    if self._validate_command_parameters(command, safe_cmd):
                        return True

            logger.warning(f"命令不在白名单中: {command}")
            return False

        except Exception as e:
            logger.error(f"验证命令安全性时出错: {str(e)}")
            return False

    def _validate_command_parameters(self, full_command, base_command):
        """
        验证命令参数的安全性

        Args:
            full_command (str): 完整命令
            base_command (str): 基础命令

        Returns:
            bool: 参数是否安全
        """
        try:
            # 提取参数部分
            if len(full_command) <= len(base_command):
                return True  # 没有参数

            parameters = full_command[len(base_command):].strip()
            if not parameters:
                return True

            # 检查危险参数模式
            dangerous_param_patterns = [
                r'>\s*\w+',  # 输出重定向
                r'<\s*\w+',  # 输入重定向
                r'\|\s*\w+', # 管道
                r'&\s*\w+',  # 命令连接
                r';\s*\w+',  # 命令分隔
                r'\.\.\/',   # 路径遍历
                r'\\\\',     # UNC路径
                r'\$\w+',    # 变量引用
                r'`\w+`',    # 命令替换
                r'%\w+%'     # 环境变量
            ]

            import re
            for pattern in dangerous_param_patterns:
                if re.search(pattern, parameters, re.IGNORECASE):
                    logger.warning(f"命令参数包含危险模式 {pattern}: {parameters}")
                    return False

            return True

        except Exception as e:
            logger.error(f"验证命令参数时出错: {str(e)}")
            return False

    def _execute_safe_command(self, command, timeout=30):
        """
        安全执行命令

        Args:
            command (str): 要执行的命令
            timeout (int): 超时时间（秒）

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"执行安全命令: {command}")

            import subprocess
            import os

            # 设置安全的执行环境
            env = os.environ.copy()
            
            # 移除一些危险的环境变量
            dangerous_env_vars = ['PATHEXT', 'COMSPEC']
            for var in dangerous_env_vars:
                env.pop(var, None)

            # 使用cmd.exe执行命令，但限制环境
            if os.name == 'nt':  # Windows
                # 使用完整路径避免路径劫持
                cmd_executable = r"C:\Windows\System32\cmd.exe"
                full_command = [cmd_executable, "/c", command]
            else:  # Linux/Unix
                full_command = ["/bin/sh", "-c", command]

            # 执行命令
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # 处理输出
            output = result.stdout
            error = result.stderr

            # 限制输出长度
            max_output_length = 10240  # 10KB
            if len(output) > max_output_length:
                output = output[:max_output_length] + "\n... (输出被截断)"

            if len(error) > max_output_length:
                error = error[:max_output_length] + "\n... (错误输出被截断)"

            success = result.returncode == 0

            if success:
                logger.info(f"命令执行成功: {command}")
                return True, output, error
            else:
                logger.warning(f"命令执行失败，返回码: {result.returncode}")
                return False, output, error

        except subprocess.TimeoutExpired:
            error_msg = f"命令执行超时 ({timeout}秒): {command}"
            logger.error(error_msg)
            return False, "", error_msg

        except subprocess.CalledProcessError as e:
            error_msg = f"命令执行错误: {str(e)}"
            logger.error(error_msg)
            return False, e.stdout or "", e.stderr or error_msg

        except Exception as e:
            error_msg = f"执行命令时发生异常: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def _handle_registry_operation_command(self, content, command_id):
        """
        处理注册表操作命令

        Args:
            content (str): 命令内容，包含注册表操作信息
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理注册表操作命令: {command_id}")

            # 检查采集器是否可用
            if self.collector is None:
                error_msg = "Windows信息采集器未初始化或不可用"
                logger.error(error_msg)
                return False, "", error_msg

            # 检查注册表管理器是否可用
            if not hasattr(self.collector, 'handle_registry_operation'):
                error_msg = "注册表管理器功能不可用"
                logger.error(error_msg)
                return False, "", error_msg

            # 检查管理员权限（注册表操作通常需要管理员权限）
            if not self._check_admin_privileges():
                logger.warning("当前用户没有管理员权限，某些注册表操作可能失败")

            # 解析命令内容
            try:
                if content:
                    operation_data = json.loads(content)
                else:
                    return False, "", "注册表操作命令内容为空"
            except json.JSONDecodeError as e:
                return False, "", f"无效的注册表操作参数格式: {str(e)}"

            # 验证必要参数
            operation = operation_data.get("operation")
            if not operation:
                return False, "", "缺少操作类型参数"

            root_key = operation_data.get("root_key")
            key_path = operation_data.get("key_path")
            
            if not root_key:
                return False, "", "缺少根键参数"
            
            if not key_path:
                return False, "", "缺少键路径参数"

            logger.info(f"执行注册表操作: {operation} - {root_key}\\{key_path}")

            # 调用注册表管理器处理操作
            result = self.collector.handle_registry_operation(operation_data)

            if result.get("success", False):
                # 操作成功
                output_data = {
                    "success": True,
                    "message": result.get("message", "操作成功"),
                    "operation": operation,
                    "root_key": root_key,
                    "key_path": key_path
                }

                # 添加特定操作的结果数据
                if "key_data" in result:
                    output_data["key_data"] = result["key_data"]
                
                if "search_results" in result:
                    output_data["search_results"] = result["search_results"]
                    output_data["total_results"] = result.get("total_results", 0)
                
                if "backup_id" in result:
                    output_data["backup_id"] = result["backup_id"]
                    output_data["backup_file"] = result.get("backup_file", "")

                # 格式化输出
                output = json.dumps(output_data, indent=2, ensure_ascii=False)
                return True, output, ""
            else:
                # 操作失败
                error_msg = result.get("error", "注册表操作失败")
                logger.error(f"注册表操作失败: {error_msg}")
                return False, "", error_msg

        except Exception as e:
            error_msg = f"处理注册表操作命令时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, "", error_msg

    def _handle_uninstall_software_command(self, content, command_id):
        """
        处理软件卸载命令

        Args:
            content (str): 命令内容，包含软件名称等信息
            command_id (str): 命令ID

        Returns:
            tuple: (成功标志, 输出内容, 错误信息)
        """
        try:
            logger.info(f"处理软件卸载命令: {command_id}")

            # 检查管理员权限
            if not self._check_admin_privileges():
                return False, "", "卸载软件需要管理员权限，当前用户没有足够权限"

            # 解析命令内容
            try:
                params = json.loads(content)
                software_name = params.get("name")
                software_version = params.get("version", "")

                if not software_name:
                    return False, "", "缺少软件名称参数"
            except json.JSONDecodeError as e:
                return False, "", f"无效的命令参数格式: {str(e)}"

            logger.info(f"准备卸载软件: {software_name}" + (f" 版本: {software_version}" if software_version else ""))

            # 实现实际卸载逻辑
            try:
                # 查找软件卸载信息
                uninstall_info = self._find_software_uninstall_command(software_name, software_version)

                if not uninstall_info:
                    return False, "", f"未找到软件 '{software_name}' 的卸载信息"

                uninstall_command = uninstall_info['command']
                installer_type = uninstall_info['type']
                software_display_name = uninstall_info['display_name']

                # 执行卸载命令
                logger.info(f"执行卸载命令: {uninstall_command}, 类型: {installer_type}")

                # 根据不同的安装程序类型使用不同的静默参数
                if installer_type == 'msi':
                    # MSI卸载命令 - 使用GUID或路径
                    if uninstall_command.startswith('{') and uninstall_command.endswith('}'):
                        # GUID形式
                        uninstall_command = f'msiexec.exe /x {uninstall_command} /qn /norestart'
                    else:
                        # 路径形式
                        uninstall_command = f'msiexec.exe /x "{uninstall_command}" /qn /norestart'
                elif installer_type == 'inno':
                    # Inno Setup卸载命令
                    uninstall_command = f'"{uninstall_command}" /VERYSILENT /NORESTART'
                elif installer_type == 'nsis':
                    # NSIS卸载命令
                    uninstall_command = f'"{uninstall_command}" /S'
                elif installer_type == 'wise':
                    # Wise卸载命令
                    uninstall_command = f'"{uninstall_command}" /q'
                else:
                    # 通用EXE卸载命令，尝试最常见的参数
                    uninstall_command = f'"{uninstall_command}" /S'

                # 向界面报告开始卸载
                progress_message = f"开始卸载 '{software_display_name}'..."
                self._report_progress(command_id, 10, progress_message)

                # 执行卸载命令，并等待完成
                process = subprocess.Popen(
                    uninstall_command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )

                # 每30秒报告一次进度
                timeout = 600  # 10分钟超时
                interval = 30  # 30秒报告一次
                elapsed = 0
                progress = 10

                while process.poll() is None and elapsed < timeout:
                    time.sleep(interval)
                    elapsed += interval
                    progress += min(5, (80 - progress) // ((timeout - elapsed) // interval + 1))
                    self._report_progress(command_id, progress, f"正在卸载 '{software_display_name}'... (已用时{elapsed}秒)")

                if elapsed >= timeout:
                    try:
                        process.kill()
                    except:
                        pass
                    return False, "", f"卸载软件 '{software_display_name}' 超时"

                # 获取结果
                stdout, stderr = process.communicate()
                exit_code = process.returncode

                if exit_code == 0:
                    self._report_progress(command_id, 90, f"'{software_display_name}' 卸载完成，正在验证...")

                    # 验证软件是否已卸载
                    if self._verify_software_uninstalled(software_name, software_version):
                        self._report_progress(command_id, 100, f"'{software_display_name}' 卸载成功")
                        # 可选：清理残留
                        self._clean_software_residuals(software_name)
                        return True, f"软件 '{software_display_name}' 卸载成功", ""
                    else:
                        return False, "", f"卸载命令执行成功，但软件 '{software_display_name}' 似乎仍然存在，可能需要重启"
                else:
                    error_details = stderr if stderr else f"错误代码：{exit_code}"
                    logger.error(f"卸载失败: {error_details}")
                    return False, "", f"卸载软件 '{software_display_name}' 失败: {error_details}"

            except Exception as e:
                logger.error(f"卸载软件时出错: {str(e)}", exc_info=True)
                return False, "", f"卸载软件 '{software_name}' 时出错: {str(e)}"

        except Exception as e:
            error_msg = f"处理软件卸载命令时出错: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return False, "", error_msg

    def _report_progress(self, command_id, percentage, message):
        """
        报告命令执行进度

        Args:
            command_id (str): 命令ID
            percentage (int): 进度百分比 (0-100)
            message (str): 进度消息
        """
        try:
            logger.info(f"命令 {command_id} 进度: {percentage}% - {message}")

            # 检查连接状态
            if not self.stub:
                logger.warning("无法上报进度：gRPC存根未初始化")
                return False

            # 创建进度上报请求
            try:
                # 如果百分比为负数，表示错误状态
                status = "error" if percentage < 0 else "running"
                if percentage >= 100:
                    status = "completed"

                # 使用命令结果上报接口来上报进度
                # 由于proto文件中CommandResult没有progress字段，我们将进度信息放在output中
                progress_output = f"[PROGRESS:{percentage}] {message}"

                request = terminal_pb2.CommandResult(
                    command_id=command_id,
                    terminal_id=self.config.terminal_id,
                    success=(status != "error"),  # 如果不是错误状态，则为成功
                    output=progress_output,  # 将进度信息编码在输出中
                    error="" if status != "error" else message,  # 如果是错误状态，将消息放在错误中
                    execution_time=int(time.time()),
                    execution_duration=0  # 进度报告不需要执行时间
                )
            except Exception as req_err:
                logger.error(f"创建进度报告请求对象失败: {str(req_err)}")
                return False

            try:
                # 调用远程接口上报进度
                response = self.stub.ReportCommandResult(request, timeout=5)  # 5秒超时

                # 服务器正确接收了进度
                if response and hasattr(response, 'received') and response.received:
                    logger.debug(f"命令进度上报成功: {command_id} - {percentage}%")
                    return True
                else:
                    # 只有真正的错误才记录警告
                    msg = getattr(response, 'message', '未知错误') if response else '服务器无响应'
                    logger.warning(f"命令进度上报失败: {msg}")
                    return False
            except Exception as call_err:
                logger.warning(f"调用上报进度接口时出错: {str(call_err)}")
                return False

        except Exception as e:
            logger.error(f"报告进度时出错: {str(e)}")
            return False

    def _check_admin_privileges(self):
        """
        检查当前进程是否具有管理员权限

        Returns:
            bool: 是否有管理员权限
        """
        try:
            if os.name == 'nt':
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception as e:
            logger.error(f"检查管理员权限时出错: {str(e)}")
            # 如果无法确定，假设没有权限
            return False

    def _find_software_uninstall_command(self, software_name, software_version=""):
        """
        查找软件的卸载命令

        Args:
            software_name (str): 软件名称
            software_version (str, optional): 软件版本

        Returns:
            dict: 包含卸载命令信息的字典，如果未找到则返回None
                 {
                     'command': 卸载命令,
                     'type': 安装程序类型 ('msi', 'inno', 'nsis', 'wise', 'unknown'),
                     'display_name': 显示名称
                 }
        """
        try:
            logger.info(f"搜索软件 '{software_name}' 的卸载信息")
            software_name_lower = software_name.lower()

            import winreg
            uninstall_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]

            matches = []

            # 搜索注册表查找卸载信息
            for regpath in uninstall_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, regpath) as key:
                        i = 0
                        while True:
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, f"{regpath}\\{subkey_name}") as subkey:
                                    try:
                                        # 获取显示名称
                                        display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                        display_name_lower = display_name.lower()

                                        # 更精确的匹配逻辑
                                        # 1. 完全匹配
                                        exact_match = software_name_lower == display_name_lower
                                        # 2. 作为独立的词存在于名称中
                                        word_match = (f" {software_name_lower} " in f" {display_name_lower} " or
                                                     display_name_lower.startswith(f"{software_name_lower} ") or
                                                     display_name_lower.endswith(f" {software_name_lower}"))
                                        # 3. 包含匹配（最低优先级）
                                        contains_match = software_name_lower in display_name_lower

                                        match_quality = 0
                                        if exact_match:
                                            match_quality = 3  # 最高优先级
                                        elif word_match:
                                            match_quality = 2  # 次高优先级
                                        elif contains_match:
                                            match_quality = 1  # 最低优先级
                                        else:
                                            i += 1
                                            continue

                                        # 检查版本（如果提供）
                                        version_match = True
                                        if software_version:
                                            try:
                                                version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                                # 使用更灵活的版本比较
                                                version_match = self._compare_versions(version, software_version)
                                                if not version_match:
                                                    i += 1
                                                    continue
                                            except Exception as e:
                                                logger.debug(f"获取版本信息时出错: {str(e)}")
                                                # 如果没有版本信息，仍然继续但降低匹配质量
                                                if software_version:  # 如果用户指定了版本但找不到，降低优先级
                                                    match_quality -= 0.5

                                        # 获取卸载命令
                                        uninstall_string = None
                                        installer_type = "unknown"

                                        try:
                                            # 首先尝试获取QuietUninstallString
                                            uninstall_string = winreg.QueryValueEx(subkey, "QuietUninstallString")[0]
                                            # 如果存在QuietUninstallString，可能是Inno Setup
                                            installer_type = "inno"
                                        except Exception as reg_error:
                                            try:
                                                # 然后尝试获取UninstallString
                                                uninstall_string = winreg.QueryValueEx(subkey, "UninstallString")[0]

                                                # 尝试判断安装程序类型
                                                if "msiexec" in uninstall_string.lower() or subkey_name.startswith('{'):
                                                    installer_type = "msi"
                                                elif "uninst" in uninstall_string.lower() and "/i" in uninstall_string:
                                                    installer_type = "inno"
                                                elif "uninstall" in uninstall_string.lower() and "/s" in uninstall_string:
                                                    installer_type = "nsis"
                                                elif "wiseunin" in uninstall_string.lower():
                                                    installer_type = "wise"
                                            except Exception as e:
                                                logger.debug(f"获取卸载命令时出错: {str(e)}")
                                                i += 1
                                                continue

                                        if not uninstall_string:
                                            i += 1
                                            continue

                                        # 记录找到的匹配项
                                        matches.append({
                                            'command': uninstall_string,
                                            'type': installer_type,
                                            'display_name': display_name,
                                            'match_quality': match_quality
                                        })

                                    except Exception as e:
                                        logger.debug(f"处理注册表项时出错: {str(e)}")
                                i += 1
                            except WindowsError:
                                break
                except Exception as e:
                    logger.debug(f"打开注册表路径时出错: {str(e)}")

            # 如果找到匹配项，按匹配质量排序并返回最佳匹配
            if matches:
                matches.sort(key=lambda x: x['match_quality'], reverse=True)
                best_match = matches[0]
                logger.info(f"找到软件 '{best_match['display_name']}' 的卸载命令: {best_match['command']}")
                return {
                    'command': best_match['command'],
                    'type': best_match['type'],
                    'display_name': best_match['display_name']
                }

            logger.warning(f"未找到软件 '{software_name}' 的卸载信息")
            return None

        except Exception as e:
            logger.error(f"搜索软件卸载信息时出错: {str(e)}", exc_info=True)
            return None

    def _compare_versions(self, version1, version2):
        """
        比较两个版本号

        Args:
            version1 (str): 第一个版本号
            version2 (str): 第二个版本号

        Returns:
            bool: 如果版本相同或非常接近则返回True
        """
        try:
            # 1. 首先尝试精确匹配
            if version1.lower() == version2.lower():
                return True

            # 2. 然后尝试提取主要版本部分进行比较
            def extract_main_version(v):
                # 提取数字和点部分，忽略其他字符
                import re
                match = re.search(r'^(\d+(\.\d+)*)', v)
                if match:
                    return match.group(1)
                return v

            main_v1 = extract_main_version(version1)
            main_v2 = extract_main_version(version2)

            # 比较主要版本部分
            if main_v1 == main_v2:
                return True

            # 3. 尝试语义化版本比较
            try:
                # 将版本字符串拆分为组件并比较
                v1_parts = [int(part) for part in main_v1.split('.')]
                v2_parts = [int(part) for part in main_v2.split('.')]

                # 兼容不同长度的版本号
                length = min(len(v1_parts), len(v2_parts))

                # 比较主要版本号部分
                for i in range(length):
                    if v1_parts[i] != v2_parts[i]:
                        # 如果只有补丁版本号不同，仍然认为是匹配的
                        return i >= 2  # 0=主版本 1=次版本 2=补丁版本

                # 长度不同但共同部分相同，视为匹配
                return True
            except (ValueError, TypeError, IndexError) as ve:
                # 如果语义化比较失败，回退到宽松匹配
                # 检查一个版本是否包含另一个版本
                logger.debug(f"语义化版本比较失败: {str(ve)}，回退到简单比较")
                return main_v1 in main_v2 or main_v2 in main_v1

        except Exception as e:
            logger.debug(f"比较版本号时出错: {str(e)}")
            # 如果比较出错，回退到简单字符串比较
            return version1.lower() == version2.lower()

    def _verify_software_uninstalled(self, software_name, software_version=""):
        """
        验证软件是否已卸载

        Args:
            software_name (str): 软件名称
            software_version (str, optional): 软件版本

        Returns:
            bool: 如果软件已卸载返回True，否则返回False
        """
        try:
            # 尝试再次查找软件，如果找不到说明已卸载
            return self._find_software_uninstall_command(software_name, software_version) is None
        except Exception as e:
            logger.error(f"验证软件卸载状态时出错: {str(e)}")
            # 如果出错，保守地假设软件还存在
            return False

    def _clean_software_residuals(self, software_name):
        """
        清理软件卸载后的潜在残留

        Args:
            software_name (str): 软件名称
        """
        try:
            # 实现残留清理逻辑，如清理临时文件
            # 这里只是一个示例框架，可根据需要实现
            logger.info(f"清理软件 '{software_name}' 的残留")

            # 1. 清理Program Files目录下的残留文件夹
            program_dirs = [
                os.environ.get('ProgramFiles', 'C:\\Program Files'),
                os.environ.get('ProgramFiles(x86)', 'C:\\Program Files (x86)')
            ]

            for program_dir in program_dirs:
                potential_path = os.path.join(program_dir, software_name)
                if os.path.exists(potential_path):
                    logger.info(f"发现残留目录: {potential_path}")
                    try:
                        # 谨慎删除，避免误删其他重要文件
                        # 实际实现时需更精确的匹配和安全检查
                        # shutil.rmtree(potential_path)
                        logger.info(f"残留目录需要手动移除: {potential_path}")
                    except Exception as e:
                        logger.error(f"移除残留目录时出错: {str(e)}")

            # 2. 其他可能的清理操作

        except Exception as e:
            logger.error(f"清理软件残留时出错: {str(e)}")
            # 失败时不影响主卸载流程

    def _start_notification_service(self):
        """启动本地gRPC服务，用于接收命令通知"""
        try:
            from http.server import HTTPServer, BaseHTTPRequestHandler
            import threading
            import json

            # 随机选择一个可用端口，从50050开始尝试
            self.notification_port = 50050
            max_attempts = 10

            for i in range(max_attempts):
                try:
                    # 检查端口是否被占用
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(("127.0.0.1", self.notification_port))
                    break
                except OSError:
                    logger.warning(f"端口 {self.notification_port} 已被占用，尝试下一个端口")
                    self.notification_port += 1
                    if i == max_attempts - 1:
                        logger.error(f"无法找到可用端口，放弃启动通知服务")
                        return False

            # 创建Handler类，用于处理命令通知和状态查询
            agent = self  # 保存self引用供内部类使用

            class NotificationHandler(BaseHTTPRequestHandler):
                def _set_headers(self, content_type="application/json"):
                    self.send_response(200)
                    self.send_header('Content-type', content_type)
                    self.end_headers()

                def _handle_error(self, error_msg, status_code=400):
                    self.send_response(status_code)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    response = {"error": error_msg}
                    self.wfile.write(json.dumps(response).encode('utf-8'))

                def do_POST(self):
                    """处理POST请求 - 命令通知"""
                    try:
                        # 检查路径
                        if self.path != "/notify":
                            self._handle_error("无效的API路径", 404)
                            return

                        # 读取请求数据
                        content_length = int(self.headers['Content-Length'])
                        post_data = self.rfile.read(content_length).decode('utf-8')
                        data = json.loads(post_data)

                        # 提取命令信息
                        command_id = data.get("command_id")
                        if not command_id:
                            self._handle_error("缺少命令ID")
                            return

                        logger.info(f"收到命令通知: {command_id}")

                        # 接收命令
                        threading.Thread(
                            target=agent.receive_commands,
                            name="CommandReceiver",
                            daemon=True
                        ).start()

                        # 返回成功响应
                        self._set_headers()
                        response = {"success": True, "message": "通知已处理"}
                        self.wfile.write(json.dumps(response).encode('utf-8'))
                    except json.JSONDecodeError:
                        self._handle_error("无效的JSON数据")
                    except Exception as e:
                        self._handle_error(f"处理请求时出错: {str(e)}", 500)

                def do_GET(self):
                    """处理GET请求 - 状态查询"""
                    try:
                        # 检查路径
                        if self.path == "/status":
                            # 获取Agent状态
                            status = agent.get_status()

                            # 返回状态信息
                            self._set_headers()
                            self.wfile.write(json.dumps(status, ensure_ascii=False).encode('utf-8'))
                        elif self.path == "/health":
                            # 简单的健康检查
                            self._set_headers(content_type="text/plain")
                            self.wfile.write("OK".encode('utf-8'))
                        else:
                            self._handle_error("无效的API路径", 404)
                    except Exception as e:
                        self._handle_error(f"处理请求时出错: {str(e)}", 500)

            # 创建HTTP服务器
            server_address = ('', self.notification_port)
            self.notification_server = HTTPServer(server_address, NotificationHandler)

            # 在单独的线程中启动服务器
            server_thread = threading.Thread(
                target=self.notification_server.serve_forever,
                name="NotificationServer",
                daemon=True
            )
            server_thread.start()

            logger.info(f"通知服务已启动，监听端口: {self.notification_port}")
            return True

        except Exception as e:
            logger.error(f"启动通知服务失败: {str(e)}")
            return False


def main():
    """
    Agent主函数
    """
    try:
        # 解析命令行参数
        import argparse
        parser = argparse.ArgumentParser(description='Terminal Management Agent')
        parser.add_argument('--config', type=str, default='agent_config.json', help='配置文件路径')
        parser.add_argument('--server', type=str, help='服务器地址 (host:port)')
        parser.add_argument('--tls', action='store_true', help='启用TLS加密')
        parser.add_argument('--cert', type=str, help='TLS证书文件路径')
        args = parser.parse_args()

        # 创建和初始化Agent
        agent = TerminalAgent()

        # 确保配置目录存在
        config_dir = os.path.dirname(os.path.abspath(args.config))
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)

        # 加载配置
        agent.config.load_config(args.config)

        # 如果命令行参数指定了服务器地址，覆盖配置文件中的设置
        if args.server:
            agent.config.server_address = args.server

        # 如果命令行参数指定了TLS选项，覆盖配置文件中的设置
        if args.tls:
            agent.config.use_tls = True

        # 如果命令行参数指定了证书文件，覆盖配置文件中的设置
        if args.cert:
            agent.config.cert_file = args.cert

        # 初始化Agent
        success = agent.initialize(args.config)

        if success:
            logger.info("Agent初始化成功，开始运行...")
            logger.info(f"当前心跳间隔配置为: {agent.config.heartbeat_interval}秒")

            try:
                # 主循环
                while agent.running:
                    # 主线程可以处理主要逻辑，后台线程处理心跳和信息采集
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("收到中断信号，正在关闭Agent...")
                agent.shutdown()

        else:
            logger.error("Agent初始化失败，退出程序")
            return 1

        return 0

    except Exception as e:
        logger.error(f"Agent运行时发生错误: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())