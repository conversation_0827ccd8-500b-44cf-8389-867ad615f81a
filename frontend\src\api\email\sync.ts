import request from '@/utils/request'

// 邮箱同步日志相关接口
export const emailSyncLogApi = {
  // 获取同步日志列表
  getSyncLogs: (params?: {
    page?: number
    size?: number
    sync_type?: string
    status?: string
  }) => {
    // 添加时间戳参数防止缓存
    const queryParams = {
      ...params,
      _t: Date.now()
    }
    return request.get('/email/sync/logs', { params: queryParams })
  },

  // 获取单个同步日志详情
  getSyncLog: (logId: number) => {
    // 添加时间戳参数防止缓存
    return request.get(`/email/sync/logs/${logId}`, { 
      params: { _t: Date.now() } 
    })
  },

  // 获取各个同步类型的最新同步时间
  getLatestSyncTimes: () => {
    // 添加时间戳参数防止缓存
    return request.get('/email/sync/latest-times', { 
      params: { _t: Date.now() } 
    })
  },

  // 清理旧日志
  cleanupOldLogs: (days: number = 30) => {
    return request.delete('/email/sync/logs/cleanup', { params: { days } })
  }
} 