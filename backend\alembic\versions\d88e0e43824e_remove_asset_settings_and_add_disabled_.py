"""remove_asset_settings_and_add_disabled_users

Revision ID: d88e0e43824e
Revises: dc179410070d
Create Date: 2025-03-20 11:47:09.213144

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = 'd88e0e43824e'
down_revision: Union[str, None] = 'dc179410070d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.add_column('ad_sync_logs', sa.Column('disabled_users', sa.Integer(), nullable=True, comment='禁用账号数'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ad_sync_logs', 'disabled_users')
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    # ### end Alembic commands ###
