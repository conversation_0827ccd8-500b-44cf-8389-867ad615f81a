from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.crud.role import role_crud
from app.crud.permission import permission_crud
from app.crud.user import user_crud

router = APIRouter()

@router.get("/", response_model=List[schemas.Role])
def read_roles(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user = Depends(deps.check_permissions(["system:role:view"])),
) -> Any:
    """
    获取角色列表
    """
    roles = role_crud.get_multi(db, skip=skip, limit=limit)
    return roles

@router.get("/{role_id}", response_model=schemas.Role)
def read_role(
    role_id: int,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:role:view"])),
) -> Any:
    """
    获取角色详情
    """
    role = role_crud.get(db, id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    return role

@router.post("/", response_model=schemas.Role)
def create_role(
    *,
    db: Session = Depends(deps.get_db),
    role_in: schemas.RoleCreate,
    current_user = Depends(deps.check_permissions(["system:role:add"])),
) -> Any:
    """
    创建角色
    """
    role = role_crud.get_by_code(db, code=role_in.code)
    if role:
        raise HTTPException(status_code=400, detail="角色代码已存在")
    role = role_crud.create(db, obj_in=role_in)
    return role

@router.put("/{role_id}", response_model=schemas.Role)
def update_role(
    *,
    db: Session = Depends(deps.get_db),
    role_id: int,
    role_in: schemas.RoleUpdate,
    current_user = Depends(deps.check_permissions(["system:role:edit"])),
) -> Any:
    """
    更新角色
    """
    role = role_crud.get(db, id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    role = role_crud.update(db, db_obj=role, obj_in=role_in)
    return role

@router.delete("/{role_id}", response_model=schemas.Role)
def delete_role(
    *,
    db: Session = Depends(deps.get_db),
    role_id: int,
    current_user = Depends(deps.check_permissions(["system:role:delete"])),
) -> Any:
    """
    删除角色
    """
    role = role_crud.get(db, id=role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    role = role_crud.remove(db, id=role_id)
    return role

@router.post("/assign-permissions", response_model=schemas.Role)
def assign_permissions(
    *,
    db: Session = Depends(deps.get_db),
    permission_assign: schemas.PermissionAssign,
    current_user = Depends(deps.check_permissions(["system:permission:assign"])),
) -> Any:
    """
    为角色分配权限
    """
    role = role_crud.get(db, id=permission_assign.role_id)
    if not role:
        raise HTTPException(status_code=404, detail="角色不存在")
    
    # 验证权限是否都存在
    for permission_id in permission_assign.permission_ids:
        if not permission_crud.get(db, id=permission_id):
            raise HTTPException(status_code=404, detail=f"权限ID {permission_id} 不存在")
    
    # 更新角色权限
    role = role_crud.update_with_permissions(
        db, db_obj=role, obj_in={}, permission_ids=permission_assign.permission_ids
    )
    return role

@router.post("/assign-roles", response_model=schemas.User)
def assign_roles(
    *,
    db: Session = Depends(deps.get_db),
    role_assign: schemas.RoleAssign,
    current_user = Depends(deps.check_permissions(["system:user:edit"])),
) -> Any:
    """
    为用户分配角色
    """
    user = user_crud.get(db, id=role_assign.user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 保护内置账号
    if user.is_builtin:
        raise HTTPException(status_code=400, detail="不能修改内置账号的角色")
    
    # 验证角色是否都存在
    for role_id in role_assign.role_ids:
        if not role_crud.get(db, id=role_id):
            raise HTTPException(status_code=404, detail=f"角色ID {role_id} 不存在")
    
    # 分配角色
    user = role_crud.assign_roles_to_user(
        db, user_id=role_assign.user_id, role_ids=role_assign.role_ids
    )
    return user 