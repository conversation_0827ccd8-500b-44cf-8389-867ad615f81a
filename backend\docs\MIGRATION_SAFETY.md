# 数据库迁移安全指南

## 概述

本文档提供数据库迁移的安全操作指南，防止意外删除重要的业务表。

## 受保护的关键表

以下表被标记为**受保护表**，在任何迁移操作中都不应删除：

- `asset_settings` - 资产管理设置（⚠️ CRITICAL）
- `assets` - 资产信息
- `users` - 用户信息
- `permissions` - 权限配置
- `roles` - 角色配置
- `email_configs` - 邮箱配置
- `ad_config` - AD配置
- `terminals` - 终端信息

## 安全检查工具

### 迁移安全检查脚本

位置：`backend/scripts/check_migration_safety.py`

用法：
```bash
cd backend
python scripts/check_migration_safety.py
```

该脚本会扫描所有迁移文件，检测是否包含对受保护表的危险操作。

### 检查结果说明

- ✅ **安全**：没有发现危险操作
- ⚠️ **WARNING**：删除表索引（通常不致命）
- 🚨 **DANGER**：删除受保护表（严重问题）

## 最佳实践

### 1. 迁移前检查

创建新迁移后，运行安全检查：
```bash
# 生成迁移
alembic revision --autogenerate -m "your_description"

# 安全检查
python scripts/check_migration_safety.py

# 如果发现问题，编辑迁移文件后再次检查
```

### 2. 代码审查

- 所有迁移文件都应经过代码审查
- 特别关注 `drop_table()` 和 `drop_index()` 操作
- 确保不会误删除受保护表

### 3. 模型标记

受保护的模型类应包含警告注释：
```python
class AssetSettings(Base):
    """资产管理设置模型
    
    ⚠️  CRITICAL TABLE - DO NOT DELETE ⚠️
    这是核心业务表，包含资产管理的重要配置信息。
    在任何迁移操作中都不应删除此表！
    """
    __tablename__ = "asset_settings"
```

## 紧急修复流程

如果不慎删除了重要表：

### 1. 立即回滚
```bash
# 查看当前版本
alembic current

# 回滚到表存在的版本
alembic downgrade <previous_revision>
```

### 2. 修复迁移文件
- 编辑问题迁移文件
- 注释掉删除表的操作
- 添加保护注释

### 3. 重新应用
```bash
# 重新应用修复后的迁移
alembic upgrade head
```

### 4. 验证修复
```bash
# 运行测试验证
python test_fix_simple.py
```

## 案例研究：asset_settings表误删

### 问题描述
迁移文件 `bb3657ac3497_add_is_builtin_field_to_users.py` 中意外包含了删除 `asset_settings` 表的操作，导致API报错。

### 修复步骤
1. 回滚到 `4c53a5efd16a`
2. 编辑迁移文件，注释删除操作
3. 添加保护注释
4. 重新应用迁移
5. 验证功能正常

### 经验教训
- 自动生成的迁移可能包含意外操作
- 必须仔细审查每个迁移文件
- 使用安全检查工具预防问题

## 联系方式

如有迁移安全相关问题，请联系开发团队。 