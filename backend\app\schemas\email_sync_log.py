from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel


class EmailSyncLogBase(BaseModel):
    """邮箱同步日志基础模式"""
    sync_type: str
    status: str
    message: Optional[str] = None
    synced_count: int = 0
    updated_count: int = 0
    total_count: int = 0
    duration: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class EmailSyncLogCreate(EmailSyncLogBase):
    """创建邮箱同步日志模式"""
    pass


class EmailSyncLogUpdate(BaseModel):
    """更新邮箱同步日志模式"""
    status: Optional[str] = None
    message: Optional[str] = None
    synced_count: Optional[int] = None
    updated_count: Optional[int] = None
    total_count: Optional[int] = None
    duration: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class EmailSyncLog(EmailSyncLogBase):
    """邮箱同步日志响应模式"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class EmailSyncLogList(BaseModel):
    """邮箱同步日志列表响应模式"""
    logs: list[EmailSyncLog]
    total: int
    page: int
    size: int
    pages: int


class LatestSyncTimes(BaseModel):
    """最新同步时间响应模式"""
    departments: Optional[str] = None
    members: Optional[str] = None
    groups: Optional[str] = None
    tags: Optional[str] = None
    full: Optional[str] = None 