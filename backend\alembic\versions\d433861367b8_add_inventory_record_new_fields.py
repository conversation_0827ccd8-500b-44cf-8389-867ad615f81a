"""add_inventory_record_new_fields

Revision ID: d433861367b8
Revises: 31700079acc7
Create Date: 2024-12-20 09:55:10.220441

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = 'd433861367b8'
down_revision: Union[str, None] = '31700079acc7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_field_values_id', table_name='field_values')
    op.drop_table('field_values')
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.add_column('inventory_records', sa.Column('new_custodian_department', sa.String(length=100), nullable=True, comment='新领用人部门'))
    op.add_column('inventory_records', sa.Column('new_user_department', sa.String(length=100), nullable=True, comment='新使用人部门'))
    op.add_column('inventory_records', sa.Column('new_location', sa.String(length=200), nullable=True, comment='新存放位置'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('inventory_records', 'new_location')
    op.drop_column('inventory_records', 'new_user_department')
    op.drop_column('inventory_records', 'new_custodian_department')
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.create_table('field_values',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('field_name', sa.VARCHAR(length=50), nullable=False),
    sa.Column('field_value', sa.VARCHAR(length=200), nullable=False),
    sa.Column('description', sa.VARCHAR(length=500), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_field_values_id', 'field_values', ['id'], unique=False)
    # ### end Alembic commands ###
