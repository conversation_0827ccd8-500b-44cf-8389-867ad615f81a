from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from app.models.email import EmailSyncLog
from app.schemas.email_sync_log import EmailSyncLogCreate


def create_sync_log(db: Session, log_data: EmailSyncLogCreate) -> EmailSyncLog:
    """创建同步日志"""
    db_log = EmailSyncLog(**log_data.dict())
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log


def get_sync_logs(
    db: Session, 
    skip: int = 0, 
    limit: int = 20,
    sync_type: Optional[str] = None,
    status: Optional[str] = None
) -> Dict[str, Any]:
    """获取同步日志列表"""
    query = db.query(EmailSyncLog)
    
    # 过滤条件
    if sync_type:
        query = query.filter(EmailSyncLog.sync_type == sync_type)
    if status:
        query = query.filter(EmailSyncLog.status == status)
    
    # 总数
    total = query.count()
    
    # 分页和排序
    logs = query.order_by(desc(EmailSyncLog.created_at)).offset(skip).limit(limit).all()
    
    return {
        "logs": logs,
        "total": total,
        "page": (skip // limit) + 1 if limit > 0 else 1,
        "size": limit,
        "pages": (total + limit - 1) // limit if limit > 0 else 1
    }


def get_sync_log_by_id(db: Session, log_id: int) -> Optional[EmailSyncLog]:
    """根据ID获取同步日志"""
    return db.query(EmailSyncLog).filter(EmailSyncLog.id == log_id).first()


def get_latest_sync_times(db: Session) -> Dict[str, Optional[str]]:
    """获取各个同步类型的最新同步时间"""
    sync_types = ['departments', 'members', 'groups', 'tags', 'full']
    result = {}
    
    for sync_type in sync_types:
        latest_log = db.query(EmailSyncLog).filter(
            EmailSyncLog.sync_type == sync_type,
            EmailSyncLog.status == 'success'
        ).order_by(desc(EmailSyncLog.created_at)).first()
        
        result[sync_type] = latest_log.created_at.strftime('%Y-%m-%d %H:%M:%S') if latest_log else None
    
    return result


def delete_old_logs(db: Session, days: int = 30) -> int:
    """删除指定天数之前的旧日志"""
    from datetime import datetime, timedelta
    
    cutoff_date = datetime.now() - timedelta(days=days)
    deleted_count = db.query(EmailSyncLog).filter(
        EmailSyncLog.created_at < cutoff_date
    ).delete()
    
    db.commit()
    return deleted_count 