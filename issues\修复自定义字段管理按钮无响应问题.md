# 修复自定义字段管理按钮无响应问题

## 问题描述
自定义字段管理页面中，所有操作按钮（新增字段、编辑、删除等）点击后没有任何反应，控制台出现JavaScript错误。

## 错误信息
```
Uncaught (in promise) ReferenceError: Cannot access 'resetForm' before initialization
Unhandled error during execution of setup function
```

## 问题原因
在 `CustomFieldForm.vue` 组件中，Vue 3的setup语法中函数定义顺序存在问题：
1. `watch` 监听器在组件初始化时立即执行（`{ immediate: true }`）
2. 当 `props.initialData` 为 `null` 时，会调用 `resetForm()` 函数
3. 但此时 `resetForm` 函数还没有被定义（在代码后面才定义）
4. 导致 `ReferenceError: Cannot access 'resetForm' before initialization` 错误

## 解决方案
重新组织 `CustomFieldForm.vue` 组件中的函数定义顺序：

### 修改文件
- `frontend/src/views/system/components/CustomFieldForm.vue`
- `frontend/src/views/system/CustomFieldManagement.vue`

### 主要变更
1. **函数定义重新排序**：
   - 将 `resetForm` 函数移到 `watch` 监听器之前定义
   - 按照依赖关系重新组织所有函数的定义顺序
   - 使用注释分组：工具函数 → 事件处理函数 → 监听器和生命周期

2. **代码结构优化**：
   ```javascript
   // ============ 工具函数定义 ============
   const resetForm = () => { /* ... */ }
   const initializeFormData = () => { /* ... */ }
   
   // ============ 事件处理函数 ============
   const handleSubmit = () => { /* ... */ }
   const handleCancel = () => { /* ... */ }
   
   // ============ 监听器和生命周期 ============
   watch(() => props.initialData, (newData) => {
     if (newData) {
       initializeFormData()
     } else {
       resetForm() // 现在可以安全调用
     }
   }, { immediate: true })
   ```

3. **事件监听器修复**：
   - 在CustomFieldManagement.vue中为CustomFieldForm组件添加缺失的@cancel事件监听器

## 后续问题修复
### 编辑字段对话框取消按钮无响应
**问题**：编辑字段对话框中的取消按钮点击无反应
**原因**：CustomFieldManagement.vue中缺少对CustomFieldForm组件的`@cancel`事件监听
**修复**：在CustomFieldForm组件上添加`@cancel="closeFieldDialog"`事件监听器

```vue
<CustomFieldForm
  ref="fieldFormRef"
  :initial-data="currentField"
  :mode="fieldDialogMode"
  @submit="handleFieldSubmit"
  @cancel="closeFieldDialog"  <!-- 添加这行 -->
/>
```

### 盘点任务自定义字段显示问题
**问题**：用户在自定义字段管理中创建了"仅盘点记录"类型的字段，但盘点任务中不显示
**原因**：前后端applies_to值不匹配
- 后端枚举定义：`INVENTORY_RECORD = "inventory_record"`
- 前端API调用：`applies_to: 'inventory'`

**修复**：统一前后端applies_to值
```typescript
// 修改前
const response = await customFieldApi.getActiveCustomFields({ applies_to: 'inventory' })

// 修改后  
const response = await customFieldApi.getActiveCustomFields({ applies_to: 'inventory_record' })
```

**修改文件**：
- `frontend/src/views/inventory/TaskDetail.vue`
- `frontend/src/mobile/views/asset/InventoryTask.vue`

## 验证功能
现在所有功能应该正常工作：
1. ✅ 自定义字段管理页面按钮正常响应
2. ✅ 新增字段、编辑字段、删除字段功能正常
3. ✅ 创建预设字段功能正常
4. ✅ 取消按钮正常关闭对话框
5. ✅ 盘点任务中正确显示"仅盘点记录"类型的自定义字段

## 修复时间
2025-01-03 当日完成（包含所有相关问题修复） 