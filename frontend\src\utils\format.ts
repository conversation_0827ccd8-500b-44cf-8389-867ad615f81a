/**
 * 格式化日期时间
 * @param dateTimeString ISO格式的日期时间字符串
 * @param format 格式化选项，可选值：'date'（仅日期）, 'time'（仅时间）, 'datetime'（日期和时间）
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(dateTimeString: string | null | undefined, format: 'date' | 'time' | 'datetime' = 'datetime'): string {
  if (!dateTimeString) return '';
  
  try {
    const date = new Date(dateTimeString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return dateTimeString;
    }
    
    // 格式化年月日
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // 格式化时分秒
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    // 根据格式返回不同的结果
    switch (format) {
      case 'date':
        return `${year}年${month}月${day}日`;
      case 'time':
        return `${hours}:${minutes}:${seconds}`;
      case 'datetime':
      default:
        return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;
    }
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateTimeString;
  }
}

/**
 * 字段名称中英文映射
 */
export const fieldNameMap: Record<string, string> = {
  // 资产相关字段 - 基于资产模型和前端展示
  name: '资产名称',
  company: '公司',
  asset_number: '资产编号',
  status: '状态',
  category: '资产类别',
  specification: '规格型号',
  custodian: '领用人',
  custodian_department: '领用人部门',
  custodian_job_number: '领用人工号',
  user: '使用人',
  user_department: '使用人部门',
  user_job_number: '使用人工号',
  location: '资产存放位置',
  purchase_date: '入账日期',
  retirement_date: '销账日期',
  remarks: '备注',
  production_number: '生产编号',
  price: '价格',
  supplier: '供应商',
  manufacturer: '厂商',
  purchaser: '采购人',
  purchaser_job_number: '采购人工号',
  inspector: '验收人',
  inspector_job_number: '验收人工号',
  created_at: '创建时间',
  updated_at: '更新时间'
  
  // 已删除未使用的字段，如有新增字段，可在此添加
} 