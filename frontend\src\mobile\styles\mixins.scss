// 移动端样式混入函数
@use './variables.scss' as *;

// 安全区域适配
@mixin safe-area-inset($property: padding, $direction: bottom) {
  #{$property}-#{$direction}: constant(safe-area-inset-#{$direction});
  #{$property}-#{$direction}: env(safe-area-inset-#{$direction});
}

// 1px边框（解决移动端1px边框问题）
@mixin hairline($direction: all, $color: #ebedf0) {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    border: 1px solid $color;
    transform-origin: 0 0;
    transform: scale(0.5);
    pointer-events: none;
    
    @if $direction == all {
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
    } @else if $direction == top {
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      border-top: 1px solid $color;
      border-left: none;
      border-right: none;
      border-bottom: none;
    } @else if $direction == bottom {
      bottom: 0;
      left: 0;
      width: 100%;
      height: 1px;
      border-bottom: 1px solid $color;
      border-left: none;
      border-right: none;
      border-top: none;
    }
  }
}

// 容器查询支持
@mixin container-query($min-width) {
  @supports (container-type: inline-size) {
    @container (min-width: #{$min-width}) {
      @content;
    }
  }
  
  // 降级方案
  @supports not (container-type: inline-size) {
    @media (min-width: #{$min-width}) {
      @content;
    }
  }
}

// 响应式断点
@mixin mobile-small {
  @media (max-width: 374px) {
    @content;
  }
}

@mixin mobile-medium {
  @media (min-width: 375px) and (max-width: 413px) {
    @content;
  }
}

@mixin mobile-large {
  @media (min-width: 414px) and (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

// 触摸优化
@mixin touch-area($min-size: 44px) {
  min-height: $min-size;
  min-width: $min-size;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  
  // 提供触觉反馈
  &:active {
    opacity: 0.7;
    transform: scale(0.95);
  }
}

// 文字省略
@mixin ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $lines;
  }
}

// 居中对齐
@mixin center($direction: both) {
  @if $direction == both {
    display: flex;
    align-items: center;
    justify-content: center;
  } @else if $direction == horizontal {
    display: flex;
    justify-content: center;
  } @else if $direction == vertical {
    display: flex;
    align-items: center;
  }
}

// 固定定位
@mixin fixed($top: auto, $right: auto, $bottom: auto, $left: auto) {
  position: fixed;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
  z-index: 100;
}

// 渐变背景
@mixin gradient($start-color, $end-color, $direction: to bottom) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 圆角
@mixin border-radius($radius: $border-radius-md) {
  border-radius: $radius;
  overflow: hidden;
}

// 阴影
@mixin shadow($level: medium) {
  @if $level == light {
    box-shadow: $box-shadow-light;
  } @else if $level == medium {
    box-shadow: $box-shadow-medium;
  } @else if $level == heavy {
    box-shadow: $box-shadow-heavy;
  }
}

// 动画过渡
@mixin transition($property: all, $duration: $transition-normal) {
  transition: $property $duration;
} 