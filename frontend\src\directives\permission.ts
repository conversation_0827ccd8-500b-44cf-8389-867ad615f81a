import type { App, Directive, DirectiveBinding } from 'vue'
import { hasPermission, hasAnyPermission } from '@/utils/permission'

/**
 * 权限指令v-permission
 * 使用方式:
 * 1. 单个权限: v-permission="'system:view'"
 * 2. 多个权限(任意一个满足): v-permission="['system:view', 'system:edit']"
 */
const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    
    if (!value) {
      console.warn('使用v-permission指令需要权限代码')
      return
    }
    
    let hasAuth = false
    
    if (typeof value === 'string') {
      // 检查单个权限
      hasAuth = hasPermission(value)
    } else if (Array.isArray(value)) {
      // 检查多个权限（有一个满足即可）
      hasAuth = hasAnyPermission(value)
    }
    
    if (!hasAuth) {
      // 如果没有权限，从DOM中移除元素
      const parentNode = el.parentNode
      if (parentNode) {
        parentNode.removeChild(el)
      } else {
        el.style.display = 'none'
      }
    }
  }
}

// 注册所有指令
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permission)
} 