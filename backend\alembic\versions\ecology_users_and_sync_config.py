"""Add ecology users and sync config tables

Revision ID: ecology_users_and_sync_config
Revises: 62376e33ea13
Create Date: 2023-02-28 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ecology_users_and_sync_config'
down_revision = '62376e33ea13'
branch_labels = None
depends_on = None


def upgrade():
    # 创建泛微用户表
    op.create_table('ecology_users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('dept_id', sa.Integer(), nullable=True),
        sa.Column('dept_name', sa.String(), nullable=True),
        sa.Column('dept_hierarchy', sa.String(), nullable=True),
        sa.Column('level', sa.Integer(), nullable=True),
        sa.Column('dept_path', sa.String(), nullable=True),
        sa.Column('company_id', sa.Integer(), nullable=True),
        sa.Column('company_name', sa.String(), nullable=True),
        sa.Column('user_name', sa.String(), nullable=True),
        sa.Column('job_number', sa.String(), nullable=True),
        sa.Column('mobile', sa.String(), nullable=True),
        sa.Column('email', sa.String(), nullable=True),
        sa.Column('job_title', sa.Integer(), nullable=True),
        sa.Column('job_title_name', sa.String(), nullable=True),
        sa.Column('gender', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index(op.f('ix_ecology_users_id'), 'ecology_users', ['id'], unique=False)
    op.create_index(op.f('ix_ecology_users_user_id'), 'ecology_users', ['user_id'], unique=False)
    op.create_index(op.f('ix_ecology_users_dept_id'), 'ecology_users', ['dept_id'], unique=False)
    op.create_index(op.f('ix_ecology_users_dept_name'), 'ecology_users', ['dept_name'], unique=False)
    op.create_index(op.f('ix_ecology_users_company_id'), 'ecology_users', ['company_id'], unique=False)
    op.create_index(op.f('ix_ecology_users_company_name'), 'ecology_users', ['company_name'], unique=False)
    op.create_index(op.f('ix_ecology_users_user_name'), 'ecology_users', ['user_name'], unique=False)
    op.create_index(op.f('ix_ecology_users_job_number'), 'ecology_users', ['job_number'], unique=False)
    
    # 创建同步配置表
    op.create_table('ecology_sync_config',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('sync_interval', sa.Integer(), nullable=True),
        sa.Column('last_sync_time', sa.DateTime(), nullable=True),
        sa.Column('next_sync_time', sa.DateTime(), nullable=True),
        sa.Column('sync_status', sa.String(), nullable=True),
        sa.Column('error_message', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建索引
    op.create_index(op.f('ix_ecology_sync_config_id'), 'ecology_sync_config', ['id'], unique=False)


def downgrade():
    # 删除表和索引
    op.drop_index(op.f('ix_ecology_sync_config_id'), table_name='ecology_sync_config')
    op.drop_table('ecology_sync_config')
    
    op.drop_index(op.f('ix_ecology_users_job_number'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_user_name'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_company_name'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_company_id'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_dept_name'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_dept_id'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_user_id'), table_name='ecology_users')
    op.drop_index(op.f('ix_ecology_users_id'), table_name='ecology_users')
    op.drop_table('ecology_users') 