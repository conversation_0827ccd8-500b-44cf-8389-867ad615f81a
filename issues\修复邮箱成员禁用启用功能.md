# 修复邮箱成员禁用启用功能

## 问题描述
邮箱管理中的禁用成员和开启成员功能没有起作用，虽然显示禁用和开启成功了，但是实际并没有变化。

## 问题分析
1. 前端调用 `updateMemberStatus` API 正确
2. 后端 `update_member_active` API 只更新了本地数据库的 `is_active` 字段 
3. **核心问题**：没有调用腾讯企业邮箱API来同步启用/禁用状态
4. 腾讯API服务中的 `disable_member` 和 `enable_member` 方法已实现但未被使用

## 解决方案
使用方案1：直接修改现有API，在 `update_member_active` API中添加对腾讯API的调用

## 修改内容

### 1. 后端API修改 (`backend/app/api/v1/email.py`)
- 修改 `update_member_active` 函数
- 添加腾讯企业邮箱API调用逻辑
- 根据 `is_active` 状态调用相应的启用/禁用方法
- 添加错误处理和日志记录
- 确保API调用成功后再更新本地数据库

### 2. 修改详情
```python
# 调用腾讯企业邮箱API进行启用/禁用操作
try:
    api_service = TencentEmailAPIService(db)
    
    if active_update.is_active:
        # 启用成员
        result = await api_service.enable_member(userid)
        action = "启用"
    else:
        # 禁用成员
        result = await api_service.disable_member(userid)
        action = "禁用"
    
    # 检查API调用结果
    if result.errcode != 0:
        logger.error(f"腾讯API{action}成员失败: {result.errmsg} (错误码: {result.errcode})")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"腾讯API{action}成员失败: {result.errmsg}"
        )
    
    logger.info(f"腾讯API{action}成员成功: {userid}")
    
except HTTPException:
    # 重新抛出HTTP异常
    raise
except Exception as e:
    logger.error(f"调用腾讯API{action}成员时发生错误: {str(e)}")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"调用腾讯API时发生错误: {str(e)}"
    )

# API调用成功后，更新本地数据库中的成员状态
```

## 测试验证
修改完成后需要测试：
1. 禁用成员功能是否同步到腾讯企业邮箱
2. 启用成员功能是否同步到腾讯企业邮箱
3. 错误处理是否正常工作
4. 前端界面是否正确显示操作结果

## 预期结果
- 启用/禁用成员时同时更新本地数据库和腾讯企业邮箱
- 保持现有前端接口不变
- 用户操作后能看到实际的启用/禁用效果
- 操作失败时能给出明确的错误提示

## 关键文件
- `backend/app/api/v1/email.py` - 后端API接口
- `backend/app/services/email_api.py` - 腾讯API服务
- `frontend/src/views/email/DepartmentMemberManagement.vue` - 前端界面
- `frontend/src/api/email/member.ts` - 前端API调用 