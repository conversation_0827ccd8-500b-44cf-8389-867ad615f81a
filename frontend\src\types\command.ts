// 命令分类
export interface CommandCategory {
  id: number
  name: string
  description?: string
  required_permission: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CommandCategoryCreate {
  name: string
  description?: string
  required_permission: string
  is_active?: boolean
}

export interface CommandCategoryUpdate {
  name?: string
  description?: string
  required_permission?: string
  is_active?: boolean
}

// 命令白名单
export interface CommandWhitelist {
  id: number
  category_id: number
  command: string
  name: string
  description?: string
  example?: string
  timeout: number
  admin_required: boolean
  is_active: boolean
  security_level: 'PUBLIC' | 'OPERATOR' | 'ADMIN'
  created_at: string
  updated_at: string
  category?: CommandCategory
}

export interface CommandWhitelistCreate {
  category_id: number
  command: string
  name: string
  description?: string
  example?: string
  timeout?: number
  admin_required?: boolean
  is_active?: boolean
  security_level?: 'PUBLIC' | 'OPERATOR' | 'ADMIN'
}

export interface CommandWhitelistUpdate {
  category_id?: number
  command?: string
  name?: string
  description?: string
  example?: string
  timeout?: number
  admin_required?: boolean
  is_active?: boolean
  security_level?: 'PUBLIC' | 'OPERATOR' | 'ADMIN'
}

// 命令模板
export interface CommandTemplate {
  category: string
  commands: CommandWhitelist[]
}

export interface CommandTemplateResponse {
  categories: CommandTemplate[]
}

// 命令验证
export interface CommandValidationRequest {
  command: string
}

export interface CommandValidationResponse {
  is_valid: boolean
  message: string
  required_permission?: string
  security_level?: string
  matched_command?: CommandWhitelist
}

// 命令审计日志
export interface CommandAuditLog {
  id: number
  user_id: number
  user_name: string
  terminal_id: number
  terminal_hostname: string
  command_id: number
  command_type: string
  command_content: string
  execution_status: string
  execution_result?: string
  execution_error?: string
  execution_duration?: number
  created_at: string
  ip_address?: string
  user_agent?: string
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
} 