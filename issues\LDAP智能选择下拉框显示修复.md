# LDAP智能选择下拉框显示修复

## 问题描述
用户反馈在LDAP登录页面，虽然系统显示"已为您智能选择'总部AD'配置"，但是下拉框仍然显示"选择LDAP配置"，看起来像是没有选中任何配置。

## 问题分析

### 根本原因
前端页面存在时序和逻辑问题：

1. **页面初始状态问题**：
   - 页面默认为`local`认证模式
   - `loadLdapConfigs()`在页面加载时被调用
   - 但只有在LDAP模式下才应该设置表单的`ldapConfig`值

2. **认证类型切换逻辑问题**：
   - `handleAuthTypeChange`函数会清空表单数据
   - 只有在配置列表为空时才重新加载配置
   - 如果配置已加载但用户切换认证类型，表单被清空但不会重新设置选中值

3. **表单状态不一致**：
   - 智能选择提示显示正确（基于`ldapConfigs`数组）
   - 但表单选择值`loginForm.ldapConfig`未正确设置

## 修复方案

### 1. 优化配置加载逻辑
**文件**: `frontend/src/views/Login.vue`

**修改**: 在`loadLdapConfigs`函数中添加认证模式检查
```typescript
// 只有在LDAP模式下才设置选中的配置
if (authType.value === 'ldap') {
  // 智能选择逻辑...
}
```

### 2. 增强认证类型切换逻辑
**修改**: 在`handleAuthTypeChange`函数中改进配置选择逻辑
```typescript
// 如果切换到LDAP登录，重新设置智能选择的配置
if (type === 'ldap') {
  if (ldapConfigs.value.length === 0) {
    loadLdapConfigs()
  } else {
    // 配置已加载，重新设置智能选择的配置
    const autoSelected = ldapConfigs.value.find((config: LdapConfig) => config.is_auto_selected)
    if (autoSelected) {
      loginForm.value.ldapConfig = autoSelected.id
      console.log('重新选择LDAP配置:', autoSelected.name)
    } else {
      // 使用默认配置
      const defaultConfig = ldapConfigs.value.find((config: LdapConfig) => config.is_default)
      if (defaultConfig) {
        loginForm.value.ldapConfig = defaultConfig.id
      }
    }
  }
}
```

## 修复详情

### 问题场景重现
1. 用户访问登录页面（默认本地登录模式）
2. 页面加载时调用`loadLdapConfigs()`加载配置，显示智能选择提示
3. 用户点击"LDAP登录"标签
4. `handleAuthTypeChange`被调用，表单被清空
5. 由于配置已加载，不会重新调用`loadLdapConfigs()`
6. 结果：智能选择提示正常显示，但下拉框没有选中值

### 修复效果
1. **页面加载时**：只有在LDAP模式下才设置表单选中值
2. **切换到LDAP模式时**：
   - 如果配置未加载，加载配置并设置选中值
   - 如果配置已加载，直接重新设置智能选择的配置
3. **确保状态一致**：智能选择提示和表单选中值保持同步

## 技术实现

### 修改文件
- `frontend/src/views/Login.vue`

### 核心逻辑改进
1. **条件设置**：只在LDAP模式下设置表单配置值
2. **重新选择**：切换模式时重新应用智能选择逻辑
3. **状态同步**：确保UI显示和表单数据一致

### 向后兼容
- 不影响现有的智能选择功能
- 保持原有的默认配置降级机制
- 维持配置加载和显示逻辑

## 测试验证

### 测试场景
1. ✅ 页面首次加载（本地模式）：不设置LDAP配置选中值
2. ✅ 切换到LDAP模式：正确设置智能选择的配置
3. ✅ LDAP模式下刷新页面：直接显示智能选择配置
4. ✅ 多次切换认证模式：每次都正确设置/清空配置
5. ✅ 智能选择提示与下拉框状态一致

### 功能验证
- ✅ 智能选择功能正常工作
- ✅ 默认配置降级机制有效
- ✅ 用户可以手动更改配置选择
- ✅ 登录功能不受影响

## 总结

修复了LDAP登录页面智能选择配置显示不一致的问题。现在当系统显示"已为您智能选择'总部AD'配置"时，下拉框也会正确显示选中状态，确保用户界面的一致性和用户体验。

关键改进：
1. **逻辑优化**：只在合适的时机设置表单配置值
2. **状态同步**：确保智能选择提示和表单选择的一致性
3. **用户体验**：消除用户困惑，提供清晰的配置选择状态 