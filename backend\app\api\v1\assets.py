from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Response
from sqlalchemy.orm import Session
from app.api.deps import get_db, check_permissions
from app.models.asset import Asset
from app.schemas.asset import AssetCreate, AssetUpdate, AssetResponse
from app.crud.asset import asset_crud
from pydantic import BaseModel
from fastapi.responses import StreamingResponse
import pandas as pd
import io
from urllib.parse import quote
from app.crud import asset_change_log as change_log_crud
from app.schemas.asset_change_log import AssetChangeLog
import time
import hashlib
import json
from app.utils.redis_cache import RedisCache

class AssetListResponse(BaseModel):
    data: List[AssetResponse]
    total: int

class ExportRequest(BaseModel):
    format: str
    asset_ids: List[int] | None = None

router = APIRouter()

# 创建Redis缓存实例
asset_cache = RedisCache()

@router.post("/", response_model=AssetResponse)
def create_asset(
    *,
    db: Session = Depends(get_db),
    asset_in: AssetCreate,
    current_user = Depends(check_permissions(["asset:add"]))
) -> AssetResponse:
    """创建固定资产"""
    asset = asset_crud.create(db, obj_in=asset_in)
    
    # 清除资产列表缓存
    asset_cache.clear_pattern("asset:list:*")
    
    return asset

@router.get("/", response_model=AssetListResponse)
def list_assets(
    response: Response,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:view"])),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    keyword: Optional[str] = None,
    search_field: Optional[str] = None,
    company: Optional[str] = None,
    status: Optional[str] = None,
    category: Optional[str] = None,
    asset_number: Optional[str] = None,
    name: Optional[str] = None,
    custodian: Optional[str] = None,
    custodian_department: Optional[str] = None,
    user: Optional[str] = None,
    user_department: Optional[str] = None,
    location: Optional[str] = None,
    purchase_date_start: Optional[str] = None,
    purchase_date_end: Optional[str] = None,
    retirement_date_start: Optional[str] = None,
    retirement_date_end: Optional[str] = None,
    sort_by: Optional[str] = None,
    sort_order: Optional[str] = None,
) -> AssetListResponse:
    """获取固定资产列表"""
    # 构建过滤条件
    filters = {}
    if keyword:
        filters["keyword"] = keyword
    if search_field:
        filters["search_field"] = search_field
    if company:
        filters["company"] = company
    if status:
        filters["status"] = status
    if category:
        filters["category"] = category
    if asset_number:
        filters["asset_number"] = asset_number
    if name:
        filters["name"] = name
    if custodian:
        filters["custodian"] = custodian
    if custodian_department:
        filters["custodian_department"] = custodian_department
    if user:
        filters["user"] = user
    if user_department:
        filters["user_department"] = user_department
    if location:
        filters["location"] = location
    if purchase_date_start:
        filters["purchase_date_start"] = purchase_date_start
    if purchase_date_end:
        filters["purchase_date_end"] = purchase_date_end
    if retirement_date_start:
        filters["retirement_date_start"] = retirement_date_start
    if retirement_date_end:
        filters["retirement_date_end"] = retirement_date_end
    
    # 生成缓存键
    params = {
        "skip": skip,
        "limit": limit,
        "filters": filters,
        "sort_by": sort_by,
        "sort_order": sort_order
    }
    params_hash = hashlib.md5(json.dumps(params, sort_keys=True).encode()).hexdigest()
    cache_key = f"asset:list:{params_hash}"
    
    # 尝试从缓存获取数据
    cached_data = asset_cache.get(cache_key)
    if cached_data:
        response.headers["X-Cache"] = "HIT"
        return cached_data
    
    # 从数据库获取数据
    total = asset_crud.get_count(db, filters=filters)
    items = asset_crud.get_multi(
        db, 
        skip=skip, 
        limit=limit,
        filters=filters,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    result = AssetListResponse(
        data=items,
        total=total
    )
    
    # 存入缓存，使用60秒的TTL
    asset_cache.set(cache_key, result, 60)
    response.headers["X-Cache"] = "MISS"
    
    return result

@router.get("/{id}", response_model=AssetResponse)
def read_asset(
    *,
    db: Session = Depends(get_db),
    id: int,
    current_user = Depends(check_permissions(["asset:view"]))
) -> AssetResponse:
    """获取单个固定资产"""
    # 尝试从缓存获取
    cache_key = f"asset:detail:{id}"
    cached_asset = asset_cache.get(cache_key)
    if cached_asset:
        return cached_asset
    
    asset = asset_crud.get(db, id=id)
    if not asset:
        raise HTTPException(status_code=404, detail="资产不存在")
    
    # 缓存资产详情，使用5分钟的TTL
    asset_cache.set(cache_key, asset, 300)
    return asset

@router.put("/{id}", response_model=AssetResponse)
def update_asset(
    *,
    db: Session = Depends(get_db),
    id: int,
    asset_in: AssetUpdate,
) -> AssetResponse:
    """更新固定资产"""
    asset = asset_crud.get(db, id=id)
    if not asset:
        raise HTTPException(status_code=404, detail="资产不存在")
    
    # 更新资产
    updated_asset = asset_crud.update(db, db_obj=asset, obj_in=asset_in)
    
    # 清除相关缓存
    asset_cache.delete(f"asset:detail:{id}")
    asset_cache.clear_pattern("asset:list:*")
    
    return updated_asset

@router.delete("/{id}", response_model=AssetResponse)
def delete_asset(
    *,
    db: Session = Depends(get_db),
    id: int,
) -> AssetResponse:
    """删除固定资产"""
    asset = asset_crud.get(db, id=id)
    if not asset:
        raise HTTPException(status_code=404, detail="资产不存在")
    asset = asset_crud.remove(db, id=id)
    
    # 清除相关缓存
    asset_cache.delete(f"asset:detail:{id}")
    asset_cache.clear_pattern("asset:list:*")
    
    return asset

@router.post("/import")
async def import_assets(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """批量导入资产"""
    if not file.filename.endswith(('.csv', '.xlsx')):
        raise HTTPException(status_code=400, detail="只支持CSV和XLSX格式文件")
    
    try:
        contents = await file.read()
        print(f"Reading file: {file.filename}")
        
        if file.filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(contents))
        else:
            df = pd.read_excel(io.BytesIO(contents))
        
        print(f"Original columns: {df.columns.tolist()}")
        
        # 字段映射（中文转英文）
        field_mapping = {
            '公司名称(*)': 'company',
            '公司名称': 'company',
            '资产名称(*)': 'name',
            '资产名称': 'name',
            '资产编号(*)': 'asset_number',
            '资产编号': 'asset_number',
            '资产状态(*)': 'status',
            '资产状态': 'status',
            '资产类别': 'category',
            '规格型号(*)': 'specification',
            '规格型号': 'specification',
            '入账日期(*)': 'purchase_date',
            '入账日期': 'purchase_date',
            '销账日期': 'retirement_date',
            '领用人(*)': 'custodian',
            '领用人': 'custodian',
            '领用人工号': 'custodian_job_number',
            '使用人(*)': 'user',
            '使用人': 'user',
            '使用人工号': 'user_job_number',
            '验收人(*)': 'inspector',
            '验收人': 'inspector',
            '验收人工号': 'inspector_job_number',
            '采购人': 'purchaser',
            '采购人工号': 'purchaser_job_number',
            '生产编号': 'production_number',
            '价格': 'price',
            '供应商': 'supplier',
            '制造商': 'manufacturer',
            '存放位置': 'location',
            '备注': 'remarks'
        }
        
        # 重命名列
        df = df.rename(columns=field_mapping)
        print(f"Renamed columns: {df.columns.tolist()}")
        
        # 创建字段名中英文反向映射表，用于错误提示
        reverse_mapping = {v: k.replace('(*)', '') for k, v in field_mapping.items() if not k.endswith('(*)')}
        
        # 验证必填字段
        required_fields = ['company', 'name', 'asset_number', 'status', 'custodian', 'user', 'inspector', 'purchase_date']
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            # 将字段名转换回中文进行提示
            missing_fields_zh = [reverse_mapping.get(field, field) for field in missing_fields]
            error_msg = f"缺少必填字段: {', '.join(missing_fields_zh)}"
            print(f"Error: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 检查是否有空值
        for field in required_fields:
            empty_rows = df[df[field].isna()].index + 2
            if not empty_rows.empty:
                error_msg = f"第 {list(empty_rows)} 行的{reverse_mapping.get(field, field)}不能为空"
                print(f"Error: {error_msg}")
                raise HTTPException(status_code=400, detail=error_msg)
        
        # 验证资产状态不为空（状态值可以自定义）
        # 不再限制特定的状态值，允许用户自定义资产状态
        
        # 验证日期格式
        try:
            print("Validating dates...")
            # 处理入账日期（必填字段）
            df['purchase_date'] = pd.to_datetime(df['purchase_date'], format='%Y-%m-%d', errors='coerce')
            invalid_dates = df[df['purchase_date'].isna()].index + 2
            if not invalid_dates.empty:
                error_msg = f"第 {list(invalid_dates)} 行的入账日期格式错误，请使用YYYY-MM-DD格式"
                print(f"Error: {error_msg}")
                raise HTTPException(status_code=400, detail=error_msg)
            df['purchase_date'] = df['purchase_date'].dt.strftime('%Y-%m-%d')

            # 处理销账日期（选填字段）
            if 'retirement_date' in df.columns:
                print("Processing retirement dates...")
                # 先将空值和无效值替换为 NaT
                df['retirement_date'] = pd.to_datetime(df['retirement_date'], format='%Y-%m-%d', errors='coerce')
                # 检查非空但格式错误的日期
                invalid_retirement_dates = df[df['retirement_date'].isna() & df['retirement_date'].notna()].index + 2
                if not invalid_retirement_dates.empty:
                    error_msg = f"第 {list(invalid_retirement_dates)} 行的销账日期格式错误，请使用YYYY-MM-DD格式"
                    print(f"Error: {error_msg}")
                    raise HTTPException(status_code=400, detail=error_msg)
                # 只转换有效的日期
                mask = df['retirement_date'].notna()
                if mask.any():
                    df.loc[mask, 'retirement_date'] = df.loc[mask, 'retirement_date'].dt.strftime('%Y-%m-%d')
                # 将 NaT 替换为空字符串
                df['retirement_date'] = df['retirement_date'].fillna('')

        except HTTPException as he:
            raise he
        except Exception as e:
            print(f"Date processing error: {str(e)}")
            raise HTTPException(status_code=400, detail="日期格式错误，请使用YYYY-MM-DD格式，例如：2024-01-01")
        
        # 验证资产编号唯一性
        duplicate_numbers = df[df['asset_number'].duplicated()]['asset_number'].unique()
        if len(duplicate_numbers) > 0:
            error_msg = f"以下资产编号重复: {', '.join(duplicate_numbers)}"
            print(f"Error: {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 检查数据库中是否已存在相同的资产编号
        for asset_number in df['asset_number'].unique():
            existing_asset = db.query(Asset).filter(Asset.asset_number == asset_number).first()
            if existing_asset:
                error_msg = f"资产编号 {asset_number} 已存在"
                print(f"Error: {error_msg}")
                raise HTTPException(status_code=400, detail=error_msg)
        
        print("Starting to create assets...")
        # 转换为字典列表并创建资产
        success_count = 0
        error_messages = []
        
        for index, row in df.iterrows():
            try:
                asset_data = row.to_dict()
                # 清理数据：移除 NaN 值
                asset_data = {k: ('' if pd.isna(v) else v) for k, v in asset_data.items()}
                print(f"Creating asset {index + 1}: {asset_data}")
                # 使用 AssetCreate 模型创建资产
                asset_create = AssetCreate(
                    company=asset_data['company'],
                    name=asset_data['name'],
                    asset_number=asset_data['asset_number'],
                    status=asset_data['status'],
                    category=asset_data.get('category', None),
                    specification=asset_data.get('specification', None),
                    purchase_date=asset_data['purchase_date'],
                    retirement_date=asset_data.get('retirement_date', None) if asset_data.get('retirement_date', '') else None,
                    custodian=asset_data['custodian'],
                    custodian_job_number=asset_data.get('custodian_job_number', None),
                    user=asset_data['user'],
                    user_job_number=asset_data.get('user_job_number', None),
                    inspector=asset_data['inspector'],
                    inspector_job_number=asset_data.get('inspector_job_number', None),
                    purchaser=asset_data.get('purchaser', None),
                    purchaser_job_number=asset_data.get('purchaser_job_number', None),
                    production_number=asset_data.get('production_number', None),
                    price=asset_data.get('price', None),
                    supplier=asset_data.get('supplier', None),
                    manufacturer=asset_data.get('manufacturer', None),
                    location=asset_data.get('location', None),
                    remarks=asset_data.get('remarks', None) if asset_data.get('remarks', '') else None
                )
                asset_crud.create(db, obj_in=asset_create)
                success_count += 1
            except Exception as e:
                error_msg = f"第 {index + 2} 行导入失败: {str(e)}"
                print(f"Error: {error_msg}")
                error_messages.append(error_msg)
        
        if error_messages:
            if success_count == 0:
                raise HTTPException(status_code=400, detail="\n".join(error_messages))
            else:
                return {
                    "message": f"部分导入成功，成功导入 {success_count} 条记录",
                    "errors": error_messages
                }
        
        return {"message": f"成功导入 {success_count} 条资产记录"}
        
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Import error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"导入失败: {str(e)}")

@router.post("/export")
async def export_assets(
    request: ExportRequest,
    db: Session = Depends(get_db)
):
    """批量导出资产"""
    try:
        # 验证导出格式
        if request.format not in ('csv', 'xlsx'):
            raise HTTPException(status_code=400, detail="格式只能是 csv 或 xlsx")
        
        # 获取指定的资产记录
        if request.asset_ids:
            assets = []
            for id in request.asset_ids:
                asset = asset_crud.get(db, id=id)
                if asset:
                    assets.append(asset)
                else:
                    print(f"Warning: Asset with id {id} not found")
            if not assets:
                raise HTTPException(status_code=400, detail="未找到指定的资产记录")
        else:
            assets = asset_crud.get_multi(db)
            if not assets:
                raise HTTPException(status_code=400, detail="没有可导出的资产记录")
        
        print(f"Found {len(assets)} assets to export")
        
        # 定义字段映射
        field_mapping = {
            'company': '公司名称',
            'name': '资产名称',
            'asset_number': '资产编号',
            'status': '资产状态',
            'category': '资产类别',
            'specification': '规格型号',
            'purchase_date': '入账日期',
            'retirement_date': '销账日期',
            'custodian': '领用人',
            'custodian_job_number': '领用人工号',
            'user': '使用人',
            'user_job_number': '使用人工号',
            'inspector': '验收人',
            'inspector_job_number': '验收人工号',
            'purchaser': '采购人',
            'purchaser_job_number': '采购人工号',
            'production_number': '生产编号',
            'price': '价格',
            'supplier': '供应商',
            'manufacturer': '制造商',
            'location': '存放位置',
            'remarks': '备注'
        }
        
        # 转换为DataFrame
        data = []
        for asset in assets:
            data.append({
                'company': asset.company,
                'name': asset.name,
                'asset_number': asset.asset_number,
                'status': asset.status,
                'category': asset.category or '',
                'specification': asset.specification,
                'purchase_date': asset.purchase_date,
                'retirement_date': asset.retirement_date or '',
                'custodian': asset.custodian,
                'custodian_job_number': asset.custodian_job_number or '',
                'user': asset.user,
                'user_job_number': asset.user_job_number or '',
                'inspector': asset.inspector,
                'inspector_job_number': asset.inspector_job_number or '',
                'purchaser': asset.purchaser or '',
                'purchaser_job_number': asset.purchaser_job_number or '',
                'production_number': asset.production_number or '',
                'price': asset.price or '',
                'supplier': asset.supplier or '',
                'manufacturer': asset.manufacturer or '',
                'location': asset.location or '',
                'remarks': asset.remarks or ''
            })
        
        df = pd.DataFrame(data)
        print(f"Created DataFrame with {len(df)} rows")
        
        # 重命名列名为中文
        df = df.rename(columns=field_mapping)
        
        # 创建内存文件对象
        output = io.BytesIO()
        
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'asset_list_{timestamp}.{request.format}'  # 使用英文文件名
            filename_display = f'资产清单_{timestamp}.{request.format}'  # 显示用的中文文件名
            
            # 根据格式导出
            if request.format == 'csv':
                df.to_csv(output, index=False, encoding='utf-8-sig')
                media_type = 'text/csv'
            else:
                # 创建Excel写入器，设置样式
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    # 设置默认行高和列宽
                    df.to_excel(writer, index=False, sheet_name='资产清单')
                    
                    # 获取工作表
                    worksheet = writer.sheets['资产清单']
                    
                    # 设置默认行高
                    worksheet.sheet_format.defaultRowHeight = 20
                    
                    # 设置列宽（根据内容自适应，但设置最小和最大宽度）
                    column_widths = {
                        'A': 20,  # 公司名称
                        'B': 25,  # 资产名称
                        'C': 15,  # 资产编号
                        'D': 12,  # 资产状态
                        'E': 15,  # 规格型号
                        'F': 12,  # 入账日期
                        'G': 12,  # 销账日期
                        'H': 10,  # 领用人
                        'I': 10,  # 使用人
                        'J': 10,  # 验收人
                        'K': 20,  # 备注
                    }
                    
                    # 应用列宽
                    for col, width in column_widths.items():
                        worksheet.column_dimensions[col].width = width
                    
                    # 设置表头样式
                    from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
                    header_font = Font(name='微软雅黑', size=11, bold=True)
                    header_fill = PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
                    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                    thin_border = Border(
                        left=Side(style='thin', color='D9D9D9'),
                        right=Side(style='thin', color='D9D9D9'),
                        top=Side(style='thin', color='D9D9D9'),
                        bottom=Side(style='thin', color='D9D9D9')
                    )
                    
                    # 应用表头样式
                    for cell in worksheet[1]:
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment
                        cell.border = thin_border
                    
                    # 设置数据行样式
                    data_font = Font(name='微软雅黑', size=10)
                    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                    
                    # 设置数据行样式并处理不同类型的单元格对齐方式
                    for row in worksheet.iter_rows(min_row=2):
                        for cell in row:
                            cell.font = data_font
                            cell.border = thin_border
                            
                            # 根据列设置不同的对齐方式
                            if cell.column_letter in ['C']:  # 资产编号居中对齐
                                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                            elif cell.column_letter in ['F', 'G']:  # 日期居中对齐
                                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                            elif cell.column_letter in ['D']:  # 状态居中对齐
                                cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                            else:  # 其他列左对齐
                                cell.alignment = data_alignment
                    
                    # 设置行高
                    worksheet.row_dimensions[1].height = 30  # 表头行高
                    for i in range(2, worksheet.max_row + 1):
                        worksheet.row_dimensions[i].height = 25  # 数据行高
                    
                    # 设置打印相关属性
                    worksheet.print_area = worksheet.dimensions
                    worksheet.page_setup.fitToWidth = 1
                    worksheet.page_setup.fitToHeight = False
                    worksheet.page_margins.left = 0.5
                    worksheet.page_margins.right = 0.5
                    worksheet.page_margins.top = 0.5
                    worksheet.page_margins.bottom = 0.5
                    worksheet.page_margins.header = 0.3
                    worksheet.page_margins.footer = 0.3
                
                media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            print(f"Successfully created {request.format} file")
            output.seek(0)
            
            # 使用 urllib.parse.quote 对中文文件名进行编码
            encoded_filename = quote(filename_display)
            
            return StreamingResponse(
                output,
                media_type=media_type,
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"; filename*=utf-8\'\'{encoded_filename}',
                    'Access-Control-Expose-Headers': 'Content-Disposition'
                }
            )
            
        except Exception as e:
            print(f"Error while creating file: {str(e)}")
            raise HTTPException(status_code=400, detail=f"创建{request.format}文件失败: {str(e)}")
        
    except HTTPException as he:
        raise he
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"导出失败: {str(e)}")

@router.get("/import/template")
async def get_import_template(
    format: str = Query(..., regex="^(csv|xlsx)$"),
    db: Session = Depends(get_db)
):
    """获取导入模板"""
    try:
        # 创建示例数据
        example_data = [{
            'company': '示例公司',
            'name': '笔记本电脑',
            'asset_number': 'GD12002400',
            'status': '使用中',
            'category': '计算机设备',
            'specification': 'ThinkPad X1',
            'purchase_date': '2024-01-01',
            'retirement_date': '',  # 选填
            'custodian': '张三',
            'custodian_job_number': 'GD001',
            'user': '李四',
            'user_job_number': 'GD002',
            'inspector': '王五',
            'inspector_job_number': 'GD003',
            'purchaser': '赵六',
            'purchaser_job_number': 'GD004',
            'production_number': 'PC20240001',
            'price': '8000.00',
            'supplier': '联想集团',
            'manufacturer': '联想',
            'location': '研发部',
            'remarks': '开发部门使用'  # 选填
        }]
        
        # 创建 DataFrame
        df = pd.DataFrame(example_data)
        
        # 定义字段映射
        field_mapping = {
            'company': '公司名称(*)',
            'name': '资产名称(*)',
            'asset_number': '资产编号(*)',
            'status': '资产状态(*)',
            'category': '资产类别',
            'specification': '规格型号',
            'purchase_date': '入账日期(*)',
            'retirement_date': '销账日期',
            'custodian': '领用人(*)',
            'custodian_job_number': '领用人工号',
            'user': '使用人(*)',
            'user_job_number': '使用人工号',
            'inspector': '验收人(*)',
            'inspector_job_number': '验收人工号',
            'purchaser': '采购人',
            'purchaser_job_number': '采购人工号',
            'production_number': '生产编号',
            'price': '价格',
            'supplier': '供应商',
            'manufacturer': '制造商',
            'location': '存放位置',
            'remarks': '备注'
        }
        
        # 重命名列名为中文
        df = df.rename(columns=field_mapping)
        
        # 创建内存文件对象
        output = io.BytesIO()
        
        # 根据格式导出
        if format == 'csv':
            df.to_csv(output, index=False, encoding='utf-8-sig')
            media_type = 'text/csv'
            filename = 'asset_import_template.csv'
            filename_display = '资产导入模板.csv'
        else:
            # 创建Excel写入器，设置样式
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='导入模板')
                
                # 获取工作表
                worksheet = writer.sheets['导入模板']
                
                # 设置列宽
                column_widths = {
                    'A': 20,  # 公司名称
                    'B': 25,  # 资产名称
                    'C': 15,  # 资产编号
                    'D': 12,  # 资产状态
                    'E': 15,  # 规格型号
                    'F': 12,  # 入账日期
                    'G': 12,  # 销账日期
                    'H': 10,  # 领用人
                    'I': 10,  # 使用人
                    'J': 10,  # 验收人
                    'K': 20,  # 备注
                }
                
                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width
                
                # 设置样式
                from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
                header_font = Font(name='微软雅黑', size=11, bold=True)
                header_fill = PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                thin_border = Border(
                    left=Side(style='thin', color='D9D9D9'),
                    right=Side(style='thin', color='D9D9D9'),
                    top=Side(style='thin', color='D9D9D9'),
                    bottom=Side(style='thin', color='D9D9D9')
                )
                
                # 应用表头样式
                for cell in worksheet[1]:
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = thin_border
                
                # 设置数据行样式
                data_font = Font(name='微软雅黑', size=10)
                data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                
                for row in worksheet.iter_rows(min_row=2):
                    for cell in row:
                        cell.font = data_font
                        cell.border = thin_border
                        if cell.column_letter in ['C', 'D', 'F', 'G']:
                            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                        else:
                            cell.alignment = data_alignment
                
                # 设置行高
                worksheet.row_dimensions[1].height = 30
                for i in range(2, worksheet.max_row + 1):
                    worksheet.row_dimensions[i].height = 25
                
                # 添加说明sheet
                instruction_sheet = writer.book.create_sheet('填写说明', 0)
                
                # 设置说明内容
                instructions = [
                    ['资产导入说明'],
                    [''],
                    ['1. 带(*)的字段为必填项'],
                    ['2. 日期格式：YYYY-MM-DD，例如：2024-01-01'],
                    ['3. 资产状态：可自定义状态值，如使用中、闲置、维修中、已报废等'],
                    ['4. 资产编号必须唯一'],
                    ['5. 请勿修改表头名称'],
                    ['6. 支持的文件格式：Excel(.xlsx)或CSV(.csv)'],
                    [''],
                    ['字段说明：'],
                    ['公司名称：资产所属公司'],
                    ['资产名称：资产的名称'],
                    ['资产编号：唯一标识符'],
                    ['资产状态：当前使用状态'],
                    ['资产类别：设备分类（选填）'],
                    ['规格型号：资产的具体型号'],
                    ['入账日期：资产入账时间'],
                    ['销账日期：资产报废时间（选填）'],
                    ['领用人：资产领用人'],
                    ['领用人工号：领用人员工号（选填）'],
                    ['使用人：资产实际使用人'],
                    ['使用人工号：使用人员工号（选填）'],
                    ['验收人：资产验收人'],
                    ['验收人工号：验收人员工号（选填）'],
                    ['采购人：资产采购人（选填）'],
                    ['采购人工号：采购人员工号（选填）'],
                    ['生产编号：厂商生产编号（选填）'],
                    ['价格：资产采购价格（选填）'],
                    ['供应商：资产供应商（选填）'],
                    ['制造商：资产制造商（选填）'],
                    ['存放位置：资产存放地点（选填）'],
                    ['备注：其他补充信息（选填）']
                ]
                
                # 写入说明内容
                for row_idx, row_data in enumerate(instructions, 1):
                    for col_idx, value in enumerate(row_data, 1):
                        cell = instruction_sheet.cell(row=row_idx, column=col_idx, value=value)
                        if row_idx == 1:
                            cell.font = Font(name='微软雅黑', size=14, bold=True)
                        else:
                            cell.font = Font(name='微软雅黑', size=11)
                
                # 设置说明sheet的列宽
                instruction_sheet.column_dimensions['A'].width = 50
                
                # 设置说明sheet的行高
                instruction_sheet.row_dimensions[1].height = 30
                for i in range(2, len(instructions) + 1):
                    instruction_sheet.row_dimensions[i].height = 20
            
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            filename = 'asset_import_template.xlsx'
            filename_display = '资产导入模板.xlsx'
        
        output.seek(0)
        
        # 使用 urllib.parse.quote 对中文文件名进行编码
        encoded_filename = quote(filename_display)
        
        return StreamingResponse(
            output,
            media_type=media_type,
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"; filename*=utf-8\'\'{encoded_filename}',
                'Access-Control-Expose-Headers': 'Content-Disposition'
            }
        )
        
    except Exception as e:
        print(f"Error creating template: {str(e)}")
        raise HTTPException(status_code=400, detail=f"创建模板失败: {str(e)}")

@router.get("/{asset_id}/change-logs", response_model=List[AssetChangeLog])
def get_asset_change_logs(
    asset_id: int,
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100)
) -> List[AssetChangeLog]:
    """获取资产变更记录"""
    # 检查资产是否存在
    asset = asset_crud.get(db, id=asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    
    return change_log_crud.get_asset_change_logs(
        db,
        asset_id=asset_id,
        skip=skip,
        limit=limit
    )
