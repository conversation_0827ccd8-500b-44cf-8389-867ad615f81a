#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Agent状态检查工具
用于检查Agent的运行状态
"""

import os
import sys
import json
import time
import argparse
import socket
import logging
import requests
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

def check_process_running(process_name="terminal_agent"):
    """
    检查Agent进程是否在运行
    
    Args:
        process_name (str): 进程名
        
    Returns:
        bool: 进程是否在运行
    """
    import psutil
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def send_status_command(agent_port=50050):
    """
    向Agent发送状态查询命令
    
    Args:
        agent_port (int): Agent通知服务的端口
        
    Returns:
        dict: 状态信息
    """
    try:
        # 构建请求URL
        url = f"http://localhost:{agent_port}/status"
        
        # 发送GET请求
        response = requests.get(url, timeout=5)
        
        # 检查响应
        if response.status_code == 200:
            return response.json()
        else:
            return {"error": f"请求失败，状态码: {response.status_code}"}
    except requests.RequestException as e:
        return {"error": f"请求异常: {str(e)}"}
    except Exception as e:
        return {"error": f"未知错误: {str(e)}"}

def check_service_status():
    """
    检查Agent服务状态
    """
    import subprocess
    try:
        if sys.platform == 'win32':
            cmd = "sc query TerminalAgent"
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            output, error = process.communicate()
            output = output.decode('gbk', errors='ignore')
            
            if "RUNNING" in output:
                return "运行中"
            elif "STOPPED" in output:
                return "已停止"
            else:
                return "未知"
        else:
            return "不支持的操作系统"
    except Exception as e:
        return f"检查服务状态出错: {str(e)}"

def format_status(status):
    """
    格式化状态信息
    
    Args:
        status (dict): 状态信息
        
    Returns:
        str: 格式化后的状态信息
    """
    if "error" in status:
        return f"获取状态信息失败: {status['error']}"
    
    lines = []
    lines.append("=" * 50)
    lines.append("终端Agent状态信息")
    lines.append("=" * 50)
    
    lines.append(f"运行状态: {'运行中' if status.get('agent_running', False) else '已停止'}")
    lines.append(f"终端ID: {status.get('terminal_id', '未分配')}")
    lines.append(f"服务器地址: {status.get('server_address', '未配置')}")
    lines.append(f"连接状态: {status.get('connection_state', '未知')}")
    lines.append(f"运行时间: {status.get('uptime_formatted', '未知')}")
    lines.append(f"最后心跳时间: {status.get('last_heartbeat_time', '未知')}")
    lines.append(f"心跳失败次数: {status.get('heartbeat_failures', 0)}")
    lines.append(f"最后信息采集时间: {status.get('last_collection_time', '未知')}")
    
    # 线程状态
    lines.append("\n线程状态:")
    threads = status.get('threads', {})
    if threads:
        for name, info in threads.items():
            state = "活动" if info.get('alive', False) else "停止"
            lines.append(f"  {name}: {state} ({info.get('name', '未知')})")
    else:
        lines.append("  无线程信息")
    
    # 配置信息
    config = status.get('config', {})
    lines.append("\n配置信息:")
    lines.append(f"  心跳间隔: {config.get('heartbeat_interval', '未知')}秒")
    lines.append(f"  信息采集间隔: {config.get('collection_interval', '未知')}秒")
    lines.append(f"  Agent版本: {config.get('agent_version', '未知')}")
    lines.append(f"  使用TLS: {'是' if config.get('use_tls', False) else '否'}")
    
    lines.append("=" * 50)
    return "\n".join(lines)

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='Agent状态检查工具')
    parser.add_argument('--port', type=int, default=50050, help='Agent通知服务端口')
    parser.add_argument('--format', choices=['text', 'json'], default='text', help='输出格式')
    args = parser.parse_args()
    
    # 检查Agent进程是否在运行
    process_running = check_process_running()
    service_status = check_service_status() if sys.platform == 'win32' else "不适用"
    
    if process_running:
        logger.info("检测到Agent进程正在运行")
        # 发送状态查询命令
        status = send_status_command(args.port)
    else:
        logger.warning("未检测到Agent进程")
        status = {"error": "Agent进程未运行"}
    
    # 添加进程和服务状态
    status["process_running"] = process_running
    status["service_status"] = service_status
    
    # 输出状态信息
    if args.format == 'json':
        print(json.dumps(status, indent=2, ensure_ascii=False))
    else:
        print(format_status(status))
    
    # 返回退出码
    if "error" in status:
        return 1
    return 0

if __name__ == "__main__":
    sys.exit(main()) 