<template>
  <div class="mobile-file-uploader">
    <!-- 文件列表预览 -->
    <div v-if="fileList.length > 0" class="file-preview-list">
      <div 
        v-for="(file, index) in fileList" 
        :key="index"
        class="file-preview-item"
      >
        <!-- 预览区域（可点击预览） -->
        <div class="preview-image" @click="onPreview(file, index)">
          <img 
            v-if="file.isImage && getPreviewUrl(file)"
            :src="getPreviewUrl(file)" 
            alt="预览图片"
          />
          <van-icon 
            v-else 
            name="description" 
            size="24"
            color="#969799"
          />
        </div>
        
        <!-- 选择框（仅在ready状态显示） -->
        <div 
          v-if="file.status === 'ready'" 
          class="file-checkbox"
          @click.stop="toggleFileSelection(index)"
        >
          <van-icon 
            :name="file.selected ? 'success' : 'circle'" 
            :color="file.selected ? '#1989fa' : '#c8c9cc'"
            size="20"
          />
        </div>
        
        <!-- 状态指示器 -->
        <div v-if="file.status === 'uploading'" class="status-indicator uploading">
          <van-loading size="16" color="#1989fa" />
        </div>
        <div 
          v-else-if="file.status === 'failed'" 
          class="status-indicator failed"
          @click.stop="retryUpload(file, index)"
        >
          <van-icon name="replay" size="16" color="#fff" />
        </div>
        <div v-else-if="file.status === 'done'" class="status-indicator done">
          <van-icon name="success" size="16" color="#fff" />
        </div>
        
        <!-- 删除按钮 -->
        <div class="delete-btn" @click.stop="deleteFile(index)">
          <van-icon name="cross" size="12" color="#fff" />
        </div>
        
        <!-- 上传进度遮罩（仅在上传中显示） -->
        <div v-if="file.status === 'uploading'" class="upload-overlay">
          <van-loading size="16" color="#1989fa" />
          <span>上传中...</span>
        </div>
      </div>
    </div>
    
    <!-- 批量上传按钮 -->
    <div v-if="hasReadyFiles" class="batch-upload-actions">
      <van-button 
        type="primary" 
        size="small" 
        block
        :loading="isUploading"
        @click="uploadAllFiles"
      >
        <van-icon name="arrow-up" />
        上传全部文件 ({{ readyFilesCount }})
      </van-button>
    </div>

    <!-- 上传操作区域 -->
    <div v-if="fileList.length < maxCount" class="upload-actions">
      <div class="upload-btn-group">
        <!-- 相册选择 -->
        <div class="upload-btn" @click="openGallery">
          <van-icon name="photo-o" size="20" color="#1989fa" />
          <span>相册</span>
        </div>
        
        <!-- 拍照 -->
        <div v-if="showCameraButton && isImageAccept" class="upload-btn" @click="openCamera">
          <van-icon name="photograph" size="20" color="#1989fa" />
          <span>拍照</span>
        </div>
      </div>
    </div>
    
    <!-- 隐藏的文件输入 -->
    <input
      ref="galleryInputRef"
      type="file"
      :accept="accept"
      :multiple="multiple"
      style="display: none"
      @change="handleGallerySelect"
    />
    
    <!-- 隐藏的相机输入 -->
    <input
      ref="cameraInputRef"
      type="file"
      accept="image/*"
      capture="environment"
      style="display: none"
      @change="handleCameraCapture"
    />
    
    <!-- 上传提示信息 -->
    <div v-if="showTips && tips" class="upload-tips">
      <van-icon name="info-o" size="12" />
      <span>{{ tips }}</span>
    </div>
    
    <!-- 图片预览弹窗 -->
    <van-image-preview
      v-model:show="previewVisible"
      :images="previewImages"
      :start-position="previewIndex"
      @change="onPreviewChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { customFieldApi } from '@/api/custom_field'
import { API_BASE_URL } from '@/config/api'
import type { ExtendedFileListItem } from '@/types/vant-extend'

interface Props {
  modelValue?: string | string[]
  accept?: string
  maxSize?: number
  maxCount?: number
  multiple?: boolean
  disabled?: boolean
  showTips?: boolean
  uploadText?: string
  uploadIcon?: string
  showCameraButton?: boolean
  // 实体关联信息（移动端支持）
  fieldId?: number
  appliesTo?: string
  entityId?: number | null
  mode?: 'create' | 'edit'
  taskId?: number
  assetId?: number
}

interface Emits {
  (e: 'update:modelValue', value: string | string[]): void
  (e: 'upload-success', file: any): void
  (e: 'upload-error', error: any): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: 'image/*',
  maxSize: 5 * 1024 * 1024, // 5MB
  maxCount: 9,
  multiple: false,
  disabled: false,
  showTips: true,
  uploadText: '上传图片',
  uploadIcon: 'photograph',
  showCameraButton: true
})

const emit = defineEmits<Emits>()

// 文件列表
const fileList = ref<ExtendedFileListItem[]>([])

// 预览相关
const previewVisible = ref(false)
const previewIndex = ref(0)

// 相机相关
const cameraInputRef = ref<HTMLInputElement>()
const galleryInputRef = ref<HTMLInputElement>()

// 计算是否为图片类型
const isImageAccept = computed(() => {
  return props.accept.includes('image') || props.accept === '*'
})

// 计算ready状态的文件
const hasReadyFiles = computed(() => {
  return fileList.value.some(file => file.status === 'ready' && file.selected)
})

const readyFilesCount = computed(() => {
  return fileList.value.filter(file => file.status === 'ready' && file.selected).length
})

const isUploading = computed(() => {
  return fileList.value.some(file => file.status === 'uploading')
})

// 计算属性
const tips = computed(() => {
  const sizeText = formatFileSize(props.maxSize)
  const typeText = formatAcceptText(props.accept)
  const countText = props.multiple ? `最多${props.maxCount}张` : '单张'
  
  return `支持${typeText}，${countText}，大小不超过${sizeText}`
})

const previewImages = computed(() => {
  return fileList.value
    .filter(file => file.isImage && getPreviewUrl(file))
    .map(file => getPreviewUrl(file))
})

// 工具函数（提前定义）
const getFileNameFromUrl = (url: string): string => {
  return url.split('/').pop() || 'unknown'
}

const isImageFile = (url: string): boolean => {
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const extension = url.toLowerCase().split('.').pop() || ''
  return imageExts.includes('.' + extension)
}

const getContentType = (url: string): string => {
  const extension = url.toLowerCase().split('.').pop() || ''
  const typeMap: Record<string, string> = {
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    bmp: 'image/bmp',
    webp: 'image/webp'
  }
  return typeMap[extension] || 'application/octet-stream'
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    fileList.value = []
    return
  }

  try {
    let files: any[] = []
    
    if (typeof newValue === 'string') {
      if (newValue.startsWith('[') || newValue.startsWith('{')) {
        // JSON格式
        files = JSON.parse(newValue)
        if (!Array.isArray(files)) {
          files = [files]
        }
      } else {
        // 单个URL
        files = [{ url: newValue, filename: getFileNameFromUrl(newValue) }]
      }
    } else if (Array.isArray(newValue)) {
      files = newValue
    }

    fileList.value = files.map((file, index) => ({
      url: file.url || file.path || '',
      name: file.filename || file.name || `文件${index + 1}`,
      isImage: isImageFile(file.url || file.path || ''),
      status: 'done'
    }))
  } catch (error) {
    console.error('解析文件数据失败:', error)
    fileList.value = []
  }
}, { immediate: true })

// 更新外部值的函数
const updateModelValue = () => {
  const validFiles = fileList.value.filter(file => file.status === 'done' && file.url)
  
  if (validFiles.length === 0) {
    emit('update:modelValue', props.multiple ? [] : '')
    return
  }

  const fileData = validFiles.map(file => ({
    filename: file.name,
    url: file.url,
    size: file.size || 0,
    content_type: getContentType(file.url || '')
  }))

  if (props.multiple) {
    emit('update:modelValue', JSON.stringify(fileData))
  } else {
    emit('update:modelValue', fileData[0]?.url || '')
  }
}

// 上传前验证
const beforeUpload = (file: File): boolean => {
  // 检查文件类型
  if (props.accept !== '*' && !isAcceptedFileType(file, props.accept)) {
    showToast(`请选择${formatAcceptText(props.accept)}格式的文件`)
    return false
  }

  // 检查文件大小
  if (file.size > props.maxSize) {
    showToast(`文件大小不能超过${formatFileSize(props.maxSize)}`)
    return false
  }

  return true
}

// 获取预览URL
const getPreviewUrl = (file: ExtendedFileListItem): string => {
  // 优先使用本地预览URL（上传中时）
  if (file.content) {
    return file.content
  }
  
  // 其次使用服务器URL（上传完成后）
  if (file.url) {
    // 如果是相对路径，需要拼接完整URL
    if (file.url.startsWith('/')) {
      const baseUrl = API_BASE_URL.replace('/api/v1', '')
      return baseUrl + file.url
    }
    return file.url
  }
  
  return ''
}

// 预览图片
const onPreview = (file: ExtendedFileListItem, index: number) => {
  const previewUrl = getPreviewUrl(file)
  if (file.isImage && previewUrl) {
    // 计算当前图片在所有图片中的索引
    const imageFiles = fileList.value.filter(f => f.isImage && getPreviewUrl(f))
    const imageIndex = imageFiles.findIndex(f => f === file)
    
    previewIndex.value = Math.max(0, imageIndex)
    previewVisible.value = true
  }
}

// 预览切换
const onPreviewChange = (index: number) => {
  previewIndex.value = index
}

// 打开相册
const openGallery = () => {
  if (galleryInputRef.value) {
    galleryInputRef.value.click()
  }
}

// 打开相机
const openCamera = () => {
  if (cameraInputRef.value) {
    cameraInputRef.value.click()
  }
}

// 删除文件
const deleteFile = async (index: number) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个文件吗？'
    })
    
    const file = fileList.value[index]
    // 清理本地预览URL
    if (file.content) {
      URL.revokeObjectURL(file.content)
    }
    
    fileList.value.splice(index, 1)
  } catch {
    // 用户取消
  }
}

// 重试上传
const retryUpload = async (file: ExtendedFileListItem, index: number) => {
  if (!file.file) return
  
  // 重置状态
  file.status = 'uploading'
  file.message = '上传中...'
  
  try {
    const response = await customFieldApi.uploadFile(file.file)
    const uploadResult = response.data || response
    
    // 更新文件信息
    file.status = 'done'
    file.url = uploadResult.url
    if (file.file) {
      file.name = uploadResult.filename
    }
    file.message = ''
    
    emit('upload-success', uploadResult)
    showToast('上传成功')
    
  } catch (error) {
    console.error('重试上传失败:', error)
    file.status = 'failed'
    file.message = '上传失败'
    
    emit('upload-error', error)
    showToast('重试失败，请检查网络连接')
  }
}

// 处理相册选择
const handleGallerySelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  await handleFileSelect(Array.from(files))  // 只选择不上传
  target.value = '' // 清空input值
}

// 处理相机拍照
const handleCameraCapture = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  await handleFileSelect([file])  // 只选择不上传
  target.value = '' // 清空input值
}

// 切换文件选择状态
const toggleFileSelection = (index: number) => {
  const file = fileList.value[index]
  if (file.status === 'ready') {
    file.selected = !file.selected
  }
}

// 统一文件选择处理（只选择文件，不自动上传）
const handleFileSelect = async (files: File[]) => {
  for (const file of files) {
    // 验证文件
    if (!beforeUpload(file)) {
      continue
    }
    
    // 检查数量限制
    if (fileList.value.length >= props.maxCount) {
      showToast(`最多只能选择${props.maxCount}个文件`)
      break
    }
    
    // 生成预览URL（用于图片）
    let previewUrl = ''
    if (file.type.startsWith('image/')) {
      previewUrl = URL.createObjectURL(file)
    }
    
    // 创建待上传文件项，只显示预览，不自动上传
    const tempFileItem: ExtendedFileListItem = {
      file,
      name: file.name,
      size: file.size,
      isImage: file.type.startsWith('image/'),
      status: 'ready', // 就绪状态，等待手动上传
      message: '点击上传',
      content: previewUrl, // 用于预览
      selected: true // 新添加的文件默认选中
    }
    
    // 添加到文件列表
    if (props.multiple) {
      fileList.value.push(tempFileItem)
    } else {
      // 单文件模式，替换现有文件
      // 清理旧文件的预览URL
      fileList.value.forEach(oldFile => {
        if (oldFile.content) {
          URL.revokeObjectURL(oldFile.content)
        }
      })
      fileList.value = [tempFileItem]
    }
  }
}

// 批量上传选中的文件
const uploadAllFiles = async () => {
  const selectedFiles = fileList.value.filter(file => file.status === 'ready' && file.selected)
  
  if (selectedFiles.length === 0) {
    showToast('请先选择要上传的文件')
    return
  }
  
  // 依次上传所有选中的文件
  for (const file of selectedFiles) {
    await uploadSingleFile(file)
  }
}

// 上传单个文件
const uploadSingleFile = async (fileItem: ExtendedFileListItem) => {
  if (!fileItem.file) return
  
  // 设置上传状态
  fileItem.status = 'uploading'
  fileItem.message = '上传中...'
  
  try {
    // 开始上传文件
    const response = await customFieldApi.uploadFile(fileItem.file)
    const uploadResult = response.data || response
    
    // 更新文件信息
    fileItem.status = 'done'
    fileItem.url = uploadResult.url
    if (fileItem.file) {
      fileItem.name = uploadResult.filename
    }
    fileItem.message = ''
    
    // 上传成功后，用服务器URL替换本地预览URL
    if (fileItem.content) {
      // 释放本地预览URL的内存
      URL.revokeObjectURL(fileItem.content)
      fileItem.content = undefined
    }
    
    // 设置服务器URL用于后续显示
    fileItem.url = uploadResult.url
    
    // 更新外部值
    updateModelValue()
    
    // 如果是编辑模式且有实体ID，立即关联到实体
    if (props.mode === 'edit' && props.appliesTo && props.fieldId) {
      if (props.entityId || (props.taskId && props.assetId)) {
        await saveFieldValueToEntity()
      }
    }
    
    emit('upload-success', uploadResult)
    showToast('上传成功')
    
  } catch (error) {
    console.error('上传失败:', error)
    
    // 更新为失败状态
    fileItem.status = 'failed'
    fileItem.message = '上传失败'
    
    emit('upload-error', error)
    showToast('上传失败，请重试')
  }
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatAcceptText = (accept: string): string => {
  if (accept === '*') return '所有文件'
  if (accept.includes('image')) return '图片'
  if (accept.includes('video')) return '视频'
  if (accept.includes('audio')) return '音频'
  return '指定格式'
}

const isAcceptedFileType = (file: File, accept: string): boolean => {
  if (accept === '*') return true
  
  const acceptTypes = accept.split(',').map(type => type.trim())
  
  return acceptTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase())
    } else if (type.includes('*')) {
      const mainType = type.split('/')[0]
      return file.type.startsWith(mainType + '/')
    } else {
      return file.type === type
    }
  })
}



// 立即保存字段值到实体（移动端支持）
const saveFieldValueToEntity = async () => {
  if (!props.fieldId || !props.appliesTo) {
    return
  }
  
  try {
    const validFiles = fileList.value.filter(file => file.status === 'done' && file.url)
    if (validFiles.length === 0) return
    
    const fileData = validFiles.map(file => ({
      filename: file.name,
      url: file.url,
      size: file.size || 0,
      content_type: getContentType(file.url || '')
    }))
    
    let fieldValue: string
    if (props.multiple) {
      fieldValue = JSON.stringify(fileData)
    } else {
      fieldValue = fileData[0]?.url || ''
    }
    
    const payload = {
      values: [{
        custom_field_id: props.fieldId,
        value: fieldValue
      }]
    }
    
    if (props.appliesTo === 'asset' && props.entityId) {
      // 保存到资产
      await customFieldApi.batchSetAssetCustomFieldValues(props.entityId, payload)
      showToast('图片已关联到资产')
    } else if (props.appliesTo === 'inventory_record') {
      if (props.entityId) {
        // 保存到实际盘点记录
        await customFieldApi.batchSetInventoryRecordCustomFieldValues(props.entityId, payload)
        showToast('图片已关联到盘点记录')
      } else if (props.taskId && props.assetId) {
        // 保存到虚拟盘点记录
        await customFieldApi.batchSetInventoryVirtualRecordCustomFieldValues(props.taskId, props.assetId, payload)
        showToast('图片已关联到盘点记录')
      }
    }
  } catch (error) {
    console.error('关联图片到实体失败:', error)
    showToast('图片上传成功，但关联失败，请手动保存')
  }
}

// 组件卸载时清理所有预览URL
onUnmounted(() => {
  fileList.value.forEach(file => {
    if (file.content) {
      URL.revokeObjectURL(file.content)
    }
  })
})
</script>

<style scoped>
.mobile-file-uploader {
  width: 100%;
}

/* 文件预览列表 */
.file-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.file-preview-item {
  position: relative;
  width: 80px;
  height: 80px; /* 恢复到原来的高度 */
  border-radius: 6px;
  overflow: hidden;
  background: #f7f8fa;
  border: 1px solid #ebedf0;
}

.preview-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 批量上传按钮 */
.batch-upload-actions {
  margin: 12px 0;
}

.batch-upload-actions .van-button {
  height: 44px;
}

/* 上传操作区域 */
.upload-actions {
  margin-top: 8px;
}

.upload-btn-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: #f7f8fa;
  border: 1px dashed #c8c9cc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  gap: 4px;
}

.upload-btn:active {
  background: #e8f3ff;
  border-color: #1989fa;
}

.upload-btn span {
  font-size: 12px;
  color: #646566;
}

/* 上传提示信息 */
.upload-tips {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f7f8fa;
  border-radius: 4px;
  font-size: 12px;
  color: #646566;
  line-height: 1.4;
}

.upload-tips .van-icon {
  flex-shrink: 0;
  color: #969799;
}

/* 选择框（仅在ready状态显示） */
.file-checkbox {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.status-indicator.uploading {
  background: rgba(25, 137, 250, 0.9);
}

.status-indicator.failed {
  background: rgba(238, 10, 36, 0.9);
  cursor: pointer;
}

.status-indicator.failed:active {
  background: rgba(238, 10, 36, 1);
  transform: scale(0.95);
}

.status-indicator.done {
  background: rgba(7, 193, 96, 0.9);
}

/* 删除按钮 */
.delete-btn {
  position: absolute;
  top: 4px;
  left: 4px;  /* 放在左上角，避免与右上角的状态指示器冲突 */
  width: 20px;
  height: 20px;
  background: rgba(238, 10, 36, 0.8);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.delete-btn:active {
  background: rgba(238, 10, 36, 0.9);
}
</style> 