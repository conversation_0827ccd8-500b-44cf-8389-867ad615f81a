"""
第三阶段测试运行脚本
统一运行所有第三阶段的测试和优化验证
"""

import os
import sys
import time
import logging
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.database import get_db
from tests.stage3_testing_optimization.optimization.sync_performance import SyncPerformanceOptimizer
from tests.stage3_testing_optimization.monitoring.sync_status_monitor import SyncStatusMonitor, SyncDashboard

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stage3_test_results.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Stage3TestRunner:
    """第三阶段测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有第三阶段测试"""
        logger.info("=" * 60)
        logger.info("开始第三阶段测试与优化")
        logger.info("=" * 60)
        
        self.start_time = datetime.now()
        
        try:
            # 1. 功能完整性测试
            logger.info("\n1. 运行功能完整性测试...")
            self.test_results['functionality'] = self._run_functionality_tests()
            
            # 2. 数据一致性验证测试
            logger.info("\n2. 运行数据一致性验证测试...")
            self.test_results['data_consistency'] = self._run_data_consistency_tests()
            
            # 3. 性能和并发测试
            logger.info("\n3. 运行性能和并发测试...")
            self.test_results['performance'] = self._run_performance_tests()
            
            # 4. 异常情况处理测试
            logger.info("\n4. 运行异常情况处理测试...")
            self.test_results['exception_handling'] = self._run_exception_tests()
            
            # 5. 性能优化验证
            logger.info("\n5. 运行性能优化验证...")
            self.test_results['optimization'] = self._run_optimization_tests()
            
            # 6. 监控机制验证
            logger.info("\n6. 运行监控机制验证...")
            self.test_results['monitoring'] = self._run_monitoring_tests()
            
            self.end_time = datetime.now()
            
            # 生成测试报告
            self._generate_test_report()
            
            logger.info("\n" + "=" * 60)
            logger.info("第三阶段测试与优化完成")
            logger.info("=" * 60)
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"测试运行失败: {e}")
            self.test_results['error'] = str(e)
            return self.test_results
    
    def _run_functionality_tests(self) -> Dict[str, Any]:
        """运行功能完整性测试"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            # 运行pytest测试
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/stage3_testing_optimization/test_functionality_complete.py',
                '-v', '--tb=short', '--json-report', '--json-report-file=functionality_report.json'
            ]
            
            process = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            result['status'] = 'passed' if process.returncode == 0 else 'failed'
            result['details'] = {
                'return_code': process.returncode,
                'stdout': process.stdout,
                'stderr': process.stderr
            }
            
            # 尝试读取JSON报告
            try:
                with open(project_root / 'functionality_report.json', 'r') as f:
                    result['details']['json_report'] = json.load(f)
            except FileNotFoundError:
                pass
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['details']['error'] = '测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"功能完整性测试完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _run_data_consistency_tests(self) -> Dict[str, Any]:
        """运行数据一致性验证测试"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/stage3_testing_optimization/test_data_consistency.py',
                '-v', '--tb=short'
            ]
            
            process = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            result['status'] = 'passed' if process.returncode == 0 else 'failed'
            result['details'] = {
                'return_code': process.returncode,
                'stdout': process.stdout,
                'stderr': process.stderr
            }
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['details']['error'] = '测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"数据一致性测试完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _run_performance_tests(self) -> Dict[str, Any]:
        """运行性能和并发测试"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/stage3_testing_optimization/test_performance.py',
                '-v', '--tb=short', '-s'
            ]
            
            process = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时（性能测试可能较慢）
            )
            
            result['status'] = 'passed' if process.returncode == 0 else 'failed'
            result['details'] = {
                'return_code': process.returncode,
                'stdout': process.stdout,
                'stderr': process.stderr
            }
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['details']['error'] = '测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"性能测试完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _run_exception_tests(self) -> Dict[str, Any]:
        """运行异常情况处理测试"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/stage3_testing_optimization/test_exception_handling.py',
                '-v', '--tb=short'
            ]
            
            process = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            result['status'] = 'passed' if process.returncode == 0 else 'failed'
            result['details'] = {
                'return_code': process.returncode,
                'stdout': process.stdout,
                'stderr': process.stderr
            }
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['details']['error'] = '测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"异常处理测试完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _run_optimization_tests(self) -> Dict[str, Any]:
        """运行性能优化验证"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            db = next(get_db())
            optimizer = SyncPerformanceOptimizer()
            
            # 运行基准测试
            benchmark_results = optimizer.benchmark_sync_methods(db)
            
            # 分析性能瓶颈
            bottlenecks = optimizer.analyze_performance_bottlenecks(db)
            
            result['status'] = 'passed'
            result['details'] = {
                'benchmark_results': {
                    method: {
                        'execution_time': metrics.execution_time,
                        'throughput': metrics.throughput,
                        'processed_count': metrics.processed_count
                    }
                    for method, metrics in benchmark_results.items()
                },
                'bottlenecks': bottlenecks
            }
            
            db.close()
            
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"性能优化验证完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _run_monitoring_tests(self) -> Dict[str, Any]:
        """运行监控机制验证"""
        result = {
            'status': 'unknown',
            'details': {},
            'execution_time': 0
        }
        
        start_time = time.time()
        
        try:
            db = next(get_db())
            monitor = SyncStatusMonitor(db)
            dashboard = SyncDashboard(monitor)
            
            # 生成监控面板数据
            dashboard_data = dashboard.generate_dashboard_data()
            
            # 检查健康状态
            health_status = monitor.check_health_status()
            
            result['status'] = 'passed'
            result['details'] = {
                'dashboard_data_keys': list(dashboard_data.keys()),
                'health_status': health_status,
                'metrics_available': 'metrics' in dashboard_data['current_status']
            }
            
            # 导出监控面板数据
            dashboard.export_dashboard_json('stage3_monitoring_dashboard.json')
            
            db.close()
            
        except Exception as e:
            result['status'] = 'error'
            result['details']['error'] = str(e)
        
        result['execution_time'] = time.time() - start_time
        logger.info(f"监控机制验证完成: {result['status']}, 耗时: {result['execution_time']:.2f}秒")
        
        return result
    
    def _generate_test_report(self):
        """生成测试报告"""
        total_time = (self.end_time - self.start_time).total_seconds()
        
        report = {
            'test_summary': {
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat(),
                'total_execution_time': total_time,
                'total_tests': len(self.test_results),
                'passed_tests': sum(1 for r in self.test_results.values() if r.get('status') == 'passed'),
                'failed_tests': sum(1 for r in self.test_results.values() if r.get('status') == 'failed'),
                'error_tests': sum(1 for r in self.test_results.values() if r.get('status') == 'error'),
                'timeout_tests': sum(1 for r in self.test_results.values() if r.get('status') == 'timeout')
            },
            'detailed_results': self.test_results
        }
        
        # 保存报告
        with open('stage3_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        # 打印摘要
        logger.info("\n" + "=" * 50)
        logger.info("第三阶段测试报告摘要")
        logger.info("=" * 50)
        logger.info(f"总执行时间: {total_time:.2f}秒")
        logger.info(f"总测试数: {report['test_summary']['total_tests']}")
        logger.info(f"通过: {report['test_summary']['passed_tests']}")
        logger.info(f"失败: {report['test_summary']['failed_tests']}")
        logger.info(f"错误: {report['test_summary']['error_tests']}")
        logger.info(f"超时: {report['test_summary']['timeout_tests']}")
        logger.info(f"详细报告已保存到: stage3_test_report.json")

if __name__ == "__main__":
    runner = Stage3TestRunner()
    results = runner.run_all_tests()
    
    # 根据测试结果设置退出码
    if any(r.get('status') in ['failed', 'error', 'timeout'] for r in results.values()):
        sys.exit(1)
    else:
        sys.exit(0)
