#!/usr/bin/env python
"""
测试Redis缓存中间件
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入Redis缓存中间件
try:
    from backend.app.middleware.redis_cache_logging import RedisCacheMiddleware, cache_metrics
    print("Redis缓存中间件导入成功")
    
    # 尝试实例化中间件
    middleware = RedisCacheMiddleware(app=None)
    print("Redis缓存中间件实例化成功")
    
    # 获取中间件统计信息
    metrics = cache_metrics.get_metrics()
    print(f"缓存指标: {metrics}")
    
    print("测试成功")
except Exception as e:
    print(f"导入或实例化Redis缓存中间件失败: {str(e)}")
    import traceback
    traceback.print_exc() 