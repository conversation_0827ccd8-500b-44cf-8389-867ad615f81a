interface LogMessage {
  level: 'info' | 'warn' | 'error';
  message: string;
  timestamp: string;
  data?: any;
  error?: Error;
}

class Logger {
  private static instance: Logger;
  private logQueue: LogMessage[] = [];
  private readonly maxQueueSize = 100;
  private readonly flushInterval = 5000; // 5秒
  
  private constructor() {
    this.startAutoFlush();
  }
  
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }
  
  private createLogMessage(
    level: LogMessage['level'],
    message: string,
    data?: any,
    error?: Error
  ): LogMessage {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      data,
      error
    };
  }
  
  private async flushLogs() {
    if (this.logQueue.length === 0) return;
    
    try {
      // 在生产环境发送到日志服务器
      if (import.meta.env.PROD) {
        await fetch('/api/logs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.logQueue)
        });
      }
      
      // 开发环境打印到控制台
      else {
        this.logQueue.forEach(log => {
          console[log.level](
            `[${log.timestamp}] ${log.message}`,
            log.data || '',
            log.error || ''
          );
        });
      }
      
      this.logQueue = [];
    } catch (error) {
      console.error('Failed to flush logs:', error);
    }
  }
  
  private startAutoFlush() {
    setInterval(() => this.flushLogs(), this.flushInterval);
  }
  
  info(message: string, data?: any) {
    this.addToQueue('info', message, data);
  }
  
  warn(message: string, data?: any) {
    this.addToQueue('warn', message, data);
  }
  
  error(message: string, error?: Error, data?: any) {
    this.addToQueue('error', message, data, error);
  }
  
  private addToQueue(
    level: LogMessage['level'],
    message: string,
    data?: any,
    error?: Error
  ) {
    this.logQueue.push(this.createLogMessage(level, message, data, error));
    
    if (this.logQueue.length >= this.maxQueueSize) {
      this.flushLogs();
    }
  }
}

export const logger = Logger.getInstance(); 