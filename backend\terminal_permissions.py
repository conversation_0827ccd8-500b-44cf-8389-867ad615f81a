from app.database import SessionLocal
from app.crud.permission import permission_crud
from app.schemas.permission import PermissionCreate

def create_terminal_permissions():
    """创建终端管理模块的权限"""
    db = SessionLocal()
    try:
        # 定义终端管理模块的权限
        terminal_permissions = [
            # 终端管理基础权限
            PermissionCreate(code="terminal:view", name="查看终端", module="terminal", description="查看终端列表和详情"),
            PermissionCreate(code="terminal:add", name="添加终端", module="terminal", description="手动添加终端"),
            PermissionCreate(code="terminal:edit", name="编辑终端", module="terminal", description="编辑终端信息"),
            PermissionCreate(code="terminal:delete", name="删除终端", module="terminal", description="删除终端"),
            
            # 终端命令权限
            PermissionCreate(code="terminal:command:send", name="发送命令", module="terminal", description="向终端发送命令"),
            PermissionCreate(code="terminal:command:view", name="查看命令", module="terminal", description="查看终端命令历史"),
            
            # 软件管理权限
            PermissionCreate(code="terminal:software:view", name="查看软件", module="terminal", description="查看终端软件列表"),
            PermissionCreate(code="terminal:software:manage", name="管理软件", module="terminal", description="管理软件合规性和备注"),
            
            # Agent管理权限
            PermissionCreate(code="terminal:agent:view", name="查看Agent", module="terminal", description="查看Agent版本列表"),
            PermissionCreate(code="terminal:agent:manage", name="管理Agent", module="terminal", description="管理Agent版本和升级"),
        ]
        
        # 检查权限是否已存在
        for permission in terminal_permissions:
            existing = permission_crud.get_by_code(db, permission.code)
            if not existing:
                created = permission_crud.create(db, obj_in=permission)
                print(f"创建权限: {created.code} - {created.name}")
            else:
                print(f"权限已存在: {permission.code} - {permission.name}")
        
        db.commit()
        print("终端管理模块权限创建完成！")
        
    except Exception as e:
        print(f"创建终端管理模块权限时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_terminal_permissions()
