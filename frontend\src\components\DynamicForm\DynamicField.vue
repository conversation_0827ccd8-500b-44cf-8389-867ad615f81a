<template>
  <el-form-item
    :label="field.label"
    :prop="field.name"
    :required="field.is_required"
    :error="error"
  >
    <!-- 文本输入 -->
    <el-input
      v-if="field.field_type === FieldType.TEXT"
      :model-value="value"
      :placeholder="field.options?.placeholder || `请输入${field.label}`"
      :disabled="disabled"
      :maxlength="field.options?.max || field.validation_rules?.max_length"
      :minlength="field.options?.min || field.validation_rules?.min_length"
      :show-word-limit="!!(field.options?.max || field.validation_rules?.max_length)"
      clearable
      @input="handleInput"
      @blur="handleBlur"
    />

    <!-- 多行文本 -->
    <el-input
      v-else-if="field.field_type === FieldType.TEXTAREA"
      :model-value="value"
      type="textarea"
      :placeholder="field.options?.placeholder || `请输入${field.label}`"
      :disabled="disabled"
      :rows="field.options?.rows || 4"
      :maxlength="field.options?.max || field.validation_rules?.max_length"
      :minlength="field.options?.min || field.validation_rules?.min_length"
      :show-word-limit="!!(field.options?.max || field.validation_rules?.max_length)"
      resize="vertical"
      @input="handleInput"
      @blur="handleBlur"
    />

    <!-- 数字输入 -->
    <el-input-number
      v-else-if="field.field_type === FieldType.NUMBER"
      :model-value="parseNumber(value)"
      :placeholder="field.options?.placeholder || `请输入${field.label}`"
      :disabled="disabled"
      :min="field.options?.min || field.validation_rules?.min_value"
      :max="field.options?.max || field.validation_rules?.max_value"
      :step="field.options?.step || 1"
      :precision="getPrecision()"
      controls-position="right"
      style="width: 100%"
      @change="handleNumberChange"
      @blur="handleBlur"
    />

    <!-- 日期选择 -->
    <el-date-picker
      v-else-if="field.field_type === FieldType.DATE"
      :model-value="parseDate(value)"
      type="date"
      :placeholder="field.options?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      style="width: 100%"
      @change="handleDateChange"
      @blur="handleBlur"
    />

    <!-- 日期时间选择 -->
    <el-date-picker
      v-else-if="field.field_type === FieldType.DATETIME"
      :model-value="parseDateTime(value)"
      type="datetime"
      :placeholder="field.options?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      format="YYYY-MM-DD HH:mm:ss"
      value-format="YYYY-MM-DD HH:mm:ss"
      style="width: 100%"
      @change="handleDateTimeChange"
      @blur="handleBlur"
    />

    <!-- 下拉选择 -->
    <el-select
      v-else-if="field.field_type === FieldType.SELECT"
      :model-value="value"
      :placeholder="field.options?.placeholder || `请选择${field.label}`"
      :disabled="disabled"
      :multiple="field.options?.multiple"
      :clearable="!field.is_required"
      style="width: 100%"
      @change="handleSelectChange"
      @blur="handleBlur"
    >
      <el-option
        v-for="option in field.options?.choices || []"
        :key="option.value"
        :label="option.label"
        :value="option.value"
        :disabled="option.disabled"
      />
    </el-select>

    <!-- 单选按钮 -->
    <el-radio-group
      v-else-if="field.field_type === FieldType.RADIO"
      :model-value="value"
      :disabled="disabled"
      @change="handleRadioChange"
    >
      <el-radio
        v-for="option in field.options?.choices || []"
        :key="option.value"
        :value="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </el-radio>
    </el-radio-group>

    <!-- 多选框 -->
    <el-checkbox-group
      v-else-if="field.field_type === FieldType.CHECKBOX"
      :model-value="parseCheckboxValue(value)"
      :disabled="disabled"
      @change="handleCheckboxChange"
    >
      <el-checkbox
        v-for="option in field.options?.choices || []"
        :key="option.value"
        :value="option.value"
        :disabled="option.disabled"
      >
        {{ option.label }}
      </el-checkbox>
    </el-checkbox-group>

    <!-- 文件上传 -->
    <div v-else-if="field.field_type === FieldType.FILE" class="file-upload-container">
      <!-- 文件选择区域 -->
      <div class="file-select-area">
        <el-upload
          ref="uploadRef"
          class="file-upload"
          :auto-upload="false"
          :show-file-list="false"
          :multiple="field.options?.multiple || false"
          :accept="field.options?.accept || '*'"
          :before-upload="beforeUpload"
          :on-change="handleFileSelect"
          :disabled="disabled"
          action="#"
        >
          <template #trigger>
            <el-button type="primary" :disabled="disabled">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
          </template>
          <template #tip>
            <div class="el-upload__tip">
              <span v-if="field.options?.accept">
                支持格式：{{ formatAcceptText(field.options.accept) }}
              </span>
              <span v-if="field.options?.max_size">
                ，文件大小不超过 {{ formatFileSize(field.options.max_size) }}
              </span>
            </div>
          </template>
        </el-upload>
        
        <!-- 移动端拍照按钮 -->
        <el-button
          v-if="isMobile && isImageAccept"
          type="success"
          class="camera-button"
          :disabled="disabled"
          @click="openCamera"
        >
          <el-icon><Camera /></el-icon>
          拍照
        </el-button>
      </div>

      <!-- 待上传文件预览区域 -->
      <div v-if="pendingFiles.length > 0" class="pending-files-area">
        <div class="pending-files-header">
          <span class="header-title">待上传文件</span>
          <el-button size="small" type="danger" @click="clearPendingFiles">清空</el-button>
        </div>
        
        <div class="pending-files-list">
          <div
            v-for="(file, index) in pendingFiles"
            :key="index"
            class="pending-file-item"
          >
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
            
            <!-- 图片预览 -->
            <div v-if="file.preview" class="file-preview">
              <img 
                :src="file.preview" 
                alt="预览" 
                class="preview-image" 
                @click="openImagePreview(file.preview)"
                title="点击查看大图"
              />
            </div>
            
            <div class="file-actions">
              <el-button
                size="small"
                type="success"
                :loading="file.uploading"
                @click="uploadSingleFile(file, index)"
                :disabled="file.uploading"
              >
                上传
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="removePendingFile(index)"
                :disabled="file.uploading"
                plain
              >
                移除
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 批量操作 -->
        <div class="batch-actions">
          <el-button
            type="primary"
            :loading="batchUploading"
            @click="uploadAllFiles"
            :disabled="pendingFiles.length === 0"
          >
            上传所有文件
          </el-button>
        </div>
      </div>

      <!-- 已上传文件列表 -->
      <div v-if="uploadedFiles.length > 0" class="uploaded-files-area">
        <div class="uploaded-files-header">
          <span class="header-title">已上传文件</span>
        </div>
        
        <div class="uploaded-files-list">
          <div
            v-for="(file, index) in uploadedFiles"
            :key="index"
            class="uploaded-file-item"
          >
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>
            
            <!-- 图片预览 -->
            <div v-if="file.url && isImageFile(file.name)" class="file-preview">
              <img 
                :src="getFullUrl(file.url)" 
                alt="预览" 
                class="preview-image" 
                @click="openImagePreview(getFullUrl(file.url))"
                title="点击查看大图"
              />
            </div>
            
            <div class="file-actions">
              <el-button
                size="small"
                type="primary"
                @click="previewFile(file)"
                v-if="file.url"
                plain
              >
                预览
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="removeUploadedFile(index)"
                plain
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 隐藏的文件输入，用于拍照 -->
      <input
        ref="cameraInputRef"
        type="file"
        accept="image/*"
        capture="environment"
        style="display: none"
        @change="handleCameraCapture"
      />
    </div>

    <!-- 未知类型 -->
    <div v-else class="unknown-field-type">
      <el-alert
        title="未知字段类型"
        :description="`字段类型 '${field.field_type}' 暂不支持`"
        type="warning"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 字段描述 -->
    <div v-if="field.description" class="field-description">
      <small>{{ field.description }}</small>
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type UploadProps, type UploadUserFile, type UploadRequestOptions } from 'element-plus'
import { Upload, Camera } from '@element-plus/icons-vue'
import { customFieldApi } from '@/api/custom_field'
import type { CustomField } from '@/types/custom_field'
import { FieldType as CustomFieldType } from '@/types/custom_field'
import { API_BASE_URL } from '@/config/api'

interface Props {
  field: CustomField
  value?: any
  error?: string
  disabled?: boolean
  // 实体关联信息
  appliesTo?: string
  entityId?: number | null
  mode?: 'create' | 'edit'
  taskId?: number
  assetId?: number
}

interface Emits {
  (e: 'update:value', value: any): void
  (e: 'blur'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 引用
const uploadRef = ref()
const cameraInputRef = ref<HTMLInputElement>()

// 常量
const FieldType = CustomFieldType

// 计算属性
const isMobile = computed(() => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
})

const isImageAccept = computed(() => {
  const accept = props.field.options?.accept || ''
  return accept.includes('image') || accept.includes('*')
})

// 文件状态管理
const pendingFiles = ref<Array<{
  name: string
  size: number
  raw: File
  preview?: string
  uploading: boolean
}>>([])

const uploadedFiles = ref<Array<{
  name: string
  url: string
  size: number
  type: string
}>>([])

const batchUploading = ref(false)

// 监听value变化，更新已上传文件列表
watch(() => props.value, (newValue) => {
  if (props.field.field_type === CustomFieldType.FILE && newValue) {
    try {
      if (typeof newValue === 'string' && newValue) {
        // 单个文件URL
        if (newValue.startsWith('/uploads/') || newValue.startsWith('http')) {
          uploadedFiles.value = [{
            name: newValue.split('/').pop() || 'unknown',
            url: newValue,
            size: 0,
            type: 'unknown'
          }]
        } else {
          // JSON字符串
          const files = JSON.parse(newValue)
          if (Array.isArray(files)) {
            uploadedFiles.value = files.map((file: any) => ({
              name: file.filename || file.name || 'unknown',
              url: file.url || file.path || '',
              size: file.size || 0,
              type: file.type || file.content_type || 'unknown'
            }))
          }
        }
      } else if (Array.isArray(newValue)) {
        uploadedFiles.value = newValue.map((file: any) => ({
          name: file.filename || file.name || 'unknown',
          url: file.url || file.path || '',
          size: file.size || 0,
          type: file.type || file.content_type || 'unknown'
        }))
      } else {
        uploadedFiles.value = []
      }
    } catch {
      uploadedFiles.value = []
    }
  } else {
    uploadedFiles.value = []
  }
}, { immediate: true })

// 通用事件处理
const handleInput = (value: any) => {
  emit('update:value', value)
}

const handleBlur = () => {
  emit('blur')
}

// 数字输入处理
const parseNumber = (value: any): number | undefined => {
  if (value === null || value === undefined || value === '') return undefined
  const num = Number(value)
  return isNaN(num) ? undefined : num
}

const getPrecision = (): number => {
  const step = props.field.options?.step || 1
  const stepStr = step.toString()
  const decimalIndex = stepStr.indexOf('.')
  return decimalIndex >= 0 ? stepStr.length - decimalIndex - 1 : 0
}

const handleNumberChange = (value: number | undefined) => {
  emit('update:value', value?.toString() || '')
}

// 日期处理
const parseDate = (value: any): Date | undefined => {
  if (!value) return undefined
  const date = new Date(value)
  return isNaN(date.getTime()) ? undefined : date
}

const parseDateTime = (value: any): Date | undefined => {
  if (!value) return undefined
  const date = new Date(value)
  return isNaN(date.getTime()) ? undefined : date
}

const handleDateChange = (value: string | null) => {
  emit('update:value', value || '')
}

const handleDateTimeChange = (value: string | null) => {
  emit('update:value', value || '')
}

// 选择框处理
const handleSelectChange = (value: any) => {
  emit('update:value', value)
}

const handleRadioChange = (value: any) => {
  emit('update:value', value)
}

// 复选框处理
const parseCheckboxValue = (value: any): any[] => {
  if (Array.isArray(value)) return value
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value)
      return Array.isArray(parsed) ? parsed : []
    } catch {
      return value ? [value] : []
    }
  }
  return []
}

const handleCheckboxChange = (value: any[]) => {
  emit('update:value', JSON.stringify(value))
}

// 文件上传处理
const beforeUpload = (file: File): boolean => {
  // 检查文件大小
  if (props.field.options?.max_size && file.size > props.field.options.max_size) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.field.options.max_size)}`)
    return false
  }

  // 检查文件类型
  if (props.field.options?.accept && props.field.options.accept !== '*') {
    const acceptTypes = props.field.options.accept.split(',').map(type => type.trim())
    const isValidType = acceptTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase())
      } else if (type.includes('*')) {
        const mainType = type.split('/')[0]
        return file.type.startsWith(mainType + '/')
      } else {
        return file.type === type
      }
    })

    if (!isValidType) {
      ElMessage.error(`文件类型不支持，请选择 ${formatAcceptText(props.field.options.accept)} 格式的文件`)
      return false
    }
  }

  return true
}

// 文件选择处理
const handleFileSelect = (file: any) => {
  if (!file.raw) return
  
  const newFile = {
    name: file.name,
    size: file.size,
    raw: file.raw,
    uploading: false,
    preview: undefined as string | undefined
  }
  
  // 先添加到数组
  if (props.field.options?.multiple) {
    pendingFiles.value.push(newFile)
  } else {
    pendingFiles.value = [newFile]
  }
  
  // 如果是图片，异步生成预览
  if (file.raw.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      // 更新已添加文件的预览
      const targetFile = pendingFiles.value.find(f => f.raw === file.raw)
      if (targetFile) {
        targetFile.preview = e.target?.result as string
      }
    }
    reader.onerror = () => {
      console.error('生成图片预览失败')
    }
    reader.readAsDataURL(file.raw)
  }
}

// 清空待上传文件
const clearPendingFiles = () => {
  pendingFiles.value = []
}

// 移除单个待上传文件
const removePendingFile = (index: number) => {
  pendingFiles.value.splice(index, 1)
}

// 上传单个文件
const uploadSingleFile = async (file: any, index: number) => {
  try {
    file.uploading = true
    const response = await customFieldApi.uploadFile(file.raw)
    
    // API响应解构
    const responseData = response.data || response
    
    if (responseData) {
      const uploadedFile = {
        name: responseData.filename || file.name,
        url: responseData.url,
        size: responseData.size || file.size,
        type: responseData.content_type || file.raw.type
      }
      
      // 添加到已上传列表
      uploadedFiles.value.push(uploadedFile)
      
      // 从待上传列表移除
      pendingFiles.value.splice(index, 1)
      
      // 更新字段值并关联
      await updateFieldValue()
      
      ElMessage.success('文件上传成功')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('文件上传失败')
  } finally {
    file.uploading = false
  }
}

// 上传所有文件
const uploadAllFiles = async () => {
  if (pendingFiles.value.length === 0) return
  
  batchUploading.value = true
  const uploadPromises = pendingFiles.value.map(async (file, index) => {
    try {
      file.uploading = true
      const response = await customFieldApi.uploadFile(file.raw)
      const responseData = response.data || response
      
      if (responseData) {
        return {
          name: responseData.filename || file.name,
          url: responseData.url,
          size: responseData.size || file.size,
          type: responseData.content_type || file.raw.type
        }
      }
      return null
    } catch (error) {
      console.error(`文件 ${file.name} 上传失败:`, error)
      throw error
    } finally {
      file.uploading = false
    }
  })
  
  try {
    const results = await Promise.allSettled(uploadPromises)
    let successCount = 0
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        uploadedFiles.value.push(result.value)
        successCount++
      }
    })
    
    // 清空待上传列表
    pendingFiles.value = []
    
    // 更新字段值并关联
    await updateFieldValue()
    
    if (successCount === results.length) {
      ElMessage.success(`所有文件上传成功 (${successCount}个)`)
    } else {
      ElMessage.warning(`上传完成：成功 ${successCount} 个，失败 ${results.length - successCount} 个`)
    }
  } catch (error) {
    ElMessage.error('批量上传失败')
  } finally {
    batchUploading.value = false
  }
}

// 移除已上传文件
const removeUploadedFile = async (index: number) => {
  uploadedFiles.value.splice(index, 1)
  await updateFieldValue()
}

// 更新字段值并立即关联到实体
const updateFieldValue = async () => {
  if (uploadedFiles.value.length === 0) {
    emit('update:value', '')
    return
  }
  
  let fieldValue: string
  if (props.field.options?.multiple) {
    // 多文件模式：存储为数组
    fieldValue = JSON.stringify(uploadedFiles.value.map(file => ({
      filename: file.name,
      url: file.url,
      size: file.size,
      content_type: file.type
    })))
  } else {
    // 单文件模式：直接存储URL
    fieldValue = uploadedFiles.value[0]?.url || ''
  }
  
  // 先更新表单值
  emit('update:value', fieldValue)
  
  // 如果是编辑模式且有实体ID，立即关联到实体
  if (props.mode === 'edit' && props.entityId && props.appliesTo) {
    await saveFieldValueToEntity(fieldValue)
  }
}

// 立即保存字段值到实体
const saveFieldValueToEntity = async (fieldValue: string) => {
  try {
    const payload = {
      values: [{
        custom_field_id: props.field.id,
        value: fieldValue
      }]
    }
    
    if (props.appliesTo === 'asset' && props.entityId) {
      // 保存到资产
      await customFieldApi.batchSetAssetCustomFieldValues(props.entityId, payload)
      ElMessage.success('图片已关联到资产')
    } else if (props.appliesTo === 'inventory_record') {
      if (props.entityId) {
        // 保存到实际盘点记录
        await customFieldApi.batchSetInventoryRecordCustomFieldValues(props.entityId, payload)
        ElMessage.success('图片已关联到盘点记录')
      } else if (props.taskId && props.assetId) {
        // 保存到虚拟盘点记录
        await customFieldApi.batchSetInventoryVirtualRecordCustomFieldValues(props.taskId, props.assetId, payload)
        ElMessage.success('图片已关联到盘点记录')
      }
    }
  } catch (error) {
    console.error('关联图片到实体失败:', error)
    ElMessage.warning('图片上传成功，但关联失败，请手动保存')
  }
}

// 拍照功能
const openCamera = () => {
  if (cameraInputRef.value) {
    cameraInputRef.value.click()
  }
}

const handleCameraCapture = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file && beforeUpload(file)) {
    try {
      const response = await customFieldApi.uploadFile(file)
      // 处理API响应数据
      const responseData = response.data || response
      
      const newFile = {
        name: responseData.filename || responseData.name || 'unknown',
        url: responseData.url || responseData.file_url || '',
        size: responseData.size || 0,
        type: responseData.content_type || responseData.type || 'unknown'
      }

      // 直接添加到已上传文件列表
      if (props.field.options?.multiple) {
        uploadedFiles.value.push(newFile)
      } else {
        uploadedFiles.value = [newFile]
      }

      // 更新字段值并关联
      await updateFieldValue()
      
      ElMessage.success('拍照上传成功')
    } catch (error) {
      console.error('拍照上传失败:', error)
      ElMessage.error('拍照上传失败，请重试')
    }
  }
  
  // 清空input值，以便下次可以选择同一个文件
  target.value = ''
}

// 工具函数
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + ' MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB'
}

const formatAcceptText = (accept: string): string => {
  return accept
    .split(',')
    .map(type => type.trim())
    .join('、')
}

// 判断是否为图片文件
const isImageFile = (filename: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
  const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return imageExtensions.includes(ext)
}

// 获取完整URL
const getFullUrl = (url: string): string => {
  if (url.startsWith('http')) {
    return url
  }
  // 使用配置文件中的API_BASE_URL，去掉'/api/v1'后缀
  const baseUrl = API_BASE_URL.replace('/api/v1', '')
  return baseUrl + url
}

// 文件预览
const previewFile = (file: { url: string, name: string }) => {
  if (isImageFile(file.name)) {
    // 图片预览 - 可以使用Element Plus的图片预览功能
    const imageUrl = getFullUrl(file.url)
    window.open(imageUrl, '_blank')
  } else {
    // 其他文件直接下载
    const link = document.createElement('a')
    link.href = getFullUrl(file.url)
    link.download = file.name
    link.click()
  }
}

// 打开图片预览
const openImagePreview = (imageUrl: string) => {
  // 创建遮罩层
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    cursor: pointer;
  `
  
  // 创建图片元素
  const img = document.createElement('img')
  img.src = imageUrl
  img.style.cssText = `
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `
  
  overlay.appendChild(img)
  document.body.appendChild(overlay)
  
  // 点击关闭
  overlay.addEventListener('click', () => {
    document.body.removeChild(overlay)
  })
  
  // 按ESC键关闭
  const handleKeyPress = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      document.body.removeChild(overlay)
      document.removeEventListener('keydown', handleKeyPress)
    }
  }
  document.addEventListener('keydown', handleKeyPress)
}


</script>

<style scoped>
.field-description {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.file-upload-container {
  width: 100%;
}

.file-select-area {
  margin-bottom: 16px;
}

.file-upload {
  width: 100%;
}

.camera-button {
  margin-top: 8px;
  margin-left: 8px;
}

/* 待上传文件区域 */
.pending-files-area {
  margin: 16px 0;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #f9fafc;
}

.pending-files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.pending-files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pending-file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.pending-file-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  word-break: break-all;
  margin-bottom: 4px;
  line-height: 1.4;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.file-preview {
  flex-shrink: 0;
  position: relative;
}

.preview-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-image:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.file-actions {
  display: flex;
  flex-direction: row;
  gap: 8px;
  flex-shrink: 0;
  align-self: center;
}

.batch-actions {
  margin-top: 12px;
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

/* 已上传文件区域 */
.uploaded-files-area {
  margin: 16px 0;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #f0f9ff;
}

.uploaded-files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.uploaded-files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploaded-file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: white;
  border: 1px solid #d4e6f1;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.uploaded-file-item:hover {
  border-color: #67c23a;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
}

.unknown-field-type {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-input-number) {
  width: 100% !important;
}
</style>