#!/usr/bin/env python3
"""
初始化命令白名单数据脚本

运行方式:
cd backend
python scripts/init_command_whitelist.py
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.database import SessionLocal
from app.models.command_whitelist import CommandCategory, CommandWhitelist
from app.schemas.command_whitelist import CommandCategoryCreate, CommandWhitelistCreate
from app.crud import command_whitelist as crud
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def init_default_command_whitelist():
    """初始化默认的命令白名单"""
    
    db = SessionLocal()
    
    try:
        logger.info("开始初始化命令白名单...")
        
        # 默认分类和命令数据
        default_data = [
            {
                "category": {
                    "name": "系统信息",
                    "description": "系统基础信息查询命令，适用于系统监控和诊断",
                    "required_permission": "terminal:command:basic"
                },
                "commands": [
                    {
                        "name": "系统信息查询",
                        "command": "systeminfo",
                        "description": "显示计算机和操作系统的详细配置信息",
                        "example": "systeminfo",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "当前用户信息",
                        "command": "whoami",
                        "description": "显示当前登录用户的用户名和域信息",
                        "example": "whoami",
                        "timeout": 10,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "主机名查询",
                        "command": "hostname",
                        "description": "显示计算机的主机名",
                        "example": "hostname",
                        "timeout": 10,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "系统版本信息",
                        "command": "ver",
                        "description": "显示Windows版本信息",
                        "example": "ver",
                        "timeout": 10,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    }
                ]
            },
            {
                "category": {
                    "name": "网络信息",
                    "description": "网络配置和连接信息查询命令",
                    "required_permission": "terminal:command:basic"
                },
                "commands": [
                    {
                        "name": "IP配置查询",
                        "command": "ipconfig",
                        "description": "显示网络适配器的IP配置信息",
                        "example": "ipconfig /all",
                        "timeout": 20,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "网络连通性测试",
                        "command": "ping",
                        "description": "测试与目标主机的网络连通性",
                        "example": "ping *******",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "DNS解析查询",
                        "command": "nslookup",
                        "description": "查询DNS记录信息",
                        "example": "nslookup google.com",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    },
                    {
                        "name": "网络连接状态",
                        "command": "netstat",
                        "description": "显示网络连接、路由表和网络接口信息",
                        "example": "netstat -an",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "PUBLIC"
                    }
                ]
            },
            {
                "category": {
                    "name": "进程服务",
                    "description": "进程和服务管理命令，需要操作员权限",
                    "required_permission": "terminal:command:operator"
                },
                "commands": [
                    {
                        "name": "进程列表",
                        "command": "tasklist",
                        "description": "显示正在运行的进程列表",
                        "example": "tasklist",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "OPERATOR"
                    },
                    {
                        "name": "服务查询",
                        "command": "sc query",
                        "description": "查询Windows服务状态",
                        "example": "sc query",
                        "timeout": 30,
                        "admin_required": False,
                        "security_level": "OPERATOR"
                    }
                ]
            }
        ]
        
        created_categories = 0
        created_commands = 0
        
        for category_data in default_data:
            # 创建或获取分类
            existing_category = crud.get_command_category_by_name(db, category_data["category"]["name"])
            if not existing_category:
                category_create = CommandCategoryCreate(**category_data["category"])
                category = crud.create_command_category(db, category_create)
                created_categories += 1
                logger.info(f"创建分类: {category.name}")
            else:
                category = existing_category
                logger.info(f"分类已存在: {category.name}")
            
            # 创建命令
            for command_data in category_data["commands"]:
                command_data["category_id"] = category.id
                command_create = CommandWhitelistCreate(**command_data)
                cmd = crud.create_command_whitelist(db, command_create)
                created_commands += 1
                logger.info(f"创建命令: {cmd.name}")
        
        logger.info("命令白名单初始化完成！")
        return {"created_categories": created_categories, "created_commands": created_commands}
        
    except Exception as e:
        logger.error(f"初始化命令白名单时出错: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    result = init_default_command_whitelist()
    print(f"初始化完成: 创建 {result['created_categories']} 个分类, {result['created_commands']} 个命令") 