# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import terminal_pb2 as terminal__pb2


class TerminalManagementStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RegisterTerminal = channel.unary_unary(
                '/terminal.TerminalManagement/RegisterTerminal',
                request_serializer=terminal__pb2.RegisterRequest.SerializeToString,
                response_deserializer=terminal__pb2.RegisterResponse.FromString,
                )
        self.Heartbeat = channel.unary_unary(
                '/terminal.TerminalManagement/Heartbeat',
                request_serializer=terminal__pb2.HeartbeatRequest.SerializeToString,
                response_deserializer=terminal__pb2.HeartbeatResponse.FromString,
                )
        self.ReportTerminalInfo = channel.unary_unary(
                '/terminal.TerminalManagement/ReportTerminalInfo',
                request_serializer=terminal__pb2.TerminalInfoReport.SerializeToString,
                response_deserializer=terminal__pb2.ReportResponse.FromString,
                )
        self.ReceiveCommands = channel.unary_stream(
                '/terminal.TerminalManagement/ReceiveCommands',
                request_serializer=terminal__pb2.CommandRequest.SerializeToString,
                response_deserializer=terminal__pb2.Command.FromString,
                )
        self.ReportCommandResult = channel.unary_unary(
                '/terminal.TerminalManagement/ReportCommandResult',
                request_serializer=terminal__pb2.CommandResult.SerializeToString,
                response_deserializer=terminal__pb2.CommandResultResponse.FromString,
                )
        self.NotifyCommand = channel.unary_unary(
                '/terminal.TerminalManagement/NotifyCommand',
                request_serializer=terminal__pb2.CommandNotification.SerializeToString,
                response_deserializer=terminal__pb2.NotificationResponse.FromString,
                )
        self.PerformRegistryOperation = channel.unary_unary(
                '/terminal.TerminalManagement/PerformRegistryOperation',
                request_serializer=terminal__pb2.RegistryOperationRequest.SerializeToString,
                response_deserializer=terminal__pb2.RegistryOperationResponse.FromString,
                )
        self.SearchRegistry = channel.unary_unary(
                '/terminal.TerminalManagement/SearchRegistry',
                request_serializer=terminal__pb2.RegistrySearchRequest.SerializeToString,
                response_deserializer=terminal__pb2.RegistrySearchResponse.FromString,
                )


class TerminalManagementServicer(object):
    """Missing associated documentation comment in .proto file."""

    def RegisterTerminal(self, request, context):
        """终端注册接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Heartbeat(self, request, context):
        """终端心跳接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReportTerminalInfo(self, request, context):
        """终端信息上报接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReceiveCommands(self, request, context):
        """任务指令下发接口 - 服务器推送模式
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReportCommandResult(self, request, context):
        """任务执行结果上报接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NotifyCommand(self, request, context):
        """命令通知接口 - 服务器主动通知终端有新命令
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PerformRegistryOperation(self, request, context):
        """===================== 注册表管理服务方法 =====================

        注册表操作接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchRegistry(self, request, context):
        """注册表搜索接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TerminalManagementServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RegisterTerminal': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterTerminal,
                    request_deserializer=terminal__pb2.RegisterRequest.FromString,
                    response_serializer=terminal__pb2.RegisterResponse.SerializeToString,
            ),
            'Heartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.Heartbeat,
                    request_deserializer=terminal__pb2.HeartbeatRequest.FromString,
                    response_serializer=terminal__pb2.HeartbeatResponse.SerializeToString,
            ),
            'ReportTerminalInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.ReportTerminalInfo,
                    request_deserializer=terminal__pb2.TerminalInfoReport.FromString,
                    response_serializer=terminal__pb2.ReportResponse.SerializeToString,
            ),
            'ReceiveCommands': grpc.unary_stream_rpc_method_handler(
                    servicer.ReceiveCommands,
                    request_deserializer=terminal__pb2.CommandRequest.FromString,
                    response_serializer=terminal__pb2.Command.SerializeToString,
            ),
            'ReportCommandResult': grpc.unary_unary_rpc_method_handler(
                    servicer.ReportCommandResult,
                    request_deserializer=terminal__pb2.CommandResult.FromString,
                    response_serializer=terminal__pb2.CommandResultResponse.SerializeToString,
            ),
            'NotifyCommand': grpc.unary_unary_rpc_method_handler(
                    servicer.NotifyCommand,
                    request_deserializer=terminal__pb2.CommandNotification.FromString,
                    response_serializer=terminal__pb2.NotificationResponse.SerializeToString,
            ),
            'PerformRegistryOperation': grpc.unary_unary_rpc_method_handler(
                    servicer.PerformRegistryOperation,
                    request_deserializer=terminal__pb2.RegistryOperationRequest.FromString,
                    response_serializer=terminal__pb2.RegistryOperationResponse.SerializeToString,
            ),
            'SearchRegistry': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchRegistry,
                    request_deserializer=terminal__pb2.RegistrySearchRequest.FromString,
                    response_serializer=terminal__pb2.RegistrySearchResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'terminal.TerminalManagement', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TerminalManagement(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def RegisterTerminal(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/RegisterTerminal',
            terminal__pb2.RegisterRequest.SerializeToString,
            terminal__pb2.RegisterResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Heartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/Heartbeat',
            terminal__pb2.HeartbeatRequest.SerializeToString,
            terminal__pb2.HeartbeatResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReportTerminalInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/ReportTerminalInfo',
            terminal__pb2.TerminalInfoReport.SerializeToString,
            terminal__pb2.ReportResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReceiveCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/terminal.TerminalManagement/ReceiveCommands',
            terminal__pb2.CommandRequest.SerializeToString,
            terminal__pb2.Command.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReportCommandResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/ReportCommandResult',
            terminal__pb2.CommandResult.SerializeToString,
            terminal__pb2.CommandResultResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NotifyCommand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/NotifyCommand',
            terminal__pb2.CommandNotification.SerializeToString,
            terminal__pb2.NotificationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PerformRegistryOperation(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/PerformRegistryOperation',
            terminal__pb2.RegistryOperationRequest.SerializeToString,
            terminal__pb2.RegistryOperationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SearchRegistry(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/terminal.TerminalManagement/SearchRegistry',
            terminal__pb2.RegistrySearchRequest.SerializeToString,
            terminal__pb2.RegistrySearchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
