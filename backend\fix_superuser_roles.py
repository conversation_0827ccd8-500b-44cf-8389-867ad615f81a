import sys
import os
sys.path.append(os.path.abspath("."))

from app.database import SessionLocal
from app.models.user import User
from app.crud.role import role_crud
from sqlalchemy.orm import joinedload

def fix_superuser_roles():
    """为所有is_superuser=True但没有超级管理员角色的用户自动分配超级管理员角色"""
    print("开始修复超级管理员角色...")
    
    db = SessionLocal()
    try:
        # 获取所有超级管理员用户
        superusers = db.query(User).filter(User.is_superuser == True).options(
            joinedload(User.roles)
        ).all()
        
        if not superusers:
            print("没有找到超级管理员用户")
            return
        
        # 获取超级管理员角色
        super_admin_role = role_crud.get_by_code(db, code="super_admin")
        if not super_admin_role:
            print("错误: 未找到super_admin角色")
            return
        
        fixed_count = 0
        for user in superusers:
            # 检查用户是否已有此角色
            has_role = False
            for role in user.roles:
                if role.code == "super_admin":
                    has_role = True
                    break
            
            # 如果没有此角色，添加
            if not has_role:
                if user.roles is None:
                    user.roles = []
                user.roles.append(super_admin_role)
                print(f"为用户 {user.username} 分配超级管理员角色")
                fixed_count += 1
        
        if fixed_count > 0:
            db.commit()
            print(f"成功修复 {fixed_count} 个超级管理员用户的角色")
        else:
            print("所有超级管理员用户已有正确角色，无需修复")
    
    finally:
        db.close()

if __name__ == "__main__":
    fix_superuser_roles() 