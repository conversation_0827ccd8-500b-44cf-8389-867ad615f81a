from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Column, Integer
from sqlalchemy.orm import Mapped, mapped_column

from app.models import Base

class FieldValue(Base):
    """字段值管理模型"""
    __tablename__ = "field_values"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    field_name: Mapped[str] = mapped_column(String(50), comment="字段名称")
    field_value: Mapped[str] = mapped_column(String(200), comment="字段值")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="描述")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    ) 