#!/usr/bin/env python3
"""创建终端相关的关联表"""

from app.database import engine
from sqlalchemy import text

def create_terminal_related_tables():
    """创建终端管理相关的所有表"""
    
    # 创建表的SQL语句
    create_statements = [
        # 硬件信息表
        """
        CREATE TABLE IF NOT EXISTS hardware_info (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER UNIQUE REFERENCES terminals(id) ON DELETE CASCADE,
            cpu_model VARCHAR(255),
            cpu_cores INTEGER,
            memory_total BIGINT,
            serial_number VARCHAR(255),
            manufacturer VARCHAR(255),
            model VARCHAR(255),
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 磁盘信息表
        """
        CREATE TABLE IF NOT EXISTS disk_info (
            id SERIAL PRIMARY KEY,
            hardware_id INTEGER REFERENCES hardware_info(id) ON DELETE CASCADE,
            name VARCHAR(50),
            total_space BIGINT,
            free_space BIGINT,
            filesystem VARCHAR(50),
            mount_point VARCHAR(255),
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 操作系统信息表
        """
        CREATE TABLE IF NOT EXISTS os_info (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER UNIQUE REFERENCES terminals(id) ON DELETE CASCADE,
            name VARCHAR(100),
            version VARCHAR(100),
            build VARCHAR(100),
            architecture VARCHAR(20),
            install_date VARCHAR(50),
            installed_updates JSONB DEFAULT '[]',
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 安全信息表
        """
        CREATE TABLE IF NOT EXISTS security_info (
            id SERIAL PRIMARY KEY,
            os_info_id INTEGER UNIQUE REFERENCES os_info(id) ON DELETE CASCADE,
            firewall_enabled BOOLEAN DEFAULT FALSE,
            antivirus VARCHAR(255),
            antivirus_enabled BOOLEAN DEFAULT FALSE,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 软件信息表
        """
        CREATE TABLE IF NOT EXISTS software (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER REFERENCES terminals(id) ON DELETE CASCADE,
            name VARCHAR(255),
            version VARCHAR(100),
            publisher VARCHAR(255),
            install_date VARCHAR(50),
            size BIGINT,
            install_location VARCHAR(255),
            is_compliant BOOLEAN DEFAULT TRUE,
            usage_notes TEXT,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 网络信息表
        """
        CREATE TABLE IF NOT EXISTS network_info (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER UNIQUE REFERENCES terminals(id) ON DELETE CASCADE,
            hostname VARCHAR(255),
            domain VARCHAR(255),
            dns_servers JSONB DEFAULT '[]',
            default_gateway VARCHAR(50),
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 网络接口表
        """
        CREATE TABLE IF NOT EXISTS network_interfaces (
            id SERIAL PRIMARY KEY,
            network_info_id INTEGER REFERENCES network_info(id) ON DELETE CASCADE,
            name VARCHAR(255),
            mac_address VARCHAR(50),
            ip_address VARCHAR(50),
            subnet_mask VARCHAR(50),
            dhcp_enabled BOOLEAN DEFAULT TRUE,
            is_connected BOOLEAN DEFAULT TRUE,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 用户登录信息表
        """
        CREATE TABLE IF NOT EXISTS user_login_info (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER UNIQUE REFERENCES terminals(id) ON DELETE CASCADE,
            username VARCHAR(255),
            full_name VARCHAR(255),
            login_time VARCHAR(50),
            domain VARCHAR(255),
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # 终端命令表
        """
        CREATE TABLE IF NOT EXISTS terminal_commands (
            id SERIAL PRIMARY KEY,
            terminal_id INTEGER REFERENCES terminals(id) ON DELETE CASCADE,
            type VARCHAR(50),
            content TEXT,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            sent_time TIMESTAMP,
            execute_time TIMESTAMP,
            timeout INTEGER DEFAULT 3600,
            status VARCHAR(20) DEFAULT 'pending',
            result TEXT,
            error TEXT,
            execution_duration INTEGER
        )
        """
    ]
    
    with engine.connect() as conn:
        for i, sql in enumerate(create_statements, 1):
            try:
                print(f"创建表 {i}/9...")
                conn.execute(text(sql))
                print(f"✅ 表 {i} 创建成功")
            except Exception as e:
                print(f"❌ 表 {i} 创建失败: {e}")
        
        # 提交事务
        conn.commit()

def verify_tables():
    """验证表创建是否成功"""
    tables_to_check = [
        'hardware_info', 'disk_info', 'os_info', 'security_info', 
        'software', 'network_info', 'network_interfaces', 
        'user_login_info', 'terminal_commands'
    ]
    
    with engine.connect() as conn:
        print("\n📋 验证表创建结果:")
        print("-" * 50)
        
        for table in tables_to_check:
            try:
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = '{table}'
                """))
                exists = result.scalar() > 0
                status = "✅ 存在" if exists else "❌ 不存在"
                print(f"{table:<20} {status}")
            except Exception as e:
                print(f"{table:<20} ❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🎯 创建终端管理相关表")
    print("=" * 50)
    print()
    
    try:
        # 创建表
        create_terminal_related_tables()
        
        print("\n" + "=" * 50)
        print("✅ 所有表创建完成!")
        
        # 验证结果
        verify_tables()
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")

if __name__ == "__main__":
    main() 