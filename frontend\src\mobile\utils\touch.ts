import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useVibrate } from '@vueuse/core'

// 触摸手势方向
export type SwipeDirection = 'up' | 'down' | 'left' | 'right' | 'none'

// 触摸事件类型
export interface TouchEventData {
  startX: number
  startY: number
  currentX: number
  currentY: number
  deltaX: number
  deltaY: number
  distance: number
  direction: SwipeDirection
  duration: number
  velocity: number
}

// 触摸配置选项
export interface TouchOptions {
  threshold?: number
  enableVibrate?: boolean
  debounce?: number
}

/**
 * 简化版触摸手势识别
 */
export function useTouch(
  targetElement: HTMLElement | null,
  options: TouchOptions = {}
) {
  const {
    threshold = 50,
    enableVibrate = true,
    debounce = 100
  } = options

  // 触觉反馈
  const { vibrate, isSupported: isVibrateSupported } = useVibrate({
    pattern: [30]
  })

  const isTouching = ref(false)
  const touchData = ref<TouchEventData>({
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    deltaX: 0,
    deltaY: 0,
    distance: 0,
    direction: 'none',
    duration: 0,
    velocity: 0
  })

  let startTime = 0
  let lastTouchTime = 0

  const getSwipeDirection = (deltaX: number, deltaY: number): SwipeDirection => {
    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    if (absDeltaX < threshold && absDeltaY < threshold) {
      return 'none'
    }

    if (absDeltaX > absDeltaY) {
      return deltaX > 0 ? 'right' : 'left'
    } else {
      return deltaY > 0 ? 'down' : 'up'
    }
  }

  const handleTouchStart = (event: TouchEvent) => {
    const touch = event.touches[0]
    if (!touch) return

    isTouching.value = true
    startTime = Date.now()
    
    touchData.value = {
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      deltaX: 0,
      deltaY: 0,
      distance: 0,
      direction: 'none',
      duration: 0,
      velocity: 0
    }
  }

  const handleTouchMove = (event: TouchEvent) => {
    if (!isTouching.value) return

    const touch = event.touches[0]
    if (!touch) return

    const currentTime = Date.now()
    const deltaX = touch.clientX - touchData.value.startX
    const deltaY = touch.clientY - touchData.value.startY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = currentTime - startTime
    const velocity = distance / Math.max(duration, 1) * 1000

    touchData.value = {
      ...touchData.value,
      currentX: touch.clientX,
      currentY: touch.clientY,
      deltaX,
      deltaY,
      distance,
      direction: getSwipeDirection(deltaX, deltaY),
      duration,
      velocity
    }
  }

  const handleTouchEnd = () => {
    if (!isTouching.value) return

    const currentTime = Date.now()
    
    if (currentTime - lastTouchTime < debounce) {
      return
    }
    lastTouchTime = currentTime

    isTouching.value = false
    
    if (enableVibrate && isVibrateSupported.value && touchData.value.direction !== 'none') {
      vibrate()
    }

    setTimeout(() => {
      touchData.value = {
        startX: 0,
        startY: 0,
        currentX: 0,
        currentY: 0,
        deltaX: 0,
        deltaY: 0,
        distance: 0,
        direction: 'none',
        duration: 0,
        velocity: 0
      }
    }, 100)
  }

  onMounted(() => {
    if (!targetElement) return

    targetElement.addEventListener('touchstart', handleTouchStart, { passive: true })
    targetElement.addEventListener('touchmove', handleTouchMove, { passive: true })
    targetElement.addEventListener('touchend', handleTouchEnd, { passive: true })
  })

  onUnmounted(() => {
    if (!targetElement) return

    targetElement.removeEventListener('touchstart', handleTouchStart)
    targetElement.removeEventListener('touchmove', handleTouchMove)
    targetElement.removeEventListener('touchend', handleTouchEnd)
  })

  return {
    isTouching,
    touchData: computed(() => touchData.value),
    isSwipeLeft: computed(() => touchData.value.direction === 'left'),
    isSwipeRight: computed(() => touchData.value.direction === 'right'),
    isSwipeUp: computed(() => touchData.value.direction === 'up'),
    isSwipeDown: computed(() => touchData.value.direction === 'down'),
    vibrate: () => {
      if (enableVibrate && isVibrateSupported.value) {
        vibrate()
      }
    }
  }
}

/**
 * 触摸反馈增强
 */
export function useTouchFeedback(
  targetElement: HTMLElement | null,
  options: {
    enableVibrate?: boolean
    feedbackClass?: string
    feedbackDuration?: number
  } = {}
) {
  const {
    enableVibrate = true,
    feedbackClass = 'mobile-touch-feedback',
    feedbackDuration = 150
  } = options

  const { vibrate, isSupported: isVibrateSupported } = useVibrate({
    pattern: [25]
  })

  const isPressed = ref(false)

  const handleTouchStart = () => {
    isPressed.value = true
    
    if (enableVibrate && isVibrateSupported.value) {
      vibrate()
    }
    
    if (targetElement && feedbackClass) {
      targetElement.classList.add(feedbackClass)
    }
  }

  const handleTouchEnd = () => {
    if (targetElement && feedbackClass) {
      setTimeout(() => {
        targetElement.classList.remove(feedbackClass)
        isPressed.value = false
      }, feedbackDuration)
    } else {
      isPressed.value = false
    }
  }

  onMounted(() => {
    if (!targetElement) return

    targetElement.addEventListener('touchstart', handleTouchStart, { passive: true })
    targetElement.addEventListener('touchend', handleTouchEnd, { passive: true })
    targetElement.addEventListener('touchcancel', handleTouchEnd, { passive: true })
  })

  onUnmounted(() => {
    if (!targetElement) return

    targetElement.removeEventListener('touchstart', handleTouchStart)
    targetElement.removeEventListener('touchend', handleTouchEnd)
    targetElement.removeEventListener('touchcancel', handleTouchEnd)
  })

  return {
    isPressed
  }
}

/**
 * 长按手势检测
 */
export function useLongPress(
  targetElement: HTMLElement | null,
  callback: (event: TouchEvent) => void,
  options: {
    delay?: number
    enableVibrate?: boolean
  } = {}
) {
  const { delay = 500, enableVibrate = true } = options

  const { vibrate, isSupported: isVibrateSupported } = useVibrate({
    pattern: [50, 100, 50]
  })

  let pressTimer: number | null = null
  const isLongPressed = ref(false)

  const handleTouchStart = (event: TouchEvent) => {
    pressTimer = window.setTimeout(() => {
      isLongPressed.value = true
      
      if (enableVibrate && isVibrateSupported.value) {
        vibrate()
      }
      
      callback(event)
    }, delay)
  }

  const handleTouchEnd = () => {
    if (pressTimer) {
      clearTimeout(pressTimer)
      pressTimer = null
    }
    isLongPressed.value = false
  }

  onMounted(() => {
    if (!targetElement) return

    targetElement.addEventListener('touchstart', handleTouchStart, { passive: true })
    targetElement.addEventListener('touchend', handleTouchEnd, { passive: true })
    targetElement.addEventListener('touchcancel', handleTouchEnd, { passive: true })
    targetElement.addEventListener('touchmove', handleTouchEnd, { passive: true })
  })

  onUnmounted(() => {
    if (!targetElement) return

    targetElement.removeEventListener('touchstart', handleTouchStart)
    targetElement.removeEventListener('touchend', handleTouchEnd)
    targetElement.removeEventListener('touchcancel', handleTouchEnd)
    targetElement.removeEventListener('touchmove', handleTouchEnd)
  })

  return {
    isLongPressed
  }
}

/**
 * 防止页面滚动
 */
export function usePreventScroll(
  targetElement: HTMLElement | null,
  enabled = true
) {
  const handleTouchMove = (event: TouchEvent) => {
    if (enabled) {
      event.preventDefault()
    }
  }

  onMounted(() => {
    if (!targetElement) return
    targetElement.addEventListener('touchmove', handleTouchMove, { passive: false })
  })

  onUnmounted(() => {
    if (!targetElement) return
    targetElement.removeEventListener('touchmove', handleTouchMove)
  })
} 