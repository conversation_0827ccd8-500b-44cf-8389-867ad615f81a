# OPS Platform 部署指南

## 概述

本文档描述如何正确部署OPS Platform，确保数据库初始化和角色权限配置正确。

## 问题背景

之前的部署可能存在以下问题：
- 创建了重复的`admin`角色而不是标准的`super_admin`角色
- 角色权限配置不一致
- 不同环境的数据初始化结果不同

## 解决方案

我们提供了标准化的部署脚本，确保：
- ✅ 统一的角色体系：`super_admin`、`asset_admin`、`normal_user`
- ✅ 完整的权限配置
- ✅ 幂等的部署过程（可重复执行）
- ✅ 数据库迁移和初始化自动化

## 部署步骤

### 1. 新环境部署（推荐）

对于全新的环境，使用标准化部署脚本：

```bash
# 进入后端目录
cd backend

# 使用uv运行部署脚本
uv run python deploy_production.py
```

这个脚本会：
1. 检查环境配置
2. 执行数据库迁移
3. 初始化标准角色和权限
4. 创建管理员用户
5. 验证部署结果

### 2. 现有环境修复

如果已有环境存在角色问题，可以手动修复：

```bash
# 运行修复脚本（之前已经执行过）
uv run python -c "
from app.database import SessionLocal
from app.crud.role import role_crud
from app.crud.user import user_crud

db = SessionLocal()
try:
    # 检查当前角色状态
    roles = role_crud.get_multi(db)
    print('当前角色:', [r.name for r in roles])
    
    admin_user = user_crud.get_by_username(db, username='admin')
    if admin_user:
        user_roles = [r.name for r in admin_user.roles]
        print('Admin用户角色:', user_roles)
finally:
    db.close()
"
```

### 3. 验证部署结果

部署完成后，验证系统状态：

```bash
uv run python -c "
from app.database import SessionLocal
from app.crud.role import role_crud
from app.crud.user import user_crud

db = SessionLocal()
try:
    print('=== 角色验证 ===')
    roles = role_crud.get_multi(db)
    for role in roles:
        perm_count = len(role.permissions) if role.permissions else 0
        print(f'{role.name} ({role.code}): {perm_count}个权限')
    
    print('\n=== 用户验证 ===')
    admin_user = user_crud.get_by_username(db, username='admin')
    if admin_user:
        user_roles = [r.name for r in admin_user.roles]
        print(f'Admin用户角色: {user_roles}')
finally:
    db.close()
"
```

预期结果：
```
=== 角色验证 ===
超级管理员 (super_admin): 48个权限
资产管理员 (asset_admin): 18个权限
普通用户 (normal_user): 2个权限

=== 用户验证 ===
Admin用户角色: ['超级管理员']
```

## 标准角色体系

### 超级管理员 (super_admin)
- **权限范围**：所有模块的所有权限
- **适用人员**：系统管理员
- **权限数量**：48个（包含所有功能）

### 资产管理员 (asset_admin)
- **权限范围**：资产管理、盘点管理、基础信息
- **适用人员**：资产管理专员
- **权限数量**：18个
- **具体权限**：
  - 资产：查看、添加、编辑、删除、导入、导出、字段管理
  - 盘点：查看、创建、编辑、记录查看、记录编辑、报告
  - 基础信息：查看、人员信息查看

### 普通用户 (normal_user)
- **权限范围**：基础查看权限
- **适用人员**：一般员工
- **权限数量**：2个
- **具体权限**：
  - 资产：查看
  - 基础信息：查看

## 权限模块分布

系统权限按功能模块组织：

- **system**: 11个权限（用户管理、角色管理等）
- **asset**: 7个权限（资产管理相关）
- **inventory**: 6个权限（盘点管理相关）
- **email**: 10个权限（邮箱管理相关）
- **ad**: 5个权限（AD管理相关）
- **terminal**: 5个权限（终端管理相关）
- **basic-info**: 2个权限（基础信息相关）
- **ldap**: 2个权限（LDAP认证相关）

## 部署最佳实践

### 1. 环境准备
```bash
# 确保依赖安装完整
uv sync

# 检查数据库连接
uv run python -c "from app.database import engine; print('数据库连接正常')"
```

### 2. 数据库备份（生产环境）
```bash
# PostgreSQL备份
pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 或使用项目备份脚本（如果有）
```

### 3. 部署执行
```bash
# 开发/测试环境
uv run python deploy_production.py

# 生产环境（建议在维护窗口执行）
uv run python deploy_production.py
```

### 4. 部署后检查
- ✅ 检查Web服务启动正常
- ✅ 使用admin/admin123登录前端
- ✅ 验证角色管理页面显示3个角色
- ✅ 验证权限分配正确
- ✅ 修改默认密码

## 故障排除

### 1. 角色重复问题
如果发现有重复的admin角色：
```bash
# 检查角色
uv run python -c "
from app.database import SessionLocal
from app.crud.role import role_crud
db = SessionLocal()
roles = role_crud.get_multi(db)
for r in roles:
    print(f'ID: {r.id}, Code: {r.code}, Name: {r.name}')
db.close()
"

# 如需要，手动清理重复角色（谨慎操作）
```

### 2. 权限不足问题
如果登录后看不到某些功能：
1. 检查用户角色分配
2. 检查角色权限配置
3. 重新分配权限

### 3. 数据库连接问题
检查配置文件中的数据库设置：
```python
# app/config.py
POSTGRES_HOST = "localhost"
POSTGRES_PORT = 5432
POSTGRES_USER = "your_user"
POSTGRES_PASSWORD = "your_password"
POSTGRES_DB = "your_database"
```

## 安全建议

1. **修改默认密码**：部署后立即修改admin用户密码
2. **权限最小化**：为用户分配最小必要权限
3. **定期审核**：定期检查用户权限分配
4. **访问日志**：启用访问日志监控

## 技术细节

### 部署脚本特性
- **幂等性**：可重复执行，不会重复创建数据
- **完整性检查**：自动验证部署结果
- **错误处理**：完善的错误处理和回滚机制
- **日志记录**：详细的操作日志

### 文件说明
- `deploy_production.py`: 生产环境部署脚本
- `app/initial_data.py`: 修改后的数据初始化脚本
- `README_部署指南.md`: 本文档

---

**注意**：请在生产环境部署前，先在测试环境验证所有步骤。如有问题，请及时联系开发团队。 