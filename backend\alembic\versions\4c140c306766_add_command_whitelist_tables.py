"""add_command_whitelist_tables

Revision ID: 4c140c306766
Revises: 0edde6415892
Create Date: 2025-06-26 18:03:37.197605

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4c140c306766'
down_revision: Union[str, None] = '0edde6415892'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('command_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='分类名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='分类描述'),
    sa.Column('required_permission', sa.String(length=100), nullable=False, comment='所需权限'),
    sa.Column('is_active', sa.<PERSON>(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_command_categories_id'), 'command_categories', ['id'], unique=False)
    op.create_table('command_whitelist',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False, comment='分类ID'),
    sa.Column('command', sa.String(length=500), nullable=False, comment='命令内容'),
    sa.Column('name', sa.String(length=200), nullable=False, comment='命令名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='命令描述'),
    sa.Column('example', sa.Text(), nullable=True, comment='使用示例'),
    sa.Column('timeout', sa.Integer(), nullable=True, comment='超时时间(秒)'),
    sa.Column('admin_required', sa.Boolean(), nullable=True, comment='是否需要管理员权限'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('security_level', sa.String(length=20), nullable=True, comment='安全级别: PUBLIC, OPERATOR, ADMIN'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['category_id'], ['command_categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_command_whitelist_id'), 'command_whitelist', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_command_whitelist_id'), table_name='command_whitelist')
    op.drop_table('command_whitelist')
    op.drop_index(op.f('ix_command_categories_id'), table_name='command_categories')
    op.drop_table('command_categories')
    # ### end Alembic commands ###
