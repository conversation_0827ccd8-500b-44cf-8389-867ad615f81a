"""add asset change log table

Revision ID: ad2d1f08372b
Revises: cda8fed61046
Create Date: 2024-12-18 17:15:27.025313

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ad2d1f08372b'
down_revision: Union[str, None] = 'cda8fed61046'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_change_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('asset_id', sa.Integer(), nullable=False),
    sa.Column('field', sa.String(length=50), nullable=False, comment='变更字段'),
    sa.Column('old_value', sa.Text(), nullable=True, comment='原值'),
    sa.Column('new_value', sa.Text(), nullable=True, comment='新值'),
    sa.Column('change_type', sa.String(length=20), nullable=False, comment='变更类型'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_change_logs_id'), 'asset_change_logs', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_asset_change_logs_id'), table_name='asset_change_logs')
    op.drop_table('asset_change_logs')
    # ### end Alembic commands ###
