# LDAP用户权限分配修复

## 问题描述
LDAP用户登录后无法获得正确的权限，即使在系统中将用户权限从普通用户改为超级管理员也无法生效。

## 问题分析

### 根本原因
1. **角色代码映射错误**：前端LDAP配置页面中的"超级管理员"选项值为"superuser"，但数据库中实际的超级管理员角色代码是"super_admin"
2. **角色分配失败**：LDAP用户创建时，系统尝试分配不存在的角色，导致用户没有获得任何角色和权限
3. **权限检查逻辑**：权限验证依赖于用户的角色，没有角色的用户无法通过权限检查

### 技术细节
- LDAP配置表的`default_role`字段存储角色代码字符串
- 系统中的标准角色代码：`normal_user`、`asset_admin`、`super_admin`
- 前端页面错误使用了：`user`、`admin`、`superuser`

## 修复方案

### 1. 修复前端角色选项映射
**文件**：`frontend/src/views/system/components/LdapConfigManagement.vue`

**修改内容**：
```vue
<!-- 修复前 -->
<el-option label="普通用户" value="user" />
<el-option label="管理员" value="admin" />
<el-option label="超级管理员" value="superuser" />

<!-- 修复后 -->
<el-option label="普通用户" value="normal_user" />
<el-option label="资产管理员" value="asset_admin" />
<el-option label="超级管理员" value="super_admin" />
```

**表单默认值修复**：
- 将`default_role`默认值从`'user'`改为`'normal_user'`

### 2. 数据库配置修复
**脚本**：`backend/scripts/fix_ldap_default_role.py`

**功能**：
- 检查现有LDAP配置的`default_role`字段
- 将旧的角色代码映射到正确的代码：
  - `user` → `normal_user`
  - `admin` → `asset_admin`
  - `superuser` → `super_admin`

**执行结果**：
- 检查到1个LDAP配置
- 配置已使用正确的角色代码`normal_user`，无需修复

### 3. 增强LDAP用户创建逻辑
**文件**：`backend/app/services/ldap_auth.py`

**改进内容**：

#### 新用户创建增强
```python
# 分配默认角色
if config.default_role:
    from ..crud.role import role_crud
    default_role = role_crud.get_by_code(self.db, code=config.default_role)
    if default_role:
        if new_user.roles is None:
            new_user.roles = []
        new_user.roles.append(default_role)
        self.db.commit()
        self.db.refresh(new_user)
        self.logger.info(f"为用户 {username} 分配默认角色: {config.default_role} (角色ID: {default_role.id})")
        
        # 如果是超级管理员角色，同时设置is_superuser标志
        if config.default_role == "super_admin":
            new_user.is_superuser = True
            self.db.commit()
            self.db.refresh(new_user)
            self.logger.info(f"设置用户 {username} 为超级管理员")
    else:
        self.logger.error(f"配置的默认角色 '{config.default_role}' 不存在，用户 {username} 未分配任何角色")
else:
    self.logger.warning(f"LDAP配置未指定默认角色，用户 {username} 未分配任何角色")
```

#### 现有用户更新增强
```python
# 检查并更新用户角色（如果LDAP配置的默认角色发生了变化）
if config.default_role:
    from ..crud.role import role_crud
    default_role = role_crud.get_by_code(self.db, code=config.default_role)
    if default_role:
        # 检查用户是否已有此角色
        has_role = any(role.code == config.default_role for role in existing_user.roles)
        
        if not has_role:
            if existing_user.roles is None:
                existing_user.roles = []
            existing_user.roles.append(default_role)
            self.logger.info(f"为现有用户 {username} 添加默认角色: {config.default_role}")
            
            # 如果是超级管理员角色，同时设置is_superuser标志
            if config.default_role == "super_admin" and not existing_user.is_superuser:
                existing_user.is_superuser = True
                self.logger.info(f"设置现有用户 {username} 为超级管理员")
```

## 修复效果

### 解决的问题
1. ✅ **角色代码映射**：前端LDAP配置页面现在使用正确的角色代码
2. ✅ **角色分配**：LDAP用户创建时能正确分配角色
3. ✅ **权限生效**：用户获得相应角色后能通过权限检查
4. ✅ **日志记录**：增加详细的角色分配日志，便于调试
5. ✅ **动态更新**：现有用户在登录时会根据最新的LDAP配置更新角色

### 测试建议
1. **新用户测试**：创建新的LDAP用户，验证角色分配是否正确
2. **现有用户测试**：现有LDAP用户重新登录，验证权限是否生效
3. **配置变更测试**：修改LDAP配置的默认角色，验证用户权限是否相应变化
4. **权限验证测试**：验证不同角色用户的菜单和功能访问权限

## 后续建议

### 权限管理优化
1. 考虑添加LDAP用户角色映射配置，支持基于LDAP属性的动态角色分配
2. 实现角色权限的批量管理功能
3. 添加用户权限变更的审计日志

### 监控和告警
1. 添加LDAP用户权限分配失败的告警机制
2. 实现权限变更的实时通知
3. 定期检查用户权限一致性

## 完成时间
2025-06-23 23:30 