<template>
  <div class="ad-config">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Setting /></el-icon>
        <h2 class="page-title">AD 配置</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>AD 管理</el-breadcrumb-item>
        <el-breadcrumb-item>AD 配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <el-form
        ref="formRef"
        :model="configForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="服务器地址" prop="server">
          <el-input 
            v-model="configForm.server" 
            placeholder="例如: ***********"
          />
        </el-form-item>
        
        <el-form-item label="使用SSL" prop="use_ssl">
          <el-switch v-model="configForm.use_ssl" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input 
            v-model.number="configForm.port" 
            type="number"
            placeholder="1-65535"
            style="width: 100%"
          />
          <template #tip>
            <span class="el-form-item__tip">
              LDAPS默认使用636端口，LDAP默认使用389端口
            </span>
          </template>
        </el-form-item>
        
        <el-form-item label="域名" prop="domain">
          <el-input 
            v-model="configForm.domain" 
            placeholder="例如: example.com"
          />
        </el-form-item>
        
        <el-form-item label="管理员账号" prop="username">
          <el-input v-model="configForm.username" placeholder="例如: <EMAIL>" />
        </el-form-item>
        
        <el-form-item label="管理员密码" prop="password">
          <el-input
            v-model="configForm.password"
            type="password"
            show-password
            placeholder="请输入管理员密码"
          />
        </el-form-item>
        
        <el-form-item label="搜索基础DN" prop="search_base">
          <el-input v-model="configForm.search_base" placeholder="例如: DC=example,DC=com" />
        </el-form-item>
        
        <el-form-item>
          <Authority permission="ad:config">
            <el-button type="primary" @click="handleSubmit">保存配置</el-button>
          </Authority>
          <Authority permission="ad:config">
            <el-button @click="handleTest">测试连接</el-button>
          </Authority>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getADConfig, updateADConfig, testADConfig } from '@/api/ad'
import { Setting } from '@element-plus/icons-vue'

const formRef = ref(null)
const configForm = ref({
  server: '',
  domain: '',
  username: '',
  password: '',
  search_base: '',
  use_ssl: false,
  port: 389
})

const rules = {
  server: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  domain: [{ required: true, message: '请输入域名', trigger: 'blur' }],
  username: [{ required: true, message: '请输入管理员账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入管理员密码', trigger: 'blur' }],
  search_base: [{ required: true, message: '请输入搜索基础DN', trigger: 'blur' }],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { 
      type: 'number', 
      min: 1, 
      max: 65535, 
      message: '端口号必须在1-65535之间', 
      trigger: 'blur' 
    }
  ]
}

const fetchConfig = async () => {
  try {
    const response = await getADConfig()
    console.log('API响应:', response)
    const { data } = response
    console.log('获取到的AD配置:', data)
    if (data) {
      configForm.value = {
        server: data.server || '',
        domain: data.domain || '',
        username: data.username || '',
        password: '',
        search_base: data.search_base || '',
        use_ssl: data.use_ssl || false,
        port: data.port || 389
      }
      console.log('设置后的表单值:', configForm.value)
    }
  } catch (error) {
    console.error('获取AD配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    await updateADConfig(configForm.value)
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存AD配置失败:', error)
    ElMessage.error(error.response?.data?.detail || '保存失败')
  }
}

const handleTest = async () => {
  try {
    console.log('开始测试AD连接')
    if (formRef.value && formRef.value.isModified) {
      console.log('表单已修改，使用新配置进行测试')
      await formRef.value.validate()
      const { data } = await testADConfig(configForm.value)
      console.log('测试结果:', data)
      if (data.status) {
        ElMessage.success(data.message || '连接测试成功')
      } else {
        ElMessage.error(data.message || '连接测试失败')
      }
    } else {
      console.log('表单未修改，使用当前配置进行测试')
      const { data } = await testADConfig()
      console.log('测试结果:', data)
      if (data.status) {
        ElMessage.success(data.message || '连接测试成功')
      } else {
        ElMessage.error(data.message || '连接测试失败')
      }
    }
  } catch (error) {
    console.error('测试AD连接失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(`测试失败: ${error.response.data.detail}`)
    } else if (error.message) {
      ElMessage.error(`测试失败: ${error.message}`)
    } else {
      ElMessage.error('测试失败')
    }
  }
}

onMounted(() => {
  fetchConfig()
})
</script>

<style scoped>
.ad-config {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  max-width: 800px;
  margin: 0 auto 20px;
}
</style> 