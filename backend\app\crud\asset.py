from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from fastapi import HTTPException
from app.crud.base import CRUDBase
from app.models.asset import Asset
from app.schemas.asset import AssetCreate, AssetUpdate
from sqlalchemy import desc, asc, or_
from fastapi.encoders import jsonable_encoder
from app.crud import asset_change_log as change_log_crud
from app.crud.asset_settings import asset_settings_crud

class CRUDAsset(CRUDBase[Asset, AssetCreate, AssetUpdate]):
    def get_by_asset_number(self, db: Session, *, asset_number: str) -> Optional[Asset]:
        """通过资产编号获取资产"""
        return db.query(Asset).filter(Asset.asset_number == asset_number).first()

    def get_last_asset_number(self, db: Session, *, prefix: str) -> Optional[str]:
        """获取指定前缀的最后一个资产编号"""
        return db.query(Asset.asset_number)\
            .filter(Asset.asset_number.like(f"{prefix}%"))\
            .order_by(desc(Asset.asset_number))\
            .first()

    def create(self, db: Session, *, obj_in: AssetCreate) -> Asset:
        """创建资产"""
        # 获取公司的资产编号规则
        settings = asset_settings_crud.get_by_company(db, company=obj_in.company)
        if not settings:
            raise HTTPException(
                status_code=400,
                detail=f"公司 {obj_in.company} 未配置资产编号规则"
            )

        # 如果没有提供资产编号，则自动生成
        if not obj_in.asset_number:
            rule = settings.asset_number_rule
            prefix = rule["prefix"]
            number_length = rule["number_length"]
            start_number = rule["start_number"]

            # 获取最后一个资产编号
            last_number = self.get_last_asset_number(db, prefix=prefix)
            if last_number:
                # 提取最后一个编号的数字部分
                last_number = last_number[0]  # 因为 get_last_asset_number 返回的是元组
                current_number = int(last_number[len(prefix):]) + 1
            else:
                current_number = start_number

            # 生成新的资产编号
            asset_number = f"{prefix}{str(current_number).zfill(number_length)}"
            obj_in.asset_number = asset_number
        
        # 检查资产编号是否已存在
        if self.get_by_asset_number(db, asset_number=obj_in.asset_number):
            raise HTTPException(
                status_code=400,
                detail=f"资产编号 {obj_in.asset_number} 已存在"
            )

        try:
            db_obj = Asset(
                company=obj_in.company,
                name=obj_in.name,
                asset_number=obj_in.asset_number,
                status=obj_in.status,
                category=obj_in.category,
                specification=obj_in.specification,
                purchase_date=obj_in.purchase_date,
                retirement_date=obj_in.retirement_date,
                custodian=obj_in.custodian,
                custodian_job_number=obj_in.custodian_job_number,
                custodian_department=obj_in.custodian_department,
                user=obj_in.user,
                user_job_number=obj_in.user_job_number,
                user_department=obj_in.user_department,
                location=obj_in.location,
                inspector=obj_in.inspector,
                inspector_job_number=obj_in.inspector_job_number,
                remarks=obj_in.remarks,
                production_number=obj_in.production_number,
                price=obj_in.price,
                supplier=obj_in.supplier,
                manufacturer=obj_in.manufacturer,
                purchaser=obj_in.purchaser,
                purchaser_job_number=obj_in.purchaser_job_number,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            db.rollback()
            raise HTTPException(
                status_code=400,
                detail=f"资产编号 {obj_in.asset_number} 已存在"
            )

    def update(
        self,
        db: Session,
        *,
        db_obj: Asset,
        obj_in: Union[AssetUpdate, Dict[str, Any]]
    ) -> Asset:
        obj_data = jsonable_encoder(db_obj)
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
            
        # 记录变更
        for field in update_data:
            if field in obj_data and obj_data[field] != update_data[field]:
                # 只有当字段值实际发生变化时才创建变更记录
                change_log_crud.create_asset_change_log(
                    db,
                    asset_id=db_obj.id,
                    field=field,
                    old_value=str(obj_data[field]) if obj_data[field] is not None else None,
                    new_value=str(update_data[field]) if update_data[field] is not None else None,
                    change_type="update"
                )
        
        # 更新资产信息
        for field in update_data:
            if field in obj_data:
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Dict[str, Any] = None,
        sort_by: str = None,
        sort_order: str = None
    ) -> List[Asset]:
        """获取资产列表，支持过滤和排序"""
        query = db.query(self.model)
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        search_field = filters.get("search_field", "asset_number")
                        search_term = f"%{value}%"
                        
                        if search_field == "asset_number":
                            query = query.filter(self.model.asset_number.ilike(search_term))
                        elif search_field == "name":
                            query = query.filter(self.model.name.ilike(search_term))
                        elif search_field == "category":
                            query = query.filter(self.model.category.ilike(search_term))
                        elif search_field == "specification":
                            query = query.filter(self.model.specification.ilike(search_term))
                        elif search_field == "person_name":
                            # 搜索姓名（领用人或使用人）
                            query = query.filter(
                                or_(
                                    self.model.custodian.ilike(search_term),
                                    self.model.user.ilike(search_term)
                                )
                            )
                        elif search_field == "job_number":
                            # 搜索工号（领用人或使用人工号）
                            query = query.filter(
                                or_(
                                    self.model.custodian_job_number.ilike(search_term),
                                    self.model.user_job_number.ilike(search_term)
                                )
                            )
                        elif search_field == "department":
                            # 搜索部门（领用人或使用人部门）
                            query = query.filter(
                                or_(
                                    self.model.custodian_department.ilike(search_term),
                                    self.model.user_department.ilike(search_term)
                                )
                            )
                        else:
                            # 默认搜索资产编号
                            query = query.filter(self.model.asset_number.ilike(search_term))
                    elif field == "purchase_date_start":
                        query = query.filter(self.model.purchase_date >= value)
                    elif field == "purchase_date_end":
                        query = query.filter(self.model.purchase_date <= value)
                    elif field == "retirement_date_start":
                        query = query.filter(self.model.retirement_date >= value)
                    elif field == "retirement_date_end":
                        query = query.filter(self.model.retirement_date <= value)
                    elif hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)
        
        # 应用排序：如果没有指定排序字段，默认按资产编号升序排序
        if sort_by and hasattr(self.model, sort_by):
            sort_field = getattr(self.model, sort_by)
            if sort_order == 'desc':
                query = query.order_by(desc(sort_field))
            else:
                query = query.order_by(asc(sort_field))
        else:
            # 默认按资产编号升序排序
            query = query.order_by(asc(self.model.asset_number))
        
        return query.offset(skip).limit(limit).all()

    def get_count(
        self,
        db: Session,
        *,
        filters: Dict[str, Any] = None
    ) -> int:
        """获取资产总数，支持过滤"""
        query = db.query(self.model)
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        search_field = filters.get("search_field", "asset_number")
                        search_term = f"%{value}%"
                        
                        if search_field == "asset_number":
                            query = query.filter(self.model.asset_number.ilike(search_term))
                        elif search_field == "name":
                            query = query.filter(self.model.name.ilike(search_term))
                        elif search_field == "category":
                            query = query.filter(self.model.category.ilike(search_term))
                        elif search_field == "specification":
                            query = query.filter(self.model.specification.ilike(search_term))
                        elif search_field == "person_name":
                            # 搜索姓名（领用人或使用人）
                            query = query.filter(
                                or_(
                                    self.model.custodian.ilike(search_term),
                                    self.model.user.ilike(search_term)
                                )
                            )
                        elif search_field == "job_number":
                            # 搜索工号（领用人或使用人工号）
                            query = query.filter(
                                or_(
                                    self.model.custodian_job_number.ilike(search_term),
                                    self.model.user_job_number.ilike(search_term)
                                )
                            )
                        elif search_field == "department":
                            # 搜索部门（领用人或使用人部门）
                            query = query.filter(
                                or_(
                                    self.model.custodian_department.ilike(search_term),
                                    self.model.user_department.ilike(search_term)
                                )
                            )
                        else:
                            # 默认搜索资产编号
                            query = query.filter(self.model.asset_number.ilike(search_term))
                    elif field == "purchase_date_start":
                        query = query.filter(self.model.purchase_date >= value)
                    elif field == "purchase_date_end":
                        query = query.filter(self.model.purchase_date <= value)
                    elif field == "retirement_date_start":
                        query = query.filter(self.model.retirement_date >= value)
                    elif field == "retirement_date_end":
                        query = query.filter(self.model.retirement_date <= value)
                    elif hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)
        return query.count()

    def remove(self, db: Session, *, id: int) -> Asset:
        """
        删除资产，确保不会删除关联的资产设置
        """
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj

asset_crud = CRUDAsset(Asset)
