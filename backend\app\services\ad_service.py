from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi import HTTPException, status
from .. import models, schemas
from ..utils import get_password_hash

async def get_ou_tree(db: Session) -> List[models.OrganizationalUnit]:
    """获取完整的OU树结构"""
    root_ous = db.query(models.OrganizationalUnit)\
        .filter(models.OrganizationalUnit.parent_dn == None)\
        .all()
    return root_ous

async def create_ou(db: Session, ou: schemas.OUCreate) -> models.OrganizationalUnit:
    """创建新的OU"""
    # 构建DN
    parent = None
    if ou.parent_dn:
        parent = db.query(models.OrganizationalUnit)\
            .filter(models.OrganizationalUnit.dn == ou.parent_dn)\
            .first()
        if not parent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="父OU不存在"
            )

    # 从数据库获取AD配置中的域名
    ad_config = db.query(models.ADConfig).first()
    if not ad_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="AD配置不存在"
        )

    # 从搜索基础中提取域名部分
    domain_part = ad_config.search_base

    # 如果没有父OU，使用域名部分
    dn = f"OU={ou.name}" + (f",{ou.parent_dn}" if ou.parent_dn else f",{domain_part}")

    db_ou = models.OrganizationalUnit(
        name=ou.name,
        description=ou.description,
        dn=dn,
        parent_dn=ou.parent_dn
    )
    db.add(db_ou)
    db.commit()
    db.refresh(db_ou)
    return db_ou

async def update_ou(
    db: Session,
    ou_id: int,
    ou: schemas.OUUpdate
) -> models.OrganizationalUnit:
    """更新OU信息"""
    db_ou = db.query(models.OrganizationalUnit).filter(models.OrganizationalUnit.id == ou_id).first()
    if not db_ou:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="OU不存在"
        )

    for field, value in ou.dict(exclude_unset=True).items():
        setattr(db_ou, field, value)

    db.commit()
    db.refresh(db_ou)
    return db_ou

async def delete_ou(db: Session, ou_id: int):
    """删除OU"""
    db_ou = db.query(models.OrganizationalUnit).filter(models.OrganizationalUnit.id == ou_id).first()
    if not db_ou:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="OU不存在"
        )

    if db_ou.children or db_ou.users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除非空OU"
        )

    db.delete(db_ou)
    db.commit()

async def get_users(db: Session, ou_dn: str) -> List[models.ADUser]:
    """获取指定OU下的用户列表"""
    return db.query(models.ADUser)\
        .filter(models.ADUser.ou_dn == ou_dn)\
        .all()

async def create_user(db: Session, user: schemas.ADUserCreate) -> models.ADUser:
    """创建AD用户"""
    # 检查OU是否存在
    ou = db.query(models.OrganizationalUnit)\
        .filter(models.OrganizationalUnit.dn == user.ou_dn)\
        .first()
    if not ou:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="OU不存在"
        )

    # 检查用户名是否已存在
    if db.query(models.ADUser)\
        .filter(models.ADUser.username == user.username)\
        .first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    user_dn = f"CN={user.username},{user.ou_dn}"
    db_user = models.ADUser(
        name=user.name,
        username=user.username,
        email=user.email,
        dn=user_dn,
        hashed_password=get_password_hash(user.password),
        ou_dn=user.ou_dn
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

async def update_user(
    db: Session,
    username: str,
    user: schemas.ADUserUpdate
) -> models.ADUser:
    """更新AD用户信息"""
    db_user = db.query(models.ADUser).filter(models.ADUser.username == username).first()
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    update_data = user.dict(exclude_unset=True)
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))

    for field, value in update_data.items():
        setattr(db_user, field, value)

    db.commit()
    db.refresh(db_user)
    return db_user

async def toggle_user_status(db: Session, username: str):
    """切换用户启用/禁用状态"""
    db_user = db.query(models.ADUser).filter(models.ADUser.username == username).first()
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    db_user.enabled = not db_user.enabled
    db.commit()