<template>
  <div class="user-detail-container">
    <div class="header">
      <div class="user-avatar">
        <el-avatar :size="64" :icon="UserFilled" />
      </div>
      <div class="user-info">
        <h2>{{ user.name }}</h2>
        <p class="username">{{ user.username }}</p>
      </div>
    </div>

    <el-divider />

    <div class="content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="显示名称">{{ user.name }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ user.username }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ user.email || '-' }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ user.department || '-' }}</el-descriptions-item>
        <el-descriptions-item label="职位">{{ user.title || '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="user.enabled ? 'success' : 'danger'">
            {{ user.enabled ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="密码过期时间">
          <template v-if="user.password_never_expires">
            <el-tag type="info" size="small">永不过期</el-tag>
          </template>
          <template v-else-if="user.password_expired">
            <el-tag type="danger" size="small">已过期</el-tag>
          </template>
          <template v-else-if="user.password_expiry_date">
            <div>
              <div>{{ formatExpiryDate(user.password_expiry_date) }}</div>
              <template v-if="user.days_until_expiry !== null">
                <el-tag 
                  :type="user.days_until_expiry <= 7 ? 'warning' : 'success'" 
                  size="small"
                >
                  {{ user.days_until_expiry }}天后过期
                </el-tag>
              </template>
            </div>
          </template>
          <template v-else>
            <span>未知</span>
          </template>
        </el-descriptions-item>
        <el-descriptions-item label="密码状态">
          <template v-if="user.password_never_expires">
            <el-tag type="info" size="small">永不过期</el-tag>
          </template>
          <template v-else-if="user.password_expired">
            <el-tag type="danger" size="small">已过期</el-tag>
          </template>
          <template v-else-if="user.days_until_expiry !== null">
            <template v-if="user.days_until_expiry <= 0">
              <el-tag type="danger" size="small">已过期</el-tag>
            </template>
            <template v-else-if="user.days_until_expiry <= 7">
              <el-tag type="warning" size="small">即将过期</el-tag>
            </template>
            <template v-else>
              <el-tag type="success" size="small">正常</el-tag>
            </template>
          </template>
          <template v-else>
            <el-tag type="info" size="small">未知</el-tag>
          </template>
        </el-descriptions-item>
      </el-descriptions>

      <div class="section">
        <div class="section-header">
          <h3>所属组</h3>
          <Authority permission="ad:user:manage">
            <el-button type="primary" size="small" @click="handleAddToGroup">
              <el-icon><Plus /></el-icon>添加到组
            </el-button>
          </Authority>
        </div>
        <div class="groups-container">
          <el-table v-loading="loadingGroups" :data="userGroups" style="width: 100%">
            <el-table-column label="组名" min-width="200">
              <template #default="{ row }">
                <div class="group-info">
                  <div class="group-name">{{ row.name }}</div>
                  <div v-if="row.ouPath" class="group-path">{{ row.ouPath }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.type === 'system' ? 'info' : 'success'" size="small">
                  {{ row.type === 'system' ? '系统组' : '安全组' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <Authority permission="ad:user:manage">
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="handleRemoveFromGroup(row)"
                  >
                    移除
                  </el-button>
                </Authority>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 添加到组对话框 -->
    <el-dialog
      v-model="addToGroupVisible"
      title="添加到组"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="group-selector">
        <el-input
          v-model="groupSearchQuery"
          placeholder="搜索组"
          clearable
          class="group-search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <div class="group-list">
          <div class="group-category">
            <div class="category-title">系统内置组</div>
            <el-checkbox-group v-model="groupForm.groups">
              <div v-for="group in filteredSystemGroups" :key="group.dn" class="group-item">
                <el-checkbox :label="group.dn">
                  <div class="group-item-content">
                    <div class="group-name">{{ group.shortName || group.name }}</div>
                    <div v-if="group.description" class="group-description">
                      - {{ group.description }}
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
          
          <div class="group-category">
            <div class="category-title">安全组</div>
            <el-checkbox-group v-model="groupForm.groups">
              <div v-for="group in filteredSecurityGroups" :key="group.dn" class="group-item">
                <el-checkbox :label="group.dn">
                  <div class="group-item-content">
                    <div class="group-name">{{ group.shortName || group.name }}</div>
                    <div v-if="group.description" class="group-description">
                      - {{ group.description }}
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="addToGroupVisible = false">取消</el-button>
        <Authority permission="ad:user:manage">
          <el-button type="primary" @click="confirmAddToGroup" :loading="addingToGroup">
            确定
          </el-button>
        </Authority>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getUserGroups, getAllGroups, updateUserGroups } from '@/api/ad'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled, Plus, Search } from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
})

const userGroups = ref([])
const loadingGroups = ref(false)

const addToGroupVisible = ref(false)
const groupForm = ref({
  groups: []
})
const availableGroups = ref([])
const loadingAvailableGroups = ref(false)
const addingToGroup = ref(false)

const groupSearchQuery = ref('')

const systemGroups = computed(() => {
  return availableGroups.value.filter(group => 
    group.isBuiltin || 
    group.name?.toLowerCase().includes('builtin') ||
    group.name?.toLowerCase().includes('domain') ||
    group.name?.toLowerCase().includes('schema') ||
    group.name?.toLowerCase().includes('enterprise') ||
    group.name?.toLowerCase().includes('administrators') ||
    group.name?.toLowerCase().includes('system')
  )
})

const securityGroups = computed(() => {
  return availableGroups.value.filter(group => 
    !systemGroups.value.includes(group)
  )
})

const filteredSystemGroups = computed(() => {
  const query = groupSearchQuery.value.toLowerCase()
  return systemGroups.value.filter(group => 
    group.name.toLowerCase().includes(query) || 
    (group.description && group.description.toLowerCase().includes(query))
  )
})

const filteredSecurityGroups = computed(() => {
  const query = groupSearchQuery.value.toLowerCase()
  return securityGroups.value.filter(group => 
    group.name.toLowerCase().includes(query) || 
    (group.description && group.description.toLowerCase().includes(query))
  )
})

const formatGroupName = (dn) => {
  if (!dn) return ''
  const cnMatch = dn.match(/CN=([^,]+)/)
  return cnMatch ? cnMatch[1] : dn
}

const formatExpiryDate = (dateString) => {
  if (!dateString) return '未知'
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '格式错误'
  }
}

const handleAddToGroup = async () => {
  groupForm.value.groups = []
  addToGroupVisible.value = true
  await fetchAvailableGroups()
}

const fetchAvailableGroups = async () => {
  loadingAvailableGroups.value = true
  try {
    const { data } = await getAllGroups()
    if (!Array.isArray(data)) {
      throw new Error('获取组列表失败：返回数据格式错误')
    }
    
    availableGroups.value = data.map(group => ({
      ...group,
      shortName: formatGroupName(group.dn)
    }))
  } catch (error) {
    console.error('获取可用组失败:', error)
    ElMessage.error(error.message || '获取可用组失败')
    availableGroups.value = []
  } finally {
    loadingAvailableGroups.value = false
  }
}

const confirmAddToGroup = async () => {
  if (!groupForm.value.groups.length) {
    ElMessage.warning('请选择要添加的组')
    return
  }

  addingToGroup.value = true
  try {
    // 获取用户当前的组
    const response = await getUserGroups(props.user.username)
    const currentGroups = Array.isArray(response.data) ? response.data : []
    
    // 合并当前的组和新选择的组
    const newGroups = [...new Set([...currentGroups, ...groupForm.value.groups])]
    
    // 更新用户的组
    await updateUserGroups(props.user.username, newGroups)
    
    ElMessage.success('添加到组成功')
    addToGroupVisible.value = false
    
    // 刷新用户组列表
    await fetchUserGroups()
  } catch (error) {
    console.error('添加到组失败:', error)
    ElMessage.error('添加到组失败')
  } finally {
    addingToGroup.value = false
  }
}

const fetchUserGroups = async () => {
  loadingGroups.value = true
  try {
    const response = await getUserGroups(props.user.username)
    const groups = Array.isArray(response.data) ? response.data : []
    userGroups.value = groups.map(group => {
      // 从 DN 中提取组名和 OU 路径
      const dnParts = group.split(',')
      const name = dnParts[0].replace('CN=', '')
      const ouPath = dnParts
        .slice(1)
        .filter(part => part.startsWith('OU='))
        .map(part => part.replace('OU=', ''))
        .reverse()
        .join(' / ')
      
      // 判断组类型
      const isSystemGroup = name.toLowerCase().includes('builtin') ||
                          name.toLowerCase().includes('domain') ||
                          name.toLowerCase().includes('schema') ||
                          name.toLowerCase().includes('enterprise') ||
                          name.toLowerCase().includes('administrators') ||
                          name.toLowerCase().includes('system')
      
      return {
        name,
        dn: group,
        type: isSystemGroup ? 'system' : 'security',
        ouPath
      }
    })
  } catch (error) {
    console.error('获取用户组失败:', error)
    ElMessage.error('获取用户组信息失败')
  } finally {
    loadingGroups.value = false
  }
}

const handleRemoveFromGroup = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要将用户从组"${group.name}"中移除吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 获取用户当前的组
    const response = await getUserGroups(props.user.username)
    const currentGroups = Array.isArray(response.data) ? response.data : []
    
    // 移除选中的组
    const newGroups = currentGroups.filter(g => g !== group.dn)
    
    // 更新用户的组
    await updateUserGroups(props.user.username, newGroups)
    
    ElMessage.success('从组中移除成功')
    
    // 刷新用户组列表
    await fetchUserGroups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('从组中移除失败:', error)
      ElMessage.error('从组中移除失败')
    }
  }
}

onMounted(() => {
  fetchUserGroups()
})
</script>

<style scoped>
.user-detail-container {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-info h2 {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.username {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section h3 {
  margin: 0;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.groups-container {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
}

:deep(.el-descriptions) {
  padding: 16px;
  background: var(--el-fill-color-blank);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

:deep(.el-descriptions__label) {
  width: 120px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:deep(.el-select-dropdown__item small) {
  margin-left: 8px;
}

.group-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.group-search {
  margin-bottom: 12px;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 8px;
}

.group-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.group-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.group-list::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.group-category {
  display: flex;
  flex-direction: column;
}

.category-title {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-regular);
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin-bottom: 8px;
}

.group-item {
  margin-bottom: 4px;
}

.group-item-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
}

.group-item-content .group-info {
  flex: 1;
  min-width: 0;
}

.group-item-content .group-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.group-item-content .group-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-checkbox__label) {
  padding-right: 8px;
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.group-path {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.group-member-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  margin-left: auto;
  padding-left: 8px;
}

:deep(.el-table .cell) {
  white-space: normal;
  line-height: 1.5;
}
</style> 