"""update_email_member_add_extid_field

Revision ID: 03c4f1f63bbf
Revises: 4f3eeae63878
Create Date: 2025-05-24 11:30:40.190552

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '03c4f1f63bbf'
down_revision: Union[str, None] = '4f3eeae63878'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # SQLite doesn't support ALTER COLUMN, so we need to add the extid column only
    # The userid column length change will be handled by the model definition
    
    # 添加extid字段（工号）
    op.add_column('email_members', sa.Column('extid', sa.String(length=50), nullable=True, comment='工号'))
    
    # 为extid字段创建唯一索引
    op.create_index(op.f('ix_email_members_extid'), 'email_members', ['extid'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 删除extid字段的索引
    op.drop_index(op.f('ix_email_members_extid'), table_name='email_members')
    
    # 删除extid字段
    op.drop_column('email_members', 'extid')
    # ### end Alembic commands ###
