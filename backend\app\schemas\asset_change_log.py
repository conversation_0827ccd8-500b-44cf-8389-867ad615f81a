from typing import Optional
from datetime import datetime
from pydantic import BaseModel

class AssetChangeLogBase(BaseModel):
    field: str
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    change_type: str

class AssetChangeLogCreate(AssetChangeLogBase):
    asset_id: int

class AssetChangeLog(AssetChangeLogBase):
    id: int
    asset_id: int
    created_at: datetime

    class Config:
        from_attributes = True 