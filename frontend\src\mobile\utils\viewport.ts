/**
 * 移动端视口高度工具
 * 用于处理移动端弹窗高度计算，解决底部按钮被地址栏遮挡的问题
 */

// 检查浏览器是否支持新的视口单位
export function supportsViewportUnits(): boolean {
  if (typeof window === 'undefined') return false
  
  try {
    // 创建测试元素检查svh支持
    const testElement = document.createElement('div')
    testElement.style.height = '100svh'
    return testElement.style.height === '100svh'
  } catch {
    return false
  }
}

// 计算安全的弹窗高度
export function calculateSafePopupHeight(percentage: number = 68): string {
  if (typeof window === 'undefined') return `${percentage}vh`
  
  // 如果支持svh单位，直接使用
  if (supportsViewportUnits()) {
    return `${percentage}svh`
  }
  
  // 否则使用JavaScript计算，预留更多空间给地址栏
  const windowHeight = window.innerHeight
  const addressBarEstimate = Math.min(windowHeight * 0.15, 80) // 估算地址栏高度，最多80px
  const availableHeight = windowHeight - addressBarEstimate
  const safeHeight = Math.floor(availableHeight * (percentage / 100))
  
  return `${safeHeight}px`
}

// 设置弹窗高度CSS变量
export function setPopupHeightVariables(): void {
  if (typeof window === 'undefined') return
  
  const root = document.documentElement
  
  // 如果支持svh，不需要设置变量
  if (supportsViewportUnits()) {
    return
  }
  
  // 计算各种弹窗高度
  const heights = {
    'mobile-popup-max-height': calculateSafePopupHeight(68),
    'mobile-popup-medium-height': calculateSafePopupHeight(58),
    'mobile-popup-small-height': calculateSafePopupHeight(48),
    'mobile-popup-large-height': calculateSafePopupHeight(72)
  }
  
  // 设置CSS变量
  Object.entries(heights).forEach(([key, value]) => {
    root.style.setProperty(`--${key}`, value)
  })
}

// 监听窗口大小变化
export function setupViewportListener(): void {
  if (typeof window === 'undefined') return
  
  // 如果支持svh，不需要监听
  if (supportsViewportUnits()) {
    return
  }
  
  let resizeTimer: number | null = null
  
  const handleResize = () => {
    if (resizeTimer) {
      window.clearTimeout(resizeTimer)
    }
    
    resizeTimer = window.setTimeout(() => {
      setPopupHeightVariables()
    }, 100) // 防抖，避免频繁计算
  }
  
  window.addEventListener('resize', handleResize)
  window.addEventListener('orientationchange', handleResize)
  
  // 初始设置
  setPopupHeightVariables()
}

// 在应用启动时调用
export function initializeViewportUtils(): void {
  if (typeof window === 'undefined') return
  
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupViewportListener)
  } else {
    setupViewportListener()
  }
} 