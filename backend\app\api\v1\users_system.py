from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.crud.user import user_crud

router = APIRouter()

@router.get("/", response_model=List[schemas.UserWithRoles])
def read_users(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user = Depends(deps.check_permissions(["system:user:view"])),
) -> Any:
    """
    获取用户列表
    """
    users = user_crud.get_multi_with_roles(db, skip=skip, limit=limit)
    return users

@router.post("/", response_model=schemas.User)
def create_user(
    *,
    db: Session = Depends(deps.get_db),
    user_in: schemas.UserCreate,
    current_user = Depends(deps.check_permissions(["system:user:add"])),
) -> Any:
    """
    创建新用户
    """
    # 检查用户名是否已存在
    user = user_crud.get_by_username(db, username=user_in.username)
    if user:
        raise HTTPException(
            status_code=400,
            detail="此用户名已被使用"
        )
    
    # 检查邮箱是否已存在
    user = user_crud.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="此邮箱已被使用"
        )
    
    # 创建用户
    user = user_crud.create(db, obj_in=user_in)
    return user

@router.get("/me", response_model=schemas.UserWithRoles)
def read_user_me(
    db: Session = Depends(deps.get_db),
    current_user: schemas.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前登录用户信息（包含角色和权限）
    """
    user_with_roles = user_crud.get_with_roles(db, id=current_user.id)
    if not user_with_roles:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user_with_roles

@router.get("/{user_id}", response_model=schemas.UserWithRoles)
def read_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:user:view"])),
) -> Any:
    """
    获取用户详情
    """
    user = user_crud.get_with_roles(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    return user

@router.put("/{user_id}", response_model=schemas.User)
def update_user(
    *,
    user_id: int,
    user_in: schemas.UserUpdate,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:user:edit"])),
) -> Any:
    """
    更新用户信息
    """
    user = user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 如果更新了用户名，检查是否与其他用户冲突
    if user_in.username != user.username:
        existing_user = user_crud.get_by_username(db, username=user_in.username)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(
                status_code=400,
                detail="此用户名已被使用"
            )
    
    # 如果更新了邮箱，检查是否与其他用户冲突
    if user_in.email != user.email:
        existing_user = user_crud.get_by_email(db, email=user_in.email)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(
                status_code=400,
                detail="此邮箱已被使用"
            )
    
    user = user_crud.update(db, db_obj=user, obj_in=user_in)
    return user

@router.patch("/{user_id}/active", response_model=schemas.User)
def update_user_active(
    *,
    user_id: int,
    active_update: schemas.UserActiveUpdate,
    db: Session = Depends(deps.get_db),
    current_user: schemas.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    启用或禁用用户
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="没有足够的权限")
    
    user = user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 保护内置账号
    if user.is_builtin:
        raise HTTPException(status_code=400, detail="不能禁用内置账号")
    
    # 不允许禁用自己
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="不能修改自己的账户状态")
        
    user = user_crud.update_active(db, db_obj=user, is_active=active_update.is_active)
    return user

@router.patch("/{user_id}/superuser", response_model=schemas.User)
def update_user_superuser(
    user_id: int,
    superuser_update: schemas.UserSuperuserUpdate,
    db: Session = Depends(deps.get_db),
    current_user: schemas.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新用户的超级管理员状态
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="没有足够的权限")
    
    user = user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 保护内置账号
    if user.is_builtin:
        raise HTTPException(status_code=400, detail="不能修改内置账号的超级管理员状态")
    
    # 不允许移除自己的超级管理员权限
    if user_id == current_user.id and not superuser_update.is_superuser:
        raise HTTPException(status_code=400, detail="不能移除自己的超级管理员权限")
    
    user = user_crud.update_superuser(db, db_obj=user, is_superuser=superuser_update.is_superuser)
    return user

@router.delete("/{user_id}", response_model=schemas.User)
def delete_user(
    user_id: int,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:user:delete"])),
) -> Any:
    """
    删除用户
    """
    user = user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 保护内置账号
    if user.is_builtin:
        raise HTTPException(status_code=400, detail="不能删除内置账号")
    
    # 不允许删除自己
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="不能删除自己的账户")
        
    user = user_crud.remove(db, id=user_id)
    return user 