from typing import Generator, Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt, JWTError
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.config import settings
from app import models
import logging

# 配置日志输出到控制台，调整为WARNING级别减少认证日志量
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   encoding='utf-8')
logger = logging.getLogger("auth")
logger.setLevel(logging.WARNING)  # 认证模块只记录WARNING级别及以上的日志

# 使用缺省值空字符串，使token可选
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login", auto_error=False)

def get_db() -> Generator:
    """获取数据库会话"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

# 直接从请求头中获取token，绕过OAuth2PasswordBearer的验证
async def get_token_from_header(request: Request) -> str:
    auth_header = request.headers.get("Authorization")
    
    if not auth_header:
        logger.error("无Authorization头")
        return ""
        
    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        logger.error(f"Authorization头格式错误: {auth_header}")
        return ""
        
    token = parts[1]
    return token

async def get_current_user(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> models.User:
    """获取当前用户"""
    # 如果OAuth2PasswordBearer未能获取token，尝试从header中直接获取
    if not token:
        token = await get_token_from_header(request)
        
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    if not token:
        logger.error("未提供token")
        raise credentials_exception
        
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        
        username: str = payload.get("sub")
        if username is None:
            logger.error("token中没有sub字段")
            raise credentials_exception
            
    except JWTError as e:
        error_msg = str(e)
        # 对token过期错误使用更温和的日志级别
        if "expired" in error_msg.lower():
            logger.warning(f"JWT Token已过期: {error_msg}")
        else:
            logger.error(f"JWT解码错误: {error_msg}")
        raise credentials_exception
    
    user = db.query(models.User).filter(models.User.username == username).first()
    if user is None:
        logger.error(f"数据库中未找到用户: {username}")
        raise credentials_exception
        
    # 只在Debug模式下记录详细用户验证信息
    if logger.level == logging.DEBUG:
        logger.debug(f"用户验证成功: {username}, is_superuser={user.is_superuser}")
    
    return user

async def get_current_active_user(
    current_user: models.User = Depends(get_current_user),
) -> models.User:
    """获取当前已激活的用户"""
    if not current_user.is_active:
        logger.error(f"用户 {current_user.username} 已被禁用")
        raise HTTPException(status_code=403, detail="用户已被禁用")
    logger.debug(f"验证用户 {current_user.username} 活跃状态成功")
    return current_user

def check_permissions(required_permissions: list[str]):
    """
    创建一个依赖项，检查当前用户是否具有所需权限
    
    :param required_permissions: 所需权限的列表
    :return: 一个依赖函数，用于检查用户权限
    """
    async def validate_permissions(
        db: Session = Depends(get_db),
        current_user: models.User = Depends(get_current_active_user),
    ):
        # 超级管理员拥有所有权限
        if current_user.is_superuser:
            logger.debug(f"用户 {current_user.username} 是超级管理员，拥有所有权限")
            return current_user
            
        # 获取用户所有角色
        user_with_roles = db.query(models.User).filter(
            models.User.id == current_user.id
        ).first()
        
        if not user_with_roles or not user_with_roles.roles:
            logger.error(f"用户 {current_user.username} 没有任何角色")
            raise HTTPException(status_code=403, detail="没有足够的权限")
            
        # 获取用户所有权限
        user_permissions = set()
        for role in user_with_roles.roles:
            for permission in role.permissions:
                user_permissions.add(permission.code)
        
        logger.debug(f"用户 {current_user.username} 的权限: {user_permissions}")
        logger.debug(f"所需权限: {required_permissions}")
        
        # 检查是否有所需权限中的任何一个
        if not any(perm in user_permissions for perm in required_permissions):
            logger.error(f"用户 {current_user.username} 没有所需权限: {required_permissions}")
            raise HTTPException(status_code=403, detail="没有足够的权限")
            
        logger.debug(f"用户 {current_user.username} 权限验证成功")
        return current_user
        
    return validate_permissions

async def verify_agent_token(request: Request) -> bool:
    """
    验证Agent系统Token
    
    :param request: HTTP请求对象
    :return: 验证成功返回True，失败抛出HTTPException
    """
    auth_header = request.headers.get("Authorization")
    
    if not auth_header:
        # 检查查询参数中是否有token
        token = request.query_params.get("agent_token")
        if not token:
            logger.warning("Agent请求缺少认证Token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="缺少Agent认证Token",
                headers={"WWW-Authenticate": "Bearer"},
            )
    else:
        # 从Authorization头中提取token
        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            logger.warning(f"Agent Authorization头格式错误: {auth_header}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization头格式错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        token = parts[1]
    
    # 验证系统Token
    if not _validate_agent_token(token):
        # 记录详细的失败信息（但不记录完整token以保护安全）
        token_preview = token[:8] + "..." if len(token) > 8 else "短token"
        client_ip = request.client.host if request.client else "未知IP"
        user_agent = request.headers.get("User-Agent", "未知UA")
        
        logger.warning(f"Agent Token验证失败 - Token: {token_preview}, IP: {client_ip}, UA: {user_agent}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的Agent系统Token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    logger.debug("Agent系统Token验证成功")
    return True

def _validate_agent_token(token: str) -> bool:
    """
    验证Agent Token的内部函数
    
    :param token: 待验证的token
    :return: 验证结果
    """
    try:
        # 基本Token验证
        if token == settings.AGENT_SYSTEM_TOKEN:
            return True
        
        # 如果token包含JWT格式，可以进一步验证
        if token.count('.') == 2:  # JWT格式
            try:
                # 这里可以添加JWT验证逻辑
                # payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
                # 验证token是否为Agent系统token
                pass
            except JWTError:
                logger.debug("JWT格式的Agent Token验证失败")
                return False
        
        return False
    except Exception as e:
        logger.error(f"验证Agent Token时发生异常: {str(e)}")
        return False
