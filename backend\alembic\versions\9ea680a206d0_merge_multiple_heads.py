"""merge multiple heads

Revision ID: 9ea680a206d0
Revises: 7eb1a3b9d4f2, d88e0e43824e
Create Date: 2025-03-20 14:14:57.043680

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ea680a206d0'
down_revision: Union[str, None] = ('7eb1a3b9d4f2', 'd88e0e43824e')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
