<template>
  <div class="loading-container">
    <el-card class="loading-card">
      <template #header>
        <div class="card-header">
          <span>加载中...</span>
        </div>
      </template>
      <div class="loading-content">
        <el-progress 
          :percentage="100" 
          :stroke-width="10"
          :show-text="false"
        />
        <div class="status-text">正在跳转...</div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { usePlatform } from '@/composables/usePlatform'

const router = useRouter()
const userStore = useUserStore()
const { shouldUseMobile } = usePlatform()

// 获取重定向目标页面
const route = useRoute()
const redirectPath = route.query.redirect as string || ''

const initialize = async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    const loginPath = shouldUseMobile.value ? '/m/login' : '/login'
    router.replace(loginPath)
    return
  }

  // 如果用户状态未就绪，尝试初始化
  if (!userStore.isReady) {
    try {
      await userStore.initializeAuth()
    } catch (error) {
      // 初始化失败，跳转到登录页
      userStore.logout()
      const loginPath = shouldUseMobile.value ? '/m/login' : '/login'
      router.replace(loginPath)
      return
    }
  }

  // 确定目标页面
  let targetPath = redirectPath
  if (!targetPath || targetPath === '/loading' || targetPath.includes('/loading')) {
    targetPath = shouldUseMobile.value ? '/m/apps' : '/dashboard'
  }
  
  // 短暂延迟后跳转，确保状态稳定
  setTimeout(() => {
    router.replace(targetPath)
  }, 50)
}

onMounted(() => {
  initialize()
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.loading-card {
  width: 90%;
  max-width: 400px;
}

.loading-content {
  padding: 20px;
  text-align: center;
}

.status-text {
  margin-top: 20px;
  color: #606266;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 