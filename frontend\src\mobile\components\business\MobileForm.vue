<template>
  <div class="mobile-form">
    <van-form
      ref="formRef"
      :label-width="labelWidth"
      :label-align="labelAlign"
      :input-align="inputAlign"
      :colon="colon"
      :disabled="disabled"
      :readonly="readonly"
      :show-error="showError"
      :show-error-message="showErrorMessage"
      :submit-on-enter="submitOnEnter"
      @submit="onSubmit"
      @failed="onFailed"
    >
      <slot :form="formRef" :validate="validate" :reset="resetValidation">
        <!-- 默认表单项 -->
        <van-cell-group v-if="fields.length > 0" inset>
          <template v-for="field in fields" :key="field.name">
            <!-- 输入框 -->
            <van-field
              v-if="field.type === 'input'"
              v-model="formData[field.name]"
              :name="field.name"
              :label="field.label"
              :placeholder="field.placeholder"
              :rules="field.rules"
              :disabled="field.disabled || disabled"
              :readonly="field.readonly || readonly"
              :maxlength="field.maxlength"
              :type="field.inputType || 'text'"
              :show-word-limit="field.showWordLimit"
              :clearable="field.clearable !== false"
              :required="field.required"
            />
            
            <!-- 文本域 -->
            <van-field
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.name]"
              :name="field.name"
              :label="field.label"
              :placeholder="field.placeholder"
              :rules="field.rules"
              :disabled="field.disabled || disabled"
              :readonly="field.readonly || readonly"
              :maxlength="field.maxlength"
              :rows="field.rows || 3"
              :autosize="field.autosize"
              :show-word-limit="field.showWordLimit"
              type="textarea"
              :required="field.required"
            />
            
            <!-- 选择器 -->
            <van-field
              v-else-if="field.type === 'select'"
              v-model="formData[field.name]"
              :name="field.name"
              :label="field.label"
              :placeholder="field.placeholder"
              :rules="field.rules"
              :disabled="field.disabled || disabled"
              :readonly="field.readonly || readonly"
              :required="field.required"
              is-link
              @click="openPicker(field)"
            />
            
            <!-- 开关 -->
            <van-field
              v-else-if="field.type === 'switch'"
              :name="field.name"
              :label="field.label"
              :disabled="field.disabled || disabled"
              :rules="field.rules"
              :required="field.required"
            >
              <template #input>
                <van-switch
                  v-model="formData[field.name]"
                  :disabled="field.disabled || disabled"
                  :size="field.size"
                />
              </template>
            </van-field>
            
            <!-- 单选框组 -->
            <van-field
              v-else-if="field.type === 'radio'"
              :name="field.name"
              :label="field.label"
              :rules="field.rules"
              :required="field.required"
            >
              <template #input>
                <van-radio-group
                  v-model="formData[field.name]"
                  :direction="field.direction || 'horizontal'"
                  :disabled="field.disabled || disabled"
                >
                  <van-radio
                    v-for="option in field.options"
                    :key="option.value"
                    :name="option.value"
                  >
                    {{ option.label }}
                  </van-radio>
                </van-radio-group>
              </template>
            </van-field>
            
            <!-- 复选框组 -->
            <van-field
              v-else-if="field.type === 'checkbox'"
              :name="field.name"
              :label="field.label"
              :rules="field.rules"
              :required="field.required"
            >
              <template #input>
                <van-checkbox-group
                  v-model="formData[field.name]"
                  :direction="field.direction || 'horizontal'"
                  :disabled="field.disabled || disabled"
                >
                  <van-checkbox
                    v-for="option in field.options"
                    :key="option.value"
                    :name="option.value"
                  >
                    {{ option.label }}
                  </van-checkbox>
                </van-checkbox-group>
              </template>
            </van-field>
            
            <!-- 文件上传 -->
            <van-field
              v-else-if="field.type === 'upload'"
              :name="field.name"
              :label="field.label"
              :rules="field.rules"
              :required="field.required"
            >
              <template #input>
                <MobileFileUploader
                  v-model="formData[field.name]"
                  :accept="field.accept || 'image/*'"
                  :max-size="field.maxSize || 5 * 1024 * 1024"
                  :multiple="field.multiple || false"
                  :max-count="field.maxCount || (field.multiple ? 9 : 1)"
                  :disabled="field.disabled || disabled"
                  :upload-text="field.uploadText || '上传文件'"
                  :show-tips="field.showTips !== false"
                  :show-camera-button="field.showCameraButton !== false"
                />
              </template>
            </van-field>
          </template>
        </van-cell-group>
      </slot>
      
      <!-- 提交按钮 -->
      <div v-if="showSubmitButton" class="form-footer">
        <van-button
          round
          block
          type="primary"
          :loading="submitting"
          :disabled="disabled"
          native-type="submit"
        >
          {{ submitText }}
        </van-button>
      </div>
    </van-form>
    
    <!-- 选择器弹窗 -->
    <van-popup
      v-model:show="showPicker"
      position="bottom"
      destroy-on-close
    >
      <van-picker
        v-if="currentField"
        :columns="currentField.options || []"
        :model-value="[formData[currentField.name]]"
        @confirm="onPickerConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { showToast } from 'vant'
import type { FormInstance } from 'vant'
import MobileFileUploader from '@/mobile/components/MobileFileUploader.vue'

interface FormFieldOption {
  label: string
  value: any
}

interface FormField {
  name: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'switch' | 'radio' | 'checkbox' | 'upload'
  placeholder?: string
  rules?: any[]
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  // input/textarea特有
  inputType?: 'text' | 'number' | 'tel' | 'email' | 'password'
  maxlength?: number
  showWordLimit?: boolean
  clearable?: boolean
  // textarea特有
  rows?: number
  autosize?: boolean
  // select/radio/checkbox特有
  options?: FormFieldOption[]
  // radio/checkbox特有
  direction?: 'horizontal' | 'vertical'
  // switch特有
  size?: string
  // upload特有
  multiple?: boolean
  maxCount?: number
  maxSize?: number
  uploadText?: string
  showTips?: boolean
  showCameraButton?: boolean
  accept?: string
}

interface Props {
  // 表单字段配置
  fields?: FormField[]
  // 表单数据
  modelValue?: Record<string, any>
  // 表单配置
  labelWidth?: string | number
  labelAlign?: 'left' | 'center' | 'right' | 'top'
  inputAlign?: 'left' | 'center' | 'right'
  colon?: boolean
  disabled?: boolean
  readonly?: boolean
  showError?: boolean
  showErrorMessage?: boolean
  submitOnEnter?: boolean
  // 提交按钮
  showSubmitButton?: boolean
  submitText?: string
  submitting?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  fields: () => [],
  modelValue: () => ({}),
  labelWidth: '6.2em',
  labelAlign: 'left',
  inputAlign: 'left',
  colon: false,
  disabled: false,
  readonly: false,
  showError: false,
  showErrorMessage: true,
  submitOnEnter: true,
  showSubmitButton: true,
  submitText: '提交',
  submitting: false
})

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'submit': [values: Record<string, any>]
  'failed': [errorInfo: { values: Record<string, any>, errors: any[] }]
}>()

const formRef = ref<FormInstance>()
const formData = reactive<Record<string, any>>({})
const showPicker = ref(false)
const currentField = ref<FormField | null>(null)

// 初始化表单数据
const initFormData = () => {
  Object.assign(formData, props.modelValue)
  
  // 为字段设置默认值
  props.fields.forEach(field => {
    if (!(field.name in formData)) {
      if (field.type === 'checkbox') {
        formData[field.name] = []
      } else if (field.type === 'switch') {
        formData[field.name] = false
      } else if (field.type === 'upload') {
        formData[field.name] = []
      } else {
        formData[field.name] = ''
      }
    }
  })
}

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:modelValue', { ...newVal })
}, { deep: true })

// 监听外部数据变化
watch(() => props.modelValue, (newVal) => {
  Object.assign(formData, newVal)
}, { deep: true })

// 表单提交
const onSubmit = (values: Record<string, any>) => {
  emit('submit', values)
}

// 表单验证失败
const onFailed = (errorInfo: { values: Record<string, any>, errors: any[] }) => {
  emit('failed', errorInfo)
}

// 打开选择器
const openPicker = (field: FormField) => {
  currentField.value = field
  showPicker.value = true
}

// 选择器确认
const onPickerConfirm = ({ selectedValues, selectedOptions }: any) => {
  if (currentField.value) {
    formData[currentField.value.name] = selectedValues[0]
  }
  showPicker.value = false
}

// 文件大小超限
const onOversize = () => {
  showToast('文件大小超出限制')
}

// 表单验证
const validate = (name?: string | string[]) => {
  return formRef.value?.validate(name)
}

// 重置验证
const resetValidation = (name?: string | string[]) => {
  formRef.value?.resetValidation(name)
}

// 获取表单值
const getValues = () => {
  return formRef.value?.getValues() || {}
}

// 提交表单
const submit = () => {
  formRef.value?.submit()
}

// 暴露方法
defineExpose({
  validate,
  resetValidation,
  getValues,
  submit,
  formRef
})

// 初始化
initFormData()
</script>

<style lang="scss" scoped>
.mobile-form {
  .form-footer {
    padding: var(--van-padding-lg) var(--van-padding-md);
    background-color: var(--van-background-2);
  }
  
  :deep(.van-cell-group) {
    margin-bottom: var(--van-padding-md);
  }
  
  :deep(.van-field__label) {
    color: var(--van-text-color);
  }
  
  :deep(.van-radio-group) {
    display: flex;
    gap: var(--van-padding-md);
    
    &[data-direction="vertical"] {
      flex-direction: column;
    }
  }
  
  :deep(.van-checkbox-group) {
    display: flex;
    gap: var(--van-padding-md);
    
    &[data-direction="vertical"] {
      flex-direction: column;
    }
  }
}
</style> 