from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, field_serializer

class FieldValueBase(BaseModel):
    """字段值基础模型"""
    field_name: str = Field(..., description="字段名称")
    field_value: str = Field(..., description="字段值")
    description: Optional[str] = Field(None, description="描述")

class FieldValueCreate(FieldValueBase):
    """创建字段值模型"""
    pass

class FieldValueUpdate(BaseModel):
    """更新字段值模型"""
    field_name: Optional[str] = None
    field_value: Optional[str] = None
    description: Optional[str] = None

class FieldValueResponse(FieldValueBase):
    """字段值响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, value: datetime, _info):
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True 

# 为了兼容性，添加别名
FieldValue = FieldValueResponse 