<template>
  <div class="dashboard">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><DataBoard /></el-icon>
        <h2 class="page-title">系统仪表盘</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>系统仪表盘</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <el-row :gutter="20">
        <!-- 这里可以根据需要添加内容 -->
      </el-row>
      <!-- 暂时注释掉缺失的组件 -->
      <!-- <PermissionDemo /> -->
    </el-card>
  </div>
</template>

<script setup>
// 仪表盘逻辑可以在这里添加
// 移除缺失的组件引用
// import PermissionDemo from './PermissionDemo.vue'
import { DataBoard } from '@element-plus/icons-vue'
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
  min-height: calc(100vh - 180px); /* 减去头部和padding的高度 */
}
</style>
