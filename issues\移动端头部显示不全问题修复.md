# 移动端头部显示不全问题修复

## 问题描述
移动端界面上方内容显示不全，主要表现为：
- 页面内容被固定头部导航栏遮挡
- 统计卡片和快速操作区域部分内容看不到
- 用户体验受到影响

## 问题分析
1. **固定导航栏遮挡内容**：`van-nav-bar`设置了`fixed="true"`，但主内容区域没有为固定头部留出空间
2. **CSS变量配置不一致**：头部高度变量在不同文件中定义不统一
3. **内容区域padding设置冲突**：响应式样式覆盖了主要的padding-top设置

## 解决方案

### 1. 修复主布局样式
**文件**: `frontend/src/mobile/layout/MobileLayout.vue`

**修改内容**:
- 为`.mobile-main`添加`padding-top: var(--header-height, 50px)`
- 修改响应式样式中的padding设置，避免覆盖padding-top
- 将`padding`改为分别设置`padding-left/right/bottom`

```scss
.mobile-main {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  position: relative;
  
  // 为固定头部留出空间
  padding-top: var(--header-height, 50px);
  
  // 背景色
  background-color: var(--theme-bg-page);
}

// 响应式样式修改
.mobile-layout--compact .mobile-main {
  padding-left: var(--theme-space-sm);
  padding-right: var(--theme-space-sm);
  padding-bottom: var(--theme-space-sm);
}
```

### 2. 优化头部组件
**文件**: `frontend/src/mobile/layout/components/MobileHeader.vue`

**修改内容**:
- 添加`z-index: 1000`确保头部在最上层
- 统一CSS变量使用

```scss
.van-nav-bar {
  --van-nav-bar-background: #fff;
  --van-nav-bar-height: var(--header-height, 50px);
  z-index: 1000; // 确保头部在最上层
}
```

## 技术细节

### CSS变量统一
- 使用`--header-height`作为统一的头部高度变量
- 在不同响应式断点下设置不同的高度值：
  - 移动端小屏：50px
  - 移动端大屏：56px
  - 紧凑模式：44px

### 响应式适配
- 保持padding-top不被响应式样式覆盖
- 只调整左右和底部的padding，保留顶部间距

## 测试验证
- [x] iPhone SE (320px) - 内容正常显示
- [x] iPhone 12 (375px) - 内容正常显示  
- [x] iPhone 12 Pro Max (414px) - 内容正常显示
- [x] iPad (768px) - 内容正常显示
- [x] 横屏模式 - 内容正常显示

## 相关文件
- `frontend/src/mobile/layout/MobileLayout.vue`
- `frontend/src/mobile/layout/components/MobileHeader.vue`
- `frontend/src/mobile/styles/theme.scss`

## 后续优化建议
1. 考虑添加动态头部高度检测
2. 优化不同设备的安全区域适配
3. 添加头部隐藏/显示动画效果

## 完成状态
✅ 问题已修复，移动端内容显示正常 