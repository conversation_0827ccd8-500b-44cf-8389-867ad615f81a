import { ref, type Ref } from 'vue'
import { ElMessage } from 'element-plus'

export interface TerminalStatusUpdate {
  type: 'terminal_status_update'
  terminal_id: number
  data: {
    online_status: boolean
    last_heartbeat_time: string | null
    hostname: string
    ip_address: string
    mac_address: string
  }
  timestamp: number
}

export interface SSEMessage {
  type: string
  [key: string]: any
}

/**
 * SSE终端状态客户端
 * 替代WebSocket，专门用于终端状态的实时推送
 */
export class TerminalSSEClient {
  private eventSource: EventSource | null = null
  private reconnectTimer: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 3000 // 3秒
  
  public isConnected: Ref<boolean> = ref(false)
  public connectionCount: Ref<number> = ref(0)
  public connectionId: Ref<string> = ref('')
  
  private callbacks: Map<string, Function[]> = new Map()
  private baseUrl: string
  
  constructor(baseUrl?: string) {
    // 使用固定的IP地址，与API配置保持一致
    this.baseUrl = baseUrl || 'http://**************:8000'
    this.setupEventHandlers()
  }

  /**
   * 建立SSE连接
   */
  public connect(token?: string): void {
    if (this.isConnected.value) {
      console.log('SSE已连接，无需重复连接')
      return
    }

    this.disconnect() // 确保清理之前的连接

    try {
      // 获取认证token
      const authToken = token || localStorage.getItem('token')
      
      const url = `${this.baseUrl}/api/v1/sse/terminal-status-stream`
      console.log('正在连接SSE:', url)
      
      // 创建EventSource（注意：EventSource不支持自定义headers，需要通过URL参数传递token）
      const urlWithToken = authToken ? `${url}?token=${encodeURIComponent(authToken)}` : url
      this.eventSource = new EventSource(urlWithToken)
      
      this.setupSSEEvents()
    } catch (error) {
      console.error('SSE连接失败:', error)
      this.scheduleReconnect()
    }
  }

  /**
   * 断开SSE连接
   */
  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
    
    this.isConnected.value = false
    this.connectionId.value = ''
    this.reconnectAttempts = 0
  }

  /**
   * 订阅特定类型的消息
   */
  public on(messageType: string, callback: Function): void {
    if (!this.callbacks.has(messageType)) {
      this.callbacks.set(messageType, [])
    }
    this.callbacks.get(messageType)!.push(callback)
  }

  /**
   * 取消订阅
   */
  public off(messageType: string, callback?: Function): void {
    if (!this.callbacks.has(messageType)) {
      return
    }
    
    if (callback) {
      const callbacks = this.callbacks.get(messageType)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.callbacks.delete(messageType)
    }
  }

  /**
   * 触发回调
   */
  private trigger(messageType: string, data: any): void {
    const callbacks = this.callbacks.get(messageType)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`SSE回调执行失败 [${messageType}]:`, error)
        }
      })
    }
  }

  /**
   * 设置SSE事件处理
   */
  private setupSSEEvents(): void {
    if (!this.eventSource) return

    this.eventSource.onopen = () => {
      console.log('SSE连接已建立')
      this.isConnected.value = true
      this.reconnectAttempts = 0
      this.trigger('connected', null)
    }

    this.eventSource.onmessage = (event) => {
      try {
        const message: SSEMessage = JSON.parse(event.data)
        console.log('收到SSE消息:', message)
        
        switch (message.type) {
          case 'connection_established':
            this.connectionCount.value = message.connection_count || 0
            this.connectionId.value = message.connection_id || ''
            console.log(`SSE连接建立: ${this.connectionId.value}, 当前连接数: ${this.connectionCount.value}`)
            break
          
          case 'terminal_status_update':
            this.trigger('terminal_status_update', message as TerminalStatusUpdate)
            break
          
          case 'ping':
            // 收到服务器心跳，连接正常
            console.debug('收到SSE心跳')
            break
          
          default:
            this.trigger(message.type, message)
        }
      } catch (error) {
        console.error('解析SSE消息失败:', error, event.data)
      }
    }

    this.eventSource.onerror = (event) => {
      console.error('SSE连接错误:', event)
      this.isConnected.value = false
      this.trigger('error', event)
      
      // SSE会自动重连，但我们需要处理特殊情况
      if (this.eventSource?.readyState === EventSource.CLOSED) {
        console.log('SSE连接已关闭，安排重连')
        this.scheduleReconnect()
      }
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('SSE重连次数已达上限，停止重连')
      ElMessage.error('实时连接已断开，请刷新页面重试')
      return
    }

    this.reconnectAttempts++
    console.log(`SSE将在${this.reconnectInterval}ms后进行第${this.reconnectAttempts}次重连`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.connect()
    }, this.reconnectInterval)
  }

  /**
   * 设置基础事件处理器
   */
  private setupEventHandlers(): void {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && !this.isConnected.value) {
        console.log('页面重新可见，尝试重连SSE')
        this.connect()
      }
    })

    // 监听网络状态变化
    window.addEventListener('online', () => {
      console.log('网络已连接，尝试重连SSE')
      if (!this.isConnected.value) {
        this.connect()
      }
    })

    window.addEventListener('offline', () => {
      console.log('网络已断开')
      this.disconnect()
    })
  }

  /**
   * 获取连接状态
   */
  public getConnectionState(): string {
    if (!this.eventSource) return 'DISCONNECTED'
    
    switch (this.eventSource.readyState) {
      case EventSource.CONNECTING:
        return 'CONNECTING'
      case EventSource.OPEN:
        return 'CONNECTED'
      case EventSource.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }

  /**
   * 获取连接统计信息
   */
  public getConnectionInfo() {
    return {
      isConnected: this.isConnected.value,
      connectionId: this.connectionId.value,
      connectionCount: this.connectionCount.value,
      reconnectAttempts: this.reconnectAttempts,
      state: this.getConnectionState()
    }
  }
}

// 创建全局SSE客户端实例
let terminalSSEClient: TerminalSSEClient | null = null

/**
 * 获取终端SSE客户端实例
 */
export function getTerminalSSEClient(): TerminalSSEClient {
  if (!terminalSSEClient) {
    terminalSSEClient = new TerminalSSEClient()
  }
  return terminalSSEClient
}

/**
 * 销毁SSE连接
 */
export function destroyTerminalSSEClient(): void {
  if (terminalSSEClient) {
    terminalSSEClient.disconnect()
    terminalSSEClient = null
  }
}

// 向后兼容：导出兼容WebSocket的接口
export const getTerminalWebSocket = getTerminalSSEClient
export const destroyTerminalWebSocket = destroyTerminalSSEClient 