import asyncio
import json
import logging
from fastapi import APIRouter, Request, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.database import get_db
from app.utils.auth import get_current_user, verify_token
from app.models.user import User
from app.services.sse_manager import sse_manager

logger = logging.getLogger(__name__)

router = APIRouter()

async def get_current_user_from_query(
    token: str = Query(..., description="JWT访问令牌"),
    db: Session = Depends(get_db)
) -> User:
    """
    从查询参数获取当前用户（专用于SSE）
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # 验证token
    username = verify_token(token)
    if username is None:
        raise credentials_exception
    
    # 查询用户
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    return user

@router.get("/email-sync-stream")
async def email_sync_stream(
    request: Request,
    current_user: User = Depends(get_current_user_from_query),
    db: Session = Depends(get_db)
):
    """
    邮箱同步进度实时推送SSE端点
    
    提供邮箱同步进度的实时更新流，使用Server-Sent Events (SSE)协议
    用于实时显示邮箱成员同步的详细进度信息
    """
    
    async def event_generator():
        """SSE事件生成器"""
        connection = None
        try:
            # 注册SSE连接
            connection = await sse_manager.register_connection(request)
            
            # 发送连接建立确认
            initial_message = {
                "type": "connection_established",
                "message": "邮箱同步SSE连接已建立",
                "connection_id": connection.connection_id,
                "connection_count": sse_manager.get_connection_count(),
                "timestamp": connection.created_at
            }
            yield f"data: {json.dumps(initial_message, ensure_ascii=False, default=str)}\n\n"
            
            logger.info(f"邮箱同步SSE连接已建立: {connection.connection_id}, 用户: {current_user.email}")
            
            # 保持连接活跃，处理消息
            while True:
                # 检查客户端连接状态
                if await request.is_disconnected():
                    logger.info(f"检测到客户端断开连接: {connection.connection_id}")
                    break
                
                # 获取待发送的消息
                message = await connection.get_message()
                if message is None:
                    # 连接可能已经失效
                    break
                    
                # 发送消息给客户端
                try:
                    message_str = json.dumps(message, ensure_ascii=False, default=str)
                    yield f"data: {message_str}\n\n"
                    
                    # 记录非心跳消息
                    if message.get("type") != "ping":
                        logger.debug(f"发送邮箱同步SSE消息: {message.get('type')} 到连接 {connection.connection_id}")
                        
                except Exception as e:
                    logger.error(f"序列化邮箱同步SSE消息失败: {e}")
                    break
                    
        except asyncio.CancelledError:
            logger.info(f"邮箱同步SSE连接被取消: {connection.connection_id if connection else 'unknown'}")
            
        except Exception as e:
            logger.error(f"邮箱同步SSE连接异常: {e}")
            
        finally:
            # 清理连接
            if connection:
                await sse_manager.unregister_connection(connection.connection_id)
                logger.info(f"邮箱同步SSE连接已清理: {connection.connection_id}")
    
    # 返回SSE响应
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "GET",
            "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
        }
    )

@router.get("/terminal-status-stream")
async def terminal_status_stream(
    request: Request,
    current_user: User = Depends(get_current_user_from_query),
    db: Session = Depends(get_db)
):
    """
    终端状态实时推送SSE端点
    
    提供终端在线状态的实时更新流，使用Server-Sent Events (SSE)协议
    相比WebSocket更轻量，适合单向推送场景
    """
    
    async def event_generator():
        """SSE事件生成器"""
        connection = None
        try:
            # 注册SSE连接
            connection = await sse_manager.register_connection(request)
            
            # 发送连接建立确认
            initial_message = {
                "type": "connection_established",
                "message": "SSE连接已建立",
                "connection_id": connection.connection_id,
                "connection_count": sse_manager.get_connection_count(),
                "timestamp": connection.created_at
            }
            yield f"data: {json.dumps(initial_message, ensure_ascii=False, default=str)}\n\n"
            
            logger.info(f"SSE连接已建立: {connection.connection_id}, 用户: {current_user.email}")
            
            # 保持连接活跃，处理消息
            while True:
                # 检查客户端连接状态
                if await request.is_disconnected():
                    logger.info(f"检测到客户端断开连接: {connection.connection_id}")
                    break
                
                # 获取待发送的消息
                message = await connection.get_message()
                if message is None:
                    # 连接可能已经失效
                    break
                    
                # 发送消息给客户端
                try:
                    message_str = json.dumps(message, ensure_ascii=False, default=str)
                    yield f"data: {message_str}\n\n"
                    
                    # 记录非心跳消息
                    if message.get("type") != "ping":
                        logger.debug(f"发送SSE消息: {message.get('type')} 到连接 {connection.connection_id}")
                        
                except Exception as e:
                    logger.error(f"序列化SSE消息失败: {e}")
                    break
                    
        except asyncio.CancelledError:
            logger.info(f"SSE连接被取消: {connection.connection_id if connection else 'unknown'}")
            
        except Exception as e:
            logger.error(f"SSE连接异常: {e}")
            
        finally:
            # 清理连接
            if connection:
                await sse_manager.unregister_connection(connection.connection_id)
                logger.info(f"SSE连接已清理: {connection.connection_id}")
    
    # 返回SSE响应
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "GET",
            "X-Accel-Buffering": "no",  # 禁用Nginx缓冲
        }
    )

@router.get("/sse-status")
async def get_sse_status(
    current_user: User = Depends(get_current_user)
):
    """
    获取SSE服务状态
    
    返回当前SSE连接数、缓存终端数量等统计信息
    """
    try:
        status = sse_manager.get_status_summary()
        
        # 添加用户权限检查
        if not current_user.is_superuser:
            # 普通用户只能看到基本信息
            status = {
                "active_connections": status["active_connections"],
                "service_status": "running" if status["cleanup_task_running"] else "stopped"
            }
            
        return {
            "status": "success",
            "data": status,
            "message": "SSE服务状态获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取SSE状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取SSE服务状态失败"
        )

@router.post("/test-email-sync-broadcast")
async def test_email_sync_broadcast(
    current_user: User = Depends(get_current_user)
):
    """
    测试邮箱同步SSE广播功能
    
    仅供超级用户测试使用，发送测试邮箱同步进度消息到所有SSE连接
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：仅超级用户可以测试广播"
        )
    
    try:
        # 发送测试邮箱同步进度消息
        test_progress = {
            "stage": "processing_members",
            "stage_name": "处理成员数据",
            "current_batch": 2,
            "total_batches": 5,
            "processed_count": 100,
            "success_count": 95,
            "failed_count": 5,
            "progress_percentage": 40,
            "status": "running",
            "message": "正在处理第2批成员数据..."
        }
        
        sync_id = "test_sync_123"
        await sse_manager.broadcast_email_sync_progress(sync_id, test_progress)
        
        return {
            "status": "success",
            "message": f"测试邮箱同步进度广播已发送到 {sse_manager.get_connection_count()} 个连接",
            "data": {
                "connection_count": sse_manager.get_connection_count(),
                "test_sync_id": sync_id,
                "test_progress": test_progress
            }
        }
        
    except Exception as e:
        logger.error(f"邮箱同步SSE测试广播失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮箱同步SSE测试广播失败"
        )

@router.post("/test-broadcast")
async def test_sse_broadcast(
    current_user: User = Depends(get_current_user)
):
    """
    测试SSE广播功能
    
    仅供超级用户测试使用，发送测试消息到所有SSE连接
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足：仅超级用户可以测试广播"
        )
    
    try:
        # 发送测试消息
        test_message = {
            "terminal_id": 999999,
            "online_status": True,
            "last_heartbeat_time": "2024-01-01T00:00:00",
            "hostname": "test-terminal",
            "ip_address": "*************",
            "mac_address": "00:11:22:33:44:55"
        }
        
        await sse_manager.broadcast_terminal_status(999999, test_message)
        
        return {
            "status": "success",
            "message": f"测试广播已发送到 {sse_manager.get_connection_count()} 个连接",
            "data": {
                "connection_count": sse_manager.get_connection_count(),
                "test_message": test_message
            }
        }
        
    except Exception as e:
        logger.error(f"SSE测试广播失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="SSE测试广播失败"
        ) 