from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime


# 命令分类模型
class CommandCategoryBase(BaseModel):
    name: str = Field(..., description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    required_permission: str = Field(..., description="所需权限")
    is_active: bool = Field(True, description="是否启用")


class CommandCategoryCreate(CommandCategoryBase):
    pass


class CommandCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    required_permission: Optional[str] = Field(None, description="所需权限")
    is_active: Optional[bool] = Field(None, description="是否启用")


class CommandCategoryResponse(CommandCategoryBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# 命令白名单模型
class CommandWhitelistBase(BaseModel):
    category_id: int = Field(..., description="分类ID")
    command: str = Field(..., description="命令内容")
    name: str = Field(..., description="命令名称")
    description: Optional[str] = Field(None, description="命令描述")
    example: Optional[str] = Field(None, description="使用示例")
    timeout: int = Field(30, description="超时时间(秒)")
    admin_required: bool = Field(False, description="是否需要管理员权限")
    is_active: bool = Field(True, description="是否启用")
    security_level: str = Field("PUBLIC", description="安全级别")


class CommandWhitelistCreate(CommandWhitelistBase):
    pass


class CommandWhitelistUpdate(BaseModel):
    category_id: Optional[int] = Field(None, description="分类ID")
    command: Optional[str] = Field(None, description="命令内容")
    name: Optional[str] = Field(None, description="命令名称")
    description: Optional[str] = Field(None, description="命令描述")
    example: Optional[str] = Field(None, description="使用示例")
    timeout: Optional[int] = Field(None, description="超时时间(秒)")
    admin_required: Optional[bool] = Field(None, description="是否需要管理员权限")
    is_active: Optional[bool] = Field(None, description="是否启用")
    security_level: Optional[str] = Field(None, description="安全级别")


class CommandWhitelistResponse(CommandWhitelistBase):
    id: int
    created_at: datetime
    updated_at: datetime
    category: Optional[CommandCategoryResponse] = None
    
    class Config:
        from_attributes = True


# 命令验证模型
class CommandValidationRequest(BaseModel):
    command: str = Field(..., description="要验证的命令")


class CommandValidationResponse(BaseModel):
    is_valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="验证消息")
    security_level: Optional[str] = Field(None, description="安全级别")
    required_permission: Optional[str] = Field(None, description="所需权限")
    matched_command: Optional[CommandWhitelistResponse] = Field(None, description="匹配的白名单命令")


# 命令模板模型
class CommandTemplate(BaseModel):
    category: str = Field(..., description="分类名称")
    commands: List[CommandWhitelistResponse] = Field(..., description="命令列表")


class CommandTemplateResponse(BaseModel):
    categories: List[CommandTemplate] = Field(..., description="命令模板分类列表") 