from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_serializer, field_validator
from enum import Enum

# 字段类型枚举
class FieldType(str, Enum):
    TEXT = "text"                # 文本输入
    TEXTAREA = "textarea"        # 多行文本
    NUMBER = "number"           # 数字输入
    DATE = "date"               # 日期选择
    DATETIME = "datetime"       # 日期时间选择
    SELECT = "select"           # 下拉选择
    RADIO = "radio"             # 单选按钮
    CHECKBOX = "checkbox"       # 多选框
    FILE = "file"               # 文件上传

# 适用范围枚举
class AppliesTo(str, Enum):
    ASSET = "asset"                      # 仅资产
    INVENTORY_RECORD = "inventory_record" # 仅盘点记录
    BOTH = "both"                        # 两者都适用

# 自定义字段基础模型
class CustomFieldBase(BaseModel):
    """自定义字段基础模型"""
    name: str = Field(..., description="字段名称", min_length=1, max_length=100)
    label: str = Field(..., description="字段显示标签", min_length=1, max_length=200)
    field_type: FieldType = Field(..., description="字段类型")
    description: Optional[str] = Field(None, description="字段描述", max_length=1000)
    is_required: bool = Field(False, description="是否必填")
    default_value: Optional[str] = Field(None, description="默认值", max_length=1000)
    options: Optional[Dict[str, Any]] = Field(None, description="字段选项配置")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")
    sort_order: int = Field(0, description="排序顺序", ge=0)
    is_active: bool = Field(True, description="是否启用")
    applies_to: AppliesTo = Field(AppliesTo.BOTH, description="适用范围")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        """验证字段名称格式"""
        import re
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', v):
            raise ValueError('字段名称必须以字母开头，只能包含字母、数字和下划线')
        return v

    @field_validator('options')
    @classmethod
    def validate_options(cls, v: Optional[Dict[str, Any]], info) -> Optional[Dict[str, Any]]:
        """验证字段选项配置"""
        if v is None:
            return v
        
        field_type = info.data.get('field_type')
        if field_type in [FieldType.SELECT, FieldType.RADIO, FieldType.CHECKBOX]:
            if 'choices' not in v or not isinstance(v['choices'], list):
                raise ValueError('选择类型字段必须提供choices选项')
        
        if field_type == FieldType.FILE:
            if 'accept' in v:
                # 验证文件类型格式
                if not isinstance(v['accept'], str):
                    raise ValueError('accept必须是字符串类型')
        
        return v

# 创建自定义字段模型
class CustomFieldCreate(CustomFieldBase):
    """创建自定义字段模型"""
    pass

# 更新自定义字段模型
class CustomFieldUpdate(BaseModel):
    """更新自定义字段模型"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    label: Optional[str] = Field(None, min_length=1, max_length=200)
    field_type: Optional[FieldType] = None
    description: Optional[str] = Field(None, max_length=1000)
    is_required: Optional[bool] = None
    default_value: Optional[str] = Field(None, max_length=1000)
    options: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    sort_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    applies_to: Optional[AppliesTo] = None

# 自定义字段响应模型
class CustomFieldResponse(CustomFieldBase):
    """自定义字段响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, value: datetime, _info):
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True

# 自定义字段值基础模型
class CustomFieldValueBase(BaseModel):
    """自定义字段值基础模型"""
    custom_field_id: int = Field(..., description="自定义字段ID")
    value: Optional[str] = Field(None, description="字段值", max_length=10000)

# 资产自定义字段值模型
class AssetCustomFieldValueCreate(CustomFieldValueBase):
    """创建资产自定义字段值模型"""
    asset_id: int = Field(..., description="资产ID")

class AssetCustomFieldValueUpdate(BaseModel):
    """更新资产自定义字段值模型"""
    value: Optional[str] = Field(None, max_length=10000)

class AssetCustomFieldValueResponse(CustomFieldValueBase):
    """资产自定义字段值响应模型"""
    id: int
    asset_id: int
    created_at: datetime
    updated_at: datetime
    custom_field: Optional[CustomFieldResponse] = None

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, value: datetime, _info):
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True

# 盘点记录自定义字段值模型
class InventoryRecordCustomFieldValueCreate(CustomFieldValueBase):
    """创建盘点记录自定义字段值模型"""
    inventory_record_id: Optional[int] = Field(None, description="盘点记录ID")
    task_id: Optional[int] = Field(None, description="盘点任务ID")
    asset_id: Optional[int] = Field(None, description="资产ID")

class InventoryRecordCustomFieldValueUpdate(BaseModel):
    """更新盘点记录自定义字段值模型"""
    value: Optional[str] = Field(None, max_length=10000)

class InventoryRecordCustomFieldValueResponse(CustomFieldValueBase):
    """盘点记录自定义字段值响应模型"""
    id: int
    inventory_record_id: Optional[int] = None
    task_id: Optional[int] = None
    asset_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    custom_field: Optional[CustomFieldResponse] = None

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, value: datetime, _info):
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True

# 带自定义字段值的自定义字段响应模型
class CustomFieldWithValues(CustomFieldResponse):
    """带自定义字段值的自定义字段响应模型"""
    asset_values: List[AssetCustomFieldValueResponse] = []
    inventory_values: List[InventoryRecordCustomFieldValueResponse] = []

# 自定义字段列表响应模型
class CustomFieldListResponse(BaseModel):
    """自定义字段列表响应模型"""
    data: List[CustomFieldResponse]
    total: int
    page: int = 1
    limit: int = 10

# 批量设置自定义字段值模型
class BatchSetCustomFieldValuesRequest(BaseModel):
    """批量设置自定义字段值请求模型"""
    values: List[Dict[str, Any]] = Field(..., description="字段值列表")

# 字段配置快速预设模型
class FieldPresetRequest(BaseModel):
    """字段配置快速预设请求模型"""
    preset_type: str = Field(..., description="预设类型")
    applies_to: AppliesTo = Field(AppliesTo.BOTH, description="适用范围") 