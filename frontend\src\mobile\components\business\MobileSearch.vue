<template>
  <div class="mobile-search">
    <!-- 搜索框 -->
    <van-search
      v-model="searchValue"
      :placeholder="placeholder"
      :shape="shape"
      :background="background"
      :show-action="showAction"
      :action-text="actionText"
      :disabled="disabled"
      :readonly="readonly"
      :autofocus="autofocus"
      @search="handleSearch"
      @cancel="handleCancel"
      @focus="handleFocus"
      @blur="handleBlur"
      @clear="handleClear"
    >
      <template v-if="$slots.label" #label>
        <slot name="label"></slot>
      </template>
      
      <template v-if="$slots.action" #action>
        <slot name="action"></slot>
      </template>
      
      <template v-if="$slots.leftIcon" #left-icon>
        <slot name="left-icon"></slot>
      </template>
      
      <template v-if="$slots.rightIcon" #right-icon>
        <slot name="right-icon"></slot>
      </template>
    </van-search>
    
    <!-- 搜索建议/结果 -->
    <div v-if="showSuggestions || showResults" class="search-content">
      <!-- 搜索结果 -->
      <div v-if="showResults && results.length > 0" class="search-results">
        <van-cell-group>
          <van-cell
            v-for="(result, index) in results"
            :key="index"
            :title="result.title"
            :label="result.description"
            :value="result.value"
            is-link
            @click="handleResultClick(result, index)"
          >
            <template v-if="result.icon" #icon>
              <van-icon :name="result.icon" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      
      <!-- 无搜索结果 -->
      <div v-else-if="showResults && results.length === 0" class="no-results">
        <van-empty description="暂无搜索结果" />
      </div>
      
      <!-- 搜索建议 -->
      <div v-else-if="showSuggestions" class="search-suggestions">
        <!-- 搜索历史 -->
        <div v-if="showHistory && searchHistory.length > 0" class="suggestion-section">
          <div class="section-header">
            <span class="section-title">搜索历史</span>
            <van-button
              size="mini"
              type="default"
              @click="clearHistory"
            >
              清空
            </van-button>
          </div>
          <div class="history-tags">
            <van-tag
              v-for="(item, index) in searchHistory"
              :key="index"
              size="medium"
              @click="handleHistoryClick(item)"
            >
              {{ item }}
            </van-tag>
          </div>
        </div>
        
        <!-- 热门搜索 -->
        <div v-if="showHotSearch && hotSearches.length > 0" class="suggestion-section">
          <div class="section-header">
            <span class="section-title">热门搜索</span>
          </div>
          <div class="hot-tags">
            <van-tag
              v-for="(item, index) in hotSearches"
              :key="index"
              :type="index < 3 ? 'danger' : 'default'"
              size="medium"
              @click="handleHotSearchClick(item)"
            >
              {{ item }}
            </van-tag>
          </div>
        </div>
        
        <!-- 搜索建议列表 -->
        <div v-if="suggestions.length > 0" class="suggestion-section">
          <van-cell-group>
            <van-cell
              v-for="(suggestion, index) in suggestions"
              :key="index"
              :title="suggestion"
              icon="search"
              is-link
              @click="handleSuggestionClick(suggestion)"
            />
          </van-cell-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

interface SearchResult {
  title: string
  description?: string
  value?: string
  icon?: string
  [key: string]: any
}

interface Props {
  // 搜索值
  modelValue?: string
  // 占位符
  placeholder?: string
  // 搜索框形状
  shape?: 'square' | 'round'
  // 背景色
  background?: string
  // 是否显示操作按钮
  showAction?: boolean
  // 操作按钮文字
  actionText?: string
  // 是否禁用
  disabled?: boolean
  // 是否只读
  readonly?: boolean
  // 是否自动聚焦
  autofocus?: boolean
  // 搜索结果
  results?: SearchResult[]
  // 搜索建议
  suggestions?: string[]
  // 热门搜索
  hotSearches?: string[]
  // 是否显示搜索历史
  showHistory?: boolean
  // 是否显示热门搜索
  showHotSearch?: boolean
  // 最大历史记录数
  maxHistory?: number
  // 历史存储键名
  historyStorageKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词',
  shape: 'square',
  showAction: true,
  actionText: '搜索',
  disabled: false,
  readonly: false,
  autofocus: false,
  results: () => [],
  suggestions: () => [],
  hotSearches: () => [],
  showHistory: true,
  showHotSearch: true,
  maxHistory: 10,
  historyStorageKey: 'mobile_search_history'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'search': [value: string]
  'cancel': []
  'focus': [event: Event]
  'blur': [event: Event]
  'clear': [event: MouseEvent]
  'result-click': [result: SearchResult, index: number]
  'suggestion-click': [suggestion: string]
  'history-click': [history: string]
  'hot-search-click': [hotSearch: string]
}>()

const searchValue = ref(props.modelValue || '')
const isFocused = ref(false)
const searchHistory = ref<string[]>([])

// 计算属性
const showSuggestions = computed(() => {
  return isFocused.value && !searchValue.value && props.results.length === 0
})

const showResults = computed(() => {
  return searchValue.value && props.results.length >= 0
})

// 监听搜索值变化
watch(searchValue, (newVal) => {
  emit('update:modelValue', newVal)
})

watch(() => props.modelValue, (newVal) => {
  searchValue.value = newVal || ''
})

// 事件处理
const handleSearch = (value: string) => {
  if (value.trim()) {
    addToHistory(value.trim())
    emit('search', value.trim())
  }
}

const handleCancel = () => {
  searchValue.value = ''
  isFocused.value = false
  emit('cancel')
}

const handleFocus = (event: Event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: Event) => {
  // 延迟隐藏建议，允许点击事件触发
  setTimeout(() => {
    isFocused.value = false
  }, 200)
  emit('blur', event)
}

const handleClear = (event: MouseEvent) => {
  emit('clear', event)
}

const handleResultClick = (result: SearchResult, index: number) => {
  emit('result-click', result, index)
}

const handleSuggestionClick = (suggestion: string) => {
  searchValue.value = suggestion
  addToHistory(suggestion)
  emit('suggestion-click', suggestion)
  emit('search', suggestion)
}

const handleHistoryClick = (history: string) => {
  searchValue.value = history
  emit('history-click', history)
  emit('search', history)
}

const handleHotSearchClick = (hotSearch: string) => {
  searchValue.value = hotSearch
  addToHistory(hotSearch)
  emit('hot-search-click', hotSearch)
  emit('search', hotSearch)
}

// 搜索历史管理
const loadHistory = () => {
  try {
    const saved = localStorage.getItem(props.historyStorageKey)
    if (saved) {
      searchHistory.value = JSON.parse(saved)
    }
  } catch (error) {
    console.warn('Failed to load search history:', error)
  }
}

const saveHistory = () => {
  try {
    localStorage.setItem(props.historyStorageKey, JSON.stringify(searchHistory.value))
  } catch (error) {
    console.warn('Failed to save search history:', error)
  }
}

const addToHistory = (keyword: string) => {
  if (!props.showHistory || !keyword.trim()) return
  
  // 移除已存在的相同关键词
  const index = searchHistory.value.indexOf(keyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  
  // 添加到开头
  searchHistory.value.unshift(keyword)
  
  // 限制历史记录数量
  if (searchHistory.value.length > props.maxHistory) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistory)
  }
  
  saveHistory()
}

const clearHistory = () => {
  searchHistory.value = []
  saveHistory()
}

// 生命周期
onMounted(() => {
  loadHistory()
})
</script>

<style lang="scss" scoped>
.mobile-search {
  background: var(--van-background);
}

.search-content {
  background: var(--van-background);
}

.search-results {
  .van-cell-group {
    margin: 0;
  }
}

.no-results {
  padding: var(--van-padding-xl) var(--van-padding-md);
}

.search-suggestions {
  padding: var(--van-padding-md);
}

.suggestion-section {
  margin-bottom: var(--van-padding-lg);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--van-padding-sm);
}

.section-title {
  font-size: var(--van-font-size-md);
  font-weight: 500;
  color: var(--van-text-color);
}

.history-tags,
.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--van-padding-xs);
  
  .van-tag {
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

.hot-tags {
  .van-tag {
    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      font-weight: 500;
    }
  }
}
</style> 