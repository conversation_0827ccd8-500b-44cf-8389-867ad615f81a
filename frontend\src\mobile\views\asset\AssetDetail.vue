<template>
  <div class="mobile-asset-detail">
    <!-- 页面头部 -->
    <van-nav-bar
      :title="asset?.name || '资产详情'"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-icon name="ellipsis" @click="showActionSheet = true" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" vertical>
      加载中...
    </van-loading>

    <!-- 错误状态 -->
    <van-empty v-else-if="error" image="error" description="加载失败">
      <van-button round type="primary" @click="loadAssetDetail">
        重新加载
      </van-button>
    </van-empty>

    <!-- 资产详情内容 -->
    <div v-else-if="asset" class="detail-content">
      <!-- 基本信息 -->
      <van-cell-group title="基本信息" inset>
        <van-cell title="资产编号" :value="asset.asset_number" />
        <van-cell title="资产名称" :value="asset.name" />
        <van-cell title="所属公司" :value="asset.company" />
                 <van-cell title="资产状态">
           <template #value>
             <van-tag :type="getStatusTagType(asset.status) as any">
               {{ asset.status }}
             </van-tag>
           </template>
         </van-cell>
        <van-cell title="资产类别" :value="asset.category || '--'" />
        <van-cell title="规格型号" :value="asset.specification || '--'" />
        <van-cell title="生产编号" :value="asset.production_number || '--'" />
        <van-cell title="价格" :value="asset.price ? `¥${asset.price}` : '--'" />
      </van-cell-group>

      <!-- 采购信息 -->
      <van-cell-group title="采购信息" inset>
        <van-cell title="采购日期" :value="formatDate(asset.purchase_date)" />
        <van-cell title="报废日期" :value="asset.retirement_date ? formatDate(asset.retirement_date) : '--'" />
        <van-cell title="供应商" :value="asset.supplier || '--'" />
        <van-cell title="制造商" :value="asset.manufacturer || '--'" />
        <van-cell title="采购人" :value="asset.purchaser || '--'" />
        <van-cell title="采购人工号" :value="asset.purchaser_job_number || '--'" />
      </van-cell-group>

      <!-- 使用信息 -->
      <van-cell-group title="使用信息" inset>
        <van-cell title="保管人" :value="asset.custodian" />
        <van-cell title="保管人工号" :value="asset.custodian_job_number || '--'" />
        <van-cell title="保管部门" :value="asset.custodian_department" />
        <van-cell title="使用人" :value="asset.user" />
        <van-cell title="使用人工号" :value="asset.user_job_number || '--'" />
        <van-cell title="使用部门" :value="asset.user_department" />
        <van-cell title="存放位置" :value="asset.location" />
        <van-cell title="检查人" :value="asset.inspector" />
        <van-cell title="检查人工号" :value="asset.inspector_job_number || '--'" />
      </van-cell-group>

      <!-- 自定义字段信息 -->
      <van-cell-group 
        v-if="customFields.length > 0 && !customFieldLoading" 
        title="自定义字段" 
        inset
      >
        <template v-for="field in customFields" :key="field.id">
          <van-cell 
            :title="field.label" 
            :value="formatCustomFieldValue(field, getCustomFieldValue(field.name))"
          />
        </template>
      </van-cell-group>

      <!-- 自定义字段加载状态 -->
      <van-cell-group v-if="customFieldLoading" title="自定义字段" inset>
        <van-cell title="加载中..." value="..." />
      </van-cell-group>

      <!-- 备注信息 -->
      <van-cell-group title="其他信息" inset>
        <van-cell title="备注" :value="asset.remarks || '--'" />
        <van-cell title="创建时间" :value="formatDateTime(asset.created_at)" />
        <van-cell title="更新时间" :value="formatDateTime(asset.updated_at)" />
      </van-cell-group>

      <!-- 变更记录 -->
      <van-cell-group title="变更记录" inset>
        <van-cell
          title="查看变更历史"
          is-link
          @click="showChangeLogDialog = true"
        >
          <template #value>
            <span class="change-count">{{ changeLogs.length }}条记录</span>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          type="primary"
          size="large"
          @click="handleEdit"
        >
          编辑资产
        </van-button>
        <van-button
          size="large"
          @click="handleTransfer"
        >
          转移资产
        </van-button>
      </div>
    </div>

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model:show="showActionSheet"
      :actions="actionSheetActions"
      @select="onActionSelect"
      cancel-text="取消"
    />

    <!-- 变更记录弹窗 -->
    <van-popup
      v-model:show="showChangeLogDialog"
      position="bottom"
      :style="{ height: 'var(--mobile-popup-medium-height, 58svh)' }"
      round
    >
      <div class="change-log-popup">
        <div class="popup-header">
          <h3>变更记录</h3>
          <van-icon name="cross" @click="showChangeLogDialog = false" />
        </div>
        
        <div class="change-log-content">
          <van-loading v-if="changeLogLoading" class="loading-center" vertical>
            加载中...
          </van-loading>
          
          <van-empty v-else-if="changeLogs.length === 0" description="暂无变更记录" />
          
          <div v-else class="change-log-list">
                         <div
               v-for="log in changeLogs"
               :key="log.id"
               class="change-log-item"
             >
               <div class="log-header">
                 <span class="log-action">{{ log.change_type }}</span>
                 <span class="log-time">{{ formatDateTime(log.created_at) }}</span>
               </div>
               <div class="log-details">
                 <div class="log-field">字段：{{ log.field }}</div>
                 <div v-if="log.old_value" class="log-old-value">
                   原值：{{ log.old_value }}
                 </div>
                 <div v-if="log.new_value" class="log-new-value">
                   新值：{{ log.new_value }}
                 </div>
               </div>
             </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 转移确认弹窗 -->
    <van-dialog
      v-model:show="showTransferDialog"
      title="转移资产"
      show-cancel-button
      @confirm="confirmTransfer"
    >
      <div class="transfer-form">
        <van-field
          v-model="transferForm.newCustodian"
          label="新保管人"
          placeholder="请输入新保管人"
          required
        />
        <van-field
          v-model="transferForm.newDepartment"
          label="新部门"
          placeholder="请输入新部门"
          required
        />
        <van-field
          v-model="transferForm.remarks"
          label="转移备注"
          placeholder="请输入转移原因"
          type="textarea"
          rows="3"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { assetApi } from '@/api/asset'
import { customFieldApi } from '@/api/custom_field'
import type { Asset } from '@/types/asset'
import type { AssetChangeLog } from '@/types/asset_change_log'
import type { CustomField, AssetCustomFieldValue } from '@/types/custom_field'
import { formatDate } from '@/utils/date'
import { formatCustomFieldValue } from '@/utils/customField'

const router = useRouter()
const route = useRoute()

// 数据状态
const loading = ref(false)
const error = ref(false)
const asset = ref<Asset | null>(null)
const changeLogs = ref<AssetChangeLog[]>([])
const changeLogLoading = ref(false)

// 自定义字段相关状态
const customFields = ref<CustomField[]>([])
const customFieldValues = ref<AssetCustomFieldValue[]>([])
const customFieldLoading = ref(false)

// 弹窗状态
const showActionSheet = ref(false)
const showChangeLogDialog = ref(false)
const showTransferDialog = ref(false)

// 转移表单
const transferForm = ref({
  newCustodian: '',
  newDepartment: '',
  remarks: ''
})

// 操作菜单
const actionSheetActions = [
  { name: '编辑资产', key: 'edit' },
  { name: '转移资产', key: 'transfer' },
  { name: '生成二维码', key: 'qrcode' },
  { name: '删除资产', key: 'delete', color: '#ee0a24' }
]

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    '使用中': 'primary',
    '闲置': 'warning',
    '维修中': 'danger',
    '已报废': 'default',
    '已转移': 'success',
    '库存': 'default'
  }
  return statusMap[status] || 'default'
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取自定义字段值
const getCustomFieldValue = (fieldName: string): string => {
  const fieldValue = customFieldValues.value.find(value => 
    value.custom_field?.name === fieldName
  )
  return fieldValue?.value || ''
}

// 加载自定义字段定义
const loadCustomFields = async () => {
  try {
    const response = await customFieldApi.getActiveCustomFields({ applies_to: 'asset' })
    customFields.value = response.data || response || []
  } catch (error) {
    console.error('加载自定义字段失败:', error)
    customFields.value = []
  }
}

// 加载资产的自定义字段值
const loadAssetCustomFieldValues = async (assetId: number) => {
  try {
    customFieldLoading.value = true
    const response = await customFieldApi.getAssetCustomFieldValues(assetId)
    customFieldValues.value = response.data || response || []
  } catch (error) {
    console.error('加载自定义字段值失败:', error)
    customFieldValues.value = []
  } finally {
    customFieldLoading.value = false
  }
}

// 格式化变更内容
const formatChanges = (changes: any) => {
  if (typeof changes === 'string') return changes
  if (typeof changes === 'object') {
    return Object.entries(changes)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')
  }
  return JSON.stringify(changes)
}

// 加载资产详情
const loadAssetDetail = async () => {
  const assetId = Number(route.params.id)
  if (!assetId) {
    error.value = true
    return
  }

  try {
    loading.value = true
    error.value = false
    
    const response = await assetApi.getAsset(assetId)
    asset.value = response.data || response
    
    // 同时加载变更记录和自定义字段
    await Promise.all([
      loadChangeLogs(assetId),
      loadCustomFields(),
      loadAssetCustomFieldValues(assetId)
    ])
  } catch (err) {
    console.error('加载资产详情失败:', err)
    error.value = true
    showToast('加载资产详情失败')
  } finally {
    loading.value = false
  }
}

// 加载变更记录
const loadChangeLogs = async (assetId: number) => {
  try {
    changeLogLoading.value = true
    const response = await assetApi.getAssetChangeLogs(assetId, {
      skip: 0,
      limit: 50
    })
    changeLogs.value = response.data || response || []
  } catch (err) {
    console.error('加载变更记录失败:', err)
  } finally {
    changeLogLoading.value = false
  }
}

// 操作菜单选择
const onActionSelect = (action: any) => {
  showActionSheet.value = false
  
  switch (action.key) {
    case 'edit':
      handleEdit()
      break
    case 'transfer':
      handleTransfer()
      break
    case 'qrcode':
      handleQRCode()
      break
    case 'delete':
      handleDelete()
      break
  }
}

// 编辑资产
const handleEdit = () => {
  if (!asset.value) return
  router.push(`/m/asset/edit/${asset.value.id}`)
}

// 转移资产
const handleTransfer = () => {
  showTransferDialog.value = true
}

// 确认转移
const confirmTransfer = async () => {
  if (!asset.value) return
  
  if (!transferForm.value.newCustodian || !transferForm.value.newDepartment) {
    showToast('请填写完整的转移信息')
    return
  }

  try {
    await assetApi.updateAsset(asset.value.id, {
      custodian: transferForm.value.newCustodian,
      custodian_department: transferForm.value.newDepartment,
      remarks: transferForm.value.remarks
    })
    
    showToast('转移成功')
    showTransferDialog.value = false
    
    // 重新加载资产详情
    loadAssetDetail()
    
    // 清空表单
    transferForm.value = {
      newCustodian: '',
      newDepartment: '',
      remarks: ''
    }
  } catch (err) {
    console.error('转移失败:', err)
    showToast('转移失败')
  }
}

// 生成二维码
const handleQRCode = () => {
  showToast('二维码功能开发中')
}

// 删除资产
const handleDelete = async () => {
  if (!asset.value) return
  
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个资产吗？删除后无法恢复。'
    })
    
    await assetApi.deleteAsset(asset.value.id)
    showToast('删除成功')
    router.back()
  } catch (err) {
    if (err !== 'cancel') {
      console.error('删除失败:', err)
      showToast('删除失败')
    }
  }
}

onMounted(() => {
  loadAssetDetail()
})
</script>

<style lang="scss" scoped>
.mobile-asset-detail {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding-bottom: 100px;
}

:deep(.van-cell-group) {
  margin-bottom: 16px;
}

.action-buttons {
  padding: 20px 16px;
  display: flex;
  gap: 12px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--van-border-color);
  z-index: 100;
  
  .van-button {
    flex: 1;
  }
}

.change-log-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--van-border-color);
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.change-log-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.change-log-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.change-log-item {
  background: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid var(--van-border-color);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-action {
  background: var(--van-primary-color);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.log-time {
  font-size: 12px;
  color: #666;
}

.log-details {
  font-size: 14px;
  
  .log-field {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .log-old-value {
    color: #f56c6c;
    margin-bottom: 2px;
  }
  
  .log-new-value {
    color: #67c23a;
  }
}

.change-count {
  color: var(--van-primary-color);
  font-size: 14px;
}

.transfer-form {
  padding: 16px;
  
  .van-field {
    margin-bottom: 12px;
  }
}
</style> 