#!/usr/bin/env python3
"""
为相关角色添加inventory:export权限
"""

from app.database import SessionLocal
from app.crud.permission import permission_crud
from app.crud.role import role_crud

def main():
    db = SessionLocal()
    try:
        # 获取inventory:export权限
        permission = permission_crud.get_by_code(db, code='inventory:export')
        if not permission:
            print('权限inventory:export不存在')
            return
        
        print(f'找到权限: {permission.name} ({permission.code})')
        
        # 获取相关角色
        roles_to_update = ['super_admin', 'asset_admin']
        
        for role_code in roles_to_update:
            role = role_crud.get_by_code(db, code=role_code)
            if not role:
                print(f'角色{role_code}不存在')
                continue
                
            # 检查是否已有该权限
            has_permission = any(p.id == permission.id for p in role.permissions)
            
            if not has_permission:
                # 添加权限
                role_crud.add_permission(db, role_id=role.id, permission_id=permission.id)
                print(f'为角色{role.name}添加了inventory:export权限')
            else:
                print(f'角色{role.name}已拥有inventory:export权限')
                
        print('权限分配完成')
        
    except Exception as e:
        print(f'错误: {e}')
    finally:
        db.close()

if __name__ == '__main__':
    main() 