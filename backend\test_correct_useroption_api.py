#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试腾讯企业邮箱 正确的useroption/update API接口
"""

import asyncio
import httpx
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService

async def test_correct_useroption_api():
    """测试正确的useroption API接口"""
    print("=== 测试腾讯企业邮箱 正确的useroption/update API接口 ===\n")
    
    db = SessionLocal()
    
    try:
        # 1. 初始化API服务
        print("1. 初始化API服务...")
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        print(f"✅ 成功创建API服务")
        print(f"   应用名称: {api_service.app_name}")
        print(f"   基础URL: {api_service.base_url}")
        
        # 2. 获取访问令牌
        print("\n2. 获取访问令牌...")
        try:
            access_token = await api_service.get_access_token()
            print(f"✅ 成功获取访问令牌: {access_token[:20]}...")
        except Exception as e:
            print(f"❌ 获取访问令牌失败: {str(e)}")
            return
        
        # 3. 首先测试 useroption/get 接口确认当前设置
        print("\n3. 获取用户当前的登录权限设置...")
        test_userid = "<EMAIL>"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            get_url = f"{api_service.base_url}/useroption/get?access_token={access_token}"
            get_data = {
                "userid": test_userid,
                "type": [1, 2, 3, 4]
            }
            
            print(f"   请求URL: {get_url}")
            print(f"   请求参数: {json.dumps(get_data, ensure_ascii=False)}")
            
            get_response = await client.post(get_url, json=get_data)
            print(f"   响应状态码: {get_response.status_code}")
            print(f"   响应内容: {get_response.text}")
            
            current_options = []
            if get_response.status_code == 200:
                get_result = get_response.json()
                if get_result.get("errcode") == 0:
                    current_options = get_result.get("option", [])
                    print(f"   ✅ 当前用户选项: {current_options}")
                else:
                    print(f"   ❌ 获取用户选项失败: {get_result.get('errcode')} - {get_result.get('errmsg')}")
                    return
            else:
                print(f"   ❌ HTTP错误: {get_response.status_code}")
                return
        
        # 4. 测试正确的 useroption/update 接口
        print("\n4. 测试正确的 useroption/update 接口...")
        
        # 使用正确的接口路径
        update_url = f"{api_service.base_url}/useroption/update?access_token={access_token}"
        update_data = {
            "userid": test_userid,
            "option": [
                {"type": 1, "value": "0"},  # 强制启用安全登录：关闭
                {"type": 2, "value": "1"},  # IMAP/SMTP服务：开启
                {"type": 3, "value": "1"},  # POP/SMTP服务：开启
                {"type": 4, "value": "0"}   # 是否启用安全登录：关闭
            ]
        }
        
        print(f"   请求URL: {update_url}")
        print(f"   请求参数: {json.dumps(update_data, ensure_ascii=False, indent=2)}")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            update_response = await client.post(update_url, json=update_data)
            print(f"   响应状态码: {update_response.status_code}")
            print(f"   响应内容: {update_response.text}")
            
            if update_response.status_code == 200:
                update_result = update_response.json()
                update_errcode = update_result.get("errcode")
                update_errmsg = update_result.get("errmsg")
                
                if update_errcode == 0:
                    print("   🎉 ✅ useroption/update 接口调用成功!")
                    print("   🎉 登录权限管理更新成功!")
                    
                    # 5. 验证设置是否生效
                    print("\n5. 验证设置是否生效...")
                    verify_response = await client.post(get_url, json=get_data)
                    if verify_response.status_code == 200:
                        verify_result = verify_response.json()
                        if verify_result.get("errcode") == 0:
                            new_options = verify_result.get("option", [])
                            print(f"   ✅ 更新后的用户选项: {new_options}")
                            print("   🎉 设置已成功生效!")
                        else:
                            print(f"   ❌ 验证失败: {verify_result.get('errcode')} - {verify_result.get('errmsg')}")
                    
                elif update_errcode == 602005:
                    print("   ❌ useroption/update 权限错误 (602005): 接口无权限访问")
                    print("   💡 建议检查功能设置应用的接口权限配置")
                elif update_errcode == 601004:
                    print("   ❌ 无效的corpsecret (601004)")
                elif update_errcode == 601014:
                    print("   ❌ 访问令牌错误 (601014)")
                else:
                    print(f"   ❌ useroption/update 其他API错误: {update_errcode} - {update_errmsg}")
                    
            elif update_response.status_code == 404:
                print("   ❌ useroption/update 接口返回404 - 请检查接口路径")
            else:
                print(f"   ❌ useroption/update 接口HTTP错误: {update_response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    finally:
        db.close()

if __name__ == "__main__":
    print("开始测试腾讯企业邮箱正确的useroption/update接口...\n")
    asyncio.run(test_correct_useroption_api())
    print("\n=== 测试完成 ===") 