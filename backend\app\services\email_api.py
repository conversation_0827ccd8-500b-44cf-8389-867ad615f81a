import httpx
import json
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from app.crud.email import email_config
from app.schemas.email import EmailAPIResponse
import logging
from app.models.email import EmailConfig

class TencentEmailAPIService:
    """腾讯企业邮箱API服务"""
    
    def __init__(self, db: Session, app_name: str = None):
        self.db = db
        
        # 如果指定了应用名称，则尝试获取该应用的配置
        if app_name:
            self.config = db.query(EmailConfig).filter(
                EmailConfig.app_name == app_name,
                EmailConfig.is_active == True
            ).first()
        else:
            # 否则获取任意活跃配置
            self.config = email_config.get_active(db)
            
        if not self.config:
            raise ValueError("未找到有效的邮箱配置")
        
        self.base_url = self.config.api_base_url
        self.corp_id = self.config.corp_id
        self.corp_secret = self.config.corp_secret
        self.app_name = self.config.app_name
        
        # 不同操作的超时配置
        self.timeout_config = {
            'token': 15.0,           # 获取token的超时时间
            'normal': 60.0,          # 普通操作超时时间
            'batch': 120.0,          # 批量操作超时时间
            'sync': 300.0,           # 大量数据同步超时时间
        }

    async def get_access_token(self) -> str:
        """获取访问令牌（改进版本，增强验证和错误处理）"""
        logger = logging.getLogger(__name__)
        
        # 检查token是否仍然有效
        if (self.config.access_token and 
            self.config.token_expires_at and 
            self.config.token_expires_at > datetime.now(timezone.utc)):
            # 验证token是否真实有效（测试API调用）
            is_valid = await self._validate_access_token(self.config.access_token)
            if is_valid:
                logger.info("使用缓存的access_token")
                return self.config.access_token
            else:
                logger.warning("存储的access_token验证失败，需要重新获取")
        
        logger.info("获取新的access_token")
        
        url = f"{self.base_url}/gettoken"
        params = {
            "corpid": self.corp_id,
            "corpsecret": self.corp_secret
        }
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout_config['token']) as client:
                response = await client.get(url, params=params)
                
                if response.status_code != 200:
                    raise Exception(f"HTTP请求失败，状态码: {response.status_code}")
                
                data = response.json()
                logger.info(f"获取token响应: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")
                
                if data.get("errcode") == 0:
                    access_token = data.get("access_token")
                    expires_in = data.get("expires_in", 7200)
                    
                    if not access_token:
                        raise Exception("获取到的access_token为空")
                    
                    # 更新配置
                    self.config.access_token = access_token
                    self.config.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in - 300)  # 提前5分钟过期
                    self.db.add(self.config)
                    self.db.commit()
                    
                    logger.info(f"成功获取新的access_token，过期时间: {self.config.token_expires_at}")
                    return access_token
                else:
                    error_msg = f"获取访问令牌失败: {data.get('errmsg')} (错误码: {data.get('errcode')})"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                    
        except httpx.TimeoutException:
            logger.error("获取access_token超时")
            raise Exception("获取访问令牌超时")
        except Exception as e:
            logger.error(f"获取access_token异常: {str(e)}")
            raise Exception(f"获取访问令牌失败: {str(e)}")

    async def _validate_access_token(self, token: str) -> bool:
        """验证access_token是否有效"""
        try:
            # 使用部门列表接口验证token
            url = f"{self.base_url}/department/list"
            params = {"access_token": token}
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    # 如果返回的不是token相关错误，说明token有效
                    return data.get("errcode") not in [40014, 41001, 42001]
                return False
        except Exception:
            return False

    async def request(self, method: str, endpoint: str, data: Optional[Dict] = None, max_retries: int = 2, operation_type: str = 'normal') -> EmailAPIResponse:
        """
        统一的API请求方法
        
        Args:
            method: HTTP方法 (GET/POST)
            endpoint: API端点
            data: 请求数据
            max_retries: 最大重试次数
            operation_type: 操作类型，用于确定超时时间 ('normal', 'batch', 'sync')
        """
        logger = logging.getLogger(__name__)
        for attempt in range(max_retries + 1):
            try:
                # 获取访问令牌
                access_token = await self.get_access_token()
                url = f"{self.base_url}/{endpoint}?access_token={access_token}"
                
                # 记录请求信息（过滤敏感信息）
                safe_data = {k: v for k, v in (data or {}).items() if k not in ["password", "secret"]}
                logger.info(f"API请求: {method} {endpoint}, 参数: {safe_data}, 尝试次数: {attempt + 1}")
                
                # 根据操作类型选择超时时间
                timeout = self.timeout_config.get(operation_type, self.timeout_config['normal'])
                logger.debug(f"使用超时设置: {timeout}秒 (操作类型: {operation_type})")
                
                async with httpx.AsyncClient(timeout=timeout) as client:
                    if method.upper() == "GET":
                        # 对于GET请求，如果有额外参数，需要合并到URL中
                        if data:
                            # 构建完整的URL参数
                            all_params = {"access_token": access_token}
                            all_params.update(data)
                            final_url = f"{self.base_url}/{endpoint}"
                            response = await client.get(final_url, params=all_params)
                        else:
                            response = await client.get(url)
                    else:
                        response = await client.post(url, json=data)
                    
                    # 记录响应详情
                    logger.info(f"响应状态码: {response.status_code}")
                    logger.debug(f"响应内容: {response.text}")
                    
                    # 检查HTTP状态码
                    if response.status_code != 200:
                        logger.error(f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}")
                        return EmailAPIResponse(
                            errcode=-1,
                            errmsg=f"HTTP请求失败，状态码: {response.status_code}",
                            data={"status_code": response.status_code, "response_text": response.text}
                        )
                    
                    try:
                        result = response.json()
                        logger.debug(f"API响应JSON解析成功: {result}")
                    except Exception as e:
                        logger.error(f"响应不是有效的JSON格式: {response.text}")
                        return EmailAPIResponse(
                            errcode=-1,
                            errmsg=f"响应不是有效的JSON格式: {str(e)}",
                            data={"response_text": response.text}
                        )

                    # 检查是否为token相关错误，需要重试
                    if result.get("errcode") in [40014, 41001, 42001]:  # access_token相关错误
                        if attempt < max_retries:
                            logger.warning(f"Access token错误 (errcode: {result.get('errcode')})，清除缓存token并重试")
                            # 清除当前token，强制重新获取
                            self.config.access_token = None
                            self.config.token_expires_at = None
                            self.db.commit()
                            continue
                        else:
                            logger.error(f"重试{max_retries}次后仍然失败: {result.get('errmsg')}")

                    logger.info(f"API请求完成: errcode={result.get('errcode')}, errmsg={result.get('errmsg')}")

                    return EmailAPIResponse(
                        errcode=result.get("errcode", -1),
                        errmsg=result.get("errmsg", "Unknown error"),
                        data=result
                    )
                    
            except httpx.TimeoutException:
                if attempt < max_retries:
                    logger.warning(f"API请求超时，第{attempt + 1}次重试")
                    continue
                else:
                    logger.error(f"腾讯企业邮箱API请求超时: {endpoint}")
                    return EmailAPIResponse(
                        errcode=-1,
                        errmsg="API请求超时",
                        data={}
                    )
            except Exception as e:
                if attempt < max_retries:
                    logger.warning(f"API请求异常，第{attempt + 1}次重试: {str(e)}")
                    continue
                else:
                    logger.error(f"腾讯企业邮箱API请求异常: {str(e)}", exc_info=True)
                    return EmailAPIResponse(
                        errcode=-1,
                        errmsg=f"API请求异常: {str(e)}",
                        data={}
                    )
        
        # 理论上不会到达这里
        return EmailAPIResponse(
            errcode=-1,
            errmsg="未知错误",
            data={}
        )

    # 部门管理API
    async def create_department(self, dept_data: Dict[str, Any]) -> EmailAPIResponse:
        """创建部门"""
        return await self.request("POST", "department/create", dept_data)

    async def update_department(self, dept_data: Dict[str, Any]) -> EmailAPIResponse:
        """更新部门"""
        return await self.request("POST", "department/update", dept_data)

    async def delete_department(self, dept_id: str) -> EmailAPIResponse:
        """删除部门"""
        return await self.request("GET", "department/delete", {"id": dept_id})

    async def get_department_list(self, dept_id: Optional[str] = None) -> EmailAPIResponse:
        """获取部门列表"""
        params = {}
        if dept_id:
            params["id"] = dept_id
        return await self.request("GET", "department/list", params)

    async def search_department(self, dept_name: str, fuzzy: bool = True) -> EmailAPIResponse:
        """查找部门
        
        Args:
            dept_name: 部门名称
            fuzzy: 是否模糊匹配，True表示模糊匹配，False表示精确匹配
        """
        return await self.request("POST", "department/search", {
            "name": dept_name,
            "fuzzy": 1 if fuzzy else 0
        })

    # 成员管理API
    async def create_member(self, member_data: Dict[str, Any]) -> EmailAPIResponse:
        """创建成员"""
        return await self.request("POST", "user/create", member_data)

    async def update_member(self, member_data: Dict[str, Any]) -> EmailAPIResponse:
        """更新成员"""
        return await self.request("POST", "user/update", member_data)

    async def delete_member(self, userid: str) -> EmailAPIResponse:
        """删除成员"""
        return await self.request("GET", "user/delete", {"userid": userid})

    async def disable_member(self, userid: str) -> EmailAPIResponse:
        """禁用成员"""
        return await self.request("POST", "user/update", {
            "userid": userid,
            "enable": 0
        })

    async def enable_member(self, userid: str) -> EmailAPIResponse:
        """启用成员"""
        return await self.request("POST", "user/update", {
            "userid": userid,
            "enable": 1
        })

    async def get_member(self, userid: str) -> EmailAPIResponse:
        """获取成员"""
        return await self.request("GET", "user/get", {"userid": userid})

    async def get_department_members(self, department_id: str, fetch_child: int = 0) -> EmailAPIResponse:
        """获取部门成员"""
        return await self.request("GET", "user/simplelist", {
            "department_id": department_id,
            "fetch_child": fetch_child
        })

    async def get_department_members_detail(self, department_id: str, fetch_child: int = 0) -> EmailAPIResponse:
        """获取部门成员详情"""
        return await self.request("GET", "user/list", {
            "department_id": department_id,
            "fetch_child": fetch_child
        }, operation_type='sync')

    async def batch_check_member(self, userid_list: List[str]) -> EmailAPIResponse:
        """批量检查账号"""
        return await self.request("POST", "user/batchcheck", {"userid_list": userid_list}, operation_type='batch')

    async def search_member(self, fuzzy_name: str) -> EmailAPIResponse:
        """模糊搜索成员"""
        return await self.request("POST", "user/search", {"fuzzy_name": fuzzy_name})

    # 标签管理API
    async def create_tag(self, tag_data: Dict[str, Any]) -> EmailAPIResponse:
        """创建标签"""
        return await self.request("POST", "tag/create", tag_data)

    async def update_tag(self, tag_data: Dict[str, Any]) -> EmailAPIResponse:
        """更新标签名字"""
        return await self.request("POST", "tag/update", tag_data)

    async def delete_tag(self, tagid: int) -> EmailAPIResponse:
        """删除标签"""
        return await self.request("GET", "tag/delete", {"tagid": tagid})

    async def get_tag_members(self, tagid: int) -> EmailAPIResponse:
        """获取标签成员"""
        return await self.request("GET", "tag/get", {"tagid": tagid})

    async def add_tag_members(self, tagid: int, userlist: List[str]) -> EmailAPIResponse:
        """增加标签成员"""
        return await self.request("POST", "tag/addtagusers", {
            "tagid": tagid,
            "userlist": userlist
        })

    async def delete_tag_members(self, tagid: int, userlist: List[str]) -> EmailAPIResponse:
        """删除标签成员"""
        return await self.request("POST", "tag/deltagusers", {
            "tagid": tagid,
            "userlist": userlist
        })

    async def get_tag_list(self) -> EmailAPIResponse:
        """获取标签列表"""
        return await self.request("GET", "tag/list")

    # 邮件群组管理API
    async def create_group(self, group_data: Dict[str, Any]) -> EmailAPIResponse:
        """创建邮件群组"""
        return await self.request("POST", "group/create", group_data)

    async def update_group(self, group_data: Dict[str, Any]) -> EmailAPIResponse:
        """更新邮件群组"""
        return await self.request("POST", "group/update", group_data)

    async def delete_group(self, groupid: str) -> EmailAPIResponse:
        """删除邮件群组"""
        return await self.request("GET", "group/delete", {"groupid": groupid})

    async def get_group(self, groupid: str) -> EmailAPIResponse:
        """获取邮件群组信息"""
        return await self.request("GET", "group/get", {"groupid": groupid})

    async def search_group(self, fuzzy_name: str) -> EmailAPIResponse:
        """模糊搜索邮件群组
        
        注意：此接口可能在某些腾讯企业邮箱版本中不可用
        """
        return await self.request("POST", "group/search", {"fuzzy_name": fuzzy_name})

    async def get_group_list(self) -> EmailAPIResponse:
        """获取群组列表
        
        注意：腾讯企业邮箱API可能不支持直接获取群组列表
        这里先返回一个模拟的空列表响应，避免404错误
        """
        # 暂时返回空列表，避免调用不存在的接口
        return EmailAPIResponse(
            errcode=0,
            errmsg="群组列表功能暂不可用",
            data={"grouplist": []}
        )

    # 功能设置API
    async def get_function_settings(self, type: str) -> EmailAPIResponse:
        """获取功能属性"""
        return await self.request("GET", "service/get_function", {"type": type})

    async def set_function_settings(self, type: str, value: str) -> EmailAPIResponse:
        """更改功能属性"""
        return await self.request("POST", "service/set_function", {
            "type": type,
            "value": value
        })

    # 用户选项管理API - 登录权限管理
    async def get_user_option(self, userid: str, option_types: List[int]) -> EmailAPIResponse:
        """获取用户功能属性
        
        Args:
            userid: 成员邮箱地址
            option_types: 功能设置属性类型列表
                1: 强制启用安全登录
                2: IMAP/SMTP服务
                3: POP/SMTP服务
                4: 是否启用安全登录
        """
        return await self.request("POST", "useroption/get", {
            "userid": userid,
            "type": option_types
        })

    async def set_user_option(self, userid: str, options: List[Dict[str, Union[int, str]]]) -> EmailAPIResponse:
        """设置用户功能属性
        
        Args:
            userid: 成员邮箱地址
            options: 功能设置属性列表，格式: [{"type": 1, "value": "0"}, {"type": 2, "value": "1"}]
                type: 1-强制启用安全登录, 2-IMAP/SMTP服务, 3-POP/SMTP服务, 4-是否启用安全登录
                value: "0"-关闭, "1"-开启
        """
        return await self.request("POST", "useroption/update", {
            "userid": userid,
            "option": options
        })

    # 单点登录API
    async def get_login_url(self, userid: str) -> EmailAPIResponse:
        """获取登录企业邮的url"""
        return await self.request("POST", "service/get_login_url", {"userid": userid})

    async def get_users_options_batch(self, userids: List[str], option_types: List[int], 
                                     batch_size: int = 10, max_concurrent: int = 5) -> Dict[str, EmailAPIResponse]:
        """批量获取多个用户的功能属性（并发控制版本）
        
        Args:
            userids: 成员邮箱地址列表
            option_types: 功能设置属性类型列表
                1: 强制启用安全登录
                2: IMAP/SMTP服务
                3: POP/SMTP服务
                4: 是否启用安全登录
            batch_size: 每批处理的用户数量
            max_concurrent: 最大并发请求数
            
        Returns:
            Dict[str, EmailAPIResponse]: 用户ID到权限响应的映射
        """
        import asyncio
        from collections import deque
        import logging
        
        logger = logging.getLogger(__name__)
        results = {}
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def get_user_option_with_semaphore(userid):
            async with semaphore:
                try:
                    result = await self.get_user_option(userid, option_types)
                    return userid, result
                except Exception as e:
                    logger.error(f"获取用户 {userid} 的权限失败: {str(e)}")
                    return userid, EmailAPIResponse(
                        errcode=-1,
                        errmsg=f"获取权限失败: {str(e)}",
                        data={}
                    )
        
        # 分批处理用户
        for i in range(0, len(userids), batch_size):
            batch_userids = userids[i:i+batch_size]
            logger.info(f"处理用户权限批次 {i//batch_size + 1}/{(len(userids) + batch_size - 1)//batch_size}，包含 {len(batch_userids)} 个用户")
            
            # 创建当前批次的任务
            tasks = [get_user_option_with_semaphore(userid) for userid in batch_userids]
            
            # 等待当前批次完成
            batch_results = await asyncio.gather(*tasks)
            
            # 处理结果
            for userid, result in batch_results:
                results[userid] = result
            
            # 短暂暂停，避免API限流
            await asyncio.sleep(0.5)
        
        return results 