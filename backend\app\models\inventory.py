from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Integer, Foreign<PERSON>ey, Enum, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models import Base

class InventoryTask(Base):
    """资产盘点任务模型"""
    __tablename__ = "inventory_tasks"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), comment="盘点任务名称")
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="任务描述")
    status: Mapped[str] = mapped_column(
        String(20), 
        comment="任务状态",
        default="pending"  # pending: 待盘点, in_progress: 盘点中, completed: 已完成
    )
    start_date: Mapped[datetime] = mapped_column(DateTime, comment="开始日期")
    end_date: Mapped[datetime] = mapped_column(DateTime, comment="结束日期")
    created_by: Mapped[str] = mapped_column(String(50), comment="创建人")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    # 关联的盘点记录
    inventory_records = relationship("InventoryRecord", back_populates="task", cascade="all, delete-orphan")
    # 添加对虚拟自定义字段值的级联删除关系
    virtual_custom_field_values = relationship("InventoryRecordCustomFieldValue", 
                                              foreign_keys="InventoryRecordCustomFieldValue.task_id",
                                              cascade="all, delete-orphan")

class InventoryRecord(Base):
    """资产盘点记录模型"""
    __tablename__ = "inventory_records"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(ForeignKey("inventory_tasks.id"), comment="盘点任务ID")
    asset_id: Mapped[int] = mapped_column(ForeignKey("assets.id"), comment="资产ID")
    status: Mapped[str] = mapped_column(
        String(20), 
        comment="盘点状态",
        default="pending"  # pending: 待盘点, normal: 正常, abnormal: 异常, missing: 丢失, info_changed: 信息变更
    )
    # 资产信息变更字段
    new_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新资产名称")
    new_specification: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="新规格型号")
    new_status: Mapped[Optional[str]] = mapped_column(String(20), nullable=True, comment="新资产状态")
    new_custodian: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="新领用人")
    new_custodian_department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新领用人部门")
    new_user: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="新使用人")
    new_user_department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新使用人部门")
    new_company: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新公司")
    new_location: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="新存放位置")
    new_remarks: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="新备注")
    new_production_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新生产编号")
    new_price: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="新价格")
    new_supplier: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新供应商")
    new_manufacturer: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="新制造商")
    new_purchaser: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="新采购人")
    
    remarks: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="盘点备注")
    checked_by: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="盘点人")
    checked_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="盘点时间")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    # 关联
    task = relationship("InventoryTask", back_populates="inventory_records")
    asset = relationship("Asset", lazy='joined')
    # 添加对自定义字段值的级联删除关系
    custom_field_values = relationship("InventoryRecordCustomFieldValue", 
                                     foreign_keys="InventoryRecordCustomFieldValue.inventory_record_id",
                                     cascade="all, delete-orphan") 