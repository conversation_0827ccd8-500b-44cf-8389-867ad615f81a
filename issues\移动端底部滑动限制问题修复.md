# 移动端底部滑动限制问题修复

## 问题描述
移动端页面无法滑动到最底部，像是有什么限制一样。用户在资产管理等页面中，底部内容被固定的底部标签栏遮挡，无法查看完整内容。

## 问题分析
1. **底部标签栏固定定位**：`MobileTabbar.vue` 使用了 `van-tabbar` 的 `:fixed="true"` 属性
2. **主内容区域缺少底部间距**：`MobileLayout.vue` 中的 `.mobile-main` 只为顶部导航栏留出了空间，没有为底部标签栏预留空间
3. **内容被遮挡**：页面最底部的内容被固定的底部标签栏覆盖，用户无法滑动到真正的底部

## 解决方案
为主内容区域添加底部间距，确保内容不被底部标签栏遮挡。

### 修改文件
**文件**: `frontend/src/mobile/layout/MobileLayout.vue`

### 具体修改

#### 1. 主内容区域添加底部间距
```scss
.mobile-main {
  flex: 1;
  overflow-y: auto;
  position: relative;
  
  // 为固定头部和底部标签栏留出空间
  padding-top: var(--mobile-header-height, 50px);
  padding-bottom: var(--mobile-tabbar-height, 50px);  // 新增
  padding-left: var(--theme-space-sm, 8px);
  padding-right: var(--theme-space-sm, 8px);
  
  // 背景色
  background-color: var(--theme-bg-page);
}
```

#### 2. 移除响应式样式中的padding-bottom覆盖
移除了各个响应式断点中对 `padding-bottom` 的覆盖设置，确保底部标签栏间距在所有设备上都生效：

- 紧凑模式 (`&--compact`)
- 容器查询 (`@container (min-width: 768px)`)
- 大屏适配 (`@container (min-width: 414px)`)
- 横屏适配 (`@media screen and (orientation: landscape)`)

## 技术细节

### CSS变量使用
- 使用 `var(--mobile-tabbar-height, 50px)` 确保在不同响应式断点下使用正确的标签栏高度
- 默认值为50px，在不同设备上会自动调整为44px或56px

### 响应式适配
底部间距会根据设备大小自动调整：
- 小屏设备：50px
- 中等设备：50px
- 大屏设备：56px
- 紧凑模式：44px
- 横屏模式：44px

## 测试验证
修复后，用户应该能够：
1. 在移动端页面中正常滑动到最底部
2. 查看到所有内容，不被底部标签栏遮挡
3. 在不同设备尺寸下都有正确的底部间距
4. 在横屏模式下也能正常滑动

## 影响范围
- 所有移动端页面的滚动体验
- 不影响桌面端功能
- 兼容现有的主题和响应式设计

## 相关文件
- `frontend/src/mobile/layout/MobileLayout.vue` - 主要修改文件
- `frontend/src/mobile/layout/components/MobileTabbar.vue` - 底部标签栏组件
- `frontend/src/mobile/styles/variables.scss` - CSS变量定义 