# AD安全组直接重命名优化

## 问题描述
用户反馈：当前AD同步中，如果检测到安全组名称变化会单独新建一个组，希望能直接修改安全组名称，这样更符合使用情况，比如共享文件就不用在单独的添加组了。

## 解决方案
将原有的"标记删除+合并成员"策略改为"直接重命名"策略，保持权限连续性，避免共享文件夹权限断开。

## 核心修改

### 1. 主要修改文件
- `backend/app/services/ad.py` - AD同步服务
- 利用现有的 `backend/app/utils/ad_client.py` 中的 `update_group` 方法

### 2. 关键优化点

#### 原有逻辑（问题）：
```python
# 检测到名称变化时：
# 1. 标记旧组为"待删除"
# 2. 合并成员到新组
# 3. 导致权限配置断开
```

#### 新逻辑（优化）：
```python
# 检测到名称变化时：
# 1. 直接重命名现有安全组
# 2. 保持DN和成员关系不变
# 3. 确保权限配置持续有效
```

### 3. 处理策略

#### 场景1：不存在标准名称的安全组
- 直接重命名第一个安全组为标准名称
- 将其他同部门安全组的成员合并到重命名后的主组
- 标记其他组为待删除

#### 场景2：已存在标准名称的安全组
- 保留现有逻辑：合并成员到标准组
- 标记其他组为待删除

#### 场景3：名称冲突处理
- 检查目标名称是否被其他部门的安全组占用
- 如有冲突，记录警告并跳过重命名

## 技术实现

### 1. AD客户端支持
AD客户端的 `update_group` 方法已支持安全组重命名：
```python
await ad_client.update_group(group_dn, {
    'name': new_name,  # 自动更新 sAMAccountName 和 cn
    'description': new_description
})
```

### 2. 重命名逻辑
```python
# 直接重命名主安全组为标准名称
await ad_client.update_group(primary_group['dn'], {
    'name': std_name,
    'description': f"部门 {dept_name} 的安全组 (部门ID: {dept_id})"
})
logger.info(f"已直接重命名安全组: '{old_name}' -> '{std_name}' (保持权限连续性)")
```

### 3. 冲突检测和处理
```python
# 检查目标名称是否被其他安全组占用
name_conflict = False
for existing_group in existing_standard_groups:
    conflict_dept_id = extract_dept_id_from_group(existing_group.get('description', ''))
    if conflict_dept_id is not None and conflict_dept_id != dept_id:
        name_conflict = True
        break
```

## 实施效果

### 优势
1. **权限连续性**：保持共享文件夹等权限配置不断开
2. **简化管理**：避免需要重新配置文件夹权限
3. **用户友好**：符合实际使用场景的期望行为
4. **安全性**：保持安全组DN不变，确保引用有效

### 兼容性
- 保持现有的合并逻辑用于处理真正的重复安全组
- 保持现有的部门ID识别机制
- 向后兼容现有的配置选项

## 配置选项
此优化在现有配置项 `auto_rename_security_groups` 启用时生效，无需额外配置。

## 测试建议
1. 测试部门名称变化后的安全组重命名
2. 验证共享文件夹权限是否保持有效
3. 测试名称冲突的处理情况
4. 验证成员关系保持完整

## 日志输出
新的日志消息能清楚表明采用了直接重命名策略：
```
已直接重命名安全组: '旧名称' -> '新名称' (保持权限连续性)
安全组名称检查和重命名完成，共处理 X 个安全组（采用直接重命名策略保持权限连续性）
``` 