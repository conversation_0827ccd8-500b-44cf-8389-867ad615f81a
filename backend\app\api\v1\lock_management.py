"""
锁管理API

提供同步锁的状态查询、清理和监控功能
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import logging

from app.database import get_db
from app.services import ad_sync_lock, email_sync_lock
from app.services.lock_cleanup_service import lock_cleanup_service
from app.api.deps import get_current_active_user, check_permissions
from app import models

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/locks", tags=["锁管理"])

def get_user_permissions(db: Session, user: models.User) -> set:
    """获取用户权限集合"""
    if user.is_superuser:
        return {"*"}  # 超级管理员拥有所有权限
    
    # 获取用户所有角色和权限
    user_with_roles = db.query(models.User).filter(
        models.User.id == user.id
    ).first()
    
    permissions = set()
    if user_with_roles and user_with_roles.roles:
        for role in user_with_roles.roles:
            for permission in role.permissions:
                permissions.add(permission.code)
    
    return permissions

def has_permission(db: Session, user: models.User, required_permissions: list) -> bool:
    """检查用户是否具有所需权限"""
    if user.is_superuser:
        return True
    
    user_permissions = get_user_permissions(db, user)
    return any(perm in user_permissions for perm in required_permissions)

@router.get("/status", response_model=Dict[str, Any])
async def get_locks_status(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    获取所有锁的状态信息
    
    需要管理员权限
    """
    try:
        # 检查用户权限
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        
        # 获取锁状态
        status_info = await lock_cleanup_service.get_locks_status()
        
        return {
            "success": True,
            "data": status_info,
            "message": "获取锁状态成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取锁状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取锁状态失败: {str(e)}"
        )

@router.get("/ad/status", response_model=Dict[str, Any])
async def get_ad_locks_status(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    获取AD同步锁状态
    
    需要AD管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["ad:read"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要AD管理权限"
            )
        
        # 获取AD锁状态
        ad_locks = await ad_sync_lock.get_all_locks_status(db)
        
        # 统计信息
        active_count = sum(1 for lock in ad_locks if lock["is_locked"] and not lock["is_expired"])
        expired_count = sum(1 for lock in ad_locks if lock["is_locked"] and lock["is_expired"])
        
        return {
            "success": True,
            "data": {
                "total": len(ad_locks),
                "active": active_count,
                "expired": expired_count,
                "locks": ad_locks
            },
            "message": "获取AD锁状态成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AD锁状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AD锁状态失败: {str(e)}"
        )

@router.get("/email/status", response_model=Dict[str, Any])
async def get_email_locks_status(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    获取邮箱同步锁状态
    
    需要邮箱管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["email:read"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要邮箱管理权限"
            )
        
        # 获取邮箱锁状态
        email_locks = await email_sync_lock.get_all_locks_status(db)
        
        # 统计信息
        active_count = sum(1 for lock in email_locks if lock["is_locked"] and not lock["is_expired"])
        expired_count = sum(1 for lock in email_locks if lock["is_locked"] and lock["is_expired"])
        
        return {
            "success": True,
            "data": {
                "total": len(email_locks),
                "active": active_count,
                "expired": expired_count,
                "locks": email_locks
            },
            "message": "获取邮箱锁状态成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮箱锁状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取邮箱锁状态失败: {str(e)}"
        )

@router.get("/ad/{lock_name}", response_model=Dict[str, Any])
async def get_ad_lock_info(
    lock_name: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    获取指定AD锁的详细信息
    
    需要AD管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["ad:read"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要AD管理权限"
            )
        
        # 获取锁信息
        lock_info = await ad_sync_lock.get_lock_info(db, lock_name)
        
        if not lock_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AD锁 '{lock_name}' 不存在"
            )
        
        return {
            "success": True,
            "data": lock_info,
            "message": "获取AD锁信息成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AD锁信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AD锁信息失败: {str(e)}"
        )

@router.get("/email/{lock_name}", response_model=Dict[str, Any])
async def get_email_lock_info(
    lock_name: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    获取指定邮箱锁的详细信息
    
    需要邮箱管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["email:read"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要邮箱管理权限"
            )
        
        # 获取锁信息
        lock_info = await email_sync_lock.get_lock_info(db, lock_name)
        
        if not lock_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"邮箱锁 '{lock_name}' 不存在"
            )
        
        return {
            "success": True,
            "data": lock_info,
            "message": "获取邮箱锁信息成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮箱锁信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取邮箱锁信息失败: {str(e)}"
        )

@router.post("/cleanup", response_model=Dict[str, Any])
async def cleanup_expired_locks(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    手动清理过期锁
    
    需要管理员权限
    """
    try:
        # 检查用户权限
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        
        # 执行清理
        cleanup_result = await lock_cleanup_service.cleanup_expired_locks()
        
        return {
            "success": True,
            "data": cleanup_result,
            "message": f"清理完成，共清理 {cleanup_result.get('total_cleaned', 0)} 个过期锁"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理过期锁失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理过期锁失败: {str(e)}"
        )

@router.post("/force-cleanup", response_model=Dict[str, Any])
async def force_cleanup_all_locks(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    强制清理所有锁（紧急情况使用）
    
    需要管理员权限
    """
    try:
        # 检查用户权限
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        
        # 执行强制清理
        cleanup_result = await lock_cleanup_service.force_cleanup_all(
            operator=current_user.email or "unknown"
        )
        
        return {
            "success": True,
            "data": cleanup_result,
            "message": f"强制清理完成，共释放 {cleanup_result.get('total_released', 0)} 个锁"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制清理锁失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"强制清理锁失败: {str(e)}"
        )

@router.delete("/ad/{lock_name}", response_model=Dict[str, Any])
async def force_release_ad_lock(
    lock_name: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    强制释放指定AD锁
    
    需要AD管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["ad:manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要AD管理权限"
            )
        
        # 强制释放锁
        success = await ad_sync_lock.release_lock(db, lock_name, "system")
        
        if success:
            logger.info(f"用户 {current_user.email or 'unknown'} 强制释放了AD锁 {lock_name}")
            return {
                "success": True,
                "message": f"已强制释放AD锁 '{lock_name}'"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"释放AD锁 '{lock_name}' 失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制释放AD锁失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"强制释放AD锁失败: {str(e)}"
        )

@router.delete("/email/{lock_name}", response_model=Dict[str, Any])
async def force_release_email_lock(
    lock_name: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_active_user)
):
    """
    强制释放指定邮箱锁
    
    需要邮箱管理权限
    """
    try:
        # 检查用户权限
        if not has_permission(db, current_user, ["email:manage"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要邮箱管理权限"
            )
        
        # 强制释放锁
        success = await email_sync_lock.release_lock(db, lock_name, "system")
        
        if success:
            logger.info(f"用户 {current_user.email or 'unknown'} 强制释放了邮箱锁 {lock_name}")
            return {
                "success": True,
                "message": f"已强制释放邮箱锁 '{lock_name}'"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"释放邮箱锁 '{lock_name}' 失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"强制释放邮箱锁失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"强制释放邮箱锁失败: {str(e)}"
        ) 