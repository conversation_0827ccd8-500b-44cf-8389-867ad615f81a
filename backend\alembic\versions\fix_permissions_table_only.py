"""fix_permissions_table_only

Revision ID: fix_permissions_table_only
Revises: add_email_sync_lock_table
Create Date: 2025-06-18 09:20:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'fix_permissions_table_only'
down_revision: Union[str, None] = 'add_email_sync_lock_table'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加code和module字段（先设为可空）
    op.add_column('permissions', sa.Column('code', sa.String(length=100), nullable=True))
    op.add_column('permissions', sa.Column('module', sa.String(length=50), nullable=True, comment='所属模块'))
    
    # 为现有权限记录生成code和module值
    from sqlalchemy import text
    connection = op.get_bind()
    result = connection.execute(text("SELECT id, name FROM permissions"))
    permissions = result.fetchall()
    
    for perm in permissions:
        # 根据name生成code（转小写，替换空格和特殊字符为下划线）
        code = perm.name.lower().replace(' ', '_').replace('-', '_').replace('(', '').replace(')', '')
        code = ''.join(c if c.isalnum() or c == '_' else '_' for c in code)
        # 去掉连续的下划线
        while '__' in code:
            code = code.replace('__', '_')
        code = code.strip('_')
        # 确保code不超过100字符且不为空
        code = code[:100] if code else f"permission_{perm.id}"
        
        # 设置默认module为"system"
        module = "system"
        
        # 更新记录
        connection.execute(text(
            "UPDATE permissions SET code = :code, module = :module WHERE id = :id"
        ), {"code": code, "module": module, "id": perm.id})
    
    # 现在设置字段为不可空
    op.alter_column('permissions', 'code', nullable=False)
    op.alter_column('permissions', 'module', nullable=False)
    
    # 修改description字段类型
    op.alter_column('permissions', 'description',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.Text(),
               existing_nullable=True)
    
    # 删除旧的唯一约束
    op.drop_constraint('permissions_name_key', 'permissions', type_='unique')
    
    # 为code字段创建唯一索引
    op.create_index('ix_permissions_code', 'permissions', ['code'], unique=True)
    op.create_index('ix_permissions_id', 'permissions', ['id'], unique=False)
    
    # 删除不需要的字段
    op.drop_column('permissions', 'action')
    op.drop_column('permissions', 'resource')


def downgrade() -> None:
    # 添加回action和resource字段
    op.add_column('permissions', sa.Column('action', sa.VARCHAR(length=100), nullable=True))
    op.add_column('permissions', sa.Column('resource', sa.VARCHAR(length=100), nullable=True))
    
    # 删除索引
    op.drop_index('ix_permissions_code', table_name='permissions')
    op.drop_index('ix_permissions_id', table_name='permissions')
    
    # 恢复name字段的唯一约束
    op.create_unique_constraint('permissions_name_key', 'permissions', ['name'])
    
    # 恢复description字段类型
    op.alter_column('permissions', 'description',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=200),
               existing_nullable=True)
    
    # 删除code和module字段
    op.drop_column('permissions', 'module')
    op.drop_column('permissions', 'code') 