import { computed, onMounted, watch } from 'vue'
import { useLocalStorage } from '@vueuse/core'

// 主题配置接口
export interface ThemeConfig {
  primaryColor: string
  borderRadius: 'none' | 'small' | 'medium' | 'large'
  compactMode: boolean
}

/**
 * 简化的主题管理系统
 * 只支持浅色主题，移除暗黑主题功能
 */
export const useTheme = () => {
  // 主题配置（持久化存储）
  const themeConfig = useLocalStorage<ThemeConfig>('ops-theme-config', {
    primaryColor: '#409eff',
    borderRadius: 'medium',
    compactMode: false
  })
  
  // 固定为浅色主题
  const isDark = computed(() => false)
  const currentTheme = computed(() => 'light' as const)
  
  // Element Plus主题变量
  const elementThemeVars = computed(() => ({
    '--el-color-primary': themeConfig.value.primaryColor,
    '--el-border-radius-base': getBorderRadius(themeConfig.value.borderRadius),
  }))
  
  // Vant主题变量
  const vantThemeVars = computed(() => ({
    '--van-primary-color': themeConfig.value.primaryColor,
    '--van-radius-md': getBorderRadius(themeConfig.value.borderRadius),
  }))
  
  // 获取边框圆角值
  function getBorderRadius(radius: string): string {
    const radiusMap = {
      none: '0px',
      small: '2px',
      medium: '4px',
      large: '8px'
    }
    return radiusMap[radius as keyof typeof radiusMap] || '4px'
  }
  
  // 设置主色调
  const setPrimaryColor = (color: string) => {
    themeConfig.value.primaryColor = color
  }
  
  // 设置边框圆角
  const setBorderRadius = (radius: ThemeConfig['borderRadius']) => {
    themeConfig.value.borderRadius = radius
  }
  
  // 切换紧凑模式
  const toggleCompactMode = () => {
    themeConfig.value.compactMode = !themeConfig.value.compactMode
  }
  
  // 应用主题到DOM
  const applyTheme = () => {
    const html = document.documentElement
    
    // 移除所有主题类
    html.classList.remove('light', 'dark', 'el-theme-dark', 'van-theme-dark')
    
    // 只应用浅色主题类
    html.classList.add('light')
    
    // 应用Element Plus变量
    Object.entries(elementThemeVars.value).forEach(([key, value]) => {
      html.style.setProperty(key, value)
    })
    
    // 应用Vant变量
    Object.entries(vantThemeVars.value).forEach(([key, value]) => {
      html.style.setProperty(key, value)
    })
    
    // 应用紧凑模式
    if (themeConfig.value.compactMode) {
      html.classList.add('compact-mode')
    } else {
      html.classList.remove('compact-mode')
    }
  }
  
  // 监听主题配置变化并应用
  watch(themeConfig, applyTheme, { deep: true, immediate: true })
  
  // 组件挂载时初始化主题
  onMounted(() => {
    applyTheme()
  })
  
  return {
    // 状态
    isDark,
    currentTheme,
    themeConfig,
    
    // 计算属性
    elementThemeVars,
    vantThemeVars,
    
    // 方法
    setPrimaryColor,
    setBorderRadius,
    toggleCompactMode,
    applyTheme
  }
}

export default useTheme 