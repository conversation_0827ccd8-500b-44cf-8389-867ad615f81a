"""
测试结果分析工具
分析第三阶段测试结果，生成详细的分析报告和改进建议
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

@dataclass
class TestAnalysisResult:
    """测试分析结果"""
    test_category: str
    status: str
    execution_time: float
    issues: List[str]
    recommendations: List[str]
    score: float  # 0-100分

@dataclass
class PerformanceAnalysis:
    """性能分析结果"""
    metric_name: str
    current_value: float
    threshold_value: float
    status: str  # "good", "warning", "critical"
    improvement_potential: float  # 改进潜力百分比

class TestResultAnalyzer:
    """测试结果分析器"""
    
    def __init__(self, report_file: str = "stage3_test_report.json"):
        self.report_file = report_file
        self.test_results = {}
        self.analysis_results = []
        self.performance_analysis = []
        
    def load_test_results(self) -> bool:
        """加载测试结果"""
        try:
            if not os.path.exists(self.report_file):
                logger.error(f"测试报告文件不存在: {self.report_file}")
                return False
            
            with open(self.report_file, 'r', encoding='utf-8') as f:
                self.test_results = json.load(f)
            
            logger.info(f"成功加载测试结果: {self.report_file}")
            return True
        
        except Exception as e:
            logger.error(f"加载测试结果失败: {e}")
            return False
    
    def analyze_functionality_tests(self) -> TestAnalysisResult:
        """分析功能完整性测试"""
        functionality_result = self.test_results.get('detailed_results', {}).get('functionality', {})
        
        issues = []
        recommendations = []
        score = 0
        
        if functionality_result.get('status') == 'passed':
            score = 100
            recommendations.append("功能测试全部通过，继续保持代码质量")
        elif functionality_result.get('status') == 'failed':
            score = 30
            issues.append("功能测试失败")
            recommendations.extend([
                "检查失败的测试用例",
                "修复功能缺陷",
                "增加单元测试覆盖率"
            ])
        elif functionality_result.get('status') == 'timeout':
            score = 20
            issues.append("功能测试超时")
            recommendations.extend([
                "优化测试执行效率",
                "检查是否存在死锁或无限循环",
                "增加测试超时时间"
            ])
        else:
            score = 0
            issues.append("功能测试状态未知")
            recommendations.append("检查测试执行环境")
        
        return TestAnalysisResult(
            test_category="功能完整性",
            status=functionality_result.get('status', 'unknown'),
            execution_time=functionality_result.get('execution_time', 0),
            issues=issues,
            recommendations=recommendations,
            score=score
        )
    
    def analyze_performance_tests(self) -> TestAnalysisResult:
        """分析性能测试"""
        performance_result = self.test_results.get('detailed_results', {}).get('performance', {})
        
        issues = []
        recommendations = []
        score = 0
        
        execution_time = performance_result.get('execution_time', 0)
        
        if performance_result.get('status') == 'passed':
            if execution_time < 60:  # 1分钟内完成
                score = 100
                recommendations.append("性能测试表现优秀")
            elif execution_time < 120:  # 2分钟内完成
                score = 80
                recommendations.append("性能测试表现良好，可考虑进一步优化")
            else:
                score = 60
                issues.append("性能测试执行时间较长")
                recommendations.extend([
                    "优化测试数据量",
                    "改进算法效率",
                    "考虑并行处理"
                ])
        else:
            score = 20
            issues.append("性能测试未通过")
            recommendations.extend([
                "检查性能瓶颈",
                "优化数据库查询",
                "改进缓存机制"
            ])
        
        return TestAnalysisResult(
            test_category="性能测试",
            status=performance_result.get('status', 'unknown'),
            execution_time=execution_time,
            issues=issues,
            recommendations=recommendations,
            score=score
        )
    
    def analyze_optimization_results(self) -> TestAnalysisResult:
        """分析优化结果"""
        optimization_result = self.test_results.get('detailed_results', {}).get('optimization', {})
        
        issues = []
        recommendations = []
        score = 0
        
        if optimization_result.get('status') == 'passed':
            benchmark_results = optimization_result.get('details', {}).get('benchmark_results', {})
            
            if benchmark_results:
                # 分析性能改进
                original_throughput = benchmark_results.get('original', {}).get('throughput', 0)
                optimized_throughput = benchmark_results.get('optimized_parallel', {}).get('throughput', 0)
                
                if optimized_throughput > original_throughput * 1.5:  # 50%以上改进
                    score = 100
                    recommendations.append("性能优化效果显著")
                elif optimized_throughput > original_throughput * 1.2:  # 20%以上改进
                    score = 80
                    recommendations.append("性能优化效果良好")
                elif optimized_throughput > original_throughput:
                    score = 60
                    recommendations.append("性能有所改进，可进一步优化")
                else:
                    score = 30
                    issues.append("优化效果不明显")
                    recommendations.extend([
                        "重新评估优化策略",
                        "寻找其他性能瓶颈",
                        "考虑硬件升级"
                    ])
            else:
                score = 50
                issues.append("缺少基准测试数据")
                recommendations.append("完善性能基准测试")
        else:
            score = 20
            issues.append("优化验证失败")
            recommendations.append("检查优化实现")
        
        return TestAnalysisResult(
            test_category="性能优化",
            status=optimization_result.get('status', 'unknown'),
            execution_time=optimization_result.get('execution_time', 0),
            issues=issues,
            recommendations=recommendations,
            score=score
        )
    
    def analyze_monitoring_results(self) -> TestAnalysisResult:
        """分析监控结果"""
        monitoring_result = self.test_results.get('detailed_results', {}).get('monitoring', {})
        
        issues = []
        recommendations = []
        score = 0
        
        if monitoring_result.get('status') == 'passed':
            details = monitoring_result.get('details', {})
            health_status = details.get('health_status', {})
            
            overall_status = health_status.get('overall_status', 'unknown')
            
            if overall_status == 'healthy':
                score = 100
                recommendations.append("监控系统运行正常")
            elif overall_status == 'warning':
                score = 70
                issues.extend(health_status.get('issues', []))
                recommendations.extend([
                    "关注监控告警",
                    "优化系统性能",
                    "定期检查系统状态"
                ])
            else:
                score = 30
                issues.extend(health_status.get('issues', []))
                recommendations.extend([
                    "立即处理系统问题",
                    "加强监控覆盖",
                    "建立告警机制"
                ])
        else:
            score = 20
            issues.append("监控系统验证失败")
            recommendations.append("检查监控系统配置")
        
        return TestAnalysisResult(
            test_category="监控机制",
            status=monitoring_result.get('status', 'unknown'),
            execution_time=monitoring_result.get('execution_time', 0),
            issues=issues,
            recommendations=recommendations,
            score=score
        )
    
    def generate_overall_analysis(self) -> Dict[str, Any]:
        """生成整体分析"""
        if not self.analysis_results:
            return {}
        
        total_score = sum(result.score for result in self.analysis_results)
        avg_score = total_score / len(self.analysis_results)
        
        all_issues = []
        all_recommendations = []
        
        for result in self.analysis_results:
            all_issues.extend(result.issues)
            all_recommendations.extend(result.recommendations)
        
        # 去重
        unique_issues = list(set(all_issues))
        unique_recommendations = list(set(all_recommendations))
        
        # 确定整体状态
        if avg_score >= 90:
            overall_status = "优秀"
            status_description = "系统各方面表现优秀，建议继续保持"
        elif avg_score >= 80:
            overall_status = "良好"
            status_description = "系统整体表现良好，有少量改进空间"
        elif avg_score >= 60:
            overall_status = "一般"
            status_description = "系统基本可用，需要重点改进"
        else:
            overall_status = "需要改进"
            status_description = "系统存在较多问题，需要立即改进"
        
        return {
            "overall_score": avg_score,
            "overall_status": overall_status,
            "status_description": status_description,
            "total_issues": len(unique_issues),
            "total_recommendations": len(unique_recommendations),
            "issues": unique_issues,
            "recommendations": unique_recommendations,
            "category_scores": {
                result.test_category: result.score 
                for result in self.analysis_results
            }
        }
    
    def generate_analysis_report(self) -> Dict[str, Any]:
        """生成完整的分析报告"""
        if not self.load_test_results():
            return {"error": "无法加载测试结果"}
        
        # 分析各个测试类别
        self.analysis_results = [
            self.analyze_functionality_tests(),
            self.analyze_performance_tests(),
            self.analyze_optimization_results(),
            self.analyze_monitoring_results()
        ]
        
        # 生成整体分析
        overall_analysis = self.generate_overall_analysis()
        
        # 生成完整报告
        report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "test_summary": self.test_results.get('test_summary', {}),
            "overall_analysis": overall_analysis,
            "detailed_analysis": [
                {
                    "category": result.test_category,
                    "status": result.status,
                    "score": result.score,
                    "execution_time": result.execution_time,
                    "issues": result.issues,
                    "recommendations": result.recommendations
                }
                for result in self.analysis_results
            ]
        }
        
        return report
    
    def save_analysis_report(self, output_file: str = "stage3_analysis_report.json"):
        """保存分析报告"""
        report = self.generate_analysis_report()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"分析报告已保存到: {output_file}")
            return True
        
        except Exception as e:
            logger.error(f"保存分析报告失败: {e}")
            return False
    
    def print_summary(self):
        """打印分析摘要"""
        report = self.generate_analysis_report()
        
        if "error" in report:
            print(f"错误: {report['error']}")
            return
        
        overall = report["overall_analysis"]
        
        print("\n" + "=" * 60)
        print("第三阶段测试结果分析摘要")
        print("=" * 60)
        print(f"整体评分: {overall['overall_score']:.1f}/100")
        print(f"整体状态: {overall['overall_status']}")
        print(f"状态描述: {overall['status_description']}")
        print(f"发现问题: {overall['total_issues']} 个")
        print(f"改进建议: {overall['total_recommendations']} 条")
        
        print("\n各类别评分:")
        for category, score in overall["category_scores"].items():
            print(f"  {category}: {score:.1f}/100")
        
        if overall["issues"]:
            print("\n主要问题:")
            for issue in overall["issues"][:5]:  # 显示前5个问题
                print(f"  • {issue}")
        
        if overall["recommendations"]:
            print("\n主要建议:")
            for rec in overall["recommendations"][:5]:  # 显示前5个建议
                print(f"  • {rec}")
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    analyzer = TestResultAnalyzer()
    
    # 生成并保存分析报告
    if analyzer.save_analysis_report():
        # 打印摘要
        analyzer.print_summary()
    else:
        print("分析失败，请检查测试报告文件是否存在")
