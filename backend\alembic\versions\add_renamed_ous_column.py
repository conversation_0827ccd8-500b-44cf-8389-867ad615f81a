"""add renamed_ous column

Revision ID: 7eb1a3b9d4f2
Revises: e37c828bbd78
Create Date: 2023-03-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7eb1a3b9d4f2'
down_revision = 'e37c828bbd78'  # 根据你的实际情况更新上一个迁移版本
branch_labels = None
depends_on = None


def upgrade():
    # 添加renamed_ous列到ad_sync_logs表
    op.add_column('ad_sync_logs', sa.Column('renamed_ous', sa.Integer(), nullable=True, server_default='0', comment='重命名的组织单位数'))


def downgrade():
    # 删除renamed_ous列
    op.drop_column('ad_sync_logs', 'renamed_ous') 