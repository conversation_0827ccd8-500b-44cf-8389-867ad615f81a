# OPS平台Jenkins配置文件
# 用于配置CI/CD流程的环境变量和参数

# Harbor镜像仓库配置
HARBOR_REGISTRY = 'harbor.zhixin.asia'
HARBOR_PROJECT = 'ops-platform'

# 镜像标签策略
IMAGE_TAG_STRATEGY = 'build-number-git-commit'

# 构建资源配置
BUILD_RESOURCES = [
    FRONTEND: [
        memory: '1Gi',
        cpu: '500m'
    ],
    BACKEND: [
        memory: '1Gi', 
        cpu: '500m'
    ],
    DOCKER: [
        memory: '2Gi',
        cpu: '1000m'
    ]
]

# 测试配置
TEST_CONFIG = [
    PYTEST_ARGS: '--maxfail=1 --disable-warnings --cov=app --cov-report=term-missing',
    COVERAGE_THRESHOLD: 80,
    TIMEOUT_MINUTES: 30
]

# 部署配置
DEPLOY_CONFIG = [
    TEST_ENVIRONMENT: 'dev',
    PRODUCTION_ENVIRONMENT: 'prod',
    ROLLBACK_ENABLED: true,
    HEALTH_CHECK_TIMEOUT: 300
]

# 通知配置
NOTIFICATION_CONFIG = [
    EMAIL_ENABLED: true,
    DINGTALK_ENABLED: false,
    WECHAT_ENABLED: false,
    SLACK_ENABLED: false
]

# 安全配置
SECURITY_CONFIG = [
    CREDENTIALS_ID: 'dev-harbor',
    GIT_CREDENTIALS_ID: 'gitea',
    KUBERNETES_NAMESPACE: 'jenkins'
]

# 清理策略
CLEANUP_CONFIG = [
    WORKSPACE_CLEANUP: true,
    DOCKER_IMAGE_CLEANUP: true,
    LOG_RETENTION_DAYS: 30,
    BUILD_RETENTION_COUNT: 50
]
