/* 
 * Element Plus 分页器修复样式
 * 这个文件用于修复分页器中每页条数选择器不显示的问题
 */

/* 分页容器通用样式 */
.pagination-container, 
.pagination-wrapper, 
.pagination {
  min-width: 650px !important;
  display: flex !important;
  justify-content: flex-end !important;
  overflow: visible !important;
  padding: 10px 0 !important;
}

/* 分页器组件样式修复 */
.el-pagination {
  white-space: normal !important;
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 5px !important;
  overflow: visible !important;
}

/* 总条数样式 */
.el-pagination__total {
  flex-shrink: 0 !important;
  display: inline-block !important;
  margin-right: 10px !important;
}

/* 每页条数选择器样式 */
.el-pagination__sizes {
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-right: 10px !important;
  min-width: 110px !important;
}

/* 调整选择器样式 */
.el-pagination__sizes .el-select {
  width: 110px !important;
  margin: 0 !important;
}

/* 调整输入框样式 */
.el-pagination__sizes .el-select .el-input {
  width: 110px !important;
  margin: 0 !important;
}

/* 确保输入框内容可见 */
.el-pagination__sizes .el-select .el-input__wrapper {
  display: inline-flex !important;
  width: 100% !important;
  box-shadow: 0 0 0 1px var(--el-border-color) inset !important;
}

/* 确保下拉图标可见 */
.el-pagination__sizes .el-select .el-input__suffix {
  display: inline-flex !important;
  align-items: center !important;
  pointer-events: auto !important;
}

/* 跳转区域样式 */
.el-pagination__jump {
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-left: 10px !important;
}

/* 页码输入框样式 */
.el-pagination__editor.el-input {
  width: 50px !important;
  margin: 0 5px !important;
}

/* 确保页码按钮可见 */
.el-pagination button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
} 