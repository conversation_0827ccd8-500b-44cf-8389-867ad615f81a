<template>
  <div class="software-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Monitor /></el-icon>
        <h2 class="page-title">软件管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>终端管理</el-breadcrumb-item>
        <el-breadcrumb-item>软件管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <el-input
            v-model="searchName"
            placeholder="按软件名称搜索"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            :prefix-icon="Search"
            class="search-input"
          />
        </div>
        <div class="action-buttons">
          <Authority permission="terminal:software:manage">
            <el-button type="primary" @click="handleExport" v-if="softwareList.length > 0">
              <el-icon><Download /></el-icon> 导出列表
            </el-button>
          </Authority>
          <Authority permission="terminal:software:manage">
            <el-dropdown @command="handleBatchCommand" v-if="selection.length > 0">
              <el-button type="primary">
                批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="compliant">
                    <el-icon><Check /></el-icon> 标记为合规
                  </el-dropdown-item>
                  <el-dropdown-item command="noncompliant">
                    <el-icon><Close /></el-icon> 标记为不合规
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </Authority>
        </div>
      </div>

      <el-table
        :data="softwareList"
        border
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-class-name="software-table-row"
        header-row-class-name="software-table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="软件名称" min-width="280">
          <template #header>
            <div class="column-header">软件名称</div>
          </template>
          <template #default="scope">
            <div class="software-name">
              <el-icon><Document /></el-icon>
              <router-link :to="`/terminal/software/${encodeURIComponent(scope.row.name)}`" class="software-link">
                {{ scope.row.name }}
              </router-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" min-width="120">
          <template #header>
            <div class="column-header">版本号</div>
          </template>
          <template #default="scope">
            <el-tag size="small" effect="plain">{{ scope.row.version || '-' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="terminal_count" label="安装终端数量" min-width="100">
          <template #header>
            <div class="column-header">安装终端数量</div>
          </template>
          <template #default="scope">
            <el-tag size="small" type="info" effect="plain" class="count-tag">
              {{ scope.row.terminal_count }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="合规状态" min-width="100">
          <template #header>
            <div class="column-header">
              <el-dropdown @command="handleComplianceFilter" trigger="click">
                <span class="el-dropdown-link">
                  合规状态
                  <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="all" :class="{ 'active-filter': searchCompliance === null }">全部</el-dropdown-item>
                    <el-dropdown-item command="true" :class="{ 'active-filter': searchCompliance === true }">合规</el-dropdown-item>
                    <el-dropdown-item command="false" :class="{ 'active-filter': searchCompliance === false }">不合规</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <template #default="scope">
            <el-tag :type="scope.row.is_compliant ? 'success' : 'danger'" effect="dark">
              {{ scope.row.is_compliant ? '合规' : '不合规' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usage_notes" label="用途备注" min-width="200">
          <template #header>
            <div class="column-header">用途备注</div>
          </template>
          <template #default="scope">
            <div class="usage-notes-cell">
              <span v-if="scope.row.usage_notes">{{ scope.row.usage_notes }}</span>
              <span v-else class="no-notes">未设置</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="scope">
            <el-dropdown @command="(command: string) => handleOperation(command, scope.row)">
              <el-button type="primary" size="small" plain>
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="view">
                    <el-icon><View /></el-icon> 查看详情
                  </el-dropdown-item>
                  <Authority permission="terminal:software:manage">
                    <el-dropdown-item command="toggle">
                      <el-icon>
                        <component :is="scope.row.is_compliant ? Close : Check" />
                      </el-icon>
                      {{ scope.row.is_compliant ? '标记为不合规' : '标记为合规' }}
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="terminal:software:manage">
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon> 编辑备注
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 编辑用途备注对话框 -->
    <el-dialog
      v-model="editNotesDialogVisible"
      title="编辑软件用途备注"
      width="500px"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="软件名称">
          <div class="form-value">{{ currentSoftware?.name }}</div>
        </el-form-item>
        <el-form-item label="版本">
          <div class="form-value">{{ currentSoftware?.version || '所有版本' }}</div>
        </el-form-item>
        <el-form-item label="用途备注">
          <el-input
            v-model="editingNotes"
            type="textarea"
            rows="4"
            placeholder="请输入软件用途备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editNotesDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveUsageNotes">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出选项对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出软件列表"
      width="500px"
      destroy-on-close
    >
      <el-form>
        <el-form-item label="包含主机信息">
          <el-checkbox v-model="exportIncludeTerminals" />
        </el-form-item>
        <el-form-item label="导出格式">
          <el-select v-model="exportFormatType" @change="handleFormatChange">
            <el-option value="csv">CSV</el-option>
            <el-option value="xlsx">Excel</el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executeExport">导出</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download, ArrowDown, Document, Monitor, View, Edit, Check, Close } from '@element-plus/icons-vue'
import { terminalApi } from '@/api/terminal'
import type { SoftwareSummary } from '@/types/terminal'
import Authority from '@/components/Authority/index.vue'

const router = useRouter()
const loading = ref(false)
const softwareList = ref<SoftwareSummary[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const searchName = ref('')
const searchCompliance = ref<boolean | null>(null)
const selection = ref<SoftwareSummary[]>([])

// 用于编辑用途备注的状态
const editNotesDialogVisible = ref(false)
const currentSoftware = ref<SoftwareSummary | null>(null)
const editingNotes = ref('')

// 修改导出功能相关状态
const exportDialogVisible = ref(false)
const exportIncludeTerminals = ref(false)
const exportFormatType = ref<'csv' | 'xlsx'>('xlsx')

// 加载软件列表
const loadSoftwareList = async () => {
  loading.value = true
  try {
    const skip = (currentPage.value - 1) * pageSize.value
    const params: any = {
      skip,
      limit: pageSize.value
    }

    if (searchName.value) {
      params.name = searchName.value
    }

    if (searchCompliance.value !== null) {
      params.is_compliant = searchCompliance.value
    }

    const response = await terminalApi.getSoftwareList(params)
    softwareList.value = response.data.items
    total.value = response.data.total
  } catch (error) {
    console.error('加载软件列表失败:', error)
    ElMessage.error('加载软件列表失败')
  } finally {
    loading.value = false
  }
}

// 处理合规性筛选
const handleComplianceFilter = (command: string) => {
  if (command === 'all') {
    searchCompliance.value = null
  } else if (command === 'true') {
    searchCompliance.value = true
  } else if (command === 'false') {
    searchCompliance.value = false
  }
  currentPage.value = 1
  loadSoftwareList()
}

// 处理单行操作
const handleOperation = (command: string, software: SoftwareSummary) => {
  if (command === 'view') {
    handleView(software)
  } else if (command === 'toggle') {
    handleToggleCompliance(software)
  } else if (command === 'edit') {
    handleEditUsageNotes(software)
  }
}

// 处理批量操作
const handleBatchCommand = (command: string) => {
  if (command === 'compliant') {
    handleBatchSetCompliance(true)
  } else if (command === 'noncompliant') {
    handleBatchSetCompliance(false)
  }
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadSoftwareList()
}

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadSoftwareList()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  loadSoftwareList()
}

// 查看软件详情
const handleView = (software: SoftwareSummary) => {
  router.push(`/terminal/software/${encodeURIComponent(software.name)}`)
}

// 处理表格选择变化
const handleSelectionChange = (selectedItems: SoftwareSummary[]) => {
  selection.value = selectedItems
}

// 切换软件合规状态
const handleToggleCompliance = async (software: SoftwareSummary) => {
  try {
    loading.value = true
    await terminalApi.updateSoftwareInfo(
      software.name,
      {
        is_compliant: !software.is_compliant,
        usage_notes: software.usage_notes
      },
      { version: software.version }
    )

    ElMessage.success(`已将 ${software.name} ${software.is_compliant ? '标记为不合规' : '标记为合规'}`)
    // 更新本地状态
    software.is_compliant = !software.is_compliant
  } catch (error) {
    console.error('更新软件合规状态失败:', error)
    ElMessage.error('更新软件合规状态失败')
  } finally {
    loading.value = false
  }
}

// 批量设置合规状态
const handleBatchSetCompliance = async (isCompliant: boolean) => {
  try {
    const message = `确定要将选中的 ${selection.value.length} 个软件${isCompliant ? '标记为合规' : '标记为不合规'}吗？`
    await ElMessageBox.confirm(message, '操作确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true

    // 批量更新每个选中的软件
    const promises = selection.value.map(software =>
      terminalApi.updateSoftwareInfo(
        software.name,
        {
          is_compliant: isCompliant,
          usage_notes: software.usage_notes
        },
        { version: software.version }
      )
    )

    await Promise.all(promises)

    ElMessage.success(`已将 ${selection.value.length} 个软件${isCompliant ? '标记为合规' : '标记为不合规'}`)

    // 刷新列表
    loadSoftwareList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新软件合规状态失败:', error)
      ElMessage.error('批量更新软件合规状态失败')
    }
  } finally {
    loading.value = false
  }
}

// 编辑用途备注
const handleEditUsageNotes = (software: SoftwareSummary) => {
  currentSoftware.value = software
  editingNotes.value = software.usage_notes || ''
  editNotesDialogVisible.value = true
}

// 保存用途备注
const handleSaveUsageNotes = async () => {
  if (!currentSoftware.value) return

  try {
    loading.value = true
    await terminalApi.updateSoftwareInfo(
      currentSoftware.value.name,
      {
        is_compliant: !!currentSoftware.value.is_compliant,
        usage_notes: editingNotes.value
      },
      { version: currentSoftware.value.version }
    )

    // 更新本地状态
    if (currentSoftware.value) {
      currentSoftware.value.usage_notes = editingNotes.value
    }

    ElMessage.success('软件用途备注已更新')
    editNotesDialogVisible.value = false
  } catch (error) {
    console.error('更新软件用途备注失败:', error)
    ElMessage.error('更新软件用途备注失败')
  } finally {
    loading.value = false
  }
}

// 导出软件列表
const handleExport = async () => {
  const params: any = {}

  try {
    if (searchName.value) {
      params.name = searchName.value
    }

    if (searchCompliance.value !== null) {
      params.is_compliant = searchCompliance.value
    }

    // 打开导出选项对话框
    exportIncludeTerminals.value = false
    exportFormatType.value = 'xlsx'
    exportDialogVisible.value = true
  } catch (error) {
    console.error('导出软件列表失败:', error)
    ElMessage.error('导出软件列表失败: ' + (error instanceof Error ? error.message : '未知错误'))
  }
}

// 执行导出操作
const executeExport = async () => {
  loading.value = true
  const params: any = {}

  if (searchName.value) {
    params.name = searchName.value
  }

  if (searchCompliance.value !== null) {
    params.is_compliant = searchCompliance.value
  }

  // 设置导出参数
  params.include_terminals = exportIncludeTerminals.value
  params.format = exportFormatType.value

  try {
    const response = await terminalApi.exportSoftwareList(params)

    // 检查响应内容
    if (!response.data || response.data.size === 0) {
      throw new Error('导出数据为空')
    }

    // 确定MIME类型
    const mimeType = exportFormatType.value === 'csv'
      ? 'text/csv;charset=utf-8;'
      : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

    // 创建Blob和下载链接
    const blob = new Blob([response.data], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    const extension = exportFormatType.value === 'csv' ? 'csv' : 'xlsx'
    link.setAttribute('download', `软件管理列表_${new Date().toISOString().split('T')[0]}.${extension}`)
    document.body.appendChild(link)
    link.click()

    // 释放URL对象
    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }, 100)

    ElMessage.success(`软件列表已导出为${exportFormatType.value === 'csv' ? 'CSV' : 'Excel'}格式${exportIncludeTerminals.value ? '(包含主机信息)' : ''}`)
    exportDialogVisible.value = false
  } catch (error) {
    console.error(`导出${exportFormatType.value === 'csv' ? 'CSV' : 'Excel'}失败:`, error)
    ElMessage.error(`导出${exportFormatType.value === 'csv' ? 'CSV' : 'Excel'}失败: ` + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    loading.value = false
  }
}

// 处理格式变化
const handleFormatChange = () => {
  // 处理格式变化后的逻辑
}

onMounted(() => {
  loadSoftwareList()
})
</script>

<style scoped>
.software-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header {
  padding-bottom: 0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 10px;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 320px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.software-table-row {
  transition: all 0.3s;
  height: 56px;
}

.software-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.software-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.count-tag {
  min-width: 36px;
  text-align: center;
  justify-content: center;
  font-weight: normal;
  border-radius: 12px;
  background-color: #f2f6fc;
  border-color: #dcdfe6;
  color: #606266;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.software-name {
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.software-name:hover {
  transform: translateX(2px);
}

.software-link {
  color: #409EFF;
  text-decoration: none;
  margin-left: 8px;
  font-weight: 500;
  transition: color 0.2s;
}

.software-link:hover {
  text-decoration: none;
  color: #337ecc;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.usage-notes-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 2px 0;
}

.no-notes {
  color: #909399;
  font-style: italic;
}

.active-filter {
  font-weight: bold;
  color: #409EFF;
}

.form-value {
  font-weight: bold;
  padding: 4px 0;
  color: #303133;
}

.el-card.box-card:hover {
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08);
}
</style>