<template>
  <div v-if="appReady">
    <router-view />
    <!-- Stagewise工具栏 - 仅在开发模式下显示 -->
    <component v-if="isDevelopment && StagewiseToolbarWrapper" :is="StagewiseToolbarWrapper" />
  </div>
  <div v-else class="app-loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">应用初始化中...</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef } from 'vue'

const isDevelopment = ref(false)
const appReady = ref(false)
const StagewiseToolbarWrapper = shallowRef<any>(null) // 使用shallowRef避免响应式警告

onMounted(async () => {
  // 设置应用就绪状态
  appReady.value = true
  
  // 只在开发模式下加载
  if (import.meta.env.DEV) {
    isDevelopment.value = true
    try {
      const module = await import('./components/StagewiseToolbar.vue')
      StagewiseToolbarWrapper.value = module.default
    } catch (error) {
      console.warn('Failed to load stagewise toolbar wrapper:', error)
    }
  }
})
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#app {
  height: 100%;
  margin: 0;
  padding: 0;
  max-width: none;
}

.app-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e4e7ed;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  color: #606266;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
