from logging.handlers import RotatingFileHandler
import logging
import json
import os
from datetime import datetime

class CustomJSONFormatter(logging.Formatter):
    def format(self, record):
        log_record = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        if hasattr(record, 'request_id'):
            log_record['request_id'] = record.request_id
            
        if record.exc_info:
            log_record['exception'] = self.formatException(record.exc_info)
            
        if hasattr(record, 'duration'):
            log_record['duration'] = record.duration
            
        return json.dumps(log_record)

def setup_logger(name='app', log_dir='logs'):
    """配置应用日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(CustomJSONFormatter())
    logger.addHandler(console_handler)
    
    # 文件处理器(按大小轮转)
    file_handler = RotatingFileHandler(
        filename=f'{log_dir}/app.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(CustomJSONFormatter())
    logger.addHandler(file_handler)
    
    # 错误日志单独存储
    error_handler = RotatingFileHandler(
        filename=f'{log_dir}/error.log',
        maxBytes=10*1024*1024,
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(CustomJSONFormatter())
    logger.addHandler(error_handler)
    
    return logger 