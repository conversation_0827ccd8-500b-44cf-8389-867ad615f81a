# AD同步非在职人员账号问题修复

## 问题描述

AD管理在进行同步时，出现以下错误：
1. 未找到非在职人员账号
2. 报错：`'ADClient' object has no attribute 'disable_user'`
3. 密码过期信息计算错误：`int() argument must be a string, a bytes-like object or a real number, not 'datetime.datetime'`

## 问题分析

### 根本原因
1. **disable_user方法缺失**：AD同步代码调用了不存在的`disable_user`方法
2. **类型转换错误**：`_calculate_password_expiry`方法中，`pwdLastSet`值可能是datetime类型，但代码尝试直接转换为int
3. **非缓存问题**：代码确实使用了Redis缓存，但在处理非在职人员时使用了`no_cache=True`参数，所以不是缓存问题

### 现有功能验证
从用户日志中可以看到：`用户状态已切换: test222, 当前状态: 禁用`，证明`toggle_user_status`方法是正常工作的。

## 修复方案

采用方案1：添加disable_user方法，保持API一致性和向后兼容。

## 修复实施

### 1. 添加便捷方法
在`backend/app/utils/ad_client.py`中的`ADClient`类中添加了两个便捷方法：

```python
async def disable_user(self, username: str) -> bool:
    """禁用AD用户账号"""
    try:
        self._logger.info(f"禁用用户账号: {username}")
        return await self.toggle_user_status(username, force_enable=False)
    except Exception as e:
        self._logger.error(f"禁用用户账号时出错: {str(e)}")
        return False

async def enable_user(self, username: str) -> bool:
    """启用AD用户账号"""
    try:
        self._logger.info(f"启用用户账号: {username}")
        return await self.toggle_user_status(username, force_enable=True)
    except Exception as e:
        self._logger.error(f"启用用户账号时出错: {str(e)}")
        return False
```

### 2. 修复密码过期计算错误
在`_calculate_password_expiry`方法中添加了安全的类型转换和时区处理：

```python
try:
    # 安全地转换 pwdLastSet 值
    pwd_last_set_value = entry.pwdLastSet.value
    if isinstance(pwd_last_set_value, datetime):
        # 如果是datetime对象，转换为AD时间戳
        from datetime import timezone
        epoch = datetime(1601, 1, 1, tzinfo=timezone.utc)
        # 确保两个datetime都有时区信息
        if pwd_last_set_value.tzinfo is None:
            pwd_last_set_value = pwd_last_set_value.replace(tzinfo=timezone.utc)
        delta = pwd_last_set_value - epoch
        pwd_last_set = int(delta.total_seconds() * 10**7)
    else:
        pwd_last_set = int(pwd_last_set_value)
except (ValueError, TypeError) as e:
    self._logger.warning(f"无法处理pwdLastSet值: {entry.pwdLastSet.value}, 错误: {str(e)}")
    # 返回安全的默认值
```

### 3. 修复move_object方法缩进错误
将错误放置在全局作用域的`move_object`方法移回到`ADClient`类内部，修复了方法缺失的问题。

### 4. 修复时区相关错误
解决了`can't subtract offset-naive and offset-aware datetimes`错误，确保datetime对象的时区一致性。

## 技术架构

### 方法层次结构
- `toggle_user_status` - 核心方法（保留），提供灵活的用户状态切换
- `disable_user` - 便捷方法，语义化的禁用操作
- `enable_user` - 便捷方法，语义化的启用操作

### 优势
1. **保持向后兼容**：现有调用`disable_user`的代码无需修改
2. **语义清晰**：方法名直观表达操作意图
3. **代码复用**：内部调用现有的`toggle_user_status`方法
4. **错误处理**：增强了类型安全和错误处理

## 验证结果

修复后，AD同步非在职人员功能应该能够正常工作：
1. ✅ `disable_user`方法现在可用
2. ✅ `move_object`方法现在可用（安全组移动功能）
3. ✅ 密码过期计算不再出现类型转换错误
4. ✅ 时区相关的datetime错误已修复
5. ✅ 非在职人员账号可以被正确禁用
6. ✅ LDAP搜索scope参数错误已修复（从字符串'base'改为BASE常量）

## 最新修复 (第二轮)

### 问题：LDAP搜索scope参数错误
在`move_object`方法中，使用了字符串`'base'`作为搜索范围，导致`invalid scope type`错误。

### 解决方案：
将`search_scope='base'`修改为`search_scope=BASE`，使用正确的LDAP3库常量。

现在安全组移动功能应该能够正常工作，不再出现"invalid scope type"错误。

## 相关文件

- `backend/app/utils/ad_client.py` - ADClient类的核心实现
- `backend/app/services/ad.py` - AD同步服务逻辑（第2871行调用disable_user）

## 注意事项

1. 保持`toggle_user_status`方法不变，确保现有功能不受影响
2. 新增方法遵循现有的错误处理和日志记录模式
3. 类型转换处理兼容不同LDAP服务器返回的数据格式 