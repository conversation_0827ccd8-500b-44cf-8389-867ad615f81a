"""
数据一致性验证测试
验证同步过程中的数据一致性保障机制
"""

import pytest
import asyncio
import logging
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timedelta

from app.database import get_db
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember, PersonnelSyncLog
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.services.email_api import TencentEmailAPIService
from app.crud.email import email_member
from app.schemas.email_personnel_sync import PersonnelSyncStats

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class TestDataConsistency:
    """数据一致性验证测试类"""
    
    @pytest.fixture(scope="class")
    def db_session(self):
        """数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture(scope="class")
    def test_data(self, db_session: Session):
        """准备测试数据"""
        # 清理现有测试数据
        db_session.query(EcologyUser).filter(
            EcologyUser.job_number.like("TEST_%")
        ).delete(synchronize_session=False)
        
        db_session.query(EmailMember).filter(
            EmailMember.extid.like("TEST_%")
        ).delete(synchronize_session=False)
        
        # 创建一致性测试数据
        test_users = [
            # 完全匹配的用户
            EcologyUser(
                user_id=2001,
                user_name="测试用户1",
                job_number="TEST_001",
                dept_name="测试部门",
                job_title_name="测试工程师",
                mobile="13900139001",
                status="active"
            ),
            # 信息不一致的用户
            EcologyUser(
                user_id=2002,
                user_name="测试用户2",
                job_number="TEST_002",
                dept_name="测试部门",
                job_title_name="高级测试工程师",  # 职位不同
                mobile="13900139002",
                status="active"
            ),
            # 离职用户
            EcologyUser(
                user_id=2003,
                user_name="测试用户3",
                job_number="TEST_003",
                dept_name="测试部门",
                job_title_name="测试经理",
                mobile="13900139003",
                status="inactive"
            ),
            # 只在人员信息中存在的用户
            EcologyUser(
                user_id=2004,
                user_name="测试用户4",
                job_number="TEST_004",
                dept_name="测试部门",
                job_title_name="自动化测试工程师",
                mobile="13900139004",
                status="active"
            )
        ]
        
        test_email_members = [
            # 完全匹配的邮箱成员
            EmailMember(
                extid="TEST_001",
                email="<EMAIL>",
                name="测试用户1",
                department_id="test_dept",
                position="测试工程师",
                mobile="13900139001",
                is_active=True
            ),
            # 信息不一致的邮箱成员
            EmailMember(
                extid="TEST_002",
                email="<EMAIL>",
                name="测试用户2",
                department_id="test_dept",
                position="测试工程师",  # 职位不同
                mobile="13900139002",
                is_active=True
            ),
            # 应该被禁用的邮箱成员（对应离职用户）
            EmailMember(
                extid="TEST_003",
                email="<EMAIL>",
                name="测试用户3",
                department_id="test_dept",
                position="测试经理",
                mobile="13900139003",
                is_active=True  # 应该被禁用
            ),
            # 只在邮箱中存在的成员
            EmailMember(
                extid="TEST_005",
                email="<EMAIL>",
                name="测试用户5",
                department_id="test_dept",
                position="性能测试工程师",
                mobile="13900139005",
                is_active=True
            )
        ]
        
        # 添加到数据库
        for user in test_users:
            db_session.merge(user)
        for member in test_email_members:
            db_session.merge(member)
        
        db_session.commit()
        
        return {
            "users": test_users,
            "email_members": test_email_members
        }
    
    def test_data_consistency_check(self, db_session: Session, test_data: Dict):
        """测试数据一致性检查功能"""
        logger.info("=== 测试数据一致性检查功能 ===")
        
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 执行数据一致性检查
        consistency_result = asyncio.run(sync_service.check_data_consistency())
        
        # 验证检查结果结构
        assert hasattr(consistency_result, 'total_personnel')
        assert hasattr(consistency_result, 'total_email_members')
        assert hasattr(consistency_result, 'matched_count')
        assert hasattr(consistency_result, 'unmatched_personnel')
        assert hasattr(consistency_result, 'unmatched_email_members')
        assert hasattr(consistency_result, 'inconsistent_data')
        
        # 验证数据逻辑一致性
        assert consistency_result.total_personnel >= 0
        assert consistency_result.total_email_members >= 0
        assert consistency_result.matched_count <= min(
            consistency_result.total_personnel, 
            consistency_result.total_email_members
        )
        
        logger.info(f"一致性检查结果:")
        logger.info(f"  人员总数: {consistency_result.total_personnel}")
        logger.info(f"  邮箱总数: {consistency_result.total_email_members}")
        logger.info(f"  匹配数量: {consistency_result.matched_count}")
        logger.info(f"  未匹配人员: {len(consistency_result.unmatched_personnel)}")
        logger.info(f"  未匹配邮箱: {len(consistency_result.unmatched_email_members)}")
        logger.info(f"  不一致数据: {len(consistency_result.inconsistent_data)}")
        
        # 验证测试数据的一致性检查结果
        # 应该检测到TEST_004用户没有对应的邮箱
        unmatched_job_numbers = [item.job_number for item in consistency_result.unmatched_personnel]
        assert "TEST_004" in unmatched_job_numbers
        
        # 应该检测到TEST_005邮箱没有对应的人员
        unmatched_extids = [item.extid for item in consistency_result.unmatched_email_members]
        assert "TEST_005" in unmatched_extids
        
        # 应该检测到TEST_002的职位信息不一致
        inconsistent_extids = [item.extid for item in consistency_result.inconsistent_data]
        assert "TEST_002" in inconsistent_extids
        
        logger.info("✅ 数据一致性检查功能测试通过")
    
    def test_sync_data_integrity(self, db_session: Session, test_data: Dict):
        """测试同步过程的数据完整性"""
        logger.info("=== 测试同步过程的数据完整性 ===")
        
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 记录同步前的状态
        before_sync = {
            "personnel_count": db_session.query(EcologyUser).filter(
                EcologyUser.job_number.like("TEST_%")
            ).count(),
            "email_count": db_session.query(EmailMember).filter(
                EmailMember.extid.like("TEST_%")
            ).count(),
            "active_email_count": db_session.query(EmailMember).filter(
                and_(EmailMember.extid.like("TEST_%"), EmailMember.is_active == True)
            ).count()
        }
        
        logger.info(f"同步前状态: 人员={before_sync['personnel_count']}, "
                   f"邮箱={before_sync['email_count']}, "
                   f"活跃邮箱={before_sync['active_email_count']}")
        
        # 执行试运行同步
        dry_run_result = asyncio.run(sync_service.sync_personnel_to_email(dry_run=True))
        
        # 验证试运行不会修改数据
        after_dry_run = {
            "personnel_count": db_session.query(EcologyUser).filter(
                EcologyUser.job_number.like("TEST_%")
            ).count(),
            "email_count": db_session.query(EmailMember).filter(
                EmailMember.extid.like("TEST_%")
            ).count(),
            "active_email_count": db_session.query(EmailMember).filter(
                and_(EmailMember.extid.like("TEST_%"), EmailMember.is_active == True)
            ).count()
        }
        
        # 试运行后数据应该保持不变
        assert before_sync == after_dry_run, "试运行不应该修改任何数据"
        
        logger.info(f"试运行结果: 处理={dry_run_result.processed_count}, "
                   f"创建申请={dry_run_result.created_requests}, "
                   f"更新={dry_run_result.updated_count}, "
                   f"禁用={dry_run_result.disabled_count}")
        
        # 验证试运行结果的逻辑性
        assert dry_run_result.processed_count >= 0
        assert dry_run_result.created_requests >= 0
        assert dry_run_result.updated_count >= 0
        assert dry_run_result.disabled_count >= 0
        
        # 根据测试数据，应该有1个用户需要禁用（TEST_003）
        assert dry_run_result.disabled_count >= 1
        
        logger.info("✅ 同步过程数据完整性测试通过")
    
    def test_rollback_mechanism(self, db_session: Session, test_data: Dict):
        """测试回滚机制"""
        logger.info("=== 测试回滚机制 ===")
        
        # 记录原始状态
        original_member = db_session.query(EmailMember).filter(
            EmailMember.extid == "TEST_001"
        ).first()
        
        if original_member:
            original_position = original_member.position
            original_mobile = original_member.mobile
            
            # 模拟更新操作
            original_member.position = "临时修改的职位"
            original_member.mobile = "临时修改的手机号"
            db_session.commit()
            
            # 验证修改生效
            updated_member = db_session.query(EmailMember).filter(
                EmailMember.extid == "TEST_001"
            ).first()
            assert updated_member.position == "临时修改的职位"
            assert updated_member.mobile == "临时修改的手机号"
            
            # 模拟回滚操作
            updated_member.position = original_position
            updated_member.mobile = original_mobile
            db_session.commit()
            
            # 验证回滚成功
            rolled_back_member = db_session.query(EmailMember).filter(
                EmailMember.extid == "TEST_001"
            ).first()
            assert rolled_back_member.position == original_position
            assert rolled_back_member.mobile == original_mobile
            
            logger.info("✅ 回滚机制测试通过")
        else:
            logger.warning("未找到测试用户TEST_001，跳过回滚测试")
    
    def test_concurrent_access_consistency(self, db_session: Session, test_data: Dict):
        """测试并发访问时的数据一致性"""
        logger.info("=== 测试并发访问时的数据一致性 ===")
        
        # 模拟并发读取操作
        sync_service1 = PersonnelEmailSyncService(db_session)
        sync_service2 = PersonnelEmailSyncService(db_session)
        
        # 同时执行数据一致性检查
        result1 = asyncio.run(sync_service1.check_data_consistency())
        result2 = asyncio.run(sync_service2.check_data_consistency())
        
        # 验证并发读取结果一致
        assert result1.total_personnel == result2.total_personnel
        assert result1.total_email_members == result2.total_email_members
        assert result1.matched_count == result2.matched_count
        
        logger.info("✅ 并发访问数据一致性测试通过")
    
    def teardown_method(self, method):
        """清理测试数据"""
        db = next(get_db())
        try:
            # 清理测试数据
            db.query(EcologyUser).filter(
                EcologyUser.job_number.like("TEST_%")
            ).delete(synchronize_session=False)
            
            db.query(EmailMember).filter(
                EmailMember.extid.like("TEST_%")
            ).delete(synchronize_session=False)
            
            db.commit()
            logger.info("测试数据清理完成")
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")
            db.rollback()
        finally:
            db.close()

if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v"])
