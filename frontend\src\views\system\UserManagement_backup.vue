<template>
  <div class="user-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleCreateUser">创建用户</el-button>
        </div>
      </template>
      
      <el-table :data="userList" border style="width: 100%">
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="email" label="邮箱" width="220" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="角色" min-width="240">
          <template #default="scope">
            <div class="role-tags">
              <el-tag 
                v-for="role in scope.row.roles" 
                :key="role.id" 
                size="small" 
                class="role-tag"
                type="info"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="!scope.row.roles || scope.row.roles.length === 0" class="no-roles">
                无角色
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleAssignRoles(scope.row)">
              分配角色
            </el-button>
            <el-button type="warning" size="small" @click="handleEditUser(scope.row)">
              编辑
            </el-button>
            <el-button 
              :type="scope.row.is_active ? 'danger' : 'success'" 
              size="small" 
              @click="handleToggleUserStatus(scope.row)"
              :disabled="scope.row.id === currentUserId"
            >
              {{ scope.row.is_active ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 角色分配对话框 -->
    <el-dialog v-model="roleDialogVisible" title="角色分配" width="500px">
      <div v-if="currentUser">
        <h3>为用户 {{ currentUser.username }} 分配角色</h3>
        
        <el-checkbox-group v-model="selectedRoles">
          <el-row :gutter="20">
            <el-col :span="12" v-for="role in roleList" :key="role.id">
              <el-checkbox :label="role.id">{{ role.name }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleAssignment">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户创建/编辑对话框 -->
    <el-dialog 
      v-model="userFormDialogVisible" 
      :title="isEditMode ? '编辑用户' : '创建用户'" 
      width="500px"
    >
      <el-form 
        ref="userFormRef" 
        :model="userForm" 
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            placeholder="请输入密码" 
            type="password" 
            :required="!isEditMode"
            show-password 
          />
          <div v-if="isEditMode" class="form-tip">如不修改密码请留空</div>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch 
            v-model="userForm.is_active"
            :active-text="'启用'"
            :inactive-text="'禁用'"
          />
        </el-form-item>
        <el-form-item label="超级管理员">
          <el-switch 
            v-model="userForm.is_superuser"
            :active-text="'是'"
            :inactive-text="'否'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userFormDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUserForm">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, FormRules, FormInstance } from 'element-plus'
import { systemApi } from '@/api/system'
import { useUserStore } from '@/stores/user'

interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  is_superuser: boolean
  roles: Role[]
}

interface Role {
  id: number
  code: string
  name: string
  description: string
  is_default: number
}

const userList = ref<User[]>([])
const roleList = ref<Role[]>([])
const selectedRoles = ref<number[]>([])
const roleDialogVisible = ref(false)
const currentUser = ref<User | null>(null)
const currentUserId = ref<number>(0)

// 用户表单相关
const userFormDialogVisible = ref(false)
const isEditMode = ref(false)
const userFormRef = ref<FormInstance>()
const userForm = reactive({
  id: 0,
  username: '',
  email: '',
  password: '',
  is_active: true,
  is_superuser: false
})

// 表单验证规则
const userFormRules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: isEditMode.value ? false : true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' }
  ]
})

// 初始化当前登录用户ID
const userStore = useUserStore()
if (userStore.userInfo) {
  currentUserId.value = userStore.userInfo.id
}

// 加载用户列表
const loadUsers = async () => {
  try {
    console.log('[UserManagement] 开始加载用户列表')
    const response = await systemApi.getUsers()
    userList.value = response.data
  } catch (error) {
    console.error('[UserManagement] 加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    console.log('[UserManagement] 开始加载角色列表')
    const response = await systemApi.getRoles()
    roleList.value = response.data
  } catch (error) {
    console.error('[UserManagement] 加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  }
}

// 分配角色
const handleAssignRoles = async (row: User) => {
  try {
    // 先获取用户的详细信息，包括完整的角色信息
    const response = await systemApi.getUserById(row.id)
    const userDetail = response.data
    
    // 设置当前用户和选中的角色
    currentUser.value = userDetail
    selectedRoles.value = userDetail.roles?.map((role: any) => role.id) || []
    
    // 如果用户是超级管理员，查找超级管理员角色并自动勾选
    if (userDetail.is_superuser) {
      // 查找code为super_admin的角色
      const superAdminRole = roleList.value.find(role => role.code === 'super_admin')
      if (superAdminRole && !selectedRoles.value.includes(superAdminRole.id)) {
        selectedRoles.value.push(superAdminRole.id)
      }
    }
    
    roleDialogVisible.value = true
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
    ElMessage.error('获取用户详细信息失败')
  }
}

// 保存角色分配
const saveRoleAssignment = async () => {
  if (!currentUser.value) return
  
  try {
    await systemApi.assignRoles(currentUser.value.id, selectedRoles.value)
    
    // 检查是否选择了超级管理员角色
    const superAdminRole = roleList.value.find(role => role.code === 'super_admin')
    const hasSuperAdminRole = !!(superAdminRole && selectedRoles.value.includes(superAdminRole.id))
    
    // 如果用户is_superuser状态与超级管理员角色选择状态不一致，则更新用户状态
    if (currentUser.value.is_superuser !== hasSuperAdminRole) {
      await systemApi.updateUserSuperuser(currentUser.value.id, hasSuperAdminRole)
    }
    
    ElMessage.success('角色分配已保存')
    roleDialogVisible.value = false
    loadUsers() // 重新加载用户列表以获取最新的角色分配
  } catch (error) {
    console.error('保存角色分配失败:', error)
    ElMessage.error('保存角色分配失败')
  }
}

// 创建用户
const handleCreateUser = () => {
  isEditMode.value = false
  // 重置表单
  userForm.id = 0
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.is_active = true
  userForm.is_superuser = false
  
  userFormDialogVisible.value = true
}

// 编辑用户
const handleEditUser = (row: User) => {
  isEditMode.value = true
  userForm.id = row.id
  userForm.username = row.username
  userForm.email = row.email
  userForm.password = '' // 编辑模式下密码置空
  userForm.is_active = row.is_active
  userForm.is_superuser = row.is_superuser
  
  userFormDialogVisible.value = true
}

// 保存用户表单
const saveUserForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEditMode.value) {
          // 编辑用户
          const userData = {
            username: userForm.username,
            email: userForm.email,
            is_active: userForm.is_active,
            is_superuser: userForm.is_superuser
          }
          
          // 只有当密码有值时才包含密码字段
          if (userForm.password) {
            Object.assign(userData, { password: userForm.password })
          }
          
          await systemApi.updateUser(userForm.id, userData)
          ElMessage.success('用户更新成功')
        } else {
          // 创建用户
          await systemApi.createUser({
            username: userForm.username,
            email: userForm.email,
            password: userForm.password,
            is_active: userForm.is_active,
            is_superuser: userForm.is_superuser
          })
          ElMessage.success('用户创建成功')
        }
        
        userFormDialogVisible.value = false
        loadUsers() // 重新加载用户列表
      } catch (error: any) {
        console.error('保存用户失败:', error)
        ElMessage.error(error.response?.data?.detail || '操作失败')
      }
    }
  })
}

// 切换用户状态（启用/禁用）
const handleToggleUserStatus = async (row: User) => {
  if (row.id === currentUserId.value) {
    ElMessage.warning('不能修改自己的账户状态')
    return
  }
  
  try {
    await systemApi.updateUserActive(row.id, !row.is_active)
    ElMessage.success(`用户已${row.is_active ? '禁用' : '启用'}`)
    loadUsers() // 重新加载用户列表
  } catch (error) {
    console.error('更新用户状态失败:', error)
    ElMessage.error('操作失败')
  }
}

onMounted(() => {
  loadUsers()
  loadRoles()
})
</script>

<style scoped>
.user-management {
  width: 100%;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.role-tag {
  margin-right: 5px;
}

.no-roles {
  color: #909399;
  font-size: 13px;
}
</style> 