#!/usr/bin/env python3
"""
手动添加LDAP IP网段支持的数据库字段
运行此脚本前请确保数据库连接正常
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_ldap_ip_support():
    """添加LDAP IP网段支持字段"""
    
    # SQL语句列表
    sql_statements = [
        # 添加新字段
        """
        ALTER TABLE ldap_config 
        ADD COLUMN IF NOT EXISTS ip_ranges JSON;
        """,
        
        """
        ALTER TABLE ldap_config 
        ADD COLUMN IF NOT EXISTS priority INTEGER;
        """,
        
        """
        ALTER TABLE ldap_config 
        ADD COLUMN IF NOT EXISTS auto_select_enabled BOOLEAN;
        """,
        
        # 添加注释
        """
        COMMENT ON COLUMN ldap_config.ip_ranges IS 'IP网段配置列表，支持CIDR和范围格式';
        """,
        
        """
        COMMENT ON COLUMN ldap_config.priority IS '匹配优先级，数字越小优先级越高';
        """,
        
        """
        COMMENT ON COLUMN ldap_config.auto_select_enabled IS '是否启用基于IP的自动选择';
        """,
        
        # 设置默认值
        """
        ALTER TABLE ldap_config ALTER COLUMN priority SET DEFAULT 1;
        """,
        
        """
        ALTER TABLE ldap_config ALTER COLUMN auto_select_enabled SET DEFAULT TRUE;
        """,
        
        # 为现有配置设置默认值
        """
        UPDATE ldap_config SET priority = 1 WHERE priority IS NULL;
        """,
        
        """
        UPDATE ldap_config SET auto_select_enabled = TRUE WHERE auto_select_enabled IS NULL;
        """,
        
        # 设置非空约束
        """
        ALTER TABLE ldap_config ALTER COLUMN priority SET NOT NULL;
        """,
        
        """
        ALTER TABLE ldap_config ALTER COLUMN auto_select_enabled SET NOT NULL;
        """,
        
        # 为默认配置设置更高优先级
        """
        UPDATE ldap_config SET priority = 0 WHERE is_default = TRUE;
        """
    ]
    
    try:
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                logger.info("开始添加LDAP IP网段支持字段...")
                
                for i, sql in enumerate(sql_statements, 1):
                    logger.info(f"执行SQL语句 {i}/{len(sql_statements)}")
                    logger.debug(f"SQL: {sql.strip()}")
                    
                    try:
                        conn.execute(text(sql))
                    except Exception as e:
                        # 如果是字段已存在错误，可以忽略
                        if "already exists" in str(e) or "duplicate column name" in str(e):
                            logger.warning(f"字段可能已存在，跳过: {str(e)}")
                            continue
                        else:
                            raise
                
                # 提交事务
                trans.commit()
                logger.info("✅ LDAP IP网段支持字段添加成功！")
                
                # 验证字段是否添加成功
                verify_result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = 'ldap_config' 
                    AND column_name IN ('ip_ranges', 'priority', 'auto_select_enabled')
                    ORDER BY column_name;
                """))
                
                logger.info("新增字段验证:")
                for row in verify_result:
                    logger.info(f"  {row.column_name}: {row.data_type} (nullable: {row.is_nullable}, default: {row.column_default})")
                
            except Exception as e:
                trans.rollback()
                logger.error(f"❌ 添加字段失败，已回滚: {str(e)}")
                raise
                
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {str(e)}")
        raise

def create_sample_config():
    """创建示例配置"""
    try:
        with engine.connect() as conn:
            # 检查是否已有配置
            result = conn.execute(text("SELECT COUNT(*) as count FROM ldap_config"))
            count = result.fetchone().count
            
            if count == 0:
                logger.info("创建示例LDAP配置...")
                
                sample_config_sql = """
                INSERT INTO ldap_config (
                    name, server, port, use_ssl, base_dn, 
                    user_search_filter, user_name_attr, user_email_attr, user_display_name_attr,
                    auto_create_user, default_role, is_active, is_default, 
                    ip_ranges, priority, auto_select_enabled, description
                ) VALUES (
                    '内网LDAP服务器', 'ldap.internal.com', 389, FALSE, 'DC=internal,DC=com',
                    '(sAMAccountName={username})', 'sAMAccountName', 'mail', 'displayName',
                    TRUE, 'normal_user', TRUE, TRUE,
                    '["***********/16", "10.0.0.0/8"]', 1, TRUE, '内网用户使用的LDAP服务器'
                );
                """
                
                conn.execute(text(sample_config_sql))
                conn.commit()
                logger.info("✅ 示例配置创建成功！")
            else:
                logger.info(f"已存在 {count} 个LDAP配置，跳过示例配置创建")
                
    except Exception as e:
        logger.error(f"创建示例配置失败: {str(e)}")

if __name__ == "__main__":
    try:
        logger.info("=== LDAP IP网段支持安装脚本 ===")
        
        # 添加字段
        add_ldap_ip_support()
        
        # 创建示例配置（可选）
        import sys
        if len(sys.argv) > 1 and sys.argv[1] == "--with-sample":
            create_sample_config()
        
        logger.info("=== 安装完成 ===")
        logger.info("现在您可以:")
        logger.info("1. 在LDAP配置管理界面配置IP网段")
        logger.info("2. 设置配置优先级")
        logger.info("3. 测试基于IP的自动配置选择")
        
    except Exception as e:
        logger.error(f"安装失败: {str(e)}")
        sys.exit(1) 