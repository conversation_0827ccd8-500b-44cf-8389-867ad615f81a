syntax = "proto3";

package terminal;

service TerminalManagement {
  // 终端注册接口
  rpc RegisterTerminal(RegisterRequest) returns (RegisterResponse);
  
  // 终端心跳接口
  rpc Heartbeat(HeartbeatRequest) returns (HeartbeatResponse);
  
  // 终端信息上报接口
  rpc ReportTerminalInfo(TerminalInfoReport) returns (ReportResponse);
  
  // 任务指令下发接口 - 服务器推送模式
  rpc ReceiveCommands(CommandRequest) returns (stream Command);
  
  // 任务执行结果上报接口
  rpc ReportCommandResult(CommandResult) returns (CommandResultResponse);
  
  // 命令通知接口 - 服务器主动通知终端有新命令
  rpc NotifyCommand(CommandNotification) returns (NotificationResponse);
  
  // ===================== 注册表管理服务方法 =====================
  
  // 注册表操作接口
  rpc PerformRegistryOperation(RegistryOperationRequest) returns (RegistryOperationResponse);
  
  // 注册表搜索接口
  rpc SearchRegistry(RegistrySearchRequest) returns (RegistrySearchResponse);
}

// 终端注册请求
message RegisterRequest {
  string hostname = 1;        // 主机名
  string mac_address = 2;     // MAC地址
  string ip_address = 3;      // IP地址
  string agent_version = 4;   // Agent版本
  string os_info = 5;         // 操作系统信息
  string unique_id = 6;       // 终端唯一标识
}

// 注册响应
message RegisterResponse {
  bool success = 1;           // 注册成功标志
  string terminal_id = 2;     // 服务器分配的终端ID
  int32 heartbeat_interval = 3; // 心跳间隔(秒)
  int32 collection_interval = 4; // 信息采集间隔(秒)
  string message = 5;         // 响应消息
}

// 心跳请求
message HeartbeatRequest {
  string terminal_id = 1;     // 终端ID
  int64 timestamp = 2;        // 时间戳
}

// 心跳响应
message HeartbeatResponse {
  bool success = 1;           // 心跳成功标志
  bool has_command = 2;       // 是否有待执行命令
}

// 终端信息上报
message TerminalInfoReport {
  string terminal_id = 1;     // 终端ID
  HardwareInfo hardware = 2;  // 硬件信息
  OSInfo os = 3;              // 操作系统信息
  repeated Software installed_software = 4; // 已安装软件
  NetworkInfo network = 5;    // 网络信息
  UserInfo last_login_user = 6; // 最后登录用户信息
  int64 timestamp = 7;        // 上报时间戳
}

// 硬件信息
message HardwareInfo {
  string cpu_model = 1;       // CPU型号
  int32 cpu_cores = 2;        // CPU核心数
  int64 memory_total = 3;     // 内存总量(KB)
  repeated DiskInfo disks = 4; // 磁盘信息
  string serial_number = 5;   // 序列号
  string manufacturer = 6;    // 制造商
  string model = 7;           // 型号
}

// 磁盘信息
message DiskInfo {
  string name = 1;            // 磁盘名称
  int64 total_space = 2;      // 总容量(KB)
  int64 free_space = 3;       // 可用容量(KB)
  string filesystem = 4;      // 文件系统类型
  string mount_point = 5;     // 挂载点(Windows上为盘符)
}

// 操作系统信息
message OSInfo {
  string name = 1;            // 操作系统名称
  string version = 2;         // 版本号
  string build = 3;           // 构建号
  string architecture = 4;    // 架构(32位/64位)
  string install_date = 5;    // 安装日期
  repeated string installed_updates = 6; // 已安装更新
  SecurityInfo security = 7;  // 安全信息
}

// 安全信息
message SecurityInfo {
  bool firewall_enabled = 1;  // 防火墙状态
  string antivirus = 2;       // 杀毒软件
  bool antivirus_enabled = 3; // 杀毒软件状态
}

// 软件信息
message Software {
  string name = 1;            // 软件名称
  string version = 2;         // 版本号
  string publisher = 3;       // 发布商
  string install_date = 4;    // 安装日期
  int64 size = 5;             // 软件大小(KB)
  string install_location = 6; // 安装位置
}

// 网络信息
message NetworkInfo {
  repeated NetworkInterface interfaces = 1; // 网络接口列表
  string hostname = 2;        // 主机名
  string domain = 3;          // 所在域
  repeated string dns_servers = 4; // DNS服务器
  string default_gateway = 5; // 默认网关
}

// 网络接口
message NetworkInterface {
  string name = 1;            // 接口名称
  string mac_address = 2;     // MAC地址
  string ip_address = 3;      // IP地址
  string subnet_mask = 4;     // 子网掩码
  bool dhcp_enabled = 5;      // 是否启用DHCP
  bool is_connected = 6;      // 是否连接
}

// 用户信息
message UserInfo {
  string username = 1;        // 用户名
  string full_name = 2;       // 全名
  string login_time = 3;      // 登录时间
  string domain = 4;          // 所属域
}

// 上报响应
message ReportResponse {
  bool success = 1;           // 上报成功标志
  string message = 2;         // 响应消息
}

// 指令请求
message CommandRequest {
  string terminal_id = 1;     // 终端ID
}

// 指令信息
message Command {
  string command_id = 1;      // 指令ID
  CommandType type = 2;       // 指令类型
  string content = 3;         // 指令内容
  int64 create_time = 4;      // 创建时间
  int32 timeout = 5;          // 超时时间(秒)
  
  // 指令类型枚举
  enum CommandType {
    COLLECT_INFO = 0;         // 采集信息
    UPGRADE_AGENT = 1;        // 升级Agent
    CUSTOM_COMMAND = 2;       // 自定义指令
    UNINSTALL_SOFTWARE = 3;   // 卸载软件
    REGISTRY_OPERATION = 4;   // 注册表操作
  }
}

// 指令执行结果
message CommandResult {
  string command_id = 1;      // 指令ID
  string terminal_id = 2;     // 终端ID
  bool success = 3;           // 执行结果
  string output = 4;          // 输出内容
  string error = 5;           // 错误信息
  int64 execution_time = 6;   // 执行时间戳
  int32 execution_duration = 7; // 执行耗时(毫秒)
}

// 指令结果响应
message CommandResultResponse {
  bool received = 1;          // 结果接收状态
  string message = 2;         // 响应消息
}

// 命令通知请求
message CommandNotification {
  string terminal_id = 1;     // 终端ID
  string command_id = 2;      // 命令ID (可选)
}

// 通知响应
message NotificationResponse {
  bool received = 1;          // 通知接收状态
  string message = 2;         // 响应消息
}

// ===================== 注册表管理相关消息 =====================

// 注册表操作类型
enum RegistryOperationType {
  READ = 0;                   // 读取
  WRITE = 1;                  // 写入
  DELETE = 2;                 // 删除
  CREATE_KEY = 3;             // 创建键
  DELETE_KEY = 4;             // 删除键
  ENUMERATE = 5;              // 枚举子键和值
  EXPORT = 6;                 // 导出
  IMPORT = 7;                 // 导入
  BACKUP = 8;                 // 备份
}

// 注册表值类型
enum RegistryValueType {
  REG_SZ = 0;                 // 字符串
  REG_EXPAND_SZ = 1;          // 可扩展字符串
  REG_BINARY = 2;             // 二进制数据
  REG_DWORD = 3;              // 32位数值
  REG_QWORD = 4;              // 64位数值
  REG_MULTI_SZ = 5;           // 多字符串
}

// 注册表根键类型
enum RegistryRootKey {
  HKEY_CLASSES_ROOT = 0;      // HKCR
  HKEY_CURRENT_USER = 1;      // HKCU
  HKEY_LOCAL_MACHINE = 2;     // HKLM
  HKEY_USERS = 3;             // HKU
  HKEY_CURRENT_CONFIG = 4;    // HKCC
}

// 注册表操作请求
message RegistryOperationRequest {
  string command_id = 1;           // 命令ID
  string terminal_id = 2;          // 终端ID
  RegistryOperationType operation = 3; // 操作类型
  RegistryRootKey root_key = 4;    // 根键
  string sub_key_path = 5;         // 子键路径
  string value_name = 6;           // 值名称 (可选)
  RegistryValueType value_type = 7; // 值类型 (可选)
  string value_data = 8;           // 值数据 (可选)
  bool create_backup = 9;          // 是否创建备份
  string backup_reason = 10;       // 备份原因
}

// 注册表值信息
message RegistryValue {
  string name = 1;                 // 值名称
  RegistryValueType type = 2;      // 值类型
  string data = 3;                 // 值数据 (字符串形式)
  int64 size = 4;                  // 数据大小
}

// 注册表键信息
message RegistryKey {
  string name = 1;                 // 键名称
  string full_path = 2;            // 完整路径
  repeated string sub_keys = 3;    // 子键列表
  repeated RegistryValue values = 4; // 值列表
  int64 last_modified = 5;         // 最后修改时间
  int32 sub_key_count = 6;         // 子键数量
  int32 value_count = 7;           // 值数量
}

// 注册表操作响应
message RegistryOperationResponse {
  string command_id = 1;           // 命令ID
  string terminal_id = 2;          // 终端ID
  bool success = 3;                // 操作结果
  string message = 4;              // 响应消息
  string error = 5;                // 错误信息
  RegistryKey key_data = 6;        // 键数据 (查询操作)
  RegistryValue value_data = 7;    // 值数据 (查询操作)
  string backup_id = 8;            // 备份ID (如果创建了备份)
  int64 operation_time = 9;        // 操作时间戳
}

// 注册表搜索请求
message RegistrySearchRequest {
  string command_id = 1;           // 命令ID
  string terminal_id = 2;          // 终端ID
  RegistryRootKey root_key = 3;    // 根键
  string start_path = 4;           // 开始路径
  string search_pattern = 5;       // 搜索模式
  bool search_keys = 6;            // 搜索键名
  bool search_values = 7;          // 搜索值名
  bool search_data = 8;            // 搜索值数据
  int32 max_depth = 9;             // 最大搜索深度
  int32 max_results = 10;          // 最大结果数
}

// 注册表搜索结果
message RegistrySearchResult {
  string path = 1;                 // 匹配路径
  string match_type = 2;           // 匹配类型 (key/value_name/value_data)
  string match_text = 3;           // 匹配文本
  RegistryValue value = 4;         // 值信息 (如果匹配的是值)
}

// 注册表搜索响应
message RegistrySearchResponse {
  string command_id = 1;           // 命令ID
  string terminal_id = 2;          // 终端ID
  bool success = 3;                // 搜索结果
  string message = 4;              // 响应消息
  repeated RegistrySearchResult results = 5; // 搜索结果
  int32 total_results = 6;         // 总结果数
  bool more_results = 7;           // 是否还有更多结果
}

// 注册表备份信息
message RegistryBackup {
  string backup_id = 1;            // 备份ID
  string backup_name = 2;          // 备份名称
  RegistryRootKey root_key = 3;    // 根键
  string key_path = 4;             // 键路径
  string reason = 5;               // 备份原因
  int64 create_time = 6;           // 创建时间
  int64 size = 7;                  // 备份大小
  string file_path = 8;            // 备份文件路径
} 