<template>
  <div class="mobile-asset-index">
    <!-- 下拉刷新 -->
    <van-pull-refresh 
      v-model="refreshing" 
      @refresh="onRefresh"
      :disabled="loading"
      success-text="刷新成功"
      pulling-text="下拉即可刷新..."
      loosing-text="释放即可刷新..."
      loading-text="加载中..."
    >
      <!-- 功能菜单 -->
      <van-grid :column-num="2" :gutter="16">
        <van-grid-item
          icon="orders-o"
          text="资产列表"
          @click="goToList"
        />
        <van-grid-item
          icon="add-o"
          text="添加资产"
          @click="goToAdd"
        />
      </van-grid>
      
      <!-- 统计信息 -->
      <van-cell-group title="资产统计" inset>
        <van-cell title="总资产数" :value="statistics.totalAssets" />
        <van-cell title="在用资产" :value="statistics.inUseAssets" />
        <van-cell title="闲置资产" :value="statistics.idleAssets" />
        <van-cell title="报废资产" :value="statistics.scrapAssets" />
      </van-cell-group>
      
      <!-- 分类统计 -->
      <van-cell-group title="分类统计" inset>
        <van-cell 
          v-for="(count, categoryName) in categoryStats" 
          :key="categoryName"
          :title="categoryName" 
          :value="count" 
        />
        <van-cell 
          v-if="Object.keys(categoryStats).length === 0" 
          title="暂无分类数据" 
          value="--" 
        />
      </van-cell-group>
      
      <!-- 管理功能 -->
      <van-cell-group title="管理功能" inset>
        <van-cell title="盘点任务" is-link @click="goToInventory" />
        <van-cell title="资产设置" is-link @click="goToSettings" />
        <van-cell title="字段值管理" is-link @click="goToFieldValues" />
        <van-cell title="自定义字段管理" is-link @click="goToCustomFields" />
      </van-cell-group>
      
      <!-- 加载状态 -->
      <van-loading v-if="loading && !refreshing" class="loading-overlay" vertical>
        加载中...
      </van-loading>
      
      <!-- 错误状态 -->
      <van-empty 
        v-if="hasError && !loading" 
        image="error" 
        description="数据加载失败"
      >
        <van-button round type="primary" @click="loadStatistics">
          重新加载
        </van-button>
      </van-empty>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { assetApi } from '@/api/asset'
import { fieldValueApi } from '@/api/field_value'
import type { Asset } from '@/types/asset'
import type { FieldValue } from '@/types/field_value'
import { FIELD_NAMES } from '@/types/field_value'

const router = useRouter()

// 状态管理
const loading = ref(false)
const refreshing = ref(false)
const hasError = ref(false)

// 统计数据
const statistics = ref({
  totalAssets: '--',
  inUseAssets: '--',
  idleAssets: '--',
  scrapAssets: '--'
})

// 分类统计 - 改为动态结构
const categoryStats = ref<Record<string, string>>({})

// 页面跳转
const goToList = () => {
  router.push('/m/asset/list')
}

const goToAdd = () => {
  router.push('/m/asset/add')
}

const goToInventory = () => {
  router.push('/m/asset/inventory')
}

const goToSettings = () => {
  router.push('/m/asset/settings')
}

const goToFieldValues = () => {
  router.push('/m/asset/field-values')
}

const goToCustomFields = () => {
  router.push('/m/asset/custom-fields')
}

// 下拉刷新
const onRefresh = async () => {
  try {
    await loadStatistics()
    showToast('刷新成功')
  } catch (error) {
    showToast('刷新失败')
  } finally {
    refreshing.value = false
  }
}



// 状态匹配函数
const isStatusMatch = (status: string, targetStatuses: string[]): boolean => {
  if (!status) return false
  const normalizedStatus = status.toLowerCase().trim()
  return targetStatuses.some(target => 
    normalizedStatus === target.toLowerCase() || 
    normalizedStatus.includes(target.toLowerCase())
  )
}

// 分类匹配函数 - 直接基于category字段
const isCategoryMatch = (asset: Asset, categoryValue: string): boolean => {
  return asset.category === categoryValue
}

// 获取设备分类配置
const getCategoryConfigs = async (): Promise<FieldValue[]> => {
  try {
    const response = await fieldValueApi.getFieldValues({
      field_name: FIELD_NAMES.CATEGORY,
      limit: 100 // 获取所有分类配置
    })
    return response.data?.data || []
  } catch (error) {
    console.error('获取设备分类配置失败:', error)
    return []
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    hasError.value = false
    
    if (!refreshing.value) {
      loading.value = true
    }
    
    console.log('开始加载资产统计数据...')
    
    // 分页获取资产数据进行统计
    let allAssets: Asset[] = []
    let currentSkip = 0
    const pageSize = 100 // 符合后端限制
    let totalCount = 0
    
    // 获取第一页数据
    const firstResponse = await assetApi.getAssets({
      skip: 0,
      limit: pageSize,
    })
    
    console.log('第一页API响应:', firstResponse)
    
    const firstPageData = firstResponse.data || firstResponse
    const firstPageAssets: Asset[] = firstPageData.data || []
    totalCount = firstPageData.total || 0
    
    allAssets.push(...firstPageAssets)
    
    // 如果有更多数据，继续获取（最多获取10页，避免过多请求）
    const maxPages = Math.min(10, Math.ceil(totalCount / pageSize))
    
    for (let page = 1; page < maxPages; page++) {
      currentSkip = page * pageSize
      
      try {
        const pageResponse = await assetApi.getAssets({
          skip: currentSkip,
          limit: pageSize,
        })
        
        const pageData = pageResponse.data || pageResponse
        const pageAssets: Asset[] = pageData.data || []
        allAssets.push(...pageAssets)
        
        console.log(`获取第${page + 1}页数据: ${pageAssets.length}条`)
      } catch (pageError) {
        console.warn(`获取第${page + 1}页数据失败:`, pageError)
        break // 如果某页失败，停止继续获取
      }
    }
    
    console.log(`总共获取到 ${allAssets.length} 条资产数据，总数: ${totalCount}`)
    
    // 使用合并后的数据进行统计
    const assets = allAssets
    const total = totalCount
    
    console.log(`获取到 ${assets.length} 条资产数据，总数: ${total}`)
    
    if (!Array.isArray(assets)) {
      throw new Error('资产数据格式错误')
    }
    
    // 计算状态统计
    const inUseCount = assets.filter(asset => 
      isStatusMatch(asset.status, ['使用中', 'IN_USE', '在用'])
    ).length
    
    const idleCount = assets.filter(asset => 
      isStatusMatch(asset.status, ['闲置', 'IDLE', '空闲'])
    ).length
    
    const scrapCount = assets.filter(asset => 
      isStatusMatch(asset.status, ['已报废', 'SCRAPPED', '报废'])
    ).length
    
    // 更新统计数据
    statistics.value = {
      totalAssets: total.toString(),
      inUseAssets: inUseCount.toString(),
      idleAssets: idleCount.toString(),
      scrapAssets: scrapCount.toString()
    }
    
    // 计算分类统计 - 基于动态配置
    const categoryConfigs = await getCategoryConfigs()
    const newCategoryStats: Record<string, string> = {}
    
    if (categoryConfigs.length > 0) {
      // 根据字段值配置计算分类统计
      for (const config of categoryConfigs) {
        const count = assets.filter(asset => 
          isCategoryMatch(asset, config.field_value)
        ).length
        newCategoryStats[config.field_value] = count.toString()
      }
    } else {
      // 如果没有配置分类，统计所有现有的资产类别
      const categoryCountMap = new Map<string, number>()
      
      for (const asset of assets) {
        const category = asset.category || '未分类'
        categoryCountMap.set(category, (categoryCountMap.get(category) || 0) + 1)
      }
      
      // 转换为统计对象
      for (const [category, count] of categoryCountMap.entries()) {
        newCategoryStats[category] = count.toString()
      }
    }
    
    categoryStats.value = newCategoryStats
    
    console.log('统计数据更新完成:', {
      statistics: statistics.value,
      categoryStats: categoryStats.value
    })
    
  } catch (error: any) {
    console.error('加载统计数据失败:', error)
    hasError.value = true
    
    // 详细错误信息
    let errorMessage = '加载统计数据失败'
    if (error.response) {
      errorMessage += `：${error.response.status} ${error.response.statusText}`
      if (error.response.data?.detail) {
        errorMessage += ` - ${error.response.data.detail}`
      }
    } else if (error.message) {
      errorMessage += `：${error.message}`
    }
    
    showToast(errorMessage)
    
    // 设置默认值
    statistics.value = {
      totalAssets: '--',
      inUseAssets: '--',
      idleAssets: '--',
      scrapAssets: '--'
    }
    categoryStats.value = {}
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.mobile-asset-index {
  padding: 16px 0;
  background-color: #f7f8fa;
  // 移除 min-height: 100vh，让内容自然撑开高度
  // min-height: 100vh;
  position: relative;
  
  // 确保底部有足够的滚动空间
  padding-bottom: 32px;
  
  .van-grid {
    margin-bottom: 16px;
  }
  
  .van-cell-group {
    margin-bottom: 16px;
    
    // 最后一个cell-group增加底部间距
    &:last-child {
      margin-bottom: 32px;
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}
</style> 