# 注册表延迟加载优化任务

## 问题描述
终端详情页打开时，注册表管理Tab页中的RegistryBrowser组件会在`onMounted`生命周期自动加载注册表根键数据，即使用户没有点击注册表管理Tab页。这导致不必要的API调用和性能损耗。

## 解决方案
实施延迟加载策略，只在用户首次激活注册表管理Tab时才加载数据。

## 实施步骤

### 1. 修改终端详情页 (frontend/src/views/terminal/Detail.vue)

#### 1.1 添加Tab切换事件监听
```typescript
// 原来：
<el-tabs v-model="activeTab" class="detail-tabs" type="card">

// 修改为：
<el-tabs v-model="activeTab" class="detail-tabs" type="card" @tab-change="handleTabChange">
```

#### 1.2 添加激活状态管理
```typescript
// 添加状态变量
const registryTabActivated = ref(false)

// 添加Tab切换处理函数
const handleTabChange = (tabName: string) => {
  if (tabName === 'software' && softwareList.value.length === 0) {
    fetchSoftwareList()
  } else if (tabName === 'commands' && commandsList.value.length === 0) {
    fetchCommandsList()
  } else if (tabName === 'registry' && !registryTabActivated.value) {
    registryTabActivated.value = true
  }
}
```

#### 1.3 传递激活状态给RegistryBrowser组件
```vue
<RegistryBrowser 
  :terminal-id="terminalId" 
  :is-active="activeTab === 'registry'"
  :has-been-activated="registryTabActivated"
  @registry-operation="handleRegistryOperation"
/>
```

### 2. 修改RegistryBrowser组件 (frontend/src/views/terminal/components/RegistryBrowser.vue)

#### 2.1 添加新的props
```typescript
const props = withDefaults(defineProps<{
  terminalId: string
  isActive?: boolean
  hasBeenActivated?: boolean
}>(), {
  isActive: false,
  hasBeenActivated: false
})
```

#### 2.2 修改初始化逻辑
```typescript
// 原来：onMounted时自动加载
onMounted(() => {
  nextTick(() => {
    loadRootKey()
  })
})

// 修改为：监听激活状态
watch(
  () => props.hasBeenActivated,
  (hasBeenActivated) => {
    if (hasBeenActivated && !treeData.value.length) {
      nextTick(() => {
        loadRootKey()
      })
    }
  },
  { immediate: true }
)
```

#### 2.3 添加未激活状态的占位内容
```vue
<template>
  <div class="registry-browser">
    <!-- 未激活状态的占位内容 -->
    <div v-if="!hasBeenActivated" class="placeholder-content">
      <el-empty description="点击注册表管理标签页开始浏览注册表">
        <el-icon class="placeholder-icon"><Folder /></el-icon>
        <p class="placeholder-text">注册表数据将在首次访问时加载</p>
      </el-empty>
    </div>
    
    <!-- 激活后的注册表内容 -->
    <div v-if="hasBeenActivated" class="registry-toolbar">
      ...
    </div>
    <div v-if="hasBeenActivated" class="registry-content">
      ...
    </div>
  </div>
</template>
```

#### 2.4 添加占位内容样式
```css
.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.placeholder-text {
  color: #909399;
  font-size: 14px;
  margin: 8px 0 0 0;
}
```

## 优化效果

### 性能提升
1. **减少不必要的API调用**：只有在用户真正需要访问注册表时才加载数据
2. **提升页面初始加载速度**：终端详情页打开时不再自动加载注册表数据
3. **减少服务器压力**：避免每次打开终端详情都触发注册表枚举操作

### 用户体验优化
1. **明确的状态提示**：未激活时显示友好的占位内容
2. **按需加载**：符合用户预期，只在需要时才加载数据
3. **避免等待**：页面主要内容能更快展示

### 技术优势
1. **组件复用**：RegistryBrowser组件保持独立性，可在其他地方复用
2. **向后兼容**：通过props默认值确保在没有传递激活状态时也能正常工作
3. **代码清晰**：通过明确的状态管理使组件行为更可预测

## 文件修改列表
- `frontend/src/views/terminal/Detail.vue` - 添加Tab切换监听和状态管理
- `frontend/src/views/terminal/components/RegistryBrowser.vue` - 实现延迟加载逻辑

## 测试建议
1. 打开终端详情页，验证不会自动加载注册表数据
2. 点击注册表管理Tab，验证数据正常加载
3. 切换到其他Tab再切回注册表Tab，验证数据不会重复加载
4. 检查控制台日志，确认API调用时机正确 