# 终端管理模块功能需求文档

## 功能背景

在企业网络环境中，统一监控和管理各终端设备的状态与信息至关重要。许多现有的终端管理平台提供**资产清单（Inventory）**功能，可全面采集每台终端的硬件和软件详细信息。通常通过在终端部署Agent客户端并定期收集数据的方式，实现对所有受管终端信息的集中掌握。基于上述需求，我们计划在现有Web平台中新增加“终端管理”模块，用于实现对终端设备的信息采集、统一展示及远程控制等功能。该模块将使管理员能够在平台上一站式查看所有客户端设备的系统信息（包括硬件配置、操作系统版本、已安装软件、网络配置等），实时监控终端在线状态，并通过控制中心对终端执行管理操作（例如远程下发升级指令或定时任务），以提升IT运维效率和终端安全性。

## 菜单结构

- **终端管理**（一级菜单）  
  - **终端概况**（子菜单）：进入终端管理模块后的默认页面，用于展示当前受管Agent终端的整体状态和信息概览。终端概况页面应显示终端设备的总体情况，如终端总数、在线/离线终端数量统计等，并提供终端列表查看每台设备的关键信息（例如主机名、IP地址、操作系统、当前状态等）。管理员可以在该页面搜索、筛选终端，并通过点击具体终端查看更详细的配置信息和状态数据。

## Agent客户端功能需求

- **操作系统兼容**：Agent客户端需支持在多版本的 Windows 操作系统上运行，包括主流的桌面Windows 7/8/10/11等版本（32位/64位），以及常见的Windows Server服务器操作系统，确保在各种Windows环境下均可正常安装和运行。  
- **首次信息采集**：Agent安装完成后应立即自动执行首次信息采集任务。也就是说，Agent在首次启动时便向控制中心上报终端的完整信息快照，确保平台能够及时获取该终端的初始状态数据。  
- **信息采集范围**：Agent需要采集终端设备的各类软硬件信息，具体包括：  
  - *硬件信息*：采集终端的CPU型号、内存容量、磁盘存储容量和利用率、主板/设备序列号等硬件规格参数。  
  - *操作系统信息*：采集操作系统名称和版本号、系统补丁级别（已安装更新情况）、系统安全设置（如防火墙和杀毒软件状态）等操作系统相关信息。  
  - *已安装软件清单*：获取终端上安装的应用程序列表，包括软件名称、版本号、安装时间等，以便掌握软件资产情况。  
  - *网络信息*：采集终端的网络配置，包括IP地址、MAC地址、主机名、DNS服务器、网关等网络参数，便于定位终端所在网络环境。  
  - *最后登录账号信息*：记录终端上最近一次登录的用户账户名称及时间，用于审计用户登录活动（业界一些管理工具也提供“用户登录报告”用于此目的）。  
- **定时信息采集**：Agent应支持周期性的信息采集机制。控制中心能够配置并下发采集周期策略（例如每隔24小时或每周一定时采集），Agent根据收到的策略定期执行信息收集并上报。通过定时采集，可保证平台中终端信息的持续更新和准确性。采集周期应支持按需调整，以平衡数据及时性和网络开销。  
- **自动升级**：Agent客户端支持通过控制中心进行远程自动升级。当有新版本的Agent发布时，控制中心可以下发升级指令或升级包，Agent接收到指令后自动下载并更新自身版本。升级过程应尽量实现无缝衔接（例如更新后自动重启Agent服务），无需人工在终端逐一重新部署，从而降低运维成本。  
- **注册与上线**：Agent安装时需要配置控制中心服务器地址（如平台的IP/域名及端口）。Agent在启动后，会根据配置主动连接到指定的控制中心，并发送注册请求以完成首次注册上线。注册过程中，Agent将上报自身标识（例如主机名、唯一ID等）和采集到的详细信息，服务器确认后将该终端标记为“在线”状态。此流程应自动完成，无需人为干预。之后Agent保持与服务器的心跳通信，定期汇报状态，以便服务器及时感知终端的在线/离线状态变化。

## 平台后台功能需求

- **通信协议**：后台平台与Agent采用高性能的 gRPC 通信机制进行交互。gRPC作为谷歌开源的远程过程调用框架，支持多种编程语言和高效的二进制传输，可保证Agent与服务器之间消息通信的实时性和可靠性。  
- **注册与状态管理**：后台应能够接收并处理Agent的注册请求。当新的Agent连接时，后台为其创建终端设备档案（记录该终端的基本信息和硬件指纹等），并将其状态标记为在线。后台需要维护每个Agent的连接会话或定期心跳，及时更新终端在线/离线状态并在界面上实时展示。若检测到Agent心跳超时或连接中断，平台应将对应终端标记为离线。  
- **数据存储与查询**：后台平台负责接收Agent上报的各类终端信息（硬件、操作系统、软件清单、网络配置、登录用户等），并将其存储到数据库中。数据存储应设计合理的结构以关联到具体终端，方便后续查询和展示。平台应提供查询接口和功能，支持按终端名称、IP地址等字段搜索终端，并能够按需过滤显示特定软硬件属性（例如筛选出内存小于某值的终端或安装了某软件的终端等），以便运维人员进行资产统计和状态排查。  
- **任务下发**：平台能够向Agent推送控制指令和任务命令。管理员在控制中心发起的管理操作可通过后台下发给Agent执行，例如远程触发Agent立即进行一次信息采集刷新、下发配置变更指令，或者分发软件更新/升级指令等。Agent收到指令后会执行相应操作并反馈结果。这样的设计使得许多在服务器端安排的任务可以由Agent自动完成（例如卸载终端上的指定软件等），实现对终端的远程控制管理能力。后台应支持对任务下发进行管理，包括支持批量下发指令以及跟踪指令执行状态（成功、失败或超时）。  
- **终端状态监控**：平台需要具备对终端状态的实时监控能力，并在前端界面直观展示。管理员在“终端概况”等页面应能看到哪些终端当前在线、哪些已离线，在线时长、最后心跳时间等信息也可供参考。后台通过与Agent的持续通信获取这些状态变化，并在状态变更时触发更新（例如Agent下线或上线事件）。终端状态的实时可视化有助于及时发现异常终端（掉线或长时间未上报的设备）并进行排查。

## 通信机制

终端管理模块采用**gRPC通信架构**实现Agent与服务器之间的交互。所示，gRPC基于客户端/服务器模型运行：服务器端在指定端口监听等待，Agent作为客户端发起连接请求建立会话。一旦连接建立，Agent会通过调用定义好的gRPC接口（proto文件中约定的方法）向服务器发送请求消息，服务器处理后返回响应。初次连接时的注册请求以及后续的心跳、信息上报都通过该长连接的RPC调用来完成，而服务器也可以通过服务器推送或流式响应的方式下发指令给Agent，实现双向实时通信。

通信过程中需要考虑安全性。gRPC框架支持基于TLS的加密传输和双向认证。所示，当启用TLS时，Agent和服务器之间将建立加密通道并进行证书双向校验，从而保障通信数据的机密性和防止非法终端接入。在终端管理模块中，应当支持配置TLS证书以启用安全通信（默认也可采用非加密通信用于内部网络便利，但在安全要求高的环境下建议开启TLS加密传输）。此外，可通过授权机制确保只有经过认证的Agent才能注册到平台，例如在Agent注册时要求有效的密钥/凭证，以防止恶意客户端冒充终端接入。

## 自动发现机制

本终端管理方案不采用广播扫描局域网的方式来发现新终端设备，即不会通过在网络中泛洪探测来自动识别终端。取而代之的是**Agent主动注册机制**：在为终端安装Agent时，运维人员需配置好控制中心的服务器地址。一旦Agent安装并启动成功，它会利用配置的地址主动与平台建立连接并注册自己。因此，所有安装了Agent的终端都会自动接入平台受控，而未安装Agent的设备即使在局域网内也不会被平台纳入管理。这样的设计避免了广播扫描带来的网络开销和安全隐患，确保只有授权部署了Agent的终端才会出现在平台中。虽然初始部署需要人为安装Agent，但一旦配置完毕，终端的发现和上线都是自动完成的：Agent主动“发现”平台并登记自己，实现终端接入的自动化。

## 部署与配置

1. **服务器端部署准备**：在现有Web平台的服务器上部署终端管理模块的后台服务。确认服务器端已集成gRPC服务组件，在指定端口（例如50051端口）监听Agent连接请求。根据需要，在服务器上配置TLS证书与密钥（可选），以支持安全的gRPC通信。确保服务器的防火墙放行了gRPC所需端口，并为终端管理模块准备好必要的数据库表结构用于存储终端信息。  
2. **平台策略配置**：管理员通过控制中心界面对终端管理模块进行基本配置，例如设置**默认的信息采集周期**（如每24小时采集一次）等策略。这些配置将下发给后续上线的Agent使用；当然管理员也可稍后再调整策略下发。平台上还可以预先定义Agent自动升级的相关策略（比如是否允许自动更新、升级包存放地址等）以备后用。  
3. **Agent安装及地址配置**：在需要纳管的终端设备（Windows系统）上安装Agent客户端程序。安装过程中，填入或确认控制中心服务器的地址和端口配置。安装完成后，Agent以系统服务形式后台运行，并获取最高权限以确保能够采集到全面的系统信息（例如需要管理员权限来读取硬件和软件清单）。对于批量部署，可借助批处理脚本或软件分发工具在多台终端静默安装Agent，并自动写入服务器地址配置，以提高部署效率。  
4. **Agent注册与首轮采集**：当Agent首次启动时，立即尝试连接配置的控制中心地址进行注册。连接建立后，Agent发送注册请求，上报自身身份标识和终端基本信息。服务器接收注册后，在数据库中创建该终端的记录并标记其为在线。紧接着，Agent会执行首次信息采集，将采集到的硬件、软件等详细信息通过gRPC上报给服务器。服务器存储这些数据，并将该终端显示在管理界面上。此时管理员即可在平台的终端列表中看见新上线的终端及其基本信息。  
5. **终端监测与数据更新**：Agent按照下发的策略进入**定时采集**运行状态。即每隔一定周期（例如每天凌晨或每隔若干小时）自动收集终端最新信息并上报，服务器据此更新数据库中的终端信息记录。同时，Agent与服务器保持心跳通信，用于通知自身在线状态和接受服务器下发的即时指令。管理员在前端界面上即可查看终端信息的最新更新时间、最近一次上线时间等。当需要获取某终端的最新状态时，管理员也可在平台上手动下发“立即刷新”命令，触发Agent即时上报当前信息。  
6. **Agent升级管理**：当终端管理模块发布新的Agent版本时，后台可通过任务下发机制通知各Agent进行自我升级。管理员在控制中心上传新的Agent安装包并发布升级命令后，在线的Agent会接收指令，并从指定的升级地址下载更新包。Agent下载完成后自动执行升级（替换可执行文件并重启服务进程）。升级过程中Agent应尽可能保持与服务器的连接（可在重启后立即重新注册），并上报升级结果状态。平台需提供升级任务的监控功能，例如显示各终端的升级进度和结果。通过自动升级机制，确保终端上的Agent客户端保持为最新版本，获取最新功能和修复，而无需运维人员逐台手动更新。