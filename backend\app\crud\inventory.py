from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, desc, asc
from fastapi import HTTPException

from app.crud.base import CRUDBase
from app.models.inventory import InventoryTask, InventoryRecord
from app.models.asset import Asset
from app.schemas.inventory import (
    InventoryTaskCreate,
    InventoryTaskUpdate,
    InventoryRecordCreate,
    InventoryRecordUpdate
)

class CRUDInventoryTask(CRUDBase[InventoryTask, InventoryTaskCreate, InventoryTaskUpdate]):
    def create(self, db: Session, *, obj_in: InventoryTaskCreate) -> InventoryTask:
        """创建盘点任务"""
        db_obj = InventoryTask(
            name=obj_in.name,
            description=obj_in.description,
            status="pending",
            start_date=obj_in.start_date,
            end_date=obj_in.end_date,
            created_by=obj_in.created_by,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Dict[str, Any] = None,
        sort_by: str = None,
        sort_order: str = None
    ) -> List[InventoryTask]:
        """获取盘点任务列表"""
        query = db.query(self.model)
        
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        search_term = f"%{value}%"
                        query = query.filter(
                            or_(
                                self.model.name.ilike(search_term),
                                self.model.description.ilike(search_term),
                                self.model.created_by.ilike(search_term)
                            )
                        )
                    elif field == "start_date_start":
                        query = query.filter(self.model.start_date >= value)
                    elif field == "start_date_end":
                        query = query.filter(self.model.start_date <= value)
                    elif field == "end_date_start":
                        query = query.filter(self.model.end_date >= value)
                    elif field == "end_date_end":
                        query = query.filter(self.model.end_date <= value)
                    elif hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)

        # 应用排序
        if sort_by and hasattr(self.model, sort_by):
            sort_field = getattr(self.model, sort_by)
            if sort_order == 'desc':
                query = query.order_by(desc(sort_field))
            else:
                query = query.order_by(asc(sort_field))
        else:
            # 默认按创建时间倒序排序
            query = query.order_by(desc(self.model.created_at))
        
        return query.offset(skip).limit(limit).all()

    def get_count(
        self,
        db: Session,
        *,
        filters: Dict[str, Any] = None
    ) -> int:
        """获取盘点任务总数"""
        query = db.query(self.model)
        
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        search_term = f"%{value}%"
                        query = query.filter(
                            or_(
                                self.model.name.ilike(search_term),
                                self.model.description.ilike(search_term),
                                self.model.created_by.ilike(search_term)
                            )
                        )
                    elif field == "start_date_start":
                        query = query.filter(self.model.start_date >= value)
                    elif field == "start_date_end":
                        query = query.filter(self.model.start_date <= value)
                    elif field == "end_date_start":
                        query = query.filter(self.model.end_date >= value)
                    elif field == "end_date_end":
                        query = query.filter(self.model.end_date <= value)
                    elif hasattr(self.model, field):
                        query = query.filter(getattr(self.model, field) == value)
        
        return query.count()

class CRUDInventoryRecord(CRUDBase[InventoryRecord, InventoryRecordCreate, InventoryRecordUpdate]):
    def create(self, db: Session, *, obj_in: InventoryRecordCreate) -> InventoryRecord:
        """创建盘点记录"""
        # 构建创建数据
        create_data = {
            "task_id": obj_in.task_id,
            "asset_id": obj_in.asset_id,
            "status": obj_in.status,
            "remarks": obj_in.remarks,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        # 如果提供了checked_by，则设置审计信息
        if hasattr(obj_in, 'checked_by') and obj_in.checked_by:
            create_data["checked_by"] = obj_in.checked_by
            create_data["checked_at"] = datetime.now()
        
        # 如果状态不是pending，自动设置审计信息
        if obj_in.status and obj_in.status != 'pending':
            if "checked_at" not in create_data:
                create_data["checked_at"] = datetime.now()
            # 如果没有提供checked_by但状态已变更，设置默认值
            if "checked_by" not in create_data or not create_data["checked_by"]:
                create_data["checked_by"] = "系统用户"  # 或者从当前用户上下文获取
        
        db_obj = InventoryRecord(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: InventoryRecord,
        obj_in: Union[InventoryRecordUpdate, Dict[str, Any]]
    ) -> InventoryRecord:
        """更新盘点记录"""
        update_data = obj_in.model_dump(exclude_unset=True) if isinstance(obj_in, InventoryRecordUpdate) else obj_in
        
        if "status" in update_data:
            update_data["checked_at"] = datetime.now()
            # 确保有盘点人信息（只在前端没有提供或为空时设置默认值）
            if "checked_by" not in update_data or not update_data.get("checked_by"):
                update_data["checked_by"] = "系统用户"  # 或者从当前用户上下文获取
            
            # 注意：信息变更状态下不再立即修改资产信息
            # 变更信息会保存在盘点记录的 new_* 字段中
            # 实际的资产信息更新将在盘点任务完成时统一应用
        
        update_data["updated_at"] = datetime.now()
        
        for field in update_data:
            setattr(db_obj, field, update_data[field])
            
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_by_task(
        self,
        db: Session,
        *,
        task_id: int,
        skip: int = 0,
        limit: int = 100,
        filters: Dict[str, Any] = None
    ) -> List[InventoryRecord]:
        """获取指定任务的盘点记录列表（动态合并资产）"""
        from sqlalchemy import text
        from app.models.asset import Asset
        
        # 构建基础查询：左连接所有资产与现有盘点记录
        base_query = """
        SELECT 
            COALESCE(ir.id, 0) as record_id,
            COALESCE(ir.task_id, :task_id) as task_id,
            a.id as asset_id,
            COALESCE(ir.status, 'pending') as status,
            ir.remarks,
            ir.checked_by,
            ir.checked_at,
            ir.new_name,
            ir.new_specification,
            ir.new_status,
            ir.new_custodian,
            ir.new_custodian_department,
            ir.new_user,
            ir.new_user_department,
            ir.new_company,
            ir.new_location,
            ir.new_remarks,
            ir.new_production_number,
            ir.new_price,
            ir.new_supplier,
            ir.new_manufacturer,
            ir.new_purchaser,
            COALESCE(ir.created_at, NOW()) as created_at,
            COALESCE(ir.updated_at, NOW()) as updated_at,
            a.asset_number,
            a.name,
            a.specification,
            a.status as asset_status,
            a.custodian,
            a.custodian_job_number,
            a.custodian_department,
            a.user as asset_user,
            a.user_job_number,
            a.user_department,
            a.company,
            a.category,
            a.location,
            a.purchase_date,
            a.retirement_date,
            a.price,
            a.supplier,
            a.manufacturer,
            a.purchaser,
            a.purchaser_job_number,
            a.inspector,
            a.inspector_job_number,
            a.remarks as asset_remarks,
            a.production_number
        FROM assets a
        LEFT JOIN inventory_records ir ON a.id = ir.asset_id AND ir.task_id = :task_id
        WHERE 1=1
        """
        
        # 添加过滤条件
        filter_conditions = []
        params = {"task_id": task_id}
        
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        filter_conditions.append("""
                            (a.asset_number ILIKE :keyword 
                             OR a.name ILIKE :keyword 
                             OR a.specification ILIKE :keyword 
                             OR a.company ILIKE :keyword 
                             OR a.custodian ILIKE :keyword 
                             OR a.user ILIKE :keyword)
                        """)
                        params["keyword"] = f"%{value}%"
                    elif field == "status" and value:
                        filter_conditions.append("COALESCE(ir.status, 'pending') = :status")
                        params["status"] = value
        
        if filter_conditions:
            base_query += " AND " + " AND ".join(filter_conditions)
        
        # 添加排序和分页
        base_query += " ORDER BY a.asset_number LIMIT :limit OFFSET :offset"
        params["limit"] = limit
        params["offset"] = skip
        
        # 执行查询
        result = db.execute(text(base_query), params).fetchall()
        
        # 转换为InventoryRecord对象
        records = []
        for row in result:
            # 创建Asset对象，确保所有必需字段都有值
            from datetime import datetime as dt
            current_time = dt.now()
            
            asset = Asset(
                id=row.asset_id,
                asset_number=row.asset_number,
                name=row.name,
                specification=row.specification,
                status=row.asset_status,
                custodian=row.custodian or '',
                custodian_job_number=row.custodian_job_number,
                custodian_department=row.custodian_department,
                user=row.asset_user or '',
                user_job_number=row.user_job_number,
                user_department=row.user_department,
                company=row.company,
                category=row.category,
                location=row.location,
                purchase_date=row.purchase_date or current_time,
                retirement_date=row.retirement_date,
                price=row.price,
                supplier=row.supplier,
                manufacturer=row.manufacturer,
                purchaser=row.purchaser,
                purchaser_job_number=row.purchaser_job_number,
                inspector=row.inspector or '',
                inspector_job_number=row.inspector_job_number,
                remarks=row.asset_remarks,
                production_number=row.production_number,
                created_at=current_time,  # 为虚拟Asset对象提供created_at
                updated_at=current_time   # 为虚拟Asset对象提供updated_at
            )
            
            # 创建InventoryRecord对象
            if row.record_id > 0:
                # 现有记录
                record = InventoryRecord(
                    id=row.record_id,
                    task_id=row.task_id,
                    asset_id=row.asset_id,
                    status=row.status,
                    remarks=row.remarks,
                    checked_by=row.checked_by,
                    checked_at=row.checked_at,
                    new_name=row.new_name,
                    new_specification=row.new_specification,
                    new_status=row.new_status,
                    new_custodian=row.new_custodian,
                    new_custodian_department=row.new_custodian_department,
                    new_user=row.new_user,
                    new_user_department=row.new_user_department,
                    new_company=row.new_company,
                    new_location=row.new_location,
                    new_remarks=row.new_remarks,
                    new_production_number=row.new_production_number,
                    new_price=row.new_price,
                    new_supplier=row.new_supplier,
                    new_manufacturer=row.new_manufacturer,
                    new_purchaser=row.new_purchaser,
                    created_at=row.created_at,
                    updated_at=row.updated_at
                )
            else:
                # 虚拟记录（新增资产）
                record = InventoryRecord(
                    # 不设置ID字段，让SQLAlchemy处理
                    task_id=row.task_id,
                    asset_id=row.asset_id,
                    status="pending",
                    remarks=None,
                    checked_by=None,
                    checked_at=None,
                    # 初始化所有 new_ 字段为 None
                    new_name=None,
                    new_specification=None,
                    new_status=None,
                    new_custodian=None,
                    new_custodian_department=None,
                    new_user=None,
                    new_user_department=None,
                    new_company=None,
                    new_location=None,
                    new_remarks=None,
                    new_production_number=None,
                    new_price=None,
                    new_supplier=None,
                    new_manufacturer=None,
                    new_purchaser=None,
                    created_at=current_time,
                    updated_at=current_time
                )
                # 手动设置ID为None以标识这是虚拟记录
                record.id = None
            
            # 关联Asset对象
            record.asset = asset
            records.append(record)
        
        return records

    def get_count_by_task(
        self,
        db: Session,
        *,
        task_id: int,
        filters: Dict[str, Any] = None
    ) -> int:
        """获取指定任务的盘点记录总数（动态合并资产）"""
        from sqlalchemy import text
        
        # 构建计数查询：左连接所有资产与现有盘点记录
        count_query = """
        SELECT COUNT(*)
        FROM assets a
        LEFT JOIN inventory_records ir ON a.id = ir.asset_id AND ir.task_id = :task_id
        WHERE 1=1
        """
        
        # 添加过滤条件
        filter_conditions = []
        params = {"task_id": task_id}
        
        if filters:
            for field, value in filters.items():
                if value is not None:
                    if field == "keyword":
                        filter_conditions.append("""
                            (a.asset_number ILIKE :keyword 
                             OR a.name ILIKE :keyword 
                             OR a.specification ILIKE :keyword 
                             OR a.company ILIKE :keyword 
                             OR a.custodian ILIKE :keyword 
                             OR a.user ILIKE :keyword)
                        """)
                        params["keyword"] = f"%{value}%"
                    elif field == "status" and value:
                        filter_conditions.append("COALESCE(ir.status, 'pending') = :status")
                        params["status"] = value
        
        if filter_conditions:
            count_query += " AND " + " AND ".join(filter_conditions)
        
        # 执行查询
        result = db.execute(text(count_query), params).scalar()
        return result or 0

    def update_or_create_record(
        self,
        db: Session,
        *,
        task_id: int,
        asset_id: int,
        obj_in: Union[InventoryRecordUpdate, Dict[str, Any]]
    ) -> InventoryRecord:
        """更新或创建盘点记录（处理虚拟记录）"""
        # 查找现有记录
        existing_record = db.query(self.model).filter(
            and_(self.model.task_id == task_id, self.model.asset_id == asset_id)
        ).first()
        
        if existing_record:
            # 更新现有记录
            return self.update(db, db_obj=existing_record, obj_in=obj_in)
        else:
            # 创建新记录
            create_data = obj_in.model_dump(exclude_unset=True) if isinstance(obj_in, InventoryRecordUpdate) else obj_in
            create_data.update({
                "task_id": task_id,
                "asset_id": asset_id
            })
            # 设置默认状态
            if "status" not in create_data:
                create_data["status"] = "pending"
            
            from app.schemas.inventory import InventoryRecordCreate
            create_obj = InventoryRecordCreate(**create_data)
            return self.create(db, obj_in=create_obj)

    def get_info_changes_by_task(
        self,
        db: Session,
        *,
        task_id: int
    ) -> List[InventoryRecord]:
        """获取指定任务中所有状态为信息变更的盘点记录"""
        return db.query(InventoryRecord)\
            .filter(InventoryRecord.task_id == task_id)\
            .filter(InventoryRecord.status == "info_changed")\
            .all()

    def apply_info_changes_to_assets(
        self,
        db: Session,
        *,
        task_id: int,
        selected_record_ids: List[int] = None
    ) -> dict:
        """将盘点任务中的信息变更应用到资产表"""
        from app.crud.asset import asset_crud
        
        # 获取所有信息变更记录
        query = db.query(InventoryRecord)\
            .filter(InventoryRecord.task_id == task_id)\
            .filter(InventoryRecord.status == "info_changed")
        
        # 如果指定了记录ID，则只处理指定的记录
        if selected_record_ids:
            query = query.filter(InventoryRecord.id.in_(selected_record_ids))
        
        change_records = query.all()
        
        applied_count = 0
        failed_count = 0
        failed_records = []
        
        # 字段映射关系
        field_mappings = {
            "new_name": "name",
            "new_specification": "specification", 
            "new_status": "status",
            "new_custodian": "custodian",
            "new_custodian_department": "custodian_department",
            "new_user": "user",
            "new_user_department": "user_department",
            "new_location": "location",
            "new_company": "company",
            "new_remarks": "remarks",
            "new_production_number": "production_number",
            "new_price": "price",
            "new_supplier": "supplier",
            "new_manufacturer": "manufacturer",
            "new_purchaser": "purchaser"
        }
        
        for record in change_records:
            try:
                asset_update = {}
                
                # 检查并添加所有可能的资产信息更新
                for new_field, asset_field in field_mappings.items():
                    new_value = getattr(record, new_field, None)
                    if new_value:  # 只更新有值的字段
                        asset_update[asset_field] = new_value
                
                if asset_update:
                    # 应用到资产表
                    asset_crud.update(db, db_obj=record.asset, obj_in=asset_update)
                    applied_count += 1
                
            except Exception as e:
                failed_count += 1
                failed_records.append({
                    "record_id": record.id,
                    "asset_number": record.asset.asset_number if record.asset else "未知",
                    "error": str(e)
                })
        
        return {
            "applied_count": applied_count,
            "failed_count": failed_count,
            "failed_records": failed_records
        }

inventory_task_crud = CRUDInventoryTask(InventoryTask)
inventory_record_crud = CRUDInventoryRecord(InventoryRecord) 