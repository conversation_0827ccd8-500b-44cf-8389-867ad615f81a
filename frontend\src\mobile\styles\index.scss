// 移动端样式入口文件

// 导入变量
@use './variables.scss' as *;
@use './mixins.scss' as *;

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f8fa;
}

// 移动端特定样式
.mobile-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

// 卡片样式
.mobile-card {
  margin: $padding-md;
  padding: $padding-md;
  background: white;
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-light;
}

// 列表样式
.mobile-list-item {
  padding: $padding-md;
  border-bottom: 1px solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
}

// 按钮样式
.mobile-button {
  width: 100%;
  height: 44px;
  border-radius: $border-radius-md;
  font-size: $font-size-lg;
  transition: $transition-fast;
}

// 输入框样式
.mobile-input {
  width: 100%;
  padding: $padding-md;
  border: 1px solid #ddd;
  border-radius: $border-radius-md;
  font-size: $font-size-md;
}

// 安全区域适配
.safe-area-bottom {
  padding-bottom: var(--mobile-safe-area-bottom);
}

// 响应式工具类
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mt-xs { margin-top: $padding-xs; }
.mt-sm { margin-top: $padding-sm; }
.mt-md { margin-top: $padding-md; }
.mt-lg { margin-top: $padding-lg; }

.mb-xs { margin-bottom: $padding-xs; }
.mb-sm { margin-bottom: $padding-sm; }
.mb-md { margin-bottom: $padding-md; }
.mb-lg { margin-bottom: $padding-lg; }

.p-xs { padding: $padding-xs; }
.p-sm { padding: $padding-sm; }
.p-md { padding: $padding-md; }
.p-lg { padding: $padding-lg; }

// 隐藏滚动条
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
} 