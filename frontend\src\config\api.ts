// API配置文件
// 请根据实际情况修改SERVER_IP为你的服务器IP地址

// 方式1：自动获取当前访问的IP（推荐）
const getCurrentServerIP = (): string => {
  const hostname = window.location.hostname
  // 如果是localhost或127.0.0.1，返回localhost
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:8000'
  }
  // 否则使用当前访问的IP
  return `http://${hostname}:8000`
}

// 方式2：手动指定IP地址
const MANUAL_SERVER_IP = 'http://**************:8000' // 修改为你的服务器IP

// 配置选择：true=自动获取，false=手动指定
const USE_AUTO_IP = true // 修改为true，使用自动获取的IP

export const API_BASE_URL = USE_AUTO_IP 
  ? getCurrentServerIP() + '/api/v1'
  : MANUAL_SERVER_IP + '/api/v1'

console.log('API Base URL:', API_BASE_URL) 