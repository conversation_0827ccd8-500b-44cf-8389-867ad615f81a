"""add_agent_versions_table

Revision ID: e03debbd58bb
Revises: e4679c29e0bd
Create Date: 2025-04-01 02:08:29.349802

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e03debbd58bb'
down_revision: Union[str, None] = 'e4679c29e0bd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 创建agent_versions表
    op.create_table(
        'agent_versions',
        sa.Column('id', sa.String(36), primary_key=True),
        sa.Column('version', sa.String(50), nullable=False),
        sa.Column('platform', sa.String(50), nullable=False),
        sa.Column('file_name', sa.String(255), nullable=False),
        sa.Column('file_size', sa.<PERSON>ger(), nullable=False),
        sa.Column('download_url', sa.String(255), nullable=False),
        sa.Column('release_notes', sa.Text(), nullable=True),
        sa.Column('upload_time', sa.DateTime(), nullable=False),
        sa.Column('is_current', sa.Boolean(), default=False)
    )
    
    # 创建索引
    op.create_index(
        'ix_agent_versions_platform',
        'agent_versions',
        ['platform']
    )
    
    op.create_index(
        'ix_agent_versions_version',
        'agent_versions',
        ['version']
    )


def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_agent_versions_version', 'agent_versions')
    op.drop_index('ix_agent_versions_platform', 'agent_versions')
    
    # 删除表
    op.drop_table('agent_versions')
