#!/usr/bin/env python3
"""
迁移安全检查脚本
用于检查Alembic迁移文件中是否包含危险的表删除操作
"""

import os
import re
import sys
from pathlib import Path

# 受保护的关键表列表
PROTECTED_TABLES = [
    'asset_settings',
    'assets', 
    'users',
    'permissions',
    'roles',
    'email_configs',
    'ad_config',
    'terminals'
]

def check_migration_file(file_path):
    """检查单个迁移文件是否包含危险操作"""
    dangerous_operations = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否删除受保护的表
    for table in PROTECTED_TABLES:
        # 查找 drop_table 操作
        drop_table_pattern = rf"op\.drop_table\(['\"]?{table}['\"]?\)"
        if re.search(drop_table_pattern, content):
            dangerous_operations.append(f"⚠️  DANGER: 删除受保护表 '{table}'")
            
        # 查找 drop_index 操作（通常伴随 drop_table）
        drop_index_pattern = rf"op\.drop_index.*{table}"
        if re.search(drop_index_pattern, content):
            dangerous_operations.append(f"⚠️  WARNING: 删除表 '{table}' 的索引")
    
    return dangerous_operations

def check_all_migrations():
    """检查所有迁移文件"""
    script_dir = Path(__file__).parent.parent
    versions_dir = script_dir / "alembic" / "versions"
    
    if not versions_dir.exists():
        print(f"❌ 迁移目录不存在: {versions_dir}")
        return False
    
    print("🔍 检查迁移文件安全性...")
    print(f"📁 迁移目录: {versions_dir}")
    print(f"🛡️  受保护表: {', '.join(PROTECTED_TABLES)}\n")
    
    has_issues = False
    
    for migration_file in versions_dir.glob("*.py"):
        if migration_file.name.startswith("__"):
            continue
            
        dangerous_ops = check_migration_file(migration_file)
        
        if dangerous_ops:
            has_issues = True
            print(f"🚨 文件: {migration_file.name}")
            for op in dangerous_ops:
                print(f"   {op}")
            print()
    
    if not has_issues:
        print("✅ 所有迁移文件安全检查通过！")
        return True
    else:
        print("❌ 发现潜在危险操作，请仔细检查迁移文件！")
        return False

if __name__ == "__main__":
    success = check_all_migrations()
    sys.exit(0 if success else 1) 