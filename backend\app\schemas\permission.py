from typing import Optional, List
from pydantic import BaseModel

# 权限基类
class PermissionBase(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    module: str

# 创建权限
class PermissionCreate(PermissionBase):
    pass

# 更新权限
class PermissionUpdate(PermissionBase):
    code: Optional[str] = None
    name: Optional[str] = None
    module: Optional[str] = None

# 数据库中的权限基类
class PermissionInDBBase(PermissionBase):
    id: int

    class Config:
        from_attributes = True

# API返回的权限
class Permission(PermissionInDBBase):
    pass

# 权限详情（包含角色信息）
class PermissionDetail(Permission):
    roles: List["RoleBasic"] = []

# 模块权限分组
class ModulePermissions(BaseModel):
    module: str
    permissions: List[Permission]

from app.schemas.role import RoleBasic
PermissionDetail.model_rebuild() 