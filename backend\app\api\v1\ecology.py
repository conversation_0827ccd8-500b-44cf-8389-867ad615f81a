from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
import pyodbc
from app.api.deps import get_db, check_permissions
from app.utils import get_current_user
from app import models
from pydantic import BaseModel
import os
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

router = APIRouter()

# 定义数据模型
class EcologyUser(BaseModel):
    DeptID: int
    DeptName: str
    DeptHierarchy: str
    Level: int
    DeptPath: str
    CompanyID: Optional[int] = None
    CompanyName: Optional[str] = None
    UserID: Optional[int] = None
    UserName: Optional[str] = None
    JobNumber: Optional[str] = None
    Mobile: Optional[str] = None
    Email: Optional[str] = None
    JobTitle: Optional[int] = None
    JobTitleName: Optional[str] = None
    Gender: Optional[str] = None
    Status: Optional[str] = None

# 获取泛微数据库连接
def get_ecology_db_connection():
    server = os.getenv("ECOLOGY_DB_SERVER")
    database = os.getenv("ECOLOGY_DB_NAME")
    username = os.getenv("ECOLOGY_DB_USER")
    password = os.getenv("ECOLOGY_DB_PASSWORD")

    connection_string = f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"

    try:
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"无法连接到泛微数据库: {str(e)}")

@router.get("/users", response_model=List[EcologyUser])
def get_ecology_users(
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    获取泛微系统中的部门和用户信息
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        # 使用SQL文件中的查询
        query = """
        WITH DepartmentCTE AS (
            -- 根部门
            SELECT
                id AS DeptID,
                departmentname AS DeptName,
                CAST(departmentname AS NVARCHAR(4000)) AS DeptHierarchy,
                supdepid AS ParentDeptID,
                1 AS Level,
                CAST(id AS VARCHAR(100)) AS DeptPath,
                subcompanyid1 AS CompanyID
            FROM hrmdepartment
            WHERE supdepid = 0  -- 根部门条件

            UNION ALL

            -- 子部门
            SELECT
                d.id,
                d.departmentname,
                CAST(c.DeptHierarchy + N' > ' + d.departmentname AS NVARCHAR(4000)),
                d.supdepid,
                c.Level + 1,
                CAST(c.DeptPath + ',' + CAST(d.id AS VARCHAR(20)) AS VARCHAR(100)),
                d.subcompanyid1
            FROM hrmdepartment d
            INNER JOIN DepartmentCTE c ON d.supdepid = c.DeptID
            WHERE d.canceled = '0' OR d.canceled IS NULL  -- 只查询未封存的部门
        )
        SELECT
            d.DeptID,
            d.DeptName,
            d.DeptHierarchy,
            d.Level,
            d.DeptPath,
            s.id AS CompanyID,
            s.subcompanyname AS CompanyName,
            h.id AS UserID,
            h.lastname AS UserName,
            h.workcode AS JobNumber,
            h.mobile AS Mobile,
            h.email AS Email,
            h.jobtitle AS JobTitle,
            j.jobtitlename AS JobTitleName,
            h.sex AS Gender,
            CASE h.status
                WHEN 0 THEN N'试用'
                WHEN 1 THEN N'正式'
                WHEN 2 THEN N'临时'
                WHEN 3 THEN N'试用延期'
                WHEN 4 THEN N'解聘'
                WHEN 5 THEN N'离职'
                WHEN 6 THEN N'退休'
                WHEN 7 THEN N'无效'
                ELSE N'未知'
            END AS Status
        FROM DepartmentCTE d
        LEFT JOIN hrmsubcompany s ON d.CompanyID = s.id
        LEFT JOIN hrmresource h ON d.DeptID = h.departmentid
        LEFT JOIN hrmjobtitles j ON h.jobtitle = j.id
        ORDER BY s.subcompanyname, d.DeptPath, d.DeptName, h.lastname;
        """

        cursor.execute(query)

        results = []
        for row in cursor.fetchall():
            user = EcologyUser(
                DeptID=row.DeptID,
                DeptName=row.DeptName,
                DeptHierarchy=row.DeptHierarchy,
                Level=row.Level,
                DeptPath=row.DeptPath,
                CompanyID=row.CompanyID,
                CompanyName=row.CompanyName,
                UserID=row.UserID,
                UserName=row.UserName,
                JobNumber=row.JobNumber,
                Mobile=row.Mobile,
                Email=row.Email,
                JobTitle=row.JobTitle,
                JobTitleName=row.JobTitleName,
                Gender=row.Gender,
                Status=row.Status
            )
            results.append(user)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取泛微用户数据失败: {str(e)}")

@router.get("/companies", response_model=List[dict])
def get_ecology_companies(
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    获取泛微系统中的公司列表
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT DISTINCT
            s.id AS CompanyID,
            s.subcompanyname AS CompanyName
        FROM hrmsubcompany s
        WHERE s.id IS NOT NULL AND s.subcompanyname IS NOT NULL
        ORDER BY s.subcompanyname
        """

        cursor.execute(query)

        results = []
        for row in cursor.fetchall():
            company = {
                "id": row.CompanyID,
                "name": row.CompanyName
            }
            results.append(company)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取泛微公司数据失败: {str(e)}")

@router.get("/departments", response_model=List[dict])
def get_ecology_departments(
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    获取泛微系统中的部门列表
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT DISTINCT
            d.id AS DeptID,
            d.departmentname AS DeptName,
            d.subcompanyid1 AS CompanyID
        FROM hrmdepartment d
        WHERE d.id IS NOT NULL AND d.departmentname IS NOT NULL
        AND (d.canceled = '0' OR d.canceled IS NULL)  -- 只查询未封存的部门
        ORDER BY d.departmentname
        """

        cursor.execute(query)

        results = []
        for row in cursor.fetchall():
            dept = {
                "id": row.DeptID,
                "name": row.DeptName,
                "company_id": row.CompanyID
            }
            results.append(dept)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取泛微部门数据失败: {str(e)}")

@router.get("/users/by-company/{company_id}", response_model=List[EcologyUser])
def get_ecology_users_by_company(
    company_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    按公司ID获取泛微系统中的用户信息
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        # 使用与get_ecology_users相同的查询，但添加公司ID过滤条件
        query = """
        WITH DepartmentCTE AS (
            -- 根部门
            SELECT
                id AS DeptID,
                departmentname AS DeptName,
                CAST(departmentname AS NVARCHAR(4000)) AS DeptHierarchy,
                supdepid AS ParentDeptID,
                1 AS Level,
                CAST(id AS VARCHAR(100)) AS DeptPath,
                subcompanyid1 AS CompanyID
            FROM hrmdepartment
            WHERE supdepid = 0  -- 根部门条件

            UNION ALL

            -- 子部门
            SELECT
                d.id,
                d.departmentname,
                CAST(c.DeptHierarchy + N' > ' + d.departmentname AS NVARCHAR(4000)),
                d.supdepid,
                c.Level + 1,
                CAST(c.DeptPath + ',' + CAST(d.id AS VARCHAR(20)) AS VARCHAR(100)),
                d.subcompanyid1
            FROM hrmdepartment d
            INNER JOIN DepartmentCTE c ON d.supdepid = c.DeptID
            WHERE d.canceled = '0' OR d.canceled IS NULL  -- 只查询未封存的部门
        )
        SELECT
            d.DeptID,
            d.DeptName,
            d.DeptHierarchy,
            d.Level,
            d.DeptPath,
            s.id AS CompanyID,
            s.subcompanyname AS CompanyName,
            h.id AS UserID,
            h.lastname AS UserName,
            h.workcode AS JobNumber,
            h.mobile AS Mobile,
            h.email AS Email,
            h.jobtitle AS JobTitle,
            j.jobtitlename AS JobTitleName,
            h.sex AS Gender,
            CASE h.status
                WHEN 0 THEN N'试用'
                WHEN 1 THEN N'正式'
                WHEN 2 THEN N'临时'
                WHEN 3 THEN N'试用延期'
                WHEN 4 THEN N'解聘'
                WHEN 5 THEN N'离职'
                WHEN 6 THEN N'退休'
                WHEN 7 THEN N'无效'
                ELSE N'未知'
            END AS Status
        FROM DepartmentCTE d
        LEFT JOIN hrmsubcompany s ON d.CompanyID = s.id
        LEFT JOIN hrmresource h ON d.DeptID = h.departmentid
        LEFT JOIN hrmjobtitles j ON h.jobtitle = j.id
        WHERE s.id = ?
        ORDER BY s.subcompanyname, d.DeptPath, d.DeptName, h.lastname;
        """

        cursor.execute(query, (company_id,))

        results = []
        for row in cursor.fetchall():
            user = EcologyUser(
                DeptID=row.DeptID,
                DeptName=row.DeptName,
                DeptHierarchy=row.DeptHierarchy,
                Level=row.Level,
                DeptPath=row.DeptPath,
                CompanyID=row.CompanyID,
                CompanyName=row.CompanyName,
                UserID=row.UserID,
                UserName=row.UserName,
                JobNumber=row.JobNumber,
                Mobile=row.Mobile,
                Email=row.Email,
                JobTitle=row.JobTitle,
                JobTitleName=row.JobTitleName,
                Gender=row.Gender,
                Status=row.Status
            )
            results.append(user)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取泛微用户数据失败: {str(e)}")

@router.get("/users/by-department/{dept_id}", response_model=List[EcologyUser])
def get_ecology_users_by_department(
    dept_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    按部门ID获取泛微系统中的用户信息
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        # 使用与get_ecology_users相同的查询，但添加部门ID过滤条件
        query = """
        WITH DepartmentCTE AS (
            -- 指定部门及其上级路径
            SELECT
                id AS DeptID,
                departmentname AS DeptName,
                CAST(departmentname AS NVARCHAR(4000)) AS DeptHierarchy,
                supdepid AS ParentDeptID,
                1 AS Level,
                CAST(id AS VARCHAR(100)) AS DeptPath,
                subcompanyid1 AS CompanyID
            FROM hrmdepartment
            WHERE id = ?
        )
        SELECT
            d.DeptID,
            d.DeptName,
            d.DeptHierarchy,
            d.Level,
            d.DeptPath,
            s.id AS CompanyID,
            s.subcompanyname AS CompanyName,
            h.id AS UserID,
            h.lastname AS UserName,
            h.workcode AS JobNumber,
            h.mobile AS Mobile,
            h.email AS Email,
            h.jobtitle AS JobTitle,
            j.jobtitlename AS JobTitleName,
            h.sex AS Gender,
            CASE h.status
                WHEN 0 THEN N'试用'
                WHEN 1 THEN N'正式'
                WHEN 2 THEN N'临时'
                WHEN 3 THEN N'试用延期'
                WHEN 4 THEN N'解聘'
                WHEN 5 THEN N'离职'
                WHEN 6 THEN N'退休'
                WHEN 7 THEN N'无效'
                ELSE N'未知'
            END AS Status
        FROM DepartmentCTE d
        LEFT JOIN hrmsubcompany s ON d.CompanyID = s.id
        LEFT JOIN hrmresource h ON d.DeptID = h.departmentid
        LEFT JOIN hrmjobtitles j ON h.jobtitle = j.id
        ORDER BY h.lastname;
        """

        cursor.execute(query, (dept_id,))

        results = []
        for row in cursor.fetchall():
            user = EcologyUser(
                DeptID=row.DeptID,
                DeptName=row.DeptName,
                DeptHierarchy=row.DeptHierarchy,
                Level=row.Level,
                DeptPath=row.DeptPath,
                CompanyID=row.CompanyID,
                CompanyName=row.CompanyName,
                UserID=row.UserID,
                UserName=row.UserName,
                JobNumber=row.JobNumber,
                Mobile=row.Mobile,
                Email=row.Email,
                JobTitle=row.JobTitle,
                JobTitleName=row.JobTitleName,
                Gender=row.Gender,
                Status=row.Status
            )
            results.append(user)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取泛微用户数据失败: {str(e)}")

@router.get("/departments/by-company/{company_id}", response_model=List[dict])
def get_departments_by_company(
    company_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    按公司ID获取该公司下的所有部门
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            d.id AS DeptID,
            d.departmentname AS DeptName,
            d.subcompanyid1 AS CompanyID,
            d.supdepid AS ParentDeptID
        FROM hrmdepartment d
        WHERE d.subcompanyid1 = ?
        AND (d.canceled = '0' OR d.canceled IS NULL)  -- 只查询未封存的部门
        ORDER BY d.departmentname
        """

        cursor.execute(query, company_id)

        results = []
        for row in cursor.fetchall():
            dept = {
                "ID": row.DeptID,
                "DeptName": row.DeptName,
                "CompanyID": row.CompanyID,
                "ParentID": row.ParentDeptID
            }
            results.append(dept)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取公司部门数据失败: {str(e)}")

@router.get("/departments/by-parent/{parent_id}", response_model=List[dict])
def get_departments_by_parent(
    parent_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    按父部门ID获取所有子部门
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            d.id AS DeptID,
            d.departmentname AS DeptName,
            d.subcompanyid1 AS CompanyID,
            d.supdepid AS ParentDeptID
        FROM hrmdepartment d
        WHERE d.supdepid = ?
        AND (d.canceled = '0' OR d.canceled IS NULL)  -- 只查询未封存的部门
        ORDER BY d.departmentname
        """

        cursor.execute(query, parent_id)

        results = []
        for row in cursor.fetchall():
            dept = {
                "ID": row.DeptID,
                "DeptName": row.DeptName,
                "CompanyID": row.CompanyID,
                "ParentID": row.ParentDeptID
            }
            results.append(dept)

        cursor.close()
        conn.close()

        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取子部门数据失败: {str(e)}")

@router.get("/departments/by-parent-recursive/{parent_id}", response_model=List[dict])
def get_departments_by_parent_recursive(
    parent_id: int,
    visited_ids=None,
    depth=0,
    max_depth=20,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    递归获取所有子部门，包括子部门的子部门

    添加了防止循环引用的逻辑和最大递归深度限制
    """
    try:
        # 初始化已访问部门ID集合，防止循环引用
        if visited_ids is None:
            visited_ids = set()

        # 防止循环引用
        if parent_id in visited_ids:
            return []

        # 限制最大递归深度，防止栈溢出
        if depth > max_depth:
            logger = logging.getLogger(__name__)
            logger.warning(f"达到最大递归深度{max_depth}，停止获取部门ID {parent_id} 的子部门")
            return []

        # 记录当前处理的部门ID
        visited_ids.add(parent_id)

        # 获取直接子部门
        all_departments = get_departments_by_parent(parent_id)

        # 为每个子部门递归获取其子部门
        all_child_departments = []
        for dept in all_departments:
            dept_id = dept["ID"]
            # 递归获取子部门
            child_departments = get_departments_by_parent_recursive(
                dept_id,
                visited_ids.copy(),  # 传递副本，避免影响其他分支
                depth + 1,
                max_depth
            )
            all_child_departments.extend(child_departments)

        # 合并所有子部门，避免重复
        for child_dept in all_child_departments:
            if not any(d["ID"] == child_dept["ID"] for d in all_departments):
                all_departments.append(child_dept)

        return all_departments
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"递归获取子部门数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"递归获取子部门数据失败: {str(e)}")

@router.get("/users/by-department-tree/{dept_id}", response_model=List[EcologyUser])
def get_ecology_users_by_department_tree(
    dept_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    获取部门及其所有子部门的用户
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"开始获取部门ID {dept_id} 及其所有子部门的用户")

        # 获取所有相关部门ID（当前部门和所有子部门）
        all_departments = get_departments_by_parent_recursive(dept_id)
        all_dept_ids = [dept["ID"] for dept in all_departments]

        # 添加当前部门ID（如果不在列表中）
        if dept_id not in all_dept_ids:
            all_dept_ids.append(dept_id)

        logger.info(f"找到 {len(all_dept_ids)} 个相关部门ID")

        # 获取所有部门的用户
        all_users = []
        user_ids_set = set()  # 用于快速查找用户ID是否存在

        for current_dept_id in all_dept_ids:
            try:
                users = get_ecology_users_by_department(current_dept_id)
                dept_user_count = 0

                # 使用集合来避免重复，提高性能
                for user in users:
                    if user.UserID and user.UserID not in user_ids_set:
                        user_ids_set.add(user.UserID)
                        all_users.append(user)
                        dept_user_count += 1

                logger.info(f"从部门ID {current_dept_id} 获取到 {len(users)} 个用户，添加了 {dept_user_count} 个新用户")
            except Exception as dept_error:
                logger.warning(f"获取部门ID {current_dept_id} 的用户失败: {str(dept_error)}")
                continue

        logger.info(f"总共收集到 {len(all_users)} 个不重复用户")
        return all_users
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取部门树用户数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取部门树用户数据失败: {str(e)}")

@router.get("/departments/{dept_id}", response_model=dict)
def get_department_by_id(
    dept_id: int,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ecology:view"]))
):
    """
    按部门ID获取部门详情
    """
    try:
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        query = """
        SELECT
            d.id AS DeptID,
            d.departmentname AS DeptName,
            d.subcompanyid1 AS CompanyID,
            d.supdepid AS ParentDeptID,
            s.subcompanyname AS CompanyName
        FROM hrmdepartment d
        LEFT JOIN hrmsubcompany s ON d.subcompanyid1 = s.id
        WHERE d.id = ?
        AND (d.canceled = '0' OR d.canceled IS NULL)  -- 只查询未封存的部门
        """

        cursor.execute(query, dept_id)
        row = cursor.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail=f"未找到ID为{dept_id}的部门")

        dept = {
            "ID": row.DeptID,
            "DeptName": row.DeptName,
            "CompanyID": row.CompanyID,
            "ParentID": row.ParentDeptID,
            "CompanyName": row.CompanyName
        }

        cursor.close()
        conn.close()

        return dept
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取部门详情失败: {str(e)}")