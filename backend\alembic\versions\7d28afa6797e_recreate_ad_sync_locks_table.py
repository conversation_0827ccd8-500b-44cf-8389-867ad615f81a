"""recreate_ad_sync_locks_table

Revision ID: 7d28afa6797e
Revises: 51893a9cc1ec
Create Date: 2025-06-23 11:53:53.087881

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7d28afa6797e'
down_revision: Union[str, None] = '51893a9cc1ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.String(), nullable=False),
    sa.Column('is_locked', sa.<PERSON>(), nullable=True),
    sa.Column('locked_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('locked_by', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index(op.f('ix_ad_sync_locks_lock_name'), 'ad_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ad_sync_locks_lock_name'), table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    # ### end Alembic commands ###
