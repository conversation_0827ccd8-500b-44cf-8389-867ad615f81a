from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from typing import Optional
from .. import models, schemas
from ..utils.ad_client import test_ad_connection
import logging

logger = logging.getLogger(__name__)

async def get_ad_config(db: Session) -> models.ADConfig:
    """获取AD配置"""
    config = db.query(models.ADConfig).first()
    if not config:
        # 如果没有配置，返回一个空的配置对象
        return models.ADConfig(
            id=0,
            server="",
            domain="",
            username="",
            password="",
            search_base="",
            use_ssl=False,
            port=389
        )
    return config

async def update_ad_config(db: Session, config: schemas.ADConfigCreate) -> models.ADConfig:
    """更新AD配置"""
    try:
        # 更新配置
        db_config = await get_ad_config(db)
        if db_config.id == 0:  # 新配置
            db_config = models.ADConfig(
                server=config.server,
                domain=config.domain,
                username=config.username,
                password=config.password,
                search_base=config.search_base,
                use_ssl=config.use_ssl,
                port=config.port
            )
            db.add(db_config)
        else:  # 更新现有配置
            db_config.server = config.server
            db_config.domain = config.domain
            db_config.username = config.username
            if config.password:  # 只在提供密码时更新密码
                db_config.password = config.password
            db_config.search_base = config.search_base
            db_config.use_ssl = config.use_ssl
            db_config.port = config.port
        
        db.commit()
        db.refresh(db_config)
        return db_config
    except Exception as e:
        logger.error(f"更新AD配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

async def test_config(config: schemas.ADConfigCreate) -> dict:
    """测试AD配置"""
    try:
        logger.info("开始测试AD配置")
        if config:
            logger.info(f"使用提供的配置进行测试: server={config.server}")
        else:
            logger.info("使用数据库中的配置进行测试")
            
        result = await test_ad_connection(config.model_dump() if config else None)
        logger.info(f"测试结果: {result}")
        
        if result:
            return {
                "message": "连接测试成功",
                "status": True,
                "details": None
            }
        return {
            "message": "连接测试失败",
            "status": False,
            "details": None
        }
    except Exception as e:
        logger.error(f"测试AD配置时发生错误: {str(e)}", exc_info=True)
        return {
            "message": f"连接测试失败: {str(e)}",
            "status": False,
            "details": {
                "error_type": type(e).__name__,
                "error_message": str(e)
            }
        } 