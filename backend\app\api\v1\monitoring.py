from fastapi import APIRouter, Depends, HTTPException, Query
from app.api.deps import get_db, check_permissions
from app.utils.redis_cache import RedisCache
from app.core.cache_config import cache_config_manager, CacheDataType
from app.middleware.redis_cache_logging import cache_metrics
from datetime import datetime
import os

router = APIRouter()
redis_cache = RedisCache()

@router.get("/redis-stats")
def get_redis_stats(
    current_user = Depends(check_permissions(["system:monitoring:view"]))
):
    """
    获取Redis缓存统计信息
    
    返回Redis服务器的基本监控信息，包括连接客户端数、内存使用量、键总数和运行时间等
    """
    try:
        # 获取Redis服务器监控信息
        metrics = redis_cache.get_metrics()
        
        # 将运行时间转换为可读格式
        uptime_seconds = metrics.get("uptime_in_seconds", 0)
        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        minutes = (uptime_seconds % 3600) // 60
        seconds = uptime_seconds % 60
        
        metrics["uptime_human"] = f"{days}天 {hours}小时 {minutes}分钟 {seconds}秒"
        
        # 获取缓存操作统计信息
        cache_stats = redis_cache.get_cache_stats()
        
        # 格式化缓存命中率
        if "hit_ratio" in cache_stats:
            cache_stats["hit_ratio_percent"] = f"{cache_stats['hit_ratio'] * 100:.2f}%"
        
        # 格式化平均操作时间
        if "avg_operation_time" in cache_stats:
            cache_stats["avg_operation_time_ms"] = f"{cache_stats['avg_operation_time'] * 1000:.2f}ms"
        
        return {
            "status": "success",
            "data": {
                "server": metrics,
                "operations": cache_stats
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Redis统计信息失败: {str(e)}")

@router.get("/request-cache-stats")
def get_request_cache_stats(
    current_user = Depends(check_permissions(["system:monitoring:view"]))
):
    """
    获取请求缓存统计信息
    
    返回API请求的缓存统计信息，包括请求总数、命中次数、未命中次数、命中率等
    """
    try:
        # 获取请求缓存统计信息
        stats = cache_metrics.get_metrics()
        
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取请求缓存统计信息失败: {str(e)}")

@router.post("/reset-request-cache-stats")
def reset_request_cache_stats(
    current_user = Depends(check_permissions(["system:monitoring:manage"]))
):
    """重置请求缓存统计信息"""
    try:
        cache_metrics.reset()
        return {
            "status": "success",
            "message": "已重置请求缓存统计信息"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置请求缓存统计信息失败: {str(e)}")

@router.post("/redis-clear")
def clear_redis_cache(
    pattern: str = "*",
    current_user = Depends(check_permissions(["system:monitoring:manage"]))
):
    """
    清除Redis缓存
    
    - **pattern**: 要清除的键模式，默认为"*"（所有键）
    """
    try:
        if pattern == "*":
            result = redis_cache.clear_all()
            message = "已清除所有Redis缓存"
        else:
            count = redis_cache.clear_pattern(pattern)
            result = count > 0
            message = f"已清除 {count} 个匹配 '{pattern}' 的Redis缓存键"
        
        return {
            "status": "success" if result else "warning",
            "message": message
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除Redis缓存失败: {str(e)}")
        
@router.post("/redis-clear-request-cache")
def clear_request_cache(
    current_user = Depends(check_permissions(["system:monitoring:manage"]))
):
    """清除所有请求缓存"""
    try:
        count = redis_cache.clear_pattern("request_cache:*")
        return {
            "status": "success",
            "message": f"已清除 {count} 个请求缓存"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清除请求缓存失败: {str(e)}")

@router.post("/redis-reset-stats")
def reset_redis_stats(
    current_user = Depends(check_permissions(["system:monitoring:manage"]))
):
    """重置Redis缓存统计信息"""
    try:
        redis_cache.reset_stats()
        return {
            "status": "success",
            "message": "已重置Redis缓存统计信息"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置Redis缓存统计信息失败: {str(e)}")

@router.get("/redis-logs")
def get_redis_logs(
    date: str = None,
    lines: int = Query(100, ge=1, le=1000),
    current_user = Depends(check_permissions(["system:monitoring:view"]))
):
    """
    获取Redis日志
    
    - **date**: 日期格式为YYYYMMDD，默认为当天
    - **lines**: 返回的日志行数，默认100行，最多1000行
    """
    try:
        # 如果未指定日期，使用当天日期
        if not date:
            date = datetime.now().strftime('%Y%m%d')
        
        log_file = f"logs/redis_{date}.log"
        
        # 检查日志文件是否存在
        if not os.path.exists(log_file):
            return {
                "status": "warning",
                "message": f"日期 {date} 的Redis日志文件不存在",
                "data": []
            }
        
        # 读取日志文件最后N行
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            total_lines = len(all_lines)
            log_lines = all_lines[-lines:] if lines < total_lines else all_lines
        
        return {
            "status": "success",
            "message": f"成功获取 {len(log_lines)} 行日志",
            "data": {
                "total_lines": total_lines,
                "returned_lines": len(log_lines),
                "logs": log_lines
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取Redis日志失败: {str(e)}")

@router.get("/cache-config")
def get_cache_config(
    current_user = Depends(check_permissions(["system:monitoring:view"]))
):
    """获取缓存配置信息"""
    try:
        configs = cache_config_manager.get_all_configs()
        
        # 转换为可序列化的格式
        serializable_configs = {}
        for key, config in configs.items():
            serializable_configs[key] = {
                "ttl": config.ttl,
                "data_type": config.data_type.value if hasattr(config.data_type, 'value') else str(config.data_type),
                "strategy": config.strategy.value if hasattr(config.strategy, 'value') else str(config.strategy),
                "auto_refresh": config.auto_refresh,
                "dependency_patterns": config.dependency_patterns,
                "description": config.description
            }
        
        return {
            "status": "success",
            "data": serializable_configs
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存配置失败: {str(e)}")

@router.get("/cache-analysis")
def get_cache_analysis(
    pattern: str = "request_cache:*",
    current_user = Depends(check_permissions(["system:monitoring:view"]))
):
    """获取缓存分析信息"""
    try:
        cache_info = redis_cache.get_cache_info(pattern)
        
        # 增强分析信息
        analysis = {
            "basic_info": cache_info,
            "recommendations": [],
            "hot_prefixes": [],
            "cache_efficiency": "unknown"
        }
        
        # 分析缓存效率
        total_keys = cache_info.get("total_keys", 0)
        keys_by_prefix = cache_info.get("keys_by_prefix", {})
        
        if total_keys > 0:
            # 找出热点前缀（键数量最多的前5个）
            sorted_prefixes = sorted(keys_by_prefix.items(), key=lambda x: x[1], reverse=True)
            analysis["hot_prefixes"] = sorted_prefixes[:5]
            
            # 分析缓存分布
            if total_keys < 100:
                analysis["cache_efficiency"] = "low_usage"
                analysis["recommendations"].append("缓存使用率较低，可能需要检查缓存策略")
            elif total_keys > 10000:
                analysis["cache_efficiency"] = "high_usage"
                analysis["recommendations"].append("缓存使用率较高，建议监控内存使用情况")
            else:
                analysis["cache_efficiency"] = "moderate"
                analysis["recommendations"].append("缓存使用率适中")
        
        # 分析TTL分布（需要检查键的剩余TTL）
        if pattern == "request_cache:*" and total_keys > 0:
            sample_keys = list(keys_by_prefix.keys())[:10]  # 采样分析
            ttl_analysis = {}
            
            for prefix in sample_keys:
                keys = redis_cache._redis.keys(f"{prefix}:*")[:5]  # 每个前缀取5个键样本
                ttl_values = []
                
                for key in keys:
                    ttl = redis_cache.get_ttl(key)
                    if ttl > 0:
                        ttl_values.append(ttl)
                
                if ttl_values:
                    avg_ttl = sum(ttl_values) / len(ttl_values)
                    ttl_analysis[prefix] = {
                        "avg_ttl": round(avg_ttl, 2),
                        "sample_size": len(ttl_values)
                    }
            
            analysis["ttl_analysis"] = ttl_analysis
        
        return {
            "status": "success", 
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存分析失败: {str(e)}")

@router.post("/cache-warmup")
def cache_warmup(
    business_keys: list = None,
    current_user = Depends(check_permissions(["system:monitoring:manage"]))
):
    """缓存预热（预留接口，后续可扩展具体的预热逻辑）"""
    try:
        if not business_keys:
            business_keys = ["users", "system_config", "asset_types"]
        
        warmed_keys = []
        for business_key in business_keys:
            # 这里可以添加具体的预热逻辑
            # 例如：提前加载用户列表、系统配置等常用数据
            warmed_keys.append(business_key)
        
        return {
            "status": "success",
            "message": f"缓存预热完成，预热业务键: {warmed_keys}",
            "warmed_keys": warmed_keys
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存预热失败: {str(e)}") 