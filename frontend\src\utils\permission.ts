import { useUserStore } from '../stores/user'

/**
 * 检查是否拥有某个权限
 * @param permission 需要检查的权限代码
 * @returns 是否拥有权限
 */
export function hasPermission(permission: string): boolean {
  const userStore = useUserStore()
  // 超级管理员拥有所有权限
  if (userStore.isSuperUser) {
    return true
  }
  // 检查用户权限列表中是否包含所需权限
  return userStore.permissions.includes(permission)
}

/**
 * 检查是否拥有多个权限中的任意一个
 * @param permissions 需要检查的权限代码列表
 * @returns 是否拥有至少一个权限
 */
export function hasAnyPermission(permissions: string[]): boolean {
  const userStore = useUserStore()
  // 超级管理员拥有所有权限
  if (userStore.isSuperUser) {
    return true
  }
  // 检查用户权限列表中是否包含所需权限中的任意一个
  return permissions.some(p => userStore.permissions.includes(p))
}

/**
 * 检查是否拥有所有列出的权限
 * @param permissions 需要检查的权限代码列表
 * @returns 是否拥有所有权限
 */
export function hasAllPermissions(permissions: string[]): boolean {
  const userStore = useUserStore()
  // 超级管理员拥有所有权限
  if (userStore.isSuperUser) {
    return true
  }
  // 检查用户权限列表中是否包含所有所需权限
  return permissions.every(p => userStore.permissions.includes(p))
} 