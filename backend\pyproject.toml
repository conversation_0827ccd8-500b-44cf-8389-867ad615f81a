[project]
name = "ops-platform-backend"
version = "1.0.2"
description = "OPS Platform Backend API Service"
readme = "README_permissions.md"
requires-python = ">=3.8"
authors = [
    {name = "OPS Platform Team"}
]

dependencies = [
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.33.0",
    "python-jose[cryptography]>=3.4.0",
    "bcrypt>=4.0.1",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.40",
    "alembic>=1.13.0",
    "psycopg2-binary>=2.9.9",
    "python-dotenv==1.0.0",
    "ldap3==2.9.1",
    "pydantic-settings>=2.8.0",
    "pyodbc>=5.2.0",
    "grpcio>=1.70.0",
    "grpcio-tools>=1.70.0",
    "protobuf>=5.26.0",
    "pywin32==310",
    "wmi==1.5.1",
    "psutil>=7.0.0",
    "pywin32-ctypes==0.2.3",
    "redis>=6.0.0",
    "email-validator>=2.2.0",
    "pandas>=2.0.3",
    "httpx>=0.28.1",
    "openpyxl>=3.1.5",
    "xlsxwriter>=3.2.3",
    "watchfiles>=0.24.0",
    "requests>=2.32.4",
    "pyinstaller>=6.14.1",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-cov",
    "black",
    "isort",
    "flake8"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=22.0",
    "isort>=5.0",
    "flake8>=4.0"
]

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88 
