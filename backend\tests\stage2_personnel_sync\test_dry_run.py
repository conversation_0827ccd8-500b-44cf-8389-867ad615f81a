"""
测试试运行同步功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_dry_run():
    """测试试运行同步"""
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(5)
    
    token = login()
    if not token:
        print("登录失败")
        return
    
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试试运行同步 ===")
    sync_data = {
        "full_sync": True,
        "dry_run": True
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/personnel-email-sync/sync/trigger", 
                           headers=headers, json=sync_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"试运行结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        print("✓ 试运行同步测试通过")
    else:
        print(f"错误: {response.text}")
        print("✗ 试运行同步测试失败")

if __name__ == "__main__":
    test_dry_run()
