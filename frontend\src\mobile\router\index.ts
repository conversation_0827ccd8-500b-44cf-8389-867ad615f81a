import { RouteRecordRaw } from 'vue-router'

export const mobileRoutes: RouteRecordRaw[] = [
  // 移动端登录页面 (独立路由，不需要布局)
  {
    path: '/m/login',
    name: 'MobileLogin',
    component: () => import('@mobile/views/auth/Login.vue'),
    meta: { 
      title: '登录',
      requiresAuth: false,
      platform: 'mobile',
      hideTabbar: true
    }
  },
  {
    path: '/m',
    component: () => import('@mobile/layout/MobileLayout.vue'),
    meta: { 
      title: '移动端',
      requiresAuth: true,
      platform: 'mobile'
    },
    children: [
      // 默认重定向到应用中心
      {
        path: '',
        redirect: 'apps'
      },
      // 仪表板
      {
        path: 'dashboard',
        name: 'MobileDashboard',
        component: () => import('@mobile/views/dashboard/index.vue'),
        meta: { 
          title: '仪表板',
          icon: 'chart-trending-o'
        }
      },
      
      // 应用中心
      {
        path: 'apps',
        name: 'MobileApps',
        component: () => import('@mobile/views/apps/index.vue'),
        meta: { 
          title: '应用',
          icon: 'apps-o'
        }
      },
      
      // AD管理
      {
        path: 'ad',
        name: 'MobileAD',
        component: () => import('@mobile/views/ad/index.vue'),
        meta: { 
          title: 'AD管理',
          permission: 'ad:view'
        }
      },
      {
        path: 'ad/config',
        name: 'MobileADConfig',
        component: () => import('@mobile/views/ad/ADConfig.vue'),
        meta: { 
          title: 'AD配置',
          permission: 'ad:config',
          hideGlobalHeader: true
        }
      },
      {
        path: 'ad/sync',
        name: 'MobileADSync',
        component: () => import('@mobile/views/ad/ADSync.vue'),
        meta: { 
          title: 'AD同步',
          permission: 'ad:sync',
          hideGlobalHeader: true
        }
      },
      
      // 邮箱管理
      {
        path: 'email',
        name: 'MobileEmail',
        component: () => import('@mobile/views/email/index.vue'),
        meta: { 
          title: '邮箱管理',
          permission: 'email:view'
        }
      },
      {
        path: 'email/config',
        name: 'MobileEmailConfig',
        component: () => import('@mobile/views/email/EmailConfig.vue'),
        meta: { 
          title: '邮箱配置',
          permission: 'email:config',
          hideGlobalHeader: true
        }
      },
      {
        path: 'email/members',
        name: 'MobileEmailMembers',
        component: () => import('@mobile/views/email/EmailMembers.vue'),
        meta: { 
          title: '邮箱成员',
          permission: 'email:read',
          hideGlobalHeader: true
        }
      },
      
      // 资产管理
      {
        path: 'asset',
        name: 'MobileAsset',
        component: () => import('@mobile/views/asset/index.vue'),
        meta: { 
          title: '资产管理',
          permission: 'asset:view'
        }
      },
      {
        path: 'asset/list',
        name: 'MobileAssetList',
        component: () => import('@mobile/views/asset/AssetList.vue'),
        meta: { 
          title: '资产列表',
          permission: 'asset:read'
        }
      },
      {
        path: 'asset/detail/:id',
        name: 'MobileAssetDetail',
        component: () => import('@mobile/views/asset/AssetDetail.vue'),
        meta: { 
          title: '资产详情',
          permission: 'asset:read',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/add',
        name: 'MobileAssetAdd',
        component: () => import('@mobile/views/asset/AssetAdd.vue'),
        meta: { 
          title: '添加资产',
          permission: 'asset:create',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/edit/:id',
        name: 'MobileAssetEdit',
        component: () => import('@mobile/views/asset/AssetEdit.vue'),
        meta: { 
          title: '编辑资产',
          permission: 'asset:update',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/inventory',
        name: 'MobileInventoryList',
        component: () => import('@mobile/views/asset/InventoryList.vue'),
        meta: { 
          title: '资产盘点',
          permission: 'inventory:view',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/inventory/task/:id',
        name: 'MobileInventoryTask',
        component: () => import('@mobile/views/asset/InventoryTask.vue'),
        meta: { 
          title: '盘点任务',
          permission: 'inventory:view',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/settings',
        name: 'MobileAssetSettings',
        component: () => import('@mobile/views/asset/AssetSettings.vue'),
        meta: { 
          title: '资产设置',
          permission: 'asset:edit',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/field-values',
        name: 'MobileFieldValueManagement',
        component: () => import('@mobile/views/asset/FieldValueManagement.vue'),
        meta: { 
          title: '字段值管理',
          permission: 'asset:edit',
          hideGlobalHeader: true
        }
      },
      {
        path: 'asset/custom-fields',
        name: 'MobileCustomFieldManagement',
        component: () => import('@mobile/views/asset/CustomFieldManagement.vue'),
        meta: { 
          title: '自定义字段管理',
          permission: 'asset:field:manage',
          hideGlobalHeader: true
        }
      },
      
      // 终端管理
      {
        path: 'terminal',
        name: 'MobileTerminal',
        component: () => import('@mobile/views/terminal/index.vue'),
        meta: { 
          title: '终端管理',
          permission: 'terminal:view'
        }
      },
      {
        path: 'terminal/list',
        name: 'MobileTerminalList',
        component: () => import('@mobile/views/terminal/TerminalList.vue'),
        meta: { 
          title: '终端列表',
          permission: 'terminal:read'
        }
      },
      {
        path: 'terminal/detail/:id',
        name: 'MobileTerminalDetail',
        component: () => import('@mobile/views/terminal/TerminalDetail.vue'),
        meta: { 
          title: '终端详情',
          permission: 'terminal:read'
        }
      },
      
      // 系统管理
      {
        path: 'system',
        name: 'MobileSystem',
        component: () => import('@mobile/views/system/index.vue'),
        meta: { 
          title: '系统管理',
          permission: 'system:view'
        }
      },
      {
        path: 'system/user',
        name: 'MobileUserManagement',
        component: () => import('@mobile/views/system/UserManagement.vue'),
        meta: { 
          title: '用户管理',
          permission: 'user:read'
        }
      }
    ]
  }
]

export default mobileRoutes 
 