import request from '@/utils/request'
import type { AxiosProgressEvent } from 'axios'

interface OUData {
  name: string
  description?: string
  [key: string]: any
}

interface UserData {
  username: string
  name: string
  email?: string
  password: string
  department?: string
  title?: string
  ou_dn: string
  groups?: string[]
  change_password_next_logon?: boolean
  password_expiry_date?: string | null
  password_never_expires?: boolean
  days_until_expiry?: number | null
  password_expired?: boolean
  [key: string]: any
}

interface GroupData {
  name: string
  description?: string
  [key: string]: any
}

interface ADConfig {
  server: string
  port: number
  base_dn: string
  username: string
  password?: string
  [key: string]: any
}

interface SearchParams {
  query?: string
  ou_dn?: string
  page?: number
  page_size?: number
  [key: string]: any
}

export function getOUTree() {
  return request({
    url: '/ad/management/ou',
    method: 'get',
    timeout: 10000
  })
}

export async function getOUTreeWithRetry(maxRetries = 3, retryDelay = 2000) {
  let attempts = 0;

  while (attempts < maxRetries) {
    try {
      const response = await request({
        url: '/ad/management/ou',
        method: 'get',
        timeout: 10000
      });

      if (response && response.data) {
        return response;
      }

      console.warn(`获取OU树数据为空，第${attempts + 1}次重试中...`);
    } catch (error) {
      console.error(`获取OU树失败 (尝试 ${attempts + 1}/${maxRetries}):`, error);
    }

    attempts++;
    if (attempts < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  return { data: null };
}

export function createOU(data: OUData) {
  return request({
    url: '/ad/management/ou',
    method: 'post',
    data
  })
}

export function updateOU(dn: string, data: OUData) {
  const encodedDn = encodeURIComponent(dn)
  return request({
    url: `/ad/management/ou/${encodedDn}`,
    method: 'put',
    data
  })
}

export function deleteOU(dn: string) {
  const encodedDn = encodeURIComponent(encodeURIComponent(dn))
  return request({
    url: `/ad/management/ou/${encodedDn}`,
    method: 'delete'
  })
}

export function getUsers(params: SearchParams) {
  return request({
    url: '/ad/management/users',
    method: 'get',
    params
  })
}

export function createUser(data: UserData) {
  return request({
    url: '/ad/management/users',
    method: 'post',
    data
  })
}

export function updateUser(username: string, data: UserData) {
  return request({
    url: `/ad/management/users/${username}`,
    method: 'put',
    data
  })
}

export function toggleUserStatus(username: string, forceEnable?: boolean) {
  return request({
    url: `/ad/management/users/${username}/toggle`,
    method: 'post',
    data: forceEnable !== undefined ? { force_enable: forceEnable } : undefined
  })
}

export function getADConfig() {
  return request<ADConfig>({
    url: '/ad/config/config',
    method: 'get'
  })
}

export function updateADConfig(data: ADConfig) {
  return request({
    url: '/ad/config/config',
    method: 'post',
    data
  })
}

export function testADConfig(data: ADConfig) {
  return request({
    url: '/ad/config/config/test',
    method: 'post',
    data
  })
}

export function getGroups(params: SearchParams) {
  return request({
    url: '/ad/management/groups',
    method: 'get',
    params
  })
}

export function createGroup(data: GroupData) {
  return request({
    url: '/ad/management/groups',
    method: 'post',
    data
  })
}

export function updateGroup(name: string, data: GroupData) {
  return request({
    url: `/ad/management/groups/${name}`,
    method: 'put',
    data
  })
}

export function deleteGroup(name: string) {
  return request({
    url: `/ad/management/groups/${name}`,
    method: 'delete'
  })
}

export function testGetUsers(ouDn: string) {
  return request({
    url: `/ad/management/test/users/${encodeURIComponent(ouDn)}`,
    method: 'get'
  })
}

export function testGetGroups(ouDn: string) {
  return request({
    url: `/ad/management/test/groups/${encodeURIComponent(ouDn)}`,
    method: 'get'
  })
}

export function getUserGroups(username: string) {
  return request({
    url: `/ad/management/users/${username}/groups`,
    method: 'get'
  })
}

export function updateUserGroups(username: string, groups: string[]) {
  return request({
    url: `/ad/management/users/${username}/groups`,
    method: 'put',
    data: groups
  })
}

export function deleteUser(username: string) {
  return request({
    url: `/ad/management/users/${username}`,
    method: 'delete'
  })
}

export function getAllGroups() {
  return request({
    url: '/ad/management/groups/all/root',
    method: 'get'
  })
}

export function getGroupMembers(name: string, params?: { page?: number, page_size?: number }) {
  return request({
    url: `/ad/management/groups/${name}/members`,
    method: 'get',
    params
  })
}

export function addGroupMembers(name: string, members: string[]) {
  return request({
    url: `/ad/management/groups/${name}/members`,
    method: 'post',
    data: { members }
  })
}

export function removeGroupMembers(name: string, members: string[]) {
  return request({
    url: `/ad/management/groups/${name}/members`,
    method: 'delete',
    data: { members }
  })
}

export function exportGroupMembers(name: string, format: 'xlsx' | 'csv' = 'xlsx', search?: string) {
  return request({
    url: `/ad/management/groups/${name}/members/export`,
    method: 'get',
    params: { format, search },
    responseType: 'blob'
  })
}

export function searchUsers(params: SearchParams) {
  return request({
    url: '/ad/management/users/search',
    method: 'get',
    params
  })
}

export function bulkImportUsers(formData: FormData) {
  return request({
    url: '/ad/management/users/bulk_import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000,
    onUploadProgress: (progressEvent: AxiosProgressEvent) => {
      if (progressEvent.total) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        console.log('上传进度:', percentCompleted)
      }
    }
  })
}

export function downloadImportTemplate(fileType: 'csv' | 'xlsx') {
  return request({
    url: `/ad/management/users/import_template/${fileType}`,
    method: 'get',
    responseType: 'blob'
  })
}

export function exportUsers(params: string | { ou_dn: string, format?: string, fields?: string[] }) {
  // 处理参数重载：如果是字符串，则当作ou_dn使用
  const ouDn = typeof params === 'string' ? params : params.ou_dn;
  const isExcel = typeof params !== 'string' && params.format === 'excel';

  // 统一使用查询参数来指定格式
  return request({
    url: `/ad/management/users/export/${ouDn}`,
    method: 'get',
    params: typeof params === 'string' ? {} : {
      format: params.format,
      fields: params.fields?.join(',')
    },
    responseType: isExcel ? 'blob' : undefined
  });
}

export function moveUser(username: string, target_ou_dn: string) {
  return request({
    url: `/ad/management/users/${username}/move`,
    method: 'post',
    data: { target_ou_dn }
  })
}

export function moveOU(dn: string, target_ou_dn: string) {
  const encodedDn = encodeURIComponent(encodeURIComponent(dn))
  return request({
    url: `/ad/management/ou/${encodedDn}/move`,
    method: 'post',
    data: { target_ou_dn }
  })
}

export function addUserToGroups(username: string, groups: string[]) {
  return request({
    url: `/ad/management/users/${username}/groups`,
    method: 'post',
    data: { groups }
  })
}

export function fixUserCN(username: string) {
  return request({
    url: `/ad/management/users/${username}/fix-cn`,
    method: 'post'
  })
}

export function fixAllUsersCN(ou_dn?: string) {
  return request({
    url: '/ad/management/users/fix-all-cn',
    method: 'post',
    data: { ou_dn }
  })
}

export function syncFromPersonnel(data: {
  company_id?: number;
  dept_id?: number;
  create_ou: boolean;
  create_security_groups?: boolean;
  add_users_to_dept_groups?: boolean;
  parent_ou_dn: string;
  change_password_next_logon: boolean;
  disable_inactive_users?: boolean;
  move_users_with_dept?: boolean;
}) {
  return request({
    url: '/ad/management/sync-from-personnel',
    method: 'post',
    data
  })
}

export function syncFromPersonnel2(data: {
  company_id?: number;
  dept_id?: number;
  create_ou: boolean;
  create_security_groups?: boolean;
  add_users_to_dept_groups?: boolean;
  parent_ou_dn: string;
  change_password_next_logon?: boolean;
  disable_inactive_users?: boolean;
  move_users_with_dept?: boolean;
}) {
  return request({
    url: '/ad/management/sync-from-personnel2',
    method: 'post',
    data
  })
}

export function syncOrganizationStructure(data: {
  parent_ou_dn: string;
}) {
  return request({
    url: '/ad/management/sync-organization-structure',
    method: 'post',
    data
  })
}

// AD人员同步配置相关接口
export interface ADSyncConfig {
  id?: number;
  enabled: boolean;
  source_company_id?: number | null;
  source_dept_id?: number | null;
  target_ou_dn: string;
  create_ou: boolean;
  create_security_groups?: boolean;
  add_users_to_dept_groups?: boolean;
  change_password_next_logon: boolean;
  disable_inactive_users: boolean;
  move_users_with_dept: boolean;
  update_user_groups_with_dept?: boolean;
  auto_rename_security_groups?: boolean;
  sync_interval: number;
  sync_time?: string | null;
  last_sync_time?: string | null;
  next_sync_time?: string | null;
}

export function getADSyncConfig() {
  return request<ADSyncConfig>({
    url: '/ad/sync-config/config',
    method: 'get'
  })
}

export function updateADSyncConfig(data: ADSyncConfig) {
  return request({
    url: '/ad/sync-config/config',
    method: 'post',
    data
  })
}

export function runManualADSync() {
  return request({
    url: '/ad/sync-config/run-sync',
    method: 'post'
  })
}

export interface ADSyncLog {
  id: number;
  operator: string;
  source_type: string; // 'all' | 'company' | 'department'
  source_id: number | null;
  sync_time: string;
  total_users: number;
  created_users: number;
  skipped_users: number;
  disabled_users?: number;
  moved_users?: number;
  updated_users?: number | any[]; // 可能是数字或用户详情数组
  created_ous?: number;
  renamed_ous?: number;
  created_groups?: number;
  updated_groups?: number;
  added_to_groups?: number;
  removed_from_groups?: number;
  errors?: string[];
  details: any;
}

// 日志分页响应接口
export interface ADSyncLogResponse {
  items: ADSyncLog[];
  total: number;
}

export function getADSyncLogs(params?: { limit?: number; skip?: number }) {
  return request<ADSyncLogResponse>({
    url: '/ad/sync-config/logs',
    method: 'get',
    params: {
      ...params,
      _t: Date.now()
    }
  })
}

export function exportSyncLogPasswords(logId: number, format: 'xlsx' | 'csv' = 'xlsx') {
  return request({
    url: `/ad/sync-config/logs/${logId}/export-passwords`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

export function exportSyncLogFullDetails(logId: number, format: 'xlsx' | 'csv' = 'xlsx') {
  return request({
    url: `/ad/sync-config/logs/${logId}/export-full`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}