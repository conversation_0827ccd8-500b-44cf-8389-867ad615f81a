from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Column, Integer, ForeignKey, Text, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models import Base

class Asset(Base):
    """固定资产模型"""
    __tablename__ = "assets"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    company: Mapped[str] = mapped_column(String(100), comment="公司")
    name: Mapped[str] = mapped_column(String(100), comment="资产名称")
    asset_number: Mapped[str] = mapped_column(String(50), unique=True, comment="资产编号")
    production_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="生产编号")
    price: Mapped[Optional[float]] = mapped_column(Float, nullable=True, comment="价格")
    supplier: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="供应商")
    manufacturer: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="制造商")
    purchaser: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="采购人")
    purchaser_job_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="采购人工号")
    status: Mapped[str] = mapped_column(String(20), comment="资产状态")
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="资产类别")
    specification: Mapped[str] = mapped_column(String(200), comment="资产规格")
    purchase_date: Mapped[datetime] = mapped_column(DateTime, comment="入账日期")
    retirement_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="销账日期")
    custodian: Mapped[str] = mapped_column(String(50), comment="领用人")
    custodian_job_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="领用人工号")
    custodian_department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="领用人部门")
    user: Mapped[str] = mapped_column(String(50), comment="使用人")
    user_job_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="使用人工号")
    user_department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="使用人部门")
    location: Mapped[Optional[str]] = mapped_column(String(200), nullable=True, comment="资产存放位置")
    inspector: Mapped[str] = mapped_column(String(50), comment="验收人")
    inspector_job_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="验收人工号")
    remarks: Mapped[Optional[str]] = mapped_column(String(500), nullable=True, comment="备注")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    change_logs = relationship("AssetChangeLog", back_populates="asset", cascade="all, delete-orphan")

class AssetChangeLog(Base):
    __tablename__ = "asset_change_logs"

    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey("assets.id", ondelete="CASCADE"), nullable=False)
    field = Column(String(50), nullable=False, comment="变更字段")
    old_value = Column(Text, nullable=True, comment="原值")
    new_value = Column(Text, nullable=True, comment="新值")
    change_type = Column(String(20), nullable=False, comment="变更类型")
    created_at = Column(DateTime, default=datetime.now, nullable=False)

    # 关联关系
    asset = relationship("Asset", back_populates="change_logs")
