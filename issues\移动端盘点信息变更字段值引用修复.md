# 移动端盘点信息变更字段值引用修复

## 问题描述
移动端资产盘点中，当盘点结果选择"信息变更"时，更新信息里面的字段无法引用字段值管理中的值，而桌面端可以正常使用。

## 问题分析

### 桌面端实现（正常）
- 使用 `el-select` 组件，配置了 `filterable`、`allow-create`、`default-first-option` 属性
- 通过 `loadFieldValueOptions()` 函数从字段值管理API获取数据
- 支持快速添加新字段值功能

### 移动端实现（问题）
- 直接使用 `van-field` 普通输入框
- 没有连接字段值管理数据源
- 缺少字段值选择和新增功能

## 解决方案
使用现有的 `MobileFieldValueSelector` 组件替换普通输入框，该组件已实现：
- 字段值数据加载和选择
- 搜索功能
- 新增字段值功能
- 与字段值管理模块的完整集成

## 修改内容

### 文件：`frontend/src/mobile/views/asset/InventoryTask.vue`

#### 1. 添加必要导入
```typescript
import MobileFieldValueSelector from '@/mobile/components/MobileFieldValueSelector.vue'
import { FIELD_NAMES } from '@/types/field_value'
```

#### 2. 替换信息变更字段组件
将以下字段从 `van-field` 替换为 `MobileFieldValueSelector`：

| 字段 | 字段类型 | 说明 |
|------|----------|------|
| 新资产名称 | `FIELD_NAMES.NAME` | 资产名称 |
| 新领用人 | `FIELD_NAMES.PERSONNEL` | 人员信息 |
| 新使用人 | `FIELD_NAMES.PERSONNEL` | 人员信息 |
| 新公司 | `FIELD_NAMES.COMPANY` | 公司信息 |
| 新位置 | `FIELD_NAMES.LOCATION` | 位置信息 |

#### 3. 组件配置示例
```vue
<MobileFieldValueSelector
  v-model="recordForm.newName"
  :field-name="FIELD_NAMES.NAME"
  name="newName"
  label="新资产名称"
  placeholder="请选择或输入新资产名称"
/>
```

## 修复效果
- ✅ 移动端盘点信息变更字段可以引用字段值管理中的数据
- ✅ 支持从下拉列表选择已有值
- ✅ 支持搜索字段值
- ✅ 支持添加新的字段值
- ✅ 与桌面端功能保持一致

## 测试建议
1. 在移动端进入资产盘点任务
2. 选择盘点结果为"信息变更"
3. 验证信息变更字段是否显示字段值管理中的数据
4. 测试选择已有值和添加新值功能
5. 确认保存后数据正确更新

## 相关文件
- `frontend/src/mobile/views/asset/InventoryTask.vue` - 主要修改文件
- `frontend/src/mobile/components/MobileFieldValueSelector.vue` - 字段值选择器组件
- `frontend/src/types/field_value.ts` - 字段类型定义
- `frontend/src/views/inventory/TaskDetail.vue` - 桌面端参考实现 