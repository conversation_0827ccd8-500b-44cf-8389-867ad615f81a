// 字段类型枚举
export enum FieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  DATE = 'date',
  DATETIME = 'datetime',
  SELECT = 'select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  FILE = 'file'
}

// 适用范围枚举
export enum AppliesTo {
  ASSET = 'asset',
  INVENTORY_RECORD = 'inventory_record',
  BOTH = 'both'
}

// 字段类型选项
export const FIELD_TYPE_OPTIONS = [
  { label: '文本输入', value: FieldType.TEXT, icon: 'EditPen' },
  { label: '多行文本', value: FieldType.TEXTAREA, icon: 'Document' },
  { label: '数字输入', value: FieldType.NUMBER, icon: 'Calculator' },
  { label: '日期选择', value: FieldType.DATE, icon: 'Calendar' },
  { label: '日期时间', value: FieldType.DATETIME, icon: 'Timer' },
  { label: '下拉选择', value: FieldType.SELECT, icon: 'ArrowDown' },
  { label: '单选按钮', value: FieldType.RADIO, icon: 'Select' },
  { label: '多选框', value: FieldType.CHECKBOX, icon: 'Checked' },
  { label: '文件上传', value: FieldType.FILE, icon: 'Upload' }
]

// 适用范围选项
export const APPLIES_TO_OPTIONS = [
  { label: '仅资产', value: AppliesTo.ASSET },
  { label: '仅盘点记录', value: AppliesTo.INVENTORY_RECORD },
  { label: '资产和盘点记录', value: AppliesTo.BOTH }
]

// 字段选项配置接口
export interface FieldOptions {
  choices?: Array<{ label: string; value: string; disabled?: boolean }>
  accept?: string  // 文件类型限制
  max_size?: number  // 文件大小限制 (bytes)
  multiple?: boolean  // 是否支持多选/多文件
  placeholder?: string  // 占位符
  min?: number | string  // 最小值/最小长度
  max?: number | string  // 最大值/最大长度
  step?: number  // 数字步长
  rows?: number  // 文本域行数
}

// 验证规则配置接口
export interface ValidationRules {
  required?: boolean
  min_length?: number
  max_length?: number
  min_value?: number
  max_value?: number
  pattern?: string  // 正则表达式
  custom_message?: string  // 自定义错误消息
}

// 自定义字段基础接口
export interface CustomFieldBase {
  name: string
  label: string
  field_type: FieldType
  description?: string
  is_required: boolean
  default_value?: string
  options?: FieldOptions
  validation_rules?: ValidationRules
  sort_order: number
  is_active: boolean
  applies_to: AppliesTo
}

// 自定义字段完整接口
export interface CustomField extends CustomFieldBase {
  id: number
  created_at: string
  updated_at: string
}

// 创建自定义字段接口
export interface CustomFieldCreate extends CustomFieldBase {}

// 更新自定义字段接口
export interface CustomFieldUpdate extends Partial<CustomFieldBase> {}

// 自定义字段值基础接口
export interface CustomFieldValueBase {
  custom_field_id: number
  value?: string
}

// 资产自定义字段值接口
export interface AssetCustomFieldValue extends CustomFieldValueBase {
  id: number
  asset_id: number
  created_at: string
  updated_at: string
  custom_field?: CustomField
}

export interface AssetCustomFieldValueCreate extends CustomFieldValueBase {
  asset_id: number
}

export interface AssetCustomFieldValueUpdate {
  value?: string
}

// 盘点记录自定义字段值接口
export interface InventoryRecordCustomFieldValue extends CustomFieldValueBase {
  id: number
  inventory_record_id?: number
  task_id?: number
  asset_id?: number
  created_at: string
  updated_at: string
  custom_field?: CustomField
}

export interface InventoryRecordCustomFieldValueCreate extends CustomFieldValueBase {
  inventory_record_id?: number
  task_id?: number
  asset_id?: number
}

export interface InventoryRecordCustomFieldValueUpdate {
  value?: string
}

// 自定义字段列表响应接口
export interface CustomFieldListResponse {
  data: CustomField[]
  total: number
  page: number
  limit: number
}

// 批量设置字段值请求接口
export interface BatchSetCustomFieldValuesRequest {
  values: Array<{
    custom_field_id: number
    value?: string
  }>
}

// 字段预设请求接口
export interface FieldPresetRequest {
  preset_type: string
  applies_to: AppliesTo
}

// 文件上传响应接口
export interface FileUploadResponse {
  filename: string
  url: string
  size: number
  content_type: string
  // 兼容不同API响应格式的可选属性
  name?: string
  file_url?: string
  type?: string
}

// 动态表单字段渲染配置
export interface DynamicFieldConfig {
  field: CustomField
  value?: string
  error?: string
  disabled?: boolean
  onChange?: (value: string) => void
  onBlur?: () => void
}

// 预设类型配置
export const PRESET_TYPE_OPTIONS = [
  { 
    label: '资产基础字段',
    value: 'asset_basic',
    description: '包含资产照片、成色评级等常用字段',
    applies_to: [AppliesTo.ASSET, AppliesTo.BOTH]
  },
  { 
    label: '盘点基础字段',
    value: 'inventory_basic',
    description: '包含盘点照片、盘点备注等字段',
    applies_to: [AppliesTo.INVENTORY_RECORD, AppliesTo.BOTH]
  }
]

// 字段排序项接口
export interface FieldSortItem {
  id: number
  sort_order: number
}

// 表单验证规则生成器
export const generateValidationRules = (field: CustomField) => {
  const rules: any[] = []
  
  // 必填验证
  if (field.is_required) {
    rules.push({
      required: true,
      message: `请${field.field_type === FieldType.SELECT ? '选择' : '输入'}${field.label}`
    })
  }
  
  // 字段特定验证
  if (field.validation_rules) {
    const { min_length, max_length, min_value, max_value, pattern } = field.validation_rules
    
    if (min_length !== undefined) {
      rules.push({
        min: min_length,
        message: `${field.label}长度不能少于${min_length}个字符`
      })
    }
    
    if (max_length !== undefined) {
      rules.push({
        max: max_length,
        message: `${field.label}长度不能超过${max_length}个字符`
      })
    }
    
    if (field.field_type === FieldType.NUMBER) {
      if (min_value !== undefined) {
        rules.push({
          type: 'number',
          min: min_value,
          message: `${field.label}不能小于${min_value}`
        })
      }
      
      if (max_value !== undefined) {
        rules.push({
          type: 'number',
          max: max_value,
          message: `${field.label}不能大于${max_value}`
        })
      }
    }
    
    if (pattern) {
      rules.push({
        pattern: new RegExp(pattern),
        message: field.validation_rules.custom_message || `${field.label}格式不正确`
      })
    }
  }
  
  return rules
} 