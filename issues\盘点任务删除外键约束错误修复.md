# 盘点任务删除外键约束错误修复

## 问题描述

删除盘点任务时出现外键约束违反错误：

```
sqlalchemy.exc.IntegrityError: (psycopg2.errors.ForeignKeyViolation) update or delete on table "inventory_records" violates foreign key constraint "inventory_record_custom_field_values_inventory_record_id_fkey" on table "inventory_record_custom_field_values"  
DETAIL:  Key (id)=(25) is still referenced from table "inventory_record_custom_field_values".
```

## 问题分析

1. **错误根本原因**：删除 `inventory_records` 表中的记录时，`inventory_record_custom_field_values` 表中仍有记录引用这些盘点记录
2. **级联删除配置缺失**：
   - `InventoryTask` 模型正确配置了对 `InventoryRecord` 的级联删除
   - 但 `InventoryRecord` 模型缺少对 `InventoryRecordCustomFieldValue` 的级联删除配置
3. **数据一致性问题**：当删除盘点记录时，其关联的自定义字段值没有被自动删除

## 修复方案

### 1. 模型关系配置修复

#### A. InventoryRecord 模型级联删除配置

在 `backend/app/models/inventory.py` 的 `InventoryRecord` 模型中添加级联删除关系：

```python
# 关联
task = relationship("InventoryTask", back_populates="inventory_records")
asset = relationship("Asset", lazy='joined')
# 添加对自定义字段值的级联删除关系
custom_field_values = relationship("InventoryRecordCustomFieldValue", 
                                 foreign_keys="InventoryRecordCustomFieldValue.inventory_record_id",
                                 cascade="all, delete-orphan")
```

#### B. InventoryTask 模型级联删除配置

在 `backend/app/models/inventory.py` 的 `InventoryTask` 模型中添加对虚拟自定义字段值的级联删除关系：

```python
# 关联的盘点记录
inventory_records = relationship("InventoryRecord", back_populates="task", cascade="all, delete-orphan")
# 添加对虚拟自定义字段值的级联删除关系
virtual_custom_field_values = relationship("InventoryRecordCustomFieldValue", 
                                          foreign_keys="InventoryRecordCustomFieldValue.task_id",
                                          cascade="all, delete-orphan")
```

### 2. SQLAlchemy警告修复

在 `backend/app/models/custom_field.py` 中添加 `overlaps` 参数：

```python
# 关联
inventory_record = relationship("InventoryRecord", lazy='joined', overlaps="custom_field_values")
task = relationship("InventoryTask", lazy='joined', overlaps="virtual_custom_field_values")
asset = relationship("Asset", lazy='joined')
custom_field = relationship("CustomField", back_populates="inventory_values")
```

### 3. 数据清理迁移

创建迁移文件 `00e6fb6b7f3f_fix_inventory_record_custom_field_values_cascade_delete.py`：

```python
def upgrade() -> None:
    # 清理孤立的自定义字段值记录
    # 删除那些引用了不存在的盘点记录的自定义字段值
    op.execute("""
        DELETE FROM inventory_record_custom_field_values 
        WHERE inventory_record_id IS NOT NULL 
        AND inventory_record_id NOT IN (
            SELECT id FROM inventory_records
        )
    """)
```

## 验证结果

1. **测试案例**：创建包含两种类型自定义字段值的盘点任务，然后删除
   - 绑定到盘点记录的自定义字段值 (通过 `inventory_record_id`)
   - 虚拟自定义字段值 (通过 `task_id` + `asset_id`)

2. **测试结果**：
   - 盘点任务删除成功
   - 关联的盘点记录被正确删除：`Remaining inventory records: 0`
   - 绑定到盘点记录的自定义字段值被正确删除：`Remaining custom field values (record-bound): 0`
   - 虚拟自定义字段值被正确删除：`Remaining custom field values (virtual): 0`
   - 无外键约束违反错误

## 影响范围

- ✅ 盘点任务删除功能恢复正常
- ✅ 数据一致性得到保障
- ✅ 级联删除按预期工作
- ✅ 解决了 SQLAlchemy 关系映射警告

## 技术要点

1. **级联删除配置**：使用 `cascade="all, delete-orphan"` 确保子记录随父记录删除
2. **外键约束处理**：通过 `foreign_keys` 参数明确指定外键关系
3. **关系映射优化**：使用 `overlaps` 参数解决重叠关系警告
4. **数据清理**：迁移脚本确保历史数据的一致性

## 相关文件

- `backend/app/models/inventory.py` - 盘点模型
- `backend/app/models/custom_field.py` - 自定义字段模型  
- `backend/alembic/versions/00e6fb6b7f3f_*.py` - 数据清理迁移 