#!/usr/bin/env python3
"""
PostgreSQL数据库标准化重建脚本
完全重建干净的数据库，确保与模型定义100%匹配
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine, SessionLocal
from app.models import Base
from sqlalchemy import text, inspect, MetaData
from app.initial_data import init_db
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def drop_all_tables():
    """删除所有表"""
    logger.info("🗑️ 开始删除所有表...")
    
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    if not existing_tables:
        logger.info("✅ 数据库为空，无需删除")
        return
    
    with engine.connect() as conn:
        # 禁用外键约束检查
        conn.execute(text("SET session_replication_role = replica;"))
        
        # 删除所有表
        for table_name in existing_tables:
            try:
                conn.execute(text(f"DROP TABLE IF EXISTS {table_name} CASCADE"))
                logger.info(f"   ✅ 删除表: {table_name}")
            except Exception as e:
                logger.warning(f"   ⚠️ 删除表 {table_name} 失败: {e}")
        
        # 恢复外键约束检查
        conn.execute(text("SET session_replication_role = DEFAULT;"))
        conn.commit()
    
    logger.info("✅ 所有表删除完成")

def create_all_tables():
    """基于模型定义创建所有表，处理依赖关系"""
    logger.info("🏗️ 开始创建所有表...")
    
    try:
        # 方法1: 尝试直接创建所有表
        try:
            Base.metadata.create_all(bind=engine)
            logger.info("✅ 使用直接方式创建所有表成功")
        except Exception as e1:
            logger.warning(f"⚠️ 直接创建失败: {e1}")
            logger.info("🔄 尝试分步创建...")
            
            # 方法2: 分步创建，先创建无外键的表
            metadata = MetaData()
            metadata.reflect(bind=engine)
            
            # 获取所有模型表
            tables_to_create = list(Base.metadata.tables.values())
            created_tables = set()
            max_attempts = len(tables_to_create) * 2
            
            attempt = 0
            while tables_to_create and attempt < max_attempts:
                attempt += 1
                tables_created_this_round = []
                
                for table in tables_to_create[:]:
                    try:
                        table.create(bind=engine, checkfirst=True)
                        tables_created_this_round.append(table)
                        created_tables.add(table.name)
                        logger.info(f"   ✅ 创建表: {table.name}")
                    except Exception as e:
                        logger.debug(f"   延迟创建表 {table.name}: {e}")
                        continue
                
                # 移除已创建的表
                for table in tables_created_this_round:
                    tables_to_create.remove(table)
                
                # 如果本轮没有创建任何表，说明可能有循环依赖
                if not tables_created_this_round and tables_to_create:
                    logger.warning("检测到可能的循环依赖，尝试跳过外键约束")
                    break
            
            # 如果还有表未创建，强制创建
            if tables_to_create:
                logger.info("🔧 强制创建剩余表...")
                with engine.connect() as conn:
                    conn.execute(text("SET session_replication_role = replica;"))
                    for table in tables_to_create:
                        try:
                            table.create(bind=engine, checkfirst=True)
                            logger.info(f"   ✅ 强制创建表: {table.name}")
                        except Exception as e:
                            logger.error(f"   ❌ 无法创建表 {table.name}: {e}")
                    conn.execute(text("SET session_replication_role = DEFAULT;"))
        
        # 验证表创建结果
        inspector = inspect(engine)
        created_tables = inspector.get_table_names()
        model_tables = list(Base.metadata.tables.keys())
        
        logger.info(f"📊 创建表统计:")
        logger.info(f"   - 模型定义表数: {len(model_tables)}")
        logger.info(f"   - 实际创建表数: {len(created_tables)}")
        
        missing_tables = set(model_tables) - set(created_tables)
        if missing_tables:
            logger.error(f"❌ 缺失的表: {missing_tables}")
            return False
        
        logger.info("✅ 表结构验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建表失败: {e}")
        return False

def setup_alembic_version():
    """设置Alembic版本为当前HEAD"""
    logger.info("🔧 设置Alembic版本...")
    
    try:
        with engine.connect() as conn:
            # 创建alembic_version表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS alembic_version (
                    version_num VARCHAR(32) NOT NULL
                )
            """))
            
            # 删除现有版本记录
            conn.execute(text("DELETE FROM alembic_version"))
            
            # 设置为当前HEAD版本
            current_head = "fix_permissions_table_only"
            conn.execute(text(
                "INSERT INTO alembic_version (version_num) VALUES (:version)"
            ), {"version": current_head})
            
            conn.commit()
            logger.info(f"✅ Alembic版本设置为: {current_head}")
            return True
            
    except Exception as e:
        logger.error(f"❌ 设置Alembic版本失败: {e}")
        return False

def initialize_data():
    """初始化基础数据"""
    logger.info("📄 开始初始化基础数据...")
    
    try:
        init_db()
        logger.info("✅ 基础数据初始化完成")
        return True
    except Exception as e:
        logger.error(f"❌ 初始化数据失败: {e}")
        return False

def verify_database():
    """验证数据库完整性"""
    logger.info("🔍 开始验证数据库...")
    
    try:
        db = SessionLocal()
        
        # 检查核心表的数据
        from app.models import User, Permission, Role, Asset
        
        user_count = db.query(User).count()
        permission_count = db.query(Permission).count() 
        role_count = db.query(Role).count()
        
        logger.info(f"📊 数据验证结果:")
        logger.info(f"   - 用户数量: {user_count}")
        logger.info(f"   - 权限数量: {permission_count}")
        logger.info(f"   - 角色数量: {role_count}")
        
        # 验证管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            logger.info(f"✅ 管理员用户: {admin_user.username} ({admin_user.email})")
        else:
            logger.error("❌ 管理员用户未找到")
            return False
        
        # 测试Asset表结构
        try:
            asset_count = db.query(Asset).count()
            logger.info(f"✅ 资产表测试通过，记录数: {asset_count}")
        except Exception as e:
            logger.error(f"❌ 资产表测试失败: {e}")
            return False
        
        db.close()
        logger.info("✅ 数据库验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库验证失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始PostgreSQL数据库标准化重建")
    logger.info("=" * 50)
    
    try:
        # 阶段1: 删除所有表
        drop_all_tables()
        
        # 阶段2: 创建所有表
        if not create_all_tables():
            logger.error("❌ 重建失败：表创建阶段")
            return 1
        
        # 阶段3: 设置Alembic版本
        if not setup_alembic_version():
            logger.error("❌ 重建失败：Alembic设置阶段")
            return 1
        
        # 阶段4: 初始化数据
        if not initialize_data():
            logger.error("❌ 重建失败：数据初始化阶段")
            return 1
        
        # 阶段5: 验证完整性
        if not verify_database():
            logger.error("❌ 重建失败：验证阶段")
            return 1
        
        logger.info("=" * 50)
        logger.info("🎉 PostgreSQL数据库重建成功！")
        logger.info("💡 后续步骤:")
        logger.info("   1. 启动应用: uv run python run.py")
        logger.info("   2. 访问: http://localhost:8000")
        logger.info("   3. 登录: admin / admin123")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 重建过程中发生严重错误: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 