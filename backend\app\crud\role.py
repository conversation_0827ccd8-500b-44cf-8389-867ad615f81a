from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.role import Role
from app.models.user import User
from app.schemas.role import RoleCreate, RoleUpdate

class CRUDRole(CRUDBase[Role, RoleCreate, RoleUpdate]):
    def get_by_code(self, db: Session, code: str) -> Optional[Role]:
        """通过角色代码获取角色"""
        return db.query(Role).filter(Role.code == code).first()
    
    def create_with_permissions(self, db: Session, *, obj_in: RoleCreate, permission_ids: List[int] = None) -> Role:
        """创建角色并关联权限"""
        from app.models.permission import Permission
        
        # 创建角色基本信息
        db_obj = Role(
            code=obj_in.code,
            name=obj_in.name,
            description=obj_in.description,
            is_default=obj_in.is_default,
        )
        db.add(db_obj)
        db.flush()  # 获取新创建的角色ID
        
        # 关联权限
        if permission_ids:
            permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
            db_obj.permissions = permissions
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_with_permissions(
        self, db: Session, *, db_obj: Role, obj_in: Union[RoleUpdate, Dict[str, Any]], permission_ids: List[int] = None
    ) -> Role:
        """更新角色信息及权限"""
        from app.models.permission import Permission
        
        # 更新角色基本信息
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
            
        # 更新角色权限
        if permission_ids is not None:
            permissions = db.query(Permission).filter(Permission.id.in_(permission_ids)).all()
            db_obj.permissions = permissions
            
        # 更新其他字段
        for field in update_data:
            if field != "permissions" and hasattr(db_obj, field):
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def assign_roles_to_user(self, db: Session, *, user_id: int, role_ids: List[int]) -> User:
        """为用户分配角色"""
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
            
        roles = db.query(Role).filter(Role.id.in_(role_ids)).all()
        user.roles = roles
        
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    def get_default_role(self, db: Session) -> Optional[Role]:
        """获取默认角色"""
        return db.query(Role).filter(Role.is_default == 1).first()

role_crud = CRUDRole(Role) 