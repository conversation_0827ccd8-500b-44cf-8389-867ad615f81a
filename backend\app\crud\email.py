from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from typing import List, Optional, Dict, Any
from app.models.email import EmailDepartment, EmailMember, EmailTag, EmailGroup, EmailConfig
from app.schemas.email import (
    EmailDepartmentCreate, EmailDepartmentUpdate,
    EmailMemberCreate, EmailMemberUpdate,
    EmailTagCreate, EmailTagUpdate,
    EmailGroupCreate, EmailGroupUpdate,
    EmailConfigCreate, EmailConfigUpdate
)

# 邮箱配置CRUD
class CRUDEmailConfig:
    def get(self, db: Session, id: int) -> Optional[EmailConfig]:
        return db.query(EmailConfig).filter(EmailConfig.id == id).first()

    def get_active(self, db: Session) -> Optional[EmailConfig]:
        return db.query(EmailConfig).filter(EmailConfig.is_active == True).first()

    def get_contact_management_config(self, db: Session) -> Optional[EmailConfig]:
        """获取通讯录管理应用配置"""
        return db.query(EmailConfig).filter(
            EmailConfig.app_name == "通讯录管理",
            EmailConfig.is_active == True
        ).first()

    def get_function_settings_config(self, db: Session) -> Optional[EmailConfig]:
        """获取功能设置应用配置"""
        return db.query(EmailConfig).filter(
            EmailConfig.app_name == "功能设置",
            EmailConfig.is_active == True
        ).first()

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EmailConfig]:
        return db.query(EmailConfig).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: EmailConfigCreate) -> EmailConfig:
        db_obj = EmailConfig(**obj_in.dict())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: EmailConfig, obj_in: EmailConfigUpdate) -> EmailConfig:
        update_data = obj_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> EmailConfig:
        obj = db.query(EmailConfig).get(id)
        db.delete(obj)
        db.commit()
        return obj

# 部门CRUD
class CRUDEmailDepartment:
    def get(self, db: Session, id: int) -> Optional[EmailDepartment]:
        return db.query(EmailDepartment).filter(EmailDepartment.id == id).first()

    def get_by_dept_id(self, db: Session, dept_id: str) -> Optional[EmailDepartment]:
        return db.query(EmailDepartment).filter(EmailDepartment.dept_id == dept_id).first()

    def get_by_name(self, db: Session, name: str) -> Optional[EmailDepartment]:
        """根据部门名称获取部门"""
        return db.query(EmailDepartment).filter(EmailDepartment.name == name).first()

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EmailDepartment]:
        return db.query(EmailDepartment).offset(skip).limit(limit).all()

    def get_by_parent(self, db: Session, parent_id: Optional[str] = None) -> List[EmailDepartment]:
        if parent_id is None:
            return db.query(EmailDepartment).filter(EmailDepartment.parent_id.is_(None)).all()
        return db.query(EmailDepartment).filter(EmailDepartment.parent_id == parent_id).all()

    def search(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[EmailDepartment]:
        return db.query(EmailDepartment).filter(
            EmailDepartment.name.contains(name)
        ).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: EmailDepartmentCreate) -> EmailDepartment:
        db_obj = EmailDepartment(**obj_in.dict())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: EmailDepartment, obj_in: EmailDepartmentUpdate) -> EmailDepartment:
        update_data = obj_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> EmailDepartment:
        obj = db.query(EmailDepartment).get(id)
        db.delete(obj)
        db.commit()
        return obj

    def get_tree(self, db: Session) -> List[Dict[str, Any]]:
        """获取部门树形结构"""
        departments = db.query(EmailDepartment).filter(EmailDepartment.is_active == True).all()
        
        # 构建部门字典
        dept_dict = {dept.dept_id: {
            'id': dept.id,
            'dept_id': dept.dept_id,
            'name': dept.name,
            'parent_id': dept.parent_id,
            'order': dept.order,
            'children': []
        } for dept in departments}
        
        # 构建树形结构
        tree = []
        for dept in departments:
            # 根部门判断：parent_id为None或者为"0"（腾讯企业邮箱API使用"0"表示根部门）
            if dept.parent_id is None or dept.parent_id == "0":
                tree.append(dept_dict[dept.dept_id])
            else:
                if dept.parent_id in dept_dict:
                    dept_dict[dept.parent_id]['children'].append(dept_dict[dept.dept_id])
        
        return tree

# 成员CRUD
class CRUDEmailMember:
    def get(self, db: Session, id: int) -> Optional[EmailMember]:
        return db.query(EmailMember).filter(EmailMember.id == id).first()

    def get_by_email(self, db: Session, email: str) -> Optional[EmailMember]:
        """根据邮箱地址获取成员"""
        return db.query(EmailMember).filter(EmailMember.email == email).first()

    def get_by_extid(self, db: Session, extid: str) -> Optional[EmailMember]:
        """根据工号获取成员"""
        return db.query(EmailMember).filter(EmailMember.extid == extid).first()
    
    def get_by_userid(self, db: Session, userid: str) -> Optional[EmailMember]:
        """
        根据腾讯企业邮箱API的userid获取成员
        
        注意：腾讯API中的userid实际上是邮箱地址
        此方法等同于get_by_email，保留是为了API兼容性
        """
        return self.get_by_email(db, email=userid)

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EmailMember]:
        return db.query(EmailMember).offset(skip).limit(limit).all()
    
    def count_all(self, db: Session) -> int:
        """统计所有成员总数"""
        return db.query(EmailMember).count()
    
    def count_by_department(self, db: Session, department_id: str) -> int:
        """统计指定部门的成员总数"""
        return db.query(EmailMember).filter(
            EmailMember.department_id == department_id
        ).count()
    
    def count_by_search(self, db: Session, name: str) -> int:
        """统计搜索条件下的成员总数"""
        return db.query(EmailMember).filter(
            or_(
                EmailMember.name.contains(name),
                EmailMember.email.contains(name),
                EmailMember.mobile.contains(name)
            )
        ).count()
    
    def get_multi_with_department(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取成员列表，包含部门信息"""
        members = db.query(EmailMember).join(
            EmailDepartment, EmailMember.department_id == EmailDepartment.dept_id, isouter=True
        ).offset(skip).limit(limit).all()
        
        result = []
        for member in members:
            member_dict = {
                'id': member.id,
                'extid': member.extid,
                'email': member.email,
                'userid': member.email,  # 腾讯API兼容性：userid字段值为邮箱地址
                'name': member.name,
                'department_id': member.department_id,
                'department_name': member.department.name if member.department else '',
                'position': member.position,
                'mobile': member.mobile,
                'tel': member.tel,
                'cpwd_login': member.cpwd_login,
                'extattr': member.extattr,
                'is_active': member.is_active,
                'created_at': member.created_at,
                'updated_at': member.updated_at
            }
            result.append(member_dict)
        return result

    def get_by_department(self, db: Session, department_id: str, *, skip: int = 0, limit: int = 100) -> List[EmailMember]:
        return db.query(EmailMember).filter(
            EmailMember.department_id == department_id
        ).offset(skip).limit(limit).all()
    
    def get_by_department_with_department(self, db: Session, department_id: str, *, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """根据部门获取成员列表，包含部门信息"""
        members = db.query(EmailMember).join(
            EmailDepartment, EmailMember.department_id == EmailDepartment.dept_id, isouter=True
        ).filter(EmailMember.department_id == department_id).offset(skip).limit(limit).all()
        
        result = []
        for member in members:
            member_dict = {
                'id': member.id,
                'extid': member.extid,
                'email': member.email,
                'userid': member.email,  # 腾讯API兼容性：userid字段值为邮箱地址
                'name': member.name,
                'department_id': member.department_id,
                'department_name': member.department.name if member.department else '',
                'position': member.position,
                'mobile': member.mobile,
                'tel': member.tel,
                'cpwd_login': member.cpwd_login,
                'extattr': member.extattr,
                'is_active': member.is_active,
                'created_at': member.created_at,
                'updated_at': member.updated_at
            }
            result.append(member_dict)
        return result

    def search(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[EmailMember]:
        return db.query(EmailMember).filter(
            or_(
                EmailMember.name.contains(name),
                EmailMember.email.contains(name),
                EmailMember.mobile.contains(name)
            )
        ).offset(skip).limit(limit).all()
    
    def search_with_department(self, db: Session, *, name: str, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """搜索成员，包含部门信息"""
        members = db.query(EmailMember).join(
            EmailDepartment, EmailMember.department_id == EmailDepartment.dept_id, isouter=True
        ).filter(
            or_(
                EmailMember.name.contains(name),
                EmailMember.email.contains(name),
                EmailMember.mobile.contains(name)
            )
        ).offset(skip).limit(limit).all()
        
        result = []
        for member in members:
            member_dict = {
                'id': member.id,
                'extid': member.extid,
                'email': member.email,
                'userid': member.email,  # 腾讯API兼容性：userid字段值为邮箱地址
                'name': member.name,
                'department_id': member.department_id,
                'department_name': member.department.name if member.department else '',
                'position': member.position,
                'mobile': member.mobile,
                'tel': member.tel,
                'cpwd_login': member.cpwd_login,
                'extattr': member.extattr,
                'is_active': member.is_active,
                'created_at': member.created_at,
                'updated_at': member.updated_at
            }
            result.append(member_dict)
        return result

    def create(self, db: Session, *, obj_in: EmailMemberCreate) -> EmailMember:
        # 注意：密码字段不在数据库中存储，仅用于腾讯API调用
        obj_data = obj_in.dict(exclude={"password"})
        db_obj = EmailMember(**obj_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: EmailMember, obj_in: EmailMemberUpdate) -> EmailMember:
        update_data = obj_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> EmailMember:
        obj = db.query(EmailMember).get(id)
        db.delete(obj)
        db.commit()
        return obj
    
    def get_members_for_export(self, db: Session, *, search: Optional[str] = None, department_id: Optional[str] = None) -> List[EmailMember]:
        """获取用于导出的成员列表（包含部门信息）"""
        query = db.query(EmailMember).join(
            EmailDepartment, EmailMember.department_id == EmailDepartment.dept_id, isouter=True
        )
        
        # 添加搜索条件
        if search:
            query = query.filter(
                or_(
                    EmailMember.name.contains(search),
                    EmailMember.email.contains(search),
                    EmailMember.mobile.contains(search)
                )
            )
        
        # 添加部门过滤
        if department_id:
            query = query.filter(EmailMember.department_id == department_id)
        
        return query.all()

# 标签CRUD
class CRUDEmailTag:
    def get(self, db: Session, id: int) -> Optional[EmailTag]:
        return db.query(EmailTag).filter(EmailTag.id == id).first()

    def get_by_tagid(self, db: Session, tagid: int) -> Optional[EmailTag]:
        return db.query(EmailTag).filter(EmailTag.tagid == tagid).first()

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EmailTag]:
        return db.query(EmailTag).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: EmailTagCreate) -> EmailTag:
        db_obj = EmailTag(**obj_in.dict())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: EmailTag, obj_in: EmailTagUpdate) -> EmailTag:
        update_data = obj_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> EmailTag:
        obj = db.query(EmailTag).get(id)
        db.delete(obj)
        db.commit()
        return obj

# 邮件群组CRUD
class CRUDEmailGroup:
    def get(self, db: Session, id: int) -> Optional[EmailGroup]:
        return db.query(EmailGroup).filter(EmailGroup.id == id).first()

    def get_by_groupid(self, db: Session, groupid: str) -> Optional[EmailGroup]:
        return db.query(EmailGroup).filter(EmailGroup.groupid == groupid).first()

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EmailGroup]:
        return db.query(EmailGroup).offset(skip).limit(limit).all()

    def search(self, db: Session, *, keyword: str, skip: int = 0, limit: int = 100) -> List[EmailGroup]:
        return db.query(EmailGroup).filter(
            or_(
                EmailGroup.groupname.contains(keyword),
                EmailGroup.groupdesc.contains(keyword)
            )
        ).offset(skip).limit(limit).all()

    def create(self, db: Session, *, obj_in: EmailGroupCreate) -> EmailGroup:
        db_obj = EmailGroup(**obj_in.dict())
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: EmailGroup, obj_in: EmailGroupUpdate) -> EmailGroup:
        update_data = obj_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: int) -> EmailGroup:
        obj = db.query(EmailGroup).get(id)
        db.delete(obj)
        db.commit()
        return obj

# 创建实例
email_config = CRUDEmailConfig()
email_department = CRUDEmailDepartment()
email_member = CRUDEmailMember()
email_tag = CRUDEmailTag()
email_group = CRUDEmailGroup() 