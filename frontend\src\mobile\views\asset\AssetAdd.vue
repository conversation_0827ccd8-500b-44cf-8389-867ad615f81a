<template>
  <div class="mobile-asset-add">
    <!-- 导航栏 -->
    <van-nav-bar
      title="添加资产"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-button
          type="primary"
          size="small"
          :loading="submitting"
          @click="handleSubmit"
        >
          保存
        </van-button>
      </template>
    </van-nav-bar>

    <!-- 表单容器 -->
    <div class="form-container">
      <van-form @submit="handleSubmit" ref="formRef">
        <!-- 基本信息 -->
        <van-cell-group title="基本信息" inset>
          <!-- 公司名称 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.company"
            field-name="company"
            name="company"
            label="公司名称"
            placeholder="请选择公司名称"
            :rules="[{ required: true, message: '请选择公司名称' }]"
            @change="handleCompanyChange"
          />
          
          <van-field
            v-model="formData.name"
            name="name"
            label="资产名称"
            placeholder="请输入资产名称"
            :rules="[{ required: true, message: '请输入资产名称' }]"
          >
            <template #button>
              <van-button 
                size="small" 
                type="primary" 
                @click="showScanDialog = true"
                icon="scan"
              >
                扫码
              </van-button>
            </template>
          </van-field>
          <van-field
            v-model="formData.asset_number"
            name="asset_number"
            label="资产编号"
            placeholder="自动生成或手动输入"
          />
          
          <!-- 规格型号 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.specification"
            field-name="specification"
            name="specification"
            label="规格型号"
            placeholder="请选择规格型号"
            :rules="[{ required: true, message: '请选择规格型号' }]"
          />
          
          <van-field
            v-model="formData.production_number"
            name="production_number"
            label="生产编号"
            placeholder="请输入生产编号"
          />
          <van-field
            v-model="formData.price"
            name="price"
            label="价格"
            placeholder="请输入价格"
            type="number"
          />
        </van-cell-group>

        <!-- 状态信息 -->
        <van-cell-group title="状态信息" inset>
          <!-- 资产状态 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.status"
            field-name="status"
            name="status"
            label="资产状态"
            placeholder="请选择资产状态"
            :rules="[{ required: true, message: '请选择资产状态' }]"
          />
          
          <!-- 资产类别 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.category"
            field-name="category"
            name="category"
            label="资产类别"
            placeholder="请选择资产类别"
            :rules="[{ required: true, message: '请选择资产类别' }]"
          />
          
          <!-- 存放位置 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.location"
            field-name="location"
            name="location"
            label="存放位置"
            placeholder="请选择存放位置"
            :rules="[{ required: true, message: '请选择存放位置' }]"
          />
        </van-cell-group>

        <!-- 采购信息 -->
        <van-cell-group title="采购信息" inset>
          <van-field
            v-model="formData.purchase_date"
            name="purchase_date"
            label="采购日期"
            placeholder="请选择采购日期"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择采购日期' }]"
          />
          <van-field
            v-model="formData.retirement_date"
            name="retirement_date"
            label="报废日期"
            placeholder="请选择报废日期（可选）"
            readonly
            is-link
            @click="showRetirementPicker = true"
          />
          
          <!-- 供应商 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.supplier"
            field-name="supplier"
            name="supplier"
            label="供应商"
            placeholder="请选择供应商"
          />
          
          <!-- 制造商 - 使用字段值选择器 -->
          <MobileFieldValueSelector
            v-model="formData.manufacturer"
            field-name="manufacturer"
            name="manufacturer"
            label="制造商"
            placeholder="请选择制造商"
          />
          
          <!-- 采购人 - 使用人员选择器 -->
          <MobilePersonnelSelector
            v-model="formData.purchaser"
            name="purchaser"
            label="采购人"
            placeholder="请选择采购人"
            @select="handlePurchaserSelect"
          />
          
          <van-field
            v-model="formData.purchaser_job_number"
            name="purchaser_job_number"
            label="采购人工号"
            placeholder="自动填充或手动输入"
            readonly
          />
        </van-cell-group>

        <!-- 保管信息 -->
        <van-cell-group title="保管信息" inset>
          <!-- 保管人 - 使用人员选择器 -->
          <MobilePersonnelSelector
            v-model="formData.custodian"
            name="custodian"
            label="保管人"
            placeholder="请选择保管人"
            :rules="[{ required: true, message: '请选择保管人' }]"
            @select="handleCustodianSelect"
          />
          
          <van-field
            v-model="formData.custodian_job_number"
            name="custodian_job_number"
            label="保管人工号"
            placeholder="自动填充或手动输入"
            readonly
          />
          
          <van-field
            v-model="formData.custodian_department"
            name="custodian_department"
            label="保管部门"
            placeholder="自动填充或手动输入"
            :rules="[{ required: true, message: '保管部门不能为空' }]"
            readonly
          />
        </van-cell-group>

        <!-- 使用信息 -->
        <van-cell-group title="使用信息" inset>
          <!-- 使用人 - 使用人员选择器 -->
          <MobilePersonnelSelector
            v-model="formData.user"
            name="user"
            label="使用人"
            placeholder="请选择使用人"
            :rules="[{ required: true, message: '请选择使用人' }]"
            @select="handleUserSelect"
          />
          
          <van-field
            v-model="formData.user_job_number"
            name="user_job_number"
            label="使用人工号"
            placeholder="自动填充或手动输入"
            readonly
          />
          
          <van-field
            v-model="formData.user_department"
            name="user_department"
            label="使用部门"
            placeholder="自动填充或手动输入"
            :rules="[{ required: true, message: '使用部门不能为空' }]"
            readonly
          />
        </van-cell-group>

        <!-- 检查信息 -->
        <van-cell-group title="检查信息" inset>
          <!-- 检查人 - 使用人员选择器 -->
          <MobilePersonnelSelector
            v-model="formData.inspector"
            name="inspector"
            label="检查人"
            placeholder="请选择检查人"
            :rules="[{ required: true, message: '请选择检查人' }]"
            @select="handleInspectorSelect"
          />
          
          <van-field
            v-model="formData.inspector_job_number"
            name="inspector_job_number"
            label="检查人工号"
            placeholder="自动填充或手动输入"
            readonly
          />
        </van-cell-group>

        <!-- 其他信息 -->
        <van-cell-group title="其他信息" inset>
          <van-field
            v-model="formData.remarks"
            name="remarks"
            label="备注"
            type="textarea"
            placeholder="请输入备注信息"
            rows="3"
            maxlength="500"
            show-word-limit
          />
        </van-cell-group>

        <!-- 自定义字段 -->
        <van-cell-group title="自定义字段" inset v-if="customFields.length > 0">
          <template v-for="field in customFields" :key="field.id">
            <!-- 文本类型 -->
            <van-field
              v-if="field.field_type === 'text'"
              v-model="customFieldData[field.name]"
              :name="`custom_${field.name}`"
              :label="field.label"
              :placeholder="`请输入${field.label}`"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
            />
            
            <!-- 多行文本 -->
            <van-field
              v-else-if="field.field_type === 'textarea'"
              v-model="customFieldData[field.name]"
              :name="`custom_${field.name}`"
              :label="field.label"
              type="textarea"
              :placeholder="`请输入${field.label}`"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
              rows="3"
            />
            
            <!-- 数字类型 -->
            <van-field
              v-else-if="field.field_type === 'number'"
              v-model="customFieldData[field.name]"
              :name="`custom_${field.name}`"
              :label="field.label"
              type="number"
              :placeholder="`请输入${field.label}`"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
            />
            
            <!-- 选择类型 -->
            <van-field
              v-else-if="field.field_type === 'select'"
              v-model="customFieldData[field.name]"
              :name="`custom_${field.name}`"
              :label="field.label"
              :placeholder="`请选择${field.label}`"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
              is-link
              readonly
              @click="showFieldPicker(field)"
            />
            
            <!-- 日期类型 -->
            <van-field
              v-else-if="field.field_type === 'date'"
              readonly
              :model-value="customFieldData[field.name]"
              :name="`custom_${field.name}`"
              :label="field.label"
              :placeholder="`请选择${field.label}`"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
              @click="showCustomDateSelector = true; currentCustomField = field"
            />
            
            <!-- 文件上传类型 -->
            <van-field
              v-else-if="field.field_type === 'file'"
              :name="`custom_${field.name}`"
              :label="field.label"
              :required="field.is_required"
              :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
            >
              <template #input>
                <MobileFileUploader
                  v-model="customFieldData[field.name]"
                  :accept="field.options?.accept || 'image/*'"
                  :max-size="field.options?.max_size || 5 * 1024 * 1024"
                  :multiple="field.options?.multiple || false"
                  :max-count="field.options?.multiple ? 9 : 1"
                  :upload-text="field.options?.multiple ? '上传图片' : '上传图片'"
                  :show-camera-button="true"
                />
              </template>
            </van-field>
          </template>
        </van-cell-group>

        <!-- 底部安全区域 -->
        <div class="safe-area-bottom"></div>
      </van-form>
    </div>



    <!-- 采购日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="currentDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
        title="选择采购日期"
      />
    </van-popup>

    <!-- 报废日期选择器 -->
    <van-popup v-model:show="showRetirementPicker" position="bottom">
      <van-date-picker
        v-model="currentRetirementDate"
        @confirm="onRetirementDateConfirm"
        @cancel="showRetirementPicker = false"
        title="选择报废日期"
      />
    </van-popup>

    <!-- 扫码弹窗 -->
    <van-dialog
      v-model:show="showScanDialog"
      title="扫码添加资产"
      show-cancel-button
      @confirm="handleScanConfirm"
    >
      <div class="scan-content">
        <van-field
          v-model="scanResult"
          label="扫码结果"
          placeholder="请扫描资产二维码或条形码"
          readonly
        />
        <div class="scan-buttons">
          <van-button 
            type="primary" 
            size="small" 
            @click="startScan"
            :loading="scanning"
          >
            {{ scanning ? '扫描中...' : '开始扫描' }}
          </van-button>
          <van-button 
            size="small" 
            @click="scanResult = ''"
          >
            清空
          </van-button>
        </div>
        <div class="scan-tips">
          <p>提示：扫码后会自动填充资产信息</p>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { assetApi } from '@/api/asset'
import type { AssetCreate } from '@/types/asset'

import type { EcologyUser } from '@/types/ecology'
import type { FieldValue } from '@/types/field_value'
import type { CustomField } from '@/types/custom_field'
import { customFieldApi } from '@/api/custom_field'
import MobileFieldValueSelector from '@/mobile/components/MobileFieldValueSelector.vue'
import MobilePersonnelSelector from '@/mobile/components/MobilePersonnelSelector.vue'
import MobileFileUploader from '@/mobile/components/MobileFileUploader.vue'
import { prepareCustomFieldValues } from '@/utils/customField'

const router = useRouter()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive<AssetCreate>({
  company: '',
  name: '',
  asset_number: '',
  status: '',
  category: '',
  specification: '',
  production_number: '',
  price: undefined,
  purchase_date: '',
  retirement_date: '',
  supplier: '',
  manufacturer: '',
  purchaser: '',
  purchaser_job_number: '',
  custodian: '',
  custodian_job_number: '',
  custodian_department: '',
  user: '',
  user_job_number: '',
  user_department: '',
  location: '',
  inspector: '',
  inspector_job_number: '',
  remarks: ''
})

// 提交状态
const submitting = ref(false)

// 选择器状态
const showDatePicker = ref(false)
const showRetirementPicker = ref(false)
const showScanDialog = ref(false)

// 扫码相关
const scanning = ref(false)
const scanResult = ref('')

// 日期状态
const currentDate = ref<string[]>([])
const currentRetirementDate = ref<string[]>([])

// 自定义字段相关
const customFields = ref<CustomField[]>([])
const customFieldData = reactive<Record<string, any>>({})
const currentCustomField = ref<CustomField | null>(null)
const showCustomFieldPicker = ref(false)
const showCustomDateSelector = ref(false)

// 返回上一页
const goBack = async () => {
  try {
    await showConfirmDialog({
      title: '确认离开',
      message: '当前有未保存的内容，确定要离开吗？'
    })
    router.back()
  } catch {
    // 用户取消
  }
}

// 采购日期确认
const onDateConfirm = ({ selectedValues }: any) => {
  currentDate.value = selectedValues
  formData.purchase_date = selectedValues.join('-')
  showDatePicker.value = false
}

// 报废日期确认
const onRetirementDateConfirm = ({ selectedValues }: any) => {
  currentRetirementDate.value = selectedValues
  formData.retirement_date = selectedValues.join('-')
  showRetirementPicker.value = false
}

// 字段值选择处理
const handleCompanyChange = (value: string, option?: FieldValue) => {
  formData.company = value
  // 可以在这里处理公司选择后的逻辑
}

// 人员选择处理
const handlePurchaserSelect = (person: EcologyUser) => {
  formData.purchaser = person.user_name || person.UserName || ''
  formData.purchaser_job_number = person.job_number || person.JobNumber || ''
}

const handleCustodianSelect = (person: EcologyUser) => {
  formData.custodian = person.user_name || person.UserName || ''
  formData.custodian_job_number = person.job_number || person.JobNumber || ''
  formData.custodian_department = person.dept_name || person.DeptName || ''
}

const handleUserSelect = (person: EcologyUser) => {
  formData.user = person.user_name || person.UserName || ''
  formData.user_job_number = person.job_number || person.JobNumber || ''
  formData.user_department = person.dept_name || person.DeptName || ''
}

const handleInspectorSelect = (person: EcologyUser) => {
  formData.inspector = person.user_name || person.UserName || ''
  formData.inspector_job_number = person.job_number || person.JobNumber || ''
}

// 自定义字段处理方法
const loadCustomFields = async () => {
  try {
    const response = await customFieldApi.getActiveCustomFields({ applies_to: 'asset' })
    customFields.value = response.data || response || []
    
    // 初始化自定义字段数据
    customFields.value.forEach(field => {
      if (!(field.name in customFieldData)) {
        customFieldData[field.name] = field.default_value || ''
      }
    })
  } catch (error) {
    console.error('加载自定义字段失败:', error)
  }
}

// 显示自定义字段选择器
const showFieldPicker = (field: CustomField) => {
  currentCustomField.value = field
  showCustomFieldPicker.value = true
}

// 显示自定义日期选择器
const openCustomDateSelector = (field: CustomField) => {
  currentCustomField.value = field
  showCustomDateSelector.value = true
}

// 保存自定义字段值
const saveCustomFieldValues = async (assetId: number) => {
  try {
    if (customFields.value.length === 0) {
      return
    }

    // 使用工具函数准备数据
    const fieldValues = prepareCustomFieldValues(customFields.value, customFieldData)

    await customFieldApi.batchSetAssetCustomFieldValues(assetId, {
      values: fieldValues
    })
  } catch (error) {
    console.error('保存自定义字段值失败:', error)
    showToast('保存自定义字段失败')
  }
}

// 开始扫描
const startScan = async () => {
  try {
    scanning.value = true
    
    // 模拟扫码过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟扫码结果
    scanResult.value = `ASSET_${Date.now()}`
    
    showToast('扫码成功')
  } catch (error) {
    console.error('扫码失败:', error)
    showToast('扫码失败，请重试')
  } finally {
    scanning.value = false
  }
}

// 扫码确认
const handleScanConfirm = () => {
  if (scanResult.value) {
    // 根据扫码结果填充表单
    formData.asset_number = scanResult.value
    formData.name = `扫码资产_${scanResult.value.slice(-6)}`
    
    showToast('已填充扫码信息')
  }
  showScanDialog.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 验证表单
    await formRef.value.validate()
    
    // 准备提交数据
    const submitData = { ...formData }
    
    // 移除空值
    Object.keys(submitData).forEach(key => {
      const value = submitData[key as keyof AssetCreate]
      if (value === '' || value === null || value === undefined) {
        delete submitData[key as keyof AssetCreate]
      }
    })
    
    // 调用API创建资产
    const response = await assetApi.createAsset(submitData)
    const newAsset = response.data || response
    
    // 保存自定义字段值
    if (newAsset?.id) {
      try {
        await saveCustomFieldValues(newAsset.id)
      } catch (error) {
        // 如果自定义字段保存失败，提示用户但不影响主流程
        console.error('保存自定义字段失败:', error)
        showToast('资产创建成功，但自定义字段保存失败')
      }
    }
    
    showSuccessToast('资产添加成功')
    
    // 跳转到资产详情页
    router.replace(`/m/asset/detail/${newAsset.id}`)
    
  } catch (error: any) {
    console.error('添加资产失败:', error)
    if (error.name === 'ValidationError') {
      showToast('请检查表单填写是否正确')
    } else {
      showToast('添加资产失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

// 组件挂载时加载自定义字段
onMounted(() => {
  loadCustomFields()
})
</script>

<style lang="scss" scoped>
.mobile-asset-add {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.form-container {
  padding-bottom: 20px;
}

.safe-area-bottom {
  height: 20px;
}

.scan-content {
  padding: 16px;
  
  .scan-buttons {
    display: flex;
    gap: 12px;
    margin: 16px 0;
    
    .van-button {
      flex: 1;
    }
  }
  
  .scan-tips {
    p {
      margin: 0;
      font-size: 12px;
      color: #969799;
      text-align: center;
    }
  }
}

:deep(.van-cell-group) {
  margin-bottom: 16px;
}

:deep(.van-field__label) {
  width: 80px;
  flex: none;
}

:deep(.van-field__control) {
  text-align: left;
}

// 优化表单样式
:deep(.van-field) {
  padding: 12px 16px;
}

:deep(.van-field--readonly) {
  .van-field__control {
    color: #323233;
  }
}

// 优化选择器样式
:deep(.van-picker) {
  .van-picker__toolbar {
    padding: 16px;
  }
}

// 优化日期选择器样式
:deep(.van-date-picker) {
  .van-picker__toolbar {
    padding: 16px;
  }
}
</style> 