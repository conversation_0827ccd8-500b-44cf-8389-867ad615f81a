<template>
  <div class="device-test">
    <h1>设备检测测试</h1>
    <div class="info-card">
      <h2>设备信息</h2>
      <p>屏幕宽度: {{ screenWidth }}px</p>
      <p>屏幕高度: {{ screenHeight }}px</p>
      <p>是否移动端: {{ isMobile }}</p>
      <p>是否平板: {{ isTablet }}</p>
      <p>是否桌面端: {{ isDesktop }}</p>
      <p>应该使用移动端: {{ shouldUseMobile }}</p>
      <p>平台类型: {{ platform }}</p>
    </div>
    
    <div class="actions">
      <button @click="goToMobile">跳转到移动端</button>
      <button @click="goToDesktop">跳转到桌面端</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useDevice } from '@/composables/useDevice'
import { usePlatform } from '@/composables/usePlatform'

const router = useRouter()
const { screenWidth, screenHeight, isMobile, isTablet, isDesktop } = useDevice()
const { shouldUseMobile, platform } = usePlatform()

const goToMobile = () => {
  router.push('/m/apps')
}

const goToDesktop = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.device-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.info-card {
  background: #f5f5f5;
  padding: 16px;
  margin: 16px 0;
  border-radius: 8px;
}

.info-card h2 {
  margin-top: 0;
  color: #333;
}

.info-card p {
  margin: 8px 0;
  font-family: monospace;
}

.actions {
  margin-top: 20px;
}

.actions button {
  margin-right: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.actions button:hover {
  background: #66b1ff;
}
</style> 