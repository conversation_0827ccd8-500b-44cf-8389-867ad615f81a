<template>
  <div class="personnel-info-container">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><User /></el-icon>
        <h2 class="page-title">人员信息</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>基础信息管理</el-breadcrumb-item>
        <el-breadcrumb-item>人员信息</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 同步状态和配置对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="泛微数据同步设置"
      width="500px"
      destroy-on-close
    >
      <div class="sync-dialog-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="同步状态">
            <el-tag :type="getSyncStatusType(syncStatus.sync_status)">
              {{ syncStatus.sync_status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上次同步时间">
            {{ syncStatus.last_sync_time ? formatDateTime(syncStatus.last_sync_time) : '从未同步' }}
          </el-descriptions-item>
          <el-descriptions-item label="下次同步时间">
            {{ syncStatus.next_sync_time ? formatDateTime(syncStatus.next_sync_time) : '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="同步时间">
            <el-time-picker
              v-model="syncTime"
              format="HH:mm"
              placeholder="选择同步时间"
              size="small"
            />
          </el-descriptions-item>
          <el-descriptions-item v-if="syncStatus.error_message" label="错误信息">
            <div class="error-message">{{ syncStatus.error_message }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <Authority permission="basic-info:personnel:edit">
            <el-button type="primary" @click="updateSyncTime">
              保存设置
            </el-button>
          </Authority>
          <Authority permission="basic-info:personnel:edit">
            <el-button type="success" @click="manualSync">
              <el-icon><RefreshRight /></el-icon> 立即同步
            </el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 导出配置对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出人员数据"
      width="400px"
      destroy-on-close
    >
      <div class="export-dialog-content">
        <el-form label-position="top">
          <el-form-item label="导出范围">
            <el-radio-group v-model="exportConfig.scope">
              <el-radio label="current">当前筛选数据</el-radio>
              <el-radio label="all">全部数据</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <Authority permission="basic-info:personnel:view">
            <el-button type="primary" @click="confirmExport">
              <el-icon><Download /></el-icon> 确认导出
            </el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <el-row :gutter="12" class="h-full">
      <!-- 左侧部门树 -->
      <el-col :span="6" class="h-full">
        <div class="aside-container">
          <div class="aside-header">
            <span class="aside-title">组织架构</span>
          </div>
          <div class="aside-content">
            <div v-if="!loading" class="tree-wrapper">
              <el-tree
                ref="deptTreeRef"
                :data="organizationTree"
                node-key="id"
                :props="{ label: 'name', children: 'children' }"
                highlight-current
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <el-icon v-if="data.type === 'company'"><OfficeBuilding /></el-icon>
                    <el-icon v-else><Folder /></el-icon>
                    <span class="ml-1">{{ node.label }}</span>
                    <span class="dept-count" v-if="data.type === 'dept' && getDeptUserCount(data.id) > 0">
                      ({{ getDeptUserCount(data.id) }})
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
            <div v-else class="loading-placeholder">
              <el-skeleton :rows="10" animated />
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧人员列表 -->
      <el-col :span="18" class="h-full">
        <div class="main-container">
          <div class="main-header">
            <div class="header-left">
              <span class="section-title">人员列表</span>
              <el-tooltip v-if="selectedNode"
                :content="selectedNodeName"
                placement="top"
                :disabled="isTooltipDisabled"
              >
                <el-tag type="info" class="current-dept ml-2" size="large">
                  <span class="dept-label">当前选择:</span>
                  <span class="dept-name-text">{{ selectedNodeName }}</span>
                  <span v-if="selectedDeptId" class="dept-id-text ml-1">(ID: {{ selectedDeptId }})</span>
                </el-tag>
              </el-tooltip>
            </div>
            <div class="header-right">
              <el-input
                v-model="searchQuery"
                placeholder="搜索姓名、工号、职位..."
                class="search-input"
                @update:model-value="handleSearch"
                clearable
                :prefix-icon="Search"
              >
              </el-input>

              <el-dropdown trigger="click">
                <el-button type="primary" plain class="menu-button">
                  <el-icon><Connection /></el-icon>
                  功能菜单
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <Authority permission="basic-info:personnel:view">
                      <el-dropdown-item @click="refreshData">
                        <el-icon><Refresh /></el-icon>
                        <span>刷新数据</span>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="basic-info:personnel:edit">
                      <el-dropdown-item @click="fetchDirectData">
                        <el-icon><Connection /></el-icon>
                        <span>从泛微获取最新数据</span>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="basic-info:personnel:edit">
                      <el-dropdown-item @click="openSyncDialog">
                        <el-icon><Setting /></el-icon>
                        <span>同步设置</span>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="basic-info:personnel:view">
                      <el-dropdown-item @click="exportPersonnelData">
                        <el-icon><Download /></el-icon>
                        <span>导出数据</span>
                      </el-dropdown-item>
                    </Authority>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="main-content">
            <div class="table-container">
              <el-table
                v-if="!loading && selectedNode"
                :data="paginatedUserList"
                style="width: 100%"
                border
                height="calc(100vh - 220px)"
                row-class-name="personnel-table-row"
                header-row-class-name="personnel-table-header"
                header-cell-class-name="table-header-cell"
              >
                <el-table-column prop="UserName" label="姓名" min-width="100">
                  <template #default="{ row }">
                    <div class="user-info-cell">
                      <el-icon><UserFilled /></el-icon>
                      <span class="ml-1">{{ row.UserName || row.user_name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="工号" min-width="100">
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.JobNumber || row.job_number }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="公司" min-width="150">
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.CompanyName || row.company_name }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="部门" min-width="150">
                  <template #header>
                    <div class="column-header">
                      <div class="column-title-wrapper">
                        <span class="column-title">部门</span>
                        <div class="column-icons">
                          <el-tooltip content="点击筛选" placement="top">
                            <el-popover
                              placement="bottom"
                              width="240"
                              trigger="click"
                              popper-class="filter-popover"
                            >
                              <template #reference>
                                <el-button
                                  class="icon-button"
                                  :class="{ 'is-active': hasFilter('deptName') }"
                                  :type="hasFilter('deptName') ? 'primary' : ''"
                                  link
                                >
                                  <el-icon><Filter /></el-icon>
                                </el-button>
                              </template>
                              <div class="filter-content">
                                <div class="filter-header">
                                  <span class="filter-title">部门筛选</span>
                                </div>
                                <div class="filter-body">
                                  <el-select
                                    v-model="searchForm.deptName"
                                    placeholder="请选择部门"
                                    clearable
                                    filterable
                                    class="filter-input"
                                  >
                                    <el-option
                                      v-for="value in deptFilterOptions"
                                      :key="value"
                                      :label="value"
                                      :value="value"
                                    />
                                  </el-select>
                                </div>
                                <div class="filter-footer">
                                  <el-button @click="resetColumnFilter('deptName')">重置</el-button>
                                  <el-button type="primary" @click="handleSearch">确定</el-button>
                                </div>
                              </div>
                            </el-popover>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.DeptName || row.dept_name }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="职位" min-width="120">
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.JobTitleName || row.job_title_name }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="性别" min-width="80">
                  <template #header>
                    <div class="column-header">
                      <div class="column-title-wrapper">
                        <span class="column-title">性别</span>
                        <div class="column-icons">
                          <el-tooltip content="点击筛选" placement="top">
                            <el-popover
                              placement="bottom"
                              width="240"
                              trigger="click"
                              popper-class="filter-popover"
                            >
                              <template #reference>
                                <el-button
                                  class="icon-button"
                                  :class="{ 'is-active': hasFilter('gender') }"
                                  :type="hasFilter('gender') ? 'primary' : ''"
                                  link
                                >
                                  <el-icon><Filter /></el-icon>
                                </el-button>
                              </template>
                              <div class="filter-content">
                                <div class="filter-header">
                                  <span class="filter-title">性别筛选</span>
                                </div>
                                <div class="filter-body">
                                  <el-select
                                    v-model="searchForm.gender"
                                    placeholder="请选择性别"
                                    clearable
                                    class="filter-input"
                                  >
                                    <el-option
                                      v-for="value in genderFilterOptions"
                                      :key="value"
                                      :label="value"
                                      :value="value"
                                    />
                                  </el-select>
                                </div>
                                <div class="filter-footer">
                                  <el-button @click="resetColumnFilter('gender')">重置</el-button>
                                  <el-button type="primary" @click="handleSearch">确定</el-button>
                                </div>
                              </div>
                            </el-popover>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.Gender || row.gender }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="手机号" min-width="120">
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.Mobile || row.mobile }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="邮箱" min-width="180" show-overflow-tooltip>
                  <template #default="{ row }">
                    <div class="user-info-cell">{{ row.Email || row.email }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="状态" min-width="100">
                  <template #header>
                    <div class="column-header">
                      <div class="column-title-wrapper">
                        <span class="column-title">状态</span>
                        <div class="column-icons">
                          <el-tooltip content="点击筛选" placement="top">
                            <el-popover
                              placement="bottom"
                              width="240"
                              trigger="click"
                              popper-class="filter-popover"
                            >
                              <template #reference>
                                <el-button
                                  class="icon-button"
                                  :class="{ 'is-active': hasFilter('status') }"
                                  :type="hasFilter('status') ? 'primary' : ''"
                                  link
                                >
                                  <el-icon><Filter /></el-icon>
                                </el-button>
                              </template>
                              <div class="filter-content">
                                <div class="filter-header">
                                  <span class="filter-title">状态筛选</span>
                                </div>
                                <div class="filter-body">
                                  <el-select
                                    v-model="searchForm.status"
                                    placeholder="请选择状态"
                                    clearable
                                    class="filter-input"
                                  >
                                    <el-option
                                      v-for="value in statusFilterOptions"
                                      :key="value"
                                      :label="value"
                                      :value="value"
                                    />
                                  </el-select>
                                </div>
                                <div class="filter-footer">
                                  <el-button @click="resetColumnFilter('status')">重置</el-button>
                                  <el-button type="primary" @click="handleSearch">确定</el-button>
                                </div>
                              </div>
                            </el-popover>
                          </el-tooltip>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template #default="{ row }">
                    <div class="user-info-cell">
                      <el-tag :type="getStatusType(row.Status || row.status)">
                        {{ row.Status || row.status }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-if="!selectedNode" class="empty-state">
              <el-empty description="请选择一个公司或部门" />
            </div>

            <div v-if="loading" class="loading-placeholder">
              <el-skeleton :rows="10" animated />
            </div>

            <div class="pagination-container" v-if="selectedNode && filteredUserList.length > 0">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="filteredUserList.length"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
              />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount, reactive } from 'vue'
import {
  Search,
  Refresh,
  UserFilled,
  Folder,
  OfficeBuilding,
  Setting,
  RefreshRight,
  Connection,
  Filter,
  ArrowDown,
  Download,
  User
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ecologyApi } from '@/api/ecology'
import type { EcologyUser, SyncConfig } from '@/types/ecology'
import Authority from '@/components/Authority/index.vue'

interface TreeNode {
  id: number | string
  name: string
  type: 'company' | 'dept'
  children: TreeNode[]
}

const loading = ref(true)
const userList = ref<EcologyUser[]>([])
const organizationTree = ref<TreeNode[]>([])
const searchQuery = ref('')
const selectedNode = ref<{ id: number | string, type: 'company' | 'dept' } | null>(null)
const currentPage = ref(1)
const pageSize = ref(20)
const deptTreeRef = ref(null)

// 同步相关
const syncDialogVisible = ref(false)
const syncStatus = ref<SyncConfig>({
  sync_hour: 8,
  sync_time: '08:00',
  last_sync_time: null,
  next_sync_time: null,
  sync_status: '未同步',
  error_message: null
})
const syncTime = ref(new Date(2025, 1, 1, 8, 0))
let syncStatusTimer: number | null = null

// 导出相关
const exportDialogVisible = ref(false)
const exportConfig = reactive({
  scope: 'current' // 'current' 或 'all'
})

// 筛选相关
const searchForm = reactive({
  deptName: '',
  gender: '',
  status: ''
})

// 获取选中节点的名称
const selectedNodeName = computed(() => {
  if (!selectedNode.value) return ''

  if (selectedNode.value.type === 'company') {
    const companyId = parseInt(selectedNode.value.id.toString().replace('company_', ''))
    const company = userList.value.find(user =>
      (user.CompanyID === companyId) || (user.company_id === companyId)
    )
    return company ? (company.CompanyName || company.company_name) : ''
  } else {
    const deptId = Number(selectedNode.value.id)
    const dept = userList.value.find(user =>
      (user.DeptID === deptId) || (user.dept_id === deptId)
    )
    return dept ? (dept.DeptName || dept.dept_name) : ''
  }
})

// 获取选中部门的ID
const selectedDeptId = computed(() => {
  if (!selectedNode.value || selectedNode.value.type === 'company') return null
  return Number(selectedNode.value.id)
})

// 判断是否需要显示tooltip
const isTooltipDisabled = computed(() => {
  return !selectedNodeName.value || selectedNodeName.value.length <= 20
})

// 重置所有筛选条件
const resetAllFilters = (): void => {
  Object.keys(searchForm).forEach(key => {
    (searchForm[key as keyof typeof searchForm] as string) = ''
  })
  handleSearch()
}

// 获取人员数据（从本地数据库）
const fetchPersonnelData = async () => {
  loading.value = true
  try {
    // 先尝试获取本地数据
    const response = await ecologyApi.getLocalEcologyUsers({
      skip: 0,
      limit: 100000 // 获取所有数据，增大限制
    })

    // 检查数据是否为空或数量很少
    if (!response.data || response.data.length < 10) {
      // 如果本地数据为空或很少，尝试手动触发同步
      try {
        await ecologyApi.syncEcologyUsers()
        ElMessage.info('正在同步数据，请稍候...')

        // 等待3秒后重新获取数据
        setTimeout(async () => {
          try {
            const newResponse = await ecologyApi.getLocalEcologyUsers({
              skip: 0,
              limit: 100000
            })
            userList.value = newResponse.data
            buildOrganizationTree()
          } catch (error) {
            console.error('重新获取数据失败:', error)
          } finally {
            loading.value = false
          }
        }, 3000)
        return
      } catch (syncError) {
        console.error('触发同步失败:', syncError)
        // 同步失败，继续使用原有数据
      }
    }

    userList.value = response.data
    buildOrganizationTree()
    loading.value = false

    // 获取同步状态
    fetchSyncStatus()
  } catch (error) {
    console.error('获取人员数据失败:', error)
    ElMessage.error('获取人员数据失败')
    loading.value = false

    // 尝试直接从泛微数据库获取数据
    try {
      const directResponse = await ecologyApi.getEcologyUsers()
      userList.value = directResponse.data
      buildOrganizationTree()
      ElMessage.info('已从泛微数据库直接获取数据')
    } catch (directError) {
      console.error('直接获取数据失败:', directError)
      ElMessage.error('无法获取人员数据，请稍后再试')
    } finally {
      loading.value = false
    }
  }
}

// 获取同步状态
const fetchSyncStatus = async () => {
  try {
    const response = await ecologyApi.getSyncStatus()
    syncStatus.value = response.data

    // 设置时间选择器的值
    if (response.data.sync_time) {
      const [hours, minutes] = response.data.sync_time.split(':').map(Number)
      syncTime.value = new Date(2025, 1, 1, hours, minutes)
    }
  } catch (error) {
    console.error('获取同步状态失败:', error)
  }
}

// 定时获取同步状态
const startSyncStatusTimer = () => {
  // 每5分钟检查一次同步状态
  syncStatusTimer = window.setInterval(fetchSyncStatus, 300000)
}

// 手动触发同步
const manualSync = async () => {
  try {
    await ecologyApi.syncEcologyUsers()
    ElMessage.success('同步任务已启动')

    // 立即获取最新状态
    setTimeout(fetchSyncStatus, 1000)

    // 3秒后刷新数据
    setTimeout(() => {
      fetchPersonnelData()
    }, 3000)
  } catch (error) {
    console.error('触发同步失败:', error)
    ElMessage.error('触发同步失败')
  }
}

// 更新同步时间
const updateSyncTime = async () => {
  try {
    // 格式化时间为 HH:MM 格式
    const hours = syncTime.value.getHours().toString().padStart(2, '0')
    const minutes = syncTime.value.getMinutes().toString().padStart(2, '0')
    const timeString = `${hours}:${minutes}`

    await ecologyApi.updateSyncTime(timeString)
    ElMessage.success('同步时间已更新')
    fetchSyncStatus()
  } catch (error) {
    console.error('更新同步时间失败:', error)
    ElMessage.error('更新同步时间失败')
  }
}

// 打开同步对话框
const openSyncDialog = () => {
  syncDialogVisible.value = true
  fetchSyncStatus()
}

// 获取同步状态标签类型
const getSyncStatusType = (status: string) => {
  switch (status) {
    case '同步成功': return 'success'
    case '同步中': return 'warning'
    case '同步失败': return 'danger'
    case '未同步': return 'info'
    default: return 'info'
  }
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 构建组织架构树（公司+部门）
const buildOrganizationTree = () => {
  // 创建公司映射
  const companies = new Map<number, TreeNode>()
  const companyTree: TreeNode[] = []

  // 创建部门映射
  const departments = new Map<number, TreeNode>()

  // 首先收集所有公司
  userList.value.forEach(user => {
    const companyId = user.CompanyID || user.company_id
    if (companyId && !companies.has(companyId)) {
      const companyNodeId = `company_${companyId}`
      companies.set(companyId, {
        id: companyNodeId,
        name: (user.CompanyName || user.company_name) || '未知公司',
        type: 'company',
        children: []
      })
    }
  })

  // 然后收集所有部门，无论是否有关联用户
  userList.value.forEach(user => {
    const deptId = user.DeptID || user.dept_id
    if (deptId && !departments.has(deptId)) {
      departments.set(deptId, {
        id: deptId,
        name: user.DeptName || user.dept_name || '',
        type: 'dept',
        children: []
      })
    }
  })

  // 构建部门树结构
  departments.forEach((dept, deptId) => {
    // 查找该部门的信息
    const user = userList.value.find(u => (u.DeptID === deptId) || (u.dept_id === deptId))
    if (!user) return // 如果找不到部门信息，跳过

    const deptPath = user.DeptPath || user.dept_path
    if (!deptPath) return // 如果没有部门路径，跳过

    const pathParts = deptPath.split(',')

    if (pathParts.length === 1) {
      // 根部门，添加到对应的公司下
      const companyId = user.CompanyID || user.company_id
      if (companyId) {
        const company = companies.get(companyId)
        if (company) {
          company.children.push(dept)
        }
      }
    } else if (pathParts.length > 1) {
      // 子部门
      const parentId = parseInt(pathParts[pathParts.length - 2])
      const parentDept = departments.get(parentId)
      if (parentDept) {
        // 避免重复添加
        if (!parentDept.children.some(child => child.id === dept.id)) {
          parentDept.children.push(dept)
        }
      } else {
        // 如果找不到父部门，尝试添加到公司下
        const companyId = user.CompanyID || user.company_id
        if (companyId) {
          const company = companies.get(companyId)
          if (company) {
            company.children.push(dept)
          }
        }
      }
    }
  })

  // 对公司和部门进行排序
  const sortChildren = (node: TreeNode) => {
    node.children.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
    node.children.forEach(child => sortChildren(child))
  }

  // 将公司添加到树中
  companies.forEach(company => {
    sortChildren(company)
    companyTree.push(company)
  })

  companyTree.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
  organizationTree.value = companyTree
}

// 处理节点点击
const handleNodeClick = (data: TreeNode) => {
  selectedNode.value = {
    id: data.id,
    type: data.type
  }
  currentPage.value = 1
}

// 处理搜索
const handleSearch = () => {
  // 重置分页
  currentPage.value = 1
}

// 刷新数据
const refreshData = async () => {
  // 重置所有筛选条件
  resetAllFilters()
  // 重置搜索关键词
  searchQuery.value = ''
  // 重新获取数据
  await fetchPersonnelData()
}

// 获取部门下的用户数量
const getDeptUserCount = (deptId: number | string) => {
  const numericId = typeof deptId === 'string' ? parseInt(deptId) : deptId
  return userList.value.filter(user => {
    // 检查是否属于该部门
    const isDeptMatch = (user.DeptID === numericId) || (user.dept_id === numericId);
    // 检查是否有用户ID
    const hasUserId = Boolean(user.UserID) || Boolean(user.user_id);
    return isDeptMatch && hasUserId;
  }).length
}

// 获取状态标签类型
const getStatusType = (status: string | null) => {
  if (!status) return ''
  switch (status) {
    case '正式': return 'success'
    case '试用': return 'warning'
    case '临时': return 'info'
    case '试用延期': return 'warning'
    case '解聘': return 'danger'
    case '离职': return 'danger'
    case '退休': return 'info'
    case '无效': return 'danger'
    default: return ''
  }
}

// 筛选选项
const deptFilterOptions = computed(() => {
  const options = new Set<string>()
  userList.value.forEach(user => {
    const dept = user.DeptName || user.dept_name
    if (dept) options.add(dept)
  })
  return Array.from(options)
})

const genderFilterOptions = computed(() => {
  const options = new Set<string>()
  userList.value.forEach(user => {
    const gender = user.Gender || user.gender
    if (gender) options.add(gender)
  })
  return Array.from(options)
})

const statusFilterOptions = computed(() => {
  const options = new Set<string>()
  userList.value.forEach(user => {
    const status = user.Status || user.status
    if (status) options.add(status)
  })
  return Array.from(options)
})

// 判断列是否有过滤条件
const hasFilter = (prop: string): boolean => {
  return !!searchForm[prop as keyof typeof searchForm]
}

// 重置单个列的过滤条件
const resetColumnFilter = (prop: string): void => {
  if (prop in searchForm) {
    (searchForm[prop as keyof typeof searchForm] as string) = ''
  }
  handleSearch()
}

// 修改筛选后的用户列表计算属性
const filteredUserList = computed(() => {
  if (!selectedNode.value) return []

  // 先按照部门或公司筛选
  let filtered = [...userList.value]

  if (selectedNode.value.type === 'company') {
    const companyId = parseInt(selectedNode.value.id.toString().replace('company_', ''))
    filtered = filtered.filter(user =>
      (user.CompanyID === companyId) || (user.company_id === companyId)
    )
  } else {
    const deptId = Number(selectedNode.value.id)
    filtered = filtered.filter(user =>
      (user.DeptID === deptId) || (user.dept_id === deptId)
    )
  }

  // 根据搜索关键字筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user => {
      return (
        (user.UserName || user.user_name || '').toLowerCase().includes(query) ||
        (user.JobNumber || user.job_number || '').toLowerCase().includes(query) ||
        (user.JobTitleName || user.job_title_name || '').toLowerCase().includes(query) ||
        (user.Mobile || user.mobile || '').toLowerCase().includes(query) ||
        (user.Email || user.email || '').toLowerCase().includes(query)
      )
    })
  }

  // 根据高级筛选条件筛选
  if (searchForm.deptName) {
    filtered = filtered.filter(user =>
      (user.DeptName === searchForm.deptName) || (user.dept_name === searchForm.deptName)
    )
  }

  if (searchForm.gender) {
    filtered = filtered.filter(user =>
      (user.Gender === searchForm.gender) || (user.gender === searchForm.gender)
    )
  }

  if (searchForm.status) {
    filtered = filtered.filter(user =>
      (user.Status === searchForm.status) || (user.status === searchForm.status)
    )
  }

  return filtered
})

// 分页相关方法
const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 计算当前页的数据
const paginatedUserList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUserList.value.slice(start, end)
})

// 直接从泛微数据库获取数据
const fetchDirectData = async () => {
  loading.value = true
  try {
    const response = await ecologyApi.getEcologyUsers()
    userList.value = response.data
    buildOrganizationTree()
    ElMessage.success('已从泛微数据库获取最新数据')

    // 重置筛选和搜索
    resetAllFilters()
    searchQuery.value = ''

    // 如果有选中节点，保持选中状态
    if (selectedNode.value) {
      const nodeId = selectedNode.value.id
      const nodeType = selectedNode.value.type
      // 重新选中节点
      selectedNode.value = { id: nodeId, type: nodeType }
    }
  } catch (error) {
    console.error('直接获取数据失败:', error)
    ElMessage.error('无法从泛微数据库获取数据，请检查连接')
  } finally {
    loading.value = false
  }
}

// 导出人员数据
const exportPersonnelData = async () => {
  // 打开导出配置对话框
  exportDialogVisible.value = true
}

// 确认导出
const confirmExport = async () => {
  try {
    exportDialogVisible.value = false

    // 根据选择的导出范围决定导出方式
    if (exportConfig.scope === 'current' && filteredUserList.value.length > 0) {
      // 导出当前筛选的数据
      interface ExportRow {
        姓名: string;
        工号: string;
        公司: string;
        部门: string;
        职位: string;
        性别: string;
        手机号: string;
        邮箱: string;
        状态: string;
        [key: string]: string;  // 索引签名
      }

      const exportData: ExportRow[] = filteredUserList.value.map(user => ({
        姓名: user.UserName || user.user_name || '',
        工号: user.JobNumber || user.job_number || '',
        公司: user.CompanyName || user.company_name || '',
        部门: user.DeptName || user.dept_name || '',
        职位: user.JobTitleName || user.job_title_name || '',
        性别: user.Gender || user.gender || '',
        手机号: user.Mobile || user.mobile || '',
        邮箱: user.Email || user.email || '',
        状态: user.Status || user.status || ''
      }));

      // 客户端导出Excel使用ExcelJS
      const ExcelJS = (await import('exceljs')).default;
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('人员信息');

      // 添加表头
      const headers = Object.keys(exportData[0] || {});
      worksheet.addRow(headers);

      // 添加数据行
      exportData.forEach(row => {
        const values = headers.map(header => row[header] || '');
        worksheet.addRow(values);
      });

      // 计算并设置列宽
      headers.forEach((header, index) => {
        const column = worksheet.getColumn(index + 1);
        const maxLength = Math.max(
          10, // 最小宽度
          header.length,
          ...exportData.map(row => (row[header]?.toString() || '').length)
        );
        column.width = maxLength;
      });

      // 导出文件
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      const now = new Date().toLocaleDateString().replace(/\//g, '-');
      link.download = `人员信息_${now}.xlsx`;
      link.click();
      URL.revokeObjectURL(url);
      ElMessage.success('人员数据已成功导出');
    } else {
      // 导出所有数据（使用后端API）
      const response = await ecologyApi.exportEcologyUsers();
      const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = '人员信息.xlsx';
      link.click();
      URL.revokeObjectURL(link.href);
      ElMessage.success('人员数据已成功导出');
    }
  } catch (error) {
    console.error('导出人员数据失败:', error);
    ElMessage.error('导出人员数据失败');
  }
}

onMounted(() => {
  fetchPersonnelData()
  startSyncStatusTimer()
})

onBeforeUnmount(() => {
  // 清除定时器
  if (syncStatusTimer) {
    clearInterval(syncStatusTimer)
  }
})
</script>

<style scoped lang="scss">
.personnel-info-container {
  padding: 20px;
  height: calc(100vh - 100px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.h-full {
  height: 100%;
}

.aside-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.aside-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.aside-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.aside-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.tree-wrapper {
  height: 100%;
  overflow: auto;
}

.main-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.main-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 250px;
  margin-right: 10px;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-placeholder {
  padding: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  flex: 1;
  padding-right: 8px;
}

.dept-count {
  font-size: 12px;
  color: #909399;
  margin-left: 4px;
}

.user-info-cell {
  display: flex;
  align-items: center;
  height: 24px;
  line-height: 24px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 5px;
  width: 100%;
}

.user-name-cell {
  display: flex;
  align-items: center;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.current-dept {
  font-weight: normal;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dept-label {
  white-space: nowrap;
  margin-right: 5px;
  flex-shrink: 0;
}

.dept-name-text {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.dept-id-text {
  color: #909399;
  font-size: 13px;
  white-space: nowrap;
}

.sync-dialog-content {
  padding: 10px 0;

  .error-message {
    color: #f56c6c;
    word-break: break-word;
    max-height: 100px;
    overflow-y: auto;
  }
}

.export-dialog-content {
  padding: 10px 0;
}

.table-row {
  transition: all 0.3s;
  height: 56px;
}

.table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.personnel-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.dropdown-item-text {
  margin-left: 5px;
}

.settings-text {
  margin: 0 4px;
}

@media screen and (max-width: 1200px) {
  .header-center {
    max-width: calc(100% - 120px);
    order: 3;
    width: 100%;
    justify-content: flex-start;
    padding-left: 0;
    margin-top: 10px;
  }

  .header-left {
    order: 1;
  }

  .header-right {
    order: 2;
  }

  .current-dept {
    width: 100%;
  }
}

@media screen and (max-width: 768px) {
  .header-right {
    width: 100%;
    order: 3;
    margin-top: 10px;
  }

  .header-center {
    order: 2;
    margin-top: 10px;
  }

  .search-input {
    flex: 1;
  }

  .main-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
    padding: 0;
  }
}

// 筛选样式
.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 5px;
  height: 100%;

  .column-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .column-title {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .column-icons {
      display: flex;
      align-items: center;

      .icon-button {
        font-size: 14px;
        margin-left: 5px;

        &.is-active {
          color: var(--el-color-primary);
        }
      }
    }
  }
}

.filter-popover {
  min-width: 200px;

  .filter-content {
    padding: 10px;

    .filter-header {
      margin-bottom: 15px;

      .filter-title {
        font-weight: bold;
        font-size: 14px;
      }
    }

    .filter-body {
      margin-bottom: 15px;

      .filter-input {
        width: 100%;
      }
    }

    .filter-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}

// 功能菜单样式
.menu-button {
  display: inline-flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 14px;
  color: var(--el-button-text-color);
  border-color: var(--el-button-border-color);
  transition: all 0.3s;
}

.menu-button:hover {
  background-color: var(--el-color-primary) !important;
  color: white !important;
  border-color: var(--el-color-primary) !important;
}

.menu-button:hover .menu-icon,
.menu-button:hover .el-icon--right {
  color: white !important;
}

.menu-icon {
  margin-right: 5px;
  font-size: 16px;
}
</style>