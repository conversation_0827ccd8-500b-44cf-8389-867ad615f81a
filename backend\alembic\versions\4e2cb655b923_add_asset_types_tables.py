"""Add asset types tables

Revision ID: 4e2cb655b923
Revises: ecology_users_and_sync_config
Create Date: 2025-02-28 11:13:52.189815

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '4e2cb655b923'
down_revision: Union[str, None] = 'ecology_users_and_sync_config'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_brands',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='品牌名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='描述'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_asset_brands_id'), 'asset_brands', ['id'], unique=False)
    op.create_table('asset_statuses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='状态名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='描述'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_asset_statuses_id'), 'asset_statuses', ['id'], unique=False)
    op.create_table('asset_types',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='类型名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='描述'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_asset_types_id'), 'asset_types', ['id'], unique=False)
    op.create_table('asset_models',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='型号名称'),
    sa.Column('brand_id', sa.Integer(), nullable=False, comment='品牌ID'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='描述'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['brand_id'], ['asset_brands.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_models_id'), 'asset_models', ['id'], unique=False)
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.drop_column('inventory_records', 'new_user_department')
    op.drop_column('inventory_records', 'new_location')
    op.drop_column('inventory_records', 'new_custodian_department')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('inventory_records', sa.Column('new_custodian_department', sa.VARCHAR(length=100), nullable=True))
    op.add_column('inventory_records', sa.Column('new_location', sa.VARCHAR(length=200), nullable=True))
    op.add_column('inventory_records', sa.Column('new_user_department', sa.VARCHAR(length=100), nullable=True))
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.drop_index(op.f('ix_asset_models_id'), table_name='asset_models')
    op.drop_table('asset_models')
    op.drop_index(op.f('ix_asset_types_id'), table_name='asset_types')
    op.drop_table('asset_types')
    op.drop_index(op.f('ix_asset_statuses_id'), table_name='asset_statuses')
    op.drop_table('asset_statuses')
    op.drop_index(op.f('ix_asset_brands_id'), table_name='asset_brands')
    op.drop_table('asset_brands')
    # ### end Alembic commands ###
