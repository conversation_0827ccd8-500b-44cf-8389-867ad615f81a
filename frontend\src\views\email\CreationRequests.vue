<template>
  <div class="creation-requests">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><DocumentChecked /></el-icon>
        <h2 class="page-title">邮箱申请管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>邮箱申请管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 统计信息卡片 -->
    <el-card class="stats-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><DataBoard /></el-icon>
            申请统计
          </span>
          <el-button type="primary" size="small" @click="loadStats" :loading="statsLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.pending_count }}</div>
              <div class="stat-label">待审批</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon approved">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.approved_count }}</div>
              <div class="stat-label">已批准</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon created">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.created_count }}</div>
              <div class="stat-label">已创建</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon quota">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.available_quota }}</div>
              <div class="stat-label">可用名额</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 申请列表 -->
    <el-card class="list-card" shadow="hover" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><List /></el-icon>
            申请列表
          </span>
        </div>
      </template>

      <!-- 搜索和筛选区域 -->
      <div class="search-area">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filterStatus" placeholder="申请状态" clearable @change="loadRequests">
              <el-option label="全部" value="" />
              <el-option label="待审批" value="pending" />
              <el-option label="已批准" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="已创建" value="created" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="searchText"
              placeholder="搜索工号或姓名"
              clearable
              @keyup.enter="loadRequests"
            >
              <template #append>
                <el-button @click="loadRequests">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-col>
          <el-col :span="12">
            <div class="action-buttons">
              <Authority permission="email:request:approve">
                <el-button
                  type="success"
                  :disabled="selectedRequests.length === 0"
                  @click="handleBatchApprove('approve')"
                >
                  <el-icon><Check /></el-icon>
                  批量批准
                </el-button>
                <el-button
                  type="danger"
                  :disabled="selectedRequests.length === 0"
                  @click="handleBatchApprove('reject')"
                >
                  <el-icon><Close /></el-icon>
                  批量拒绝
                </el-button>
              </Authority>
              <el-button type="primary" @click="loadRequests" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新列表
              </el-button>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 申请表格 -->
      <el-table
        :data="requestList"
        border
        stripe
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="margin-top: 20px;"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="job_number" label="工号" width="120" />
        <el-table-column prop="user_name" label="姓名" width="120" />
        <el-table-column prop="dept_name" label="部门" width="150" show-overflow-tooltip />
        <el-table-column prop="job_title_name" label="职位" width="120" show-overflow-tooltip />
        <el-table-column prop="mobile" label="手机号" width="130" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="requested_by" label="申请人" width="120" />
        <el-table-column prop="created_at" label="申请时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="approved_by" label="审批人" width="120" />
        <el-table-column prop="created_email" label="创建邮箱" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <Authority permission="email:request:view">
              <el-button type="primary" size="small" @click="viewDetails(scope.row)">
                查看
              </el-button>
            </Authority>
            <Authority permission="email:request:approve">
              <el-button
                v-if="scope.row.status === 'pending'"
                type="success"
                size="small"
                @click="handleApprove(scope.row, 'approve')"
              >
                批准
              </el-button>
              <el-button
                v-if="scope.row.status === 'pending'"
                type="danger"
                size="small"
                @click="handleApprove(scope.row, 'reject')"
              >
                拒绝
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-area">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadRequests"
          @current-change="loadRequests"
        />
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="申请详情"
      width="600px"
    >
      <div v-if="currentRequest" class="request-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请ID">{{ currentRequest.id }}</el-descriptions-item>
          <el-descriptions-item label="工号">{{ currentRequest.job_number }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ currentRequest.user_name }}</el-descriptions-item>
          <el-descriptions-item label="部门">{{ currentRequest.dept_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="职位">{{ currentRequest.job_title_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentRequest.mobile || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusType(currentRequest.status)">
              {{ getStatusText(currentRequest.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentRequest.requested_by }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(currentRequest.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="审批人">{{ currentRequest.approved_by || '-' }}</el-descriptions-item>
          <el-descriptions-item label="审批时间">
            {{ currentRequest.approved_at ? formatDate(currentRequest.approved_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建邮箱">{{ currentRequest.created_email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请原因" :span="2">
            {{ currentRequest.reason || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ currentRequest.notes || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <Authority permission="email:request:approve">
          <el-button
            v-if="currentRequest?.status === 'pending'"
            type="success"
            @click="handleApprove(currentRequest, 'approve')"
          >
            批准申请
          </el-button>
          <el-button
            v-if="currentRequest?.status === 'pending'"
            type="danger"
            @click="handleApprove(currentRequest, 'reject')"
          >
            拒绝申请
          </el-button>
        </Authority>
      </template>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      :title="approvalAction === 'approve' ? '批准申请' : '拒绝申请'"
      width="500px"
    >
      <el-form :model="approvalForm" label-width="80px">
        <el-form-item label="操作类型">
          <span>{{ approvalAction === 'approve' ? '批准申请' : '拒绝申请' }}</span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="approvalForm.notes"
            type="textarea"
            :rows="4"
            :placeholder="approvalAction === 'approve' ? '请输入批准原因（可选）' : '请输入拒绝原因'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="approvalDialogVisible = false">取消</el-button>
        <el-button
          :type="approvalAction === 'approve' ? 'success' : 'danger'"
          @click="confirmApproval"
          :loading="approvalLoading"
        >
          确认{{ approvalAction === 'approve' ? '批准' : '拒绝' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentChecked,
  DataBoard,
  Clock,
  Check,
  UserFilled,
  Monitor,
  List,
  Search,
  Refresh,
  Close
} from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'
import {
  getCreationRequests,
  getRequestsStats,
  approveCreationRequest,
  batchApproveRequests
} from '@/api/email/creation-requests'
import type {
  EmailCreationRequest,
  RequestStats,
  ApprovalAction
} from '@/types/email'

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const approvalLoading = ref(false)
const detailDialogVisible = ref(false)
const approvalDialogVisible = ref(false)

const requestList = ref<EmailCreationRequest[]>([])
const selectedRequests = ref<EmailCreationRequest[]>([])
const currentRequest = ref<EmailCreationRequest | null>(null)

const stats = reactive<RequestStats>({
  pending_count: 0,
  approved_count: 0,
  rejected_count: 0,
  created_count: 0,
  total_requests: 0,
  active_email_count: 0,
  email_quota: 1000,
  available_quota: 1000
})

// 搜索和筛选
const filterStatus = ref('')
const searchText = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 审批相关
const approvalAction = ref<'approve' | 'reject'>('approve')
const approvalForm = reactive({
  notes: ''
})
const currentApprovalRequest = ref<EmailCreationRequest | null>(null)
const batchApprovalMode = ref(false)

// 加载统计信息
const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await getRequestsStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计信息失败:', error)
    ElMessage.error('加载统计信息失败')
  } finally {
    statsLoading.value = false
  }
}

// 加载申请列表
const loadRequests = async () => {
  try {
    loading.value = true
    const params = {
      status: filterStatus.value || undefined,
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }
    
    const response = await getCreationRequests(params)
    const responseData = response.data
    requestList.value = responseData.data
    total.value = responseData.total || 0
  } catch (error) {
    console.error('加载申请列表失败:', error)
    ElMessage.error('加载申请列表失败')
  } finally {
    loading.value = false
  }
}

// 表格选择变化
const handleSelectionChange = (selection: EmailCreationRequest[]) => {
  selectedRequests.value = selection
}

// 查看详情
const viewDetails = (request: EmailCreationRequest) => {
  currentRequest.value = request
  detailDialogVisible.value = true
}

// 处理审批
const handleApprove = (request: EmailCreationRequest, action: 'approve' | 'reject') => {
  currentApprovalRequest.value = request
  approvalAction.value = action
  approvalForm.notes = ''
  batchApprovalMode.value = false
  approvalDialogVisible.value = true
}

// 处理批量审批
const handleBatchApprove = (action: 'approve' | 'reject') => {
  if (selectedRequests.value.length === 0) {
    ElMessage.warning('请先选择要操作的申请')
    return
  }

  // 检查是否都是待审批状态
  const pendingRequests = selectedRequests.value.filter(req => req.status === 'pending')
  if (pendingRequests.length !== selectedRequests.value.length) {
    ElMessage.warning('只能审批状态为"待审批"的申请')
    return
  }

  approvalAction.value = action
  approvalForm.notes = ''
  batchApprovalMode.value = true
  approvalDialogVisible.value = true
}

// 确认审批
const confirmApproval = async () => {
  try {
    approvalLoading.value = true

    if (batchApprovalMode.value) {
      // 批量审批
      const requestIds = selectedRequests.value.map(req => req.id)
      await batchApproveRequests({
        request_ids: requestIds,
        action: approvalAction.value,
        notes: approvalForm.notes || undefined
      })
      
      ElMessage.success(`批量${approvalAction.value === 'approve' ? '批准' : '拒绝'}成功`)
    } else {
      // 单个审批
      if (!currentApprovalRequest.value) return
      
      await approveCreationRequest(currentApprovalRequest.value.id, {
        action: approvalAction.value,
        notes: approvalForm.notes || undefined
      })
      
      ElMessage.success(`${approvalAction.value === 'approve' ? '批准' : '拒绝'}申请成功`)
    }

    approvalDialogVisible.value = false
    detailDialogVisible.value = false
    await loadRequests()
    await loadStats()
  } catch (error: any) {
    console.error('审批失败:', error)
    ElMessage.error(error.response?.data?.detail || '审批失败')
  } finally {
    approvalLoading.value = false
  }
}

// 状态相关辅助函数
const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    created: 'success'
  }
  return statusMap[status as keyof typeof statusMap] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待审批',
    approved: '已批准',
    rejected: '已拒绝',
    created: '已创建'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadStats()
  loadRequests()
})
</script>

<style scoped>
.creation-requests {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: #409eff;
}

.page-title {
  margin: 0;
  color: #303133;
}

.stats-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.header-title .el-icon {
  margin-right: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-item:hover {
  background: #e9ecef;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
}

.stat-icon.pending {
  background: #fef0e6;
  color: #e6a23c;
}

.stat-icon.approved {
  background: #f0f9f0;
  color: #67c23a;
}

.stat-icon.created {
  background: #e6f7ff;
  color: #409eff;
}

.stat-icon.quota {
  background: #f5f5f5;
  color: #909399;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.search-area {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.pagination-area {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.request-detail {
  padding: 20px 0;
}
</style> 