#!/usr/bin/env python
"""
测试FastAPI端点 - 模拟登录权限API调用
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService
from app.crud.email import email_member

async def test_fastapi_logic():
    """模拟FastAPI端点逻辑"""
    print("=== 模拟FastAPI登录权限端点逻辑 ===")
    
    db = SessionLocal()
    try:
        # 1. 模拟端点参数
        userid = "<EMAIL>"
        print(f"测试用户: {userid}")
        
        # 2. 查找成员 (模拟API端点中的逻辑)
        print("\n1. 查找成员...")
        member = email_member.get_by_email(db, email=userid)
        if not member:
            print("❌ 成员不存在")
            return
        else:
            print(f"✅ 找到成员: ID={member.id}, name={member.name}")
        
        # 3. 创建API服务 (模拟API端点中的逻辑)
        print("\n2. 创建API服务...")
        try:
            api_service = TencentEmailAPIService(db, app_name="功能设置")
            print("✅ API服务创建成功")
            print(f"   应用名称: {api_service.app_name}")
            print(f"   配置ID: {api_service.config.id}")
        except Exception as e:
            print(f"❌ API服务创建失败: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return
        
        # 4. 调用API (模拟API端点中的逻辑)
        print("\n3. 调用登录权限API...")
        try:
            result = await api_service.get_user_option(member.email, [1, 2, 3, 4])
            print("✅ API调用成功")
            print(f"   errcode: {result.errcode}")
            print(f"   errmsg: {result.errmsg}")
            
            if result.errcode != 0:
                print(f"❌ API返回错误，这会导致FastAPI抛出HTTPException")
                print(f"   错误码: {result.errcode}")
                print(f"   错误信息: {result.errmsg}")
                return
            
            # 5. 构造响应 (模拟API端点中的逻辑)
            print("\n4. 构造响应...")
            from app.schemas.email import EmailUserOptionResponse
            
            response = EmailUserOptionResponse(
                errcode=result.errcode,
                errmsg=result.errmsg,
                option=result.data.get("option", []) if result.data else []
            )
            
            print("✅ 成功构造响应")
            print(f"   响应: {response.dict()}")
            
        except Exception as e:
            print(f"❌ API调用失败: {str(e)}")
            print("   这会导致FastAPI返回500错误")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            
    except Exception as e:
        print(f"❌ 整体测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    finally:
        db.close()

# 测试异常处理
async def test_exception_handling():
    """测试异常处理"""
    print("\n=== 测试异常处理机制 ===")
    
    db = SessionLocal()
    try:
        # 测试不存在的用户
        print("1. 测试不存在的用户...")
        userid = "<EMAIL>"
        member = email_member.get_by_email(db, email=userid)
        
        if not member:
            print("✅ 正确处理不存在的用户")
            print("   这应该返回404错误而不是500错误")
        
        # 测试错误的app_name
        print("\n2. 测试错误的应用名称...")
        try:
            api_service = TencentEmailAPIService(db, app_name="不存在的应用")
            print("❌ 应该抛出异常但没有")
        except ValueError as e:
            print(f"✅ 正确抛出ValueError: {str(e)}")
            print("   这会导致后端返回500错误")
        except Exception as e:
            print(f"❌ 抛出了意外的异常: {str(e)}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_fastapi_logic())
    asyncio.run(test_exception_handling()) 