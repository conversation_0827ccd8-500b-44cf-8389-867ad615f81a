"""recreate_asset_settings_table

Revision ID: 4c53a5efd16a
Revises: 7d28afa6797e
Create Date: 2025-06-23 15:02:31.825994

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4c53a5efd16a'
down_revision: Union[str, None] = '7d28afa6797e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_settings',
    sa.Column('id', sa.Integer(), nullable=False, comment='ID'),
    sa.Column('company', sa.String(length=100), nullable=False, comment='公司'),
    sa.Column('asset_number_rule', sa.JSON(), nullable=False, comment='资产编号规则'),
    sa.<PERSON>umn('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index(op.f('ix_asset_settings_id'), 'asset_settings', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_asset_settings_id'), table_name='asset_settings')
    op.drop_table('asset_settings')
    # ### end Alembic commands ###
