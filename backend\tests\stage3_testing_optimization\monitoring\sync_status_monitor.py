"""
同步状态监控
实时监控同步状态和进度，提供监控面板和告警机制
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.database import get_db
from app.models.email import PersonnelSyncLog, EmailMember
from app.models.ecology_user import EcologyUser
from app.services.personnel_email_sync import PersonnelEmailSyncService

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    """同步状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class SyncMetrics:
    """同步指标"""
    total_personnel: int
    total_email_members: int
    matched_count: int
    unmatched_count: int
    sync_success_rate: float
    last_sync_time: Optional[datetime]
    avg_sync_duration: float
    error_count_24h: int
    
@dataclass
class SyncProgress:
    """同步进度"""
    current_step: str
    total_steps: int
    completed_steps: int
    progress_percentage: float
    estimated_remaining_time: Optional[float]
    current_operation: str
    
@dataclass
class AlertInfo:
    """告警信息"""
    level: AlertLevel
    message: str
    timestamp: datetime
    source: str
    details: Optional[Dict[str, Any]] = None

class SyncStatusMonitor:
    """同步状态监控器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.current_status = SyncStatus.IDLE
        self.current_progress = None
        self.alerts = []
        self.metrics_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前同步状态"""
        return {
            "status": self.current_status.value,
            "progress": asdict(self.current_progress) if self.current_progress else None,
            "last_update": datetime.now().isoformat(),
            "metrics": self.get_sync_metrics(),
            "recent_alerts": [asdict(alert) for alert in self.alerts[-10:]]
        }
    
    def get_sync_metrics(self) -> Dict[str, Any]:
        """获取同步指标"""
        cache_key = "sync_metrics"
        now = time.time()
        
        # 检查缓存
        if (cache_key in self.metrics_cache and 
            now - self.metrics_cache[cache_key]['timestamp'] < self.cache_ttl):
            return self.metrics_cache[cache_key]['data']
        
        # 计算指标
        metrics = self._calculate_sync_metrics()
        
        # 更新缓存
        self.metrics_cache[cache_key] = {
            'data': asdict(metrics),
            'timestamp': now
        }
        
        return asdict(metrics)
    
    def _calculate_sync_metrics(self) -> SyncMetrics:
        """计算同步指标"""
        # 获取基础统计
        total_personnel = self.db.query(EcologyUser).filter(
            and_(
                EcologyUser.job_number.isnot(None),
                EcologyUser.job_number != ""
            )
        ).count()
        
        total_email_members = self.db.query(EmailMember).filter(
            and_(
                EmailMember.extid.isnot(None),
                EmailMember.extid != ""
            )
        ).count()
        
        # 计算匹配数量
        matched_count = self.db.query(EcologyUser).join(
            EmailMember, EcologyUser.job_number == EmailMember.extid
        ).count()
        
        unmatched_count = total_personnel - matched_count
        
        # 计算成功率
        sync_success_rate = (matched_count / total_personnel * 100) if total_personnel > 0 else 0
        
        # 获取最后同步时间
        last_sync_log = self.db.query(PersonnelSyncLog).order_by(
            desc(PersonnelSyncLog.created_at)
        ).first()
        
        last_sync_time = last_sync_log.created_at if last_sync_log else None
        
        # 计算平均同步时长（从duration字段解析）
        recent_logs = self.db.query(PersonnelSyncLog).filter(
            PersonnelSyncLog.created_at >= datetime.now() - timedelta(days=7)
        ).all()

        durations = []
        for log in recent_logs:
            if log.duration:
                try:
                    # 解析duration字符串，假设格式为"XX.XX秒"
                    duration_str = log.duration.replace('秒', '').strip()
                    duration_float = float(duration_str)
                    durations.append(duration_float)
                except (ValueError, AttributeError):
                    continue

        avg_sync_duration = sum(durations) / len(durations) if durations else 0
        
        # 计算24小时内错误数量
        error_count_24h = self.db.query(PersonnelSyncLog).filter(
            and_(
                PersonnelSyncLog.status == "failed",
                PersonnelSyncLog.created_at >= datetime.now() - timedelta(hours=24)
            )
        ).count()
        
        return SyncMetrics(
            total_personnel=total_personnel,
            total_email_members=total_email_members,
            matched_count=matched_count,
            unmatched_count=unmatched_count,
            sync_success_rate=sync_success_rate,
            last_sync_time=last_sync_time,
            avg_sync_duration=avg_sync_duration,
            error_count_24h=error_count_24h
        )
    
    def update_sync_progress(self, step: str, completed: int, total: int, operation: str = ""):
        """更新同步进度"""
        progress_percentage = (completed / total * 100) if total > 0 else 0
        
        # 估算剩余时间（基于历史数据）
        estimated_remaining_time = self._estimate_remaining_time(completed, total)
        
        self.current_progress = SyncProgress(
            current_step=step,
            total_steps=total,
            completed_steps=completed,
            progress_percentage=progress_percentage,
            estimated_remaining_time=estimated_remaining_time,
            current_operation=operation
        )
        
        logger.info(f"同步进度更新: {step} ({completed}/{total}, {progress_percentage:.1f}%)")
    
    def _estimate_remaining_time(self, completed: int, total: int) -> Optional[float]:
        """估算剩余时间"""
        if completed == 0 or total == 0:
            return None
        
        # 基于历史平均时长估算
        metrics = self._calculate_sync_metrics()
        if metrics.avg_sync_duration > 0:
            remaining_ratio = (total - completed) / total
            return metrics.avg_sync_duration * remaining_ratio
        
        return None
    
    def add_alert(self, level: AlertLevel, message: str, source: str, details: Optional[Dict] = None):
        """添加告警"""
        alert = AlertInfo(
            level=level,
            message=message,
            timestamp=datetime.now(),
            source=source,
            details=details
        )
        
        self.alerts.append(alert)
        
        # 保持告警列表大小
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-500:]
        
        logger.warning(f"告警: [{level.value}] {source}: {message}")
    
    def check_health_status(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            "overall_status": "healthy",
            "checks": {},
            "issues": []
        }
        
        metrics = self._calculate_sync_metrics()
        
        # 检查同步成功率
        if metrics.sync_success_rate < 90:
            health_status["checks"]["sync_success_rate"] = "warning"
            health_status["issues"].append(f"同步成功率较低: {metrics.sync_success_rate:.1f}%")
            if metrics.sync_success_rate < 80:
                health_status["overall_status"] = "unhealthy"
        else:
            health_status["checks"]["sync_success_rate"] = "healthy"
        
        # 检查最后同步时间
        if metrics.last_sync_time:
            time_since_last_sync = datetime.now() - metrics.last_sync_time
            if time_since_last_sync > timedelta(hours=24):
                health_status["checks"]["last_sync_time"] = "warning"
                health_status["issues"].append(f"超过24小时未同步")
                if time_since_last_sync > timedelta(hours=48):
                    health_status["overall_status"] = "unhealthy"
            else:
                health_status["checks"]["last_sync_time"] = "healthy"
        else:
            health_status["checks"]["last_sync_time"] = "unknown"
            health_status["issues"].append("无同步记录")
        
        # 检查错误数量
        if metrics.error_count_24h > 10:
            health_status["checks"]["error_count"] = "warning"
            health_status["issues"].append(f"24小时内错误数量过多: {metrics.error_count_24h}")
            if metrics.error_count_24h > 50:
                health_status["overall_status"] = "unhealthy"
        else:
            health_status["checks"]["error_count"] = "healthy"
        
        # 检查数据一致性
        consistency_ratio = (metrics.matched_count / metrics.total_personnel * 100) if metrics.total_personnel > 0 else 0
        if consistency_ratio < 85:
            health_status["checks"]["data_consistency"] = "warning"
            health_status["issues"].append(f"数据一致性较低: {consistency_ratio:.1f}%")
            if consistency_ratio < 70:
                health_status["overall_status"] = "unhealthy"
        else:
            health_status["checks"]["data_consistency"] = "healthy"
        
        return health_status
    
    def get_sync_history(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取同步历史"""
        start_date = datetime.now() - timedelta(days=days)
        
        sync_logs = self.db.query(PersonnelSyncLog).filter(
            PersonnelSyncLog.created_at >= start_date
        ).order_by(desc(PersonnelSyncLog.created_at)).all()
        
        history = []
        for log in sync_logs:
            history.append({
                "id": log.id,
                "sync_type": log.sync_type,
                "status": log.status,
                "processed_count": log.processed_count,
                "created_requests": log.created_requests,
                "updated_count": log.updated_count,
                "disabled_count": log.disabled_count,
                "error_count": log.error_count,
                "execution_time": log.execution_time,
                "created_at": log.created_at.isoformat(),
                "error_details": log.error_details
            })
        
        return history
    
    def get_performance_trends(self, days: int = 30) -> Dict[str, Any]:
        """获取性能趋势"""
        start_date = datetime.now() - timedelta(days=days)
        
        # 按天统计同步性能
        daily_stats = self.db.query(
            func.date(PersonnelSyncLog.created_at).label('date'),
            func.count(PersonnelSyncLog.id).label('sync_count'),
            func.avg(PersonnelSyncLog.execution_time).label('avg_duration'),
            func.sum(PersonnelSyncLog.processed_count).label('total_processed'),
            func.sum(PersonnelSyncLog.error_count).label('total_errors')
        ).filter(
            PersonnelSyncLog.created_at >= start_date
        ).group_by(
            func.date(PersonnelSyncLog.created_at)
        ).all()
        
        trends = {
            "dates": [],
            "sync_counts": [],
            "avg_durations": [],
            "total_processed": [],
            "error_rates": []
        }
        
        for stat in daily_stats:
            trends["dates"].append(stat.date.isoformat())
            trends["sync_counts"].append(stat.sync_count)
            trends["avg_durations"].append(float(stat.avg_duration or 0))
            trends["total_processed"].append(stat.total_processed or 0)
            
            error_rate = (stat.total_errors / stat.total_processed * 100) if stat.total_processed > 0 else 0
            trends["error_rates"].append(error_rate)
        
        return trends
    
    async def start_monitoring(self, interval: int = 60):
        """启动监控循环"""
        logger.info(f"启动同步状态监控，检查间隔: {interval}秒")
        
        while True:
            try:
                # 检查健康状态
                health_status = self.check_health_status()
                
                # 根据健康状态生成告警
                if health_status["overall_status"] == "unhealthy":
                    self.add_alert(
                        AlertLevel.ERROR,
                        f"系统健康状态异常: {', '.join(health_status['issues'])}",
                        "health_monitor",
                        {"health_status": health_status}
                    )
                elif health_status["issues"]:
                    self.add_alert(
                        AlertLevel.WARNING,
                        f"系统健康状态警告: {', '.join(health_status['issues'])}",
                        "health_monitor",
                        {"health_status": health_status}
                    )
                
                # 清理缓存
                self._cleanup_cache()
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                self.add_alert(
                    AlertLevel.ERROR,
                    f"监控系统异常: {e}",
                    "monitor_system"
                )
                await asyncio.sleep(interval)
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        now = time.time()
        expired_keys = [
            key for key, value in self.metrics_cache.items()
            if now - value['timestamp'] > self.cache_ttl
        ]
        
        for key in expired_keys:
            del self.metrics_cache[key]

class SyncDashboard:
    """同步监控面板"""
    
    def __init__(self, monitor: SyncStatusMonitor):
        self.monitor = monitor
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """生成监控面板数据"""
        return {
            "current_status": self.monitor.get_current_status(),
            "health_status": self.monitor.check_health_status(),
            "sync_history": self.monitor.get_sync_history(days=7),
            "performance_trends": self.monitor.get_performance_trends(days=30),
            "alerts": [asdict(alert) for alert in self.monitor.alerts[-20:]],
            "generated_at": datetime.now().isoformat()
        }
    
    def export_dashboard_json(self, filepath: str):
        """导出监控面板数据为JSON"""
        dashboard_data = self.generate_dashboard_data()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(dashboard_data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"监控面板数据已导出到: {filepath}")

if __name__ == "__main__":
    # 测试监控功能
    db = next(get_db())
    try:
        monitor = SyncStatusMonitor(db)
        dashboard = SyncDashboard(monitor)
        
        # 生成监控面板数据
        dashboard_data = dashboard.generate_dashboard_data()
        print("监控面板数据生成完成")
        
        # 导出数据
        dashboard.export_dashboard_json("sync_dashboard.json")
        
    finally:
        db.close()
