import { Monitor, Connection, Box, User, Setting, List, Tickets, Document, InfoFilled, Grid, Message, Lock } from '@element-plus/icons-vue'

export interface MenuItem {
  path: string
  title: string
  icon?: any
  children?: MenuItem[]
  permissions?: string[]
  hidden?: boolean
}

export const menus: MenuItem[] = [
  {
    path: '/dashboard',
    title: '仪表盘',
    icon: Monitor
  },
  {
    path: '/basic-info',
    title: '基础信息',
    icon: InfoFilled,
    permissions: ['basic-info:view'],
    children: [
      {
        path: '/basic-info/personnel',
        title: '人员信息',
        icon: User,
        permissions: ['basic-info:personnel:view']
      }
    ]
  },
  {
    path: '/ad',
    title: 'AD管理',
    icon: Connection,
    permissions: ['ad:view'],
    children: [
      {
        path: '/ad',
        title: '用户与组',
        icon: User,
        permissions: ['ad:view']
      },
      {
        path: '/ad/config',
        title: '服务器配置',
        icon: Setting,
        permissions: ['ad:config']
      },
      {
        path: '/ad/sync-config',
        title: '人员同步',
        icon: Connection,
        permissions: ['ad:sync']
      }
    ]
  },
  {
    path: '/email',
    title: '邮箱管理',
    icon: Message,
    permissions: ['email:view'],
    children: [
      {
        path: '/email',
        title: '管理首页',
        icon: Monitor,
        permissions: ['email:view']
      },
      {
        path: '/email/config',
        title: '邮箱配置',
        icon: Setting,
        permissions: ['email:config:view']
      },
      {
        path: '/email/departments-members',
        title: '部门与成员管理',
        icon: User,
        permissions: ['email:department:view', 'email:member:view']
      },
      {
        path: '/email/groups',
        title: '群组管理',
        icon: Connection,
        permissions: ['email:group:view']
      },
      {
        path: '/email/tags',
        title: '标签管理',
        icon: Document,
        permissions: ['email:tag:view']
      },
      {
        path: '/email/sync',
        title: '同步管理',
        icon: Connection,
        permissions: ['email:sync:view', 'email:member:edit']
      }
    ]
  },
  {
    path: '/asset',
    title: '资产管理',
    icon: Box,
    permissions: ['asset:view'],
    children: [
      {
        path: '/asset/list',
        title: '资产列表',
        icon: List,
        permissions: ['asset:view']
      },
      {
        path: '/asset/settings',
        title: '资产设置',
        icon: Setting,
        permissions: ['asset:field:manage']
      },
      {
        path: '/asset/custom-fields',
        title: '自定义字段',
        icon: Document,
        permissions: ['asset:field:manage']
      },
      {
        path: '/field-values',
        title: '字段值管理',
        icon: Document,
        permissions: ['asset:field:manage']
      },
      {
        path: '/inventory/tasks',
        title: '资产盘点',
        icon: Tickets,
        permissions: ['inventory:view']
      }
    ]
  },
  {
    path: '/system',
    title: '系统设置',
    icon: Setting,
    permissions: ['system:view'],
    children: [
      {
        path: '/system/settings',
        title: '权限管理',
        icon: Setting,
        permissions: ['system:view']
      },
      {
        path: '/system/users',
        title: '用户管理',
        icon: User,
        permissions: ['system:view']
      },
      {
        path: '/system/ldap-config',
        title: 'LDAP配置',
        icon: Connection,
        permissions: ['ldap:config:view']
      },
      {
        path: '/system/lock-management',
        title: '同步锁管理',
        icon: Lock,
        permissions: ['system:lock:manage']
      }
    ]
  },
  {
    path: '/terminal',
    title: '终端管理',
    icon: Grid,
    permissions: ['terminal:view'],
    children: [
      {
        path: '/terminal/overview',
        title: '终端概况',
        icon: Monitor,
        permissions: ['terminal:view']
      },
      {
        path: '/terminal/list',
        title: '终端列表',
        icon: List,
        permissions: ['terminal:view']
      },
      {
        path: '/terminal/software',
        title: '软件管理',
        icon: Document,
        permissions: ['terminal:view']
      },
      {
        path: '/terminal/agent',
        title: 'Agent管理',
        icon: Setting,
        permissions: ['terminal:agent:manage']
      },
      {
        path: '/terminal/command-whitelist',
        title: '命令白名单',
        icon: Document,
        permissions: ['terminal:command:manage']
      }
    ]
  }
]

export default menus