#!/usr/bin/env python3
"""
邮箱部门层级结构修复脚本

这个脚本的目的是修正现有的邮箱部门层级关系，确保：
1. "重庆至信实业股份有限公司（重庆至信）"作为根公司部门存在
2. 其他部门按正确的层级关系组织
3. 同步企业邮箱API和本地数据库

使用方法:
python fix_email_department_hierarchy.py [--dry-run] [--sync-to-api]
"""

import asyncio
import argparse
import logging
from typing import Dict, List, Set, Optional
from sqlalchemy.orm import Session

# 设置项目路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import SessionLocal
from app.crud.email import email_department
from app.schemas.email import EmailDepartmentCreate, EmailDepartmentUpdate
from app.services.email_api import TencentEmailAPIService
from app.models.ecology_user import EcologyUser
from app.crud.ecology_user import get_ecology_users

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class EmailDepartmentHierarchyFixer:
    def __init__(self, sync_to_api: bool = False):
        self.db = SessionLocal()
        self.sync_to_api = sync_to_api
        self.api_service = None
        self.stats = {
            'total_departments': 0,
            'fixed_departments': 0,
            'created_departments': 0,
            'updated_departments': 0,
            'errors': []
        }
        
        # 部门名称映射表，处理命名不一致问题
        self.department_name_mapping = {
            # 泛微数据 -> 邮箱部门
            "重庆至信实业股份有限公司（重庆至信）": "重庆至信实业有限公司",
            "至信实业股份有限公司": "至信实业股份有限公司",
            "至信股份总部": "至信股份总部",
            # 可以根据需要添加更多映射
        }
        
        if self.sync_to_api:
            try:
                self.api_service = TencentEmailAPIService()
                logger.info("初始化腾讯企业邮箱API服务成功")
            except Exception as e:
                logger.error(f"初始化API服务失败: {str(e)}")
                self.sync_to_api = False
    
    def map_department_name(self, ecology_name: str) -> str:
        """将泛微数据中的部门名称映射到邮箱部门名称"""
        return self.department_name_mapping.get(ecology_name, ecology_name)
    
    def find_email_department_by_name(self, name: str) -> Optional[object]:
        """智能查找邮箱部门，支持精确匹配和模糊匹配"""
        # 先尝试精确匹配
        dept = email_department.get_by_name(self.db, name)
        if dept:
            return dept
        
        # 尝试映射后的名称
        mapped_name = self.map_department_name(name)
        if mapped_name != name:
            dept = email_department.get_by_name(self.db, mapped_name)
            if dept:
                return dept
        
        # 尝试模糊匹配（包含关系）
        all_depts = email_department.get_multi(self.db, skip=0, limit=1000)
        for dept in all_depts:
            # 检查是否部分匹配
            if name in dept.name or dept.name in name:
                logger.info(f"模糊匹配找到部门: {name} -> {dept.name}")
                return dept
                
            # 检查映射后是否匹配
            mapped_name = self.map_department_name(name)
            if mapped_name in dept.name or dept.name in mapped_name:
                logger.info(f"映射匹配找到部门: {name} -> {mapped_name} -> {dept.name}")
                return dept
        
        return None

    async def analyze_current_structure(self) -> Dict:
        """分析当前的邮箱部门结构和泛微数据"""
        logger.info("分析当前的邮箱部门结构...")
        
        # 获取所有邮箱部门
        email_departments = email_department.get_multi(self.db, skip=0, limit=1000)
        
        # 获取泛微数据
        ecology_users = get_ecology_users(self.db, skip=0, limit=100000)
        
        analysis = {
            'email_departments': len(email_departments),
            'root_departments': [],
            'companies_in_ecology': set(),
            'dept_hierarchy_map': {},
            'missing_company_dept': None
        }
        
        # 分析根部门
        for dept in email_departments:
            if dept.parent_id is None or dept.parent_id == "1":
                analysis['root_departments'].append((dept.name, dept.dept_id))
        
        # 分析泛微数据中的公司和部门层级
        company_dept_map = {}
        for user in ecology_users:
            if not user.dept_hierarchy:
                continue
            
            hierarchy_parts = [part.strip() for part in user.dept_hierarchy.split(' > ')]
            if hierarchy_parts:
                company_name = hierarchy_parts[0]
                analysis['companies_in_ecology'].add(company_name)
                
                # 构建部门层级映射
                if company_name not in company_dept_map:
                    company_dept_map[company_name] = {
                        'name': company_name,
                        'sub_departments': set()
                    }
                
                # 记录子部门
                for i in range(1, len(hierarchy_parts)):
                    company_dept_map[company_name]['sub_departments'].add(hierarchy_parts[i])
        
        analysis['dept_hierarchy_map'] = company_dept_map
        
        # 检查是否存在"重庆至信实业股份有限公司（重庆至信）"
        target_company = "重庆至信实业股份有限公司（重庆至信）"
        company_dept = self.find_email_department_by_name(target_company)
        
        if not company_dept:
            analysis['missing_company_dept'] = target_company
        elif company_dept.parent_id not in [None, "1"]:
            analysis['incorrect_company_parent'] = {
                'name': target_company,
                'current_parent': company_dept.parent_id,
                'should_be_parent': None
            }
        
        return analysis

    async def get_api_departments(self) -> List[Dict]:
        """从企业邮箱API获取部门列表"""
        if not self.api_service:
            return []

        try:
            result = await self.api_service.get_department_list()
            if result.errcode == 0:
                return result.data.get("department", [])
            else:
                logger.error(f"获取API部门列表失败: {result.errmsg}")
                return []
        except Exception as e:
            logger.error(f"调用API获取部门列表异常: {str(e)}")
            return []

    async def ensure_company_department(self, company_name: str, dry_run: bool = False) -> str:
        """确保公司部门存在并且是根部门"""
        logger.info(f"确保公司部门存在: {company_name}")
        
        # 使用智能查找
        existing_dept = self.find_email_department_by_name(company_name)
        
        if existing_dept:
            # 检查是否需要修正父部门
            if existing_dept.parent_id not in [None, "1"]:
                logger.info(f"修正公司部门 {company_name} 的父部门关系: {existing_dept.parent_id} -> None")
                if not dry_run:
                    update_data = EmailDepartmentUpdate(parent_id=None)
                    email_department.update(self.db, db_obj=existing_dept, obj_in=update_data)
                    self.stats['updated_departments'] += 1
                else:
                    logger.info(f"[试运行] 将修正公司部门的父部门关系")
            else:
                logger.info(f"公司部门 {company_name} 已正确配置")
            
            return existing_dept.dept_id
        
        # 部门不存在，需要创建
        logger.info(f"公司部门 {company_name} 不存在，需要创建")
        
        # 首先尝试从API创建
        new_dept_id = "1"  # 默认值
        
        if self.api_service and not dry_run:
            try:
                # 根据API文档格式创建部门
                create_data = {
                    "name": company_name,
                    "parentid": 1,  # 根部门的parentid为1
                    "order": 0
                }
                result = await self.api_service.create_department(create_data)
                if result.errcode == 0:
                    new_dept_id = str(result.data.get("id", "1"))
                    logger.info(f"API创建公司部门成功: {company_name} -> {new_dept_id}")
                    
                    # 验证创建结果，有时需要重新查询
                    departments = await self.get_api_departments()
                    for dept in departments:
                        if dept.get("name") == company_name:
                            new_dept_id = str(dept.get("id", new_dept_id))
                            break
                    
                    if new_dept_id == "1":
                        # 如果还是默认值，尝试搜索
                        search_result = await self.api_service.search_department(company_name)
                        if search_result.errcode == 0 and search_result.data.get("department"):
                            dept_list = search_result.data["department"]
                            if dept_list:
                                new_dept_id = str(dept_list[0].get("id", "1"))
                                logger.info(f"通过搜索找到部门ID: {new_dept_id}")
                        else:
                            logger.error(f"搜索部门失败: {search_result.errmsg}")
                            return "1"
                else:
                    logger.error(f"API创建部门失败: {result.errmsg}")
                    return "1"
            except Exception as e:
                logger.error(f"API创建部门异常: {str(e)}")
                return "1"
        else:
            new_dept_id = "dummy_id_for_dry_run"
        
        # 在本地数据库中创建记录
        if not dry_run:
            dept_create = EmailDepartmentCreate(
                dept_id=new_dept_id,
                name=company_name,
                parent_id=None,  # 本地数据库中的根部门
                order=0
            )
            local_dept = email_department.create(self.db, obj_in=dept_create)
            logger.info(f"本地创建公司部门: {company_name} -> {new_dept_id}")
            self.stats['created_departments'] += 1
            return local_dept.dept_id
        else:
            logger.info(f"[试运行] 将创建公司部门: {company_name} -> {new_dept_id}")
            return new_dept_id

    async def fix_department_hierarchy(self, dry_run: bool = False) -> Dict:
        """修复部门层级关系"""
        logger.info("开始修复部门层级关系...")
        
        # 分析当前结构
        analysis = await self.analyze_current_structure()
        logger.info(f"分析结果: {analysis}")
        
        # 确保重庆至信公司部门存在
        target_company = "重庆至信实业股份有限公司（重庆至信）"
        if target_company in analysis['companies_in_ecology']:
            company_dept_id = await self.ensure_company_department(target_company, dry_run)
            logger.info(f"公司部门ID: {company_dept_id}")
        else:
            logger.warning(f"在泛微数据中未找到公司: {target_company}")
            company_dept_id = "1"

        # 获取所有需要修复层级的部门
        departments_to_fix = []
        
        # 从泛微数据中收集部门层级信息
        ecology_users = get_ecology_users(self.db, skip=0, limit=100000)
        dept_hierarchy_info = {}
        
        for user in ecology_users:
            if not user.dept_hierarchy or not user.dept_name:
                continue

            hierarchy_parts = [part.strip() for part in user.dept_hierarchy.split(' > ')]
            if len(hierarchy_parts) < 2:  # 至少要有公司和部门两级
                continue

            dept_name = user.dept_name
            if dept_name not in dept_hierarchy_info:
                dept_hierarchy_info[dept_name] = {
                    'hierarchy': hierarchy_parts,
                    'company': hierarchy_parts[0],
                    'level': len(hierarchy_parts)
                }

        logger.info(f"从泛微数据中收集到 {len(dept_hierarchy_info)} 个部门的层级信息")

        # 按层级排序，先处理上级部门
        sorted_depts = sorted(dept_hierarchy_info.items(), key=lambda x: x[1]['level'])
        
        for dept_name, info in sorted_depts:
            if dept_name == info['company']:  # 跳过公司级别
                continue

            # 确定父部门
            hierarchy = info['hierarchy']
            if len(hierarchy) == 2:  # 直接隶属于公司
                expected_parent_id = company_dept_id
                expected_parent_name = info['company']
            else:  # 隶属于上级部门
                expected_parent_name = hierarchy[-2]  # 倒数第二个是父部门
                parent_dept = self.find_email_department_by_name(expected_parent_name)
                expected_parent_id = parent_dept.dept_id if parent_dept else company_dept_id

            # 检查当前部门的父子关系
            current_dept = self.find_email_department_by_name(dept_name)
            if current_dept:
                if current_dept.parent_id != expected_parent_id:
                    logger.info(f"发现需要修正的部门: {dept_name}")
                    logger.info(f"  当前父部门ID: {current_dept.parent_id}")
                    logger.info(f"  应该的父部门ID: {expected_parent_id} ({expected_parent_name})")
                    departments_to_fix.append({
                        'dept': current_dept,
                        'expected_parent_id': expected_parent_id,
                        'expected_parent_name': expected_parent_name
                    })
            else:
                logger.debug(f"邮箱部门中未找到: {dept_name}")

            self.stats['total_departments'] += 1

        logger.info(f"找到 {len(departments_to_fix)} 个需要修复的部门")

        # 执行修复
        for fix_info in departments_to_fix:
            dept = fix_info['dept']
            new_parent_id = fix_info['expected_parent_id']
            parent_name = fix_info['expected_parent_name']

            logger.info(f"修复部门 {dept.name}: 父部门 {dept.parent_id} -> {new_parent_id} ({parent_name})")

            if not dry_run:
                update_data = EmailDepartmentUpdate(parent_id=new_parent_id)
                email_department.update(self.db, db_obj=dept, obj_in=update_data)

                # 同步到API
                if self.sync_to_api and self.api_service:
                    try:
                        # 根据API文档格式，确保参数类型正确
                        api_data = {
                            "id": int(dept.dept_id),  # API要求id为整数
                            "name": dept.name,
                            "parentid": int(new_parent_id) if new_parent_id != "1" else 1,  # 确保parentid为整数
                            "order": dept.order or 0
                        }
                        result = await self.api_service.update_department(api_data)
                        if result.errcode != 0:
                            logger.error(f"API更新部门失败: {result.errmsg}")
                            self.stats['errors'].append(f"API更新部门 {dept.name} 失败: {result.errmsg}")
                        else:
                            logger.info(f"API更新部门成功: {dept.name}")
                    except Exception as e:
                        logger.error(f"API更新部门异常: {str(e)}")
                        self.stats['errors'].append(f"API更新部门 {dept.name} 异常: {str(e)}")

                self.stats['fixed_departments'] += 1
            else:
                logger.info(f"[试运行] 将修复部门 {dept.name} 的父部门关系")

        return self.stats

    async def run(self, dry_run: bool = False) -> Dict:
        """运行修复流程"""
        logger.info(f"开始邮箱部门层级修复，试运行: {dry_run}, 同步API: {self.sync_to_api}")

        try:
            result = await self.fix_department_hierarchy(dry_run)

            logger.info("修复完成，统计信息:")
            logger.info(f"  总部门数: {result['total_departments']}")
            logger.info(f"  修复部门数: {result['fixed_departments']}")
            logger.info(f"  创建部门数: {result['created_departments']}")
            logger.info(f"  更新部门数: {result['updated_departments']}")

            if result['errors']:
                logger.error(f"  错误数: {len(result['errors'])}")
                for error in result['errors']:
                    logger.error(f"    {error}")

            return result

        except Exception as e:
            logger.error(f"修复过程中发生异常: {str(e)}", exc_info=True)
            return {'error': str(e)}

    def __del__(self):
        if hasattr(self, 'db'):
            self.db.close()

async def main():
    parser = argparse.ArgumentParser(description="邮箱部门层级结构修复脚本")
    parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改数据")
    parser.add_argument("--sync-to-api", action="store_true", help="同步修改到企业邮箱API")
    
    args = parser.parse_args()
    
    print("开始执行邮箱部门层级修复脚本...")
    print(f"试运行模式: {args.dry_run}")
    print(f"同步到API: {args.sync_to_api}")
    
    print("创建修复器实例...")
    fixer = EmailDepartmentHierarchyFixer(sync_to_api=args.sync_to_api)
    
    print("开始执行修复...")
    result = await fixer.run(dry_run=args.dry_run)
    
    print("\n=== 修复完成 ===")
    if 'error' in result:
        print(f"修复失败: {result['error']}")
    else:
        print(f"成功修复 {result['fixed_departments']} 个部门的层级关系")

if __name__ == "__main__":
    asyncio.run(main()) 