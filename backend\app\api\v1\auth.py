﻿from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import Optional
from ... import models, schemas
from ...database import get_db
from ...utils import verify_password, create_access_token
from ...config import settings
from ...services.ldap_auth import LdapAuthService

router = APIRouter()

@router.post("/login")
async def login(
    username: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(False),
    auth_type: str = Form("local"),  # 新增认证类型参数：local 或 ldap
    db: Session = Depends(get_db)
):
    """用户登录 - 支持本地和LDAP认证"""
    
    if auth_type == "ldap":
        # LDAP认证
        ldap_service = LdapAuthService(db)
        login_result = ldap_service.login(username, password)
        
        if not login_result:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="LDAP认证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return login_result
    
    else:
        # 本地认证（原有逻辑）
        user = db.query(models.User).filter(models.User.username == username).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        if not verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户是否被禁用
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户已被禁用，请联系管理员",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 根据是否记住登录设置不同的过期时间
        if remember:
            access_token_expires = timedelta(days=settings.REMEMBER_TOKEN_EXPIRE_DAYS)
        else:
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        # 生成访问令牌
        access_token = create_access_token(
            data={"sub": user.username}, expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "username": user.username,
                "email": user.email,
                "is_superuser": user.is_superuser,
                "auth_type": "local"
            }
        }

@router.post("/ldap-login")
async def ldap_login(
    auth_request: schemas.LdapAuthRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """专用LDAP登录接口"""
    # 获取客户端IP
    client_ip = request.client.host
    
    ldap_service = LdapAuthService(db)
    login_result = ldap_service.login(
        username=auth_request.username,
        password=auth_request.password,
        config_id=auth_request.config_id,
        client_ip=client_ip
    )
    
    if not login_result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="LDAP认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return login_result

@router.get("/ldap-configs")
async def get_available_ldap_configs(
    request: Request,
    db: Session = Depends(get_db)
):
    """获取可用的LDAP配置列表（仅返回基本信息）"""
    from ...crud.ldap_config import ldap_config_crud
    from ...utils.ip_matcher import IPMatcher
    
    # 获取客户端IP
    client_ip = request.client.host
    
    configs = ldap_config_crud.get_active_configs(db)
    
    # 找到匹配的配置
    ldap_service = LdapAuthService(db)
    auto_select_result = ldap_service.select_config_by_ip(client_ip)
    auto_selected_config_id = auto_select_result[0].id if auto_select_result else None
    
    # 返回配置信息，包含自动选择标识
    result = []
    for config in configs:
        config_info = {
            "id": config.id,
            "name": config.name,
            "server": config.server,
            "is_default": config.is_default,
            "description": config.description,
            "priority": config.priority,
            "auto_select_enabled": config.auto_select_enabled,
            "is_auto_selected": config.id == auto_selected_config_id
        }
        
        # 如果是自动选择的配置，添加匹配信息
        if config.id == auto_selected_config_id and auto_select_result:
            config_info["match_reason"] = auto_select_result[1]
        
        result.append(config_info)
    
    return {
        "configs": result,
        "client_ip": client_ip,
        "auto_selected_config_id": auto_selected_config_id
    }

@router.post("/ldap-configs/preview-match")
async def preview_ldap_config_match(
    request: Request,
    db: Session = Depends(get_db)
):
    """预览当前IP地址的LDAP配置匹配结果"""
    from ...crud.ldap_config import ldap_config_crud
    from ...utils.ip_matcher import IPMatcher
    
    client_ip = request.client.host
    
    # 获取所有配置
    all_configs = ldap_config_crud.get_multi(db, limit=100)
    
    # 查找匹配的配置
    matching_configs = IPMatcher.find_matching_configs(client_ip, all_configs)
    
    # 格式化结果
    matches = []
    for config, matched_range, match_reason in matching_configs:
        matches.append({
            "config_id": config.id,
            "config_name": config.name,
            "server": config.server,
            "priority": config.priority,
            "matched_range": matched_range,
            "match_reason": match_reason,
            "is_active": config.is_active
        })
    
    # 获取备用配置
    fallback_config = IPMatcher.get_fallback_config(all_configs)
    
    return {
        "client_ip": client_ip,
        "matching_configs": matches,
        "fallback_config": {
            "id": fallback_config.id,
            "name": fallback_config.name,
            "server": fallback_config.server,
            "is_default": fallback_config.is_default
        } if fallback_config else None,
        "recommendation": {
            "config_id": matches[0]["config_id"] if matches else (fallback_config.id if fallback_config else None),
            "reason": matches[0]["match_reason"] if matches else "默认配置（无IP匹配）"
        }
    }
 
