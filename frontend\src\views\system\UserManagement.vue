<template>
  <div class="user-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><User /></el-icon>
        <h2 class="page-title">用户管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>系统设置</el-breadcrumb-item>
        <el-breadcrumb-item>用户管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">用户列表</span>
        </div>
        <div class="action-buttons">
          <Authority permission="system:user:add">
            <el-button type="primary" @click="handleCreateUser">
              <el-icon><Plus /></el-icon> 创建用户
            </el-button>
          </Authority>
        </div>
      </div>
      
      <el-table 
        :data="paginatedUsers" 
        border 
        style="width: 100%"
        row-class-name="user-table-row"
        header-row-class-name="user-table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column prop="username" label="用户名" width="180">
          <template #header>
            <div class="column-header">用户名</div>
          </template>
          <template #default="scope">
            <div class="username-cell">
              <span>{{ scope.row.username }}</span>
              <el-tag v-if="scope.row.is_builtin" size="small" type="warning" class="builtin-tag">
                内置
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" width="220">
          <template #header>
            <div class="column-header">邮箱</div>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #header>
            <div class="column-header">状态</div>
          </template>
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="角色" min-width="240">
          <template #header>
            <div class="column-header">角色</div>
          </template>
          <template #default="scope">
            <div class="role-tags">
              <el-tag 
                v-for="role in scope.row.roles" 
                :key="role.id" 
                size="small" 
                class="role-tag"
                type="info"
                effect="plain"
              >
                {{ role.name }}
              </el-tag>
              <span v-if="!scope.row.roles || scope.row.roles.length === 0" class="no-roles">
                无角色
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="scope">
            <Authority permission="system:role:edit">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleAssignRoles(scope.row)"
                :disabled="scope.row.is_builtin"
                :title="scope.row.is_builtin ? '内置账号不能修改角色' : ''"
              >
                分配角色
              </el-button>
            </Authority>
            <Authority permission="system:user:edit">
              <el-button type="warning" size="small" @click="handleEditUser(scope.row)">
                编辑
              </el-button>
            </Authority>
            <Authority permission="system:user:edit">
              <el-button 
                :type="scope.row.is_active ? 'danger' : 'success'" 
                size="small" 
                @click="handleToggleUserStatus(scope.row)"
                :disabled="scope.row.id === currentUserId || scope.row.is_builtin"
                :title="scope.row.is_builtin ? '内置账号不能禁用' : (scope.row.id === currentUserId ? '不能禁用自己' : '')"
              >
                {{ scope.row.is_active ? '禁用' : '启用' }}
              </el-button>
            </Authority>
            <Authority permission="system:user:delete">
              <el-button 
                type="danger" 
                size="small" 
                @click="handleDeleteUser(scope.row)"
                :disabled="scope.row.id === currentUserId || scope.row.is_builtin"
                :title="scope.row.is_builtin ? '内置账号不能删除' : (scope.row.id === currentUserId ? '不能删除自己' : '')"
              >
                删除
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="userList.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 角色分配对话框 -->
    <el-dialog v-model="roleDialogVisible" title="角色分配" width="500px" destroy-on-close>
      <div v-if="currentUser">
        <h3>为用户 {{ currentUser.username }} 分配角色</h3>
        
        <el-checkbox-group v-model="selectedRoles">
          <el-row :gutter="20">
            <el-col :span="12" v-for="role in roleList" :key="role.id">
              <el-checkbox :label="role.id">{{ role.name }}</el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roleDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleAssignment">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 用户创建/编辑对话框 -->
    <el-dialog 
      v-model="userFormDialogVisible" 
      :title="isEditMode ? '编辑用户' : '创建用户'" 
      width="500px"
      destroy-on-close
    >
      <el-form 
        ref="userFormRef" 
        :model="userForm" 
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="userForm.password" 
            placeholder="请输入密码" 
            type="password" 
            :required="!isEditMode"
            show-password 
          />
          <div v-if="isEditMode" class="form-tip">如不修改密码请留空</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userFormDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUserForm">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox, FormRules, FormInstance } from 'element-plus'
import { systemApi } from '@/api/system'
import { useUserStore } from '@/stores/user'
import { User, Plus } from '@element-plus/icons-vue'

interface User {
  id: number
  username: string
  email: string
  is_active: boolean
  is_superuser: boolean
  is_builtin: boolean
  roles: Role[]
}

interface Role {
  id: number
  code: string
  name: string
  description: string
  is_default: number
}

const userList = ref<User[]>([])
const roleList = ref<Role[]>([])
const selectedRoles = ref<number[]>([])
const roleDialogVisible = ref(false)
const currentUser = ref<User | null>(null)
const currentUserId = ref<number>(0)

// 用户表单相关
const userFormDialogVisible = ref(false)
const isEditMode = ref(false)
const userFormRef = ref<FormInstance>()
const userForm = reactive({
  id: 0,
  username: '',
  email: '',
  password: '',
  is_active: true,
  is_superuser: false
})

// 表单验证规则
const userFormRules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: isEditMode.value ? false : true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' }
  ]
})

// 初始化当前登录用户ID
const userStore = useUserStore()
if (userStore.userInfo) {
  currentUserId.value = userStore.userInfo.id
}

// 加载用户列表
const loadUsers = async () => {
  try {
    console.log('[UserManagement] 开始加载用户列表')
    const response = await systemApi.getUsers()
    userList.value = response.data
  } catch (error) {
    console.error('[UserManagement] 加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    console.log('[UserManagement] 开始加载角色列表')
    const response = await systemApi.getRoles()
    roleList.value = response.data
  } catch (error) {
    console.error('[UserManagement] 加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  }
}

// 分配角色
const handleAssignRoles = async (row: User) => {
  if (row.is_builtin) {
    ElMessage.warning('不能修改内置账号的角色')
    return
  }
  
  try {
    // 先获取用户的详细信息，包括完整的角色信息
    const response = await systemApi.getUserById(row.id)
    const userDetail = response.data
    
    // 设置当前用户和选中的角色
    currentUser.value = userDetail
    selectedRoles.value = userDetail.roles?.map((role: Role) => role.id) || []
    
    // 如果用户是超级管理员，查找超级管理员角色并自动勾选
    if (userDetail.is_superuser) {
      // 查找code为super_admin的角色
      const superAdminRole = roleList.value.find(role => role.code === 'super_admin')
      if (superAdminRole && !selectedRoles.value.includes(superAdminRole.id)) {
        selectedRoles.value.push(superAdminRole.id)
      }
    }
    
    roleDialogVisible.value = true
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
    ElMessage.error('获取用户详细信息失败')
  }
}

// 保存角色分配
const saveRoleAssignment = async () => {
  if (!currentUser.value) return
  
  try {
    await systemApi.assignRoles(currentUser.value.id, selectedRoles.value)
    
    // 检查是否选择了超级管理员角色
    const superAdminRole = roleList.value.find(role => role.code === 'super_admin')
    const hasSuperAdminRole = !!(superAdminRole && selectedRoles.value.includes(superAdminRole.id))
    
    // 如果用户is_superuser状态与超级管理员角色选择状态不一致，则更新用户状态
    if (currentUser.value.is_superuser !== hasSuperAdminRole) {
      await systemApi.updateUserSuperuser(currentUser.value.id, hasSuperAdminRole)
    }
    
    ElMessage.success('角色分配已保存')
    roleDialogVisible.value = false
    loadUsers() // 重新加载用户列表以获取最新的角色分配
  } catch (error) {
    console.error('保存角色分配失败:', error)
    ElMessage.error('保存角色分配失败')
  }
}

// 创建用户
const handleCreateUser = () => {
  isEditMode.value = false
  // 重置表单
  userForm.id = 0
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.is_active = true
  userForm.is_superuser = false
  
  userFormDialogVisible.value = true
}

// 编辑用户
const handleEditUser = (row: User) => {
  isEditMode.value = true
  userForm.id = row.id
  userForm.username = row.username
  userForm.email = row.email
  userForm.password = '' // 编辑模式下密码置空
  userForm.is_active = row.is_active
  userForm.is_superuser = row.is_superuser
  
  userFormDialogVisible.value = true
}

// 保存用户表单
const saveUserForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEditMode.value) {
          // 编辑用户 - 保持原有的状态和权限设置
          const userData = {
            username: userForm.username,
            email: userForm.email,
            is_active: userForm.is_active,
            is_superuser: userForm.is_superuser
          }
          
          // 只有当密码有值时才包含密码字段
          if (userForm.password) {
            Object.assign(userData, { password: userForm.password })
          }
          
          await systemApi.updateUser(userForm.id, userData)
          ElMessage.success('用户更新成功')
        } else {
          // 创建用户 - 新用户默认启用且为普通用户
          await systemApi.createUser({
            username: userForm.username,
            email: userForm.email,
            password: userForm.password,
            is_active: true,
            is_superuser: false
          })
          ElMessage.success('用户创建成功')
        }
        
        userFormDialogVisible.value = false
        loadUsers() // 重新加载用户列表
      } catch (error: any) {
        console.error('保存用户失败:', error)
        ElMessage.error(error.response?.data?.detail || '操作失败')
      }
    }
  })
}

// 切换用户状态（启用/禁用）
const handleToggleUserStatus = async (row: User) => {
  if (row.id === currentUserId.value) {
    ElMessage.warning('不能修改自己的账户状态')
    return
  }
  
  if (row.is_builtin) {
    ElMessage.warning('不能禁用内置账号')
    return
  }
  
  try {
    await systemApi.updateUserActive(row.id, !row.is_active)
    ElMessage.success(`用户已${row.is_active ? '禁用' : '启用'}`)
    loadUsers() // 重新加载用户列表
  } catch (error: any) {
    console.error('更新用户状态失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 删除用户
const handleDeleteUser = async (row: User) => {
  if (row.id === currentUserId.value) {
    ElMessage.warning('不能删除自己的账户')
    return
  }
  
  if (row.is_builtin) {
    ElMessage.warning('不能删除内置账号')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.username}"吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await systemApi.deleteUser(row.id)
    ElMessage.success('用户删除成功')
    loadUsers() // 重新加载用户列表
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error(error.response?.data?.detail || '删除失败')
    }
  }
}

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 计算分页后的用户列表
const paginatedUsers = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return userList.value.slice(startIndex, startIndex + pageSize.value)
})

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

// 处理当前页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

onMounted(() => {
  loadUsers()
  loadRoles()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.user-table-row {
  transition: all 0.3s;
  height: 56px;
}

.user-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.user-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.role-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.role-tag {
  margin-right: 4px;
}

.no-roles {
  color: #909399;
  font-style: italic;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.builtin-tag {
  font-size: 10px;
  margin-left: 5px;
}
</style> 