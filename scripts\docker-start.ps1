# OPS平台Docker启动脚本 (PowerShell版本)
# 使用方法: .\scripts\docker-start.ps1 [环境]

param(
    [string]$Environment = "production"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色定义
function Write-Info { Write-Host "[INFO] $args" -ForegroundColor Green }
function Write-Warning { Write-Host "[WARNING] $args" -ForegroundColor Yellow }
function Write-Error { Write-Host "[ERROR] $args" -ForegroundColor Red }
function Write-Step { Write-Host "[STEP] $args" -ForegroundColor Blue }

# 检查Docker是否安装
function Test-Docker {
    try {
        $dockerVersion = docker --version
        $composeVersion = docker-compose --version
        Write-Info "Docker环境检查通过"
        Write-Info "Docker版本: $dockerVersion"
        Write-Info "Compose版本: $composeVersion"
    }
    catch {
        Write-Error "Docker未安装或未启动，请先安装并启动Docker Desktop"
        exit 1
    }
}

# 检查环境变量文件
function Test-EnvFile {
    if (-not (Test-Path ".env")) {
        Write-Warning "未找到.env文件，将使用默认配置"
        if (Test-Path "env.example") {
            Write-Info "复制env.example为.env"
            Copy-Item "env.example" ".env"
        }
    }
    else {
        Write-Info "找到.env配置文件"
    }
}

# 停止现有服务
function Stop-Services {
    Write-Step "停止现有服务..."
    docker-compose down --remove-orphans
}

# 清理资源
function Clear-DockerResources {
    Write-Step "清理Docker资源..."
    docker system prune -f
}

# 构建镜像
function Build-Images {
    Write-Step "构建Docker镜像..."
    docker-compose build --no-cache
}

# 启动服务
function Start-Services {
    Write-Step "启动服务..."
    docker-compose up -d
    
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 10
    
    # 检查服务状态
    Write-Step "检查服务状态..."
    docker-compose ps
}

# 运行数据库迁移
function Run-Migrations {
    Write-Step "运行数据库迁移..."
    docker-compose run --rm migration
}

# 显示访问信息
function Show-AccessInfo {
    Write-Info "服务启动完成！"
    Write-Host ""
    Write-Host "访问地址:" -ForegroundColor Cyan
    Write-Host "  前端应用: http://localhost" -ForegroundColor White
    Write-Host "  后端API:  http://localhost:8000" -ForegroundColor White
    Write-Host "  数据库:   localhost:5432" -ForegroundColor White
    Write-Host "  Redis:    localhost:6379" -ForegroundColor White
    Write-Host ""
    Write-Host "常用命令:" -ForegroundColor Cyan
    Write-Host "  查看日志: docker-compose logs -f [服务名]" -ForegroundColor White
    Write-Host "  停止服务: docker-compose down" -ForegroundColor White
    Write-Host "  重启服务: docker-compose restart" -ForegroundColor White
    Write-Host ""
}

# 主函数
function Main {
    Write-Info "开始部署OPS平台 (环境: $Environment)"
    Write-Host ""
    
    Test-Docker
    Test-EnvFile
    Stop-Services
    Clear-DockerResources
    Build-Images
    Start-Services
    Run-Migrations
    Show-AccessInfo
}

# 脚本入口
try {
    Main
}
catch {
    Write-Error "部署过程中发生错误: $($_.Exception.Message)"
    Write-Error "请检查错误日志并重试"
    exit 1
}
