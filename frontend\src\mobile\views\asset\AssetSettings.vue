<template>
  <div class="mobile-asset-settings">
    <!-- 导航栏 -->
    <van-nav-bar
      title="资产设置"
      left-arrow
      @click-left="goBack"
    />

    <!-- 主要内容 -->
    <div class="settings-content">
      <!-- 下拉刷新 -->
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <!-- 资产编号规则列表 -->
        <van-cell-group title="资产编号规则" inset>
          <van-empty 
            v-if="!loading && settings.length === 0" 
            description="暂无设置记录"
            image="search"
          />
          
          <van-cell
            v-for="setting in settings"
            :key="setting.id"
            :title="setting.company"
            is-link
            @click="handleEdit(setting)"
          >
            <template #label>
              <div class="rule-preview">
                <span class="rule-text">{{ formatRule(setting.asset_number_rule) }}</span>
                <span class="example-text">示例: {{ generateExample(setting.asset_number_rule) }}</span>
              </div>
            </template>
            <template #right-icon>
              <van-icon name="edit" @click.stop="handleEdit(setting)" />
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 加载状态 -->
        <van-loading v-if="loading" class="loading-center" vertical>
          加载中...
        </van-loading>
      </van-pull-refresh>
    </div>

    <!-- 添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAdd"
    />

    <!-- 编辑弹窗 -->
    <van-popup
      v-model:show="showEditDialog"
      position="bottom"
      :style="{ height: 'var(--mobile-popup-max-height, 68svh)' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="edit-popup">
        <div class="popup-header">
          <h3>{{ dialogTitle }}</h3>
        </div>
        
        <div class="popup-content">
          <van-form
            ref="formRef"
            @submit="handleSubmit"
            @failed="onFormFailed"
          >
            <van-cell-group inset>
              <van-field
                v-model="form.company"
                name="company"
                label="公司名称"
                placeholder="请输入公司名称"
                :disabled="dialogMode === 'edit'"
                :rules="[{ required: true, message: '请输入公司名称' }]"
              />
              
              <van-field
                v-model="form.asset_number_rule.prefix"
                name="prefix"
                label="编号前缀"
                placeholder="请输入编号前缀，如：GD"
                :rules="[{ required: true, message: '请输入编号前缀' }]"
              />
              
              <van-field
                v-model="form.asset_number_rule.number_length"
                name="number_length"
                label="数字长度"
                type="digit"
                placeholder="请输入数字长度"
                :rules="[
                  { required: true, message: '请输入数字长度' },
                  { 
                    validator: (value) => value >= 1 && value <= 12,
                    message: '数字长度必须在1-12之间'
                  }
                ]"
              />
              
              <van-field
                v-model="form.asset_number_rule.start_number"
                name="start_number"
                label="起始数字"
                type="digit"
                placeholder="请输入起始数字"
                :rules="[{ required: true, message: '请输入起始数字' }]"
              />
            </van-cell-group>

            <!-- 预览示例 -->
            <van-cell-group title="编号预览" inset>
              <van-cell
                title="生成示例"
                :value="generateExample(form.asset_number_rule)"
                label="根据当前设置生成的编号示例"
              />
            </van-cell-group>

            <!-- 操作按钮 -->
            <MobilePopupFooter :buttons="editFormFooterButtons" />
          </van-form>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { assetSettingsApi } from '@/api/asset_settings'
import type { AssetSettings, AssetNumberRule } from '@/types/asset_settings'
import MobilePopupFooter from '@/mobile/components/MobilePopupFooter.vue'

const router = useRouter()

// 数据状态
const loading = ref(false)
const refreshing = ref(false)
const submitting = ref(false)
const deleting = ref(false)
const settings = ref<AssetSettings[]>([])

// 弹窗状态
const showEditDialog = ref(false)
const dialogMode = ref<'create' | 'edit'>('create')
const formRef = ref()

// 表单数据
const defaultForm = {
  company: '',
  asset_number_rule: {
    prefix: '',
    number_length: 8,
    start_number: 1
  }
}

const form = reactive({
  company: '',
  asset_number_rule: {
    prefix: '',
    number_length: 8,
    start_number: 1
  }
})

// 当前编辑的设置
const currentSetting = ref<AssetSettings | null>(null)

// 计算属性
const dialogTitle = computed(() => 
  dialogMode.value === 'create' ? '新增设置' : '编辑设置'
)

// 返回上一页
const goBack = () => {
  router.back()
}

// 下拉刷新
const onRefresh = async () => {
  await fetchSettings()
  refreshing.value = false
  showToast('刷新成功')
}

// 获取设置列表
const fetchSettings = async () => {
  try {
    loading.value = true
    const response = await assetSettingsApi.getAssetSettings({})
    settings.value = response.data || response || []
  } catch (error) {
    console.error('获取设置列表失败:', error)
    showToast('获取设置列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化规则显示
const formatRule = (rule: AssetNumberRule) => {
  return `前缀: ${rule.prefix} | 长度: ${rule.number_length} | 起始: ${rule.start_number}`
}

// 生成示例编号
const generateExample = (rule: AssetNumberRule) => {
  const { prefix, number_length, start_number } = rule
  if (!prefix || !number_length) return '--'
  return `${prefix}${String(start_number).padStart(number_length, '0')}`
}

// 新增设置
const handleAdd = () => {
  dialogMode.value = 'create'
  Object.assign(form, defaultForm)
  currentSetting.value = null
  showEditDialog.value = true
}

// 编辑设置
const handleEdit = (setting: AssetSettings) => {
  dialogMode.value = 'edit'
  Object.assign(form, {
    company: setting.company,
    asset_number_rule: { ...setting.asset_number_rule }
  })
  currentSetting.value = setting
  showEditDialog.value = true
}

// 表单提交
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    if (dialogMode.value === 'create') {
      await assetSettingsApi.createAssetSettings(form)
      showToast('创建成功')
    } else {
      await assetSettingsApi.updateAssetSettings(form.company, {
        asset_number_rule: form.asset_number_rule
      })
      showToast('更新成功')
    }
    
    showEditDialog.value = false
    fetchSettings()
  } catch (error: any) {
    console.error('保存失败:', error)
    showToast(error.response?.data?.detail || '保存失败')
  } finally {
    submitting.value = false
  }
}

// 表单验证失败
const onFormFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  showToast('请检查表单输入')
}

// 删除设置
const handleDelete = async () => {
  if (!currentSetting.value) return
  
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除 ${currentSetting.value.company} 的资产设置吗？`
    })
    
    deleting.value = true
    await assetSettingsApi.deleteAssetSettings(currentSetting.value.company)
    showToast('删除成功')
    showEditDialog.value = false
    fetchSettings()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      showToast('删除失败')
    }
  } finally {
    deleting.value = false
  }
}

// 编辑表单底部按钮配置
const editFormFooterButtons = computed(() => {
  const buttons = []
  
  buttons.push({
    text: dialogMode.value === 'create' ? '创建' : '更新',
    type: 'primary' as const,
    loading: submitting.value,
    onClick: handleSubmit
  })
  
  if (dialogMode.value === 'edit') {
    buttons.push({
      text: '删除设置',
      type: 'danger' as const,
      loading: deleting.value,
      onClick: handleDelete
    })
  }
  
  return buttons
})

// 初始化
onMounted(() => {
  fetchSettings()
})
</script>

<style lang="scss" scoped>
.mobile-asset-settings {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.settings-content {
  padding: 16px 0;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.rule-preview {
  margin-top: 4px;
  
  .rule-text {
    display: block;
    font-size: 12px;
    color: #646566;
    margin-bottom: 2px;
  }
  
  .example-text {
    display: block;
    font-size: 12px;
    color: var(--van-primary-color);
    font-weight: 500;
  }
}

.edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
    }
  }
  
  .popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
}



// 浮动按钮样式调整
:deep(.van-floating-bubble) {
  bottom: 80px;
  right: 20px;
}
</style> 