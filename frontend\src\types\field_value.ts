export interface FieldValue {
  id: number
  field_name: string
  field_value: string
  description?: string
  created_at: string
  updated_at: string
}

export type FieldValueCreate = Omit<FieldValue, 'id' | 'created_at' | 'updated_at'>

export type FieldValueUpdate = Partial<FieldValueCreate>

// 可选的字段名称
export const FIELD_NAMES = {
  COMPANY: 'company',
  NAME: 'name',
  STATUS: 'status',
  SPECIFICATION: 'specification',
  PERSONNEL: 'personnel',           // 统一的人员字段
  DEPARTMENT: 'department',         // 统一的部门字段
  LOCATION: 'location',
  INSPECTOR: 'inspector',
  SUPPLIER: 'supplier',             // 供应商
  MANUFACTURER: 'manufacturer',     // 制造商
  PURCHASER: 'purchaser',           // 采购人
  CATEGORY: 'category'              // 资产类别
} as const

export type FieldName = typeof FIELD_NAMES[keyof typeof FIELD_NAMES]

// 字段名称显示文本
export const FIELD_NAME_LABELS: Record<FieldName, string> = {
  [FIELD_NAMES.COMPANY]: '公司',
  [FIELD_NAMES.NAME]: '资产名称',
  [FIELD_NAMES.STATUS]: '资产状态',
  [FIELD_NAMES.SPECIFICATION]: '资产规格',
  [FIELD_NAMES.PERSONNEL]: '人员',
  [FIELD_NAMES.DEPARTMENT]: '部门',
  [FIELD_NAMES.LOCATION]: '资产存放位置',
  [FIELD_NAMES.INSPECTOR]: '验收人',
  [FIELD_NAMES.SUPPLIER]: '供应商',
  [FIELD_NAMES.MANUFACTURER]: '制造商',
  [FIELD_NAMES.PURCHASER]: '采购人',
  [FIELD_NAMES.CATEGORY]: '资产类别'
} 