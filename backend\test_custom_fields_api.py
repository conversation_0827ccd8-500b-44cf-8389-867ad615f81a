#!/usr/bin/env python3
import requests
import json

def test_custom_fields_api():
    """测试自定义字段API"""
    base_url = "http://localhost:8000/api/v1"
    
    # 首先登录获取token  
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 尝试登录...")
    login_response = requests.post(f"{base_url}/auth/login", data=login_data)
    print(f"登录响应状态: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return
    
    login_result = login_response.json()
    token = login_result.get("access_token")
    print(f"✅ 登录成功，获取到token: {token[:20]}...")
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试自定义字段列表API
    print("\n📋 测试自定义字段列表API...")
    fields_response = requests.get(f"{base_url}/custom-fields/", headers=headers)
    print(f"响应状态: {fields_response.status_code}")
    
    if fields_response.status_code == 200:
        fields_data = fields_response.json()
        print(f"✅ API调用成功")
        print(f"   返回数据类型: {type(fields_data)}")
        print(f"   响应内容: {json.dumps(fields_data, ensure_ascii=False, indent=2)}")
    else:
        print(f"❌ API调用失败")
        print(f"   错误内容: {fields_response.text}")
        
    # 测试创建自定义字段
    print("\n🆕 测试创建自定义字段...")
    create_data = {
        "name": "test_field",
        "label": "测试字段",
        "field_type": "text",
        "description": "这是一个测试字段",
        "is_required": False,
        "applies_to": "asset"
    }
    
    create_response = requests.post(f"{base_url}/custom-fields/", headers=headers, json=create_data)
    print(f"创建响应状态: {create_response.status_code}")
    
    if create_response.status_code in [200, 201]:
        create_result = create_response.json()
        print(f"✅ 字段创建成功")
        print(f"   创建的字段: {json.dumps(create_result, ensure_ascii=False, indent=2)}")
        
        # 清理测试数据
        field_id = create_result.get("id")
        if field_id:
            print(f"\n🗑️ 清理测试数据...")
            delete_response = requests.delete(f"{base_url}/custom-fields/{field_id}", headers=headers)
            print(f"删除响应状态: {delete_response.status_code}")
            
    else:
        print(f"❌ 字段创建失败")
        print(f"   错误内容: {create_response.text}")

if __name__ == '__main__':
    test_custom_fields_api() 