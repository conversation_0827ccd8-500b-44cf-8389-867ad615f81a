# AD用户状态切换缓存不更新问题修复

## 问题描述

在AD域管理中切换账号状态时，虽然后端操作成功并显示"启用成功"，但是前端用户列表仍然显示原来的状态（如禁用），需要手动刷新页面才能看到最新状态。

## 问题分析

### 1. 日志分析

从后端日志可以看出：
```
用户状态已切换: 111850827, 当前状态: 禁用
CLEAR PATTERN request_cache:/api/v1/ad/management/users/111850827/toggle* - 0 keys
缓存命中: /api/v1/ad/management/users:ou_dn=...
```

**关键问题**：
- 后端确实成功切换了用户状态
- 缓存清理操作执行了，但清理了0个键（无效清理）
- 后续的用户列表查询仍然从缓存获取旧数据

### 2. 根本原因

#### 缓存路径匹配问题
- 用户状态切换API路径：`/api/v1/ad/management/users/{username}/toggle`
- 用户列表查询API路径：`/api/v1/ad/management/users`
- 缓存依赖清理中只匹配了 `/ad/users`，而实际路径是 `/ad/management/users`

#### 缓存键生成问题
- `get_cache_prefix_for_path` 方法只能移除数字ID，不能处理`toggle`等动作路径
- 导致缓存前缀生成不正确，清理时无法匹配到正确的缓存键

## 修复方案

### 1. 修复缓存依赖清理逻辑

**文件**: `backend/app/middleware/redis_cache_logging.py`

```python
# 修复前
elif "/ad/users" in path:
    # AD用户变更影响组信息
    dependencies.append("request_cache:/api/v1/ad/groups*")

# 修复后  
elif "/ad/management/users" in path or "/ad/users" in path:
    # AD用户变更影响用户列表和组信息
    dependencies.append("request_cache:/api/v1/ad/management/users*")
    dependencies.append("request_cache:/api/v1/ad/users*")
    dependencies.append("request_cache:/api/v1/ad/groups*")
```

### 2. 修复缓存前缀生成逻辑

**文件**: `backend/app/middleware/redis_cache_logging.py`

```python
@staticmethod
def get_cache_prefix_for_path(path: str) -> str:
    """
    根据请求路径获取缓存前缀
    例如：/api/v1/users/123 -> request_cache:/api/v1/users
    例如：/api/v1/ad/management/users/123/toggle -> request_cache:/api/v1/ad/management/users
    """
    # 移除最后一个路径段（如果是ID或动作）
    parts = path.rstrip('/').split('/')
    
    # 从末尾开始检查并移除ID或动作路径段
    while parts and (parts[-1].isdigit() or parts[-1] in ['toggle', 'move', 'fix-cn', 'active', 'superuser']):
        parts.pop()
        
    base_path = '/'.join(parts)
    return f"request_cache:{base_path}"
```

### 3. 增强AD客户端缓存清理

**文件**: `backend/app/utils/ad_client.py`

在 `toggle_user_status` 方法成功后添加缓存清理逻辑：

```python
self._logger.info(f"用户状态已切换: {username}, 当前状态: {'禁用' if should_disable else '启用'}")

# 清除相关缓存
try:
    # 清除用户详细信息缓存
    user_cache_key = self._get_cache_key("get_user", username)
    self._cache.delete(user_cache_key)
    
    # 清除用户批量查询缓存
    batch_cache_pattern = f"ad:get_users_batch:*{username}*"
    self._cache.clear_pattern(batch_cache_pattern)
    
    self._logger.debug(f"已清除用户 {username} 的相关缓存")
except Exception as cache_error:
    self._logger.warning(f"清除用户缓存失败: {str(cache_error)}")

return True
```

## 修复效果验证

### 1. 缓存键生成测试
```python
# 状态切换路径
toggle_path = "/api/v1/ad/management/users/111850827/toggle"
prefix = CacheKeyGenerator.get_cache_prefix_for_path(toggle_path)
# 期望结果: "request_cache:/api/v1/ad/management/users"

# 用户列表路径  
list_path = "/api/v1/ad/management/users"
list_prefix = CacheKeyGenerator.get_cache_prefix_for_path(list_path)
# 期望结果: "request_cache:/api/v1/ad/management/users"
```

### 2. 缓存依赖清理测试
```python
toggle_path = "/api/v1/ad/management/users/111850827/toggle"
dependencies = middleware._get_cache_dependencies(toggle_path)
# 期望结果包含: "request_cache:/api/v1/ad/management/users*"
```

### 3. 实际操作验证
1. 在AD管理页面切换用户状态
2. 观察后端日志，应该看到：
   ```
   用户状态已切换: {username}, 当前状态: {新状态}
   已清除用户 {username} 的相关缓存
   智能清除缓存: 方法=POST, 路径={toggle_path}, 总清除键数量>0
   ```
3. 前端用户列表应该立即显示最新状态，无需手动刷新

## 相关文件

- `backend/app/middleware/redis_cache_logging.py` - 缓存中间件
- `backend/app/utils/ad_client.py` - AD客户端
- `backend/test_ad_cache_fix.py` - 测试脚本（可选）

## 注意事项

1. **向后兼容性**: 修复同时支持旧的 `/ad/users` 路径和新的 `/ad/management/users` 路径
2. **性能影响**: 缓存清理操作是异步的，不会影响API响应时间
3. **错误处理**: 缓存清理失败不会影响主要业务逻辑
4. **扩展性**: 新的动作路径可以通过添加到动作列表中轻松支持

## 测试建议

1. 运行测试脚本验证修复逻辑
2. 在测试环境验证用户状态切换功能
3. 监控生产环境的缓存命中率和清理效果
4. 检查其他类似的缓存依赖问题 