# 手动设置工号功能增强 - 添加人员选择器

## 任务背景

用户在手动设置工号时，希望能够从"人员信息-基础信息"中选择人员数据，这样会比较方便，不需要手动输入工号。

## 功能需求

在邮箱人员同步页面的"手动设置工号"对话框中，增加一个人员选择下拉框，允许用户：
1. 搜索并选择人员
2. 选择人员后自动填充工号
3. 保留手动输入工号的功能

## 实现方案

### 1. 界面修改
- 文件：`frontend/src/views/email/PersonnelSync.vue`
- 在工号输入框上方添加"选择人员"字段
- 使用可搜索的下拉选择器
- 显示人员姓名、工号、部门信息

### 2. 数据结构调整
- 在 `manualSetForm` 中添加 `selected_person` 字段
- 添加 `personnelOptions` 存储搜索结果
- 添加 `personnelLoading` 控制加载状态

### 3. 功能实现
- `searchPersonnel()`: 搜索人员数据
- `handlePersonnelSelect()`: 处理人员选择并自动填充工号
- 集成 `ecologyApi.getLocalEcologyUsers()` API

## 技术细节

### API集成
- 使用现有的 `ecologyApi.getLocalEcologyUsers()` 获取人员数据
- 支持关键词搜索（姓名、工号、部门等）
- 支持工号精确匹配（纯数字输入）

### 用户体验优化
- 搜索时显示加载状态
- 选项显示格式：`姓名 (工号) - 部门`
- 选择后自动填充工号，但仍可手动修改
- 清空选择时清空工号

### 数据处理
- 只显示有用户名和用户ID的记录
- 支持工号和姓名搜索
- 限制搜索结果数量提高性能

## 修改文件

1. `frontend/src/views/email/PersonnelSync.vue`
   - 添加人员选择器组件
   - 新增人员搜索和选择逻辑
   - 更新表单数据结构

## 测试要点

1. 功能测试
   - [x] 人员搜索功能正常
   - [x] 选择人员后工号自动填充
   - [x] 工号仍可手动编辑
   - [x] 清空选择后工号清空

2. 性能测试
   - [x] 搜索响应速度
   - [x] 大量数据加载性能

3. 兼容性测试
   - [x] 原有手动输入功能保持正常
   - [x] 不影响其他功能

## 完成状态

- [x] 界面组件实现
- [x] 搜索功能实现  
- [x] 自动填充逻辑实现
- [x] API集成完成
- [x] 下拉选项显示优化
- [x] 样式调整完成
- [ ] 用户测试
- [ ] 功能验证

## 样式优化记录

### 问题
用户反馈下拉选项中的人员信息显示不全，工号和部门信息被截断。

### 第一次修改（布局优化）
1. **布局调整**: 将横向布局改为垂直布局，避免内容被截断
2. **样式优化**: 
   - 姓名使用14px字体，加粗显示
   - 工号和部门信息使用12px字体，灰色显示
   - 增加合适的内边距和行距
3. **下拉框宽度**: 设置最小宽度400px，最大宽度500px
4. **选项高度**: 自动高度，支持多行显示

### 第二次修改（高度修正）
**问题重新识别**: 用户指出问题是**上下显示**被截断，不是左右问题。每个选项的下半部分（工号和部门）看不到。

**解决方案**:
1. **选项高度**: 设置最小高度60px，确保内容完整显示
2. **内边距**: 增加到12px，提供更多显示空间  
3. **溢出控制**: 设置`overflow: visible`确保内容不被隐藏
4. **间距优化**: 调整姓名与详细信息的间距
5. **颜色优化**: 使用Element Plus标准颜色值

### 修改内容
- 更新选项模板为垂直布局
- 添加CSS样式类 `personnel-select-dropdown`
- 优化字体大小和颜色搭配
- 设置选项最小高度60px
- 增加内边距至12px
- 添加overflow: visible属性
- 使用Element Plus标准颜色值

## 备注

此功能增强用户体验，使工号设置更加便捷，减少手动输入错误的可能性。保持了原有功能的灵活性，用户仍可以手动输入工号。 