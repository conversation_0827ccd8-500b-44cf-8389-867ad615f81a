"""
功能完整性测试
测试人员邮箱同步系统的所有核心功能
"""

import pytest
import asyncio
import logging
from typing import Dict, List, Any
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

from app.main import app
from app.database import get_db
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember, PersonnelSyncLog, EmailCreationRequest
from app.services.email_extid_completion import EmailExtidCompletionService
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.services.data_backup import DataBackupService
from app.crud.email import email_member
from app.schemas.email_personnel_sync import (
    ExtidCompletionStats,
    PersonnelSyncStats,
    DataBackupInfo
)

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class TestFunctionalityComplete:
    """功能完整性测试类"""
    
    @pytest.fixture(scope="class")
    def client(self):
        """测试客户端"""
        return TestClient(app)
    
    @pytest.fixture(scope="class")
    def db_session(self):
        """数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture(scope="class")
    def test_data(self, db_session: Session):
        """准备测试数据"""
        # 创建测试用的人员信息
        test_users = [
            EcologyUser(
                user_id=1001,
                user_name="张三",
                job_number="E001",
                dept_name="技术部",
                job_title_name="软件工程师",
                mobile="13800138001",
                status="active"
            ),
            EcologyUser(
                user_id=1002,
                user_name="李四",
                job_number="E002",
                dept_name="产品部",
                job_title_name="产品经理",
                mobile="13800138002",
                status="active"
            ),
            EcologyUser(
                user_id=1003,
                user_name="王五",
                job_number="E003",
                dept_name="技术部",
                job_title_name="测试工程师",
                mobile="13800138003",
                status="inactive"  # 离职员工
            )
        ]
        
        # 创建测试用的邮箱成员
        test_email_members = [
            EmailMember(
                extid="E001",
                email="<EMAIL>",
                name="张三",
                department_id="tech",
                position="软件工程师",
                mobile="13800138001",
                is_active=True
            ),
            EmailMember(
                extid="E002",
                email="<EMAIL>",
                name="李四",
                department_id="product",
                position="产品经理",
                mobile="13800138002",
                is_active=True
            )
        ]
        
        # 添加到数据库
        for user in test_users:
            db_session.merge(user)
        for member in test_email_members:
            db_session.merge(member)
        
        db_session.commit()
        
        return {
            "users": test_users,
            "email_members": test_email_members
        }
    
    def test_extid_completion_functionality(self, db_session: Session, test_data: Dict):
        """测试工号补全功能完整性"""
        logger.info("=== 测试工号补全功能完整性 ===")
        
        # 初始化服务
        completion_service = EmailExtidCompletionService(db_session)
        
        # 测试统计信息获取
        stats = completion_service.get_completion_stats()
        assert isinstance(stats, ExtidCompletionStats)
        assert stats.total_email_users >= 0
        assert stats.users_with_extid >= 0
        assert stats.users_without_extid >= 0
        
        logger.info(f"工号补全统计: 总用户数={stats.total_email_users}, "
                   f"有工号={stats.users_with_extid}, 无工号={stats.users_without_extid}")
        
        # 测试姓名匹配功能
        matches = completion_service.find_name_matches(page=1, page_size=10)
        assert hasattr(matches, 'items')
        assert hasattr(matches, 'total')
        assert hasattr(matches, 'page')
        assert hasattr(matches, 'page_size')
        
        logger.info(f"姓名匹配结果: 找到 {matches.total} 个匹配项")
        
        # 测试分页功能
        if matches.total > 0:
            # 测试不同页码
            page2_matches = completion_service.find_name_matches(page=2, page_size=5)
            assert page2_matches.page == 2
            assert page2_matches.page_size == 5
        
        logger.info("✅ 工号补全功能完整性测试通过")
    
    def test_personnel_sync_functionality(self, db_session: Session, test_data: Dict):
        """测试人员同步功能完整性"""
        logger.info("=== 测试人员同步功能完整性 ===")
        
        # 初始化服务
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 测试试运行同步
        dry_run_result = asyncio.run(sync_service.sync_personnel_to_email(dry_run=True))
        assert isinstance(dry_run_result, PersonnelSyncStats)
        assert dry_run_result.processed_count >= 0
        assert dry_run_result.created_requests >= 0
        assert dry_run_result.updated_count >= 0
        assert dry_run_result.disabled_count >= 0
        
        logger.info(f"试运行同步结果: 处理={dry_run_result.processed_count}, "
                   f"创建申请={dry_run_result.created_requests}, "
                   f"更新={dry_run_result.updated_count}, "
                   f"禁用={dry_run_result.disabled_count}")
        
        # 测试数据一致性检查
        consistency_result = asyncio.run(sync_service.check_data_consistency())
        assert hasattr(consistency_result, 'total_personnel')
        assert hasattr(consistency_result, 'total_email_members')
        assert hasattr(consistency_result, 'matched_count')
        assert hasattr(consistency_result, 'unmatched_personnel')
        assert hasattr(consistency_result, 'unmatched_email_members')
        
        logger.info(f"数据一致性检查: 人员总数={consistency_result.total_personnel}, "
                   f"邮箱总数={consistency_result.total_email_members}, "
                   f"匹配数={consistency_result.matched_count}")
        
        logger.info("✅ 人员同步功能完整性测试通过")
    
    def test_data_backup_functionality(self, db_session: Session):
        """测试数据备份功能完整性"""
        logger.info("=== 测试数据备份功能完整性 ===")
        
        # 初始化服务
        backup_service = DataBackupService(db_session)
        
        # 测试创建备份
        backup_result = backup_service.create_backup("test_backup", "功能完整性测试备份")
        assert isinstance(backup_result, DataBackupInfo)
        assert backup_result.name == "test_backup"
        assert backup_result.description == "功能完整性测试备份"
        assert backup_result.file_path is not None
        
        logger.info(f"创建备份成功: {backup_result.name}, 文件路径: {backup_result.file_path}")
        
        # 测试备份列表
        backup_list = backup_service.list_backups()
        assert isinstance(backup_list, list)
        assert len(backup_list) > 0
        
        # 验证刚创建的备份在列表中
        backup_names = [backup.name for backup in backup_list]
        assert "test_backup" in backup_names
        
        logger.info(f"备份列表: 共 {len(backup_list)} 个备份")
        
        # 测试删除备份
        delete_result = backup_service.delete_backup("test_backup")
        assert delete_result is True
        
        logger.info("✅ 数据备份功能完整性测试通过")
    
    def test_api_endpoints_functionality(self, client: TestClient):
        """测试API接口功能完整性"""
        logger.info("=== 测试API接口功能完整性 ===")
        
        # 测试工号补全相关接口
        endpoints_to_test = [
            ("/api/v1/email/personnel-sync/extid-completion/stats", "GET"),
            ("/api/v1/email/personnel-sync/extid-completion/matches", "GET"),
            ("/api/v1/email/personnel-sync/data-backup/list", "GET"),
            ("/api/v1/email/personnel-sync/sync/config", "GET"),
        ]
        
        for endpoint, method in endpoints_to_test:
            if method == "GET":
                response = client.get(endpoint)
            elif method == "POST":
                response = client.post(endpoint, json={})
            
            # 检查响应状态码（200或401都是正常的，401表示需要认证）
            assert response.status_code in [200, 401, 422], f"接口 {endpoint} 返回异常状态码: {response.status_code}"
            
            logger.info(f"接口 {endpoint} 测试通过，状态码: {response.status_code}")
        
        logger.info("✅ API接口功能完整性测试通过")
    
    def test_error_handling_functionality(self, db_session: Session):
        """测试错误处理功能完整性"""
        logger.info("=== 测试错误处理功能完整性 ===")
        
        # 测试无效参数处理
        completion_service = EmailExtidCompletionService(db_session)
        
        # 测试无效页码
        try:
            matches = completion_service.find_name_matches(page=0, page_size=10)
            # 应该处理无效页码，返回第一页
            assert matches.page >= 1
        except Exception as e:
            # 或者抛出合适的异常
            assert isinstance(e, (ValueError, TypeError))
        
        # 测试无效页面大小
        try:
            matches = completion_service.find_name_matches(page=1, page_size=0)
            # 应该处理无效页面大小
            assert matches.page_size > 0
        except Exception as e:
            # 或者抛出合适的异常
            assert isinstance(e, (ValueError, TypeError))
        
        logger.info("✅ 错误处理功能完整性测试通过")

if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v"])
