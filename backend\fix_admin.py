from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.models.user import User

def fix_admin_permissions():
    db = SessionLocal()
    try:
        # 查找admin用户
        admin = db.query(User).filter(User.username == "admin").first()
        
        if not admin:
            print("找不到admin用户！将创建新的admin用户...")
            from app.utils import get_password_hash
            # 创建超级管理员账户
            hashed_password = get_password_hash("admin123")  # 默认密码
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password,
                is_active=True,
                is_superuser=True
            )
            db.add(admin_user)
            db.commit()
            print("超级管理员账户创建成功！")
        else:
            print(f"找到admin用户，当前is_superuser值: {admin.is_superuser}")
            if not admin.is_superuser:
                admin.is_superuser = True
                db.commit()
                print("已将admin用户设置为超级管理员！")
            else:
                print("admin用户已经是超级管理员，无需修改。")
    finally:
        db.close()

if __name__ == "__main__":
    print("正在修复admin用户权限...")
    fix_admin_permissions()
    print("操作完成！") 