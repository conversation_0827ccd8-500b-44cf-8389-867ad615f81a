"""add_asset_department_and_location_fields

Revision ID: 31700079acc7
Revises: ef79f48b0b19
Create Date: 2024-12-20 09:17:13.514711

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '31700079acc7'
down_revision: Union[str, None] = 'ef79f48b0b19'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('custodian_department', sa.String(length=100), nullable=True, comment='领用人部门'))
    op.add_column('assets', sa.Column('user_department', sa.String(length=100), nullable=True, comment='使用人部门'))
    op.add_column('assets', sa.Column('location', sa.String(length=200), nullable=True, comment='资产存放位置'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assets', 'location')
    op.drop_column('assets', 'user_department')
    op.drop_column('assets', 'custodian_department')
    # ### end Alembic commands ###
