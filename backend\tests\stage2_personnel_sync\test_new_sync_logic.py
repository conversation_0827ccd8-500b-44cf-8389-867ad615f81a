"""
测试新的人员邮箱同步逻辑
验证：
1. 不自动创建新账号
2. 自动禁用离职账号
3. 创建邮箱申请记录
4. 管理员审批功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
    if response.status_code == 200:
        result = response.json()
        return result.get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_new_sync_logic(token):
    """测试新的同步逻辑"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("=== 测试新的同步逻辑（试运行） ===")
    sync_data = {
        "full_sync": True,
        "dry_run": True
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/personnel-email-sync/sync/trigger", 
                           headers=headers, json=sync_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"同步结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 分析结果
        if result.get("stats"):
            stats = result["stats"]
            print(f"\n同步统计:")
            print(f"  处理数量: {stats.get('processed_count', 0)}")
            print(f"  创建申请: {stats.get('created_count', 0)}")
            print(f"  更新数量: {stats.get('updated_count', 0)}")
            print(f"  禁用数量: {stats.get('disabled_count', 0)}")
            print(f"  错误数量: {stats.get('error_count', 0)}")
        
        return True
    else:
        print(f"错误: {response.text}")
        return False

def test_creation_requests(token):
    """测试邮箱创建申请功能"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试邮箱创建申请列表 ===")
    response = requests.get(f"{BASE_URL}/api/v1/email-creation-requests/requests", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        requests_list = response.json()
        print(f"申请数量: {len(requests_list)}")
        
        if requests_list:
            print("前5个申请:")
            for req in requests_list[:5]:
                print(f"  ID: {req['id']}, 工号: {req['job_number']}, 姓名: {req['user_name']}, 状态: {req['status']}")
        
        return True
    else:
        print(f"错误: {response.text}")
        return False

def test_requests_stats(token):
    """测试申请统计信息"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试申请统计信息 ===")
    response = requests.get(f"{BASE_URL}/api/v1/email-creation-requests/requests/stats", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"统计信息: {json.dumps(stats, indent=2, ensure_ascii=False)}")
        
        print(f"\n邮箱使用情况:")
        print(f"  当前活跃邮箱: {stats.get('active_email_count', 0)}")
        print(f"  邮箱配额: {stats.get('email_quota', 0)}")
        print(f"  可用配额: {stats.get('available_quota', 0)}")
        
        print(f"\n申请状态统计:")
        print(f"  待审批: {stats.get('pending_count', 0)}")
        print(f"  已批准: {stats.get('approved_count', 0)}")
        print(f"  已拒绝: {stats.get('rejected_count', 0)}")
        print(f"  已创建: {stats.get('created_count', 0)}")
        
        return True
    else:
        print(f"错误: {response.text}")
        return False

def test_actual_sync(token):
    """测试实际同步（非试运行）"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n=== 测试实际同步（创建申请记录） ===")
    sync_data = {
        "full_sync": False,  # 增量同步
        "dry_run": False     # 实际执行
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/personnel-email-sync/sync/trigger", 
                           headers=headers, json=sync_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"同步结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 分析结果
        if result.get("stats"):
            stats = result["stats"]
            print(f"\n实际同步统计:")
            print(f"  处理数量: {stats.get('processed_count', 0)}")
            print(f"  创建申请: {stats.get('created_count', 0)}")
            print(f"  更新数量: {stats.get('updated_count', 0)}")
            print(f"  禁用数量: {stats.get('disabled_count', 0)}")
            print(f"  错误数量: {stats.get('error_count', 0)}")
        
        return True
    else:
        print(f"错误: {response.text}")
        return False

def main():
    """主测试函数"""
    print("开始测试新的人员邮箱同步逻辑...")
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(3)
    
    # 登录
    token = login()
    if not token:
        print("登录失败，无法继续测试")
        return
    
    print(f"登录成功，获取到token: {token[:20]}...")
    
    # 测试各个功能
    tests = [
        ("新同步逻辑（试运行）", test_new_sync_logic),
        ("邮箱创建申请列表", test_creation_requests),
        ("申请统计信息", test_requests_stats),
        ("实际同步（创建申请）", test_actual_sync),
        ("同步后申请列表", test_creation_requests),
        ("同步后统计信息", test_requests_stats)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            success = test_func(token)
            results[test_name] = "✓ 通过" if success else "✗ 失败"
        except Exception as e:
            results[test_name] = f"✗ 异常: {str(e)}"
    
    # 输出测试结果
    print("\n" + "="*60)
    print("测试结果汇总:")
    print("="*60)
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 检查是否所有测试都通过
    all_passed = all("✓" in result for result in results.values())
    if all_passed:
        print("\n🎉 所有测试都通过了！新的同步逻辑工作正常。")
        print("\n📋 功能说明:")
        print("1. ✅ 系统不再自动创建邮箱账号")
        print("2. ✅ 检测到需要邮箱的在职员工时，创建申请记录")
        print("3. ✅ 自动禁用离职员工的邮箱账号")
        print("4. ✅ 提供管理员审批界面")
        print("5. ✅ 邮箱名额限制保护（1000个）")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
