export interface DiskInfo {
  name: string
  total_space: number
  free_space: number
  filesystem?: string
  mount_point?: string
}

export interface HardwareInfo {
  cpu_model?: string
  cpu_cores?: number
  memory_total?: number
  serial_number?: string
  manufacturer?: string
  model?: string
  disks: DiskInfo[]
  update_time?: string
}

export interface SecurityInfo {
  firewall_enabled?: boolean
  antivirus?: string
  antivirus_enabled?: boolean
}

export interface OSInfo {
  name?: string
  version?: string
  build?: string
  architecture?: string
  install_date?: string
  installed_updates: string[]
  security_info?: SecurityInfo
  update_time?: string
}

export interface Software {
  name: string
  version?: string
  publisher?: string
  install_date?: string
  size?: number
  install_location?: string
}

export interface NetworkInterface {
  name: string
  mac_address: string
  ip_address?: string
  subnet_mask?: string
  dhcp_enabled?: boolean
  is_connected?: boolean
}

export interface NetworkInfo {
  hostname?: string
  domain?: string
  dns_servers: string[]
  default_gateway?: string
  interfaces: NetworkInterface[]
  update_time?: string
}

export interface UserLoginInfo {
  username: string
  full_name?: string
  login_time?: string
  domain?: string
  update_time?: string
}

export interface TerminalCommand {
  id: string
  type: string
  content: string
  create_time: string
  sent_time?: string
  execute_time?: string
  timeout: number
  status: string
  result?: string
  error?: string
  execution_duration?: number
}

export interface TerminalDetail {
  id: string
  unique_id: string
  hostname: string
  mac_address: string
  ip_address?: string
  agent_version?: string
  online_status: boolean
  last_online_time?: string
  last_offline_time?: string
  last_heartbeat_time?: string
  registration_time: string
  heartbeat_interval: number
  collection_interval: number
  hardware_info?: HardwareInfo
  os_info?: OSInfo
  network_info?: NetworkInfo
  last_login_user?: UserLoginInfo
}

export interface TerminalSummary {
  id: string
  hostname: string
  unique_id: string
  mac_address: string
  ip_address?: string
  online_status: boolean
  last_heartbeat_time?: string
  os_name?: string
  os_version?: string
  agent_version?: string
}

export interface TerminalStats {
  total: number
  online: number
  offline: number
  windows_count: number
  windows_versions: Record<string, number>
  hardware_stats: Record<string, any>
}

export interface TerminalCommandStats {
  total: number
  pending: number
  sent: number
  executed: number
  failed: number
  timeout: number
}

export interface AgentVersion {
  id: string
  version: string
  platform: string
  file_name: string
  file_size: number
  download_url: string
  release_notes?: string
  upload_time: string
  is_current: boolean
}

export interface SoftwareSummary {
  name: string
  version?: string
  terminal_count: number
  is_compliant?: boolean
  usage_notes?: string
}

export interface SoftwareDetail {
  name: string
  version?: string
  terminals: TerminalSummary[]
  is_compliant?: boolean
  usage_notes?: string
}

// === Agent升级监控类型定义 ===

export interface AgentUpgradeStatus {
  command_id: string
  terminal_id: number
  target_version: string
  current_version: string
  status: 'pending' | 'downloading' | 'installing' | 'completed' | 'failed' | 'rolled_back'
  progress: number  // 0-100, -1表示失败, -2表示回滚
  message?: string
  error_details?: string
  started_at: string
  completed_at?: string
  duration?: number  // 升级耗时（秒）
}

export interface AgentUpgradeProgress {
  command_id: string
  progress: number  // 0-100, -1表示失败, -2表示回滚
  message: string
  timestamp: string
}

export interface AgentUpgradeHistory {
  id: number
  terminal_id: number
  from_version: string
  to_version: string
  status: string
  started_at: string
  completed_at?: string
  duration?: number
  error_message?: string
}

export interface AgentUpgradeStats {
  total_upgrades: number
  success_rate: number
  active_upgrades: number
  status_distribution: Record<string, number>
  daily_stats: Array<{
    date: string
    count: number
  }>
  period_days: number
} 