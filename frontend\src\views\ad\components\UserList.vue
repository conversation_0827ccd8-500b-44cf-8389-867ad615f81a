<template>
  <div class="user-list-container">
    <!-- 固定工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 占位，保留空间 -->
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户"
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="users"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="username" label="用户名" min-width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleShowDetail(row)">
              {{ row.username }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="显示名称" min-width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleShowDetail(row)">
              {{ row.name }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="department" label="部门" min-width="120" show-overflow-tooltip />
        <el-table-column prop="title" label="职位" min-width="120" show-overflow-tooltip />
        <el-table-column label="所属组" min-width="200">
          <template #default="{ row }">
            <div class="group-tags">
              <template v-if="row.groups && row.groups.length > 0">
                <el-tag
                  v-for="(group, index) in row.groups.slice(0, 2)"
                  :key="group"
                  class="mx-1"
                  size="small"
                >
                  {{ group }}
                </el-tag>
                <el-popover
                  v-if="row.groups.length > 2"
                  placement="top"
                  trigger="hover"
                  :width="300"
                >
                  <template #reference>
                    <el-tag size="small" type="info">
                      +{{ row.groups.length - 2 }} 个组
                    </el-tag>
                  </template>
                  <div class="group-popover">
                    <el-tag
                      v-for="group in row.groups.slice(2)"
                      :key="group"
                      class="mx-1 my-1"
                      size="small"
                    >
                      {{ group }}
                    </el-tag>
                  </div>
                </el-popover>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          label="密码过期" 
          min-width="140" 
          align="center"
          :filters="passwordExpiryFilters"
          :filter-method="filterPasswordExpiry"
        >
          <template #default="{ row }">
            <div class="password-expiry-info">
              <template v-if="row.password_never_expires">
                <el-tag type="info" size="small">永不过期</el-tag>
              </template>
              <template v-else-if="row.password_expired">
                <el-tag type="danger" size="small">已过期</el-tag>
              </template>
              <template v-else-if="row.days_until_expiry !== null">
                <template v-if="row.days_until_expiry <= 7">
                  <el-tag type="warning" size="small">{{ row.days_until_expiry }}天后过期</el-tag>
                </template>
                <template v-else>
                  <el-tag type="success" size="small">{{ row.days_until_expiry }}天后过期</el-tag>
                </template>
              </template>
              <template v-else>
                <el-tag type="info" size="small">未知</el-tag>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          label="状态" 
          width="80" 
          align="center"
          :filters="userStatusFilters"
          :filter-method="filterUserStatus"
        >
          <template #default="{ row }">
            <el-tag :type="row.enabled ? 'success' : 'danger'" size="small">
              {{ row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <Authority permission="ad:user:manage">
                    <el-dropdown-item @click="handleEdit(row)">
                      <el-icon><Edit /></el-icon>编辑信息
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:user:manage">
                    <el-dropdown-item @click="handleToggleStatus(row)">
                      <el-icon><Switch /></el-icon>{{ row.enabled ? '禁用' : '启用' }}
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:user:manage">
                    <el-dropdown-item @click="handleMoveUser(row)">
                      <el-icon><Position /></el-icon>移动用户
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:user:manage">
                    <el-dropdown-item @click="handleFixCN(row)">
                      <el-icon><Edit /></el-icon>修复CN
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:user:manage">
                    <el-dropdown-item divided @click="handleDelete(row)" class="text-red">
                      <el-icon><Delete /></el-icon>删除用户
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加批量操作工具栏 -->
    <div class="batch-operation-toolbar" v-if="selectedUsers.length > 0">
      <div class="selected-count">
        已选择 {{ selectedUsers.length }} 个用户
      </div>
      <div class="batch-operations">
        <el-button-group>
          <Authority permission="ad:user:manage">
            <el-button
              type="primary"
              @click="handleBatchMove"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Position /></el-icon>批量移动
            </el-button>
          </Authority>
          <Authority permission="ad:user:manage">
            <el-button
              type="primary"
              @click="handleBatchToggleStatus"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Switch /></el-icon>批量启用/禁用
            </el-button>
          </Authority>
          <Authority permission="ad:user:manage">
            <el-button
              type="primary"
              @click="handleBatchAddToGroup"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Plus /></el-icon>批量添加到组
            </el-button>
          </Authority>
          <Authority permission="ad:user:manage">
            <el-button
              type="warning"
              @click="handleBatchRemoveFromGroup"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Remove /></el-icon>批量移除组
            </el-button>
          </Authority>
          <Authority permission="ad:user:manage">
            <el-button
              type="warning"
              @click="handleBatchFixCN"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Edit /></el-icon>批量修复CN
            </el-button>
          </Authority>
          <Authority permission="ad:user:manage">
            <el-button
              type="danger"
              @click="handleBatchDelete"
              :disabled="!selectedUsers.length"
            >
              <el-icon><Delete /></el-icon>批量删除
            </el-button>
          </Authority>
        </el-button-group>
      </div>
    </div>

    <!-- 固定分页 -->
    <div class="pagination-container">
      <Authority permission="ad:view">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </Authority>
    </div>

    <!-- 添加/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户信息' : '新建用户'"
      width="500px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="dialogType === 'edit'" />
        </el-form-item>
        <el-form-item label="显示名称" prop="name">
          <el-input v-model="userForm.name" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="userForm.department" />
        </el-form-item>
        <el-form-item label="职务" prop="title">
          <el-input v-model="userForm.title" />
        </el-form-item>
        <el-form-item label="所属组">
          <div class="group-select">
            <div class="selected-groups">
              <el-tag
                v-for="dn in userForm.groups"
                :key="dn"
                closable
                @close="removeGroup(dn)"
                class="mx-1"
                size="small"
              >
                {{ getGroupName(dn) }}
              </el-tag>
            </div>
            <el-button type="primary" link @click="showGroupSelector">
              <el-icon><Plus /></el-icon>添加组
            </el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            show-password
          />
          <template #label>
            密码
            <el-tooltip
              content="密码长度至少8位，必须包含大小写字母、数字和特殊字符中的至少3种"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
        </el-form-item>
        <el-form-item v-if="dialogType === 'add'" label="下次登录时">
          <el-checkbox v-model="userForm.change_password_next_logon">
            必须修改密码
          </el-checkbox>
        </el-form-item>
        <el-form-item v-if="dialogType === 'edit'" label="新密码">
          <el-input
            v-model="userForm.password"
            type="password"
            show-password
            placeholder="不修改请留空"
            @input="validatePassword"
          />
          <template #label>
            新密码
            <el-tooltip
              content="密码长度至少8位，必须包含大小写字母、数字和特殊字符中的至少3种"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
          <div v-if="passwordError" class="error-message" style="color: #F56C6C; font-size: 12px; margin-top: 5px;">
            {{ passwordError }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 添加组选择对话框 -->
    <el-dialog
      v-model="groupSelectorVisible"
      title="选择用户组"
      width="800px"
      append-to-body
    >
      <div class="group-selector">
        <el-input
          v-model="groupSearchQuery"
          placeholder="搜索组"
          clearable
          class="group-search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <div class="group-list">
          <div class="group-category">
            <div class="category-title">系统内置组</div>
            <el-checkbox-group v-model="userForm.groups">
              <div v-for="group in filteredSystemGroups" :key="group.dn" class="group-item">
                <el-checkbox :label="group.dn">
                  <div class="group-item-content">
                    <div class="group-name">{{ group.shortName || group.name }}</div>
                    <div v-if="group.description" class="group-description">
                      - {{ group.description }}
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <div class="group-category">
            <div class="category-title">安全组</div>
            <el-checkbox-group v-model="userForm.groups">
              <div v-for="group in filteredSecurityGroups" :key="group.dn" class="group-item">
                <el-checkbox :label="group.dn">
                  <div class="group-item-content">
                    <div class="group-name">{{ group.shortName || group.name }}</div>
                    <div v-if="group.description" class="group-description">
                      - {{ group.description }}
                    </div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="groupSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGroupSelection">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="bulkImportDialogVisible"
      title="批量导入用户"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="import-container">
        <!-- 步骤说明 -->
        <div class="import-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4>下载模板</h4>
              <p>请先下载导入模板，模板包含必要的字段说明和示例数据</p>
              <div class="template-downloads">
                <el-button type="primary" @click="downloadTemplate('excel')">
                  <el-icon><Document /></el-icon>下载Excel模板
                </el-button>
                <el-button @click="downloadTemplate('csv')">
                  <el-icon><Document /></el-icon>下载CSV模板
                </el-button>
              </div>
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4>填写数据</h4>
              <p>请按照模板格式填写用户信息，确保必��字段已正确填写</p>
            </div>
          </div>

          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4>上传文件</h4>
              <div class="upload-area">
                <el-upload
                  ref="uploadRef"
                  class="import-upload"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="true"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :file-list="fileList"
                  :limit="1"
                >
                  <template #trigger>
                    <el-button type="primary">
                      <el-icon><Upload /></el-icon>选文件
                    </el-button>
                  </template>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持 CSV 或 Excel 文件（.xlsx/.xls）文件大小不超过10MB
                    </div>
                  </template>
                </el-upload>
              </div>
            </div>
          </div>
        </div>

        <!-- 导入结果 -->
        <div v-if="importResult" class="import-result">
          <el-alert
            :type="importResult.failed === 0 ? 'success' : 'warning'"
            :closable="false"
            show-icon
          >
            <template v-if="importResult.failed === 0">
              <p>导入成功完成：共导入 {{ importResult.total }} 个用户</p>
            </template>
            <template v-else>
              <p>导入已完成，但存在部分失败记录：</p>
              <p>总计：{{ importResult.total }} 个用户</p>
              <p>成功：{{ importResult.successful }} 个</p>
              <p>失败：{{ importResult.failed }} 个</p>
            </template>
          </el-alert>

          <!-- 成功导入的用户列表 -->
          <div v-if="importResult.successful > 0" class="success-details">
            <h4>成功导入的用户：</h4>
            <el-table
              :data="importResult.successful_users"
              size="small"
              style="width: 100%"
              border
            >
              <el-table-column prop="username" label="用户名" width="140" />
              <el-table-column prop="name" label="显示名称" width="140" />
              <el-table-column label="密码" min-width="280">
                <template #default="{ row }">
                  <div class="password-field">
                    <el-input
                      v-model="row.password"
                      readonly
                      type="password"
                      show-password
                      size="small"
                    >
                    </el-input>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      class="copy-btn"
                      @click="copyPassword(row.password)"
                    >
                      复制
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="下次登录" width="120" align="center">
                <template #default="{ row }">
                  <el-tag :type="row.change_password_next_logon ? 'warning' : 'info'" size="small">
                    {{ row.change_password_next_logon ? '需修改密码' : '无需修改' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
            <!-- 添加导出按钮 -->
            <div class="export-buttons">
              <el-button type="primary" @click="exportUserInfo">
                <el-icon><Download /></el-icon>导出用户账号信息
              </el-button>
            </div>
          </div>

          <!-- 失败记录的显示 -->
          <div v-if="importResult.failed > 0" class="failed-details">
            <h4>失败详情：</h4>
            <el-table
              :data="importResult.failed_users"
              size="small"
              style="width: 100%"
              border
            >
              <el-table-column prop="username" label="用户名" width="140" />
              <el-table-column prop="error" label="失败原因" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeImportDialog">
            {{ importResult ? '关闭' : '取消' }}
          </el-button>
          <el-button
            type="primary"
            @click="performBulkImport"
            :loading="importing"
            :disabled="!hasFileSelected"
          >
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加自定义导出对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="自定义导出"
      width="500px"
    >
      <el-form :model="exportForm" label-width="100px">
        <el-form-item label="文件格式">
          <el-radio-group v-model="exportForm.format">
            <el-radio label="excel">Excel文件</el-radio>
            <el-radio label="csv">CSV文件</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="导出字段">
          <el-checkbox-group v-model="exportForm.fields">
            <el-checkbox label="username">用户名</el-checkbox>
            <el-checkbox label="name">显示名称</el-checkbox>
            <el-checkbox label="email">邮箱</el-checkbox>
            <el-checkbox label="department">部门</el-checkbox>
            <el-checkbox label="title">职务</el-checkbox>
            <el-checkbox label="groups">所属组</el-checkbox>
            <el-checkbox label="enabled">状态</el-checkbox>
            <el-checkbox label="password_expiry_date">密码过期日期</el-checkbox>
            <el-checkbox label="password_expiry_status">密码过期状态</el-checkbox>
            <el-checkbox label="days_until_expiry">剩余天数</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleCustomExport"
          :loading="exporting"
        >
          开始导出
        </el-button>
      </template>
    </el-dialog>

    <!-- 添加导���进度对话框 -->
    <el-dialog
      v-model="exportProgressVisible"
      title="导出进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="export-progress">
        <el-progress
          :percentage="exportProgress"
          :status="exportProgress === 100 ? 'success' : ''"
        />
        <div class="progress-info">
          {{ exportProgressText }}
        </div>
      </div>
    </el-dialog>

    <!-- 添加移动用户对话框 -->
    <el-dialog
      v-model="moveDialogVisible"
      title="移动用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="move-user-container">
        <!-- 简化用户信息显示 -->
        <div class="user-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="用户">{{ moveForm.username }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 目标OU选择区域 -->
        <div class="target-ou-section">
          <div class="section-title">
            <span>选择目标OU</span>
            <el-tag v-if="moveForm.target_ou_dn" type="success" class="selected-ou">
              已选择: {{ getOUNameFromDN(moveForm.target_ou_dn) }}
            </el-tag>
          </div>

          <div class="ou-tree-container">
            <el-scrollbar height="300px">
              <el-tree
                ref="ouTreeRef"
                :data="ouData"
                :props="{
                  children: 'children',
                  label: 'name'
                }"
                node-key="dn"
                highlight-current
                @node-click="handleOUSelect"
                :expand-on-click-node="false"
              >
                <template #default="{ node }">
                  <span class="custom-tree-node">
                    <el-icon><Folder /></el-icon>
                    <span>{{ node.label }}</span>
                  </span>
                </template>
              </el-tree>
            </el-scrollbar>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="moveDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleMoveConfirm"
            :loading="moving"
            :disabled="!moveForm.target_ou_dn"
          >
            移动到选中位置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加批量添加到组的对话框 -->
    <el-dialog
      v-model="batchAddToGroupDialogVisible"
      title="批量添加到组"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-add-to-group-container">
        <div class="selected-users-info">
          已选择 {{ selectedUsers.length }} 个用户
        </div>
        <div class="group-selector">
          <el-form :model="batchAddToGroupForm" label-width="80px">
            <el-form-item label="选组">
              <el-select
                v-model="batchAddToGroupForm.groups"
                multiple
                filterable
                placeholder="请选择要添加的组"
                style="width: 100%"
              >
                <el-option-group label="安全组">
                  <el-option
                    v-for="group in securityGroups"
                    :key="group.name"
                    :label="formatGroupName(group.name)"
                    :value="group.name"
                  />
                </el-option-group>
                <el-option-group label="系统组">
                  <el-option
                    v-for="group in systemGroups"
                    :key="group.name"
                    :label="formatGroupName(group.name)"
                    :value="group.name"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchAddToGroupDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleBatchAddToGroupConfirm"
            :loading="addingToGroup"
          >
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量移除组对话框 -->
    <el-dialog
      v-model="batchRemoveFromGroupDialogVisible"
      title="批量移除组"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="batch-remove-from-group-container">
        <div class="selected-users-info">
          已选择 {{ selectedUsers.length }} 个用户
        </div>
        <div class="group-selector">
          <el-form :model="batchRemoveFromGroupForm" label-width="80px">
            <el-form-item label="选择组">
              <el-select
                v-model="batchRemoveFromGroupForm.groups"
                multiple
                filterable
                placeholder="请选择要移除的组"
                style="width: 100%"
                :popper-append-to-body="false"
                popper-class="batch-remove-group-popper"
              >
                <el-option
                  v-for="group in userGroups"
                  :key="group.name"
                  :label="group.displayName || formatGroupName(group.name)"
                  :value="group.name"
                >
                  <span class="group-option">
                    <span class="group-name">{{ group.displayName || formatGroupName(group.name) }}</span>
                    <el-tooltip
                      v-if="group.name !== (group.displayName || formatGroupName(group.name))"
                      effect="dark"
                      :content="group.name"
                      placement="top"
                    >
                      <el-icon class="info-icon"><InfoFilled /></el-icon>
                    </el-tooltip>
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchRemoveFromGroupDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleBatchRemoveFromGroupConfirm"
            :loading="removingFromGroup"
          >
            确定移除
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="用户详情"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
      class="user-detail-dialog"
    >
      <user-detail :user="currentUser" v-if="detailVisible" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Edit, Switch, ArrowDown, QuestionFilled, Delete, Upload, Download, Document, UserFilled, Setting, Position, Folder, Remove, InfoFilled } from '@element-plus/icons-vue'
import { getUsers, createUser, updateUser, toggleUserStatus, getGroups, getUserGroups, deleteUser, getAllGroups, bulkImportUsers, downloadImportTemplate, exportUsers, moveUser, getOUTree, updateUserGroups, fixUserCN, fixAllUsersCN } from '@/api/ad'
import * as ExcelJS from 'exceljs'
import Papa from 'papaparse'
import { saveAs } from 'file-saver'
import { logger } from '@/utils/logger'
import UserDetail from './UserDetail.vue'
import Authority from '@/components/Authority/index.vue'

const props = defineProps({
  ouDn: {
    type: String,
    required: true
  }
})

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const users = ref([])
const availableGroups = ref([])
const searchQuery = ref('')
const dialogVisible = ref(false)
const dialogType = ref('add')
const userFormRef = ref(null)
const loading = ref(false)

// 筛选选项定义
const passwordExpiryFilters = ref([
  { text: '永不过期', value: 'never_expires' },
  { text: '已过期', value: 'expired' },
  { text: '即将过期(7天内)', value: 'expiring_soon' },
  { text: '正常', value: 'normal' },
  { text: '未知', value: 'unknown' }
])

const userStatusFilters = ref([
  { text: '启用', value: 'enabled' },
  { text: '禁用', value: 'disabled' }
])

// 添加移动用户相关的变量
const ouData = ref([])
const moveDialogVisible = ref(false)
const moving = ref(false)
const moveForm = ref({
  username: '',
  target_ou_dn: '',
  current_dn: ''
})

const userForm = ref({
  username: '',
  name: '',
  email: '',
  password: '',
  department: '',
  title: '',
  groups: [],
  change_password_next_logon: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名长度至少为3个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  email: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          callback(new Error('请输入正确的邮箱地址'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  password: [
    { required: dialogType.value === 'add', message: '请输入密码', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$/,
      message: '密码必须包含大小写字母、数字和特殊字符',
      trigger: 'blur'
    }
  ]
}

const fetchGroups = async () => {
  try {
    const { data } = await getAllGroups()
    availableGroups.value = Array.isArray(data) ? data : []
  } catch (error) {
    console.error('获取组列表失败:', error)
    ElMessage.error('获取组列表失败')
  }
}

const fetchUsers = async () => {
  if (!props.ouDn) {
    users.value = []
    total.value = 0
    return
  }

  loading.value = true
  try {
    logger.info('开始获取用户列表', {
      ou_dn: props.ouDn,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value
    })

    const { data } = await getUsers({
      ou_dn: props.ouDn,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value
    })

    logger.info('获取用户数据成功', { total: data.total })

    users.value = data.items.map(user => ({
      ...user,
      groups: Array.isArray(user.groups) ? user.groups : []
    }))
    total.value = data.total
    currentPage.value = data.page
    pageSize.value = data.page_size
  } catch (error) {
    logger.error('获取用户列表失败', error instanceof Error ? error : new Error(String(error)), {
      ou_dn: props.ouDn
    })
    users.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 添加获取OU数据的函数
const fetchOUData = async () => {
  try {
    const { data } = await getOUTree()
    ouData.value = Array.isArray(data) ? data : []
  } catch (error) {
    console.error('获取OU数据失败:', error)
    ElMessage.error('获取组织单位数据失败')
    ouData.value = [] // 确保失败时设置为空数组
  }
}

watch(() => props.ouDn, async (newVal) => {
  console.log('OU DN changed:', newVal)
  if (newVal) {
    await fetchUsers()
  } else {
    users.value = []
  }
}, { immediate: true })

const handleAdd = async () => {
  dialogType.value = 'add'
  userForm.value = {
    username: '',
    name: '',
    email: '',
    password: '',
    department: '',
    title: '',
    groups: [],
    change_password_next_logon: false
  }
  loading.value = true
  try {
    await fetchGroups()
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取组列表失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = async (row) => {
  dialogType.value = 'edit'
  loading.value = true
  try {
    await fetchGroups()
    const { data: userGroups } = await getUserGroups(row.username)

    // 优先使用API获取的用户组信息，这样可以保证信息是最新的
    const groups = Array.isArray(userGroups) ? userGroups : (Array.isArray(row.groups) ? row.groups : [])

    console.log('编辑用户获取组信息:', {
      username: row.username,
      apiGroups: userGroups,
      rowGroups: row.groups,
      finalGroups: groups
    });

    userForm.value = {
      ...row,
      password: '',
      groups: groups
    }
    dialogVisible.value = true
  } catch (error) {
    console.error('获取组列表失败:', error)
    ElMessage.error('获取组列表失败')
  } finally {
    loading.value = false
  }
}

const handleToggleStatus = async (row) => {
  try {
    // 检查是否是受保护的系统账户
    const protectedUsers = ['Administrator', 'Guest', 'krbtgt', 'admin']
    if (protectedUsers.some(p => p.toLowerCase() === row.username.toLowerCase())) {
      ElMessage.warning(`无法修改受保护的系统账户: ${row.username}`)
      return
    }

    const action = row.enabled ? '禁用' : '启用'

    await ElMessageBox.confirm(
      `确定要${action}用户"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    try {
      await toggleUserStatus(row.username)
      ElMessage.success(`${action}成功`)
      await fetchUsers()
    } catch (err) {
      console.error(`切换用户 ${row.username} 状态失败:`, err)

      // 显示详细的错误信息
      if (err.response?.status === 404) {
        ElMessage.error(`用户 ${row.username} 不存在`)
      } else if (err.response?.status === 403) {
        ElMessage.error(`无权修改用户 ${row.username} 的状态`)
      } else if (err.response?.data?.detail) {
        // 如果有详细错误信息，显示完整的错误信息
        await ElMessageBox.alert(
          `操作失败: ${err.response.data.detail}`,
          `切换用户 ${row.username} 状态失败`,
          {
            confirmButtonText: '我知道了',
            type: 'error'
          }
        )
      } else {
        ElMessage.error(`操作失败: ${err.message || '未知错误'}`)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换用户状态失败:', error)
      ElMessage.error(error.response?.data?.detail || `操作失败: ${error.message || '未知错误'}`)
    }
  }
}

const handleSubmit = async () => {
  if (userForm.value.password && !validatePassword(userForm.value.password)) {
    return
  }

  try {
    loading.value = true
    if (dialogType.value === 'add') {
      await createUser({
        ...userForm.value,
        ou_dn: props.ouDn
      })
      ElMessage.success('创建用户成功')
      dialogVisible.value = false
      await fetchUsers()
    } else {
      try {
        // 分离组信息和其他用户信息
        const groups = userForm.value.groups;

        // 准备其他用户信息
        const updateData = {
          username: userForm.value.username,
          name: userForm.value.name,
          email: userForm.value.email,
          department: userForm.value.department,
          title: userForm.value.title,
          password: userForm.value.password || undefined,
          change_password_next_logon: userForm.value.change_password_next_logon
        }

        // 移除undefined的字段
        Object.keys(updateData).forEach(key => {
          if (updateData[key] === undefined) {
            delete updateData[key]
          }
        })

        // 首先更新基本用户信息
        await updateUser(userForm.value.username, updateData)

        // 然后单独更新用户组
        if (groups !== undefined) {
          console.log('更新用户组信息:', {
            username: userForm.value.username,
            groupsCount: groups ? groups.length : 0,
            groups: groups
          });

          // 使用专门的API更新用户组
          await updateUserGroups(userForm.value.username, groups)
        }

        ElMessage.success('更新用户成功')
        dialogVisible.value = false
        await fetchUsers()
      } catch (error) {
        // 如果是后端返回的密码错误，只显示一次错误信息
        if (error.response?.status === 400) {
          const errorMessage = error.response?.data?.detail || error.message
          if (errorMessage.includes('密码')) {
            passwordError.value = errorMessage
            return
          }
        }
        throw error  // 如果不是密码错误，继续抛出异常
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    // 显示详细的错误信息
    const errorMessage = error.response?.data?.detail || error.message || '操作失败'
    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 5000,
      showClose: true,
      dangerouslyUseHTMLString: true
    })
  } finally {
    loading.value = false
  }
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除用户"${row.name}"(${row.username})吗？操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(async () => {
    try {
      await deleteUser(row.username)
      ElMessage.success('用户删除成功')
      await fetchUsers()
    } catch (error) {
      ElMessage.error(error.response?.data?.detail || '删除用户失败')
    }
  })
}

// 处理页码改变
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUsers()
}

// 处理每页条数变
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1 // 重置第一页
  fetchUsers()
}

// 监听搜索关键词变化
watch(searchQuery, () => {
  currentPage.value = 1 // 重置到第一页
  fetchUsers()
}, { debounce: 300 }) // 添加防抖

// 简化组名显示格式的方法
const formatGroupName = (groupName) => {
  if (!groupName) return ''

  // 如果是DN格式，只提取CN部分
  if (typeof groupName === 'string' && groupName.includes('CN=')) {
    const cnMatch = groupName.match(/CN=([^,]+)/)
    if (cnMatch && cnMatch[1]) {
      return cnMatch[1].trim()
    }
  }

  return groupName
}

// 添加计算属性来分类组
const systemGroups = computed(() => {
  return availableGroups.value.filter(group =>
    group.name.toLowerCase().includes('domain') ||
    group.name.toLowerCase().includes('builtin')
  )
})

const securityGroups = computed(() => {
  return availableGroups.value.filter(group =>
    !group.name.toLowerCase().includes('domain') &&
    !group.name.toLowerCase().includes('builtin')
  )
})

// 添加新的响应式量
const groupSelectorVisible = ref(false)
const groupSearchQuery = ref('')
const selectedGroups = ref([])

// 添加新的计算属性
const filteredSystemGroups = computed(() => {
  const query = groupSearchQuery.value.toLowerCase()
  return systemGroups.value.filter(group =>
    group.name.toLowerCase().includes(query) ||
    (group.description && group.description.toLowerCase().includes(query))
  )
})

const filteredSecurityGroups = computed(() => {
  const query = groupSearchQuery.value.toLowerCase()
  return securityGroups.value.filter(group =>
    group.name.toLowerCase().includes(query) ||
    (group.description && group.description.toLowerCase().includes(query))
  )
})

// 添加新的方法
const showGroupSelector = () => {
  groupSelectorVisible.value = true
}

const getGroupName = (dn) => {
  const group = availableGroups.value.find(g => g.dn === dn)
  return group ? (group.shortName || group.name) : dn
}

const removeGroup = async (dn) => {
  try {
    // 先更新本地状态
    userForm.value.groups = userForm.value.groups.filter(g => g !== dn)

    // 如果是编辑模式，则立即更新后端
    if (dialogType.value === 'edit') {
      console.log('移除组:', {
        username: userForm.value.username,
        removedGroup: dn,
        remainingGroups: userForm.value.groups
      });

      loading.value = true
      try {
        // 使用专门的API更新用户组
        await updateUserGroups(userForm.value.username, userForm.value.groups)
        ElMessage.success('从组中移除成功')
      } catch (error) {
        console.error('从组中移除失败:', error)
        ElMessage.error('从组中移除失败')
        // 如果后端更新失败，恢复本地状态
        userForm.value.groups.push(dn)
      } finally {
        loading.value = false
      }
    }
  } catch (error) {
    console.error('移除组失败:', error)
  }
}

const confirmGroupSelection = async () => {
  // userForm.groups已经通过el-checkbox-group的v-model双向绑定，已经保存在内存中

  // 如果是编辑模式，立即更新后端数据
  if (dialogType.value === 'edit') {
    loading.value = true
    try {
      console.log('更新用户组选择:', {
        username: userForm.value.username,
        groups: userForm.value.groups
      });

      // 使用与UserDetail.vue相同的方式更新用户组
      await updateUserGroups(userForm.value.username, userForm.value.groups)

      ElMessage.success('用户组更新成功')
    } catch (error) {
      console.error('更新用户组失败:', error)
      ElMessage.error('更新用户组失败')
    } finally {
      loading.value = false
    }
  } else {
    // 新建用户模式下只给提示，不立即保存
    ElMessage({
      message: '组选择已更新，保存用户信息后生效',
      type: 'success',
      duration: 2000
    })
  }

  groupSelectorVisible.value = false
}

// 筛选方法
const filterPasswordExpiry = (value, row) => {
  switch (value) {
    case 'never_expires':
      return row.password_never_expires
    case 'expired':
      return row.password_expired
    case 'expiring_soon':
      return !row.password_never_expires && !row.password_expired && 
             row.days_until_expiry !== null && row.days_until_expiry <= 7
    case 'normal':
      return !row.password_never_expires && !row.password_expired && 
             row.days_until_expiry !== null && row.days_until_expiry > 7
    case 'unknown':
      return !row.password_never_expires && !row.password_expired && 
             row.days_until_expiry === null
    default:
      return true
  }
}

const filterUserStatus = (value, row) => {
  switch (value) {
    case 'enabled':
      return row.enabled === true
    case 'disabled':
      return row.enabled === false
    default:
      return true
  }
}

const bulkImportDialogVisible = ref(false)
const importing = ref(false)
const uploadRef = ref(null)
const importResult = ref(null)
const hasFileSelected = ref(false)
const fileList = ref([])

const showBulkImportDialog = () => {
  bulkImportDialogVisible.value = true
}

const handleFileChange = (uploadFile) => {
  console.log('文件变更:', uploadFile)
  if (uploadFile && uploadFile.raw) {
    // 检查文件大小（10MB限制）
    if (uploadFile.raw.size > 10 * 1024 * 1024) {
      ElMessage.warning('文件大小不能过10MB')
      uploadRef.value.clearFiles()
      hasFileSelected.value = false
      fileList.value = []
      return false
    }

    const fileExtension = uploadFile.name.toLowerCase().split('.').pop()
    const validExtensions = ['csv', 'xls', 'xlsx']

    if (!validExtensions.includes(fileExtension)) {
      ElMessage.error('只支持 Excel 或 CSV 文件')
      uploadRef.value.clearFiles()
      hasFileSelected.value = false
      fileList.value = []
      return false
    }

    hasFileSelected.value = true
    fileList.value = [uploadFile]
    console.log('文件已选择:', uploadFile.name)
    return true
  }

  hasFileSelected.value = false
  fileList.value = []
  return false
}

// 修改文件移除处理方法
const handleFileRemove = () => {
  hasFileSelected.value = false
  fileList.value = []
  console.log('文件已移除')
}

// 文件验证方法
const validateFile = (file) => {
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]

  if (!validTypes.includes(file.type)) {
    ElMessage.error('只支持 Excel 或 CSV 文件')
    return false
  }

  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }

  return true
}

// 修改导入方法
const performBulkImport = async () => {
  if (!props.ouDn) {
    ElMessage.warning('请先选择组织单位')
    return
  }

  if (!fileList.value.length) {
    ElMessage.warning('请先选择文件')
    return
  }

  const file = fileList.value[0].raw
  const formData = new FormData()
  formData.append('file', file)
  formData.append('ou_dn', props.ouDn)

  importing.value = true
  try {
    console.log('开始导入用户，文件:', file.name)
    const { data } = await bulkImportUsers(formData)
    console.log('导入结果：', data)

    importResult.value = data

    if (data.failed === 0) {
      ElMessage.success(`导入成功：共导入 ${data.successful} 个用户`)
      await fetchUsers()
    } else {
      ElMessage.warning(
        `导入已完成，但存在部分失败记录：\n` +
        `总计：${data.total} 个用户\n` +
        `成功：${data.successful} 个\n` +
        `失败：${data.failed} 个\n` +
        `请查看详细信息`
      )
    }
  } catch (error) {
    console.error('导入失败：', error)
    ElMessage.error(error.response?.data?.detail || '导入失败，请检查文件格式是否正确')
  } finally {
    importing.value = false
  }
}

// 下载模板方法
const downloadTemplate = async (fileType) => {
  try {
    const response = await downloadImportTemplate(fileType)
    const blob = new Blob([response.data], {
      type: fileType === 'csv'
        ? 'text/csv'
        : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileType === 'csv' ? 'user_import_template.csv' : 'user_import_template.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('下载模板失败')
  }
}

// 修改关闭对话框方法
const closeImportDialog = () => {
  bulkImportDialogVisible.value = false
  importResult.value = null
  hasFileSelected.value = false
  fileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 监听对话框关闭
watch(bulkImportDialogVisible, (newVal) => {
  if (!newVal) {
    closeImportDialog()
  }
})

onMounted(() => {
  console.log('UserList mounted, ouDn:', props.ouDn)
})

// 复制密码到剪贴板
const copyPassword = async (password) => {
  try {
    await navigator.clipboard.writeText(password)
    ElMessage.success('密码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 导出用户信息为Excel文件
const exportUserInfo = async () => {
  if (!importResult.value?.successful_users?.length) return

  const data = importResult.value.successful_users.map(user => ({
    '用户名': user.username,
    '显示名称': user.name,
    '密码': user.password
  }))

  // 创建工作簿
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('导入用户信息')

  // 添加表头
  if (data.length > 0) {
    const headers = Object.keys(data[0])
    worksheet.addRow(headers)
    
    // 添加数据行
    data.forEach(row => {
      const values = headers.map(header => row[header] || '')
      worksheet.addRow(values)
    })
  }

  // 导出文件
  const buffer = await workbook.xlsx.writeBuffer()
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = '导入用户信息.xlsx'
  link.click()
  URL.revokeObjectURL(url)
}

// 添加导出相关的响应式变量
const exporting = ref(false)

// 添加新的响应式变量
const exportDialogVisible = ref(false)
const exportProgressVisible = ref(false)
const exportProgress = ref(0)
const exportProgressText = ref('')

const exportForm = ref({
  format: 'excel',
  fields: ['username', 'name', 'email', 'department', 'title', 'groups', 'enabled', 'password_expiry_status']
})

// 处理来自父组件的自定义导出（外部调用）
const processCustomExport = async (customOptions) => {
  if (!props.ouDn) return

  if (!customOptions.fields || !customOptions.fields.length) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  // 保存本地导出设置
  exportForm.value.format = customOptions.format
  exportForm.value.fields = customOptions.fields

  // 使用本地导出方法
  await handleExport(customOptions.format)
}

// 处理导出命令
const handleExportCommand = async (command) => {
  if (command === 'custom') {
    // 打开自定义导出对话框（仅用于本地调用，父组件会直接调用handleCustomExport）
    exportDialogVisible.value = true
    return
  }

  exporting.value = true
  try {
    const response = await exportUsers({
      ou_dn: props.ouDn,
      format: command
    })

    if (command === 'excel') {
      // Excel导出 - 直接处理blob响应
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const filename = `用户信息_${new Date().toLocaleDateString()}.xlsx`
      saveAs(blob, filename)
    } else {
      // CSV导出 - 处理JSON响应
      const blob = new Blob([response.data.content], { type: 'text/csv;charset=utf-8' })
      saveAs(blob, response.data.filename || 'users.csv')
    }

    ElMessage.success('导出成功')
  } catch (error) {
    logger.error('导出用户失败', error instanceof Error ? error : new Error(String(error)))
    ElMessage.error(`导出用户失败: ${error.message || '未知错误'}`)
    console.error('导出错误详情:', error)
  } finally {
    exporting.value = false
  }
}

// 优化导出方法
const handleExport = async (format = 'excel') => {
  if (!props.ouDn) {
    ElMessage.warning('请先选择组织单位')
    return
  }

  exporting.value = true
  exportProgressVisible.value = true
  exportProgress.value = 0
  exportProgressText.value = '正在获取数据...'

  try {
    // 使用原始方法获取数据，不传递format参数
    const { data } = await exportUsers(props.ouDn)

    if (!data || !data.data || !data.data.length) {
      ElMessage.warning('当前组织单位下没有可导出用户数据')
      exportProgressVisible.value = false
      exporting.value = false
      return
    }

    exportProgress.value = 30
    exportProgressText.value = '正在处理数据...'

    // 转换数据,添加中文表头
    const exportData = data.data.map(item => ({
      '用户名': item.username,
      '显示名称': item.name,
      '邮箱': item.email,
      '部门': item.department,
      '职务': item.title,
      '所属组': Array.isArray(item.groups) ? item.groups.join(', ') : '',
      '状态': typeof item.enabled === 'boolean' ? (item.enabled ? '启用' : '禁用') : item.enabled
    }))

    exportProgress.value = 60
    exportProgressText.value = '正在生成文件...'

    try {
      // 根据格式导出
      if (format === 'csv') {
        // 导出CSV
        const csv = Papa.unparse(exportData)
        const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' })
        saveAs(blob, `用户信息_${new Date().toLocaleDateString()}.csv`)
      } else {
        // 导出Excel
        const workbook = new ExcelJS.Workbook()
        const worksheet = workbook.addWorksheet('用户信息')

        // 添加表头
        if (exportData.length > 0) {
          const headers = Object.keys(exportData[0])
          worksheet.addRow(headers)
          
          // 添加数据行
          exportData.forEach(row => {
            const values = headers.map(header => row[header] || '')
            worksheet.addRow(values)
          })

          // 设置列宽
          const columnWidths = [15, 15, 25, 15, 15, 30, 8] // 对应各列宽度
          headers.forEach((header, index) => {
            const column = worksheet.getColumn(index + 1)
            column.width = columnWidths[index] || 15
          })
        }

        // 使用ExcelJS生成并下载Excel文件
        const excelBuffer = await workbook.xlsx.writeBuffer()
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        saveAs(blob, `用户信息_${new Date().toLocaleDateString()}.xlsx`)
      }

      exportProgress.value = 100
      exportProgressText.value = '导出完成!'

      setTimeout(() => {
        exportProgressVisible.value = false
        ElMessage.success(`成功导出 ${data.data.length} 条用户数据`)
      }, 1000)
    } catch (error) {
      console.error('文件生成或下载失败:', error)
      ElMessage.error('文件生成或下载失败')
      exportProgressVisible.value = false
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error(error.response?.data?.detail || '导出数据失败')
    exportProgressVisible.value = false
  } finally {
    exporting.value = false
  }
}

// 处理自定义导出（本地调用）
const handleCustomExport = async () => {
  if (!exportForm.value.fields.length) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  exportDialogVisible.value = false

  // 使用本地导出方法
  await handleExport(exportForm.value.format)
}

// 处理移动用户
const handleMoveUser = (user) => {
  moveForm.value = {
    username: user.username,
    target_ou_dn: '',
    current_dn: user.dn
  }
  moveDialogVisible.value = true
  fetchOUData()
}

// 处理OU选择
const handleOUSelect = (data) => {
  moveForm.value.target_ou_dn = data.dn
}

// 处理移动确认
const handleMoveConfirm = async () => {
  if (!moveForm.value.target_ou_dn) {
    ElMessage.warning('请择目标组织单位')
    return
  }

  moving.value = true
  try {
    if (Array.isArray(moveForm.value.usernames)) {
      // 批量移动
      const promises = moveForm.value.usernames.map(username =>
        moveUser(username, moveForm.value.target_ou_dn)
      )
      await Promise.all(promises)
      ElMessage.success('批量移动成功')
    } else {
      // 单个移动
      await moveUser(moveForm.value.username, moveForm.value.target_ou_dn)
      ElMessage.success('移动成功')
    }
    moveDialogVisible.value = false
    await fetchUsers()
  } catch (error) {
    ElMessage.error(error.response?.data?.detail || '移动失败')
  } finally {
    moving.value = false
  }
}

// 添加从DN获取OU名称的方法
const getOUNameFromDN = (dn) => {
  if (!dn) return ''
  const parts = dn.split(',')
  return parts.filter(p => p.startsWith('OU=')).map(p => p.slice(3)).join('/')
}

// 添加新的响应式变量
const selectedUsers = ref([])

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 批量移动用户
const handleBatchMove = () => {
  if (!selectedUsers.value.length) return

  moveForm.value = {
    usernames: selectedUsers.value.map(user => user.username),
    target_ou_dn: '',
    current_dn: props.ouDn
  }
  moveDialogVisible.value = true
  fetchOUData()
}

// 批量启用/禁用用户
const handleBatchToggleStatus = async () => {
  if (!selectedUsers.value.length) return

  try {
    const enable = !selectedUsers.value[0].enabled
    const action = enable ? '启用' : '禁用'

    await ElMessageBox.confirm(
      `确定要${action}选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量操作确认',
      {
        confirmButtonText: `确定${action}`,
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 过滤受保护的系统账户
    const protectedUsers = ['Administrator', 'Guest', 'krbtgt', 'admin']
    const normalUsers = selectedUsers.value.filter(user =>
      !protectedUsers.some(p => p.toLowerCase() === user.username.toLowerCase())
    )

    // 如果有受保护的账户被过滤掉，显示警告
    if (normalUsers.length < selectedUsers.value.length) {
      const skippedUsers = selectedUsers.value.filter(user =>
        protectedUsers.some(p => p.toLowerCase() === user.username.toLowerCase())
      )
      ElMessage.warning(`已跳过 ${skippedUsers.length} 个受保护的系统账户: ${skippedUsers.map(u => u.username).join(', ')}`)
    }

    if (normalUsers.length === 0) {
      ElMessage.warning('所有选中的用户都是受保护的系统账户，无法批量操作')
      return
    }

    // 分批处理，每次处理不超过10个用户
    const batchSize = 10
    const results = { success: [], failed: [] }

    for (let i = 0; i < normalUsers.length; i += batchSize) {
      const batch = normalUsers.slice(i, i + batchSize)
      const promises = batch.map(async user => {
        try {
          await toggleUserStatus(user.username, enable)
          return { success: true, user }
        } catch (err) {
          console.error(`切换用户 ${user.username} 状态失败:`, err)
          return {
            success: false,
            user,
            error: err.response?.data?.detail || err.message || '未知错误'
          }
        }
      })

      const batchResults = await Promise.all(promises)

      batchResults.forEach(result => {
        if (result.success) {
          results.success.push(result.user)
        } else {
          results.failed.push({
            user: result.user,
            error: result.error
          })
        }
      })
    }

    // 显示结果
    if (results.failed.length === 0) {
      ElMessage.success(`批量${action}成功，共处理 ${results.success.length} 个用户`)
    } else if (results.success.length === 0) {
      // 全部失败时，分析并分类错误
      const errorTypes = {};
      results.failed.forEach(f => {
        if (!errorTypes[f.error]) {
          errorTypes[f.error] = [];
        }
        errorTypes[f.error].push(f.user.username);
      });

      // 构建错误消息
      let errorMessage = `批量${action}失败，所有 ${results.failed.length} 个用户处理失败\n\n错误类型汇总:`;

      for (const [error, usernames] of Object.entries(errorTypes)) {
        errorMessage += `\n\n错误: ${error}\n影响用户: ${usernames.join(', ')}`;
      }

      await ElMessageBox.alert(
        errorMessage,
        '批量操作失败',
        {
          confirmButtonText: '我知道了',
          type: 'error',
          dangerouslyUseHTMLString: false
        }
      );
    } else {
      // 如果有成功也有失败的情况
      // 分类错误信息，使其更易读
      const errorTypes = {};
      results.failed.forEach(f => {
        if (!errorTypes[f.error]) {
          errorTypes[f.error] = [];
        }
        errorTypes[f.error].push(f.user.username);
      });

      let errorDetails = '';
      for (const [error, usernames] of Object.entries(errorTypes)) {
        errorDetails += `\n\n错误: ${error}\n影响用户: ${usernames.join(', ')}`;
      }

      await ElMessageBox.alert(
        `批量${action}部分成功:\n\n成功: ${results.success.length} 个用户\n失败: ${results.failed.length} 个用户\n\n失败详情:${errorDetails}`,
        '批量操作结果',
        {
          confirmButtonText: '我知道了',
          type: 'warning',
          dangerouslyUseHTMLString: false
        }
      )
    }

    await fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量切换用户状态失败:', error)
      ElMessage.error(error.response?.data?.detail || `批量操作失败: ${error.message || '未知错误'}`)
    }
  }
}

// 批量删除用户
const handleBatchDelete = async () => {
  if (!selectedUsers.value.length) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const promises = selectedUsers.value.map(user =>
      deleteUser(user.username)
    )

    await Promise.all(promises)
    ElMessage.success('批量删除成功')
    await fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.detail || '批量删除失败')
    }
  }
}

// 添加批量添加到组的对话框
const batchAddToGroupDialogVisible = ref(false)
const batchAddToGroupForm = ref({
  groups: []
})
const addingToGroup = ref(false)

// 处理���量添加到组
const handleBatchAddToGroup = async () => {
  if (!selectedUsers.value.length) return

  loading.value = true
  try {
    await fetchGroups()
    batchAddToGroupForm.value.groups = []
    batchAddToGroupDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取组列表失败')
  } finally {
    loading.value = false
  }
}

// 处理批量添加到组的确认
const handleBatchAddToGroupConfirm = async () => {
  if (!batchAddToGroupForm.value.groups.length) {
    ElMessage.warning('请选择要添加的组')
    return
  }

  addingToGroup.value = true
  try {
    // 获取所有组信息以进行名称到DN的转换
    const { data: allGroups } = await getAllGroups()
    const groupNameToDN = {}
    allGroups.forEach(group => {
      groupNameToDN[group.name] = group.dn
    })

    // 转换组名为DN
    const groupDNs = batchAddToGroupForm.value.groups
      .map(groupName => groupNameToDN[groupName])
      .filter(dn => dn)

    if (!groupDNs.length) {
      throw new Error('没有找到有效的组DN')
    }

    console.log('开始批量添加用户到组:', {
      users: selectedUsers.value.map(u => u.username),
      groups: groupDNs
    })

    // 为每个用户获取当前的组,并合并新组
    const promises = selectedUsers.value.map(async user => {
      // 获取用户当前的组
      const { data: currentGroups } = await getUserGroups(user.username)
      // 合并当前组和新组
      const allGroups = [...new Set([...currentGroups, ...groupDNs])]
      // 更新用户组
      return updateUserGroups(user.username, allGroups)
    })

    await Promise.all(promises)
    ElMessage.success('批量添加到组成功')
    batchAddToGroupDialogVisible.value = false
    await fetchUsers()
  } catch (error) {
    console.error('批量添加到组失败:', error)
    console.error('错误详情:', {
      response: error.response?.data,
      status: error.response?.status,
      message: error.message
    })
    if (error.message === '没有找到有效的组DN') {
      ElMessage.error('批量添加到组失败: 选择的组不存在或无法获取组信息')
    } else if (error.response?.data?.detail) {
      ElMessage.error(`批量添加到组失败: ${error.response.data.detail}`)
    } else {
      ElMessage.error('批量添加到组失败,请检查组是否存在或是否有权限')
    }
  } finally {
    addingToGroup.value = false
  }
}

// 批量移除组相关的响应式变量
const batchRemoveFromGroupDialogVisible = ref(false)
const batchRemoveFromGroupForm = ref({
  groups: []
})
const removingFromGroup = ref(false)
const userGroups = ref([])

// 批量移除组方法
const handleBatchRemoveFromGroup = async () => {
  console.log('handleBatchRemoveFromGroup 被调用')
  if (!selectedUsers.value.length) {
    console.log('没有选中的用户')
    return
  }

  try {
    console.log('开始获取用户组信息:', {
      selectedUsers: selectedUsers.value.map(u => u.username)
    })

    // 获取所有选中用户的组信息
    const groupsPromises = selectedUsers.value.map(user => {
      return getUserGroups(user.username)
        .then(({ data }) => {
          console.log(`获取用户 ${user.username} 的组信息成功:`, {
            rawData: data,
            isArray: Array.isArray(data),
            length: data?.length
          })

          // 确保数据是数组
          if (!Array.isArray(data)) {
            console.warn(`用户 ${user.username} 的组数据不是数组:`, data)
            return []
          }

          // 处理组数据 - 只保留简单的组名
          const groups = data.map(groupName => ({
            name: groupName,
            dn: groupName,
            displayName: formatGroupName(groupName)
          }))

          console.log(`用户 ${user.username} 的组列表:`, groups)
          return groups
        })
        .catch(error => {
          console.error(`获取用户 ${user.username} 的组信息失败:`, error)
          throw error
        })
    })

    const allUserGroups = await Promise.all(groupsPromises)
    console.log('所有用户的组信息:', allUserGroups)

    // 找出所有用户共同的组
    const commonGroups = allUserGroups.reduce((common, current, index) => {
      if (index === 0) return current
      return common.filter(group =>
        current.some(g => g.name === group.name)
      )
    }, [])

    console.log('用���共同的组:', commonGroups)

    // 设置到组件中
    userGroups.value = commonGroups
    batchRemoveFromGroupForm.value.groups = []

    console.log('设置到 userGroups 的数据:', userGroups.value)

    // 在设置对话框可见性之前先确保其他状态正确
    console.log('设置对话框可见性之前的状态:', {
      selectedUsersLength: selectedUsers.value.length,
      userGroupsLength: userGroups.value.length,
      formGroups: batchRemoveFromGroupForm.value.groups,
      dialogVisibleBefore: batchRemoveFromGroupDialogVisible.value
    })

    // 强制更新对话框可见性
    await nextTick()
    batchRemoveFromGroupDialogVisible.value = true

    // 检查对话框可见性是否已更新
    console.log('对话框可见性已更新:', {
      dialogVisibleAfter: batchRemoveFromGroupDialogVisible.value,
      userGroupsAfter: userGroups.value
    })
  } catch (error) {
    console.error('获取用户组信息失败:', {
      error,
      response: error.response?.data,
      status: error.response?.status
    })
    ElMessage.error(error.response?.data?.detail || '获取用户组信息失败')
  }
}

const handleBatchRemoveFromGroupConfirm = async () => {
  if (!batchRemoveFromGroupForm.value.groups.length) {
    ElMessage.warning('请选择要移除的组')
    return
  }

  removingFromGroup.value = true
  try {
    console.log('开始批量移除组:', {
      selectedUsers: selectedUsers.value.map(u => u.username),
      groupsToRemove: batchRemoveFromGroupForm.value.groups
    })

    const promises = selectedUsers.value.map(user => {
      return getUserGroups(user.username)
        .then(({ data: currentGroups }) => {
          console.log(`获取用户 ${user.username} 当前的组:`, currentGroups)

          // 过滤掉要移除的组
          const remainingGroups = currentGroups.filter(group =>
            !batchRemoveFromGroupForm.value.groups.includes(group)
          )

          console.log(`用户 ${user.username} 更新后的组:`, remainingGroups)

          // 调用 API 更新用户组
          return updateUserGroups(user.username, remainingGroups)
            .then(() => {
              console.log(`更新用户 ${user.username} 的组成功`)
            })
            .catch(error => {
              console.error(`更新用户 ${user.username} 的组失败:`, error)
              throw error
            })
        })
    })

    await Promise.all(promises)
    ElMessage.success('批量移除组成功')
    batchRemoveFromGroupDialogVisible.value = false
    await fetchUsers() // 刷新用户列表
  } catch (error) {
    console.error('批量移除组失败:', {
      error,
      response: error.response?.data,
      status: error.response?.status,
      message: error.message
    })
    ElMessage.error(error.response?.data?.detail || '批量移除组失败')
  } finally {
    removingFromGroup.value = false
  }
}

// 添加 isEdit 计算属性
const isEdit = computed(() => dialogType.value === 'edit')

// 在methods中添加
const handleFixCN = async (username) => {
  try {
    // 先获取用户信息
    const user = users.value.find(u => u.username === username)
    if (!user) {
      ElMessage.error('未找到用户信息')
      return
    }

    // 检查CN是否已经是默认的(与displayName相���)
    if (user.name === user.cn) {
      ElMessage.info('该用户的CN已经是默认的,无需修复')
      return
    }

    await ElMessageBox.confirm(
      '确定要修复该用户的CN吗？如果当前OU下存在同名用户，系统将自动在CN后添加数字后缀以确保唯一性。',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    try {
      await fixUserCN(username)
      ElMessage.success('CN修复成功')
      await fetchUsers()
    } catch (error) {
      if (error.response?.data?.detail?.includes('entryAlreadyExists')) {
        // 提供更详细的错误信息和建议
        await ElMessageBox.alert(
          `修复失败：当前OU下已存在同名用户。\n\n您可以：\n1. 修改用户显示名称后再尝试修复CN\n2. 将用户移动到其他OU\n3. 手动指定一个唯一的CN`,
          '修复CN失败',
          {
            confirmButtonText: '我知道了',
            type: 'warning'
          }
        )
      } else {
        ElMessage.error('修复CN失败：' + (error.response?.data?.detail || '未知错误'))
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('修复CN失败')
    }
  }
}

const handleBatchFixCN = async () => {
  try {
    // 先检查选中的用户中是否有需要修复的
    const needFixUsers = selectedUsers.value.filter(user => user.name !== user.cn)

    if (needFixUsers.length === 0) {
      ElMessage.info('选中的用户CN都已经是默认的,无需修复')
      return
    }

    await ElMessageBox.confirm(
      `在选中的 ${selectedUsers.value.length} 个用户中,有 ${needFixUsers.length} 个用户需要修复CN。\n\n注意：如果发现CN冲突，系统将自动在CN后添加数字后缀以确保唯一性。是否继续？`,
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    let success = 0;
    let failed = 0;
    let skipped = selectedUsers.value.length - needFixUsers.length;
    let existsErrors = 0;
    let conflictUsers = [];

    // 只处理需要修复的用户
    for (const user of needFixUsers) {
      try {
        await fixUserCN(user.username)
        success++;
      } catch (error) {
        if (error.response?.data?.detail?.includes('entryAlreadyExists')) {
          existsErrors++;
          conflictUsers.push(user.name);
          console.error(`修复用户 ${user.username} 的CN失败: 当前OU下已存在同名用户`)
        } else {
          failed++;
          console.error(`修复���户 ${user.username} 的CN失败:`, error)
        }
      }
    }

    let message = `批量修复完成：成功${success}个，失败${failed}个，跳过${skipped}个(CN已是默认)`
    if (existsErrors > 0) {
      message += `\n\n${existsErrors}个用户因CN冲突而失败：\n${conflictUsers.join('\n')}\n\n您可以：\n1. 修改这些用户的显示名称后再尝试修复CN\n2. 将这些用户移动到其他OU\n3. 手动指定唯一的CN`

      await ElMessageBox.alert(message, '批量修复结果', {
        confirmButtonText: '我知道了',
        type: 'warning'
      })
    } else {
      ElMessage.success(message)
    }

    await fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量修复CN失败')
    }
  }
}

// 在script部分添加
const passwordError = ref('')

const validatePassword = (value) => {
  if (!value) {
    passwordError.value = ''
    return true
  }

  if (value.length < 8) {
    passwordError.value = '密码长度至少8位'
    return false
  }

  let strengthCount = 0
  if (/[A-Z]/.test(value)) strengthCount++
  if (/[a-z]/.test(value)) strengthCount++
  if (/[0-9]/.test(value)) strengthCount++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(value)) strengthCount++

  if (strengthCount < 3) {
    passwordError.value = '密码必须包含大小写字母、数字和特殊字符中的至少3种'
    return false
  }

  passwordError.value = ''
  return true
}

// 添加用户详情相关的响应式变量
const detailVisible = ref(false)
const currentUser = ref(null)

// 添加显示详情的方法
const handleShowDetail = (user) => {
  currentUser.value = user
  detailVisible.value = true
}

// 暴露给父组件的方法
defineExpose({
  handleAdd,
  showBulkImportDialog,
  handleExportCommand,
  handleCustomExport: processCustomExport,
  handleSearch: (query) => {
    searchQuery.value = query
    fetchUsers()
  }
})
</script>

<style scoped>
.user-list-container {
  padding: 12px 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

/* 复用与 GroupList 相同的样式 */
.toolbar {
  display: none; /* 隐藏工具栏 */
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 0 16px;
}

.pagination-container {
  margin-top: 12px;
  margin-bottom: 12px;
}

/* 美化滚动条 */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.group-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.group-popover {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-tag) {
  margin: 2px;
}

:deep(.el-select__tags) {
  flex-wrap: nowrap;
  overflow: hidden;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-icon) {
  margin-right: 4px;
}

.text-red {
  color: var(--el-color-danger);
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
}

.group-select {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.group-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.group-search {
  margin-bottom: 12px;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 8px;
}

/* 统一滚动条式 */
.group-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.group-list::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.group-list::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.group-category {
  display: flex;
  flex-direction: column;
}

.category-title {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-regular);
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin-bottom: 8px;
}

.group-item {
  margin-bottom: 4px;
}

.group-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-height: 32px;
  flex-wrap: wrap;
}

.group-name {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  white-space: nowrap;
}

.group-description {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-checkbox) {
  display: flex;
  align-items: flex-start;
  padding: 8px 12px;
  border-radius: 4px;
  margin: 0 !important;
  width: 100%;
}

:deep(.el-checkbox:hover) {
  background-color: var(--el-fill-color-light);
}

:deep(.el-checkbox__label) {
  display: block;
  padding-left: 12px;
  line-height: 1.4;
  width: 100%;
}

:deep(.el-checkbox__input) {
  margin-top: 8px;
  flex-shrink: 0;
}

/* 当描���文��过长时显示省略号 */
.group-item:hover .group-description {
  white-space: normal;
  word-break: break-all;
}

.import-container {
  padding: 0 20px;
}

.import-steps {
  margin-bottom: 24px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 16px;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 8px;
  color: var(--el-text-color-primary);
}

.step-content p {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.template-downloads {
  margin-top: 12px;
  display: flex;
  gap: 12px;
}

.upload-area {
  margin-top: 12px;
}

.import-upload {
  width: 100%;
}

.import-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: auto;
  padding: 20px;
}

.import-result {
  margin-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 24px;
}

.success-details,
.failed-details {
  margin-top: 20px;
}

.success-details h4,
.failed-details h4 {
  margin: 0 0 12px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.password-field {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
}

.password-input {
  width: 100%;
}

.password-input :deep(.el-input__wrapper) {
  padding-right: 8px;
}

.password-input :deep(.el-input__inner) {
  font-family: monospace;
}

.password-input :deep(.el-input__suffix) {
  margin-right: 8px;
}

.password-input :deep(.el-input-group__append) {
  padding: 0;
}

.password-input :deep(.el-input-group__append .el-button) {
  border: none;
  height: 32px;
  padding: 0 15px;
  white-space: nowrap;
  border-left: 1px solid var(--el-border-color);
}

.copy-btn {
  margin-left: 8px;
}

.success-details {
  margin-top: 20px;
  margin-bottom: 20px;
}

.export-buttons {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.success-details h4,
.failed-details h4 {
  margin-bottom: 12px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.batch-operation-toolbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--el-bg-color);
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.selected-count {
  color: var(--el-text-color-secondary);
}

.batch-operations {
  display: flex;
  gap: 12px;
}

/* 调整表格容器的底部边距，为批量操作工具栏留出空间 */
.table-container {
  margin-bottom: 0; /* 修改为0，移除空白 */
}

/* 表格筛选器样式优化 */
:deep(.el-table__column-filter-trigger) {
  margin-left: 6px;
  color: var(--el-text-color-placeholder);
  transition: color 0.3s ease;
}

:deep(.el-table__column-filter-trigger:hover) {
  color: var(--el-color-primary);
}

:deep(.el-table__column-filter-trigger.el-table__column-filter-trigger--active) {
  color: var(--el-color-primary);
}

/* 筛选下拉框样式 */
:deep(.el-table-filter) {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--el-border-color-light);
  padding: 8px 0;
}

:deep(.el-table-filter__list) {
  padding: 4px 0;
}

:deep(.el-table-filter__list-item) {
  padding: 8px 16px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 4px;
  margin: 2px 8px;
}

:deep(.el-table-filter__list-item:hover) {
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
}

:deep(.el-table-filter__list-item.is-active) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
}

:deep(.el-table-filter__bottom) {
  border-top: 1px solid var(--el-border-color-lighter);
  padding: 8px 16px;
  background-color: var(--el-fill-color-extra-light);
}

:deep(.el-table-filter__bottom .el-button) {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 4px;
}

/* 表头列文字与筛选图标的间距优化 */
:deep(.el-table th .cell) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

:deep(.el-table th.is-center .cell) {
  justify-content: center;
}

:deep(.el-table th.is-left .cell) {
  justify-content: flex-start;
}

:deep(.el-table th.is-right .cell) {
  justify-content: flex-end;
}

.batch-remove-from-group-container {
  padding: 20px;
}

.selected-users-info {
  margin-bottom: 20px;
  color: #606266;
}

.group-selector {
  width: 100%;
}

.group-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.group-name {
  flex: 1;
}

.info-icon {
  color: #909399;
  font-size: 16px;
}

:deep(.batch-remove-group-popper) {
  z-index: 2100;
}

:deep(.el-button.el-button--primary.is-link) {
  padding: 0;
  height: auto;
  font-size: inherit;
}

:deep(.user-detail-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}
</style>