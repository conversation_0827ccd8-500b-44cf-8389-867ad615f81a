<template>
  <div class="software-detail">
    <!-- 页面头部导航 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" class="back-button">
          <el-icon><Back /></el-icon> 返回
        </el-button>
        <div class="title-area">
          <el-icon class="header-icon"><Document /></el-icon>
          <h2 class="page-title">{{ softwareDetail.name }} {{ softwareDetail.version ? `v${softwareDetail.version}` : '' }}</h2>
        </div>
      </div>
      <div class="header-actions">
        <el-tag 
          :type="softwareDetail.is_compliant ? 'success' : 'danger'" 
          size="large"
          effect="dark"
          class="status-tag"
        >
          {{ softwareDetail.is_compliant ? '合规软件' : '不合规软件' }}
        </el-tag>
        <el-button 
          :type="softwareDetail.is_compliant ? 'danger' : 'success'" 
          @click="handleToggleCompliance"
        >
          <el-icon>
            <template v-if="softwareDetail.is_compliant">
              <Close />
            </template>
            <template v-else>
              <Check />
            </template>
          </el-icon>
          {{ softwareDetail.is_compliant ? '标记为不合规' : '标记为合规' }}
        </el-button>
      </div>
    </div>

    <!-- 主要内容卡片 -->
    <div class="content-layout" v-loading="loading">
      <!-- 左侧软件信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3><el-icon><InfoFilled /></el-icon> 软件概览</h3>
          </div>
        </template>
        
        <div class="info-section">
          <div class="info-item">
            <div class="info-label">安装终端数量</div>
            <div class="info-value">
              <el-tag size="large" type="info" effect="plain" class="count-tag">
                {{ softwareDetail.terminals?.length || 0 }}
              </el-tag>
            </div>
          </div>
          
          <el-divider />
          
          <div class="info-item">
            <div class="info-label">用途备注</div>
            <div class="info-value notes-area">
              <el-button type="primary" size="small" class="edit-button" @click="handleEditNotes">
                <el-icon><Edit /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="notes-content">
            <p v-if="softwareDetail.usage_notes">{{ softwareDetail.usage_notes }}</p>
            <p v-else class="no-notes">暂无用途备注信息</p>
          </div>
        </div>
      </el-card>

      <!-- 右侧终端列表卡片 -->
      <el-card class="terminals-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3><el-icon><Monitor /></el-icon> 安装此软件的终端列表</h3>
            <div class="header-count">
              共 <span class="count-number">{{ softwareDetail.terminals?.length || 0 }}</span> 台终端
            </div>
          </div>
        </template>
        
        <el-table 
          :data="softwareDetail.terminals || []" 
          border 
          style="width: 100%"
          row-class-name="terminal-table-row"
        >
          <el-table-column prop="hostname" label="终端名称" min-width="180">
            <template #default="scope">
              <div class="terminal-name">
                <el-icon><Monitor /></el-icon>
                <span>{{ scope.row.hostname }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ip_address" label="IP地址" min-width="150" />
          <el-table-column prop="os_name" label="操作系统" min-width="120" />
          <el-table-column prop="os_version" label="系统版本" min-width="120" />
          <el-table-column prop="online_status" label="在线状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.online_status ? 'success' : 'danger'" effect="light">
                {{ scope.row.online_status ? '在线' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <router-link :to="`/terminal/detail/${scope.row.id}`">
                <el-button
                  size="small"
                  type="primary"
                  text
                >
                  <el-icon><View /></el-icon> 查看
                </el-button>
              </router-link>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 编辑用途备注对话框 -->
    <el-dialog
      v-model="editNotesDialogVisible"
      title="编辑软件用途备注"
      width="550px"
      destroy-on-close
      class="notes-dialog"
      top="15vh"
    >
      <div class="dialog-content">
        <div class="software-info">
          <el-icon class="software-icon"><Document /></el-icon>
          <div class="software-details">
            <div class="software-name">{{ softwareDetail.name }}</div>
            <div class="software-version">{{ softwareDetail.version || '所有版本' }}</div>
          </div>
        </div>
        
        <div class="notes-form">
          <div class="form-label">
            <el-icon><Memo /></el-icon>
            <span>用途备注</span>
          </div>
          <el-input
            v-model="editingNotes"
            type="textarea"
            rows="6"
            placeholder="请输入软件用途备注，说明此软件在组织中的用途..."
            resize="none"
            class="notes-textarea"
          />
          <div class="notes-tips">
            清晰的用途备注有助于确定软件的合规性，以及在组织内部的使用规范。
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editNotesDialogVisible = false" plain>取消</el-button>
          <el-button type="primary" @click="handleSaveUsageNotes" :loading="saveLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, InfoFilled, Monitor, Back, Memo, View, Check, Close, Edit } from '@element-plus/icons-vue'
import { terminalApi } from '@/api/terminal'
import type { SoftwareDetail } from '@/types/terminal'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const saveLoading = ref(false)
const softwareDetail = ref<SoftwareDetail>({
  name: '',
  terminals: [],
  is_compliant: true
})

// 用于编辑用途备注的状态
const editNotesDialogVisible = ref(false)
const editingNotes = ref('')

// 获取软件名称（从路由参数中）
const getSoftwareName = (): string => {
  return decodeURIComponent(route.params.name as string)
}

// 获取版本参数（如果存在）
const getVersionParam = (): string | undefined => {
  return route.query.version as string | undefined
}

// 加载软件详情
const loadSoftwareDetail = async () => {
  loading.value = true
  try {
    const name = getSoftwareName()
    const version = getVersionParam()
    
    const params = version ? { version } : undefined
    const response = await terminalApi.getSoftwareDetail(name, params)
    
    softwareDetail.value = response.data
  } catch (error) {
    console.error('加载软件详情失败:', error)
    ElMessage.error('加载软件详情失败')
  } finally {
    loading.value = false
  }
}

// 切换软件合规状态
const handleToggleCompliance = async () => {
  try {
    loading.value = true
    await terminalApi.updateSoftwareInfo(
      softwareDetail.value.name, 
      { 
        is_compliant: !softwareDetail.value.is_compliant,
        usage_notes: softwareDetail.value.usage_notes 
      },
      { version: softwareDetail.value.version }
    )
    
    // 更新本地状态
    softwareDetail.value.is_compliant = !softwareDetail.value.is_compliant
    
    ElMessage.success(`已将软件${softwareDetail.value.is_compliant ? '标记为合规' : '标记为不合规'}`)
  } catch (error) {
    console.error('更新软件合规状态失败:', error)
    ElMessage.error('更新软件合规状态失败')
  } finally {
    loading.value = false
  }
}

// 编辑用途备注
const handleEditNotes = () => {
  editingNotes.value = softwareDetail.value.usage_notes || ''
  editNotesDialogVisible.value = true
}

// 保存用途备注
const handleSaveUsageNotes = async () => {
  try {
    saveLoading.value = true
    await terminalApi.updateSoftwareInfo(
      softwareDetail.value.name, 
      { 
        is_compliant: !!softwareDetail.value.is_compliant,
        usage_notes: editingNotes.value 
      },
      { version: softwareDetail.value.version }
    )
    
    // 更新本地状态
    softwareDetail.value.usage_notes = editingNotes.value
    
    ElMessage.success('软件用途备注已更新')
    editNotesDialogVisible.value = false
  } catch (error) {
    console.error('更新软件用途备注失败:', error)
    ElMessage.error('更新软件用途备注失败')
  } finally {
    saveLoading.value = false
  }
}

// 返回软件列表页
const goBack = () => {
  router.push('/terminal/software')
}

onMounted(() => {
  loadSoftwareDetail()
})
</script>

<style scoped>
.software-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 16px;
  font-size: 14px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-tag {
  padding: 6px 12px;
  font-weight: 600;
}

/* 内容布局 */
.content-layout {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
}

/* 卡片通用样式 */
.info-card, .terminals-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.info-card:hover, .terminals-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

/* 信息卡片内容 */
.info-section {
  padding: 0 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0;
}

.info-label {
  font-weight: 600;
  color: #606266;
}

.count-tag {
  font-size: 16px;
  padding: 4px 12px;
  border-radius: 20px;
}

.notes-area {
  display: flex;
  justify-content: flex-end;
}

.notes-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  margin-top: 8px;
  line-height: 1.6;
  min-height: 80px;
}

.no-notes {
  color: #909399;
  font-style: italic;
}

/* 终端列表样式 */
.header-count {
  font-size: 14px;
  color: #606266;
}

.count-number {
  font-weight: 600;
  color: #409EFF;
}

.terminal-table-row {
  transition: all 0.2s;
}

.terminal-table-row:hover {
  background-color: #f0f9ff;
  transform: translateY(-2px);
}

.terminal-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409EFF;
  font-weight: 500;
}

/* 表单样式 */
.form-value {
  font-weight: bold;
  padding: 4px 0;
  color: #303133;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .content-layout {
    grid-template-columns: 1fr;
  }
}

/* 对话框样式 */
:deep(.notes-dialog .el-dialog__header) {
  margin: 0;
  padding: 20px 20px 15px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.notes-dialog .el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

:deep(.notes-dialog .el-dialog__headerbtn) {
  top: 20px;
}

:deep(.notes-dialog .el-dialog__body) {
  padding: 20px;
}

:deep(.notes-dialog .el-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #ebeef5;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.software-info {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: #f9fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.software-icon {
  font-size: 32px;
  color: #409EFF;
}

.software-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.software-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.software-version {
  font-size: 14px;
  color: #606266;
}

.notes-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.notes-textarea {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.notes-tips {
  font-size: 13px;
  color: #909399;
  line-height: 1.5;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 