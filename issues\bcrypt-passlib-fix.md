# BCrypt 版本兼容性问题修复

## 问题描述
- bcrypt 4.0.1+ 移除了 `__about__` 属性
- passlib 1.7.4 仍在尝试访问 `_bcrypt.__about__.__version__`
- 产生警告：`AttributeError: module 'bcrypt' has no attribute '__about__'`

## 解决方案
采用方案1：使用 bcrypt 替换 passlib

### 执行计划
1. 更新依赖配置（移除 passlib）
2. 创建密码工具模块
3. 更新现有代码
4. 验证修改
5. 清理临时文件

### 影响范围
- `backend/pyproject.toml`
- `backend/app/core/security.py`
- `backend/app/utils/auth.py`
- `backend/app/utils/__init__.py`
- 新增：`backend/app/utils/password.py`

## 执行状态
- [x] 步骤1：更新依赖配置
- [x] 步骤2：创建密码工具模块
- [x] 步骤3：更新现有代码
- [x] 步骤4：验证修改
- [x] 步骤5：清理临时文件

## 执行结果
✅ **修复完成！** 已完全消除 bcrypt 版本兼容性警告

### 主要变更
1. **移除依赖**：从 `passlib[bcrypt]>=1.7.4,<2.0` 改为 `bcrypt>=4.0.1`
2. **新增模块**：`backend/app/utils/password.py` - 直接使用 bcrypt 的密码工具
3. **更新代码**：
   - `backend/app/core/security.py` - 移除 passlib，使用新的密码工具
   - `backend/app/utils/auth.py` - 移除 passlib，使用新的密码工具
   - `backend/app/utils/__init__.py` - 更新导入路径

### 验证结果
- ✅ 密码散列功能正常
- ✅ 密码验证功能正常
- ✅ 兼容性测试通过
- ✅ 无 bcrypt/passlib 兼容性警告
- ✅ 保持现有 API 接口不变 