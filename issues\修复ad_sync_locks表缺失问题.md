# 修复数据库表缺失问题

## 问题描述

**错误现象**：
- 系统启动时AD自动同步功能报错：`relation "ad_sync_locks" does not exist`
- 访问资产设置API时报错：`relation "asset_settings" does not exist`
- 导致相关功能模块无法正常工作

**错误日志**：
```
2025-06-23 11:49:50,980 - app.services.ad_sync_lock - ERROR - 检查锁 ad_personnel_sync 状态时出错: (psycopg2.errors.UndefinedTable) relation "ad_sync_locks" does not exist

2025-06-23 14:52:16,557 - sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedTable) relation "asset_settings" does not exist
```

## 问题分析

**根本原因**：
1. 数据库中缺少关键表：`ad_sync_locks` 和 `asset_settings`
2. 这两个表在多个Alembic迁移版本中被频繁删除和重新创建
3. 在当前数据库迁移版本中被删除但没有重新创建，造成表缺失

**影响范围**：
- AD人员同步功能无法正常启动
- 资产设置API返回500错误
- 同步锁机制失效
- 可能导致并发同步操作冲突

## 修复方案

### 执行步骤

#### 1. 修复 ad_sync_locks 表

1. **创建新的数据库迁移**
   ```bash
   cd backend
   uv sync  # 确保依赖已安装
   alembic revision -m "recreate_ad_sync_locks_table"
   ```

2. **编写迁移代码**
   - 文件：`alembic/versions/7d28afa6797e_recreate_ad_sync_locks_table.py`
   - 在 `upgrade()` 中创建表和索引
   - 在 `downgrade()` 中删除表和索引

3. **应用数据库迁移**
   ```bash
   uv run alembic upgrade head
   ```

#### 2. 修复 asset_settings 表

1. **创建新的数据库迁移**
   ```bash
   alembic revision -m "recreate_asset_settings_table"
   ```

2. **编写迁移代码**
   - 文件：`alembic/versions/4c53a5efd16a_recreate_asset_settings_table.py`
   - 在 `upgrade()` 中创建表、索引和约束
   - 在 `downgrade()` 中删除表

3. **应用数据库迁移**
   ```bash
   uv run alembic upgrade head
   ```

### 创建的表结构

#### ad_sync_locks 表
```sql
CREATE TABLE ad_sync_locks (
    lock_name character varying NOT NULL PRIMARY KEY,
    is_locked boolean,
    locked_at timestamp with time zone,
    updated_at timestamp with time zone DEFAULT now(),
    locked_by character varying
);

CREATE INDEX ix_ad_sync_locks_lock_name ON ad_sync_locks (lock_name);
```

#### asset_settings 表
```sql
CREATE TABLE asset_settings (
    id integer NOT NULL PRIMARY KEY,
    company character varying(100) NOT NULL UNIQUE,
    asset_number_rule json NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);

CREATE INDEX ix_asset_settings_id ON asset_settings (id);
```

## 修复结果

✅ **修复成功**
- `ad_sync_locks` 表已重新创建
- `asset_settings` 表已重新创建
- 所有表结构包含必需字段
- 索引和约束创建正常
- 相关功能测试通过

### 验证结果

1. **ad_sync_locks 表**：
   - ✅ 表存在性确认
   - ✅ 5个字段结构正确
   - ✅ 索引 `ix_ad_sync_locks_lock_name` 正常
   - ✅ 锁获取/释放功能正常

2. **asset_settings 表**：
   - ✅ 表存在性确认
   - ✅ 5个字段结构正确
   - ✅ 主键和唯一约束正常
   - ✅ API查询功能正常

3. **其他关键表检查**：
   - ✅ `field_values` - 存在
   - ✅ `asset_types` - 存在
   - ✅ `asset_brands` - 存在
   - ✅ `asset_models` - 存在
   - ✅ `asset_statuses` - 存在

## 后续建议

1. **避免频繁删除关键表**：在未来的迁移中，避免删除关键的系统表
2. **增加表检查机制**：在应用启动时检查关键表是否存在
3. **完善错误处理**：为相关服务添加更好的错误处理和自动恢复机制
4. **迁移审查流程**：对会删除现有表的迁移进行更严格的审查

## 修复时间

- 开始时间：2025-06-23 11:53
- 完成时间：2025-06-23 15:05
- 总耗时：约72分钟

## 相关迁移文件

- `7d28afa6797e_recreate_ad_sync_locks_table.py` - 恢复AD同步锁表
- `4c53a5efd16a_recreate_asset_settings_table.py` - 恢复资产设置表 