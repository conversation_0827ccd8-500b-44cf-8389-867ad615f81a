# 网络访问配置指南

## 概述

本指南说明如何配置OPS平台，使其能够被局域网内的所有设备访问，而不仅限于本机访问。

## 配置说明

### 前端配置

前端使用Vite开发服务器，已配置为允许外部访问：

```typescript
// frontend/vite.config.ts
server: {
  host: '0.0.0.0', // 允许外部设备访问
  port: 3000,
  proxy: {
    '/api': {
      target: process.env.VITE_API_TARGET || 'http://127.0.0.1:8000', // 动态配置后端地址
      changeOrigin: true,
      secure: false // 开发环境允许非HTTPS
    }
  }
}
```

**重要说明**：
- 前端代理默认指向本机的后端服务 `127.0.0.1:8000`
- 可通过环境变量 `VITE_API_TARGET` 自定义后端地址
- 外部设备访问前端时，API请求会通过前端服务器代理到后端

### 后端配置

后端FastAPI服务器配置：

1. **服务器绑定**：已配置为 `host="0.0.0.0"`，允许外部访问
2. **CORS配置**：开发模式下允许所有源访问，生产模式下需要配置允许的源

## 使用方法

### 1. 启动服务

#### 启动后端服务
```bash
cd backend
python run.py
```
后端服务将在 `http://0.0.0.0:8000` 启动

#### 启动前端服务
```bash
cd frontend
npm run dev
```
前端服务将在 `http://0.0.0.0:3000` 启动

### 2. 获取本机IP地址

#### Windows系统
```cmd
ipconfig
```
查找 "IPv4 地址" 或 "IP Address"

#### Linux/Mac系统
```bash
ifconfig
# 或
ip addr show
```

### 3. 访问方式

假设本机IP地址为 `*************`：

- **前端访问**：`http://*************:3000`
- **后端API**：`http://*************:8000`
- **API文档**：`http://*************:8000/docs`

### 4. 移动设备访问

确保移动设备与服务器在同一局域网内，然后使用上述地址访问。

## 环境变量配置

### 自定义CORS源

如需要限制特定IP访问，可设置环境变量：

```bash
# Windows
set CORS_ALLOWED_ORIGINS=http://************:3000,http://************:3000

# Linux/Mac
export CORS_ALLOWED_ORIGINS=http://************:3000,http://************:3000
```

### 自定义后端API地址

如果后端运行在不同的地址或端口，可设置环境变量：

```bash
# Windows
set VITE_API_TARGET=http://*************:8000

# Linux/Mac
export VITE_API_TARGET=http://*************:8000
```

### 生产环境配置

生产环境下建议设置：

```bash
# Windows
set ENVIRONMENT=production
set CORS_ALLOWED_ORIGINS=http://your-domain.com

# Linux/Mac
export ENVIRONMENT=production
export CORS_ALLOWED_ORIGINS=http://your-domain.com
```

## 安全建议

### 开发环境
- 确保在可信的局域网环境中使用
- 定期更新依赖和安全补丁
- 不要在公网环境中使用开发配置

### 生产环境
- 使用HTTPS协议
- 配置防火墙规则
- 限制CORS源到具体的域名
- 启用访问日志监控
- 使用反向代理（如Nginx）

## 故障排查

### 1. 无法访问前端

**问题**：其他设备无法访问前端页面

**解决方案**：
1. 确认前端服务已启动并监听 `0.0.0.0:3000`
2. 检查防火墙是否阻止3000端口
3. 确认设备在同一局域网内

```bash
# 检查端口监听状态
netstat -an | findstr :3000  # Windows
netstat -an | grep :3000     # Linux/Mac
```

### 2. API请求被CORS阻止

**问题**：浏览器控制台显示CORS错误

**解决方案**：
1. 确认后端CORS配置正确
2. 检查请求的源地址是否在允许列表中
3. 开发模式下应该允许所有源访问

### 3. 无法获取本机IP

**解决方案**：
```bash
# Windows - 获取所有网络接口
ipconfig /all

# Linux/Mac - 获取活动网络接口
ip route get ******* | awk '{print $7}'
```

### 4. 防火墙问题

**Windows防火墙**：
1. 打开"Windows Defender 防火墙"
2. 选择"允许应用或功能通过Windows Defender防火墙"
3. 添加Python和Node.js应用

**Linux防火墙**：
```bash
# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw allow 8000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## 网络测试

### 连通性测试

从其他设备测试连接：

```bash
# 测试后端API连通性
curl http://*************:8000/

# 测试前端连通性
curl http://*************:3000/
```

### 浏览器测试

1. 在其他设备的浏览器中访问前端地址
2. 打开浏览器开发者工具查看网络请求
3. 确认API请求正常响应

## 性能优化

### 局域网访问优化

1. **使用有线连接**：提供更稳定的网络连接
2. **关闭不必要的网络服务**：减少网络干扰
3. **配置静态IP**：避免IP地址变化

### 多用户并发

如需支持多用户同时访问：

1. 增加数据库连接池大小
2. 配置Redis缓存
3. 监控服务器资源使用情况

## 常见问题

### Q: 为什么移动设备访问很慢？
A: 检查WiFi信号强度和网络带宽，考虑使用5GHz频段。

### Q: 可以通过互联网访问吗？
A: 需要配置端口转发和域名解析，建议使用VPN或专线连接。

### Q: 如何限制特定设备访问？
A: 通过路由器MAC地址过滤或配置CORS源白名单。

## 技术支持

如遇到其他问题，请检查：
1. 服务器日志文件
2. 浏览器开发者工具
3. 网络连接状态
4. 防火墙配置

---

**更新日期**：2024-12-20  
**版本**：1.0 