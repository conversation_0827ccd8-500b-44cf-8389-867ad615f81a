<template>
  <div class="mobile-terminal-list">
    <van-search v-model="searchValue" placeholder="搜索终端" />
    
    <van-list>
      <van-cell
        v-for="terminal in terminals"
        :key="terminal.id"
        :title="terminal.name"
        :label="terminal.ip"
        :value="terminal.status"
        is-link
        @click="showToast('功能开发中...')"
      >
        <template #icon>
          <van-icon 
            :name="terminal.status === '在线' ? 'success' : 'warning-o'" 
            :color="terminal.status === '在线' ? '#67c23a' : '#e6a23c'"
            size="16"
          />
        </template>
      </van-cell>
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'

const searchValue = ref('')
const terminals = ref([
  { id: 1, name: 'PC-001', ip: '************0', status: '在线' },
  { id: 2, name: 'PC-002', ip: '************1', status: '离线' },
  { id: 3, name: 'SERVER-001', ip: '************', status: '在线' }
])
</script>

<style lang="scss" scoped>
.mobile-terminal-list {
  background-color: #f7f8fa;
  min-height: 100vh;
}
</style> 