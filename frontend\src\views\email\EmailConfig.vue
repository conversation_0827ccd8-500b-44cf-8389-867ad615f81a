<template>
  <div class="email-config">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Setting /></el-icon>
        <h2 class="page-title">邮箱配置</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>邮箱配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">企业邮箱配置</span>
        </div>
        <div class="action-buttons">
          <Authority permission="email:config:create">
            <el-button type="primary" @click="handleCreateConfig">
              <el-icon><Plus /></el-icon> 新增应用配置
            </el-button>
          </Authority>
        </div>
      </div>

      <!-- 企业信息配置 -->
      <el-form 
        ref="corpFormRef" 
        :model="corpForm" 
        label-width="120px"
        class="corp-form"
      >
        <el-form-item label="企业ID">
          <el-input 
            v-model="corpForm.corp_id" 
            placeholder="请输入企业ID"
            :disabled="false"
          />
          <div class="form-tip">企业微信的CorpID，可在企业微信管理后台查看</div>
        </el-form-item>
        
        <el-form-item label="API地址">
          <el-input 
            v-model="corpForm.api_base_url" 
            placeholder="请输入API地址"
            :disabled="true"
          />
          <div class="form-tip">腾讯企业邮箱API地址</div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveCorpForm">保存企业配置</el-button>
        </el-form-item>
      </el-form>

      <el-divider content-position="left">应用配置</el-divider>

      <el-table 
        :data="configList" 
        border 
        style="width: 100%"
        row-class-name="config-table-row"
        header-row-class-name="config-table-header"
        header-cell-class-name="table-header-cell"
        v-loading="loading"
      >
        <el-table-column prop="app_name" label="应用名称" min-width="250">
          <template #header>
            <div class="column-header">应用名称</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="is_active" label="状态" width="100">
          <template #header>
            <div class="column-header">状态</div>
          </template>
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #header>
            <div class="column-header">创建时间</div>
          </template>
          <template #default="scope">
            <span>{{ formatDate(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="scope">
            <Authority permission="email:config:update">
              <el-button type="primary" size="small" @click="handleEditConfig(scope.row)">
                编辑
              </el-button>
            </Authority>
            <Authority permission="email:config:view">
              <el-button type="success" size="small" @click="handleTestConfig(scope.row)">
                测试连接
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 配置表单对话框 -->
    <el-dialog 
      v-model="configDialogVisible" 
      :title="configForm.id ? '编辑应用配置' : '新增应用配置'" 
      width="600px"
      destroy-on-close
    >
      <el-form 
        ref="configFormRef" 
        :model="configForm" 
        :rules="configFormRules"
        label-width="120px"
      >
        <el-form-item label="应用名称" prop="app_name" v-if="!configForm.id">
          <el-input 
            v-model="configForm.app_name" 
            placeholder="请输入应用名称"
          />
        </el-form-item>
        
        <el-form-item label="应用名称" v-else>
          <el-input 
            v-model="configForm.app_name" 
            placeholder="应用名称"
            disabled
          />
        </el-form-item>
        
        <el-form-item label="应用标识" prop="app_key" v-if="!configForm.id">
          <el-input 
            v-model="configForm.app_key" 
            placeholder="请输入应用标识"
          />
        </el-form-item>
        
        <el-form-item label="企业密钥" prop="corp_secret">
          <el-input 
            v-model="configForm.corp_secret" 
            placeholder="请输入企业密钥"
            type="password"
            show-password
          />
          <div class="form-tip">企业应用的Secret，用于API调用认证</div>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch 
            v-model="configForm.is_active"
            :active-text="'启用'"
            :inactive-text="'禁用'"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfigForm" :loading="saving">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, FormRules, FormInstance } from 'element-plus'
import { Setting, Plus } from '@element-plus/icons-vue'
import { getEmailConfigs, createEmailConfig, updateEmailConfig, deleteEmailConfig, testEmailConfig } from '@/api/email/config'
import Authority from '@/components/Authority/index.vue'
import type { EmailConfig } from '@/types/email'

// 预设的应用列表
const PRESET_APPS = [
  { name: '通讯录管理', key: 'contact_manager' },
  { name: '功能设置', key: 'function_settings' },
  { name: '单点登录', key: 'sso' },
  { name: '新邮件提醒', key: 'mail_notification' },
  { name: '日志查询', key: 'log_query' }
]

// 固定的企业信息
const corpForm = reactive({
  corp_id: '',  // 可编辑的企业ID
  api_base_url: 'https://api.exmail.qq.com/cgi-bin'  // 固定的API地址
})

const loading = ref(false)
const saving = ref(false)
const configList = ref<EmailConfig[]>([])
const configDialogVisible = ref(false)
const configFormRef = ref<FormInstance>()

const configForm = reactive({
  id: 0,
  corp_id: corpForm.corp_id,
  corp_secret: '',
  app_name: '',
  app_key: '',
  api_base_url: corpForm.api_base_url,
  is_active: true
})

const configFormRules = reactive<FormRules>({
  app_name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' }
  ],
  app_key: [
    { required: true, message: '请输入应用标识', trigger: 'blur' }
  ],
  corp_secret: [
    { required: true, message: '请输入企业密钥', trigger: 'blur' },
    { min: 20, message: '企业密钥长度不能少于20个字符', trigger: 'blur' }
  ]
})

// 加载配置列表
const loadConfigs = async () => {
  try {
    loading.value = true
    const response = await getEmailConfigs()
    const configs = response.data || []
    
    // 如果有配置，获取第一个配置的企业ID
    if (configs.length > 0 && configs[0].corp_id) {
      corpForm.corp_id = configs[0].corp_id
    }
    
    // 确保所有预设应用都在列表中
    const existingApps = new Map(configs.map((config: EmailConfig) => [config.app_key, config]))
    
    configList.value = PRESET_APPS.map(app => {
      if (existingApps.has(app.key)) {
        return existingApps.get(app.key)!
      } else {
        // 返回未配置的应用
        return {
          corp_id: corpForm.corp_id,
          corp_secret: '',
          app_name: app.name,
          app_key: app.key,
          api_base_url: corpForm.api_base_url,
          is_active: false,
          created_at: '-'
        } as EmailConfig
      }
    }) as EmailConfig[]
  } catch (error) {
    console.error('加载配置列表失败:', error)
    ElMessage.error('加载配置列表失败')
  } finally {
    loading.value = false
  }
}

// 保存企业配置
const saveCorpForm = async () => {
  try {
    saving.value = true
    
    // 更新所有应用配置的企业ID
    for (const config of configList.value) {
      if (config.id) {
        await updateEmailConfig(config.id, {
          corp_id: corpForm.corp_id
        })
      }
    }
    
    ElMessage.success('企业配置更新成功')
    
    // 重新加载配置列表
    await loadConfigs()
  } catch (error: any) {
    console.error('保存企业配置失败:', error)
    ElMessage.error(error.response?.data?.detail || '操作失败')
  } finally {
    saving.value = false
  }
}

// 编辑配置
const handleEditConfig = (row: EmailConfig) => {
  configForm.id = row.id || 0
  configForm.corp_id = corpForm.corp_id
  configForm.corp_secret = row.corp_secret || ''
  configForm.app_name = row.app_name || ''
  configForm.app_key = row.app_key || ''
  configForm.api_base_url = corpForm.api_base_url
  configForm.is_active = row.is_active
  configDialogVisible.value = true
}

// 新增配置
const handleCreateConfig = () => {
  configForm.id = 0
  configForm.corp_id = corpForm.corp_id
  configForm.corp_secret = ''
  configForm.app_name = ''
  configForm.app_key = ''
  configForm.api_base_url = corpForm.api_base_url
  configForm.is_active = true
  configDialogVisible.value = true
}

// 保存配置
const saveConfigForm = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        saving.value = true
        
        if (configForm.id) {
          // 更新现有配置
          await updateEmailConfig(configForm.id, {
            corp_id: configForm.corp_id,
            corp_secret: configForm.corp_secret,
            app_name: configForm.app_name,
            app_key: configForm.app_key,
            api_base_url: configForm.api_base_url,
            is_active: configForm.is_active
          })
          ElMessage.success('配置更新成功')
        } else {
          // 创建新配置
          await createEmailConfig({
            corp_id: configForm.corp_id,
            corp_secret: configForm.corp_secret,
            app_name: configForm.app_name,
            app_key: configForm.app_key,
            api_base_url: configForm.api_base_url,
            is_active: configForm.is_active
          })
          ElMessage.success('配置创建成功')
        }
        
        configDialogVisible.value = false
        loadConfigs()
      } catch (error: any) {
        console.error('保存配置失败:', error)
        ElMessage.error(error.response?.data?.detail || '操作失败')
      } finally {
        saving.value = false
      }
    }
  })
}

// 测试连接
const handleTestConfig = async (row: EmailConfig) => {
  if (!row.id) {
    ElMessage.warning('请先保存配置后再测试连接')
    handleEditConfig(row)
    return
  }
  
  try {
    ElMessage.info('正在测试连接...')
    const response = await testEmailConfig(row.id)
    if (response.data.success) {
      ElMessage.success(response.data.message)
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error: any) {
    console.error('连接测试失败:', error)
    ElMessage.error(error.response?.data?.detail || '连接测试失败')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString || dateString === '-') return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.email-config {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.corp-form {
  margin-bottom: 20px;
  max-width: 800px;
}

.config-table-row {
  transition: all 0.3s;
  height: 56px;
}

.config-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.config-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 