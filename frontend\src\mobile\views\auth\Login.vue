<template>
  <van-config-provider 
    :theme-vars="vantThemeVars"
  >
    <div class="mobile-login-container">
      <!-- 背景装饰 -->
      <div class="login-background">
        <div class="bg-gradient"></div>
        <div class="bg-circles">
          <div v-for="n in 6" :key="n" class="circle" :class="`circle-${n}`"></div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="login-content">
        <!-- 品牌区域 -->
        <div class="brand-section">
          <div class="logo">
            <van-icon name="setting-o" size="48" color="#fff" />
          </div>
          <h1 class="brand-title">运维管理平台</h1>
          <p class="brand-subtitle">移动端管理您的IT资产</p>
        </div>

        <!-- 登录表单 -->
        <div class="form-section">
          <div class="login-card">
            <!-- 认证方式选择 -->
            <van-tabs 
              v-model:active="authType" 
              class="auth-tabs"
              @change="handleAuthTypeChange"
            >
              <van-tab title="本地登录" name="local"></van-tab>
              <van-tab title="LDAP登录" name="ldap"></van-tab>
            </van-tabs>

            <!-- 登录表单 -->
            <van-form @submit="handleLogin" class="login-form">
              <!-- 用户名输入 -->
              <van-field
                v-model="loginForm.username"
                name="username"
                :label="authType === 'ldap' ? 'LDAP用户名' : '用户名'"
                :placeholder="authType === 'ldap' ? '请输入LDAP用户名' : '请输入用户名'"
                left-icon="contact"
                :rules="[{ required: true, message: '请输入用户名' }]"
                autocomplete="username"
              />

              <!-- 密码输入 -->
              <van-field
                v-model="loginForm.password"
                type="password"
                name="password"
                label="密码"
                placeholder="请输入密码"
                left-icon="lock"
                :rules="[{ required: true, message: '请输入密码' }]"
                autocomplete="current-password"
                @keyup.enter="handleLogin"
              />

              <!-- LDAP配置选择 -->
              <van-field
                v-if="authType === 'ldap'"
                v-model="ldapConfigText"
                name="ldapConfig"
                label="LDAP配置"
                placeholder="请选择LDAP配置"
                left-icon="cluster-o"
                is-link
                readonly
                :rules="[{ required: true, message: '请选择LDAP配置' }]"
                @click="showLdapPicker = true"
              />

              <!-- 记住登录 (仅本地登录) -->
              <van-field v-if="authType === 'local'" name="remember">
                <template #input>
                  <van-checkbox v-model="loginForm.remember">
                    记住登录状态
                  </van-checkbox>
                </template>
              </van-field>

              <!-- 登录按钮 -->
              <div class="login-button-wrapper">
                <van-button
                  type="primary"
                  size="large"
                  block
                  round
                  :loading="loading"
                  native-type="submit"
                  class="login-button"
                >
                  {{ authType === 'ldap' ? 'LDAP登录' : '登录' }}
                </van-button>
              </div>
            </van-form>
          </div>
        </div>
      </div>

      <!-- LDAP配置选择器 -->
      <van-popup
        v-model:show="showLdapPicker"
        position="bottom"
        :style="{ height: 'var(--mobile-popup-small-height, 48svh)' }"
      >
        <van-picker
          :columns="ldapConfigOptions"
          :loading="ldapConfigsLoading"
          @confirm="onLdapConfigConfirm"
          @cancel="showLdapPicker = false"
        />
      </van-popup>
    </div>
  </van-config-provider>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useTheme } from '@/composables/useTheme'
import { showToast, showSuccessToast, showFailToast } from 'vant'
import request from '@/utils/request'
import type { LdapConfig } from '@/types/auth'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 主题管理
const { vantThemeVars } = useTheme()

// 表单数据
const authType = ref<'local' | 'ldap'>('local')
const loading = ref(false)
const loginForm = ref({
  username: '',
  password: '',
  remember: false,
  ldapConfig: ''
})

// LDAP配置相关
const ldapConfigs = ref<LdapConfig[]>([])
const ldapConfigsLoading = ref(false)
const showLdapPicker = ref(false)

// 计算属性
const ldapConfigText = computed(() => {
  if (!loginForm.value.ldapConfig) return ''
  const config = ldapConfigs.value.find(c => c.id.toString() === loginForm.value.ldapConfig)
  return config ? `${config.name} (${config.server})` : ''
})

const ldapConfigOptions = computed(() => {
  return ldapConfigs.value.map(config => ({
    text: `${config.name} (${config.server})`,
    value: config.id.toString()
  }))
})

// 方法
const handleAuthTypeChange = (name: string | number) => {
  authType.value = name as 'local' | 'ldap'
  // 清空表单
  loginForm.value.username = ''
  loginForm.value.password = ''
  loginForm.value.ldapConfig = ''
  
  // 如果切换到LDAP登录，重新设置智能选择的配置
  if (name === 'ldap') {
    if (ldapConfigs.value.length === 0) {
      loadLdapConfigs()
    } else {
      // 配置已加载，重新设置智能选择的配置
      const autoSelected = ldapConfigs.value.find((config: LdapConfig) => config.is_auto_selected)
      if (autoSelected) {
        loginForm.value.ldapConfig = autoSelected.id.toString()
        console.log('重新选择LDAP配置:', autoSelected.name)
      } else {
        // 如果没有自动选择，使用默认配置
        const defaultConfig = ldapConfigs.value.find((config: LdapConfig) => config.is_default)
        if (defaultConfig) {
          loginForm.value.ldapConfig = defaultConfig.id.toString()
          console.log('使用默认LDAP配置:', defaultConfig.name)
        }
      }
    }
  }
}

const loadLdapConfigs = async () => {
  try {
    ldapConfigsLoading.value = true
    const response = await request.get('/auth/ldap-configs')
    ldapConfigs.value = response.data.configs || []
    
    // 只有在LDAP模式下才设置选中的配置
    if (authType.value === 'ldap') {
      // 如果有自动选择的配置，优先使用
      const autoSelected = ldapConfigs.value.find((config: LdapConfig) => config.is_auto_selected)
      if (autoSelected) {
        loginForm.value.ldapConfig = autoSelected.id.toString()
        console.log('自动选择LDAP配置:', autoSelected.name, autoSelected.match_reason)
      } else {
        // 如果没有自动选择，使用默认配置
        const defaultConfig = ldapConfigs.value.find((config: LdapConfig) => config.is_default)
        if (defaultConfig) {
          loginForm.value.ldapConfig = defaultConfig.id.toString()
          console.log('使用默认LDAP配置:', defaultConfig.name)
        }
      }
    }
  } catch (error) {
    console.error('加载LDAP配置失败:', error)
    showToast('加载LDAP配置失败')
  } finally {
    ldapConfigsLoading.value = false
  }
}

const onLdapConfigConfirm = ({ selectedValues }: { selectedValues: string[] }) => {
  loginForm.value.ldapConfig = selectedValues[0]
  showLdapPicker.value = false
}

const handleLogin = async () => {
  try {
    loading.value = true
    
    if (authType.value === 'local') {
      // 本地登录
      const formData = new FormData()
      formData.append('username', loginForm.value.username)
      formData.append('password', loginForm.value.password)
      formData.append('remember', loginForm.value.remember.toString())
      formData.append('auth_type', 'local')
      
      await userStore.login(formData)
    } else {
      // LDAP登录
      const response = await request.post('/auth/ldap-login', {
        username: loginForm.value.username,
        password: loginForm.value.password,
        config_id: parseInt(loginForm.value.ldapConfig)
      })
      
      const { access_token, user } = response.data
      userStore.setToken(access_token)
      userStore.userInfo = user
      userStore.initialized = true
      
      // 处理权限
      if (user.roles) {
        userStore.extractPermissions(user)
      }
    }
    
    showSuccessToast('登录成功')
    
    // 跳转到移动端应用菜单
    router.replace('/m/apps')
  } catch (error: any) {
    console.error('登录失败:', error)
    const errorMsg = error.response?.data?.detail || 
                    (authType.value === 'ldap' ? 'LDAP登录失败' : '登录失败')
    showFailToast(errorMsg)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 如果已登录，直接跳转
  if (userStore.isLoggedIn) {
    router.replace('/m/apps')
    return
  }
  
  // 页面加载时加载LDAP配置（为了确保配置可用）
  loadLdapConfigs()
})
</script>

<style lang="scss" scoped>
.mobile-login-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

// 背景装饰
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.8) 0%, 
    rgba(118, 75, 162, 0.8) 100%);
}

.bg-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
  
  &.circle-1 {
    width: 60px;
    height: 60px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }
  
  &.circle-2 {
    width: 80px;
    height: 80px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
  }
  
  &.circle-3 {
    width: 40px;
    height: 40px;
    top: 60%;
    left: 20%;
    animation-delay: 2s;
  }
  
  &.circle-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
  }
  
  &.circle-5 {
    width: 50px;
    height: 50px;
    bottom: 40%;
    left: 5%;
    animation-delay: 4s;
  }
  
  &.circle-6 {
    width: 70px;
    height: 70px;
    top: 40%;
    right: 5%;
    animation-delay: 5s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

// 主要内容
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--van-padding-md);
  position: relative;
  z-index: 1;
}

// 品牌区域
.brand-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
  padding: var(--van-padding-xl) 0;
  min-height: 200px;
}

.logo {
  margin-bottom: var(--van-padding-md);
  
  .van-icon {
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
  }
}

.brand-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 var(--van-padding-sm) 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

// 表单区域
.form-section {
  flex-shrink: 0;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--van-radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 0;
}

// 认证标签
.auth-tabs {
  :deep(.van-tabs__nav) {
    background: transparent;
  }
  
  :deep(.van-tab) {
    color: var(--van-text-color);
    font-weight: 500;
  }
  
  :deep(.van-tab--active) {
    color: var(--van-primary-color);
    font-weight: 600;
  }
  
  :deep(.van-tabs__line) {
    background: var(--van-primary-color);
  }
}

// 登录表单
.login-form {
  padding: var(--van-padding-md);
  
  .van-field {
    margin-bottom: var(--van-padding-sm);
    
    :deep(.van-field__control) {
      font-size: 16px; // 防止iOS缩放
    }
  }
}

.login-button-wrapper {
  margin-top: var(--van-padding-lg);
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, var(--van-primary-color) 0%, #5a67d8 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  
  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}

// 响应式适配
@media (max-width: 375px) {
  .brand-title {
    font-size: 24px;
  }
  
  .brand-subtitle {
    font-size: 14px;
  }
  
  .login-form {
    padding: var(--van-padding-sm);
  }
}

@media (min-width: 414px) {
  .login-content {
    max-width: 400px;
    margin: 0 auto;
  }
}

// 安全区域适配
.mobile-login-container {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style> 