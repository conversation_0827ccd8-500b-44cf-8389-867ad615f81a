# 简化网络访问配置

## 问题说明
原来的配置过于复杂，现在提供更简单的解决方案。

## 简化方案

### 1. 自动IP检测（推荐）
前端会自动检测当前访问的IP地址：
- 如果通过 `localhost` 访问 → 后端使用 `localhost:8000`
- 如果通过局域网IP访问 → 后端使用相同IP的8000端口

### 2. 手动指定IP（备选）
如需手动指定后端IP，修改文件：`frontend/src/config/api.ts`

```typescript
// 修改这两行
const MANUAL_SERVER_IP = 'http://你的IP:8000' // 改为实际IP
const USE_AUTO_IP = false // 改为false启用手动IP
```

## 使用方法

### 1. 启动服务
```bash
# 后端
cd backend
python run.py

# 前端
cd frontend  
npm run dev
```

### 2. 访问测试
- **本机访问**：`http://localhost:3000`
- **局域网访问**：`http://你的IP:3000`

### 3. 验证
打开浏览器控制台，查看 "API Base URL" 日志，确认地址正确。

## 优势
1. **零配置**：大多数情况下自动工作
2. **简单**：只需要启动服务即可
3. **灵活**：支持手动配置特殊情况

## 故障排查
如果API请求仍然失败：
1. 检查后端是否正常启动在8000端口
2. 检查防火墙是否阻止8000端口
3. 在浏览器控制台查看实际请求的URL是否正确

---
这是行业标准的最佳实践，类似于Create React App等主流框架的处理方式。 