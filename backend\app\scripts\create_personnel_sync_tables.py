"""
创建人员邮箱同步相关数据表的脚本
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, backend_dir)

from sqlalchemy import create_engine, text
from app.config import Settings
from app.models.email import PersonnelSyncConfig, PersonnelSyncLog
from app.database import Base
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

def create_personnel_sync_tables():
    """创建人员邮箱同步相关表"""
    try:
        settings = Settings()
        engine = create_engine(settings.DATABASE_URL)
        
        logger.info("开始创建人员邮箱同步相关表...")
        
        # 创建表
        Base.metadata.create_all(bind=engine, tables=[
            PersonnelSyncConfig.__table__,
            PersonnelSyncLog.__table__
        ])
        
        logger.info("人员邮箱同步相关表创建完成")
        
        # 检查表是否创建成功
        with engine.connect() as conn:
            # 检查 personnel_sync_config 表
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='personnel_sync_config'"))
            if result.fetchone():
                logger.info("✓ personnel_sync_config 表创建成功")
            else:
                logger.error("✗ personnel_sync_config 表创建失败")
            
            # 检查 personnel_sync_logs 表
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='personnel_sync_logs'"))
            if result.fetchone():
                logger.info("✓ personnel_sync_logs 表创建成功")
            else:
                logger.error("✗ personnel_sync_logs 表创建失败")
        
        return True
        
    except Exception as e:
        logger.error(f"创建人员邮箱同步相关表失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = create_personnel_sync_tables()
    if success:
        print("人员邮箱同步相关表创建成功！")
    else:
        print("人员邮箱同步相关表创建失败！")
        sys.exit(1)
