# 移动端资产添加SCSS语法错误修复

## 问题描述
移动端资产添加页面 (`AssetAdd.vue`) 在构建时出现SCSS语法错误：
```
[sass] Selector ":deep(.van-field)" can't have a suffix
```

## 错误原因
在Vue 3的深度选择器 `:deep()` 语法中，不能在选择器内部使用 `&` 后缀操作符。

## 错误代码
```scss
:deep(.van-field) {
  padding: 12px 16px;
  
  &--readonly {  // ❌ 错误：深度选择器内不能使用 & 后缀
    .van-field__control {
      color: #323233;
    }
  }
}
```

## 修复方案
将深度选择器分离，避免在 `:deep()` 内使用 `&` 后缀：

```scss
:deep(.van-field) {
  padding: 12px 16px;
}

:deep(.van-field--readonly) {  // ✅ 正确：分离深度选择器
  .van-field__control {
    color: #323233;
  }
}
```

## 修复文件
- `frontend/src/mobile/views/asset/AssetAdd.vue`

## 验证结果
- ✅ SCSS编译错误已解决
- ✅ 样式功能保持不变
- ✅ 移动端添加资产页面正常运行

## 状态
🔧 **已修复** - 移动端资产添加页面SCSS语法错误已完全解决 