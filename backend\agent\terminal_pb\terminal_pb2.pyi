from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class RegisterRequest(_message.Message):
    __slots__ = ("hostname", "mac_address", "ip_address", "agent_version", "os_info", "unique_id")
    HOSTNAME_FIELD_NUMBER: _ClassVar[int]
    MAC_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    AGENT_VERSION_FIELD_NUMBER: _ClassVar[int]
    OS_INFO_FIELD_NUMBER: _ClassVar[int]
    UNIQUE_ID_FIELD_NUMBER: _ClassVar[int]
    hostname: str
    mac_address: str
    ip_address: str
    agent_version: str
    os_info: str
    unique_id: str
    def __init__(self, hostname: _Optional[str] = ..., mac_address: _Optional[str] = ..., ip_address: _Optional[str] = ..., agent_version: _Optional[str] = ..., os_info: _Optional[str] = ..., unique_id: _Optional[str] = ...) -> None: ...

class RegisterResponse(_message.Message):
    __slots__ = ("success", "terminal_id", "heartbeat_interval", "collection_interval", "message")
    SUCCESS_FIELD_NUMBER: _ClassVar[int]
    TERMINAL_ID_FIELD_NUMBER: _ClassVar[int]
    HEARTBEAT_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    COLLECTION_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    success: bool
    terminal_id: str
    heartbeat_interval: int
    collection_interval: int
    message: str
    def __init__(self, success: bool = ..., terminal_id: _Optional[str] = ..., heartbeat_interval: _Optional[int] = ..., collection_interval: _Optional[int] = ..., message: _Optional[str] = ...) -> None: ...

class HeartbeatRequest(_message.Message):
    __slots__ = ("terminal_id", "timestamp")
    TERMINAL_ID_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    terminal_id: str
    timestamp: int
    def __init__(self, terminal_id: _Optional[str] = ..., timestamp: _Optional[int] = ...) -> None: ...

class HeartbeatResponse(_message.Message):
    __slots__ = ("success", "has_command")
    SUCCESS_FIELD_NUMBER: _ClassVar[int]
    HAS_COMMAND_FIELD_NUMBER: _ClassVar[int]
    success: bool
    has_command: bool
    def __init__(self, success: bool = ..., has_command: bool = ...) -> None: ...

class TerminalInfoReport(_message.Message):
    __slots__ = ("terminal_id", "hardware", "os", "installed_software", "network", "last_login_user", "timestamp")
    TERMINAL_ID_FIELD_NUMBER: _ClassVar[int]
    HARDWARE_FIELD_NUMBER: _ClassVar[int]
    OS_FIELD_NUMBER: _ClassVar[int]
    INSTALLED_SOFTWARE_FIELD_NUMBER: _ClassVar[int]
    NETWORK_FIELD_NUMBER: _ClassVar[int]
    LAST_LOGIN_USER_FIELD_NUMBER: _ClassVar[int]
    TIMESTAMP_FIELD_NUMBER: _ClassVar[int]
    terminal_id: str
    hardware: HardwareInfo
    os: OSInfo
    installed_software: _containers.RepeatedCompositeFieldContainer[Software]
    network: NetworkInfo
    last_login_user: UserInfo
    timestamp: int
    def __init__(self, terminal_id: _Optional[str] = ..., hardware: _Optional[_Union[HardwareInfo, _Mapping]] = ..., os: _Optional[_Union[OSInfo, _Mapping]] = ..., installed_software: _Optional[_Iterable[_Union[Software, _Mapping]]] = ..., network: _Optional[_Union[NetworkInfo, _Mapping]] = ..., last_login_user: _Optional[_Union[UserInfo, _Mapping]] = ..., timestamp: _Optional[int] = ...) -> None: ...

class HardwareInfo(_message.Message):
    __slots__ = ("cpu_model", "cpu_cores", "memory_total", "disks", "serial_number", "manufacturer", "model")
    CPU_MODEL_FIELD_NUMBER: _ClassVar[int]
    CPU_CORES_FIELD_NUMBER: _ClassVar[int]
    MEMORY_TOTAL_FIELD_NUMBER: _ClassVar[int]
    DISKS_FIELD_NUMBER: _ClassVar[int]
    SERIAL_NUMBER_FIELD_NUMBER: _ClassVar[int]
    MANUFACTURER_FIELD_NUMBER: _ClassVar[int]
    MODEL_FIELD_NUMBER: _ClassVar[int]
    cpu_model: str
    cpu_cores: int
    memory_total: int
    disks: _containers.RepeatedCompositeFieldContainer[DiskInfo]
    serial_number: str
    manufacturer: str
    model: str
    def __init__(self, cpu_model: _Optional[str] = ..., cpu_cores: _Optional[int] = ..., memory_total: _Optional[int] = ..., disks: _Optional[_Iterable[_Union[DiskInfo, _Mapping]]] = ..., serial_number: _Optional[str] = ..., manufacturer: _Optional[str] = ..., model: _Optional[str] = ...) -> None: ...

class DiskInfo(_message.Message):
    __slots__ = ("name", "total_space", "free_space", "filesystem", "mount_point")
    NAME_FIELD_NUMBER: _ClassVar[int]
    TOTAL_SPACE_FIELD_NUMBER: _ClassVar[int]
    FREE_SPACE_FIELD_NUMBER: _ClassVar[int]
    FILESYSTEM_FIELD_NUMBER: _ClassVar[int]
    MOUNT_POINT_FIELD_NUMBER: _ClassVar[int]
    name: str
    total_space: int
    free_space: int
    filesystem: str
    mount_point: str
    def __init__(self, name: _Optional[str] = ..., total_space: _Optional[int] = ..., free_space: _Optional[int] = ..., filesystem: _Optional[str] = ..., mount_point: _Optional[str] = ...) -> None: ...

class OSInfo(_message.Message):
    __slots__ = ("name", "version", "build", "architecture", "install_date", "installed_updates", "security")
    NAME_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    BUILD_FIELD_NUMBER: _ClassVar[int]
    ARCHITECTURE_FIELD_NUMBER: _ClassVar[int]
    INSTALL_DATE_FIELD_NUMBER: _ClassVar[int]
    INSTALLED_UPDATES_FIELD_NUMBER: _ClassVar[int]
    SECURITY_FIELD_NUMBER: _ClassVar[int]
    name: str
    version: str
    build: str
    architecture: str
    install_date: str
    installed_updates: _containers.RepeatedScalarFieldContainer[str]
    security: SecurityInfo
    def __init__(self, name: _Optional[str] = ..., version: _Optional[str] = ..., build: _Optional[str] = ..., architecture: _Optional[str] = ..., install_date: _Optional[str] = ..., installed_updates: _Optional[_Iterable[str]] = ..., security: _Optional[_Union[SecurityInfo, _Mapping]] = ...) -> None: ...

class SecurityInfo(_message.Message):
    __slots__ = ("firewall_enabled", "antivirus", "antivirus_enabled")
    FIREWALL_ENABLED_FIELD_NUMBER: _ClassVar[int]
    ANTIVIRUS_FIELD_NUMBER: _ClassVar[int]
    ANTIVIRUS_ENABLED_FIELD_NUMBER: _ClassVar[int]
    firewall_enabled: bool
    antivirus: str
    antivirus_enabled: bool
    def __init__(self, firewall_enabled: bool = ..., antivirus: _Optional[str] = ..., antivirus_enabled: bool = ...) -> None: ...

class Software(_message.Message):
    __slots__ = ("name", "version", "publisher", "install_date", "size", "install_location")
    NAME_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    PUBLISHER_FIELD_NUMBER: _ClassVar[int]
    INSTALL_DATE_FIELD_NUMBER: _ClassVar[int]
    SIZE_FIELD_NUMBER: _ClassVar[int]
    INSTALL_LOCATION_FIELD_NUMBER: _ClassVar[int]
    name: str
    version: str
    publisher: str
    install_date: str
    size: int
    install_location: str
    def __init__(self, name: _Optional[str] = ..., version: _Optional[str] = ..., publisher: _Optional[str] = ..., install_date: _Optional[str] = ..., size: _Optional[int] = ..., install_location: _Optional[str] = ...) -> None: ...

class NetworkInfo(_message.Message):
    __slots__ = ("interfaces", "hostname", "domain", "dns_servers", "default_gateway")
    INTERFACES_FIELD_NUMBER: _ClassVar[int]
    HOSTNAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    DNS_SERVERS_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_GATEWAY_FIELD_NUMBER: _ClassVar[int]
    interfaces: _containers.RepeatedCompositeFieldContainer[NetworkInterface]
    hostname: str
    domain: str
    dns_servers: _containers.RepeatedScalarFieldContainer[str]
    default_gateway: str
    def __init__(self, interfaces: _Optional[_Iterable[_Union[NetworkInterface, _Mapping]]] = ..., hostname: _Optional[str] = ..., domain: _Optional[str] = ..., dns_servers: _Optional[_Iterable[str]] = ..., default_gateway: _Optional[str] = ...) -> None: ...

class NetworkInterface(_message.Message):
    __slots__ = ("name", "mac_address", "ip_address", "subnet_mask", "dhcp_enabled", "is_connected")
    NAME_FIELD_NUMBER: _ClassVar[int]
    MAC_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    SUBNET_MASK_FIELD_NUMBER: _ClassVar[int]
    DHCP_ENABLED_FIELD_NUMBER: _ClassVar[int]
    IS_CONNECTED_FIELD_NUMBER: _ClassVar[int]
    name: str
    mac_address: str
    ip_address: str
    subnet_mask: str
    dhcp_enabled: bool
    is_connected: bool
    def __init__(self, name: _Optional[str] = ..., mac_address: _Optional[str] = ..., ip_address: _Optional[str] = ..., subnet_mask: _Optional[str] = ..., dhcp_enabled: bool = ..., is_connected: bool = ...) -> None: ...

class UserInfo(_message.Message):
    __slots__ = ("username", "full_name", "login_time", "domain")
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    FULL_NAME_FIELD_NUMBER: _ClassVar[int]
    LOGIN_TIME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    username: str
    full_name: str
    login_time: str
    domain: str
    def __init__(self, username: _Optional[str] = ..., full_name: _Optional[str] = ..., login_time: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class ReportResponse(_message.Message):
    __slots__ = ("success", "message")
    SUCCESS_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    success: bool
    message: str
    def __init__(self, success: bool = ..., message: _Optional[str] = ...) -> None: ...

class CommandRequest(_message.Message):
    __slots__ = ("terminal_id",)
    TERMINAL_ID_FIELD_NUMBER: _ClassVar[int]
    terminal_id: str
    def __init__(self, terminal_id: _Optional[str] = ...) -> None: ...

class Command(_message.Message):
    __slots__ = ("command_id", "type", "content", "create_time", "timeout")
    class CommandType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        COLLECT_INFO: _ClassVar[Command.CommandType]
        UPGRADE_AGENT: _ClassVar[Command.CommandType]
        CUSTOM_COMMAND: _ClassVar[Command.CommandType]
        UNINSTALL_SOFTWARE: _ClassVar[Command.CommandType]
    COLLECT_INFO: Command.CommandType
    UPGRADE_AGENT: Command.CommandType
    CUSTOM_COMMAND: Command.CommandType
    UNINSTALL_SOFTWARE: Command.CommandType
    COMMAND_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    TIMEOUT_FIELD_NUMBER: _ClassVar[int]
    command_id: str
    type: Command.CommandType
    content: str
    create_time: int
    timeout: int
    def __init__(self, command_id: _Optional[str] = ..., type: _Optional[_Union[Command.CommandType, str]] = ..., content: _Optional[str] = ..., create_time: _Optional[int] = ..., timeout: _Optional[int] = ...) -> None: ...

class CommandResult(_message.Message):
    __slots__ = ("command_id", "terminal_id", "success", "output", "error", "execution_time", "execution_duration")
    COMMAND_ID_FIELD_NUMBER: _ClassVar[int]
    TERMINAL_ID_FIELD_NUMBER: _ClassVar[int]
    SUCCESS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_FIELD_NUMBER: _ClassVar[int]
    ERROR_FIELD_NUMBER: _ClassVar[int]
    EXECUTION_TIME_FIELD_NUMBER: _ClassVar[int]
    EXECUTION_DURATION_FIELD_NUMBER: _ClassVar[int]
    command_id: str
    terminal_id: str
    success: bool
    output: str
    error: str
    execution_time: int
    execution_duration: int
    def __init__(self, command_id: _Optional[str] = ..., terminal_id: _Optional[str] = ..., success: bool = ..., output: _Optional[str] = ..., error: _Optional[str] = ..., execution_time: _Optional[int] = ..., execution_duration: _Optional[int] = ...) -> None: ...

class CommandResultResponse(_message.Message):
    __slots__ = ("received", "message")
    RECEIVED_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    received: bool
    message: str
    def __init__(self, received: bool = ..., message: _Optional[str] = ...) -> None: ...
