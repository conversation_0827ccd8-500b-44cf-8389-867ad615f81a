"""add_ad_sync_logs

Revision ID: e37c828bbd78
Revises: 5910494c9393
Create Date: 2025-03-18 16:47:46.200813

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = 'e37c828bbd78'
down_revision: Union[str, None] = '5910494c9393'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ad_sync_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('operator', sa.String(length=100), nullable=True, comment='操作员用户名'),
    sa.Column('source_type', sa.String(length=20), nullable=True, comment='同步来源类型：all/company/department'),
    sa.Column('source_id', sa.Integer(), nullable=True, comment='同步来源ID（公司ID或部门ID）'),
    sa.Column('sync_time', sa.DateTime(), nullable=False, comment='同步时间'),
    sa.Column('total_users', sa.Integer(), nullable=True, comment='总用户数'),
    sa.Column('created_users', sa.Integer(), nullable=True, comment='新创建用户数'),
    sa.Column('skipped_users', sa.Integer(), nullable=True, comment='跳过用户数'),
    sa.Column('created_ous', sa.Integer(), nullable=True, comment='创建的组织单位数'),
    sa.Column('errors', sa.Text(), nullable=True, comment='错误信息（JSON格式）'),
    sa.Column('details', sa.Text(), nullable=True, comment='详细信息（JSON格式）'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ad_sync_logs_id'), 'ad_sync_logs', ['id'], unique=False)
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.drop_index(op.f('ix_ad_sync_logs_id'), table_name='ad_sync_logs')
    op.drop_table('ad_sync_logs')
    # ### end Alembic commands ###
