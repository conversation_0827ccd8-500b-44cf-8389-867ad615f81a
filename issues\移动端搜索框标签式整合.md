# 移动端搜索框标签式整合任务

## 任务背景
将当前移动端的搜索框改成：将搜索类型选择器与搜索框整合为一个整体，使用内置的标签式设计显示当前搜索类型，点击可快速切换搜索类型，更简洁统一的外观。

## 当前现状
- 现有组件：`SimpleSearch.vue` 使用分离的下拉菜单和搜索框
- 使用场景：`AssetList.vue` 和 `InventoryTask.vue`
- 设计问题：界面分散，占用空间较多

## 解决方案
采用方案1：内置标签式设计
- 搜索类型以标签形式内嵌在搜索框左侧
- 点击标签快速切换类型
- 视觉统一，外观简洁
- 符合移动端设计习惯

## 实施计划

### 步骤1：创建新的IntegratedSearch组件 ✅
- 文件路径: `frontend/src/mobile/components/business/IntegratedSearch.vue`
- 功能: 替代SimpleSearch，集成标签式搜索类型选择器
- API: 保持与SimpleSearch相同的接口

### 步骤2：实现标签式UI设计 ✅
- 搜索容器: Flexbox布局，统一的搜索容器
- 标签区域: 左侧显示当前搜索类型，带颜色和图标
- 输入区域: 右侧自适应搜索框
- 切换交互: 点击标签弹出选择列表

### 步骤3：实现搜索类型切换逻辑 ✅
- 弹窗选择: 使用van-action-sheet展示搜索类型选项
- 状态管理: 维护搜索类型状态
- 事件处理: 处理切换和搜索逻辑

### 步骤4：样式优化 ✅
- 统一外观设计: 整合的搜索容器
- 响应式适配: 适配不同屏幕尺寸
- 主题匹配: 支持Vant主题
- 图标支持: 为每个搜索类型添加图标

### 步骤5：替换现有使用场景 ✅
- AssetList.vue: 已替换SimpleSearch为IntegratedSearch
- InventoryTask.vue: 已替换SimpleSearch为IntegratedSearch

### 步骤6：清理和文档 ✅
- 代码整理: 完成所有组件替换
- 测试验证: 接口保持一致，无需修改使用方代码

## 实施结果
✅ **已完成所有步骤**

### 核心功能
- ✅ 创建了IntegratedSearch组件，实现标签式设计
- ✅ 搜索类型以标签形式内嵌在搜索框左侧
- ✅ 点击标签弹出van-action-sheet进行快速切换
- ✅ 每个搜索类型配置了独特的颜色和图标
- ✅ 保持了与SimpleSearch相同的API接口

### 视觉效果
- ✅ 统一简洁的搜索容器外观
- ✅ 彩色标签显示当前搜索类型
- ✅ 图标增强了类型识别度
- ✅ 响应式设计适配移动端
- ✅ 平滑的动画交互效果

### 使用场景
- ✅ AssetList.vue已成功替换
- ✅ InventoryTask.vue已成功替换
- ✅ 保持向后兼容，无需修改使用方代码

## 最终效果
- 统一简洁的搜索体验
- 直观便捷的类型切换
- 显著减少界面占用空间
- 大幅提升移动端操作体验 