# 同步锁管理页面访问问题修复

## 问题描述
用户反馈无法进入同步锁管理页面，日志显示API错误：
```
'User' object has no attribute 'get'
```

## 问题分析

### 根本原因
锁管理API (`backend/app/api/v1/lock_management.py`) 中的权限检查代码存在错误：

1. **错误的依赖导入**：使用了 `from app.utils.auth import get_current_user`
2. **错误的对象类型假设**：代码假设 `current_user` 是字典类型，使用 `.get()` 方法
3. **实际对象类型**：`get_current_user` 返回的是 `models.User` 对象，不是字典

### 错误代码示例
```python
# 错误的导入
from app.utils.auth import get_current_user

# 错误的权限检查
if not current_user.get("is_superuser", False):  # User对象没有get方法
    raise HTTPException(...)
```

## 修复方案

### 1. 修复导入和依赖
```python
# 修复前
from app.utils.auth import get_current_user

# 修复后
from app.api.deps import get_current_active_user, check_permissions
from app import models
```

### 2. 修复权限检查逻辑
```python
# 修复前
current_user: Dict = Depends(get_current_user)
if not current_user.get("is_superuser", False):

# 修复后
current_user: models.User = Depends(get_current_active_user)
if not current_user.is_superuser:
```

### 3. 添加权限检查辅助函数
```python
def get_user_permissions(db: Session, user: models.User) -> set:
    """获取用户权限集合"""
    if user.is_superuser:
        return {"*"}  # 超级管理员拥有所有权限
    
    # 获取用户所有角色和权限
    user_with_roles = db.query(models.User).filter(
        models.User.id == user.id
    ).first()
    
    permissions = set()
    if user_with_roles and user_with_roles.roles:
        for role in user_with_roles.roles:
            for permission in role.permissions:
                permissions.add(permission.code)
    
    return permissions

def has_permission(db: Session, user: models.User, required_permissions: list) -> bool:
    """检查用户是否具有所需权限"""
    if user.is_superuser:
        return True
    
    user_permissions = get_user_permissions(db, user)
    return any(perm in user_permissions for perm in required_permissions)
```

## 修复内容

### 修复的API端点
1. `GET /api/v1/locks/status` - 获取所有锁状态
2. `GET /api/v1/locks/ad/status` - 获取AD锁状态
3. `GET /api/v1/locks/email/status` - 获取邮箱锁状态
4. `GET /api/v1/locks/ad/{lock_name}` - 获取指定AD锁信息
5. `GET /api/v1/locks/email/{lock_name}` - 获取指定邮箱锁信息
6. `POST /api/v1/locks/cleanup` - 清理过期锁
7. `POST /api/v1/locks/force-cleanup` - 强制清理所有锁
8. `DELETE /api/v1/locks/ad/{lock_name}` - 强制释放AD锁
9. `DELETE /api/v1/locks/email/{lock_name}` - 强制释放邮箱锁

### 权限要求
- **管理员权限**：`current_user.is_superuser` 
- **AD管理权限**：`["ad:read"]` 或 `["ad:manage"]`
- **邮箱管理权限**：`["email:read"]` 或 `["email:manage"]`

## 页面访问路径

### 前端路由配置
- **路径**：`/system/lock-management`
- **组件**：`@/views/system/LockManagement.vue`
- **权限**：`system:lock:manage`

### 菜单配置
- **位置**：系统设置 → 同步锁管理
- **图标**：Lock
- **权限**：`system:lock:manage`

## 验证步骤

### 1. 检查用户权限
确保用户具有以下权限之一：
- 超级管理员权限 (`is_superuser = true`)
- 系统锁管理权限 (`system:lock:manage`)

### 2. 访问页面
- 登录系统后，在左侧菜单中找到"系统设置"
- 展开"系统设置"菜单
- 点击"同步锁管理"

### 3. 功能验证
- 查看锁状态概览
- 查看AD同步锁列表
- 查看邮箱同步锁列表
- 执行清理操作（如有权限）

## 修复结果

✅ **API错误已修复**：所有锁管理API端点现在正确处理User对象  
✅ **权限检查正常**：使用正确的权限检查逻辑  
✅ **页面路由正常**：前端路由和菜单配置正确  
✅ **功能完整**：所有锁管理功能可正常使用  

## 预防措施

1. **类型检查**：使用TypeScript和类型注解确保对象类型正确
2. **代码审查**：确保API开发时正确使用认证和权限系统
3. **测试覆盖**：添加API端点的单元测试和集成测试
4. **文档更新**：更新API文档说明正确的权限要求

---
**修复时间**: 2025-07-07  
**修复人员**: AI Assistant  
**问题级别**: 中 (影响功能访问)  
**修复状态**: 已完成 