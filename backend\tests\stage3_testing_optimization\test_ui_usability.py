"""
用户界面易用性测试
测试前端界面的用户体验和易用性
"""

import pytest
import logging
import time
from typing import Dict, List, Any
from fastapi.testclient import TestClient

from app.main import app

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class TestUIUsability:
    """用户界面易用性测试类"""
    
    @pytest.fixture(scope="class")
    def client(self):
        """测试客户端"""
        return TestClient(app)
    
    def test_api_response_time(self, client: TestClient):
        """测试API响应时间"""
        logger.info("=== 测试API响应时间 ===")
        
        # 测试关键API接口的响应时间
        api_endpoints = [
            "/api/v1/email/personnel-sync/extid-completion/stats",
            "/api/v1/email/personnel-sync/extid-completion/matches",
            "/api/v1/email/personnel-sync/data-backup/list",
            "/api/v1/email/personnel-sync/sync/config",
        ]
        
        response_times = {}
        
        for endpoint in api_endpoints:
            start_time = time.time()
            response = client.get(endpoint)
            response_time = time.time() - start_time
            
            response_times[endpoint] = response_time
            
            # API响应时间应该在合理范围内（2秒以内）
            if response.status_code in [200, 401, 422]:  # 正常响应或认证/验证错误
                assert response_time < 2.0, f"API {endpoint} 响应时间过长: {response_time:.3f}秒"
                logger.info(f"✅ {endpoint}: {response_time:.3f}秒")
            else:
                logger.warning(f"⚠️ {endpoint}: 状态码 {response.status_code}, 响应时间 {response_time:.3f}秒")
        
        # 计算平均响应时间
        avg_response_time = sum(response_times.values()) / len(response_times)
        logger.info(f"平均API响应时间: {avg_response_time:.3f}秒")
        
        assert avg_response_time < 1.0, f"平均API响应时间过长: {avg_response_time:.3f}秒"
    
    def test_api_error_handling(self, client: TestClient):
        """测试API错误处理的用户友好性"""
        logger.info("=== 测试API错误处理 ===")
        
        # 测试无效参数的错误响应
        test_cases = [
            {
                "endpoint": "/api/v1/email/personnel-sync/extid-completion/matches",
                "params": {"page": -1, "page_size": 10},
                "expected_status": [400, 422],
                "description": "负数页码"
            },
            {
                "endpoint": "/api/v1/email/personnel-sync/extid-completion/matches",
                "params": {"page": 1, "page_size": 0},
                "expected_status": [400, 422],
                "description": "零页面大小"
            },
            {
                "endpoint": "/api/v1/email/personnel-sync/extid-completion/matches",
                "params": {"page": 1, "page_size": 10000},
                "expected_status": [400, 422],
                "description": "过大页面大小"
            }
        ]
        
        for case in test_cases:
            response = client.get(case["endpoint"], params=case["params"])
            
            # 检查状态码是否符合预期
            assert response.status_code in case["expected_status"] or response.status_code == 401, \
                f"{case['description']}: 期望状态码 {case['expected_status']}, 实际 {response.status_code}"
            
            # 检查错误响应是否包含有用信息
            if response.status_code in [400, 422]:
                try:
                    error_data = response.json()
                    assert "detail" in error_data or "message" in error_data, \
                        f"{case['description']}: 错误响应缺少详细信息"
                    logger.info(f"✅ {case['description']}: 错误处理正确")
                except Exception:
                    logger.warning(f"⚠️ {case['description']}: 无法解析错误响应")
            else:
                logger.info(f"✅ {case['description']}: 状态码 {response.status_code}")
    
    def test_pagination_usability(self, client: TestClient):
        """测试分页功能的易用性"""
        logger.info("=== 测试分页功能易用性 ===")
        
        # 测试分页参数的合理性
        response = client.get("/api/v1/email/personnel-sync/extid-completion/matches", 
                            params={"page": 1, "page_size": 20})
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查分页响应结构
            required_fields = ["items", "total", "page", "page_size", "pages"]
            for field in required_fields:
                assert field in data, f"分页响应缺少字段: {field}"
            
            # 检查分页数据的合理性
            assert data["page"] >= 1, "页码应该从1开始"
            assert data["page_size"] > 0, "页面大小应该大于0"
            assert data["total"] >= 0, "总数应该大于等于0"
            assert data["pages"] >= 0, "总页数应该大于等于0"
            
            # 检查items数量是否合理
            if data["total"] > 0:
                expected_items = min(data["page_size"], data["total"])
                if data["page"] == data["pages"]:  # 最后一页
                    remaining_items = data["total"] % data["page_size"]
                    if remaining_items > 0:
                        expected_items = remaining_items
                
                assert len(data["items"]) <= expected_items, \
                    f"返回的items数量不合理: {len(data['items'])} > {expected_items}"
            
            logger.info(f"✅ 分页功能正常: 第{data['page']}页, 共{data['pages']}页, 总计{data['total']}条")
        
        elif response.status_code == 401:
            logger.info("✅ 分页接口需要认证（正常）")
        else:
            logger.warning(f"⚠️ 分页接口异常: 状态码 {response.status_code}")
    
    def test_data_format_consistency(self, client: TestClient):
        """测试数据格式的一致性"""
        logger.info("=== 测试数据格式一致性 ===")
        
        # 测试统计信息的数据格式
        response = client.get("/api/v1/email/personnel-sync/extid-completion/stats")
        
        if response.status_code == 200:
            stats_data = response.json()
            
            # 检查统计数据的字段类型
            numeric_fields = ["total_email_users", "users_with_extid", "users_without_extid", 
                            "completion_rate"]
            
            for field in numeric_fields:
                if field in stats_data:
                    assert isinstance(stats_data[field], (int, float)), \
                        f"统计字段 {field} 应该是数字类型"
            
            # 检查百分比字段的合理性
            if "completion_rate" in stats_data:
                assert 0 <= stats_data["completion_rate"] <= 100, \
                    f"完成率应该在0-100之间: {stats_data['completion_rate']}"
            
            logger.info("✅ 统计数据格式正确")
        
        elif response.status_code == 401:
            logger.info("✅ 统计接口需要认证（正常）")
        else:
            logger.warning(f"⚠️ 统计接口异常: 状态码 {response.status_code}")
    
    def test_api_documentation_compliance(self, client: TestClient):
        """测试API是否符合文档规范"""
        logger.info("=== 测试API文档规范符合性 ===")
        
        # 测试OpenAPI文档是否可访问
        response = client.get("/docs")
        assert response.status_code == 200, "API文档页面无法访问"
        
        # 测试OpenAPI JSON是否可访问
        response = client.get("/openapi.json")
        assert response.status_code == 200, "OpenAPI JSON无法访问"
        
        if response.status_code == 200:
            try:
                openapi_data = response.json()
                
                # 检查基本的OpenAPI结构
                assert "openapi" in openapi_data, "缺少OpenAPI版本信息"
                assert "info" in openapi_data, "缺少API信息"
                assert "paths" in openapi_data, "缺少API路径信息"
                
                # 检查是否包含人员同步相关的API
                personnel_sync_paths = [
                    path for path in openapi_data["paths"].keys()
                    if "personnel-sync" in path
                ]
                
                assert len(personnel_sync_paths) > 0, "OpenAPI文档中缺少人员同步相关的API"
                
                logger.info(f"✅ API文档正常，包含 {len(personnel_sync_paths)} 个人员同步API")
                
            except Exception as e:
                logger.warning(f"⚠️ 解析OpenAPI文档失败: {e}")
    
    def test_response_content_type(self, client: TestClient):
        """测试响应内容类型的正确性"""
        logger.info("=== 测试响应内容类型 ===")
        
        api_endpoints = [
            "/api/v1/email/personnel-sync/extid-completion/stats",
            "/api/v1/email/personnel-sync/extid-completion/matches",
            "/docs",
            "/openapi.json"
        ]
        
        for endpoint in api_endpoints:
            response = client.get(endpoint)
            
            if response.status_code == 200:
                content_type = response.headers.get("content-type", "")
                
                if endpoint.endswith(".json") or endpoint.startswith("/api/"):
                    # API接口应该返回JSON
                    assert "application/json" in content_type, \
                        f"{endpoint} 应该返回JSON格式: {content_type}"
                elif endpoint == "/docs":
                    # 文档页面应该返回HTML
                    assert "text/html" in content_type, \
                        f"{endpoint} 应该返回HTML格式: {content_type}"
                
                logger.info(f"✅ {endpoint}: {content_type}")
            
            elif response.status_code == 401:
                logger.info(f"✅ {endpoint}: 需要认证（正常）")
            else:
                logger.warning(f"⚠️ {endpoint}: 状态码 {response.status_code}")
    
    def test_error_message_clarity(self, client: TestClient):
        """测试错误消息的清晰度"""
        logger.info("=== 测试错误消息清晰度 ===")
        
        # 测试不存在的端点
        response = client.get("/api/v1/nonexistent-endpoint")
        assert response.status_code == 404, "不存在的端点应该返回404"
        
        # 测试错误的HTTP方法
        response = client.post("/api/v1/email/personnel-sync/extid-completion/stats")
        assert response.status_code in [405, 422], "错误的HTTP方法应该返回405或422"
        
        # 测试无效的JSON数据
        response = client.post(
            "/api/v1/email/personnel-sync/extid-completion/manual-match",
            json={"invalid": "data"}
        )
        
        if response.status_code in [400, 422]:
            try:
                error_data = response.json()
                # 错误消息应该包含有用信息
                assert "detail" in error_data or "message" in error_data, \
                    "错误响应应该包含详细信息"
                logger.info("✅ 错误消息包含详细信息")
            except Exception:
                logger.warning("⚠️ 无法解析错误响应")
        elif response.status_code == 401:
            logger.info("✅ 接口需要认证（正常）")
        else:
            logger.warning(f"⚠️ 意外的状态码: {response.status_code}")

if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v"])
