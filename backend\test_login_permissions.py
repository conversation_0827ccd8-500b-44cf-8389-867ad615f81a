#!/usr/bin/env python
"""
测试登录权限API的脚本
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService
from app.models.email import EmailConfig

async def test_login_permissions():
    """测试登录权限API"""
    db = SessionLocal()
    
    try:
        print("=== 测试登录权限API ===")
        
        # 1. 检查功能设置应用配置
        print("\n1. 检查功能设置应用配置...")
        config = db.query(EmailConfig).filter(
            EmailConfig.app_name == "功能设置",
            EmailConfig.is_active == True
        ).first()
        
        if not config:
            print("❌ 未找到有效的功能设置应用配置")
            return
        else:
            print(f"✅ 找到功能设置配置: ID={config.id}, corp_id={config.corp_id}")
            print(f"   corp_secret前10位: {config.corp_secret[:10]}...")
        
        # 2. 初始化API服务
        print("\n2. 初始化API服务...")
        try:
            api_service = TencentEmailAPIService(db, app_name="功能设置")
            print("✅ API服务初始化成功")
        except Exception as e:
            print(f"❌ API服务初始化失败: {str(e)}")
            return
        
        # 3. 测试获取访问令牌
        print("\n3. 测试获取访问令牌...")
        try:
            access_token = await api_service.get_access_token()
            print(f"✅ 获取访问令牌成功: {access_token[:20]}...")
        except Exception as e:
            print(f"❌ 获取访问令牌失败: {str(e)}")
            return
        
        # 4. 测试登录权限API
        print("\n4. 测试登录权限API...")
        test_userid = "<EMAIL>"  # 使用日志中的测试用户
        
        try:
            result = await api_service.get_user_option(test_userid, [1, 2, 3, 4])
            print(f"✅ 调用登录权限API成功")
            print(f"   errcode: {result.errcode}")
            print(f"   errmsg: {result.errmsg}")
            print(f"   data: {result.data}")
        except Exception as e:
            print(f"❌ 调用登录权限API失败: {str(e)}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_login_permissions()) 