"""add ldap ip range support

Revision ID: add_ldap_ip_range_support
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_ldap_ip_range_support'
down_revision = None  # 这里需要根据实际情况设置
branch_labels = None
depends_on = None


def upgrade():
    # 为ldap_config表添加新字段
    op.add_column('ldap_config', sa.Column('ip_ranges', sa.JSON(), nullable=True, comment='IP网段配置列表'))
    op.add_column('ldap_config', sa.Column('priority', sa.Integer(), nullable=False, server_default='1', comment='匹配优先级，数字越小优先级越高'))
    op.add_column('ldap_config', sa.Column('auto_select_enabled', sa.<PERSON>(), nullable=False, server_default='true', comment='是否启用基于IP的自动选择'))


def downgrade():
    # 删除添加的字段
    op.drop_column('ldap_config', 'auto_select_enabled')
    op.drop_column('ldap_config', 'priority')
    op.drop_column('ldap_config', 'ip_ranges') 