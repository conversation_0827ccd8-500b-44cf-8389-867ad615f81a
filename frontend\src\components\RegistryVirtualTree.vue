<template>
  <div class="registry-virtual-tree">
    <VirtualScrollList
      ref="virtualListRef"
      :items="flattenedItems"
      :total-items="totalItems"
      :item-height="32"
      :page-size="pageSize"
      :has-more="hasMore"
      :loading="loading"
      key-field="path"
      empty-text="暂无子键"
      :buffer-size="10"
      :preload-threshold="20"
      @load-more="handleLoadMore"
      @item-click="handleItemClick"
    >
      <template #default="{ item, index, isLoading }">
        <div 
          class="registry-tree-node"
          :style="{ paddingLeft: (item.level * 20 + 12) + 'px' }"
          :class="{
            'is-selected': item.path === selectedPath,
            'is-loading': isLoading
          }"
        >
          <!-- 展开/折叠按钮 -->
          <div 
            class="node-expand-icon"
            v-if="!isLoading && item.hasChildren"
            @click.stop="toggleExpand(item)"
            :title="item.expanded ? '折叠' : '展开'"
          >
            <el-icon :class="{ 'is-expanded': item.expanded }">
              <ArrowRight />
            </el-icon>
          </div>
          <div v-else class="node-expand-placeholder"></div>
          
          <!-- 加载指示器 -->
          <div v-if="isLoading" class="node-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
          </div>
          
          <!-- 节点图标 -->
          <el-icon v-else class="node-icon">
            <Folder v-if="item.hasChildren" />
            <Document v-else />
          </el-icon>
          
          <!-- 节点标签 -->
          <span class="node-label" :title="item.name">
            {{ item.name }}
          </span>
          
          <!-- 节点操作按钮 -->
          <div 
            v-if="!isLoading && item.path !== selectedRootKey" 
            class="node-actions"
          >
            <el-button-group class="node-btn-group">
              <el-button 
                size="small" 
                text 
                @click.stop="$emit('create-subkey', item)"
                title="创建子键"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
              <el-button 
                size="small" 
                text 
                type="danger"
                @click.stop="$emit('delete-key', item)"
                title="删除键"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
    </VirtualScrollList>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { 
  ArrowRight, Folder, Document, Plus, Delete, Loading 
} from '@element-plus/icons-vue'
import VirtualScrollList from './VirtualScrollList.vue'

// 树节点接口
interface RegistryTreeNode {
  name: string
  path: string
  level: number
  expanded: boolean
  hasChildren: boolean
  children?: RegistryTreeNode[]
  loaded: boolean
  loading: boolean
  parent?: RegistryTreeNode
}

interface Props {
  rootKey: string
  selectedPath?: string
  loading?: boolean
  pageSize?: number
}

interface Emits {
  (e: 'node-click', node: RegistryTreeNode): void
  (e: 'load-children', node: RegistryTreeNode): void
  (e: 'create-subkey', node: RegistryTreeNode): void
  (e: 'delete-key', node: RegistryTreeNode): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedPath: '',
  loading: false,
  pageSize: 100
})

const emit = defineEmits<Emits>()

// 组件引用
const virtualListRef = ref<any>()

// 树数据
const treeNodes = ref<Map<string, RegistryTreeNode>>(new Map())
const rootNode = ref<RegistryTreeNode>()

// 选中的根键
const selectedRootKey = computed(() => props.rootKey)

// 计算扁平化的树节点列表（只包含展开的可见节点）
const flattenedItems = computed(() => {
  const items: RegistryTreeNode[] = []
  
  const flatten = (node: RegistryTreeNode, level = 0) => {
    // 添加当前节点
    const nodeWithLevel = { ...node, level }
    items.push(nodeWithLevel)
    
    // 关键修复：确保正确检查展开状态和子节点
    if (node.expanded && node.children && node.children.length > 0) {
      console.log(`展开节点 ${node.name} (${node.path})，处理 ${node.children.length} 个子节点`)
      for (const child of node.children) {
        // 重要：从Map中获取最新的子节点数据
        const latestChild = treeNodes.value.get(child.path) || child
        flatten(latestChild, level + 1)
      }
    }
  }
  
  if (rootNode.value) {
    // 重要：从Map中获取最新的根节点数据
    const latestRootNode = treeNodes.value.get(rootNode.value.path) || rootNode.value
    flatten(latestRootNode)
  }
  
  console.log('flattenedItems 重新计算:', items.length, '个可见节点')
  console.log('根节点状态:', rootNode.value?.expanded, '子节点数:', rootNode.value?.children?.length)
  
  // 调试信息：显示前几个节点的详细信息
  items.slice(0, 5).forEach((item, index) => {
    console.log(`  节点${index}: ${item.name} (${item.path}) - level:${item.level}, expanded:${item.expanded}`)
  })
  
  return items
})

// 总项目数（包括未加载的）
const totalItems = computed(() => {
  return flattenedItems.value.length
})

// 是否还有更多数据
const hasMore = computed(() => {
  return flattenedItems.value.some(item => item.hasChildren && !item.loaded && !item.loading)
})

// 初始化根节点
const initializeRootNode = () => {
  if (!selectedRootKey.value) return
  
  console.log('初始化根节点:', selectedRootKey.value)
  
  const root: RegistryTreeNode = {
    name: selectedRootKey.value,
    path: selectedRootKey.value,
    level: 0,
    expanded: true,  // 关键修复：确保根节点默认展开
    hasChildren: true,
    children: [],
    loaded: false,
    loading: false
  }
  
  treeNodes.value.clear()
  treeNodes.value.set(selectedRootKey.value, root)
  rootNode.value = root
  
  console.log('根节点创建完成, 展开状态:', root.expanded, '路径:', root.path)
  
  // 自动加载根节点的子节点
  if (!root.loaded && !root.loading) {
    console.log('开始加载根节点子键')
    loadNodeChildren(root)
  }
}

// 加载节点的子节点
const loadNodeChildren = async (node: RegistryTreeNode) => {
  if (node.loaded || node.loading) {
    console.log('loadNodeChildren 跳过:', node.path, '已加载:', node.loaded, '加载中:', node.loading)
    return
  }
  
  console.log('loadNodeChildren 开始:', node.path)
  
  // 关键修复：正确更新loading状态
  const mapNode = treeNodes.value.get(node.path)
  if (mapNode) {
    const updatedNode = { 
      ...mapNode, 
      loading: true 
    }
    
    // 更新Map中的节点
    treeNodes.value.set(node.path, updatedNode)
    
    // 如果是根节点，同时更新rootNode引用
    if (node.path === selectedRootKey.value) {
      rootNode.value = updatedNode
    }
    
    // 强制触发Map响应式更新
    treeNodes.value = new Map(treeNodes.value)
    
    console.log('节点loading状态已设置:', node.path)
  }
  
  // 触发父组件的加载事件
  emit('load-children', node)
}

// 设置节点的子节点数据
const setNodeChildren = (nodePath: string, children: string[], hasMore = false) => {
  const node = treeNodes.value.get(nodePath)
  if (!node) {
    console.warn('setNodeChildren: 未找到节点', nodePath)
    return
  }
  
  console.log('setNodeChildren:', nodePath, '子键数量:', children.length, '是否有更多:', hasMore)
  
  // 创建子节点 - 修复路径生成逻辑
  const childNodes: RegistryTreeNode[] = children.map(childName => {
    // 重要修复：正确生成子键的完整路径
    const childPath = `${nodePath}\\${childName}`
    
    console.log('创建子节点:', childName, '路径:', childPath, '父节点路径:', nodePath)
    
    let childNode = treeNodes.value.get(childPath)
    if (!childNode) {
      childNode = {
        name: childName,
        path: childPath,
        level: node.level + 1,
        expanded: false,
        hasChildren: true, // 默认假设有子节点，在首次展开时会检查
        children: [],
        loaded: false,
        loading: false,
        parent: node
      }
      treeNodes.value.set(childPath, childNode)
    }
    
    return childNode
  })
  
  // 更新节点属性
  const updatedNode = {
    ...node,
    children: childNodes,
    loaded: true,
    loading: false,
    hasChildren: childNodes.length > 0 || hasMore,
    // 如果是根节点，确保保持展开状态
    expanded: node.path === selectedRootKey.value ? true : node.expanded
  }
  
  // 更新Map中的节点
  treeNodes.value.set(nodePath, updatedNode)
  
  // 如果是根节点，同时更新rootNode引用
  if (node.path === selectedRootKey.value) {
    rootNode.value = updatedNode
  }
  
  // 关键修复：强制触发Vue的响应式更新
  treeNodes.value = new Map(treeNodes.value)
  
  console.log('setNodeChildren 完成:', {
    nodePath: updatedNode.path,
    nodeName: updatedNode.name,
    expanded: updatedNode.expanded,
    childrenCount: updatedNode.children?.length || 0,
    hasMore,
    childrenPaths: updatedNode.children?.map(c => c.path) || []
  })
  
  // 验证子节点是否正确添加到Map中
  console.log('验证子节点Map状态:')
  childNodes.forEach(child => {
    const mapChild = treeNodes.value.get(child.path)
    console.log(`  ${child.name} (${child.path}):`, mapChild ? '✓存在' : '✗缺失')
  })
  
  // 强制触发nextTick确保DOM更新
  nextTick(() => {
    console.log('nextTick后验证 - flattenedItems长度:', flattenedItems.value.length)
    console.log('nextTick后验证 - 根节点展开状态:', rootNode.value?.expanded)
    console.log('nextTick后验证 - 根节点子节点数:', rootNode.value?.children?.length)
  })
}

// 当节点加载完成后检查其子节点的hasChildren状态
const updateChildrenHasChildrenStatus = (parentNode: RegistryTreeNode) => {
  if (!parentNode.children) return
  
  // 为每个子节点异步检查是否有子键
  parentNode.children.forEach(async (childNode) => {
    if (!childNode.loaded) {
      // 简单的检查方法：如果子节点没有被加载过，保持hasChildren为true
      // 只有在尝试展开时才会真正检查
      return
    }
    
    // 如果已经加载过且没有子节点，更新状态
    if (childNode.loaded && (!childNode.children || childNode.children.length === 0)) {
      childNode.hasChildren = false
    }
  })
}

// 处理节点展开/折叠
const toggleExpand = (clickedNode: RegistryTreeNode) => {
  console.log('toggleExpand 开始:', {
    path: clickedNode.path,
    name: clickedNode.name,
    currentExpanded: clickedNode.expanded,
    loaded: clickedNode.loaded,
    loading: clickedNode.loading,
    hasChildren: clickedNode.hasChildren
  })
  
  // 重要：从Map中获取最新的节点数据，而不是使用传入的节点对象
  const mapNode = treeNodes.value.get(clickedNode.path)
  if (!mapNode) {
    console.error('toggleExpand: 未找到节点在Map中:', clickedNode.path)
    return
  }
  
  // 切换展开状态
  const newExpanded = !mapNode.expanded
  
  // 创建更新后的节点对象
  const updatedNode = { 
    ...mapNode,
    expanded: newExpanded
  }
  
  // 更新Map中的节点
  treeNodes.value.set(clickedNode.path, updatedNode)
  
  // 如果是根节点，同时更新rootNode引用
  if (clickedNode.path === selectedRootKey.value) {
    rootNode.value = updatedNode
    console.log('更新根节点引用:', {
      path: updatedNode.path,
      expanded: updatedNode.expanded,
      childrenCount: updatedNode.children?.length || 0,
      loaded: updatedNode.loaded
    })
  }
  
  // 关键修复：强制触发Map的响应式更新
  treeNodes.value = new Map(treeNodes.value)
  
  console.log('展开状态已更新:', {
    path: clickedNode.path,
    name: clickedNode.name, 
    newExpanded,
    loaded: updatedNode.loaded,
    loading: updatedNode.loading,
    shouldLoadChildren: newExpanded && !updatedNode.loaded && !updatedNode.loading
  })
  
  // 如果展开且未加载，开始加载子节点
  if (newExpanded && !updatedNode.loaded && !updatedNode.loading) {
    console.log('触发子节点加载:', clickedNode.path)
    loadNodeChildren(updatedNode)
  }
  
  // 关键修复：强制触发nextTick确保界面更新
  nextTick(() => {
    console.log('toggleExpand后 - flattenedItems长度:', flattenedItems.value.length)
    console.log('toggleExpand后 - 当前节点状态:', {
      path: clickedNode.path,
      mapNodeExpanded: treeNodes.value.get(clickedNode.path)?.expanded,
      rootNodeExpanded: rootNode.value?.expanded
    })
    
    // 额外验证：检查展开后子节点是否可见
    const expandedNode = treeNodes.value.get(clickedNode.path)
    if (expandedNode?.expanded && expandedNode?.children) {
      console.log('展开后子节点验证:', expandedNode.children.map(c => ({
        name: c.name,
        path: c.path,
        inFlattenedItems: flattenedItems.value.some(item => item.path === c.path)
      })))
    }
  })
}

// 处理节点点击
const handleItemClick = (item: RegistryTreeNode, index: number) => {
  emit('node-click', item)
}

// 处理加载更多
const handleLoadMore = () => {
  const nodeToLoad = flattenedItems.value.find(item => 
    item.hasChildren && !item.loaded && !item.loading && item.expanded
  )
  
  if (nodeToLoad) {
    loadNodeChildren(nodeToLoad)
  }
}

// 展开到指定路径
const expandToPath = async (targetPath: string) => {
  if (!targetPath || targetPath === selectedRootKey.value) return
  
  const pathParts = targetPath.split('\\')
  const rootKey = pathParts[0]
  
  if (rootKey !== selectedRootKey.value) {
    return false
  }
  
  let currentPath = rootKey
  let currentNode = rootNode.value
  
  if (!currentNode) return false
  
  // 逐级展开路径
  for (let i = 1; i < pathParts.length; i++) {
    const partName = pathParts[i]
    currentPath = `${currentPath}\\${partName}`
    
    // 确保当前节点已展开并加载
    if (!currentNode?.expanded) {
      currentNode.expanded = true
    }
    
    if (!currentNode?.loaded) {
      await new Promise(resolve => {
        const unwatch = watch(() => currentNode!.loaded, (loaded) => {
          if (loaded) {
            unwatch()
            resolve(void 0)
          }
        })
        loadNodeChildren(currentNode!)
      })
    }
    
    // 查找子节点
    const childNode: RegistryTreeNode | undefined = currentNode?.children?.find(child => child.name === partName)
    if (!childNode) {
      return false // 路径不存在
    }
    
    currentNode = childNode
  }
  
  return true
}

// 刷新节点
const refreshNode = (nodePath: string) => {
  const node = treeNodes.value.get(nodePath)
  if (node) {
    node.loaded = false
    node.children = []
    if (node.expanded) {
      loadNodeChildren(node)
    }
  }
}

// 添加新子节点
const addChildNode = (parentPath: string, childName: string) => {
  const parentNode = treeNodes.value.get(parentPath)
  if (!parentNode) return
  
  const childPath = parentPath === selectedRootKey.value ? childName : `${parentPath}\\${childName}`
  const childNode: RegistryTreeNode = {
    name: childName,
    path: childPath,
    level: parentNode.level + 1,
    expanded: false,
    hasChildren: true,
    children: [],
    loaded: false,
    loading: false,
    parent: parentNode
  }
  
  if (!parentNode.children) {
    parentNode.children = []
  }
  
  parentNode.children.push(childNode)
  parentNode.children.sort((a, b) => a.name.localeCompare(b.name))
  treeNodes.value.set(childPath, childNode)
}

// 删除节点
const removeNode = (nodePath: string) => {
  const node = treeNodes.value.get(nodePath)
  if (!node || !node.parent) return
  
  const parent = node.parent
  if (parent.children) {
    const index = parent.children.findIndex(child => child.path === nodePath)
    if (index > -1) {
      parent.children.splice(index, 1)
    }
  }
  
  // 递归删除子节点
  const deleteRecursive = (nodeToDelete: RegistryTreeNode) => {
    if (nodeToDelete.children) {
      nodeToDelete.children.forEach(deleteRecursive)
    }
    treeNodes.value.delete(nodeToDelete.path)
  }
  
  deleteRecursive(node)
}

// 监听根键变化
watch(() => props.rootKey, () => {
  initializeRootNode()
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
  setNodeChildren,
  refreshNode,
  addChildNode,
  removeNode,
  expandToPath,
  virtualListRef
})
</script>

<style scoped>
.registry-virtual-tree {
  height: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
}

.registry-tree-node {
  display: flex;
  align-items: center;
  height: 32px;
  padding-right: 8px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.registry-tree-node:hover {
  background-color: var(--el-fill-color-light);
}

.registry-tree-node.is-selected {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.registry-tree-node.is-loading {
  color: var(--el-text-color-secondary);
  cursor: default;
}

.node-expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s;
  margin-right: 4px;
}

.node-expand-icon .el-icon.is-expanded {
  transform: rotate(90deg);
}

.node-expand-placeholder {
  width: 20px;
  height: 16px;
}

.node-loading {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.node-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
  margin-left: 8px;
}

.registry-tree-node:hover .node-actions {
  opacity: 1;
}

.node-btn-group {
  display: flex;
  gap: 2px;
}

.node-btn-group .el-button {
  padding: 2px 4px;
  margin: 0;
  min-height: auto;
}
</style> 