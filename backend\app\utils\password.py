"""
密码散列工具模块

使用 bcrypt 直接处理密码散列和验证，替代 passlib
"""
import bcrypt


def hash_password(password: str) -> str:
    """
    使用 bcrypt 散列密码
    
    Args:
        password: 明文密码
        
    Returns:
        str: bcrypt 散列后的密码
    """
    # 将密码编码为字节
    password_bytes = password.encode('utf-8')
    
    # 生成盐并散列
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password_bytes, salt)
    
    # 返回字符串格式
    return hashed.decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 散列后的密码
        
    Returns:
        bool: 验证结果
    """
    # 将密码编码为字节
    password_bytes = plain_password.encode('utf-8')
    hashed_bytes = hashed_password.encode('utf-8')
    
    # 验证密码
    return bcrypt.checkpw(password_bytes, hashed_bytes) 