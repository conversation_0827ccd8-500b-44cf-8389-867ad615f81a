import request from '@/utils/request'

// 获取标签列表
export const getTags = (params?: { 
  page?: number
  size?: number
}) => {
  return request.get('/email/tags', { params })
}

// 获取标签详情
export const getTag = (tagid: number) => {
  return request.get(`/email/tags/${tagid}`)
}

// 创建标签
export const createTag = (data: {
  tagid: number
  tagname: string
}, options?: { sync_to_api?: boolean }) => {
  return request.post('/email/tags', data, {
    params: options
  })
}

// 更新标签
export const updateTag = (tagid: number, data: {
  tagname?: string
}, options?: { sync_to_api?: boolean }) => {
  return request.put(`/email/tags/${tagid}`, data, {
    params: options
  })
}

// 删除标签
export const deleteTag = (tagid: number, options?: { sync_to_api?: boolean }) => {
  return request.delete(`/email/tags/${tagid}`, {
    params: options
  })
}

// 从API同步标签数据
export const syncTagsFromApi = () => {
  return request.post('/email/sync/tags')
} 