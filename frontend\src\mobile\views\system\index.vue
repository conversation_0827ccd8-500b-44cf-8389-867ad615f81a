<template>
  <div class="mobile-system-index">
    <!-- 功能菜单 -->
    <van-grid :column-num="2" :gutter="16">
      <van-grid-item
        icon="manager-o"
        text="用户管理"
        @click="goToUserManagement"
      />
      <van-grid-item
        icon="setting-o"
        text="系统设置"
        @click="goToSettings"
      />
      <van-grid-item
        icon="shield-o"
        text="权限管理"
        @click="goToPermissions"
      />
      <van-grid-item
        icon="chat-o"
        text="日志管理"
        @click="goToLogs"
      />
    </van-grid>
    
    <!-- 用户统计 -->
    <van-cell-group title="用户统计" inset>
      <van-cell title="总用户数" :value="userStats.totalUsers" />
      <van-cell title="活跃用户" :value="userStats.activeUsers" />
      <van-cell title="管理员" :value="userStats.adminUsers" />
      <van-cell title="普通用户" :value="userStats.normalUsers" />
    </van-cell-group>
    
    <!-- 系统状态 -->
    <van-cell-group title="系统状态" inset>
      <van-cell title="系统版本" :value="systemInfo.version" />
      <van-cell title="运行时间" :value="systemInfo.uptime" />
      <van-cell title="数据库状态" :value="systemInfo.dbStatus" />
      <van-cell title="缓存状态" :value="systemInfo.cacheStatus" />
    </van-cell-group>
    
    <!-- 快速操作 -->
    <van-cell-group title="快速操作" inset>
      <van-cell title="备份数据" is-link @click="backupData" />
      <van-cell title="清理缓存" is-link @click="clearCache" />
      <van-cell title="系统监控" is-link @click="goToMonitor" />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'

const router = useRouter()

// 用户统计
const userStats = ref({
  totalUsers: '--',
  activeUsers: '--',
  adminUsers: '--',
  normalUsers: '--'
})

// 系统信息
const systemInfo = ref({
  version: '--',
  uptime: '--',
  dbStatus: '--',
  cacheStatus: '--'
})

// 页面跳转
const goToUserManagement = () => {
  router.push('/m/system/user')
}

const goToSettings = () => {
  showToast('系统设置开发中')
}

const goToPermissions = () => {
  showToast('权限管理开发中')
}

const goToLogs = () => {
  showToast('日志管理开发中')
}

const goToMonitor = () => {
  showToast('系统监控开发中')
}

// 备份数据
const backupData = async () => {
  const loading = showLoadingToast({
    message: '备份中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    closeToast()
    showToast('备份完成')
  } catch (error) {
    closeToast()
    showToast('备份失败')
  }
}

// 清理缓存
const clearCache = async () => {
  const loading = showLoadingToast({
    message: '清理中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    closeToast()
    showToast('缓存清理完成')
  } catch (error) {
    closeToast()
    showToast('清理失败')
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 模拟加载统计数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    userStats.value = {
      totalUsers: '45',
      activeUsers: '38',
      adminUsers: '5',
      normalUsers: '40'
    }
    systemInfo.value = {
      version: 'v2.1.0',
      uptime: '7天12小时',
      dbStatus: '正常',
      cacheStatus: '正常'
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.mobile-system-index {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .van-grid {
    margin-bottom: 16px;
  }
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}
</style> 