<template>
  <div class="tag-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><CollectionTag /></el-icon>
        <h2 class="page-title">标签管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>标签管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 主要内容 -->
    <el-card class="main-card">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <Authority :value="['email:tag:create']">
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新增标签
            </el-button>
          </Authority>
          <Authority :value="['email:tag:sync']">
            <el-button type="success" @click="handleSync" :loading="syncLoading">
              <el-icon><Refresh /></el-icon>
              同步标签
            </el-button>
          </Authority>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索标签名称"
            style="width: 250px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 标签表格 -->
      <el-table
        v-loading="loading"
        :data="filteredTagList"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="tagid" label="标签ID" width="120" />
        <el-table-column prop="tagname" label="标签名称" min-width="200" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <Authority :value="['email:tag:view']">
              <el-button type="primary" size="small" @click.stop="handleView(row)">
                查看
              </el-button>
            </Authority>
            <Authority :value="['email:tag:update']">
              <el-button type="warning" size="small" @click.stop="handleEdit(row)">
                编辑
              </el-button>
            </Authority>
            <Authority :value="['email:tag:delete']">
              <el-button type="danger" size="small" @click.stop="handleDelete(row)">
                删除
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="标签ID" prop="tagid">
          <el-input-number
            v-model="form.tagid"
            placeholder="请输入标签ID"
            :disabled="isEdit"
            style="width: 100%"
            :min="1"
          />
        </el-form-item>
        <el-form-item label="标签名称" prop="tagname">
          <el-input v-model="form.tagname" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="form.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="标签详情"
      width="500px"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="标签ID">{{ viewData.tagid }}</el-descriptions-item>
        <el-descriptions-item label="标签名称">{{ viewData.tagname }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.is_active ? 'success' : 'danger'">
            {{ viewData.is_active ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(viewData.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDateTime(viewData.updated_at) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CollectionTag, Plus, Refresh, Search } from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'
import { getTags, createTag, updateTag, deleteTag, syncTagsFromApi } from '@/api/email/tag'
import { formatDateTime } from '@/utils/date'
import type { EmailTag, EmailTagForm } from '@/types/email'

// 响应式数据
const loading = ref(false)
const syncLoading = ref(false)
const submitLoading = ref(false)
const tagList = ref<EmailTag[]>([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const searchKeyword = ref('')

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  tagid: null,
  tagname: '',
  is_active: true
})

// 查看数据
const viewData = ref<EmailTag>({} as EmailTag)

// 表单验证规则
const formRules = {
  tagid: [
    { required: true, message: '请输入标签ID', trigger: 'blur' }
  ],
  tagname: [
    { required: true, message: '请输入标签名称', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑标签' : '新增标签'
})

// 过滤后的标签列表
const filteredTagList = computed(() => {
  if (!searchKeyword.value) {
    return tagList.value
  }
  return tagList.value.filter((tag: any) => 
    tag.tagname.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 获取标签列表
const getTagList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size
    }
    const response = await getTags(params)
    tagList.value = response.data.items || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取标签列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  // 前端搜索，不需要重新请求
}

// 新增
const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑
const handleEdit = (row: any) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, row)
}

// 查看
const handleView = (row: any) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签 "${row.tagname}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteTag(row.tagid)
    ElMessage.success('删除成功')
    getTagList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 同步标签
const handleSync = async () => {
  try {
    syncLoading.value = true
    await syncTagsFromApi()
    ElMessage.success('同步成功')
    getTagList()
  } catch (error) {
    ElMessage.error('同步失败')
  } finally {
    syncLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value && form.tagid !== null) {
      await updateTag(form.tagid, form)
      ElMessage.success('更新成功')
    } else if (!isEdit.value) {
      await createTag({
        tagid: form.tagid || 0,
        tagname: form.tagname
      })
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    getTagList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    tagid: null,
    tagname: '',
    is_active: true
  })
  formRef.value?.clearValidate()
}

// 对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 行点击
const handleRowClick = (row: any) => {
  handleView(row)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  getTagList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getTagList()
}

// 初始化
onMounted(() => {
  getTagList()
})
</script>

<style scoped>
.tag-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 24px;
  color: #409eff;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.main-card {
  min-height: calc(100vh - 200px);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table) {
  .el-table__row {
    cursor: pointer;
  }
  
  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}
</style> 