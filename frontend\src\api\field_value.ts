import request from '@/utils/request'
import type { FieldValue, FieldValueCreate, FieldValueUpdate } from '@/types/field_value'

export interface FieldValueListResponse {
  data: FieldValue[]
  total: number
  page: number
  limit: number
}

export const fieldValueApi = {
  // 获取字段值列表
  getFieldValues: (params: {
    field_name?: string
    keyword?: string
    skip?: number
    limit?: number
  }) => {
    return request.get<FieldValueListResponse>('/field-values', { params })
  },

  // 获取单个字段值
  getFieldValue: (id: number) => {
    return request.get<FieldValue>(`/field-values/${id}`)
  },

  // 创建字段值
  createFieldValue: (data: FieldValueCreate) => {
    return request.post<FieldValue>('/field-values', data)
  },

  // 更新字段值
  updateFieldValue: (id: number, data: FieldValueUpdate) => {
    return request.put<FieldValue>(`/field-values/${id}`, data)
  },

  // 删除字段值
  deleteFieldValue: (id: number) => {
    return request.delete<FieldValue>(`/field-values/${id}`)
  }
} 