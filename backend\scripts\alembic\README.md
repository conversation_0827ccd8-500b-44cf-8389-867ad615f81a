# Alembic数据库迁移工具脚本

这个目录包含处理Alembic数据库迁移问题的实用工具脚本。

## 可用脚本

### check_alembic.py
检查数据库中当前的alembic版本情况。该脚本会连接到SQLite数据库，并显示alembic_version表中的内容。

使用方法:
```bash
python scripts/alembic/check_alembic.py
```

### fix_alembic.py
修复数据库中错误的alembic版本号。如果发现无效版本号，会将其更新为有效的头版本。

使用方法:
```bash
python scripts/alembic/fix_alembic.py
```

### fix_migration_names.py
检查并修复不规范的迁移文件命名。该脚本会扫描alembic/versions目录中的迁移文件，
找出命名不规范的文件并提供重命名选项。

使用方法:
```bash
python scripts/alembic/fix_migration_names.py
```

## 注意事项

- 执行修复脚本前，建议先备份数据库和迁移文件
- 重命名迁移文件可能会影响数据库迁移历史，请谨慎操作
- 对于中文命名的迁移文件，建议手动重命名为英文 