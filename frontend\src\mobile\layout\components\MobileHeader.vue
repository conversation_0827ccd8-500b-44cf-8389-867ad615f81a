<template>
  <van-nav-bar
    :title="title"
    :left-arrow="showBack"
    :fixed="true"
    :safe-area-inset-top="true"
    @click-left="handleBack"
  >
    <template #right>
      <van-icon name="ellipsis" @click="showMore" />
    </template>
  </van-nav-bar>
</template>

<script setup lang="ts">
import { showNotify } from 'vant'

interface Props {
  title: string
  showBack?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showBack: false
})

const emit = defineEmits<{
  back: []
}>()

const handleBack = () => {
  emit('back')
}

const showMore = () => {
  showNotify('更多功能开发中...')
}
</script>

<style lang="scss" scoped>
.van-nav-bar {
  --van-nav-bar-background: #fff;
  --van-nav-bar-height: var(--mobile-header-height, 50px);
  z-index: 1000; // 确保头部在最上层
}
</style> 