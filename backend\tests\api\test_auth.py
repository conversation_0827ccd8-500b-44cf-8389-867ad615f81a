import requests
import json
import sys

def test_auth():
    # 1. 尝试登录获取token
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123",
        "remember": "false"
    }
    
    try:
        login_response = requests.post(
            login_url, 
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data.get("access_token")
            print(f"登录成功，获取到token: {token[:15]}...")
            
            # 测试认证的API接口
            headers = {"Authorization": f"Bearer {token}"}
            
            # 测试/users/me接口
            me_url = "http://localhost:8000/api/v1/users/me"
            me_response = requests.get(me_url, headers=headers)
            print(f"用户信息API: {me_response.status_code}")
            if me_response.status_code == 200:
                user_data = me_response.json()
                print(f"  用户: {user_data.get('username')}")
                print(f"  是否超级管理员: {user_data.get('is_superuser')}")
            
            # 测试系统权限模块API
            modules_url = "http://localhost:8000/api/v1/system/permissions/modules"
            modules_response = requests.get(modules_url, headers=headers)
            print(f"权限模块API: {modules_response.status_code}")
            if modules_response.status_code == 200:
                print(f"  模块列表: {modules_response.json()}")
            else:
                print(f"  错误信息: {modules_response.text}")
            
            # 测试系统角色API
            roles_url = "http://localhost:8000/api/v1/system/roles/"
            roles_response = requests.get(roles_url, headers=headers)
            print(f"角色列表API: {roles_response.status_code}")
            if roles_response.status_code == 200:
                print(f"  角色数量: {len(roles_response.json())}")
            else:
                print(f"  错误信息: {roles_response.text}")
        else:
            print(f"登录失败: {login_response.status_code}")
            print(login_response.text)
    
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    test_auth() 