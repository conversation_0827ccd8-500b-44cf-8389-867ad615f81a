"""
人员邮箱同步服务
核心同步逻辑和流程控制
数据映射和格式转换
腾讯API操作（创建/更新/禁用用户）
数据验证和一致性检查
"""

import logging
import time
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import asyncio

from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember, EmailSyncLog, EmailCreationRequest, PersonnelSyncConfig, EmailDepartment
from app.services.email_api import TencentEmailAPIService
from app.services.personnel_change_detector import PersonnelChangeDetector
from app.crud.email import email_member, email_department
from app.schemas.email_personnel_sync import (
    PersonnelChangeType,
    PersonnelChangeRecord,
    PersonnelSyncResult,
    PersonnelSyncStats,
    PersonnelSyncOperationResult
)
from app.schemas.email import EmailMemberCreate, EmailMemberUpdate, EmailDepartmentCreate

logger = logging.getLogger(__name__)


class PersonnelEmailSyncService:
    """人员邮箱同步服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        self.change_detector = PersonnelChangeDetector(db)
        # 部门映射缓存，避免重复查询和创建
        self.dept_name_to_id_cache = {}
        # 新增：部门操作统计跟踪
        self.department_stats = {
            'created': [],  # 创建的部门列表
            'updated': [],  # 更新的部门列表  
            'failed': [],   # 创建失败的部门列表
            'cached': []    # 缓存命中的部门列表
        }
    
    async def _ensure_department_exists(self, dept_name: str, auto_create: bool = True) -> str:
        """
        确保部门在企业邮箱中存在，如果不存在则根据配置决定是否创建
        
        Args:
            dept_name: 部门名称
            auto_create: 是否自动创建不存在的部门
            
        Returns:
            str: 企业邮箱中的部门ID
        """
        if not dept_name or dept_name.strip() == "":
            logger.warning("部门名称为空，使用默认部门")
            return "1"
        
        dept_name = dept_name.strip()
        
        # 检查缓存
        if dept_name in self.dept_name_to_id_cache:
            # 记录缓存命中
            if dept_name not in [item['name'] for item in self.department_stats['cached']]:
                self.department_stats['cached'].append({
                    'name': dept_name,
                    'dept_id': self.dept_name_to_id_cache[dept_name]
                })
            return self.dept_name_to_id_cache[dept_name]
        
        # 先从本地数据库查找是否已存在映射
        existing_dept = email_department.get_by_name(self.db, name=dept_name)
        if existing_dept:
            self.dept_name_to_id_cache[dept_name] = existing_dept.dept_id
            logger.info(f"找到现有部门映射: {dept_name} -> {existing_dept.dept_id}")
            # 记录为缓存命中（已存在的部门）
            self.department_stats['cached'].append({
                'name': dept_name,
                'dept_id': existing_dept.dept_id
            })
            return existing_dept.dept_id
        
        # 本地不存在，检查是否允许自动创建
        if not auto_create:
            logger.warning(f"部门 {dept_name} 不存在且未启用自动创建，使用默认部门")
            self.dept_name_to_id_cache[dept_name] = "1"
            # 记录为失败（因为配置不允许创建）
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': '未启用自动创建部门'
            })
            return "1"
        
        # 需要在企业邮箱中创建部门
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 根据腾讯企业邮箱API文档，创建部门的正确参数格式
                api_data = {
                    "name": dept_name,      # 部门名称（必需）
                    "parentid": 1,          # 父部门id，1表示根部门（必需）
                    "order": 1              # 排序值（可选）
                }
                
                if retry_count > 0:
                    logger.info(f"正在重试创建部门 {dept_name} (第 {retry_count + 1} 次尝试)")
                else:
                    logger.info(f"正在企业邮箱中创建部门: {dept_name}, 参数: {api_data}")
                    
                result = await self.api_service.create_department(api_data)
                
                if result.errcode != 0:
                    error_msg = f"企业邮箱创建部门失败: {result.errmsg} (错误码: {result.errcode})"
                    logger.error(error_msg)
                    
                    # 根据错误码提供更详细的错误信息
                    if result.errcode == -1:
                        error_detail = "参数格式错误，请检查部门名称是否符合规范"
                    elif result.errcode == 40001:
                        error_detail = "access_token无效或已过期"
                        # 如果是token问题，强制刷新token后重试
                        if retry_count < max_retries:
                            logger.info("access_token可能已过期，强制刷新token后重试")
                            # 清除缓存的token，下次请求时会重新获取
                            self.api_service._access_token = None
                            self.api_service._token_expires_at = None
                            retry_count += 1
                            continue
                    elif result.errcode == 40003:
                        error_detail = "请求参数不正确"
                    elif result.errcode == 60008:
                        error_detail = "部门名称已存在"
                        # 如果部门已存在，尝试搜索获取部门ID
                        logger.info(f"部门 {dept_name} 已存在，尝试获取部门ID")
                        search_result = await self.api_service.search_department(dept_name, fuzzy=False)
                        if search_result.errcode == 0 and search_result.data.get('department'):
                            departments = search_result.data['department']
                            for dept in departments:
                                if dept.get('name') == dept_name:
                                    existing_dept_id = str(dept.get('id', ''))
                                    if existing_dept_id:
                                        logger.info(f"找到已存在的部门: {dept_name} -> {existing_dept_id}")
                                        # 更新本地数据库记录
                                        dept_create = EmailDepartmentCreate(
                                            dept_id=existing_dept_id,
                                            name=dept_name,
                                            order=1
                                        )
                                        local_dept = email_department.create(self.db, obj_in=dept_create)
                                        # 更新缓存
                                        self.dept_name_to_id_cache[dept_name] = existing_dept_id
                                        # 记录为已存在但更新的部门
                                        self.department_stats['updated'].append({
                                            'name': dept_name,
                                            'dept_id': existing_dept_id
                                        })
                                        return existing_dept_id
                                    break
                    else:
                        error_detail = f"未知错误: {result.errmsg}"
                    
                    logger.error(f"详细错误信息: {error_detail}")
                    
                    # 对于非重试类错误，直接返回失败
                    if result.errcode != 40001:  # 不是token错误
                        break
                        
                else:
                    # 成功创建部门
                    break
                    
            except Exception as e:
                error_msg = f"创建部门 {dept_name} 时发生异常 (第 {retry_count + 1} 次尝试): {str(e)}"
                logger.error(error_msg, exc_info=True)
                
                # 如果还有重试机会，继续重试
                if retry_count < max_retries:
                    retry_count += 1
                    logger.info(f"将在短暂等待后重试创建部门 {dept_name}")
                    await asyncio.sleep(1)  # 等待1秒后重试
                    continue
                else:
                    # 重试次数用尽，记录失败并返回默认部门
                    self.dept_name_to_id_cache[dept_name] = "1"
                    self.department_stats['failed'].append({
                        'name': dept_name,
                        'reason': f'创建时发生异常: {str(e)} (重试 {max_retries} 次后失败)'
                    })
                    return "1"
                    
            retry_count += 1
        
        # 如果到这里说明所有重试都失败了
        if result.errcode != 0:
            # 创建失败，使用默认部门
            logger.warning(f"部门 {dept_name} 创建失败（重试 {max_retries} 次后），使用默认部门")
            self.dept_name_to_id_cache[dept_name] = "1"
            # 记录创建失败
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': f'API创建失败: {result.errmsg} ({error_detail})'
            })
            return "1"
        
        # 成功创建部门，处理响应
        try:
            # 从API响应中获取新创建的部门ID
            new_dept_id = str(result.data.get('id', ''))
            if not new_dept_id:
                logger.error(f"API响应中未包含部门ID: {result.data}")
                # 如果没有返回ID，尝试通过部门名称查找
                search_result = await self.api_service.search_department(dept_name, fuzzy=False)
                if search_result.errcode == 0 and search_result.data.get('department'):
                    departments = search_result.data['department']
                    # 查找完全匹配的部门
                    for dept in departments:
                        if dept.get('name') == dept_name:
                            new_dept_id = str(dept.get('id', ''))
                            logger.info(f"通过搜索找到新创建的部门ID: {new_dept_id}")
                            break
                
                if not new_dept_id:
                    logger.error(f"无法获取新创建部门的ID，使用默认部门")
                    self.dept_name_to_id_cache[dept_name] = "1"
                    self.department_stats['failed'].append({
                        'name': dept_name,
                        'reason': 'API响应中未包含部门ID'
                    })
                    return "1"
            
            # 在本地数据库中创建部门记录
            dept_create = EmailDepartmentCreate(
                dept_id=new_dept_id,
                name=dept_name,
                order=1
            )
            local_dept = email_department.create(self.db, obj_in=dept_create)
            
            # 更新缓存
            self.dept_name_to_id_cache[dept_name] = new_dept_id
            
            # 记录成功创建
            self.department_stats['created'].append({
                'name': dept_name,
                'dept_id': new_dept_id
            })
            
            logger.info(f"成功创建部门: {dept_name} -> {new_dept_id}")
            return new_dept_id
            
        except Exception as e:
            error_msg = f"处理新创建部门 {dept_name} 的响应时发生异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            # 发生错误时使用默认部门
            self.dept_name_to_id_cache[dept_name] = "1"
            # 记录创建失败
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': f'处理响应时发生异常: {str(e)}'
            })
            return "1"
    
    async def _ensure_department_hierarchy_exists(self, personnel_data: dict, auto_create: bool = True) -> str:
        """
        根据人员数据确保完整的部门层级结构存在，按层级顺序创建部门
        
        Args:
            personnel_data: 人员数据，包含dept_hierarchy、company_name等信息
            auto_create: 是否自动创建不存在的部门
            
        Returns:
            str: 最终部门的企业邮箱ID
        """
        # 获取部门层级信息
        dept_hierarchy = personnel_data.get('dept_hierarchy', '')
        company_name = personnel_data.get('company_name', '')
        dept_name = personnel_data.get('dept_name', '')
        
        if not dept_hierarchy or not dept_name:
            logger.warning(f"人员 {personnel_data.get('user_name', 'Unknown')} 缺少部门层级信息，使用默认部门")
            return "1"
        
        # 解析部门层级: "重庆至信实业股份有限公司（重庆至信） > 总经理办公室N0 > 设备设施部"
        hierarchy_parts = [part.strip() for part in dept_hierarchy.split(' > ')]
        
        if not hierarchy_parts:
            logger.warning(f"无法解析部门层级 {dept_hierarchy}，使用默认部门")
            return "1"
        
        # 第一部分应该是公司名称
        company_dept_name = hierarchy_parts[0]
        
        # 确保公司部门存在（作为根部门的直接子部门）
        company_dept_id = await self._ensure_single_department_exists(
            company_dept_name, "1", auto_create
        )
        
        if company_dept_id == "1":
            logger.warning(f"无法创建公司部门 {company_dept_name}，使用默认部门")
            return "1"
        
        # 如果只有公司级别，直接返回公司部门ID
        if len(hierarchy_parts) == 1:
            return company_dept_id
        
        # 逐级创建部门层级
        current_parent_id = company_dept_id
        
        for i in range(1, len(hierarchy_parts)):
            current_dept_name = hierarchy_parts[i]
            
            # 创建当前级别的部门
            current_dept_id = await self._ensure_single_department_exists(
                current_dept_name, current_parent_id, auto_create
            )
            
            if current_dept_id == "1":
                logger.warning(f"无法创建部门 {current_dept_name}，使用上级部门 {current_parent_id}")
                return current_parent_id
            
            current_parent_id = current_dept_id
        
        return current_parent_id

    async def _ensure_single_department_exists(self, dept_name: str, parent_id: str, auto_create: bool = True) -> str:
        """
        确保单个部门存在，在指定的父部门下创建
        
        Args:
            dept_name: 部门名称
            parent_id: 父部门ID
            auto_create: 是否自动创建不存在的部门
            
        Returns:
            str: 企业邮箱中的部门ID
        """
        if not dept_name or dept_name.strip() == "":
            logger.warning("部门名称为空，返回父部门ID")
            return parent_id
        
        dept_name = dept_name.strip()
        
        # 生成缓存键，包含父部门信息以区分同名部门
        cache_key = f"{dept_name}#{parent_id}"
        
        # 检查缓存
        if cache_key in self.dept_name_to_id_cache:
            cached_id = self.dept_name_to_id_cache[cache_key]
            # 记录缓存命中
            if dept_name not in [item['name'] for item in self.department_stats['cached']]:
                self.department_stats['cached'].append({
                    'name': dept_name,
                    'dept_id': cached_id,
                    'parent_id': parent_id
                })
            return cached_id
        
        # 先从本地数据库查找是否已存在映射
        existing_dept = email_department.get_by_name(self.db, name=dept_name)
        if existing_dept:
            # 检查父部门是否匹配
            if existing_dept.parent_id == parent_id:
                self.dept_name_to_id_cache[cache_key] = existing_dept.dept_id
                logger.info(f"找到现有部门映射: {dept_name} -> {existing_dept.dept_id} (父部门: {parent_id})")
                self.department_stats['cached'].append({
                    'name': dept_name,
                    'dept_id': existing_dept.dept_id,
                    'parent_id': parent_id
                })
                return existing_dept.dept_id
            else:
                logger.info(f"部门 {dept_name} 存在但父部门不匹配，本地: {existing_dept.parent_id}, 期望: {parent_id}")
        
        # 本地不存在或父部门不匹配，检查是否允许自动创建
        if not auto_create:
            logger.warning(f"部门 {dept_name} 不存在且未启用自动创建，返回父部门ID")
            self.dept_name_to_id_cache[cache_key] = parent_id
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': '未启用自动创建部门',
                'parent_id': parent_id
            })
            return parent_id
        
        # 需要在企业邮箱中创建部门
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            try:
                # 根据腾讯企业邮箱API文档，创建部门的正确参数格式
                api_data = {
                    "name": dept_name,      # 部门名称（必需）
                    "parentid": parent_id,  # 父部门id（必需）
                    "order": 1              # 排序值（可选）
                }
                
                if retry_count > 0:
                    logger.info(f"正在重试创建部门 {dept_name} (第 {retry_count + 1} 次尝试，父部门: {parent_id})")
                else:
                    logger.info(f"正在企业邮箱中创建部门: {dept_name}, 参数: {api_data}")
                    
                result = await self.api_service.create_department(api_data)
                
                if result.errcode != 0:
                    error_msg = f"企业邮箱创建部门失败: {result.errmsg} (错误码: {result.errcode})"
                    logger.error(error_msg)
                    
                    # 根据错误码提供更详细的错误信息
                    if result.errcode == -1:
                        error_detail = "参数格式错误，请检查部门名称是否符合规范"
                    elif result.errcode == 40001:
                        error_detail = "access_token无效或已过期"
                        # 如果是token问题，强制刷新token后重试
                        if retry_count < max_retries:
                            logger.info("access_token可能已过期，强制刷新token后重试")
                            # 清除缓存的token，下次请求时会重新获取
                            self.api_service._access_token = None
                            self.api_service._token_expires_at = None
                            retry_count += 1
                            continue
                    elif result.errcode == 40003:
                        error_detail = "请求参数不正确"
                    elif result.errcode == 60008:
                        error_detail = "部门名称已存在"
                        # 如果部门已存在，尝试搜索获取部门ID
                        logger.info(f"部门 {dept_name} 已存在，尝试获取部门ID")
                        search_result = await self.api_service.search_department(dept_name, fuzzy=False)
                        if search_result.errcode == 0 and search_result.data.get('department'):
                            departments = search_result.data['department']
                            for dept in departments:
                                if dept.get('name') == dept_name and str(dept.get('parentid', '')) == parent_id:
                                    existing_dept_id = str(dept.get('id', ''))
                                    if existing_dept_id:
                                        logger.info(f"找到已存在的部门: {dept_name} -> {existing_dept_id}")
                                        
                                        # 在本地数据库中创建记录
                                        dept_create = EmailDepartmentCreate(
                                            dept_id=existing_dept_id,
                                            name=dept_name,
                                            parent_id=parent_id,
                                            order=1
                                        )
                                        local_dept = email_department.create(self.db, obj_in=dept_create)
                                        
                                        # 更新缓存
                                        self.dept_name_to_id_cache[cache_key] = existing_dept_id
                                        
                                        # 记录为更新（已存在的部门）
                                        self.department_stats['updated'].append({
                                            'name': dept_name,
                                            'dept_id': existing_dept_id,
                                            'parent_id': parent_id
                                        })
                                        
                                        return existing_dept_id
                        
                        # 如果搜索失败，继续处理错误
                        error_detail = f"部门名称已存在但无法获取部门ID: {dept_name}"
                    elif result.errcode == 45024:
                        error_detail = "企业邮箱用户数量已达上限"
                    else:
                        error_detail = f"未知错误，错误码: {result.errcode}"
                    
                    # 如果不是可重试的错误或已达最大重试次数，记录失败并返回父部门ID
                    logger.error(f"{error_msg} - {error_detail}")
                    retry_count += 1
                    if retry_count > max_retries:
                        break
                    continue
                
                break  # 成功时跳出循环
                
            except Exception as e:
                logger.error(f"调用企业邮箱API创建部门异常: {str(e)}", exc_info=True)
                retry_count += 1
                if retry_count > max_retries:
                    break
                continue
        
        # 如果到这里说明所有重试都失败了
        if result.errcode != 0:
            # 创建失败，返回父部门ID
            logger.warning(f"部门 {dept_name} 创建失败（重试 {max_retries} 次后），返回父部门ID {parent_id}")
            self.dept_name_to_id_cache[cache_key] = parent_id
            # 记录创建失败
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': f'API创建失败: {result.errmsg} ({error_detail})',
                'parent_id': parent_id
            })
            return parent_id
        
        # 成功创建部门，处理响应
        try:
            # 从API响应中获取新创建的部门ID
            new_dept_id = str(result.data.get('id', ''))
            if not new_dept_id:
                logger.error(f"API响应中未包含部门ID: {result.data}")
                # 如果没有返回ID，尝试通过部门名称查找
                search_result = await self.api_service.search_department(dept_name, fuzzy=False)
                if search_result.errcode == 0 and search_result.data.get('department'):
                    departments = search_result.data['department']
                    # 查找完全匹配的部门
                    for dept in departments:
                        if dept.get('name') == dept_name and str(dept.get('parentid', '')) == parent_id:
                            new_dept_id = str(dept.get('id', ''))
                            logger.info(f"通过搜索找到新创建的部门ID: {new_dept_id}")
                            break
                
                if not new_dept_id:
                    logger.error(f"无法获取新创建部门的ID，返回父部门ID")
                    self.dept_name_to_id_cache[cache_key] = parent_id
                    self.department_stats['failed'].append({
                        'name': dept_name,
                        'reason': 'API响应中未包含部门ID',
                        'parent_id': parent_id
                    })
                    return parent_id
            
            # 在本地数据库中创建部门记录
            dept_create = EmailDepartmentCreate(
                dept_id=new_dept_id,
                name=dept_name,
                parent_id=parent_id,
                order=1
            )
            local_dept = email_department.create(self.db, obj_in=dept_create)
            
            # 更新缓存
            self.dept_name_to_id_cache[cache_key] = new_dept_id
            
            # 记录成功创建
            self.department_stats['created'].append({
                'name': dept_name,
                'dept_id': new_dept_id,
                'parent_id': parent_id
            })
            
            logger.info(f"成功创建部门: {dept_name} -> {new_dept_id} (父部门: {parent_id})")
            return new_dept_id
            
        except Exception as e:
            error_msg = f"处理新创建部门 {dept_name} 的响应时发生异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            # 发生错误时返回父部门ID
            self.dept_name_to_id_cache[cache_key] = parent_id
            # 记录创建失败
            self.department_stats['failed'].append({
                'name': dept_name,
                'reason': f'处理响应时发生异常: {str(e)}',
                'parent_id': parent_id
            })
            return parent_id
    
    async def sync_personnel_to_email(self, 
                                    full_sync: bool = False,
                                    since_time: Optional[datetime] = None,
                                    dry_run: bool = False) -> PersonnelSyncResult:
        """
        同步人员信息到腾讯企业邮箱
        
        Args:
            full_sync: 是否全量同步
            since_time: 增量同步的起始时间
            dry_run: 是否为试运行（不实际执行API操作）
            
        Returns:
            PersonnelSyncResult: 同步结果
        """
        start_time = datetime.now()
        sync_type = "full" if full_sync else "incremental"
        
        logger.info(f"开始人员邮箱同步，类型: {sync_type}, 试运行: {dry_run}")
        
        # 加载过滤配置
        sync_config = self.db.query(PersonnelSyncConfig).first()
        
        if sync_config:
            # 设置过滤配置到变更检测器
            self.change_detector.set_filter_config(sync_config)
            
            # 记录过滤配置状态
            if sync_config.filter_enabled:
                filter_info = {
                    "included_companies": sync_config.included_companies,
                    "included_departments": sync_config.included_departments,
                    "included_job_titles": sync_config.included_job_titles,
                    "excluded_job_titles": sync_config.excluded_job_titles,
                    "filter_logic": sync_config.filter_logic
                }
                logger.info(f"使用过滤配置: {filter_info}")
            else:
                logger.info("未启用过滤功能，将处理所有人员")
        else:
            logger.warning("未找到同步配置，使用默认设置")
        
        # 创建同步日志
        import uuid
        sync_id = str(uuid.uuid4())

        sync_log = EmailSyncLog(
            sync_type="personnel",
            sync_category="personnel",
            sync_id=sync_id,
            operator="系统",  # 默认操作员
            status="running",
            message="同步开始",
            start_time=start_time
        )
        self.db.add(sync_log)
        self.db.commit()
        
        try:
            # 检测变更
            detection_result = self.change_detector.detect_changes(
                full_sync=full_sync,
                since_time=since_time
            )
            
            if not detection_result.success:
                raise Exception(detection_result.error_message)
            
            changes = detection_result.changes
            logger.info(f"检测到 {len(changes)} 项变更")
            
            # 更新同步日志
            sync_log.total_count = len(changes)
            self.db.commit()
            
            # 执行同步操作
            sync_stats = PersonnelSyncStats()
            operation_results = []
            
            for change in changes:
                try:
                    result = await self._process_change(change, dry_run)
                    operation_results.append(result)
                    
                    # 更新统计信息
                    if result.success:
                        if change.change_type == PersonnelChangeType.CREATE:
                            sync_stats.created_count += 1
                        elif change.change_type == PersonnelChangeType.UPDATE:
                            sync_stats.updated_count += 1
                        elif change.change_type == PersonnelChangeType.DISABLE:
                            sync_stats.disabled_count += 1
                    else:
                        sync_stats.error_count += 1
                    
                    sync_stats.processed_count += 1
                    
                    # 更新同步日志进度
                    sync_log.processed_count = sync_stats.processed_count
                    sync_log.synced_count = sync_stats.processed_count  # 同步数量等于处理数量
                    sync_log.created_count = sync_stats.created_count
                    sync_log.updated_count = sync_stats.updated_count
                    sync_log.disabled_count = sync_stats.disabled_count
                    sync_log.error_count = sync_stats.error_count
                    self.db.commit()
                    
                    # 避免API限流，短暂暂停
                    if not dry_run:
                        time.sleep(0.5)
                        
                except Exception as e:
                    logger.error(f"处理变更失败: {str(e)}", exc_info=True)
                    sync_stats.error_count += 1
                    sync_stats.processed_count += 1
                    
                    operation_results.append(PersonnelSyncOperationResult(
                        success=False,
                        change_type=change.change_type,
                        job_number=change.job_number,
                        error_message=str(e)
                    ))
            
            # 统计部门操作结果
            sync_stats.departments_created = len(self.department_stats['created'])
            sync_stats.departments_failed = len(self.department_stats['failed'])
            # departments_updated 暂时设为0，因为当前逻辑主要是创建新部门
            
            # 计算耗时
            end_time = datetime.now()
            duration = end_time - start_time
            duration_str = f"{duration.total_seconds():.2f}秒"
            
            # 更新同步日志
            sync_log.status = "success" if sync_stats.error_count == 0 else "failed"
            sync_log.message = f"同步完成，处理 {sync_stats.processed_count} 项变更"
            sync_log.duration = duration_str
            sync_log.completed_at = end_time
            sync_log.details = {
                "detection_result": {
                    "total_personnel": detection_result.total_personnel,
                    "total_email_members": detection_result.total_email_members,
                    "since_time": detection_result.since_time
                },
                "department_operations": {
                    "created": self.department_stats['created'],
                    "failed": self.department_stats['failed'],
                    "cached": self.department_stats['cached']
                },
                "operation_results": [result.dict() for result in operation_results[:10]]  # 只保存前10个结果
            }
            
            if sync_stats.error_count > 0:
                sync_log.error_message = f"同步过程中发生 {sync_stats.error_count} 个错误"
            
            self.db.commit()
            
            logger.info(f"人员邮箱同步完成，耗时: {duration_str}")
            
            return PersonnelSyncResult(
                success=sync_stats.error_count == 0,
                sync_log_id=sync_log.id,
                stats=sync_stats,
                operation_results=operation_results,
                duration=duration_str,
                dry_run=dry_run
            )
            
        except Exception as e:
            # 更新同步日志为失败状态
            end_time = datetime.now()
            duration = end_time - start_time
            
            sync_log.status = "failed"
            sync_log.message = f"同步失败: {str(e)}"
            sync_log.error_message = str(e)
            sync_log.duration = f"{duration.total_seconds():.2f}秒"
            sync_log.completed_at = end_time
            self.db.commit()
            
            logger.error(f"人员邮箱同步失败: {str(e)}", exc_info=True)
            
            return PersonnelSyncResult(
                success=False,
                sync_log_id=sync_log.id,
                error_message=str(e),
                duration=f"{duration.total_seconds():.2f}秒",
                dry_run=dry_run
            )
    
    async def _process_change(self, change: PersonnelChangeRecord, dry_run: bool) -> PersonnelSyncOperationResult:
        """处理单个变更"""
        logger.info(f"处理变更: {change.change_type.value}, 工号: {change.job_number}")
        
        try:
            if change.change_type == PersonnelChangeType.CREATE:
                # CREATE操作需要管理员审批，创建申请记录
                return await self._create_email_request(change, dry_run)
            elif change.change_type == PersonnelChangeType.UPDATE:
                return await self._update_email_user(change, dry_run)
            elif change.change_type == PersonnelChangeType.DISABLE:
                return await self._disable_email_user(change, dry_run)
            else:
                raise ValueError(f"不支持的变更类型: {change.change_type}")
                
        except Exception as e:
            logger.error(f"处理变更失败: {str(e)}", exc_info=True)
            return PersonnelSyncOperationResult(
                success=False,
                change_type=change.change_type,
                job_number=change.job_number,
                error_message=str(e)
            )

    async def _create_email_request(self, change: PersonnelChangeRecord, dry_run: bool) -> PersonnelSyncOperationResult:
        """创建邮箱申请记录"""
        personnel_data = change.personnel_data
        job_number = change.job_number

        if dry_run:
            logger.info(f"[试运行] 创建邮箱申请: 工号={job_number}, 姓名={personnel_data['user_name']}")
            return PersonnelSyncOperationResult(
                success=True,
                change_type=change.change_type,
                job_number=job_number,
                message=f"试运行：为 {personnel_data['user_name']} 创建邮箱申请"
            )

        # 检查是否已经存在待审批的申请
        existing_request = self.db.query(EmailCreationRequest).filter(
            and_(
                EmailCreationRequest.job_number == job_number,
                EmailCreationRequest.status.in_(["pending", "approved"])
            )
        ).first()

        if existing_request:
            logger.info(f"工号 {job_number} 已存在待处理的邮箱申请，状态: {existing_request.status}")
            return PersonnelSyncOperationResult(
                success=True,
                change_type=change.change_type,
                job_number=job_number,
                message=f"已存在待处理的邮箱申请（状态：{existing_request.status}）"
            )

        # 创建新的申请记录
        request = EmailCreationRequest(
            job_number=job_number,
            user_name=personnel_data["user_name"],
            dept_name=personnel_data.get("dept_name"),
            job_title_name=personnel_data.get("job_title_name"),
            mobile=personnel_data.get("mobile"),
            status="pending",
            reason=change.reason,
            requested_by="系统自动检测"
        )

        self.db.add(request)
        self.db.commit()

        logger.info(f"为工号 {job_number}（{personnel_data['user_name']}）创建邮箱申请，申请ID: {request.id}")

        return PersonnelSyncOperationResult(
            success=True,
            change_type=change.change_type,
            job_number=job_number,
            message=f"已为 {personnel_data['user_name']} 创建邮箱申请，等待管理员审批"
        )

    async def _create_email_user(self, change: PersonnelChangeRecord, dry_run: bool) -> PersonnelSyncOperationResult:
        """创建邮箱用户"""
        personnel_data = change.personnel_data
        job_number = change.job_number
        
        # 生成邮箱地址
        email_address = self._generate_email_address(personnel_data["user_name"], job_number)
        
        # 处理部门信息：根据完整的部门层级创建部门结构
        dept_name = personnel_data.get("dept_name", "")
        
        # 从配置中读取是否自动创建部门
        sync_config = self.db.query(PersonnelSyncConfig).first()
        auto_create_departments = sync_config.auto_create_departments if sync_config else True
        
        # 使用新的层级部门创建方法
        dept_id = await self._ensure_department_hierarchy_exists(personnel_data, auto_create_departments)
        
        # 确定部门操作类型
        dept_operation = None
        if dept_name:
            # 检查这个部门在本次同步中的操作类型
            for created in self.department_stats['created']:
                if created['name'] == dept_name:
                    dept_operation = 'created'
                    break
            if not dept_operation:
                for failed in self.department_stats['failed']:
                    if failed['name'] == dept_name:
                        dept_operation = 'failed'
                        break
            if not dept_operation:
                dept_operation = 'cached'  # 已存在或缓存命中
        
        # 构建腾讯API数据
        api_data = {
            "userid": email_address,
            "name": personnel_data["user_name"],
            "extid": job_number,
            "department": [dept_id],  # 使用实际的部门ID
            "position": personnel_data.get("job_title_name") or "",
            "mobile": personnel_data.get("mobile") or "",
            "password": "TempPass123!"  # 临时密码
        }
        
        # 清理API参数，确保不包含None值
        api_data = self._clean_api_params(api_data)
        
        if dry_run:
            logger.info(f"[试运行] 创建邮箱用户: {api_data}")
            return PersonnelSyncOperationResult(
                success=True,
                change_type=change.change_type,
                job_number=job_number,
                message=f"试运行：创建邮箱用户，部门: {dept_name} ({dept_id})",
                department_operation=dept_operation,
                department_name=dept_name,
                department_id=dept_id
            )
        
        # 调用腾讯API创建用户
        result = await self.api_service.create_member(api_data)
        
        if result.errcode != 0:
            raise Exception(f"腾讯API创建用户失败: {result.errmsg} (错误码: {result.errcode})")
        
        # 创建本地邮箱成员记录
        member_create = EmailMemberCreate(
            extid=job_number,
            email=email_address,
            name=personnel_data["user_name"],
            department_id=dept_id,  # 使用实际的部门ID
            password="TempPass123!",
            position=personnel_data.get("job_title_name"),
            mobile=personnel_data.get("mobile")
        )
        
        new_member = email_member.create(self.db, obj_in=member_create)
        
        logger.info(f"成功创建邮箱用户: {email_address}, 工号: {job_number}, 部门: {dept_name} ({dept_id})")
        
        return PersonnelSyncOperationResult(
            success=True,
            change_type=change.change_type,
            job_number=job_number,
            message=f"成功创建邮箱用户: {email_address}，分配到部门: {dept_name}",
            department_operation=dept_operation,
            department_name=dept_name,
            department_id=dept_id
        )
    
    async def _update_email_user(self, change: PersonnelChangeRecord, dry_run: bool) -> PersonnelSyncOperationResult:
        """更新邮箱用户"""
        personnel_data = change.personnel_data
        email_data = change.email_data
        job_number = change.job_number

        # 处理部门信息：检查部门是否有变更
        dept_name = personnel_data.get("dept_name", "")
        
        # 从配置中读取是否自动创建部门
        sync_config = self.db.query(PersonnelSyncConfig).first()
        auto_create_departments = sync_config.auto_create_departments if sync_config else True
        
        dept_id = await self._ensure_department_exists(dept_name, auto_create_departments)

        # 确定部门操作类型
        dept_operation = None
        if dept_name:
            # 检查这个部门在本次同步中的操作类型
            for created in self.department_stats['created']:
                if created['name'] == dept_name:
                    dept_operation = 'created'
                    break
            if not dept_operation:
                for failed in self.department_stats['failed']:
                    if failed['name'] == dept_name:
                        dept_operation = 'failed'
                        break
            if not dept_operation:
                dept_operation = 'cached'  # 已存在或缓存命中

        # 构建腾讯API更新数据
        api_data = {
            "userid": email_data["email"],
            "name": personnel_data["user_name"],
            "department": [dept_id],  # 包含部门更新
            "position": personnel_data.get("job_title_name") or "",
            "mobile": personnel_data.get("mobile") or ""
        }
        
        # 清理API参数，确保不包含None值
        api_data = self._clean_api_params(api_data)

        if dry_run:
            logger.info(f"[试运行] 更新邮箱用户: {api_data}")
            return PersonnelSyncOperationResult(
                success=True,
                change_type=change.change_type,
                job_number=job_number,
                message=f"试运行：更新邮箱用户，部门: {dept_name} ({dept_id})",
                department_operation=dept_operation,
                department_name=dept_name,
                department_id=dept_id
            )

        # 调用腾讯API更新用户
        result = await self.api_service.update_member(api_data)

        if result.errcode != 0:
            # 如果用户不存在，尝试创建用户
            if result.errcode == 60111:  # 用户不存在
                logger.warning(f"用户 {email_data['email']} 在腾讯企业邮箱中不存在，尝试自动创建")
                
                # 构建创建用户的API数据，使用现有的邮箱地址
                create_api_data = {
                    "userid": email_data["email"],
                    "name": personnel_data["user_name"],
                    "extid": job_number,
                    "department": [dept_id],
                    "position": personnel_data.get("job_title_name") or "",
                    "mobile": personnel_data.get("mobile") or "",
                    "password": "TempPass123!"  # 临时密码
                }
                
                # 清理API参数
                create_api_data = self._clean_api_params(create_api_data)
                
                # 调用腾讯API创建用户
                create_result = await self.api_service.create_member(create_api_data)
                
                if create_result.errcode != 0:
                    raise Exception(f"腾讯API创建用户失败: {create_result.errmsg} (错误码: {create_result.errcode})")
                
                logger.info(f"成功自动创建缺失的邮箱用户: {email_data['email']}, 工号: {job_number}")
                
                # 创建本地邮箱成员记录（如果不存在）
                member = self.db.query(EmailMember).filter(EmailMember.extid == job_number).first()
                if not member:
                    from app.schemas.email import EmailMemberCreate
                    
                    member_create = EmailMemberCreate(
                        extid=job_number,
                        email=email_data["email"],
                        name=personnel_data["user_name"],
                        department_id=dept_id,
                        password="TempPass123!",
                        position=personnel_data.get("job_title_name"),
                        mobile=personnel_data.get("mobile")
                    )
                    
                    member = email_member.create(self.db, obj_in=member_create)
                
                return PersonnelSyncOperationResult(
                    success=True,
                    change_type=change.change_type,
                    job_number=job_number,
                    message=f"用户不存在，已自动创建邮箱用户: {email_data['email']}，部门: {dept_name}",
                    department_operation=dept_operation,
                    department_name=dept_name,
                    department_id=dept_id
                )
            else:
                raise Exception(f"腾讯API更新用户失败: {result.errmsg} (错误码: {result.errcode})")

        # 更新本地邮箱成员记录
        member = self.db.query(EmailMember).filter(EmailMember.extid == job_number).first()
        if member:
            member_update = EmailMemberUpdate(
                name=personnel_data["user_name"],
                department_id=dept_id,  # 更新部门ID
                position=personnel_data.get("job_title_name"),
                mobile=personnel_data.get("mobile")
            )
            email_member.update(self.db, db_obj=member, obj_in=member_update)

        logger.info(f"成功更新邮箱用户: {email_data['email']}, 工号: {job_number}, 部门: {dept_name} ({dept_id})")

        return PersonnelSyncOperationResult(
            success=True,
            change_type=change.change_type,
            job_number=job_number,
            message=f"成功更新邮箱用户: {email_data['email']}，部门: {dept_name}",
            department_operation=dept_operation,
            department_name=dept_name,
            department_id=dept_id
        )

    async def _disable_email_user(self, change: PersonnelChangeRecord, dry_run: bool) -> PersonnelSyncOperationResult:
        """禁用邮箱用户"""
        email_data = change.email_data
        job_number = change.job_number

        if dry_run:
            logger.info(f"[试运行] 禁用邮箱用户: {email_data['email']}")
            return PersonnelSyncOperationResult(
                success=True,
                change_type=change.change_type,
                job_number=job_number,
                message="试运行：禁用邮箱用户"
            )

        # 使用禁用接口来停用离职用户的邮箱访问
        result = await self.api_service.disable_member(email_data["email"])

        if result.errcode != 0:
            # 如果禁用失败，可能用户已经不存在，这种情况下也算成功
            if result.errcode == 60111:  # 用户不存在
                logger.warning(f"用户 {email_data['email']} 在腾讯企业邮箱中不存在，跳过禁用")
            elif result.errcode in [40014, 41001, 42001]:  # access_token相关错误已经在API层处理重试
                raise Exception(f"腾讯API禁用用户失败: {result.errmsg} (错误码: {result.errcode})")
            else:
                # 其他错误也抛出，但记录详细信息
                logger.error(f"禁用用户失败: {result.errmsg} (错误码: {result.errcode})")
                raise Exception(f"腾讯API禁用用户失败: {result.errmsg} (错误码: {result.errcode})")

        # 更新本地邮箱成员记录为非活跃状态
        member = self.db.query(EmailMember).filter(EmailMember.extid == job_number).first()
        if member:
            member.is_active = False
            self.db.commit()

        logger.info(f"成功禁用邮箱用户: {email_data['email']}, 工号: {job_number}")

        return PersonnelSyncOperationResult(
            success=True,
            change_type=change.change_type,
            job_number=job_number,
            message=f"成功禁用邮箱用户: {email_data['email']}"
        )

    def _clean_api_params(self, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """清理API参数，确保不包含None值或无效值，严格按照腾讯企业邮箱API格式要求"""
        cleaned_data = {}
        
        for key, value in api_data.items():
            if value is None:
                # 根据字段类型设置默认值
                if key in ["position", "mobile", "name", "userid", "extid"]:
                    cleaned_data[key] = ""
                elif key in ["department"]:
                    # department必须是数组，即使为空也要传空数组
                    cleaned_data[key] = []
                else:
                    cleaned_data[key] = ""
            elif key in ["position", "mobile", "name", "userid", "extid", "password"]:
                # 字符串字段，确保是字符串类型且满足长度要求
                str_value = str(value) if value is not None else ""
                str_value = str_value.strip()
                
                if key in ["position", "name"] and len(str_value.encode('utf-8')) > 64:
                    # position和name字段长度限制为64字节
                    cleaned_data[key] = str_value.encode('utf-8')[:64].decode('utf-8', errors='ignore')
                else:
                    cleaned_data[key] = str_value
            elif isinstance(value, list):
                # 列表类型，确保不包含None值且不超过限制
                if key == "department":
                    # department为部门ID列表，不超过20个，确保都是整数
                    valid_depts = []
                    for item in value:
                        if item is not None and isinstance(item, (int, str)):
                            try:
                                dept_id = int(item)
                                if dept_id > 0:  # 部门ID应该是正整数
                                    valid_depts.append(dept_id)
                            except (ValueError, TypeError):
                                continue
                    cleaned_data[key] = valid_depts[:20]  # 限制最多20个部门
                else:
                    cleaned_data[key] = [item for item in value if item is not None]
            elif isinstance(value, (int, float)):
                # 数值类型，直接使用
                cleaned_data[key] = value
            else:
                # 其他类型，转换为字符串
                cleaned_data[key] = str(value) if value is not None else ""
        
        # 确保必要字段存在且格式正确
        if "department" in cleaned_data and not isinstance(cleaned_data["department"], list):
            cleaned_data["department"] = []
            
        return cleaned_data

    def _generate_email_address(self, user_name: str, job_number: str) -> str:
        """生成邮箱地址"""
        # 简单的邮箱地址生成策略：工号@domain
        # 这里需要从配置中获取实际的邮箱域名
        domain = "example.com"  # 需要从配置中获取
        return f"{job_number}@{domain}"

    async def check_data_consistency(self) -> Dict:
        """检查数据一致性"""
        logger.info("开始检查人员信息与邮箱数据一致性")

        try:
            # 获取所有有工号的人员信息
            personnel_with_job_number = self.db.query(EcologyUser).filter(
                and_(
                    EcologyUser.job_number.isnot(None),
                    EcologyUser.job_number != ""
                )
            ).all()

            # 获取所有有工号的邮箱成员
            email_members_with_extid = self.db.query(EmailMember).filter(
                and_(
                    EmailMember.extid.isnot(None),
                    EmailMember.extid != ""
                )
            ).all()

            # 创建工号映射
            personnel_by_job_number = {p.job_number: p for p in personnel_with_job_number}
            email_members_by_extid = {m.extid: m for m in email_members_with_extid}

            # 统计信息
            stats = {
                "total_personnel": len(personnel_with_job_number),
                "total_email_members": len(email_members_with_extid),
                "personnel_without_email": 0,
                "email_without_personnel": 0,
                "inconsistent_data": 0,
                "departed_with_active_email": 0
            }

            inconsistencies = []

            # 检查人员信息中有但邮箱中没有的
            for job_number, person in personnel_by_job_number.items():
                if job_number not in email_members_by_extid:
                    stats["personnel_without_email"] += 1
                    inconsistencies.append({
                        "type": "personnel_without_email",
                        "job_number": job_number,
                        "user_name": person.user_name,
                        "status": person.status
                    })
                else:
                    # 检查数据一致性
                    email_member = email_members_by_extid[job_number]
                    if (person.user_name != email_member.name or
                        person.mobile != email_member.mobile or
                        person.job_title_name != email_member.position):
                        stats["inconsistent_data"] += 1
                        inconsistencies.append({
                            "type": "inconsistent_data",
                            "job_number": job_number,
                            "personnel_name": person.user_name,
                            "email_name": email_member.name,
                            "personnel_mobile": person.mobile,
                            "email_mobile": email_member.mobile
                        })

                    # 检查离职人员是否还有活跃邮箱
                    if self.change_detector._is_departed(person) and email_member.is_active:
                        stats["departed_with_active_email"] += 1
                        inconsistencies.append({
                            "type": "departed_with_active_email",
                            "job_number": job_number,
                            "user_name": person.user_name,
                            "status": person.status,
                            "email": email_member.email
                        })

            # 检查邮箱中有但人员信息中没有的
            for extid, email_member in email_members_by_extid.items():
                if extid not in personnel_by_job_number:
                    stats["email_without_personnel"] += 1
                    inconsistencies.append({
                        "type": "email_without_personnel",
                        "job_number": extid,
                        "email": email_member.email,
                        "name": email_member.name
                    })

            logger.info(f"数据一致性检查完成，发现 {len(inconsistencies)} 个不一致项")

            return {
                "success": True,
                "stats": stats,
                "inconsistencies": inconsistencies[:100],  # 只返回前100个不一致项
                "total_inconsistencies": len(inconsistencies)
            }

        except Exception as e:
            logger.error(f"数据一致性检查失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error_message": str(e)
            }
