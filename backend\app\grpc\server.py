import grpc
import asyncio
import logging
import time
import datetime
from concurrent import futures
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app import models

# 全局变量：临时ID到数据库ID的映射
_temp_id_mapping = {}

# 导入生成的gRPC代码
from app.grpc.terminal_pb.terminal_pb2 import (
    RegisterResponse, HeartbeatResponse, ReportResponse, Command, CommandResultResponse, NotificationResponse,
    RegistryOperationResponse, RegistrySearchResponse, RegistryKey, RegistryValue, RegistrySearchResult,
    RegistryOperationType, RegistryValueType, RegistryRootKey
)
from app.grpc.terminal_pb.terminal_pb2_grpc import TerminalManagementServicer, add_TerminalManagementServicer_to_server
import uuid
import winreg
import os

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class TerminalManagementService(TerminalManagementServicer):
    """终端管理gRPC服务实现"""
    
    def __init__(self):
        # 受保护的注册表路径列表（小写）
        self.protected_paths = {
            'sam', 'security', 'bcd00000000', 'ntuser.dat', 'userdiff',
            'software\\microsoft\\windows nt\\currentversion\\profilelist'
        }
    
    def _is_protected_registry_path(self, path):
        """检查是否是受保护的注册表路径"""
        if not path:
            return False
        path_lower = path.lower()
        return any(protected in path_lower for protected in self.protected_paths)

    def RegisterTerminal(self, request, context):
        """
        处理终端注册请求
        """
        logger.info(f"收到终端注册请求: {request.hostname}, MAC: {request.mac_address}, IP: {request.ip_address}")

        # 创建数据库会话
        db = SessionLocal()
        try:
            # 检查终端是否已注册（根据MAC地址和IP地址组合查找）
            terminal = db.query(models.Terminal).filter(
                models.Terminal.mac_address == request.mac_address,
                models.Terminal.ip_address == request.ip_address
            ).first()

            # 创建protobuf响应对象
            response = RegisterResponse()

            if terminal:
                # 终端已存在，更新信息
                logger.info(f"终端已存在，更新信息: {terminal.id}")
                # 如果之前是离线状态，现在重新上线，记录最后在线时间
                if terminal.status != "online":
                    terminal.last_online_time = datetime.datetime.utcnow()
                
                terminal.hostname = request.hostname
                terminal.mac_address = request.mac_address
                terminal.ip_address = request.ip_address
                terminal.agent_version = request.agent_version
                terminal.status = "online"
                terminal.last_heartbeat = datetime.datetime.utcnow()
                terminal.updated_at = datetime.datetime.utcnow()

                # 填充响应
                response.success = True
                response.terminal_id = str(terminal.id)  # 转换为字符串
                # 使用默认值
                response.heartbeat_interval = 300
                response.collection_interval = 86400
                response.message = "终端重新注册成功"
                logger.info(f"终端重新注册成功, ID: {terminal.id}, 心跳间隔: 300秒")
            else:
                # 创建新终端
                new_terminal = models.Terminal(
                    hostname=request.hostname,
                    mac_address=request.mac_address,
                    ip_address=request.ip_address,
                    agent_version=request.agent_version,
                    status="online",
                    last_heartbeat=datetime.datetime.utcnow(),
                    last_online_time=datetime.datetime.utcnow()  # 新终端首次注册时设置最后在线时间
                )

                db.add(new_terminal)
                db.commit()  # 提交以获得自动生成的ID
                db.refresh(new_terminal)
                logger.info(f"注册新终端: {new_terminal.id}")

                # 填充响应
                response.success = True
                response.terminal_id = str(new_terminal.id)  # 转换为字符串
                response.heartbeat_interval = 300
                response.collection_interval = 86400
                response.message = "终端注册成功"
                logger.info(f"新终端注册成功, ID: {new_terminal.id}, 心跳间隔: 300秒")

            # 如果是更新操作，再次提交事务
            if terminal:
                db.commit()

            # 返回响应
            return response

        except Exception as e:
            db.rollback()
            logger.error(f"终端注册失败: {str(e)}")

            # 构建错误响应
            response = RegisterResponse()
            response.success = False
            response.terminal_id = ""
            response.heartbeat_interval = 300  # 确保是整数
            response.collection_interval = 86400  # 确保是整数
            response.message = f"终端注册失败: {str(e)}"

            # 返回响应
            return response

        finally:
            db.close()

    def Heartbeat(self, request, context):
        """
        处理终端心跳请求
        """
        terminal_id = request.terminal_id
        logger.debug(f"收到终端心跳: {terminal_id}")

        # 创建数据库会话
        db = SessionLocal()
        try:
            # 查找终端
            terminal = None
            
            # 首先检查是否是临时ID，如果是则查找映射
            if terminal_id.startswith("temp-") or terminal_id.startswith("local-"):
                if terminal_id in _temp_id_mapping:
                    actual_id = _temp_id_mapping[terminal_id]
                    terminal = db.query(models.Terminal).filter(
                        models.Terminal.id == actual_id
                    ).first()
                    logger.debug(f"使用映射查找终端: {terminal_id} -> {actual_id}")
            else:
                # 尝试将 terminal_id 转换为整数
                try:
                    terminal_id_int = int(terminal_id)
                    terminal = db.query(models.Terminal).filter(
                        models.Terminal.id == terminal_id_int
                    ).first()
                except ValueError:
                    logger.warning(f"无效的终端ID格式: '{terminal_id}'")

            # 创建protobuf响应对象
            response = HeartbeatResponse()

            # 如果是临时ID（以temp-或local-开头），且终端不存在，则创建临时终端记录
            if not terminal and (terminal_id.startswith("temp-") or terminal_id.startswith("local-")):
                logger.info(f"处理临时终端ID: {terminal_id}")
                try:
                    # 创建临时终端记录，使用MAC地址查重
                    temp_terminal = models.Terminal(
                        hostname=f"临时终端-{terminal_id[:8]}",
                        mac_address="00:00:00:00:00:00",  # 临时MAC地址
                        ip_address="0.0.0.0",
                        agent_version="unknown",
                        status="online",
                        last_heartbeat=datetime.datetime.utcnow(),
                        os_info=f"临时终端 {terminal_id}"
                    )
                    db.add(temp_terminal)
                    db.commit()
                    db.refresh(temp_terminal)

                    # 更新terminal引用，并记录映射关系
                    terminal = temp_terminal
                    _temp_id_mapping[terminal_id] = terminal.id
                    logger.info(f"临时终端记录创建成功: 临时ID {terminal_id} -> 数据库ID {terminal.id}")
                    logger.info(f"映射已保存到内存，当前映射数量: {len(_temp_id_mapping)}")
                    
                except Exception as e:
                    db.rollback()
                    logger.error(f"创建临时终端记录失败: {str(e)}")

            if not terminal:
                logger.warning(f"心跳请求的终端不存在，ID: '{terminal_id}'")
                # 构建错误响应
                response.success = False
                response.has_command = False
                return response

            # 更新终端心跳时间和在线状态
            # 如果之前是离线状态，现在重新上线，记录最后在线时间
            if terminal.status != "online":
                terminal.last_online_time = datetime.datetime.utcnow()
            
            terminal.last_heartbeat = datetime.datetime.utcnow()
            terminal.status = "online"
            terminal.updated_at = datetime.datetime.utcnow()

            # 检查是否有待执行的命令
            has_pending_command = db.query(models.TerminalCommand).filter(
                models.TerminalCommand.terminal_id == terminal.id,
                models.TerminalCommand.status == "pending"
            ).first() is not None

            # 提交事务
            db.commit()

            # 广播终端状态变化到SSE客户端
            try:
                from app.services.sse_manager import broadcast_terminal_status_change
                
                # 准备状态数据
                status_data = {
                    "online_status": True,
                    "last_heartbeat_time": terminal.last_heartbeat_time.isoformat() if terminal.last_heartbeat_time else None,
                    "hostname": terminal.hostname,
                    "ip_address": terminal.ip_address,
                    "mac_address": terminal.mac_address
                }
                
                # 异步广播状态变化
                import asyncio
                import threading
                
                def broadcast_in_new_loop():
                    """在新的事件循环中广播状态"""
                    try:
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        new_loop.run_until_complete(broadcast_terminal_status_change(terminal.id, status_data))
                        new_loop.close()
                    except Exception as e:
                        logger.error(f"在新事件循环中SSE广播失败: {str(e)}")
                
                try:
                    # 尝试获取当前事件循环
                    loop = asyncio.get_running_loop()
                    # 如果存在运行中的事件循环，在其中创建任务
                    loop.create_task(broadcast_terminal_status_change(terminal.id, status_data))
                except RuntimeError:
                    # 如果没有运行中的事件循环，在新线程中运行
                    broadcast_thread = threading.Thread(target=broadcast_in_new_loop, daemon=True)
                    broadcast_thread.start()
                    
            except Exception as broadcast_error:
                logger.error(f"SSE广播终端状态变化失败: {str(broadcast_error)}")

            # 构建响应
            response.success = True
            response.has_command = has_pending_command

            # 返回响应
            return response

        except Exception as e:
            db.rollback()
            logger.error(f"处理心跳请求失败: {str(e)}")

            # 构建错误响应
            response = HeartbeatResponse()
            response.success = False
            response.has_command = False

            # 返回响应
            return response

        finally:
            db.close()

    def ReportTerminalInfo(self, request, context):
        """
        处理终端信息上报请求
        """
        logger.info(f"收到终端信息上报: {request.terminal_id}")

        # 创建数据库会话
        db = SessionLocal()
        try:
            # 查找终端
            terminal = None
            terminal_id = request.terminal_id
            
            # 首先检查是否是临时ID，如果是则查找映射
            if terminal_id.startswith("temp-") or terminal_id.startswith("local-"):
                if terminal_id in _temp_id_mapping:
                    actual_id = _temp_id_mapping[terminal_id]
                    terminal = db.query(models.Terminal).filter(
                        models.Terminal.id == actual_id
                    ).first()
                    logger.debug(f"使用映射查找终端: {terminal_id} -> {actual_id}")
            else:
                # 尝试将 terminal_id 转换为整数
                try:
                    terminal_id_int = int(terminal_id)
                    terminal = db.query(models.Terminal).filter(
                        models.Terminal.id == terminal_id_int
                    ).first()
                except ValueError:
                    logger.warning(f"无效的终端ID格式: '{terminal_id}'")

            # 创建protobuf响应对象
            response = ReportResponse()

            if not terminal:
                logger.warning(f"上报信息的终端不存在: {request.terminal_id}")
                # 构建错误响应
                response.success = False
                response.message = "终端不存在"
                return response

            # 更新终端的在线状态和心跳时间
            terminal.online_status = True
            terminal.last_heartbeat_time = datetime.datetime.utcnow()

            # 更新或创建硬件信息
            self._update_hardware_info(db, terminal.id, request.hardware)

            # 更新或创建操作系统信息
            self._update_os_info(db, terminal.id, request.os)

            # 更新软件列表
            self._update_software_list(db, terminal.id, request.installed_software)

            # 更新网络信息
            self._update_network_info(db, terminal.id, request.network)

            # 更新最后登录用户信息
            self._update_login_user(db, terminal.id, request.last_login_user)

            # 提交事务
            db.commit()

            # 构建响应
            response.success = True
            response.message = "终端信息上报成功"

            # 返回响应
            return response

        except Exception as e:
            db.rollback()
            logger.error(f"处理终端信息上报失败: {str(e)}")

            # 构建错误响应
            response = ReportResponse()
            response.success = False
            response.message = f"终端信息上报失败: {str(e)}"

            # 返回响应
            return response

        finally:
            db.close()

    def ReceiveCommands(self, request, context):
        """
        处理终端接收命令请求，采用服务器流方式
        """
        logger.info(f"收到接收命令请求: {request.terminal_id}")
        logger.debug(f"请求类型: {type(request.terminal_id)}, 值: {request.terminal_id}")

        # 创建数据库会话
        db = SessionLocal()
        try:
            # 尝试转换terminal_id为整数
            try:
                terminal_id_int = int(request.terminal_id) if request.terminal_id else None
                logger.debug(f"转换后的terminal_id: {terminal_id_int}")
            except (ValueError, TypeError) as e:
                logger.error(f"终端ID转换失败: {request.terminal_id}, 错误: {str(e)}")
                return

            # 查找终端
            terminal = db.query(models.Terminal).filter(
                models.Terminal.id == terminal_id_int
            ).first()

            if not terminal:
                logger.warning(f"接收命令请求的终端不存在: {request.terminal_id}")
                # 对于不存在的终端，不返回任何命令
                return

            # 更新终端心跳时间和在线状态
            terminal.last_heartbeat_time = datetime.datetime.utcnow()
            terminal.online_status = True
            db.commit()

            # 查询待执行的命令
            pending_commands = db.query(models.TerminalCommand).filter(
                models.TerminalCommand.terminal_id == terminal.id,
                models.TerminalCommand.status == "pending"
            ).all()

            for cmd in pending_commands:
                logger.debug(f"处理命令: {cmd.id}, 类型: {cmd.type}, create_time: {cmd.create_time}")
                
                # 将命令状态更新为已发送
                cmd.status = "sent"
                cmd.sent_time = datetime.datetime.utcnow()

                # 构建并发送命令
                command = Command()
                command.command_id = str(cmd.id)  # 转换为字符串，匹配protobuf定义

                # 设置命令类型
                try:
                    cmd_type_lower = cmd.type.lower() if cmd.type else ""
                    logger.debug(f"命令类型: {cmd_type_lower}")
                    
                    if cmd_type_lower == "collect_info":
                        command.type = Command.CommandType.COLLECT_INFO
                    elif cmd_type_lower == "upgrade_agent":
                        command.type = Command.CommandType.UPGRADE_AGENT
                    elif cmd_type_lower == "custom_command":
                        command.type = Command.CommandType.CUSTOM_COMMAND
                    elif cmd_type_lower == "uninstall_software":
                        command.type = Command.CommandType.UNINSTALL_SOFTWARE
                    else:
                        # 未知命令类型，跳过
                        logger.warning(f"跳过未知类型的命令: {cmd.type}")
                        continue
                except AttributeError as e:
                    logger.error(f"处理命令类型失败: {cmd.type}, 错误: {str(e)}")
                    continue

                # 安全处理各个字段
                try:
                    command.content = cmd.content if cmd.content else ""
                    logger.debug(f"命令内容长度: {len(command.content)}")
                    
                    # 安全处理 create_time，避免 None 值导致的类型错误
                    if cmd.create_time is not None:
                        command.create_time = int(cmd.create_time.timestamp())
                        logger.debug(f"命令创建时间: {command.create_time}")
                    else:
                        command.create_time = int(datetime.datetime.utcnow().timestamp())
                        logger.warning(f"命令 {cmd.id} 的create_time为空，使用当前时间")
                    
                    command.timeout = cmd.timeout or 60  # 默认60秒超时
                    logger.debug(f"命令超时时间: {command.timeout}")
                    
                except Exception as e:
                    logger.error(f"设置命令字段时出错: {str(e)}")
                    continue

                # 提交数据库更改
                db.commit()

                # 发送命令给客户端
                logger.info(f"发送命令 {cmd.id} 给终端 {terminal.id}")
                yield command

        except Exception as e:
            db.rollback()
            logger.error(f"处理接收命令请求失败: {str(e)}")

        finally:
            db.close()

    def ReportCommandResult(self, request, context):
        """
        处理命令执行结果上报
        """
        logger.info(f"收到命令执行结果上报: 命令ID={request.command_id}, 终端ID={request.terminal_id}")

        # 创建数据库会话
        db = SessionLocal()
        try:
            # 处理终端ID映射
            terminal_id = request.terminal_id
            actual_terminal_id = terminal_id
            
            # 首先检查是否是临时ID，如果是则查找映射
            if terminal_id.startswith("temp-") or terminal_id.startswith("local-"):
                if terminal_id in _temp_id_mapping:
                    actual_terminal_id = _temp_id_mapping[terminal_id]
                    logger.debug(f"使用映射查找终端: {terminal_id} -> {actual_terminal_id}")
            else:
                # 尝试将 terminal_id 转换为整数
                try:
                    actual_terminal_id = int(terminal_id)
                except ValueError:
                    logger.warning(f"无效的终端ID格式: '{terminal_id}'")
                    
            # 查找对应的命令
            command = db.query(models.TerminalCommand).filter(
                models.TerminalCommand.id == request.command_id,
                models.TerminalCommand.terminal_id == actual_terminal_id
            ).first()

            # 创建protobuf响应对象
            response = CommandResultResponse()

            if not command:
                logger.warning(f"命令不存在: {request.command_id}")
                # 构建错误响应
                response.received = False
                response.message = "命令不存在"
                return response

            # 检查是否是进度报告
            is_progress_report = False
            progress_value = 0
            output = request.output

            # 解析进度信息，格式为[PROGRESS:XX] Message
            if output and output.startswith("[PROGRESS:"):
                try:
                    progress_end = output.find("]")
                    if progress_end > 0:
                        progress_str = output[10:progress_end]  # 提取进度数字
                        progress_value = int(progress_str)
                        message = output[progress_end+2:]  # 提取消息部分
                        is_progress_report = True
                        output = message  # 将消息部分作为输出
                        logger.info(f"收到命令 {request.command_id} 的进度报告: {progress_value}% - {message}")
                except Exception as e:
                    logger.warning(f"解析进度信息失败: {str(e)}")

            # 如果是进度报告，只更新输出，不改变状态
            if is_progress_report:
                # 如果是最终完成状态，更新状态
                if progress_value >= 100:
                    command.status = "completed"
                    command.execute_time = datetime.datetime.utcnow()
                elif progress_value < 0:  # 负数表示错误
                    command.status = "failed"
                    command.execute_time = datetime.datetime.utcnow()
                else:
                    # 其他进度值表示正在运行
                    command.status = "running"

                # 更新输出信息，使用正确的字段名
                command.result = output
                if request.error:
                    command.error = request.error
            else:
                # 如果不是进度报告，则是最终结果
                command.status = "completed" if request.success else "failed"
                command.result = output
                command.error = request.error
                # 安全处理 execution_time，避免类型错误
                if hasattr(request, 'execution_time') and request.execution_time:
                    command.execute_time = datetime.datetime.fromtimestamp(request.execution_time)
                else:
                    command.execute_time = datetime.datetime.utcnow()
                command.execution_duration = request.execution_duration

            # 提交事务
            db.commit()

            # 构建响应
            response.received = True
            response.message = "命令执行结果已接收"

            # 返回响应
            return response

        except Exception as e:
            db.rollback()
            logger.error(f"处理命令执行结果上报失败: {str(e)}")

            # 构建错误响应
            response = CommandResultResponse()
            response.received = False
            response.message = f"处理命令执行结果失败: {str(e)}"

            # 返回响应
            return response

        finally:
            db.close()

    def NotifyCommand(self, request, context):
        """
        处理命令通知请求，用于服务器主动通知终端有新命令
        """
        terminal_id = request.terminal_id
        command_id = request.command_id if hasattr(request, 'command_id') and request.command_id else None

        logger.info(f"发送命令通知到终端: {terminal_id}, 命令ID: {command_id or '所有命令'}")

        # 创建响应对象
        response = NotificationResponse()

        try:
            # 如果终端在线（心跳正常），则尝试通知，否则返回失败
            db = SessionLocal()

            # 处理终端ID映射
            actual_terminal_id = terminal_id
            
            # 首先检查是否是临时ID，如果是则查找映射
            if terminal_id.startswith("temp-") or terminal_id.startswith("local-"):
                if terminal_id in _temp_id_mapping:
                    actual_terminal_id = _temp_id_mapping[terminal_id]
                    logger.debug(f"使用映射查找终端: {terminal_id} -> {actual_terminal_id}")
            else:
                # 尝试将 terminal_id 转换为整数
                try:
                    actual_terminal_id = int(terminal_id)
                except ValueError:
                    logger.warning(f"无效的终端ID格式: '{terminal_id}'")
                    actual_terminal_id = None

            if actual_terminal_id is None:
                logger.warning(f"终端 {terminal_id} ID无效，无法发送通知")
                response.received = False
                response.message = "终端ID无效"
                return response

            # 查询终端是否在线
            terminal = db.query(models.Terminal).filter(
                models.Terminal.id == actual_terminal_id,
                models.Terminal.status == "online"
            ).first()

            if not terminal:
                logger.warning(f"终端 {terminal_id} 不在线或不存在，无法发送通知")
                response.received = False
                response.message = "终端不在线或不存在"
                return response

            # 通知已发送(实际上这里只是记录了通知尝试，真正的通知是终端轮询时发送的)
            response.received = True
            response.message = "通知已发送"

            return response

        except Exception as e:
            logger.error(f"发送命令通知时发生错误: {str(e)}")
            response.received = False
            response.message = f"发送通知失败: {str(e)}"
            return response

    # 辅助方法，用于更新各类终端信息
    def _update_hardware_info(self, db: Session, terminal_id: str, hardware_data):
        """更新硬件信息"""
        # 查找现有硬件信息
        hardware = db.query(models.HardwareInfo).filter(
            models.HardwareInfo.terminal_id == terminal_id
        ).first()

        if hardware:
            # 更新现有记录
            hardware.cpu_model = hardware_data.cpu_model
            hardware.cpu_cores = hardware_data.cpu_cores
            hardware.memory_total = hardware_data.memory_total
            hardware.serial_number = hardware_data.serial_number
            hardware.manufacturer = hardware_data.manufacturer
            hardware.model = hardware_data.model
            hardware.update_time = datetime.datetime.utcnow()
        else:
            # 创建新记录，不指定id让数据库自动生成
            hardware = models.HardwareInfo(
                terminal_id=terminal_id,
                cpu_model=hardware_data.cpu_model,
                cpu_cores=hardware_data.cpu_cores,
                memory_total=hardware_data.memory_total,
                serial_number=hardware_data.serial_number,
                manufacturer=hardware_data.manufacturer,
                model=hardware_data.model
            )
            db.add(hardware)
            db.flush()  # 刷新以获取ID

        # 更新磁盘信息
        # 首先删除现有的磁盘信息
        db.query(models.DiskInfo).filter(
            models.DiskInfo.hardware_id == hardware.id
        ).delete()

        # 添加新的磁盘信息
        for disk_data in hardware_data.disks:
            disk = models.DiskInfo(
                hardware_id=hardware.id,
                name=disk_data.name,
                total_space=disk_data.total_space,
                free_space=disk_data.free_space,
                filesystem=disk_data.filesystem,
                mount_point=disk_data.mount_point
            )
            db.add(disk)

    def _update_os_info(self, db: Session, terminal_id: str, os_data):
        """更新操作系统信息"""
        # 查找现有操作系统信息
        os_info = db.query(models.OSInfo).filter(
            models.OSInfo.terminal_id == terminal_id
        ).first()

        if os_info:
            # 更新现有记录
            os_info.name = os_data.name
            os_info.version = os_data.version
            os_info.build = os_data.build
            os_info.architecture = os_data.architecture
            os_info.install_date = os_data.install_date
            os_info.installed_updates = list(os_data.installed_updates)
            os_info.update_time = datetime.datetime.utcnow()
        else:
            # 创建新记录
            os_info = models.OSInfo(
                terminal_id=terminal_id,
                name=os_data.name,
                version=os_data.version,
                build=os_data.build,
                architecture=os_data.architecture,
                install_date=os_data.install_date,
                installed_updates=list(os_data.installed_updates)
            )
            db.add(os_info)
            db.flush()  # 刷新以获取ID

        # 更新安全信息
        security = db.query(models.SecurityInfo).filter(
            models.SecurityInfo.os_info_id == os_info.id
        ).first()

        if security:
            # 更新现有记录
            security.firewall_enabled = os_data.security.firewall_enabled
            security.antivirus = os_data.security.antivirus
            security.antivirus_enabled = os_data.security.antivirus_enabled
            security.update_time = datetime.datetime.utcnow()
        else:
            # 创建新记录
            security = models.SecurityInfo(
                os_info_id=os_info.id,
                firewall_enabled=os_data.security.firewall_enabled,
                antivirus=os_data.security.antivirus,
                antivirus_enabled=os_data.security.antivirus_enabled
            )
            db.add(security)

    def _update_software_list(self, db: Session, terminal_id: str, software_list):
        """更新软件列表"""
        # 删除现有软件记录
        db.query(models.Software).filter(
            models.Software.terminal_id == terminal_id
        ).delete()

        # 添加新的软件记录
        for software_data in software_list:
            software = models.Software(
                terminal_id=terminal_id,
                name=software_data.name,
                version=software_data.version,
                publisher=software_data.publisher,
                install_date=software_data.install_date,
                size=software_data.size,
                install_location=software_data.install_location
            )
            db.add(software)

    def _update_network_info(self, db: Session, terminal_id: str, network_data):
        """更新网络信息"""
        # 查找现有网络信息
        network_info = db.query(models.NetworkInfo).filter(
            models.NetworkInfo.terminal_id == terminal_id
        ).first()

        if network_info:
            # 更新现有记录
            network_info.hostname = network_data.hostname
            network_info.domain = network_data.domain
            network_info.dns_servers = list(network_data.dns_servers)
            network_info.default_gateway = network_data.default_gateway
            network_info.update_time = datetime.datetime.utcnow()
        else:
            # 创建新记录
            network_info = models.NetworkInfo(
                terminal_id=terminal_id,
                hostname=network_data.hostname,
                domain=network_data.domain,
                dns_servers=list(network_data.dns_servers),
                default_gateway=network_data.default_gateway
            )
            db.add(network_info)
            db.flush()  # 刷新以获取ID

        # 更新网络接口
        # 首先删除现有的网络接口
        db.query(models.NetworkInterface).filter(
            models.NetworkInterface.network_info_id == network_info.id
        ).delete()

        # 添加新的网络接口
        for interface_data in network_data.interfaces:
            interface = models.NetworkInterface(
                network_info_id=network_info.id,
                name=interface_data.name,
                mac_address=interface_data.mac_address,
                ip_address=interface_data.ip_address,
                subnet_mask=interface_data.subnet_mask,
                dhcp_enabled=interface_data.dhcp_enabled,
                is_connected=interface_data.is_connected
            )
            db.add(interface)

    def _update_login_user(self, db: Session, terminal_id: str, user_data):
        """更新最后登录用户信息"""
        # 查找现有用户登录信息
        login_info = db.query(models.UserLoginInfo).filter(
            models.UserLoginInfo.terminal_id == terminal_id
        ).first()

        if login_info:
            # 更新现有记录
            login_info.username = user_data.username
            login_info.full_name = user_data.full_name
            login_info.login_time = user_data.login_time
            login_info.domain = user_data.domain
            login_info.update_time = datetime.datetime.utcnow()
        else:
            # 创建新记录
            login_info = models.UserLoginInfo(
                terminal_id=terminal_id,
                username=user_data.username,
                full_name=user_data.full_name,
                login_time=user_data.login_time,
                domain=user_data.domain
            )
            db.add(login_info)

    def PerformRegistryOperation(self, request, context):
        """
        处理注册表操作请求
        """
        logger.info(f"收到注册表操作请求: 终端ID={request.terminal_id}, 操作={request.operation}, 路径={request.sub_key_path}")
        
        # 创建响应对象
        response = RegistryOperationResponse()
        response.command_id = request.command_id
        response.terminal_id = request.terminal_id
        response.operation_time = int(time.time())
        
        try:
            # 根键映射
            root_key_mapping = {
                RegistryRootKey.HKEY_CLASSES_ROOT: winreg.HKEY_CLASSES_ROOT,
                RegistryRootKey.HKEY_CURRENT_USER: winreg.HKEY_CURRENT_USER,
                RegistryRootKey.HKEY_LOCAL_MACHINE: winreg.HKEY_LOCAL_MACHINE,
                RegistryRootKey.HKEY_USERS: winreg.HKEY_USERS,
                RegistryRootKey.HKEY_CURRENT_CONFIG: winreg.HKEY_CURRENT_CONFIG
            }
            
            root_key = root_key_mapping.get(request.root_key, winreg.HKEY_LOCAL_MACHINE)
            
            # 处理不同的操作类型
            if request.operation == RegistryOperationType.REGISTRY_READ:
                success, message, key_data, value_data = self._perform_registry_read(
                    root_key, request.sub_key_path, request.value_name
                )
                response.success = success
                response.message = message
                if key_data:
                    response.key_data.CopyFrom(key_data)
                if value_data:
                    response.value_data.CopyFrom(value_data)
                    
            elif request.operation == RegistryOperationType.REGISTRY_WRITE:
                success, message = self._perform_registry_write(
                    root_key, request.sub_key_path, request.value_name, 
                    request.value_type, request.value_data
                )
                response.success = success
                response.message = message
                
            elif request.operation == RegistryOperationType.REGISTRY_DELETE:
                success, message = self._perform_registry_delete(
                    root_key, request.sub_key_path, request.value_name
                )
                response.success = success
                response.message = message
                
            elif request.operation == RegistryOperationType.REGISTRY_CREATE_KEY:
                success, message = self._perform_registry_create_key(
                    root_key, request.sub_key_path
                )
                response.success = success
                response.message = message
                
            elif request.operation == RegistryOperationType.REGISTRY_DELETE_KEY:
                success, message = self._perform_registry_delete_key(
                    root_key, request.sub_key_path
                )
                response.success = success
                response.message = message
                
            elif request.operation == RegistryOperationType.REGISTRY_ENUMERATE:
                success, message, key_data = self._perform_registry_enumerate(
                    root_key, request.sub_key_path, 
                    page=request.page if request.page > 0 else 1,
                    page_size=request.page_size if request.page_size > 0 else 1000,
                    search_filter=request.search_filter
                )
                response.success = success
                response.message = message
                if key_data:
                    response.key_data.CopyFrom(key_data)
                    
            elif request.operation == RegistryOperationType.REGISTRY_BACKUP:
                success, message, backup_id = self._perform_registry_backup(
                    root_key, request.sub_key_path, request.backup_reason
                )
                response.success = success
                response.message = message
                response.backup_id = backup_id
                
            else:
                response.success = False
                response.message = f"不支持的操作类型: {request.operation}"
                
        except Exception as e:
            logger.error(f"注册表操作失败: {str(e)}")
            response.success = False
            response.error = f"操作失败: {str(e)}"
            response.message = "注册表操作执行失败"
        
        logger.info(f"注册表操作完成: 成功={response.success}, 消息={response.message}")
        
        # 添加调试日志
        if response.key_data:
            logger.info(f"返回键数据: 子键数量={len(response.key_data.sub_keys)}, 值数量={len(response.key_data.values)}")
            if response.key_data.sub_keys:
                logger.info(f"子键列表: {list(response.key_data.sub_keys)[:5]}...")  # 只显示前5个
        else:
            logger.warning("未返回键数据")
        
        return response

    def SearchRegistry(self, request, context):
        """
        处理注册表搜索请求
        """
        logger.info(f"收到注册表搜索请求: 终端ID={request.terminal_id}, 搜索模式={request.search_pattern}")
        
        # 创建响应对象
        response = RegistrySearchResponse()
        response.command_id = request.command_id
        response.terminal_id = request.terminal_id
        
        try:
            # 根键映射
            root_key_mapping = {
                RegistryRootKey.HKEY_CLASSES_ROOT: winreg.HKEY_CLASSES_ROOT,
                RegistryRootKey.HKEY_CURRENT_USER: winreg.HKEY_CURRENT_USER,
                RegistryRootKey.HKEY_LOCAL_MACHINE: winreg.HKEY_LOCAL_MACHINE,
                RegistryRootKey.HKEY_USERS: winreg.HKEY_USERS,
                RegistryRootKey.HKEY_CURRENT_CONFIG: winreg.HKEY_CURRENT_CONFIG
            }
            
            root_key = root_key_mapping.get(request.root_key, winreg.HKEY_LOCAL_MACHINE)
            
            # 执行搜索
            results = self._perform_registry_search(
                root_key, request.start_path, request.search_pattern,
                request.search_keys, request.search_values, request.search_data,
                request.max_depth, request.max_results
            )
            
            # 构建响应
            response.success = True
            response.message = f"搜索完成，找到 {len(results)} 个结果"
            response.total_results = len(results)
            response.more_results = len(results) >= request.max_results
            
            # 添加搜索结果
            for result in results:
                search_result = RegistrySearchResult()
                search_result.path = result['path']
                search_result.match_type = result['match_type']
                search_result.match_text = result['match_text']
                
                if result.get('value'):
                    value = RegistryValue()
                    value.name = result['value']['name']
                    value.type = result['value']['type']
                    value.data = result['value']['data']
                    value.size = result['value']['size']
                    search_result.value.CopyFrom(value)
                
                response.results.append(search_result)
                
        except Exception as e:
            logger.error(f"注册表搜索失败: {str(e)}")
            response.success = False
            response.message = f"搜索失败: {str(e)}"
            response.total_results = 0
        
        logger.info(f"注册表搜索完成: 成功={response.success}, 结果数量={response.total_results}")
        return response

    # ================ 注册表操作辅助方法 ================
    
    def _perform_registry_read(self, root_key, sub_key_path, value_name=None):
        """执行注册表读取操作"""
        try:
            # 如果sub_key_path为空，直接访问根键
            if not sub_key_path:
                # 对于根键，我们需要枚举其直接子键
                with winreg.OpenKey(root_key, "", 0, winreg.KEY_READ) as key:
                    if value_name:
                        # 读取特定值
                        try:
                            value, reg_type = winreg.QueryValueEx(key, value_name)
                            value_data = RegistryValue()
                            value_data.name = value_name
                            value_data.type = self._winreg_type_to_proto(reg_type)
                            value_data.data = str(value)
                            value_data.size = len(str(value))
                            return True, f"成功读取值: {value_name}", None, value_data
                        except FileNotFoundError:
                            return False, f"值不存在: {value_name}", None, None
                    else:
                        # 枚举根键的子键和值
                        key_data = RegistryKey()
                        key_data.name = ""  # 根键名称为空
                        key_data.full_path = ""
                        
                        # 枚举子键
                        sub_key_count = 0
                        try:
                            while True:
                                sub_key_name = winreg.EnumKey(key, sub_key_count)
                                key_data.sub_keys.append(sub_key_name)
                                sub_key_count += 1
                        except OSError:
                            pass
                        
                        # 枚举值
                        value_count = 0
                        try:
                            while True:
                                value_name, value_data_raw, reg_type = winreg.EnumValue(key, value_count)
                                value = RegistryValue()
                                value.name = value_name
                                value.type = self._winreg_type_to_proto(reg_type)
                                value.data = str(value_data_raw)
                                value.size = len(str(value_data_raw))
                                key_data.values.append(value)
                                value_count += 1
                        except OSError:
                            pass
                        
                        key_data.sub_key_count = sub_key_count
                        key_data.value_count = value_count
                        key_data.last_modified = int(time.time())
                        
                        return True, f"成功枚举键，子键: {sub_key_count}, 值: {value_count}", key_data, None
            else:
                # 处理有路径的情况
                with winreg.OpenKey(root_key, sub_key_path, 0, winreg.KEY_READ) as key:
                    if value_name:
                        # 读取特定值
                        try:
                            value, reg_type = winreg.QueryValueEx(key, value_name)
                            value_data = RegistryValue()
                            value_data.name = value_name
                            value_data.type = self._winreg_type_to_proto(reg_type)
                            value_data.data = str(value)
                            value_data.size = len(str(value))
                            return True, f"成功读取值: {value_name}", None, value_data
                        except FileNotFoundError:
                            return False, f"值不存在: {value_name}", None, None
                    else:
                        # 枚举键和值
                        key_data = RegistryKey()
                        key_data.name = sub_key_path.split('\\')[-1] if sub_key_path else ""
                        key_data.full_path = sub_key_path
                        
                        # 枚举子键
                        sub_key_count = 0
                        try:
                            while True:
                                sub_key_name = winreg.EnumKey(key, sub_key_count)
                                key_data.sub_keys.append(sub_key_name)
                                sub_key_count += 1
                        except OSError:
                            pass
                        
                        # 枚举值
                        value_count = 0
                        try:
                            while True:
                                value_name, value_data_raw, reg_type = winreg.EnumValue(key, value_count)
                                value = RegistryValue()
                                value.name = value_name
                                value.type = self._winreg_type_to_proto(reg_type)
                                value.data = str(value_data_raw)
                                value.size = len(str(value_data_raw))
                                key_data.values.append(value)
                                value_count += 1
                        except OSError:
                            pass
                        
                        key_data.sub_key_count = sub_key_count
                        key_data.value_count = value_count
                        key_data.last_modified = int(time.time())
                        
                        return True, f"成功枚举键，子键: {sub_key_count}, 值: {value_count}", key_data, None
                    
        except FileNotFoundError:
            return False, f"注册表键不存在: {sub_key_path}", None, None
        except PermissionError:
            # 检查是否是受保护的路径
            if self._is_protected_registry_path(sub_key_path):
                return False, f"没有权限访问: {sub_key_path}", None, None
            else:
                return False, f"没有权限访问: {sub_key_path}", None, None
        except Exception as e:
            return False, f"读取失败: {str(e)}", None, None

    def _perform_registry_write(self, root_key, sub_key_path, value_name, value_type, value_data):
        """执行注册表写入操作"""
        try:
            # 转换protobuf类型到winreg类型
            reg_type = self._proto_type_to_winreg(value_type)
            
            # 转换数据
            if reg_type == winreg.REG_DWORD:
                data = int(value_data)
            elif reg_type == winreg.REG_BINARY:
                data = value_data.encode()
            else:
                data = value_data
            
            with winreg.CreateKey(root_key, sub_key_path) as key:
                winreg.SetValueEx(key, value_name, 0, reg_type, data)
                return True, f"成功写入值: {value_name} = {value_data}"
                
        except Exception as e:
            return False, f"写入失败: {str(e)}"

    def _perform_registry_delete(self, root_key, sub_key_path, value_name):
        """执行注册表值删除操作"""
        try:
            with winreg.OpenKey(root_key, sub_key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.DeleteValue(key, value_name)
                return True, f"成功删除值: {value_name}"
                
        except FileNotFoundError:
            return False, f"值不存在: {value_name}"
        except Exception as e:
            return False, f"删除失败: {str(e)}"

    def _perform_registry_create_key(self, root_key, sub_key_path):
        """执行注册表键创建操作"""
        try:
            winreg.CreateKey(root_key, sub_key_path)
            return True, f"成功创建键: {sub_key_path}"
        except Exception as e:
            return False, f"创建键失败: {str(e)}"

    def _perform_registry_delete_key(self, root_key, sub_key_path):
        """执行注册表键删除操作"""
        try:
            winreg.DeleteKey(root_key, sub_key_path)
            return True, f"成功删除键: {sub_key_path}"
        except Exception as e:
            return False, f"删除键失败: {str(e)}"

    def _perform_registry_enumerate(self, root_key, sub_key_path, page=1, page_size=1000, search_filter=""):
        """执行注册表枚举操作（支持分页）"""
        try:
            if not sub_key_path:
                # 处理根键的情况
                with winreg.OpenKey(root_key, "", 0, winreg.KEY_READ) as key:
                    key_data = RegistryKey()
                    key_data.name = ""
                    key_data.full_path = ""
                    
                    # 先获取总子键数量
                    total_sub_keys = 0
                    all_sub_keys = []
                    try:
                        while True:
                            sub_key_name = winreg.EnumKey(key, total_sub_keys)
                            # 应用搜索过滤器
                            if not search_filter or search_filter.lower() in sub_key_name.lower():
                                all_sub_keys.append(sub_key_name)
                            total_sub_keys += 1
                    except OSError:
                        pass
                    
                    # 计算分页
                    start_idx = (page - 1) * page_size
                    end_idx = start_idx + page_size
                    paged_sub_keys = all_sub_keys[start_idx:end_idx]
                    
                    # 填充分页数据
                    key_data.sub_keys.extend(paged_sub_keys)
                    key_data.sub_key_count = len(paged_sub_keys)
                    key_data.total_sub_keys = len(all_sub_keys)
                    key_data.current_page = page
                    key_data.page_size = page_size
                    key_data.has_more_sub_keys = end_idx < len(all_sub_keys)
                    
                    # 枚举值（不分页，通常根键值很少）
                    value_count = 0
                    try:
                        while True:
                            value_name, value_data_raw, reg_type = winreg.EnumValue(key, value_count)
                            value = RegistryValue()
                            value.name = value_name
                            value.type = self._winreg_type_to_proto(reg_type)
                            value.data = str(value_data_raw)
                            value.size = len(str(value_data_raw))
                            key_data.values.append(value)
                            value_count += 1
                    except OSError:
                        pass
                    
                    key_data.value_count = value_count
                    key_data.last_modified = int(time.time())
                    
                    return True, f"成功枚举键，子键: {len(paged_sub_keys)}/{len(all_sub_keys)}, 值: {value_count}", key_data
            else:
                # 处理有路径的情况
                with winreg.OpenKey(root_key, sub_key_path, 0, winreg.KEY_READ) as key:
                    key_data = RegistryKey()
                    key_data.name = sub_key_path.split('\\')[-1] if sub_key_path else ""
                    key_data.full_path = sub_key_path
                    
                    # 先获取总子键数量
                    total_sub_keys = 0
                    all_sub_keys = []
                    try:
                        while True:
                            sub_key_name = winreg.EnumKey(key, total_sub_keys)
                            # 应用搜索过滤器
                            if not search_filter or search_filter.lower() in sub_key_name.lower():
                                all_sub_keys.append(sub_key_name)
                            total_sub_keys += 1
                    except OSError:
                        pass
                    
                    # 计算分页
                    start_idx = (page - 1) * page_size
                    end_idx = start_idx + page_size
                    paged_sub_keys = all_sub_keys[start_idx:end_idx]
                    
                    # 填充分页数据
                    key_data.sub_keys.extend(paged_sub_keys)
                    key_data.sub_key_count = len(paged_sub_keys)
                    key_data.total_sub_keys = len(all_sub_keys)
                    key_data.current_page = page
                    key_data.page_size = page_size
                    key_data.has_more_sub_keys = end_idx < len(all_sub_keys)
                    
                    # 枚举值（不分页）
                    value_count = 0
                    try:
                        while True:
                            value_name, value_data_raw, reg_type = winreg.EnumValue(key, value_count)
                            value = RegistryValue()
                            value.name = value_name
                            value.type = self._winreg_type_to_proto(reg_type)
                            value.data = str(value_data_raw)
                            value.size = len(str(value_data_raw))
                            key_data.values.append(value)
                            value_count += 1
                    except OSError:
                        pass
                    
                    key_data.value_count = value_count
                    key_data.last_modified = int(time.time())
                    
                    return True, f"成功枚举键，子键: {len(paged_sub_keys)}/{len(all_sub_keys)}, 值: {value_count}", key_data
                    
        except FileNotFoundError:
            return False, f"注册表键不存在: {sub_key_path}", None
        except PermissionError:
            return False, f"没有权限访问: {sub_key_path}", None
        except Exception as e:
            return False, f"枚举失败: {str(e)}", None

    def _perform_registry_backup(self, root_key, sub_key_path, backup_reason):
        """执行注册表备份操作"""
        try:
            backup_id = str(uuid.uuid4())
            backup_dir = "C:\\Registry_Backups"
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_file = os.path.join(backup_dir, f"{backup_id}.reg")
            
            # 这里简化处理，实际应该使用reg export命令
            # 由于winreg模块不直接支持导出，我们模拟一个成功的备份
            with open(backup_file, 'w', encoding='utf-16le') as f:
                f.write(f"Windows Registry Editor Version 5.00\n\n")
                f.write(f"; 备份时间: {datetime.datetime.now()}\n")
                f.write(f"; 备份原因: {backup_reason}\n")
                f.write(f"; 备份路径: {sub_key_path}\n\n")
            
            return True, f"成功创建备份: {backup_file}", backup_id
            
        except Exception as e:
            return False, f"备份失败: {str(e)}", ""

    def _perform_registry_search(self, root_key, start_path, search_pattern, search_keys, search_values, search_data, max_depth, max_results):
        """执行注册表搜索操作"""
        results = []
        current_depth = 0
        
        def search_recursive(current_key_path, depth):
            if depth > max_depth or len(results) >= max_results:
                return
                
            try:
                with winreg.OpenKey(root_key, current_key_path, 0, winreg.KEY_READ) as key:
                    # 搜索子键
                    if search_keys:
                        sub_key_count = 0
                        try:
                            while len(results) < max_results:
                                sub_key_name = winreg.EnumKey(key, sub_key_count)
                                if search_pattern.lower() in sub_key_name.lower():
                                    results.append({
                                        'path': f"{current_key_path}\\{sub_key_name}",
                                        'match_type': 'key',
                                        'match_text': sub_key_name,
                                        'value': None
                                    })
                                
                                # 递归搜索子键
                                if depth < max_depth:
                                    search_recursive(f"{current_key_path}\\{sub_key_name}", depth + 1)
                                    
                                sub_key_count += 1
                        except OSError:
                            pass
                    
                    # 搜索值
                    if search_values and len(results) < max_results:
                        value_count = 0
                        try:
                            while len(results) < max_results:
                                value_name, value_data, reg_type = winreg.EnumValue(key, value_count)
                                
                                # 搜索值名
                                if search_pattern.lower() in value_name.lower():
                                    results.append({
                                        'path': current_key_path,
                                        'match_type': 'value_name',
                                        'match_text': value_name,
                                        'value': {
                                            'name': value_name,
                                            'type': self._winreg_type_to_proto(reg_type),
                                            'data': str(value_data),
                                            'size': len(str(value_data))
                                        }
                                    })
                                
                                # 搜索值数据
                                if search_data and search_pattern.lower() in str(value_data).lower():
                                    results.append({
                                        'path': current_key_path,
                                        'match_type': 'value_data',
                                        'match_text': str(value_data),
                                        'value': {
                                            'name': value_name,
                                            'type': self._winreg_type_to_proto(reg_type),
                                            'data': str(value_data),
                                            'size': len(str(value_data))
                                        }
                                    })
                                
                                value_count += 1
                        except OSError:
                            pass
                            
            except (FileNotFoundError, PermissionError):
                pass
        
        # 开始搜索
        search_recursive(start_path, 0)
        return results

    def _winreg_type_to_proto(self, winreg_type):
        """将winreg类型转换为protobuf类型"""
        mapping = {
            winreg.REG_SZ: RegistryValueType.REG_SZ,
            winreg.REG_EXPAND_SZ: RegistryValueType.REG_EXPAND_SZ,
            winreg.REG_BINARY: RegistryValueType.REG_BINARY,
            winreg.REG_DWORD: RegistryValueType.REG_DWORD,
            winreg.REG_QWORD: RegistryValueType.REG_QWORD,
            winreg.REG_MULTI_SZ: RegistryValueType.REG_MULTI_SZ
        }
        return mapping.get(winreg_type, RegistryValueType.REG_SZ)

    def _proto_type_to_winreg(self, proto_type):
        """将protobuf类型转换为winreg类型"""
        mapping = {
            RegistryValueType.REG_SZ: winreg.REG_SZ,
            RegistryValueType.REG_EXPAND_SZ: winreg.REG_EXPAND_SZ,
            RegistryValueType.REG_BINARY: winreg.REG_BINARY,
            RegistryValueType.REG_DWORD: winreg.REG_DWORD,
            RegistryValueType.REG_QWORD: winreg.REG_QWORD,
            RegistryValueType.REG_MULTI_SZ: winreg.REG_MULTI_SZ
        }
        return mapping.get(proto_type, winreg.REG_SZ)


def serve():
    """启动gRPC服务"""
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # 注册服务
    add_TerminalManagementServicer_to_server(
        TerminalManagementService(), server
    )

    # 设置监听地址和端口
    port = 50051
    server.add_insecure_port(f'[::]:{port}')

    # 启动服务
    server.start()
    logger.info(f"gRPC服务已启动，监听端口: {port}")

    try:
        # 保持服务运行
        server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("收到中断信号，关闭服务...")
        server.stop(0)
        logger.info("gRPC服务已关闭")


if __name__ == "__main__":
    serve()