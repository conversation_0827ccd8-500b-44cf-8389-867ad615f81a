from app.database import SessionLocal
from app.models import User

def check_admin():
    db = SessionLocal()
    try:
        admin = db.query(User).filter(User.username == "admin").first()
        if admin:
            print("管理员账户信息：")
            print(f"用户名: {admin.username}")
            print(f"邮箱: {admin.email}")
            print(f"是否激活: {admin.is_active}")
            print(f"是否是超级管理员: {admin.is_superuser}")
        else:
            print("未找到管理员账户！")
    finally:
        db.close()

if __name__ == "__main__":
    check_admin() 