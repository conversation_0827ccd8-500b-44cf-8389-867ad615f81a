from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, File, UploadFile
from sqlalchemy.orm import Session
import os
import uuid
from datetime import datetime
import re

from app.api.deps import get_db, check_permissions
from app.crud.custom_field import (
    custom_field_crud, 
    asset_custom_field_value_crud,
    inventory_record_custom_field_value_crud
)
from app.schemas.custom_field import (
    CustomFieldCreate, CustomFieldUpdate, CustomFieldResponse, CustomFieldListResponse,
    AssetCustomFieldValueCreate, AssetCustomFieldValueUpdate, AssetCustomFieldValueResponse,
    InventoryRecordCustomFieldValueCreate, InventoryRecordCustomFieldValueUpdate, InventoryRecordCustomFieldValueResponse,
    BatchSetCustomFieldValuesRequest, FieldPresetRequest
)

router = APIRouter()

# ============ 自定义字段管理 API ============

@router.get("/", response_model=CustomFieldListResponse)
def list_custom_fields(
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"])),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    keyword: Optional[str] = None,
    field_type: Optional[str] = None,
    applies_to: Optional[str] = None,
    is_active: Optional[bool] = None
) -> CustomFieldListResponse:
    """获取自定义字段列表"""
    fields = custom_field_crud.search(
        db,
        keyword=keyword,
        field_type=field_type,
        applies_to=applies_to,
        is_active=is_active,
        skip=skip,
        limit=limit
    )
    total = custom_field_crud.get_total(
        db,
        keyword=keyword,
        field_type=field_type,
        applies_to=applies_to,
        is_active=is_active
    )
    
    return CustomFieldListResponse(
        data=fields,
        total=total,
        page=(skip // limit) + 1,
        limit=limit
    )

@router.get("/active", response_model=List[CustomFieldResponse])
def list_active_custom_fields(
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:view", "inventory:view"])),
    applies_to: Optional[str] = None
) -> List[CustomFieldResponse]:
    """获取启用的自定义字段列表"""
    return custom_field_crud.get_active_fields(db, applies_to=applies_to)

@router.post("/", response_model=CustomFieldResponse)
def create_custom_field(
    field: CustomFieldCreate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
) -> CustomFieldResponse:
    """创建自定义字段"""
    # 检查字段名称是否已存在
    existing = custom_field_crud.get_by_name(db, name=field.name)
    if existing:
        raise HTTPException(
            status_code=400, 
            detail=f"字段名称 '{field.name}' 已存在，请使用其他名称"
        )
    
    # 检查字段名称是否符合规范（只允许字母、数字、下划线）
    if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', field.name):
        raise HTTPException(
            status_code=400,
            detail="字段名称只能包含字母、数字和下划线，且必须以字母开头"
        )
    
    return custom_field_crud.create(db, obj_in=field)

@router.get("/{field_id}", response_model=CustomFieldResponse)
def get_custom_field(
    field_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
) -> CustomFieldResponse:
    """获取指定自定义字段"""
    field = custom_field_crud.get(db, id=field_id)
    if not field:
        raise HTTPException(status_code=404, detail="自定义字段不存在")
    return field

@router.put("/{field_id}", response_model=CustomFieldResponse)
def update_custom_field(
    field_id: int,
    field_update: CustomFieldUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
) -> CustomFieldResponse:
    """更新自定义字段"""
    field = custom_field_crud.get(db, id=field_id)
    if not field:
        raise HTTPException(status_code=404, detail="自定义字段不存在")
    
    # 如果要更新字段名称，检查是否重复
    if field_update.name and field_update.name != field.name:
        existing = custom_field_crud.get_by_name(db, name=field_update.name)
        if existing:
            raise HTTPException(
                status_code=400, 
                detail=f"字段名称 '{field_update.name}' 已存在，请使用其他名称"
            )
        
        # 检查字段名称是否符合规范
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', field_update.name):
            raise HTTPException(
                status_code=400,
                detail="字段名称只能包含字母、数字和下划线，且必须以字母开头"
            )
    
    return custom_field_crud.update(db, db_obj=field, obj_in=field_update)

@router.delete("/{field_id}")
def delete_custom_field(
    field_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
):
    """删除自定义字段"""
    field = custom_field_crud.get(db, id=field_id)
    if not field:
        raise HTTPException(status_code=404, detail="自定义字段不存在")
    
    # 使用安全删除方法
    success, message = custom_field_crud.safe_delete_custom_field(db, field_id=field_id)
    
    if not success:
        raise HTTPException(status_code=400, detail=message)
    
    return {"message": message}

@router.post("/batch-sort")
def batch_update_sort_orders(
    field_orders: List[Dict[str, int]],
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
):
    """批量更新字段排序"""
    success = custom_field_crud.update_sort_orders(db, field_orders=field_orders)
    if not success:
        raise HTTPException(status_code=400, detail="更新排序失败")
    return {"message": "排序更新成功"}

@router.post("/presets", response_model=List[CustomFieldResponse])
def create_preset_fields(
    preset_request: FieldPresetRequest,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
) -> List[CustomFieldResponse]:
    """创建预设字段配置"""
    created_fields = custom_field_crud.create_preset_fields(
        db, 
        preset_type=preset_request.preset_type,
        applies_to=preset_request.applies_to
    )
    return created_fields

# ============ 资产自定义字段值 API ============

@router.get("/values/asset/{asset_id}", response_model=List[AssetCustomFieldValueResponse])
def get_asset_custom_field_values(
    asset_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:view"]))
) -> List[AssetCustomFieldValueResponse]:
    """获取资产的自定义字段值"""
    return asset_custom_field_value_crud.get_by_asset(db, asset_id=asset_id)

@router.post("/values/asset/{asset_id}/batch")
def batch_set_asset_custom_field_values(
    asset_id: int,
    request: BatchSetCustomFieldValuesRequest,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:edit"]))
):
    """批量设置资产自定义字段值"""
    # 验证资产是否存在
    from app.crud.asset import asset_crud
    asset = asset_crud.get(db, id=asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="资产不存在")
    
    results = asset_custom_field_value_crud.batch_create_or_update(
        db, asset_id=asset_id, field_values=request.values
    )
    return {"message": "设置成功", "count": len(results)}

@router.put("/values/asset/{value_id}", response_model=AssetCustomFieldValueResponse)
def update_asset_custom_field_value(
    value_id: int,
    value_update: AssetCustomFieldValueUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:edit"]))
) -> AssetCustomFieldValueResponse:
    """更新资产自定义字段值"""
    value = asset_custom_field_value_crud.get(db, id=value_id)
    if not value:
        raise HTTPException(status_code=404, detail="字段值不存在")
    
    return asset_custom_field_value_crud.update(db, db_obj=value, obj_in=value_update)

@router.delete("/values/asset/{value_id}")
def delete_asset_custom_field_value(
    value_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:edit"]))
):
    """删除资产自定义字段值"""
    value = asset_custom_field_value_crud.get(db, id=value_id)
    if not value:
        raise HTTPException(status_code=404, detail="字段值不存在")
    
    asset_custom_field_value_crud.remove(db, id=value_id)
    return {"message": "删除成功"}

# ============ 盘点记录自定义字段值 API ============

@router.get("/values/inventory/{inventory_record_id}", response_model=List[InventoryRecordCustomFieldValueResponse])
def get_inventory_record_custom_field_values(
    inventory_record_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"]))
) -> List[InventoryRecordCustomFieldValueResponse]:
    """获取盘点记录的自定义字段值"""
    return inventory_record_custom_field_value_crud.get_by_record(
        db, inventory_record_id=inventory_record_id
    )

@router.get("/values/inventory/task/{task_id}/asset/{asset_id}", response_model=List[InventoryRecordCustomFieldValueResponse])
def get_inventory_virtual_record_custom_field_values(
    task_id: int,
    asset_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:view"]))
) -> List[InventoryRecordCustomFieldValueResponse]:
    """获取虚拟盘点记录的自定义字段值"""
    return inventory_record_custom_field_value_crud.get_by_record(
        db, task_id=task_id, asset_id=asset_id
    )

@router.post("/values/inventory/{inventory_record_id}/batch")
def batch_set_inventory_record_custom_field_values(
    inventory_record_id: int,
    request: BatchSetCustomFieldValuesRequest,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:edit"]))
):
    """批量设置盘点记录自定义字段值"""
    # 验证盘点记录是否存在
    from app.crud.inventory import inventory_record_crud
    record = inventory_record_crud.get(db, id=inventory_record_id)
    if not record:
        raise HTTPException(status_code=404, detail="盘点记录不存在")
    
    results = inventory_record_custom_field_value_crud.batch_create_or_update(
        db, inventory_record_id=inventory_record_id, field_values=request.values
    )
    return {"message": "设置成功", "count": len(results)}

@router.post("/values/inventory/task/{task_id}/asset/{asset_id}/batch")
def batch_set_inventory_virtual_record_custom_field_values(
    task_id: int,
    asset_id: int,
    request: BatchSetCustomFieldValuesRequest,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["inventory:edit"]))
):
    """批量设置虚拟盘点记录自定义字段值"""
    # 验证任务和资产是否存在
    from app.crud.inventory import inventory_task_crud
    from app.crud.asset import asset_crud
    
    task = inventory_task_crud.get(db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="盘点任务不存在")
    
    asset = asset_crud.get(db, id=asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="资产不存在")
    
    results = inventory_record_custom_field_value_crud.batch_create_or_update(
        db, task_id=task_id, asset_id=asset_id, field_values=request.values
    )
    return {"message": "设置成功", "count": len(results)}

# ============ 文件上传 API ============

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:edit", "inventory:edit"]))
):
    """上传文件（如图片）"""
    # 文件类型验证
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="只支持图片文件")
    
    # 文件大小验证 (5MB)
    if file.size and file.size > 5 * 1024 * 1024:
        raise HTTPException(status_code=400, detail="文件大小不能超过5MB")
    
    # 生成唯一文件名
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # 确保上传目录存在
    upload_dir = "uploads/custom_fields"
    os.makedirs(upload_dir, exist_ok=True)
    
    # 保存文件
    file_path = os.path.join(upload_dir, unique_filename)
    try:
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")
    
    # 返回文件访问URL
    file_url = f"/uploads/custom_fields/{unique_filename}"
    return {
        "filename": file.filename,
        "url": file_url,
        "size": file.size,
        "content_type": file.content_type
    } 