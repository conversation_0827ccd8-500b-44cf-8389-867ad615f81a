# LDAP配置页面完善-用户过滤器和属性映射

## 问题描述
LDAP配置页面缺少用户过滤器和属性映射字段的配置界面，虽然后端已经支持这些功能。

## 影响范围
- 前端：`frontend/src/views/system/components/LdapConfigManagement.vue`
- 功能：LDAP认证配置管理

## 解决方案
在现有LDAP配置表单中添加高级配置折叠面板，包含：
- 用户搜索过滤器（user_search_filter）
- 用户名属性映射（user_name_attr）  
- 邮箱属性映射（user_email_attr）
- 显示名称属性映射（user_display_name_attr）
- 自动创建用户开关（auto_create_user）
- 默认角色选择（default_role）
- 设为默认配置开关（is_default）

## 实施步骤
1. ✅ 在配置表单中添加高级配置折叠面板
2. ✅ 添加用户过滤器和属性映射字段
3. ✅ 为新字段添加表单验证规则
4. ✅ 添加字段说明和样式优化

## 技术细节
- 使用 `el-collapse` 组织高级配置
- 添加字段帮助说明提升用户体验
- 对用户搜索过滤器添加必须包含`{username}`的验证规则
- 提供合理的默认值和占位符

## 测试验证
- [x] 表单字段正确显示
- [ ] 配置数据正确提交到后端
- [ ] LDAP登录使用新配置生效
- [ ] 表单验证规则工作正常

## 完成状态
🟡 进行中 - 前端界面已完成，需要验证功能完整性 