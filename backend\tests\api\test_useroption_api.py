#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试腾讯企业邮箱 useroption API接口
"""

import asyncio
import httpx
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService

async def test_useroption_api():
    """测试useroption API接口"""
    print("=== 测试腾讯企业邮箱 useroption API接口 ===\n")
    
    db = SessionLocal()
    
    try:
        # 1. 初始化API服务
        print("1. 初始化API服务...")
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        print(f"✅ 成功创建API服务")
        print(f"   应用名称: {api_service.app_name}")
        print(f"   基础URL: {api_service.base_url}")
        print(f"   企业ID: {api_service.corp_id}")
        
        # 2. 获取访问令牌
        print("\n2. 获取访问令牌...")
        try:
            access_token = await api_service.get_access_token()
            print(f"✅ 成功获取访问令牌: {access_token[:20]}...")
        except Exception as e:
            print(f"❌ 获取访问令牌失败: {str(e)}")
            return
        
        # 3. 测试 useroption/get 接口
        print("\n3. 测试 useroption/get 接口...")
        test_userid = "<EMAIL>"  # 使用日志中的用户
        
        get_url = f"{api_service.base_url}/useroption/get?access_token={access_token}"
        get_data = {
            "userid": test_userid,
            "type": [1, 2, 3, 4]
        }
        
        print(f"   请求URL: {get_url}")
        print(f"   请求参数: {json.dumps(get_data, ensure_ascii=False)}")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            get_response = await client.post(get_url, json=get_data)
            print(f"   响应状态码: {get_response.status_code}")
            print(f"   响应内容: {get_response.text}")
            
            if get_response.status_code == 200:
                get_result = get_response.json()
                errcode = get_result.get("errcode")
                errmsg = get_result.get("errmsg")
                
                if errcode == 0:
                    print("✅ useroption/get 接口调用成功!")
                    print(f"   返回的用户选项: {get_result.get('option', [])}")
                    
                    # 4. 如果get成功，则测试 useroption/update 接口
                    print("\n4. 测试 useroption/update 接口...")
                    
                    set_url = f"{api_service.base_url}/useroption/update?access_token={access_token}"
                    set_data = {
                        "userid": test_userid,
                        "option": [
                            {"type": 1, "value": "0"},  # 强制安全登录：关闭
                            {"type": 2, "value": "1"},  # IMAP/SMTP服务：开启
                            {"type": 3, "value": "1"},  # POP/SMTP服务：开启
                            {"type": 4, "value": "0"}   # 安全登录：关闭
                        ]
                    }
                    
                    print(f"   请求URL: {set_url}")
                    print(f"   请求参数: {json.dumps(set_data, ensure_ascii=False)}")
                    
                    set_response = await client.post(set_url, json=set_data)
                    print(f"   响应状态码: {set_response.status_code}")
                    print(f"   响应内容: {set_response.text}")
                    
                    if set_response.status_code == 200:
                        set_result = set_response.json()
                        set_errcode = set_result.get("errcode")
                        set_errmsg = set_result.get("errmsg")
                        
                        if set_errcode == 0:
                            print("✅ useroption/update 接口调用成功!")
                        else:
                            print(f"❌ useroption/update 接口返回错误: {set_errcode} - {set_errmsg}")
                    elif set_response.status_code == 404:
                        print("❌ useroption/update 接口返回404 - 接口不存在或已废弃")
                        print("   可能的原因:")
                        print("   1. 该接口在当前API版本中不存在")
                        print("   2. 应用权限不足")
                        print("   3. 接口路径已变更")
                    else:
                        print(f"❌ useroption/update 接口HTTP错误: {set_response.status_code}")
                        
                elif errcode == 602005:
                    print("❌ useroption/get 权限错误 (602005): 接口无权限访问")
                    print("   建议检查功能设置应用的接口权限配置")
                elif errcode == 601004:
                    print("❌ 无效的corpsecret (601004)")
                elif errcode == 601014:
                    print("❌ 访问令牌错误 (601014)")
                else:
                    print(f"❌ useroption/get 其他API错误: {errcode} - {errmsg}")
            elif get_response.status_code == 404:
                print("❌ useroption/get 接口返回404 - 接口不存在")
            else:
                print(f"❌ useroption/get 接口HTTP错误: {get_response.status_code}")
                
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
    finally:
        db.close()

async def test_alternative_api():
    """测试替代方案 - 使用其他可能的API接口"""
    print("\n=== 测试替代API方案 ===\n")
    
    db = SessionLocal()
    
    try:
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        access_token = await api_service.get_access_token()
        
        # 测试是否有其他相关的API接口
        test_apis = [
            "service/get_function",
            "service/set_function", 
            "user/get",
            "user/update"
        ]
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            for api_path in test_apis:
                print(f"测试API: {api_path}")
                url = f"{api_service.base_url}/{api_path}?access_token={access_token}"
                
                if api_path == "user/get":
                    response = await client.get(url, params={"userid": "<EMAIL>"})
                elif api_path in ["service/get_function", "service/set_function"]:
                    response = await client.post(url, json={"type": "1"})
                else:
                    response = await client.post(url, json={})
                
                print(f"   状态码: {response.status_code}")
                if response.status_code != 404:
                    print(f"   响应: {response.text[:200]}...")
                print()
                
    except Exception as e:
        print(f"❌ 替代方案测试失败: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    print("开始测试腾讯企业邮箱 useroption API接口...\n")
    asyncio.run(test_useroption_api())
    asyncio.run(test_alternative_api())
    print("\n=== 测试完成 ===") 