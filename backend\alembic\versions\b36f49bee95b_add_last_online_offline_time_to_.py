"""add_last_online_offline_time_to_terminals

Revision ID: b36f49bee95b
Revises: dbb4c1399289
Create Date: 2025-06-26 09:30:32.116257

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b36f49bee95b'
down_revision: Union[str, None] = 'dbb4c1399289'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加最后在线时间字段
    op.add_column('terminals', sa.Column('last_online_time', sa.DateTime(), nullable=True))
    
    # 添加最后离线时间字段
    op.add_column('terminals', sa.Column('last_offline_time', sa.DateTime(), nullable=True))
    
    # 为新字段添加索引以优化查询性能
    op.create_index('ix_terminals_last_online_time', 'terminals', ['last_online_time'])
    op.create_index('ix_terminals_last_offline_time', 'terminals', ['last_offline_time'])


def downgrade() -> None:
    # 删除索引
    op.drop_index('ix_terminals_last_offline_time', table_name='terminals')
    op.drop_index('ix_terminals_last_online_time', table_name='terminals')
    
    # 删除字段
    op.drop_column('terminals', 'last_offline_time')
    op.drop_column('terminals', 'last_online_time')
