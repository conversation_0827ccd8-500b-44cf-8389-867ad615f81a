# 终端信息上报临时ID映射错误修复

## 问题描述

gRPC服务器在处理终端信息上报时出现PostgreSQL类型错误：

```
(psycopg2.errors.InvalidTextRepresentation) invalid input syntax for type integer: "local-682a9b0c-799c-4c5f-b399-e4ca602ec4c9"
```

## 错误分析

1. **问题根源**：`ReportTerminalInfo` 方法缺少临时ID映射处理逻辑
2. **具体表现**：
   - 终端注册时使用临时ID `local-682a9b0c-799c-4c5f-b399-e4ca602ec4c9`
   - 注册成功并创建了映射关系（临时ID -> 数据库ID 8）
   - 但在信息上报时，直接使用临时ID作为数据库查询条件
   - PostgreSQL期望整数类型，收到字符串导致类型错误

3. **影响范围**：
   - `ReportTerminalInfo` 方法
   - `ReportCommandResult` 方法  
   - `NotifyCommand` 方法

## 解决方案

### 修复内容

为所有相关gRPC方法添加临时ID映射处理逻辑，与 `Heartbeat` 方法保持一致：

#### 1. ReportTerminalInfo 方法修复

```python
# 查找终端
terminal = None
terminal_id = request.terminal_id

# 首先检查是否是临时ID，如果是则查找映射
if terminal_id.startswith("temp-") or terminal_id.startswith("local-"):
    if terminal_id in _temp_id_mapping:
        actual_id = _temp_id_mapping[terminal_id]
        terminal = db.query(models.Terminal).filter(
            models.Terminal.id == actual_id
        ).first()
        logger.debug(f"使用映射查找终端: {terminal_id} -> {actual_id}")
else:
    # 尝试将 terminal_id 转换为整数
    try:
        terminal_id_int = int(terminal_id)
        terminal = db.query(models.Terminal).filter(
            models.Terminal.id == terminal_id_int
        ).first()
    except ValueError:
        logger.warning(f"无效的终端ID格式: '{terminal_id}'")
```

#### 2. ReportCommandResult 方法修复

添加终端ID映射处理逻辑，确保命令查询使用正确的数据库ID。

#### 3. NotifyCommand 方法修复

添加终端ID映射处理逻辑，确保终端状态查询使用正确的数据库ID。

### 修复文件

- `backend/app/grpc/server.py`

### 测试验证

1. 编译检查：`python -m py_compile app/grpc/server.py` ✅
2. 功能测试：等待终端重新连接并上报信息

## 预期效果

1. 终端信息上报不再出现PostgreSQL类型错误
2. 临时ID能够正确映射到数据库ID
3. 所有gRPC方法都能正确处理临时ID

## 相关日志

修复前错误日志：
```
[0] 2025-06-26 08:28:14,542 - app.grpc.server - ERROR - 处理终端信息上报失败: (psycopg2.errors.InvalidTextRepresentation) invalid input syntax for type integer: "local-682a9b0c-799c-4c5f-b399-e4ca602ec4c9"
```

## 后续优化建议

1. **重构临时ID处理**：将临时ID映射逻辑提取为通用方法，避免代码重复
2. **增强错误处理**：添加更详细的错误信息和恢复机制  
3. **监控告警**：添加临时ID映射失败的监控告警

## 完成时间

2025-01-26 