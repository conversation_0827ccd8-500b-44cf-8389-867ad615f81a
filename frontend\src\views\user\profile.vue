<template>
  <div class="profile-container">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><User /></el-icon>
        <h2 class="page-title">用户资料</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>用户</el-breadcrumb-item>
        <el-breadcrumb-item>个人资料</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <el-form 
        ref="formRef"
        :model="userForm"
        :rules="rules"
        label-width="100px"
        class="profile-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" disabled />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        
        <el-form-item label="用户角色">
          <el-tag :type="userForm.is_superuser ? 'danger' : 'info'">
            {{ userForm.is_superuser ? '超级管理员' : '普通用户' }}
          </el-tag>
        </el-form-item>
        
        <el-form-item label="修改密码" prop="newPassword">
          <el-input 
            v-model="userForm.newPassword" 
            type="password" 
            placeholder="不修改请留空"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="userForm.confirmPassword" 
            type="password" 
            placeholder="不修改请留空"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存修改
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '../../stores/user'
import { ElMessage } from 'element-plus'
import { User } from '@element-plus/icons-vue'

const userStore = useUserStore()
const loading = ref(false)

const userForm = ref({
  username: '',
  email: '',
  is_superuser: false,
  newPassword: '',
  confirmPassword: ''
})

const validatePass = (rule, value, callback) => {
  if (value && value !== userForm.value.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const rules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validatePass, trigger: 'blur' }
  ]
}

onMounted(async () => {
  const userInfo = userStore.userInfo
  if (userInfo) {
    userForm.value = {
      username: userInfo.username,
      email: userInfo.email,
      is_superuser: userInfo.is_superuser,
      newPassword: '',
      confirmPassword: ''
    }
  }
})

const handleSubmit = async () => {
  // TODO: 实现更新用户信息的功能
  ElMessage.success('更新成功')
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  max-width: 600px;
  margin: 0 auto 20px;
}

.profile-form {
  margin-top: 20px;
}
</style> 