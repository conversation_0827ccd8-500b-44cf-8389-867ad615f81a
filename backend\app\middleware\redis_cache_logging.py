import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging
from typing import Optional, Dict, List
import json
from app.utils.redis_cache import RedisCache
from app.utils.auth import verify_token
from app.core.cache_config import (
    <PERSON>ache<PERSON>ataT<PERSON>, <PERSON>ache<PERSON>eyManager, cache_config_manager,
    get_data_type_for_business, get_ttl_for_business
)

logger = logging.getLogger('redis_cache')

class RedisCacheMetrics:
    """Redis缓存请求指标收集器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置指标数据"""
        self.metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_cache_time": 0,
            "slow_cache_operations": []
        }
    
    def record_request(self, path: str, query_params: str, hit: bool, duration: float):
        """记录单个请求的缓存情况"""
        self.metrics["total_requests"] += 1
        
        if hit:
            self.metrics["cache_hits"] += 1
        else:
            self.metrics["cache_misses"] += 1
            
        self.metrics["total_cache_time"] += duration
        
        # 记录慢缓存操作（超过100ms的操作）
        if duration > 0.1:
            self.metrics["slow_cache_operations"].append({
                "path": path,
                "query_params": query_params,
                "hit": hit,
                "duration": duration
            })
            
            # 只保留最近20个慢操作记录
            if len(self.metrics["slow_cache_operations"]) > 20:
                self.metrics["slow_cache_operations"].pop(0)
    
    def get_metrics(self) -> Dict:
        """获取指标数据"""
        metrics = self.metrics.copy()
        
        # 计算命中率
        if metrics["total_requests"] > 0:
            metrics["hit_ratio"] = metrics["cache_hits"] / metrics["total_requests"]
            metrics["hit_ratio_percent"] = f"{metrics['hit_ratio'] * 100:.2f}%"
        else:
            metrics["hit_ratio"] = 0
            metrics["hit_ratio_percent"] = "0.00%"
        
        # 计算平均缓存时间
        if metrics["total_requests"] > 0:
            metrics["avg_cache_time"] = metrics["total_cache_time"] / metrics["total_requests"]
            metrics["avg_cache_time_ms"] = f"{metrics['avg_cache_time'] * 1000:.2f}ms"
        else:
            metrics["avg_cache_time"] = 0
            metrics["avg_cache_time_ms"] = "0.00ms"
            
        return metrics


# 创建全局缓存指标收集器
cache_metrics = RedisCacheMetrics()


class CacheKeyGenerator:
    """缓存键生成器"""
    
    @staticmethod
    def generate_cache_key(request: Request) -> str:
        """根据请求生成缓存键"""
        path = request.url.path
        query_params = dict(request.query_params)
        if "_t" in query_params:
            del query_params["_t"]
        
        # 按键排序以确保查询字符串的一致性
        sorted_query_params = sorted(query_params.items())
        query = "&".join([f"{k}={v}" for k, v in sorted_query_params]) if sorted_query_params else ""
        
        method = request.method
        
        # 如果是GET请求，使用路径+查询参数作为缓存键
        if method == "GET":
            return f"request_cache:{path}:{query}"
        
        # 其他类型请求不使用缓存，返回None
        return None

    @staticmethod
    def get_cache_prefix_for_path(path: str) -> str:
        """
        根据请求路径获取缓存前缀
        例如：/api/v1/users/123 -> request_cache:/api/v1/users
        例如：/api/v1/ad/management/users/123/toggle -> request_cache:/api/v1/ad/management/users
        """
        # 移除最后一个路径段（如果是ID或动作）
        parts = path.rstrip('/').split('/')
        
        # 从末尾开始检查并移除ID或动作路径段
        while parts and (parts[-1].isdigit() or parts[-1] in ['toggle', 'move', 'fix-cn', 'active', 'superuser']):
            parts.pop()
            
        base_path = '/'.join(parts)
        return f"request_cache:{base_path}"
        
    @staticmethod
    def extract_username_from_token(token: str) -> Optional[str]:
        """从token中提取用户名"""
        if not token:
            return None
            
        try:
            # 使用verify_token函数从token中提取用户名
            username = verify_token(token)
            return username
        except Exception:
            # token解析失败时静默返回None，避免影响缓存功能
            return None


class RedisCacheMiddleware(BaseHTTPMiddleware):
    """Redis缓存中间件"""
    
    def __init__(self, app, ttl: int = 300):
        super().__init__(app)
        self.redis_cache = RedisCache()
        self.default_ttl = ttl  # 默认缓存5分钟（作为fallback）
        
        # 不缓存的路径列表
        self.exclude_paths = [
            "/api/v1/auth/login",
            "/api/v1/system/monitoring",
            "/api/v1/ad/sync-config/logs",  # 添加AD同步日志API路径，确保实时获取同步日志
            "/api/v1/ad/sync-from-personnel",  # 添加AD与人员同步API路径
            "/api/v1/ad/sync-from-personnel2",  # 添加AD与人员同步备用API路径
            "/api/v1/ad/sync-organization-structure",  # 添加AD组织结构同步API路径
            "/api/v1/ad_sync_config/config",  # 添加AD同步配置API路径
            "/api/v1/ad_sync_config/run-sync",  # 添加手动运行同步API路径
            "/api/v1/ecology/users",  # 添加泛微用户数据API路径
            "/api/v1/ecology/departments",  # 添加泛微部门数据API路径
            "/api/v1/ecology/companies",  # 添加泛微公司数据API路径
            "/api/v1/ecology/users/by-company",  # 添加按公司筛选用户API
            "/api/v1/ecology/users/by-department",  # 添加按部门筛选用户API
            "/api/v1/ecology/users/by-department-tree",  # 添加按部门树筛选用户API
            "/api/v1/ecology/departments/by-company",  # 添加按公司筛选部门API
            "/api/v1/ecology/departments/by-parent",  # 添加按父部门筛选部门API
            "/api/v1/ecology/departments/by-parent-recursive",  # 添加递归获取子部门API
            "/api/v1/email/sync/logs",  # 邮箱同步日志列表 - 确保实时获取最新同步日志
            "/api/v1/email/sync/latest-times",  # 邮箱最新同步时间 - 确保实时获取最新同步状态
            "/api/v1/email/sync/logs/",  # 邮箱同步日志详情 - 支持单个日志查看的实时性
            "/api/v1/email-personnel-sync/extid-completion/candidates",  # 工号补全候选者 - 使用应用级Redis缓存
            "/api/v1/sse/",  # 排除所有 SSE 路径，避免中间件干扰 SSE 流式响应
        ]
        
        # 缓存不检查权限的路径
        self.public_paths = [
            "/api/v1/assets/public",
            "/api/v1/system/info"
        ]
        
        # 路径到业务键的映射（用于智能TTL选择）
        self.path_business_mapping = {
            "/api/v1/ad/users": "ad_users",
            "/api/v1/ad/groups": "ad_groups", 
            "/api/v1/ad/sync-config/logs": "ad_sync_logs",
            "/api/v1/ad/config": "ad_config",
            "/api/v1/email/members": "email_members",
            "/api/v1/email/departments": "email_departments", 
            "/api/v1/email/sync/logs": "email_sync_logs",
            "/api/v1/email/config": "email_config",
            "/api/v1/assets": "assets",
            "/api/v1/assets/types": "asset_types",
            "/api/v1/terminals": "terminals",
            "/api/v1/terminals/status": "terminal_status",
            "/api/v1/users": "users",
            "/api/v1/users/permissions": "user_permissions",
            "/api/v1/system/config": "system_config",
            "/api/v1/system/monitoring": "monitoring_metrics",
        }
    
    def _get_business_key_for_path(self, path: str) -> Optional[str]:
        """根据请求路径获取业务键"""
        # 精确匹配
        if path in self.path_business_mapping:
            return self.path_business_mapping[path]
        
        # 前缀匹配（支持带ID的路径）
        for mapped_path, business_key in self.path_business_mapping.items():
            if path.startswith(mapped_path):
                return business_key
        
        return None
    
    def _get_smart_ttl_for_path(self, path: str) -> int:
        """根据路径智能选择TTL"""
        business_key = self._get_business_key_for_path(path)
        if business_key:
            return get_ttl_for_business(business_key)
        return self.default_ttl
    
    def _get_cache_dependencies(self, path: str) -> List[str]:
        """获取缓存依赖模式列表
        
        返回当前路径相关的需要清除的缓存模式
        """
        dependencies = []
        
        # 基本规则：当前路径的所有相关缓存
        base_path_prefix = CacheKeyGenerator.get_cache_prefix_for_path(path)
        dependencies.append(f"{base_path_prefix}*")
        
        # 特殊依赖关系
        if "/email/members" in path:
            # 邮箱成员变更影响部门列表
            dependencies.append("request_cache:/api/v1/email/departments*")
        elif "/ad/management/users" in path or "/ad/users" in path:
            # AD用户变更影响用户列表和组信息
            dependencies.append("request_cache:/api/v1/ad/management/users*")
            dependencies.append("request_cache:/api/v1/ad/users*")
            dependencies.append("request_cache:/api/v1/ad/groups*")
        elif "/assets" in path:
            # 资产变更影响统计信息
            dependencies.append("request_cache:/api/v1/assets/statistics*")
        elif "/users" in path:
            # 用户变更影响权限缓存
            dependencies.append("request_cache:/api/v1/users/permissions*")
        elif "/inventory" in path:
            # 盘点记录变更影响盘点任务列表和统计信息
            if "/records" in path:
                # 盘点记录更新需要清除多个相关缓存
                dependencies.append("request_cache:/api/v1/inventory/tasks*")  # 清除所有盘点任务相关缓存
                dependencies.append("request_cache:/api/v1/inventory*")        # 清除所有盘点相关缓存
            elif "/tasks" in path:
                # 盘点任务更新也需要清除相关缓存
                dependencies.append("request_cache:/api/v1/inventory*")        # 清除所有盘点相关缓存
        
        return dependencies
    
    async def dispatch(self, request: Request, call_next):
        # 获取请求路径和方法
        path = request.url.path
        method = request.method
        
        # 如果路径在排除列表中，跳过缓存
        if any(path.startswith(prefix) for prefix in self.exclude_paths):
            return await call_next(request)

        # 处理GET请求的缓存逻辑
        if method == "GET":
            # 生成缓存键
            cache_key = CacheKeyGenerator.generate_cache_key(request)
            
            # 如果无法生成缓存键，跳过缓存
            if not cache_key:
                return await call_next(request)
            
            # 检查是否需要认证的路径
            is_public = any(path.startswith(prefix) for prefix in self.public_paths)
            
            # 对需要认证的路径，在缓存键中加入用户信息
            if not is_public:
                # 获取授权头
                auth_header = request.headers.get("Authorization")
                if auth_header and auth_header.startswith("Bearer "):
                    # 提取完整token
                    token = auth_header[7:]
                    # 从token提取用户名，而不是使用token片段
                    username = CacheKeyGenerator.extract_username_from_token(token)
                    if username:
                        # 使用用户名作为缓存键的一部分，而不是token
                        cache_key = f"{cache_key}:user:{username}"
            
            # 尝试从缓存获取响应
            start_time = time.time()
            cached_response = self.redis_cache.get(cache_key)
            cache_time = time.time() - start_time
            
            # 记录缓存尝试
            if cached_response:
                # 缓存命中
                logger.info(
                    f"缓存命中: {path}, 键: {cache_key}, 耗时: {cache_time:.6f}s",
                    extra={
                        "path": path,
                        "cache_key": cache_key,
                        "duration": cache_time,
                        "hit": True
                    }
                )
                
                # 记录指标
                cache_metrics.record_request(path, str(request.query_params), True, cache_time)
                
                # 恢复响应对象
                response_data, status_code, headers = cached_response
                response = Response(content=response_data, status_code=status_code, headers=dict(headers))
                return response
            
            # 缓存未命中，记录
            logger.info(
                f"缓存未命中: {path}, 键: {cache_key}, 耗时: {cache_time:.6f}s",
                extra={
                    "path": path,
                    "cache_key": cache_key,
                    "duration": cache_time,
                    "hit": False
                }
            )
            
            # 记录指标
            cache_metrics.record_request(path, str(request.query_params), False, cache_time)
            
            # 执行实际请求
            response = await call_next(request)
            
            # 如果是成功的GET请求，缓存响应
            if response.status_code == 200:
                # 获取响应内容
                response_body = [chunk async for chunk in response.body_iterator]
                response.body_iterator = iterate_bytes_chunks(response_body)
                
                # 缓存响应
                response_data = b"".join(response_body)
                cache_data = (response_data, response.status_code, list(response.headers.items()))
                
                # 智能TTL选择
                smart_ttl = self._get_smart_ttl_for_path(path)
                business_key = self._get_business_key_for_path(path)
                
                # 设置缓存，使用智能TTL
                self.redis_cache.set(cache_key, cache_data, ttl=smart_ttl)
                
                logger.debug(
                    f"缓存响应: {path}, 键: {cache_key}, TTL: {smart_ttl}秒, 业务类型: {business_key or 'default'}",
                    extra={
                        "path": path,
                        "cache_key": cache_key,
                        "ttl": smart_ttl,
                        "business_key": business_key
                    }
                )
            
            return response
        else:
            # 对于非GET请求（如POST, PUT, DELETE等），执行请求
            response = await call_next(request)
            
            # 如果请求成功（状态码2xx），自动清除相关路径的缓存
            # 注意：OPTIONS请求不应该清除缓存，这是浏览器的预检请求
            if response.status_code >= 200 and response.status_code < 300 and method != "OPTIONS":
                # 获取缓存依赖模式列表
                dependency_patterns = self._get_cache_dependencies(path)
                
                total_keys_cleared = 0
                cleared_patterns = []
                
                # 清除所有依赖的缓存
                for pattern in dependency_patterns:
                    keys_cleared = self.redis_cache.clear_pattern(pattern)
                    total_keys_cleared += keys_cleared
                    if keys_cleared > 0:
                        cleared_patterns.append(f"{pattern}({keys_cleared})")
                
                logger.info(
                    f"智能清除缓存: 方法={method}, 路径={path}, 总清除键数量={total_keys_cleared}, 模式={cleared_patterns}",
                    extra={
                        "method": method,
                        "path": path,
                        "dependency_patterns": dependency_patterns,
                        "total_keys_cleared": total_keys_cleared,
                        "cleared_patterns": cleared_patterns
                    }
                )
            
            return response


async def iterate_bytes_chunks(chunks):
    """迭代字节块"""
    for chunk in chunks:
        yield chunk 