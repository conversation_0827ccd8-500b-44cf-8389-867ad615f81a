# 权限管理系统说明

## 默认权限设计

我们已经为系统创建了一套完整的默认权限，按功能模块划分如下：

### 系统管理模块 (system)
- `system:view` - 查看系统设置
- `system:config` - 配置系统参数
- `system:user:view` - 查看用户管理
- `system:user:add` - 添加用户
- `system:user:edit` - 编辑用户
- `system:user:delete` - 删除用户
- `system:role:view` - 查看角色
- `system:role:add` - 添加角色
- `system:role:edit` - 编辑角色
- `system:role:delete` - 删除角色
- `system:permission:view` - 查看权限
- `system:permission:add` - 添加权限
- `system:permission:edit` - 编辑权限
- `system:permission:delete` - 删除权限

### 基础信息模块 (basic-info)
- `basic-info:view` - 查看基础信息
- `basic-info:personnel:view` - 查看人员信息
- `basic-info:personnel:edit` - 编辑人员信息

### 资产管理模块 (asset)
- `asset:view` - 查看资产
- `asset:add` - 添加资产
- `asset:edit` - 编辑资产
- `asset:delete` - 删除资产
- `asset:import` - 导入资产
- `asset:export` - 导出资产
- `asset:field:manage` - 管理资产字段

### 盘点管理模块 (inventory)
- `inventory:view` - 查看盘点任务
- `inventory:add` - 创建盘点任务
- `inventory:edit` - 编辑盘点任务
- `inventory:delete` - 删除盘点任务
- `inventory:record:view` - 查看盘点记录
- `inventory:record:edit` - 编辑盘点记录
- `inventory:report` - 查看盘点报告

### AD管理模块 (ad)
- `ad:view` - 查看AD用户
- `ad:sync` - 同步AD数据
- `ad:config` - 配置AD连接
- `ad:user:manage` - 管理AD用户
- `ad:group:manage` - 管理AD组

### 泛微集成模块 (ecology)
- `ecology:view` - 查看泛微用户
- `ecology:sync` - 同步泛微数据
- `ecology:config` - 配置泛微连接

## 默认角色设计

我们创建了以下三个默认角色，并为它们分配了不同的权限：

### 超级管理员 (super_admin)
- 描述：拥有所有权限的管理员角色
- 权限：系统中的全部权限

### 资产管理员 (asset_admin)
- 描述：管理资产和盘点的角色
- 权限：
  - 所有资产管理权限
  - 盘点查看权限
  - 盘点记录查看和编辑权限

### 普通用户 (normal_user)
- 描述：普通用户角色
- 权限：
  - 资产查看权限
  - 盘点记录查看权限
  - 盘点记录编辑权限

## 如何添加新权限

1. 在系统管理 -> 权限管理界面点击"添加权限"
2. 填写权限代码（遵循"模块:操作"格式）
3. 填写权限名称
4. 选择所属模块
5. 添加描述（可选）
6. 点击确定保存

## 如何分配权限

1. 在系统管理 -> 角色管理界面选择需要编辑的角色
2. 点击"编辑"按钮
3. 在权限列表中勾选需要分配的权限
4. 点击确定保存 