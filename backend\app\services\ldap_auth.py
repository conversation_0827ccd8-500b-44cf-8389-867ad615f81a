from typing import Optional, Dict, Any
from ldap3 import Server, Connection, ALL, SIMPLE, SUBTREE, BASE
from sqlalchemy.orm import Session
import logging
from .. import models, schemas
from ..utils.auth import create_access_token, get_password_hash
from ..utils.ip_matcher import IPMatcher
from ..crud.user import user_crud

logger = logging.getLogger(__name__)

class LdapAuthService:
    def __init__(self, db: Session):
        self.db = db
        self.logger = logging.getLogger(__name__)

    def get_default_ldap_config(self) -> Optional[models.LdapConfig]:
        """获取默认的LDAP配置"""
        config = self.db.query(models.LdapConfig).filter(
            models.LdapConfig.is_default == True,
            models.LdapConfig.is_active == True
        ).first()
        
        if not config:
            # 如果没有默认配置，获取第一个启用的配置
            config = self.db.query(models.LdapConfig).filter(
                models.LdapConfig.is_active == True
            ).first()
        
        return config

    def get_all_active_configs(self) -> list[models.LdapConfig]:
        """获取所有启用的LDAP配置"""
        return self.db.query(models.LdapConfig).filter(
            models.LdapConfig.is_active == True
        ).order_by(models.LdapConfig.priority.asc()).all()

    def select_config_by_ip(self, client_ip: str) -> Optional[tuple[models.LdapConfig, str]]:
        """根据客户端IP自动选择LDAP配置"""
        try:
            # 获取所有启用的配置
            all_configs = self.get_all_active_configs()
            
            if not all_configs:
                logger.warning("没有找到任何启用的LDAP配置")
                return None
            
            # 尝试基于IP匹配
            result = IPMatcher.get_best_config(client_ip, all_configs)
            if result:
                config, match_reason = result
                logger.info(f"基于IP {client_ip} 自动选择配置: {config.name} ({match_reason})")
                return config, match_reason
            
            # 如果没有IP匹配，使用备用配置
            fallback_config = IPMatcher.get_fallback_config(all_configs)
            if fallback_config:
                match_reason = "默认配置（无IP匹配）"
                logger.info(f"使用备用配置: {fallback_config.name}")
                return fallback_config, match_reason
            
            logger.error("无法选择任何LDAP配置")
            return None
            
        except Exception as e:
            logger.error(f"自动选择LDAP配置时出错: {str(e)}")
            # 降级到默认配置
            fallback_config = self.get_default_ldap_config()
            if fallback_config:
                return fallback_config, "异常降级到默认配置"
            return None

    def get_ldap_config_by_id(self, config_id: int) -> Optional[models.LdapConfig]:
        """根据ID获取LDAP配置"""
        return self.db.query(models.LdapConfig).filter(
            models.LdapConfig.id == config_id,
            models.LdapConfig.is_active == True
        ).first()

    def test_connection(self, config: models.LdapConfig) -> bool:
        """测试LDAP连接"""
        try:
            server = Server(
                config.server,
                port=config.port,
                use_ssl=config.use_ssl,
                get_info=ALL
            )

            # 如果有绑定用户，使用绑定用户连接
            if config.bind_dn and config.bind_password:
                conn = Connection(
                    server,
                    user=config.bind_dn,
                    password=config.bind_password,
                    authentication=SIMPLE,
                    auto_bind=True
                )
            else:
                # 匿名连接
                conn = Connection(server, auto_bind=True)

            # 测试搜索
            success = conn.search(
                search_base=config.base_dn,
                search_filter='(objectClass=*)',
                search_scope=SUBTREE,
                attributes=['*'],
                size_limit=1
            )

            conn.unbind()
            return success

        except Exception as e:
            self.logger.error(f"LDAP连接测试失败: {str(e)}")
            return False

    def authenticate_user(self, username: str, password: str, config: Optional[models.LdapConfig] = None) -> Optional[Dict[str, Any]]:
        """LDAP用户认证"""
        try:
            if not config:
                config = self.get_default_ldap_config()
            
            if not config:
                self.logger.error("未找到可用的LDAP配置")
                return None

            # 创建LDAP服务器连接
            server = Server(
                config.server,
                port=config.port,
                use_ssl=config.use_ssl,
                get_info=ALL
            )

            # 查找用户DN
            user_dn = self._find_user_dn(server, config, username)
            if not user_dn:
                self.logger.warning(f"未找到用户: {username}")
                return None

            # 尝试使用用户凭据绑定
            user_conn = Connection(
                server,
                user=user_dn,
                password=password,
                authentication=SIMPLE,
                auto_bind=False
            )

            if not user_conn.bind():
                self.logger.warning(f"用户认证失败: {username}")
                return None

            # 获取用户信息
            user_info = self._get_user_info(user_conn, user_dn, config)
            user_conn.unbind()

            if user_info:
                self.logger.info(f"LDAP用户认证成功: {username}")
                return user_info

            return None

        except Exception as e:
            self.logger.error(f"LDAP认证过程出错: {str(e)}")
            return None

    def _find_user_dn(self, server: Server, config: models.LdapConfig, username: str) -> Optional[str]:
        """查找用户DN"""
        try:
            # 使用绑定用户连接来搜索
            if config.bind_dn and config.bind_password:
                search_conn = Connection(
                    server,
                    user=config.bind_dn,
                    password=config.bind_password,
                    authentication=SIMPLE,
                    auto_bind=True
                )
                self.logger.debug(f"使用绑定用户连接: {config.bind_dn}")
            else:
                # 匿名连接
                search_conn = Connection(server, auto_bind=True)
                self.logger.debug("使用匿名连接进行搜索")

            # 构建搜索过滤器
            search_filter = config.user_search_filter.format(username=username)
            search_base = config.user_search_base or config.base_dn

            # 记录详细搜索参数
            self.logger.info(f"LDAP用户搜索参数 - 用户名: {username}")
            self.logger.info(f"LDAP用户搜索参数 - 搜索基础DN: {search_base}")
            self.logger.info(f"LDAP用户搜索参数 - 搜索过滤器: {search_filter}")
            self.logger.info(f"LDAP用户搜索参数 - LDAP服务器: {config.server}:{config.port}")

            # 搜索用户
            success = search_conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName']
            )

            # 记录搜索结果
            self.logger.info(f"LDAP搜索执行结果: success={success}")
            if hasattr(search_conn, 'result'):
                result_info = search_conn.result
                self.logger.info(f"LDAP搜索结果详情: {result_info}")
            
            if success and search_conn.entries:
                self.logger.info(f"找到 {len(search_conn.entries)} 个匹配的用户")
                for i, entry in enumerate(search_conn.entries):
                    self.logger.info(f"用户 {i+1}: DN={entry.entry_dn}")
                    if hasattr(entry, 'sAMAccountName'):
                        self.logger.info(f"用户 {i+1}: sAMAccountName={entry.sAMAccountName.value}")
                
                user_dn = search_conn.entries[0].entry_dn
                self.logger.info(f"选择第一个用户DN: {user_dn}")
                search_conn.unbind()
                return user_dn
            else:
                self.logger.warning(f"LDAP搜索未找到任何匹配用户")
                self.logger.warning(f"搜索条件: base={search_base}, filter={search_filter}")
                
                # 尝试诊断问题
                self._diagnose_search_issue(search_conn, config, username, search_base, search_filter)

            search_conn.unbind()
            return None

        except Exception as e:
            self.logger.error(f"查找用户DN失败: {str(e)}")
            self.logger.error(f"搜索参数: username={username}, base_dn={config.base_dn}, user_search_base={config.user_search_base}")
            return None

    def _diagnose_search_issue(self, conn: Connection, config: models.LdapConfig, username: str, search_base: str, search_filter: str):
        """诊断搜索问题"""
        try:
            # 1. 测试搜索基础DN是否存在
            self.logger.info("开始诊断搜索问题...")
            
            base_success = conn.search(
                search_base=search_base,
                search_filter='(objectClass=*)',
                search_scope=BASE,
                attributes=['distinguishedName']
            )
            
            if not base_success:
                self.logger.error(f"搜索基础DN不存在或无法访问: {search_base}")
            else:
                self.logger.info(f"搜索基础DN存在: {search_base}")
            
            # 2. 测试在基础DN下是否有任何用户
            users_success = conn.search(
                search_base=search_base,
                search_filter='(&(objectClass=user)(sAMAccountName=*))',
                search_scope=SUBTREE,
                attributes=['sAMAccountName'],
                size_limit=5
            )
            
            if users_success and conn.entries:
                self.logger.info(f"在 {search_base} 下找到 {len(conn.entries)} 个用户示例:")
                for entry in conn.entries[:3]:  # 只显示前3个
                    if hasattr(entry, 'sAMAccountName'):
                        self.logger.info(f"  示例用户: {entry.sAMAccountName.value}")
            else:
                self.logger.warning(f"在 {search_base} 下未找到任何用户")
            
            # 3. 尝试在更广泛的范围内搜索目标用户
            if config.user_search_base and config.user_search_base != config.base_dn:
                self.logger.info(f"尝试在更广泛的范围内搜索用户: {config.base_dn}")
                broad_success = conn.search(
                    search_base=config.base_dn,
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=['distinguishedName', 'sAMAccountName']
                )
                
                if broad_success and conn.entries:
                    self.logger.info(f"在更广泛范围内找到用户: {conn.entries[0].entry_dn}")
                    self.logger.info(f"建议将user_search_base设置为包含此用户的OU")
                else:
                    self.logger.warning(f"在整个域中也未找到用户 {username}")
            
        except Exception as e:
            self.logger.error(f"诊断过程出错: {str(e)}")

    def _get_user_info(self, conn: Connection, user_dn: str, config: models.LdapConfig) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            # 获取用户属性
            attributes = [
                config.user_name_attr,
                config.user_email_attr,
                config.user_display_name_attr,
                'distinguishedName'
            ]

            success = conn.search(
                search_base=user_dn,
                search_filter='(objectClass=*)',
                search_scope=BASE,
                attributes=attributes
            )

            if not success or not conn.entries:
                return None

            entry = conn.entries[0]
            
            # 提取用户信息
            user_info = {
                'username': getattr(entry, config.user_name_attr).value if hasattr(entry, config.user_name_attr) else None,
                'email': getattr(entry, config.user_email_attr).value if hasattr(entry, config.user_email_attr) else None,
                'display_name': getattr(entry, config.user_display_name_attr).value if hasattr(entry, config.user_display_name_attr) else None,
                'dn': user_dn
            }

            return user_info

        except Exception as e:
            self.logger.error(f"获取用户信息失败: {str(e)}")
            return None

    def create_or_update_local_user(self, ldap_user_info: Dict[str, Any], config: models.LdapConfig) -> Optional[models.User]:
        """创建或更新本地用户"""
        try:
            username = ldap_user_info.get('username')
            if not username:
                return None

            # 查找现有用户
            existing_user = user_crud.get_by_username(self.db, username=username)
            
            if existing_user:
                # 更新现有用户信息
                if ldap_user_info.get('email'):
                    existing_user.email = ldap_user_info['email']
                
                # 检查并更新用户角色（如果LDAP配置的默认角色发生了变化）
                if config.default_role:
                    from ..crud.role import role_crud
                    default_role = role_crud.get_by_code(self.db, code=config.default_role)
                    if default_role:
                        # 检查用户是否已有此角色
                        has_role = any(role.code == config.default_role for role in existing_user.roles)
                        
                        if not has_role:
                            if existing_user.roles is None:
                                existing_user.roles = []
                            existing_user.roles.append(default_role)
                            self.logger.info(f"为现有用户 {username} 添加默认角色: {config.default_role}")
                            
                            # 如果是超级管理员角色，同时设置is_superuser标志
                            if config.default_role == "super_admin" and not existing_user.is_superuser:
                                existing_user.is_superuser = True
                                self.logger.info(f"设置现有用户 {username} 为超级管理员")
                
                self.db.commit()
                self.db.refresh(existing_user)
                return existing_user
            else:
                # 检查是否允许自动创建用户
                if not config.auto_create_user:
                    self.logger.warning(f"配置不允许自动创建用户: {username}")
                    return None

                # 创建新用户
                user_data = {
                    'username': username,
                    'email': ldap_user_info.get('email', ''),
                    'password': 'ldap_user',  # LDAP用户不使用本地密码
                    'is_active': True,
                    'is_superuser': False
                }

                new_user = user_crud.create(self.db, obj_in=schemas.UserCreate(**user_data))
                
                # 分配默认角色
                if config.default_role:
                    from ..crud.role import role_crud
                    default_role = role_crud.get_by_code(self.db, code=config.default_role)
                    if default_role:
                        if new_user.roles is None:
                            new_user.roles = []
                        new_user.roles.append(default_role)
                        self.db.commit()
                        self.db.refresh(new_user)
                        self.logger.info(f"为用户 {username} 分配默认角色: {config.default_role} (角色ID: {default_role.id})")
                        
                        # 如果是超级管理员角色，同时设置is_superuser标志
                        if config.default_role == "super_admin":
                            new_user.is_superuser = True
                            self.db.commit()
                            self.db.refresh(new_user)
                            self.logger.info(f"设置用户 {username} 为超级管理员")
                    else:
                        self.logger.error(f"配置的默认角色 '{config.default_role}' 不存在，用户 {username} 未分配任何角色")
                else:
                    self.logger.warning(f"LDAP配置未指定默认角色，用户 {username} 未分配任何角色")

                self.logger.info(f"创建新的LDAP用户: {username}")
                return new_user

        except Exception as e:
            self.logger.error(f"创建或更新本地用户失败: {str(e)}")
            self.db.rollback()
            return None

    def login(self, username: str, password: str, config_id: Optional[int] = None, client_ip: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """LDAP登录"""
        try:
            config = None
            match_reason = ""
            
            # 获取LDAP配置
            if config_id:
                # 如果指定了配置ID，直接使用
                config = self.get_ldap_config_by_id(config_id)
                match_reason = f"手动指定配置ID: {config_id}"
                if config:
                    self.logger.info(f"使用手动指定的LDAP配置: {config.name}")
            else:
                # 如果没有指定配置，尝试自动选择
                if client_ip:
                    result = self.select_config_by_ip(client_ip)
                    if result:
                        config, match_reason = result
                        self.logger.info(f"基于IP {client_ip} 自动选择配置: {config.name}")
                
                # 如果自动选择失败，使用默认配置
                if not config:
                    config = self.get_default_ldap_config()
                    match_reason = "默认配置（自动选择失败）"

            if not config:
                self.logger.error("未找到可用的LDAP配置")
                return None

            # LDAP认证
            ldap_user_info = self.authenticate_user(username, password, config)
            if not ldap_user_info:
                return None

            # 创建或更新本地用户
            local_user = self.create_or_update_local_user(ldap_user_info, config)
            if not local_user:
                return None

            # 生成访问令牌
            access_token = create_access_token(data={"sub": local_user.username})

            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "username": local_user.username,
                    "email": local_user.email,
                    "is_superuser": local_user.is_superuser,
                    "auth_type": "ldap"
                },
                "ldap_config": {
                    "id": config.id,
                    "name": config.name,
                    "match_reason": match_reason
                }
            }

        except Exception as e:
            self.logger.error(f"LDAP登录失败: {str(e)}")
            return None 