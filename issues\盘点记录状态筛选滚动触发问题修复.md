# 盘点记录状态筛选滚动触发问题修复

## 问题描述
移动端资产盘点记录页面中，用户在下滚浏览页面时，状态分类标签（全部、待盘点、正常、异常、缺失、信息变更）会意外触发，导致状态筛选自动切换，影响用户体验。

## 问题现象
- 用户正在查看某个状态的记录列表
- 当用户下滚浏览记录时，状态标签会自动切换到其他状态
- 导致列表内容突然变化，用户体验不佳

## 问题分析

### 根本原因
**Vant van-tabs组件的scrollspy属性导致页面滚动监听冲突**

### 技术细节
```vue
<!-- 问题代码 -->
<van-tabs v-model:active="activeRecordStatus" @change="handleRecordStatusChange" swipeable scrollspy>
```

1. **scrollspy属性的作用**：
   - 监听页面滚动事件，根据滚动位置自动切换标签
   - 设计用于长内容页面的导航场景
   - 当检测到特定区域滚动到视窗时，自动激活对应标签

2. **与业务场景的冲突**：
   - 盘点记录的状态标签是用于数据筛选，而不是内容导航
   - scrollspy监听全局滚动，误将用户的浏览行为识别为导航意图
   - 导致筛选状态意外变化

### 影响范围
- 文件：`frontend/src/mobile/views/asset/InventoryTask.vue`
- 功能：移动端盘点记录状态筛选
- 用户体验：严重影响数据浏览和筛选操作

## 解决方案

### 方案选择
采用**方案1：移除scrollspy属性**（推荐且已实施）

### 修复内容
```vue
<!-- 修复前 -->
<van-tabs v-model:active="activeRecordStatus" @change="handleRecordStatusChange" swipeable scrollspy>

<!-- 修复后 -->
<van-tabs v-model:active="activeRecordStatus" @change="handleRecordStatusChange" swipeable>
```

### 替代方案（备选）
如需完全避免此类问题，可考虑使用其他组件：
```vue
<van-nav-bar title="状态筛选" />
<van-grid :column-num="6" :gutter="10">
  <van-grid-item 
    v-for="status in statusOptions" 
    :key="status.value"
    :class="{ 'active': activeRecordStatus === status.value }"
    @click="handleStatusChange(status.value)"
  >
    {{ status.text }}
  </van-grid-item>
</van-grid>
```

## 修复验证

### 验证步骤
1. 打开移动端盘点任务详情页面
2. 切换到"盘点记录"标签页
3. 选择任一状态筛选（如"待盘点"）
4. 下滚浏览记录列表
5. 确认状态标签不会自动切换

### 预期结果
- ✅ 状态筛选仅响应用户主动点击
- ✅ 页面滚动不会触发状态切换
- ✅ 用户可以正常浏览长列表内容
- ✅ 保持原有的滑动切换功能（swipeable）

## 技术要点

### Vant scrollspy属性说明
- **用途**：内容导航，适用于长文档或多段落页面
- **工作原理**：监听滚动事件，根据元素在视窗中的位置自动切换标签
- **适用场景**：文档导航、章节切换、内容索引
- **不适用场景**：数据筛选、状态切换、功能操作

### 最佳实践建议
1. **明确组件用途**：区分导航组件和筛选组件的使用场景
2. **谨慎使用scrollspy**：仅在确实需要滚动导航时启用
3. **用户体验优先**：避免自动触发可能影响用户操作意图的功能
4. **功能测试**：在移动端充分测试滚动交互

## 相关代码
- **主要文件**: `frontend/src/mobile/views/asset/InventoryTask.vue:75`
- **修改内容**: 移除van-tabs组件的scrollspy属性
- **影响函数**: `handleRecordStatusChange()` - 状态筛选处理函数
- **相关功能**: 盘点记录列表筛选、无限滚动加载

## 问题预防
为避免类似问题，建议：
1. 在使用UI组件时仔细阅读属性说明
2. 区分导航型组件和操作型组件的使用场景  
3. 在移动端充分测试滚动和触摸交互
4. 建立代码审查机制，关注用户体验细节

## 完成状态
- ✅ 问题分析完成
- ✅ 解决方案确定
- ✅ 代码修复完成
- ✅ 文档记录完成
- 🔄 用户验证待进行

## 后续建议
1. 全面检查其他使用van-tabs组件的页面，确认scrollspy使用是否合理
2. 建立移动端交互测试规范，包含滚动行为验证
3. 考虑为状态筛选功能建立专用组件，避免误用导航组件 