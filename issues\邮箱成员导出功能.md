# 邮箱成员导出功能实现

## 任务概述
为当前项目中的邮箱管理模块的"部门与成员管理"功能增加成员数据导出功能。

## 需求详情
1. **功能位置**：在部门与成员管理页面添加导出功能
2. **导出格式支持**：Excel格式（.xlsx）、CSV格式（.csv）
3. **用户交互**：提供格式选择界面，让用户可以选择导出xlsx或csv格式，添加导出按钮或菜单项
4. **导出内容**：导出当前显示的成员列表数据，包含成员的基本信息字段（如姓名、邮箱、部门等）
5. **技术实现**：前端需要添加格式选择UI组件，后端需要实现两种格式的数据导出接口，确保导出的文件可以正确下载到用户本地

## 实施计划

### 后端实现 ✅
1. **API接口开发** - `backend/app/api/v1/email.py`
   - 新增 `/email/members/export` GET接口
   - 支持format参数(csv/xlsx)
   - 支持现有筛选参数(department_id, search)
   - 权限检查: `email:member:export`

2. **CRUD方法** - `backend/app/crud/email.py`
   - 添加 `get_members_for_export` 方法
   - 支持搜索和部门过滤
   - 包含部门信息的关联查询

### 前端实现 ✅
1. **API接口封装** - `frontend/src/api/email/member.ts`
   - 新增 `exportMembers` 函数
   - 支持格式选择和筛选参数
   - 设置正确的responseType为'blob'

2. **UI组件** - `frontend/src/views/email/DepartmentMemberManagement.vue`
   - 在功能菜单中添加"导出成员"选项
   - 创建导出格式选择对话框
   - 添加导出状态管理
   - 实现文件下载逻辑

## 技术特性
- **多格式支持**: 支持Excel(.xlsx)和CSV(.csv)两种格式
- **智能筛选**: 支持按部门和搜索条件筛选导出
- **中文支持**: 正确处理中文文件名和内容编码
- **用户体验**: 友好的进度提示和错误处理
- **权限控制**: 基于`email:member:export`权限

## 导出字段
- 工号
- 邮箱地址  
- 姓名
- 部门
- 职位
- 手机号
- 电话
- POP/SMTP服务状态
- IMAP/SMTP服务状态
- 安全登录状态
- 强制安全登录状态
- 账户状态
- 创建时间
- 更新时间

## 状态记录
- [x] 后端API接口开发
- [x] 后端CRUD方法实现
- [x] 前端API接口封装
- [x] 前端UI组件实现
- [x] 导出对话框设计
- [x] 文件下载逻辑
- [x] 样式优化
- [ ] 功能测试
- [ ] 权限验证测试

## 完成时间
实现时间：2024年12月19日 