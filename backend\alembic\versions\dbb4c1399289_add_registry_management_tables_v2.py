"""add_registry_management_tables_v2

Revision ID: dbb4c1399289
Revises: fb67efaf9f06
Create Date: 2025-06-24 13:52:30.725148

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dbb4c1399289'
down_revision: Union[str, None] = 'fb67efaf9f06'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('registry_backups',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('backup_id', sa.String(length=255), nullable=False, comment='备份ID'),
    sa.Column('backup_name', sa.String(length=500), nullable=False, comment='备份名称'),
    sa.Column('terminal_id', sa.String(length=255), nullable=False, comment='终端ID'),
    sa.Column('root_key', sa.String(length=50), nullable=False, comment='根键'),
    sa.Column('key_path', sa.Text(), nullable=False, comment='键路径'),
    sa.Column('file_path', sa.Text(), nullable=False, comment='备份文件路径'),
    sa.Column('file_size', sa.Integer(), nullable=True, comment='文件大小(字节)'),
    sa.Column('file_hash', sa.String(length=255), nullable=True, comment='文件哈希值'),
    sa.Column('reason', sa.String(length=500), nullable=True, comment='备份原因'),
    sa.Column('description', sa.Text(), nullable=True, comment='备份描述'),
    sa.Column('tags', sa.String(length=500), nullable=True, comment='标签(逗号分隔)'),
    sa.Column('status', sa.String(length=50), nullable=False, comment='备份状态(active/deleted/expired)'),
    sa.Column('is_verified', sa.Boolean(), nullable=False, comment='是否已验证'),
    sa.Column('related_operation_id', sa.Integer(), nullable=True, comment='关联的操作记录ID'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.Column('expire_at', sa.DateTime(), nullable=True, comment='过期时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_registry_backups_backup_id'), 'registry_backups', ['backup_id'], unique=True)
    op.create_index(op.f('ix_registry_backups_id'), 'registry_backups', ['id'], unique=False)
    op.create_index(op.f('ix_registry_backups_status'), 'registry_backups', ['status'], unique=False)
    op.create_index(op.f('ix_registry_backups_terminal_id'), 'registry_backups', ['terminal_id'], unique=False)
    op.create_table('registry_operations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('command_id', sa.String(length=255), nullable=False, comment='命令ID'),
    sa.Column('terminal_id', sa.String(length=255), nullable=False, comment='终端ID'),
    sa.Column('operation_type', sa.String(length=50), nullable=False, comment='操作类型'),
    sa.Column('root_key', sa.String(length=50), nullable=False, comment='根键'),
    sa.Column('key_path', sa.Text(), nullable=False, comment='键路径'),
    sa.Column('value_name', sa.String(length=255), nullable=True, comment='值名称'),
    sa.Column('value_type', sa.String(length=50), nullable=True, comment='值类型'),
    sa.Column('old_value_data', sa.Text(), nullable=True, comment='原始值数据'),
    sa.Column('new_value_data', sa.Text(), nullable=True, comment='新值数据'),
    sa.Column('success', sa.Boolean(), nullable=False, comment='操作是否成功'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('backup_id', sa.String(length=255), nullable=True, comment='备份ID'),
    sa.Column('backup_reason', sa.String(length=500), nullable=True, comment='备份原因'),
    sa.Column('operation_data', sa.JSON(), nullable=True, comment='完整操作数据'),
    sa.Column('execution_duration', sa.Integer(), nullable=True, comment='执行耗时(毫秒)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_registry_operations_backup_id'), 'registry_operations', ['backup_id'], unique=False)
    op.create_index(op.f('ix_registry_operations_command_id'), 'registry_operations', ['command_id'], unique=False)
    op.create_index(op.f('ix_registry_operations_id'), 'registry_operations', ['id'], unique=False)
    op.create_index(op.f('ix_registry_operations_operation_type'), 'registry_operations', ['operation_type'], unique=False)
    op.create_index(op.f('ix_registry_operations_terminal_id'), 'registry_operations', ['terminal_id'], unique=False)
    op.create_table('registry_search_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('search_id', sa.String(length=255), nullable=False, comment='搜索ID'),
    sa.Column('terminal_id', sa.String(length=255), nullable=False, comment='终端ID'),
    sa.Column('root_key', sa.String(length=50), nullable=False, comment='搜索根键'),
    sa.Column('start_path', sa.Text(), nullable=False, comment='开始路径'),
    sa.Column('search_pattern', sa.String(length=500), nullable=False, comment='搜索模式'),
    sa.Column('search_keys', sa.Boolean(), nullable=False, comment='是否搜索键名'),
    sa.Column('search_values', sa.Boolean(), nullable=False, comment='是否搜索值名'),
    sa.Column('search_data', sa.Boolean(), nullable=False, comment='是否搜索值数据'),
    sa.Column('max_depth', sa.Integer(), nullable=False, comment='最大搜索深度'),
    sa.Column('max_results', sa.Integer(), nullable=False, comment='最大结果数'),
    sa.Column('total_results', sa.Integer(), nullable=False, comment='搜索结果总数'),
    sa.Column('results_data', sa.JSON(), nullable=True, comment='搜索结果数据'),
    sa.Column('success', sa.Boolean(), nullable=False, comment='搜索是否成功'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('execution_duration', sa.Integer(), nullable=True, comment='执行耗时(毫秒)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_registry_search_logs_id'), 'registry_search_logs', ['id'], unique=False)
    op.create_index(op.f('ix_registry_search_logs_search_id'), 'registry_search_logs', ['search_id'], unique=False)
    op.create_index(op.f('ix_registry_search_logs_terminal_id'), 'registry_search_logs', ['terminal_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_registry_search_logs_terminal_id'), table_name='registry_search_logs')
    op.drop_index(op.f('ix_registry_search_logs_search_id'), table_name='registry_search_logs')
    op.drop_index(op.f('ix_registry_search_logs_id'), table_name='registry_search_logs')
    op.drop_table('registry_search_logs')
    op.drop_index(op.f('ix_registry_operations_terminal_id'), table_name='registry_operations')
    op.drop_index(op.f('ix_registry_operations_operation_type'), table_name='registry_operations')
    op.drop_index(op.f('ix_registry_operations_id'), table_name='registry_operations')
    op.drop_index(op.f('ix_registry_operations_command_id'), table_name='registry_operations')
    op.drop_index(op.f('ix_registry_operations_backup_id'), table_name='registry_operations')
    op.drop_table('registry_operations')
    op.drop_index(op.f('ix_registry_backups_terminal_id'), table_name='registry_backups')
    op.drop_index(op.f('ix_registry_backups_status'), table_name='registry_backups')
    op.drop_index(op.f('ix_registry_backups_id'), table_name='registry_backups')
    op.drop_index(op.f('ix_registry_backups_backup_id'), table_name='registry_backups')
    op.drop_table('registry_backups')
    # ### end Alembic commands ###
