"""
人员邮箱同步配置管理服务
类似AD同步配置，管理同步设置和调度
"""

import logging
from typing import Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.email import PersonnelSyncConfig, EmailSyncLog
from app.models.ecology_user import EcologyUser

logger = logging.getLogger(__name__)


class PersonnelSyncConfigService:
    """人员邮箱同步配置服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def get_sync_config(self) -> PersonnelSyncConfig:
        """获取同步配置"""
        config = self.db.query(PersonnelSyncConfig).first()
        
        if not config:
            # 创建默认配置
            config = PersonnelSyncConfig(
                enabled=False,
                sync_time="08:00",
                sync_interval=24,
                auto_create_users=True,
                auto_update_users=True,
                auto_disable_users=True,
                sync_status="未同步"
            )
            self.db.add(config)
            self.db.commit()
            self.db.refresh(config)
            logger.info("创建默认人员邮箱同步配置")
        
        return config
    
    async def update_sync_config(self, config_data: dict) -> PersonnelSyncConfig:
        """更新同步配置"""
        config = await self.get_sync_config()
        
        # 更新配置字段
        for key, value in config_data.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 如果启用了同步，计算下次同步时间
        if config.enabled:
            await self._calculate_next_sync_time(config)
        
        self.db.commit()
        logger.info(f"更新人员邮箱同步配置: {config_data}")
        
        return config
    
    async def _calculate_next_sync_time(self, config: PersonnelSyncConfig):
        """计算下次同步时间"""
        now = datetime.now()
        
        if config.sync_time:
            # 使用指定时间同步
            try:
                sync_hour, sync_minute = map(int, config.sync_time.split(':'))
                
                # 获取今天的同步时间点
                target_time_today = now.replace(hour=sync_hour, minute=sync_minute, second=0, microsecond=0)
                
                # 如果当前时间已经过了今天的同步时间点，计算明天的同步时间
                if now >= target_time_today:
                    next_sync = target_time_today + timedelta(days=1)
                else:
                    next_sync = target_time_today
                
                config.next_sync_time = next_sync.strftime("%Y-%m-%d %H:%M:%S")
                
            except ValueError:
                logger.error(f"无效的同步时间格式: {config.sync_time}")
                config.next_sync_time = None
        else:
            # 使用间隔时间同步
            if config.last_sync_time:
                try:
                    last_sync = datetime.strptime(config.last_sync_time, "%Y-%m-%d %H:%M:%S")
                    next_sync = last_sync + timedelta(hours=config.sync_interval)
                    
                    # 如果下次同步时间已经过了，从当前时间开始计算
                    if next_sync <= now:
                        next_sync = now + timedelta(hours=config.sync_interval)
                    
                    config.next_sync_time = next_sync.strftime("%Y-%m-%d %H:%M:%S")
                    
                except ValueError:
                    logger.error(f"无效的上次同步时间格式: {config.last_sync_time}")
                    config.next_sync_time = (now + timedelta(hours=config.sync_interval)).strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 如果从未同步过，立即同步
                config.next_sync_time = now.strftime("%Y-%m-%d %H:%M:%S")
    
    async def is_sync_due(self) -> bool:
        """检查是否到了同步时间"""
        try:
            config = await self.get_sync_config()
            
            # 如果同步未启用，直接返回False
            if not config.enabled:
                return False
            
            # 如果从未同步过，应该进行同步
            if not config.last_sync_time:
                return True
            
            # 获取当前时间
            now = datetime.now()
            
            # 如果设置了指定同步时间
            if config.sync_time:
                try:
                    sync_hour, sync_minute = map(int, config.sync_time.split(':'))
                    
                    # 解析上次同步时间
                    last_sync = datetime.strptime(config.last_sync_time, "%Y-%m-%d %H:%M:%S")
                    
                    # 获取目标同步时间（今天）
                    target_time_today = now.replace(hour=sync_hour, minute=sync_minute, second=0, microsecond=0)
                    
                    # 如果当前时间已经超过了今天的同步时间，且上次同步时间早于今天的同步时间，则需要同步
                    today_date = now.date()
                    last_sync_date = last_sync.date()
                    
                    if now >= target_time_today and last_sync_date < today_date:
                        return True
                    
                except ValueError:
                    logger.error(f"时间格式错误，无法判断是否需要同步")
                    return False
            else:
                # 使用间隔时间判断
                try:
                    last_sync = datetime.strptime(config.last_sync_time, "%Y-%m-%d %H:%M:%S")
                    next_sync_time = last_sync + timedelta(hours=config.sync_interval)
                    
                    if now >= next_sync_time:
                        return True
                        
                except ValueError:
                    logger.error(f"时间格式错误，无法判断是否需要同步")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"检查同步时间失败: {str(e)}")
            return False
    
    async def update_sync_status(self, status: str, message: str = None, error_message: str = None):
        """更新同步状态"""
        config = await self.get_sync_config()
        config.sync_status = status
        
        if message:
            # 这里可以添加消息字段到配置模型中
            pass
        
        if error_message:
            config.error_message = error_message
        
        # 如果同步成功，更新最后同步时间
        if status == "success":
            config.last_sync_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            config.error_message = None
            
            # 计算下次同步时间
            await self._calculate_next_sync_time(config)
        
        self.db.commit()
    
    async def get_sync_logs(self, skip: int = 0, limit: int = 100) -> list:
        """获取同步日志"""
        logs = self.db.query(EmailSyncLog).filter(
            EmailSyncLog.sync_category == "personnel"
        ).order_by(
            EmailSyncLog.created_at.desc()
        ).offset(skip).limit(limit).all()
        
        return logs
    
    async def get_sync_log_by_id(self, log_id: int) -> Optional[EmailSyncLog]:
        """根据ID获取同步日志"""
        return self.db.query(EmailSyncLog).filter(
            EmailSyncLog.id == log_id,
            EmailSyncLog.sync_category == "personnel"
        ).first()
    
    async def get_sync_stats(self) -> dict:
        """获取同步统计信息"""
        try:
            config = await self.get_sync_config()
            
            # 获取最近的同步日志
            recent_logs = self.db.query(EmailSyncLog).filter(
                EmailSyncLog.sync_category == "personnel"
            ).order_by(
                EmailSyncLog.created_at.desc()
            ).limit(10).all()
            
            # 统计成功和失败的同步次数
            total_syncs = len(recent_logs)
            successful_syncs = len([log for log in recent_logs if log.status == "success"])
            failed_syncs = len([log for log in recent_logs if log.status == "failed"])
            
            # 获取最后一次成功同步的信息
            last_successful_sync = self.db.query(EmailSyncLog).filter(
                EmailSyncLog.sync_category == "personnel",
                EmailSyncLog.status == "success"
            ).order_by(EmailSyncLog.completed_at.desc()).first()
            
            return {
                "config": {
                    "enabled": config.enabled,
                    "sync_time": config.sync_time,
                    "sync_interval": config.sync_interval,
                    "last_sync_time": config.last_sync_time,
                    "next_sync_time": config.next_sync_time,
                    "sync_status": config.sync_status
                },
                "stats": {
                    "total_syncs": total_syncs,
                    "successful_syncs": successful_syncs,
                    "failed_syncs": failed_syncs,
                    "success_rate": round(successful_syncs / total_syncs * 100, 2) if total_syncs > 0 else 0
                },
                "last_successful_sync": {
                    "id": last_successful_sync.id if last_successful_sync else None,
                    "completed_at": last_successful_sync.completed_at.isoformat() if last_successful_sync and last_successful_sync.completed_at else None,
                    "processed_count": last_successful_sync.processed_count if last_successful_sync else 0,
                    "created_count": last_successful_sync.created_count if last_successful_sync else 0,
                    "updated_count": last_successful_sync.updated_count if last_successful_sync else 0,
                    "disabled_count": last_successful_sync.disabled_count if last_successful_sync else 0
                } if last_successful_sync else None
            }
            
        except Exception as e:
            logger.error(f"获取同步统计信息失败: {str(e)}")
            return {
                "error": str(e)
            }
    
    async def get_available_departments(self) -> List[str]:
        """获取可用的部门列表"""
        # 查询所有有效的部门名称
        departments = self.db.query(EcologyUser.dept_name).filter(
            EcologyUser.dept_name.isnot(None),
            EcologyUser.dept_name != ""
        ).distinct().all()
        
        dept_list = [dept[0] for dept in departments if dept[0]]
        dept_list.sort()
        
        logger.info(f"找到 {len(dept_list)} 个部门")
        return dept_list
    
    async def get_available_job_titles(self) -> List[str]:
        """获取可用的职位列表"""
        # 查询所有有效的职位名称
        job_titles = self.db.query(EcologyUser.job_title_name).filter(
            EcologyUser.job_title_name.isnot(None),
            EcologyUser.job_title_name != ""
        ).distinct().all()
        
        job_title_list = [title[0] for title in job_titles if title[0]]
        job_title_list.sort()
        
        logger.info(f"找到 {len(job_title_list)} 个职位")
        return job_title_list
    
    async def get_available_companies(self) -> List[str]:
        """获取可用的公司列表"""
        # 查询所有有效的公司名称
        companies = self.db.query(EcologyUser.company_name).filter(
            EcologyUser.company_name.isnot(None),
            EcologyUser.company_name != ""
        ).distinct().all()
        
        company_list = [company[0] for company in companies if company[0]]
        company_list.sort()
        
        logger.info(f"找到 {len(company_list)} 个公司")
        return company_list
    
    async def get_departments_by_company(self, company_name: str) -> List[str]:
        """根据公司名获取该公司下的部门列表"""
        if not company_name or company_name.strip() == "":
            logger.warning("公司名称为空，返回所有部门")
            return await self.get_available_departments()
        
        company_name = company_name.strip()
        
        # 查询指定公司下的所有有效部门名称
        departments = self.db.query(EcologyUser.dept_name).filter(
            EcologyUser.company_name == company_name,
            EcologyUser.dept_name.isnot(None),
            EcologyUser.dept_name != ""
        ).distinct().all()
        
        dept_list = [dept[0] for dept in departments if dept[0]]
        dept_list.sort()
        
        logger.info(f"找到公司 '{company_name}' 下的 {len(dept_list)} 个部门")
        return dept_list
    
    async def preview_filter_results(self, filter_config: dict) -> dict:
        """预览过滤结果"""
        # 基础查询条件
        base_conditions = [
            EcologyUser.job_number.isnot(None),
            EcologyUser.job_number != "",
            EcologyUser.user_name.isnot(None),
            EcologyUser.user_name != ""
        ]
        
        # 查询全部符合基础条件的人员数量
        total_personnel = self.db.query(EcologyUser).filter(and_(*base_conditions)).count()
        
        # 如果没有启用过滤，返回全部数量
        if not filter_config.get('filter_enabled', False):
            return {
                "total_personnel": total_personnel,
                "filtered_personnel": total_personnel,
                "reduction_rate": 0.0,
                "department_breakdown": {},
                "job_title_breakdown": {},
                "sample_personnel": []
            }
        
        # 构建过滤条件
        filter_conditions = []
        
        # 公司过滤
        included_companies = filter_config.get('included_companies', [])
        if included_companies:
            company_conditions = []
            for company in included_companies:
                if company.strip():
                    company_conditions.append(EcologyUser.company_name.contains(company.strip()))
            if company_conditions:
                filter_conditions.append(or_(*company_conditions))
        
        # 部门过滤
        included_departments = filter_config.get('included_departments', [])
        if included_departments:
            dept_conditions = []
            for dept in included_departments:
                if dept.strip():
                    dept_conditions.append(EcologyUser.dept_name.contains(dept.strip()))
            if dept_conditions:
                filter_conditions.append(or_(*dept_conditions))
        
        # 职位包含过滤
        included_job_titles = filter_config.get('included_job_titles', [])
        if included_job_titles:
            job_include_conditions = []
            for job_title in included_job_titles:
                if job_title.strip():
                    job_include_conditions.append(EcologyUser.job_title_name.contains(job_title.strip()))
            if job_include_conditions:
                filter_conditions.append(or_(*job_include_conditions))
        
        # 职位排除过滤
        excluded_job_titles = filter_config.get('excluded_job_titles', [])
        if excluded_job_titles:
            job_exclude_conditions = []
            for job_title in excluded_job_titles:
                if job_title.strip():
                    job_exclude_conditions.append(~EcologyUser.job_title_name.contains(job_title.strip()))
            if job_exclude_conditions:
                filter_conditions.append(and_(*job_exclude_conditions))
        
        # 组合过滤条件
        final_conditions = base_conditions.copy()
        if filter_conditions:
            filter_logic = filter_config.get('filter_logic', 'AND')
            if filter_logic == 'OR':
                final_filter = or_(*filter_conditions)
            else:
                final_filter = and_(*filter_conditions)
            final_conditions.append(final_filter)
        
        # 查询过滤后的人员
        filtered_query = self.db.query(EcologyUser).filter(and_(*final_conditions))
        filtered_personnel_count = filtered_query.count()
        
        # 计算减少比例
        reduction_rate = ((total_personnel - filtered_personnel_count) / total_personnel * 100) if total_personnel > 0 else 0
        
        # 获取样本数据（最多10个）
        sample_personnel = filtered_query.limit(10).all()
        sample_data = []
        for person in sample_personnel:
            sample_data.append({
                "job_number": person.job_number,
                "user_name": person.user_name,
                "dept_name": person.dept_name,
                "job_title_name": person.job_title_name,
                "status": person.status
            })
        
        # 统计部门分布
        dept_breakdown = {}
        if filtered_personnel_count > 0:
            dept_stats = self.db.query(
                EcologyUser.dept_name, 
                func.count(EcologyUser.id)
            ).filter(and_(*final_conditions)).group_by(EcologyUser.dept_name).all()
            
            for dept_name, count in dept_stats:
                if dept_name:
                    dept_breakdown[dept_name] = count
        
        # 统计职位分布
        job_title_breakdown = {}
        if filtered_personnel_count > 0:
            job_title_stats = self.db.query(
                EcologyUser.job_title_name,
                func.count(EcologyUser.id)
            ).filter(and_(*final_conditions)).group_by(EcologyUser.job_title_name).all()
            
            for job_title, count in job_title_stats:
                if job_title:
                    job_title_breakdown[job_title] = count
        
        logger.info(f"过滤预览结果: 总数={total_personnel}, 过滤后={filtered_personnel_count}, 减少率={reduction_rate:.1f}%")
        
        return {
            "total_personnel": total_personnel,
            "filtered_personnel": filtered_personnel_count,
            "reduction_rate": round(reduction_rate, 1),
            "department_breakdown": dept_breakdown,
            "job_title_breakdown": job_title_breakdown,
            "sample_personnel": sample_data
        }
