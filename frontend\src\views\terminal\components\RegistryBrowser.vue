<template>
  <div class="registry-browser">
    <!-- 未激活状态的占位内容 -->
    <div v-if="!hasBeenActivated" class="placeholder-content">
      <el-empty description="点击注册表管理标签页开始浏览注册表">
        <el-icon class="placeholder-icon"><Folder /></el-icon>
        <p class="placeholder-text">注册表数据将在首次访问时加载</p>
      </el-empty>
    </div>
    
    <!-- 激活后的注册表内容 -->
    <div v-if="hasBeenActivated" class="registry-toolbar">
      <div class="toolbar-left">
        <el-select
          v-model="selectedRootKey"
          placeholder="选择根键"
          @change="handleRootKeyChange"
          class="root-key-select"
        >
          <el-option
            v-for="rootKey in rootKeys"
            :key="rootKey.value"
            :label="rootKey.label"
            :value="rootKey.value"
          />
        </el-select>
        
        <el-input
          v-model="currentPath"
          placeholder="注册表路径"
          class="path-input"
          @keyup.enter="navigateToPath"
        >
          <template #append>
            <el-dropdown trigger="click">
              <el-button :loading="loading" type="primary">
                <el-icon><Search /></el-icon>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="navigateToPath">
                    <el-icon><FolderOpened /></el-icon>
                    导航到路径
                  </el-dropdown-item>
                  <el-dropdown-item @click="showSearchDialog = true">
                    <el-icon><Search /></el-icon>
                    搜索注册表
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-input>
        
        <el-button @click="refreshCurrentKey" :loading="loading" class="action-btn">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="showBackupDialog = true" type="primary" class="backup-btn">
          <el-icon><Download /></el-icon>
          备份
        </el-button>
      </div>
    </div>

    <div v-if="hasBeenActivated" class="registry-content">
      <div class="registry-tree">
        <div class="tree-header">
          <span>注册表键</span>
        </div>
        <div class="tree-container">
          <RegistryVirtualTree
            ref="registryVirtualTree"
            :root-key="selectedRootKey"
            :selected-path="currentPath"
            :loading="treeLoading"
            :page-size="50"
            @node-click="handleVirtualNodeClick"
            @load-children="handleLoadVirtualChildren"
            @create-subkey="createSubKey"
            @delete-key="deleteKey"
          />
        </div>
      </div>

      <div class="registry-values">
        <div class="values-header">
          <span class="header-title">值</span>
          <el-button @click="createValue" :disabled="!currentKeyData" type="primary" class="header-btn">
            <el-icon><Plus /></el-icon>
            新建值
          </el-button>
        </div>
        
        <el-table
          :data="currentValues"
          v-loading="valuesLoading"
          :height="tableHeight"
          @row-dblclick="editValue"
        >
          <el-table-column prop="name" label="名称" width="200">
            <template #default="{ row }">
              <div class="value-name">
                <el-icon class="value-icon"><Document /></el-icon>
                <span>{{ row.name || '(默认)' }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getValueTypeTagType(row.type)">
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="data" label="数据" min-width="300">
            <template #default="{ row }">
              <div class="value-data">
                <span :title="row.data">{{ formatValueData(row.data, row.type) }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="160" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button-group class="value-btn-group">
                  <el-button size="small" @click="editValue(row)" title="编辑">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteValue(row)" title="删除">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </el-button-group>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 值编辑对话框 -->
    <el-dialog
      v-model="showValueDialog"
      :title="valueDialogTitle"
      width="600px"
      @close="resetValueDialog"
    >
      <el-form :model="valueForm" label-width="80px">
        <el-form-item label="名称">
          <el-input
            v-model="valueForm.name"
            :disabled="valueForm.isEdit"
            placeholder="值名称"
          />
        </el-form-item>
        
        <el-form-item label="类型">
          <el-select v-model="valueForm.type" :disabled="valueForm.isEdit">
            <el-option
              v-for="type in valueTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据">
          <el-input
            v-if="valueForm.type !== 'REG_MULTI_SZ'"
            v-model="valueForm.data"
            :type="valueForm.type === 'REG_BINARY' ? 'textarea' : 'text'"
            :rows="3"
            placeholder="值数据"
          />
          <el-input
            v-else
            v-model="valueForm.data"
            type="textarea"
            :rows="5"
            placeholder="每行一个字符串"
          />
        </el-form-item>
        
        <el-form-item v-if="valueForm.type === 'REG_BINARY'" label="格式">
          <el-radio-group v-model="valueForm.binaryFormat">
            <el-radio value="hex">十六进制</el-radio>
            <el-radio value="text">文本</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showValueDialog = false">取消</el-button>
        <el-button type="primary" @click="saveValue" :loading="saving">
          {{ valueForm.isEdit ? '保存' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 搜索对话框 -->
    <el-dialog
      v-model="showSearchDialog"
      title="搜索注册表"
      width="700px"
    >
      <el-form :model="searchForm" label-width="100px">
        <el-form-item label="搜索根键">
          <el-select v-model="searchForm.rootKey">
            <el-option
              v-for="rootKey in rootKeys"
              :key="rootKey.value"
              :label="rootKey.label"
              :value="rootKey.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="起始路径">
          <el-input v-model="searchForm.startPath" placeholder="可选，留空从根开始搜索" />
        </el-form-item>
        
        <el-form-item label="搜索模式">
          <el-input v-model="searchForm.pattern" placeholder="搜索关键词" />
        </el-form-item>
        
        <el-form-item label="搜索范围">
          <el-checkbox-group v-model="searchForm.searchScope">
            <el-checkbox value="keys">键名</el-checkbox>
            <el-checkbox value="values">值名</el-checkbox>
            <el-checkbox value="data">值数据</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="高级选项">
          <div class="search-options">
            <el-input-number
              v-model="searchForm.maxDepth"
              :min="1"
              :max="20"
              label="最大深度"
            />
            <el-input-number
              v-model="searchForm.maxResults"
              :min="10"
              :max="1000"
              label="最大结果数"
              style="margin-left: 10px"
            />
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showSearchDialog = false">取消</el-button>
        <el-button type="primary" @click="performSearch" :loading="searching">
          搜索
        </el-button>
      </template>
    </el-dialog>

    <!-- 搜索结果对话框 -->
    <el-dialog
      v-model="showSearchResults"
      title="搜索结果"
      width="900px"
    >
      <div class="search-results">
        <div class="results-summary">
          <span>找到 {{ searchResults.length }} 个结果</span>
        </div>
        
        <el-table :data="searchResults" height="400">
          <el-table-column prop="path" label="路径" min-width="300" />
          <el-table-column prop="match_type" label="匹配类型" width="120">
            <template #default="{ row }">
              <el-tag size="small">{{ getMatchTypeLabel(row.match_type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="match_text" label="匹配内容" min-width="200">
            <template #default="{ row }">
              <span :title="row.match_text">{{ row.match_text }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button size="small" @click="navigateToSearchResult(row)">
                <el-icon><Position /></el-icon>
                定位
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <el-button @click="showSearchResults = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 备份对话框 -->
    <el-dialog
      v-model="showBackupDialog"
      title="创建备份"
      width="500px"
    >
      <el-form :model="backupForm" label-width="80px">
        <el-form-item label="根键">
          <el-select v-model="backupForm.rootKey" disabled>
            <el-option :label="getRootKeyLabel(backupForm.rootKey)" :value="backupForm.rootKey" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="键路径">
          <el-input v-model="backupForm.keyPath" disabled />
        </el-form-item>
        
        <el-form-item label="备份原因">
          <el-input v-model="backupForm.reason" placeholder="备份原因说明" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showBackupDialog = false">取消</el-button>
        <el-button type="primary" @click="createBackup" :loading="backingUp">
          创建备份
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Download, Plus, Delete, Edit, Document,
  Folder, Position, ArrowDown, FolderOpened
} from '@element-plus/icons-vue'
import {
  performRegistryOperation,
  performRegistryOperationPaginated,
  searchRegistry,
  createRegistryBackup,
  getRegistryValueTypeTagType,
  formatRegistryValueData
} from '@/api/registry'
import RegistryVirtualTree from '@/components/RegistryVirtualTree.vue'
import {
  RegistryRootKey,
  RegistryOperationType,
  RegistryValueType
} from '@/types/registry'
import type {
  RegistryOperationRequest,
  RegistryOperationResponse,
  RegistrySearchRequest,
  RegistrySearchResponse,
  RegistryBackupRequest,
  RegistryKey,
  RegistryValue
} from '@/types/registry'

interface RegistryTreeNode {
  name: string
  path: string
  children?: RegistryTreeNode[]
  isLeaf?: boolean
}

const props = withDefaults(defineProps<{
  terminalId: string
  isActive?: boolean
  hasBeenActivated?: boolean
}>(), {
  isActive: false,
  hasBeenActivated: false
})

const emit = defineEmits<{
  'registry-operation': [data: any]
}>()

// 根键选项
const rootKeys = [
  { label: 'HKEY_CLASSES_ROOT (HKCR)', value: 'HKEY_CLASSES_ROOT' },
  { label: 'HKEY_CURRENT_USER (HKCU)', value: 'HKEY_CURRENT_USER' },
  { label: 'HKEY_LOCAL_MACHINE (HKLM)', value: 'HKEY_LOCAL_MACHINE' },
  { label: 'HKEY_USERS (HKU)', value: 'HKEY_USERS' },
  { label: 'HKEY_CURRENT_CONFIG (HKCC)', value: 'HKEY_CURRENT_CONFIG' }
]

// 值类型选项
const valueTypes = [
  { label: 'REG_SZ (字符串)', value: 'REG_SZ' },
  { label: 'REG_EXPAND_SZ (可扩展字符串)', value: 'REG_EXPAND_SZ' },
  { label: 'REG_BINARY (二进制)', value: 'REG_BINARY' },
  { label: 'REG_DWORD (32位数值)', value: 'REG_DWORD' },
  { label: 'REG_QWORD (64位数值)', value: 'REG_QWORD' },
  { label: 'REG_MULTI_SZ (多字符串)', value: 'REG_MULTI_SZ' }
]

// 响应式数据
const selectedRootKey = ref('HKEY_LOCAL_MACHINE')
const currentPath = ref('')
const loading = ref(false)
const treeLoading = ref(false)
const valuesLoading = ref(false)
const saving = ref(false)
const searching = ref(false)
const backingUp = ref(false)

const treeData = ref<RegistryTreeNode[]>([])
const currentKeyData = ref<RegistryKey | null>(null)
const currentValues = ref<RegistryValue[]>([])
const registryTree = ref<any>(null)
const registryVirtualTree = ref<InstanceType<typeof RegistryVirtualTree>>()

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name',
  isLeaf: 'isLeaf'
}

// 对话框状态
const showValueDialog = ref(false)
const showSearchDialog = ref(false)
const showSearchResults = ref(false)
const showBackupDialog = ref(false)

// 表单数据
const valueForm = reactive({
  name: '',
  type: 'REG_SZ' as RegistryValueType,
  data: '',
  isEdit: false,
  binaryFormat: 'hex'
})

const searchForm = reactive({
  rootKey: 'HKEY_LOCAL_MACHINE' as RegistryRootKey,
  startPath: '',
  pattern: '',
  searchScope: ['keys', 'values'],
  maxDepth: 10,
  maxResults: 100
})

const backupForm = reactive({
  rootKey: 'HKEY_LOCAL_MACHINE' as RegistryRootKey,
  keyPath: '',
  reason: ''
})

const searchResults = ref<any[]>([])

// 计算属性
const valueDialogTitle = computed(() => {
  return valueForm.isEdit ? '编辑注册表值' : '创建注册表值'
})

const tableHeight = computed(() => {
  // 根据容器高度动态计算表格高度，确保与左侧树形区域高度协调
  return 'calc(100% - 50px)'
})

// 方法
const handleRootKeyChange = () => {
  currentPath.value = ''
  treeData.value = []
  currentKeyData.value = null
  currentValues.value = []
  loadRootKey()
}

const loadRootKey = async () => {
  if (!selectedRootKey.value) return
  
  treeLoading.value = true
  console.log('开始加载根键:', selectedRootKey.value)
  
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: ''  // 根键使用空路径
      }
    )
    
    console.log('根键加载结果:', result)
    
    if (result.success && result.key_data) {
      if (result.key_data.sub_keys && Array.isArray(result.key_data.sub_keys) && result.key_data.sub_keys.length > 0) {
        const rootNode: RegistryTreeNode = {
          name: selectedRootKey.value,
          path: selectedRootKey.value,
          children: result.key_data.sub_keys.map((subKey: string) => ({
            name: subKey,
            path: `${selectedRootKey.value}\\${subKey}`,  // 构建完整路径
            isLeaf: false
          }))
        }
        
        treeData.value = [rootNode]
        
        // 设置当前键数据
        currentKeyData.value = result.key_data
        currentValues.value = result.key_data.values || []
        currentPath.value = selectedRootKey.value
        
        // 重要！将根键数据传递给虚拟树组件
        if (registryVirtualTree.value) {
          const children = result.key_data.sub_keys || []
          const hasMore = result.key_data.has_more_sub_keys || false
          console.log('设置根键子节点到虚拟树:', selectedRootKey.value, '子键数量:', children.length)
          registryVirtualTree.value.setNodeChildren(selectedRootKey.value, children, hasMore)
        }
        
        console.log('根键加载成功:', result.key_data.sub_keys.length, '个子键', result.key_data.values?.length || 0, '个值')
        ElMessage.success(`成功加载 ${result.key_data.sub_keys.length} 个子键${result.key_data.values?.length > 0 ? `，${result.key_data.values.length} 个值` : ''}`)
      } else {
        console.log('根键下没有子键')
        ElMessage.warning('该键下没有子键')
        // 仍然设置根节点，但没有子节点
        treeData.value = [{
          name: selectedRootKey.value,
          path: selectedRootKey.value,
          children: []
        }]
        currentKeyData.value = result.key_data
        currentValues.value = result.key_data.values || []
        currentPath.value = selectedRootKey.value
        
        // 通知虚拟树没有子键
        if (registryVirtualTree.value) {
          registryVirtualTree.value.setNodeChildren(selectedRootKey.value, [], false)
        }
      }
    } else {
      console.error('根键加载失败:', result)
      if (!result.success) {
        const errorMsg = result.error || '未知错误'
        // 检查是否是权限相关的错误
        if (errorMsg.includes('没有权限访问') || errorMsg.includes('权限不足') || errorMsg.includes('Permission') || errorMsg.includes('Access is denied')) {
          ElMessage.warning({
            message: `无法访问该注册表路径，这通常是由于Windows系统安全保护所致。\n某些敏感路径（如SAM、Security等）受到系统级保护。`,
            duration: 6000,
            showClose: true
          })
        } else {
          ElMessage.error(`操作失败: ${errorMsg}`)
        }
      } else {
        ElMessage.error('未返回键数据')
      }
    }
  } catch (error) {
    console.error('加载根键失败:', error)
    ElMessage.error('加载根键失败')
  } finally {
    treeLoading.value = false
  }
}

const loadTreeNode = async (node: any, resolve: any) => {
  if (node.level === 0) {
    resolve([])
    return
  }
  
  const nodePath = node.data.path
  
  // 计算要传递给API的相对路径
  let keyPath = ''
  if (nodePath === selectedRootKey.value) {
    // 根键节点，使用空路径
    keyPath = ''
  } else if (nodePath.startsWith(selectedRootKey.value + '\\')) {
    // 子键节点，移除根键前缀
    keyPath = nodePath.substring(selectedRootKey.value.length + 1)
  } else {
    // 直接使用路径（兼容性处理）
    keyPath = nodePath
  }
  
  console.log('加载树节点:', nodePath, '计算出的keyPath:', keyPath, 'level:', node.level)
  
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: keyPath
      }
    )
    
    if (result.success && result.key_data) {
      const children = result.key_data.sub_keys.map((subKey: string) => ({
        name: subKey,
        path: nodePath === selectedRootKey.value ? subKey : `${nodePath}\\${subKey}`,
        isLeaf: false
      }))
      console.log('树节点加载成功:', children.length, '个子节点')
      resolve(children)
    } else {
      console.error('树节点加载失败:', result)
      // 对于树节点加载失败，我们静默处理，不显示错误消息
      // 因为用户可能会点击很多受保护的路径，频繁显示错误消息会影响体验
      resolve([])
    }
  } catch (error) {
    console.error('加载树节点失败:', error)
    resolve([])
  }
}

// 通用注册表错误处理函数
const showRegistryError = (errorMsg: string, pathToCheck: string = '') => {
  // 检查是否是权限相关的错误
  const isPermissionError = errorMsg.includes('没有权限访问') || 
                          errorMsg.includes('权限不足') || 
                          errorMsg.includes('Permission') || 
                          errorMsg.includes('Access is denied') ||
                          errorMsg.includes('路径不存在或无法访问') ||
                          errorMsg.includes('加载键数据失败')
  
  if (isPermissionError) {
    ElMessage.warning({
      message: `没有权限访问该注册表路径`,
      duration: 3000,
      showClose: true
    })
  } else {
    // 非权限错误，使用红色错误提示
    ElMessage.error(errorMsg)
  }
}

const handleNodeClick = async (data: RegistryTreeNode) => {
  valuesLoading.value = true
  
  // 处理路径逻辑：如果点击的是根键节点，使用空路径；否则使用相对路径
  let keyPath = ''
  if (data.path === selectedRootKey.value) {
    // 点击根键，使用空路径
    keyPath = ''
    currentPath.value = selectedRootKey.value
  } else {
    // 点击子键，从路径中移除根键前缀
    if (data.path.startsWith(selectedRootKey.value + '\\')) {
      keyPath = data.path.substring(selectedRootKey.value.length + 1)
    } else {
      keyPath = data.path
    }
    currentPath.value = `${selectedRootKey.value}\\${keyPath}`
  }
  
  console.log('点击节点:', data.path, '计算出的keyPath:', keyPath, '当前路径:', currentPath.value)
  
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: keyPath
      }
    )
    
    if (result.success && result.key_data) {
      currentKeyData.value = result.key_data
      currentValues.value = result.key_data.values || []
      console.log('成功加载键数据:', result.key_data.sub_key_count, '个子键', result.key_data.value_count, '个值')
    } else {
      console.error('键数据加载失败:', result)
      const errorMsg = result.error || '加载键数据失败'
      showRegistryError(errorMsg, currentPath.value)
    }
  } catch (error) {
    console.error('加载键值失败:', error)
    showRegistryError('加载键值失败', currentPath.value)
  } finally {
    valuesLoading.value = false
  }
}

const navigateToPath = async () => {
  if (!currentPath.value) return
  
  // 解析路径，分离根键和子路径
  const pathParts = currentPath.value.split('\\')
  const rootKey = pathParts[0]
  const subPath = pathParts.slice(1).join('\\')
  
  if (!rootKeys.find(k => k.value === rootKey)) {
    ElMessage.error('无效的根键')
    return
  }
  
  selectedRootKey.value = rootKey
  
  loading.value = true
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: rootKey as RegistryRootKey,
        key_path: subPath
      }
    )
    
    if (result.success && result.key_data) {
      currentKeyData.value = result.key_data
      currentValues.value = result.key_data.values || []
      // 展开树形结构到对应节点
      await expandTreeToPath()
    } else {
      const errorMsg = result.error || '路径不存在或无法访问'
      showRegistryError(errorMsg, currentPath.value)
    }
  } catch (error) {
    console.error('导航到路径失败:', error)
    showRegistryError('导航失败', currentPath.value)
  } finally {
    loading.value = false
  }
}

const refreshCurrentKey = async () => {
  if (!currentKeyData.value) return
  
  const pathParts = currentPath.value.split('\\')
  const rootKey = pathParts[0]
  const subPath = pathParts.slice(1).join('\\')
  
  loading.value = true
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: rootKey as RegistryRootKey,
        key_path: subPath
      }
    )
    
    if (result.success && result.key_data) {
      currentKeyData.value = result.key_data
      currentValues.value = result.key_data.values || []
      ElMessage.success('刷新成功')
    } else {
      const errorMsg = result.error || '刷新失败'
      showRegistryError(errorMsg, currentPath.value)
    }
  } catch (error) {
    console.error('刷新失败:', error)
    showRegistryError('刷新失败', currentPath.value)
  } finally {
    loading.value = false
  }
}

const createValue = () => {
  resetValueDialog()
  showValueDialog.value = true
}

const editValue = (value: RegistryValue) => {
  valueForm.name = value.name
  valueForm.type = value.type
  valueForm.data = value.data
  valueForm.isEdit = true
  
  if (value.type === 'REG_BINARY') {
    // 尝试检测是否为十六进制格式
    valueForm.binaryFormat = /^[0-9a-fA-F\s]+$/.test(value.data) ? 'hex' : 'text'
  }
  
  showValueDialog.value = true
}

const saveValue = async () => {
  if (!valueForm.name && valueForm.name !== '') {
    ElMessage.error('请输入值名称')
    return
  }
  
  saving.value = true
  try {
    const operation = valueForm.isEdit ? 'REGISTRY_WRITE' : 'REGISTRY_WRITE'
    const pathParts = currentPath.value.split('\\')
    const rootKey = pathParts[0]
    const subPath = pathParts.slice(1).join('\\')
    
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.WRITE,
        root_key: rootKey as RegistryRootKey,
        key_path: subPath,
        value_name: valueForm.name,
        value_type: valueForm.type as RegistryValueType,
        value_data: valueForm.data,
        create_backup: true
      }
    )
    
    if (result.success) {
      ElMessage.success(valueForm.isEdit ? '值保存成功' : '值创建成功')
      showValueDialog.value = false
      refreshCurrentKey() // 刷新当前键的值列表
    } else {
      ElMessage.error(result.error || '操作失败')
    }
  } catch (error) {
    console.error('保存值失败:', error)
    ElMessage.error('保存值失败')
  } finally {
    saving.value = false
  }
}

const deleteValue = async (value: RegistryValue) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除值 "${value.name || '(默认)'}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const pathParts = currentPath.value.split('\\')
    const rootKey = pathParts[0]
    const subPath = pathParts.slice(1).join('\\')
    
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.DELETE,
        root_key: rootKey as RegistryRootKey,
        key_path: subPath,
        value_name: value.name,
        create_backup: true
      }
    )
    
    if (result.success) {
      ElMessage.success('值删除成功')
      refreshCurrentKey()
    } else {
      ElMessage.error(result.error || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除值失败:', error)
      ElMessage.error('删除值失败')
    }
  }
}

const createSubKey = async (parentData: any) => {
  try {
    const { value: keyName } = await ElMessageBox.prompt(
      '请输入新键的名称:',
      '创建子键',
      {
        confirmButtonText: '创建',
        cancelButtonText: '取消',
        inputPattern: /^[^\\/:*?"<>|]+$/,
        inputErrorMessage: '键名不能包含特殊字符'
      }
    )
    
    if (!keyName) return
    
    const newKeyPath = parentData.path === selectedRootKey.value 
      ? keyName 
      : `${parentData.path}\\${keyName}`
    
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.CREATE_KEY,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: newKeyPath
      }
    )
    
    if (result.success) {
      ElMessage.success('子键创建成功')
      // 刷新虚拟树节点以显示新创建的子键
      if (registryVirtualTree.value) {
        registryVirtualTree.value.addChildNode(parentData.path, keyName)
      }
    } else {
      ElMessage.error(result.error || '创建失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建子键失败:', error)
      ElMessage.error('创建子键失败')
    }
  }
}

const deleteKey = async (keyData: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除键 "${keyData.name}" 及其所有子键和值吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.DELETE_KEY,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: keyData.path,
        create_backup: true
      }
    )
    
    if (result.success) {
      ElMessage.success('键删除成功')
      // 从虚拟树中移除已删除的键
      if (registryVirtualTree.value) {
        registryVirtualTree.value.removeNode(keyData.path)
      }
    } else {
      ElMessage.error(result.error || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除键失败:', error)
      ElMessage.error('删除键失败')
    }
  }
}

const performSearch = async () => {
  if (!searchForm.pattern) {
    ElMessage.error('请输入搜索关键词')
    return
  }
  
  searching.value = true
  try {
    // 集成搜索API
    const result = await searchRegistry(
      parseInt(props.terminalId),
      {
        root_key: searchForm.rootKey,
        start_path: searchForm.startPath,
        search_pattern: searchForm.pattern,
        search_keys: searchForm.searchScope.includes('keys'),
        search_values: searchForm.searchScope.includes('values'),
        search_data: searchForm.searchScope.includes('data'),
        max_depth: searchForm.maxDepth,
        max_results: searchForm.maxResults
      }
    )
    
    if (result.success) {
      searchResults.value = result.results || []
      showSearchDialog.value = false
      showSearchResults.value = true
      
      if (searchResults.value.length === 0) {
        ElMessage.info('未找到匹配的结果')
      } else {
        ElMessage.success(`找到 ${searchResults.value.length} 个结果`)
      }
    } else {
      ElMessage.error(result.error || '搜索失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

const navigateToSearchResult = (result: any) => {
  // 解析路径并导航
  const parts = result.path.split('\\')
  if (parts.length > 0) {
    selectedRootKey.value = parts[0]
    currentPath.value = result.path
    navigateToPath()
    showSearchResults.value = false
  }
}

const createBackup = async () => {
  if (!backupForm.reason) {
    ElMessage.error('请输入备份原因')
    return
  }
  
  backingUp.value = true
  try {
    const result = await createRegistryBackup(
      parseInt(props.terminalId),
      {
        root_key: backupForm.rootKey,
        key_path: backupForm.keyPath,
        reason: backupForm.reason
      }
    )
    
    if (result.success) {
      ElMessage.success('备份创建成功')
      showBackupDialog.value = false
    } else {
      ElMessage.error(result.error || '备份失败')
    }
  } catch (error) {
    console.error('创建备份失败:', error)
    ElMessage.error('创建备份失败')
  } finally {
    backingUp.value = false
  }
}

const resetValueDialog = () => {
  valueForm.name = ''
  valueForm.type = RegistryValueType.REG_SZ
  valueForm.data = ''
  valueForm.isEdit = false
  valueForm.binaryFormat = 'hex'
}

// 虚拟树相关处理函数
const handleVirtualNodeClick = async (node: any) => {
  valuesLoading.value = true
  
  // 处理路径逻辑：如果点击的是根键节点，使用空路径；否则使用相对路径
  let keyPath = ''
  if (node.path === selectedRootKey.value) {
    // 点击根键，使用空路径
    keyPath = ''
    currentPath.value = selectedRootKey.value
  } else {
    // 点击子键，从路径中移除根键前缀
    if (node.path.startsWith(selectedRootKey.value + '\\')) {
      keyPath = node.path.substring(selectedRootKey.value.length + 1)
    } else {
      keyPath = node.path
    }
    currentPath.value = `${selectedRootKey.value}\\${keyPath}`
  }
  
  console.log('虚拟树节点点击:', node.path, '计算出的keyPath:', keyPath, '当前路径:', currentPath.value)
  
  try {
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: keyPath
      }
    )
    
    if (result.success && result.key_data) {
      currentKeyData.value = result.key_data
      currentValues.value = result.key_data.values || []
      console.log('成功加载键数据:', result.key_data.sub_key_count, '个子键', result.key_data.value_count, '个值')
    } else {
      console.error('键数据加载失败:', result)
      const errorMsg = result.error || '加载键数据失败'
      showRegistryError(errorMsg, currentPath.value)
    }
  } catch (error) {
    console.error('加载键值失败:', error)
    showRegistryError('加载键值失败', currentPath.value)
  } finally {
    valuesLoading.value = false
  }
}

const handleLoadVirtualChildren = async (node: any) => {
  if (!registryVirtualTree.value) return
  
  // 计算要传递给API的相对路径 - 修复路径计算逻辑
  let keyPath = ''
  
  console.log('处理虚拟树子节点加载:', {
    nodePath: node.path,
    selectedRootKey: selectedRootKey.value,
    nodeLevel: node.level
  })
  
  if (node.path === selectedRootKey.value) {
    // 根键节点，使用空路径
    keyPath = ''
    console.log('根键节点，使用空路径')
  } else if (node.path.startsWith(selectedRootKey.value + '\\')) {
    // 子键节点，移除根键前缀获得相对路径
    keyPath = node.path.substring(selectedRootKey.value.length + 1)
    console.log('子键节点，移除根键前缀:', keyPath)
  } else {
    // 这种情况不应该发生，但保留兼容性处理
    console.warn('异常路径格式:', node.path, '期望格式:', selectedRootKey.value + '\\...')
    keyPath = node.path
  }
  
  console.log('加载虚拟树子节点:', node.path, '→ API keyPath:', keyPath)
  
  try {
    // 关键修复：统一使用分页API，确保一致性
    const result = await performRegistryOperationPaginated(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.ENUMERATE,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: keyPath,
        page: 1,
        page_size: 50
      }
    )
    
    console.log('虚拟树API响应:', result)
    
    if (result.success && result.key_data) {
      const children = result.key_data.sub_keys || []
      const hasMore = result.key_data.has_more_sub_keys || false
      
      console.log('设置子节点到虚拟树:', node.path, '子键数量:', children.length, '是否有更多:', hasMore)
      
      // 关键修复：确保正确调用setNodeChildren
      registryVirtualTree.value.setNodeChildren(node.path, children, hasMore)
      
      console.log('虚拟树节点加载成功:', children.length, '个子节点', hasMore ? '(还有更多)' : '')
    } else {
      console.error('虚拟树节点加载失败:', result)
      const errorMsg = result.error || '无法加载子键'
      
      // 对于权限相关错误，给出友好提示
      if (errorMsg.includes('没有权限访问') || errorMsg.includes('权限不足') || 
          errorMsg.includes('Permission') || errorMsg.includes('Access is denied')) {
        console.warn('权限不足，无法访问:', node.path)
        registryVirtualTree.value.setNodeChildren(node.path, [], false)
      } else {
        registryVirtualTree.value.setNodeChildren(node.path, [], false)
      }
    }
  } catch (error) {
    console.error('加载虚拟树节点失败:', error)
    registryVirtualTree.value.setNodeChildren(node.path, [], false)
  }
}

// 工具函数
const formatValueData = (data: string, type: string) => {
  if (!data) return ''
  
  // 限制显示长度
  const maxLength = 100
  const truncated = data.length > maxLength ? data.substring(0, maxLength) + '...' : data
  
  switch (type) {
    case 'REG_MULTI_SZ':
      return truncated.replace(/\n/g, ' | ')
    case 'REG_BINARY':
      return truncated.toUpperCase()
    default:
      return truncated
  }
}

const getValueTypeTagType = (type: string) => {
  switch (type) {
    case 'REG_SZ':
    case 'REG_EXPAND_SZ':
    case 'REG_MULTI_SZ':
      return 'primary'
    case 'REG_DWORD':
    case 'REG_QWORD':
      return 'success'
    case 'REG_BINARY':
      return 'warning'
    default:
      return 'info'
  }
}

const getMatchTypeLabel = (type: string) => {
  switch (type) {
    case 'key': return '键名'
    case 'value_name': return '值名'
    case 'value_data': return '值数据'
    default: return type
  }
}

const getRootKeyLabel = (value: string) => {
  const rootKey = rootKeys.find(k => k.value === value)
  return rootKey ? rootKey.label : value
}

// 树形结构操作函数
const expandTreeToPath = async () => {
  // 展开树形结构到当前路径（简化实现）
  console.log('展开树形结构到路径:', currentPath.value)
  // 这里可以根据需要实现具体的树节点展开逻辑
}

const refreshTreeNode = async (nodeData: RegistryTreeNode) => {
  try {
    // 如果是根节点，重新加载整个树
    if (!nodeData.path || nodeData.path === selectedRootKey.value) {
      await loadRootKey()
      return
    }
    
    // 重新加载指定节点的子节点
    const result = await performRegistryOperation(
      parseInt(props.terminalId),
      {
        operation: RegistryOperationType.READ,
        root_key: selectedRootKey.value as RegistryRootKey,
        key_path: nodeData.path
      }
    )
    
    if (result.success && result.key_data) {
      // 更新节点的子节点信息
      nodeData.children = result.key_data.sub_keys.map((subKeyName: string) => ({
        name: subKeyName,
        path: `${nodeData.path}\\${subKeyName}`,
        children: []
      }))
      
      // 如果是当前选中的节点，也更新值列表
      if (nodeData.path === currentPath.value) {
        currentValues.value = result.key_data.values || []
      }
    }
  } catch (error) {
    console.error('刷新树节点失败:', error)
    // 如果单个节点刷新失败，退回到刷新整个树
    await loadRootKey()
  }
}

const refreshAfterKeyDeletion = async (deletedKeyData: RegistryTreeNode) => {
  try {
    // 如果删除的是当前选中的键，清空值列表并返回上级
    if (deletedKeyData.path === currentPath.value) {
      currentValues.value = []
      
      // 导航到父级路径
      const pathParts = deletedKeyData.path.split('\\')
      if (pathParts.length > 1) {
        pathParts.pop() // 移除最后一级
        currentPath.value = pathParts.join('\\')
        await navigateToPath()
      } else {
        // 如果是根级键，重新加载根键
        currentPath.value = selectedRootKey.value
        await loadRootKey()
      }
    }
    
    // 找到并刷新父节点
    const pathParts = deletedKeyData.path.split('\\')
    if (pathParts.length > 1) {
      pathParts.pop() // 移除被删除的键名
      const parentPath = pathParts.join('\\')
      
      // 简化实现：重新加载整个树以确保状态一致性
      await loadRootKey()
      
      // 如果有父路径，导航回去
      if (parentPath && parentPath !== selectedRootKey.value) {
        currentPath.value = parentPath
        await navigateToPath()
      }
    } else {
      // 如果删除的是根级键，重新加载根键
      await loadRootKey()
    }
  } catch (error) {
    console.error('删除后刷新失败:', error)
    // 保险起见，重新加载整个树
    await loadRootKey()
  }
}

// 注册表操作API调用已从@/api/registry导入

// 监听当前路径变化，更新备份表单
watch(currentPath, (newPath) => {
  if (newPath) {
    const parts = newPath.split('\\')
    backupForm.rootKey = parts[0] as RegistryRootKey
    backupForm.keyPath = parts.slice(1).join('\\')
  }
})

// 监听激活状态，只在首次激活时加载数据
watch(
  () => props.hasBeenActivated,
  (hasBeenActivated) => {
    if (hasBeenActivated && !treeData.value.length) {
      // 使用nextTick确保虚拟树组件完全渲染后再加载数据
      nextTick(() => {
        loadRootKey()
      })
    }
  },
  { immediate: true }
)

// 组件挂载时初始化（不再自动加载数据）
onMounted(() => {
  // 移除自动加载逻辑，改为按需加载
  console.log('RegistryBrowser组件已挂载，等待激活状态')
})
</script>

<style scoped>
.registry-browser {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.placeholder-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.placeholder-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.placeholder-text {
  color: #909399;
  font-size: 14px;
  margin: 8px 0 0 0;
}

.registry-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 工具栏组件样式 */
.root-key-select {
  width: 180px;
  flex-shrink: 0;
}

.path-input {
  flex: 1;
  min-width: 300px;
}

.action-btn {
  flex-shrink: 0;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.backup-btn {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.registry-content {
  flex: 1;
  display: flex;
  height: calc(100% - 80px);
  min-height: 600px;
  max-height: 800px;
}

.registry-tree {
  width: 350px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  max-height: 100%;
  overflow: hidden;
}

.tree-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  font-weight: 600;
  flex-shrink: 0;
  color: #303133;
  font-size: 14px;
}

.tree-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.node-icon {
  color: #606266;
}

.node-actions {
  margin-left: auto;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-btn-group {
  border-radius: 4px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.04);
}

.node-btn-group .el-button {
  border: 1px solid #dcdfe6;
}

.node-btn-group .el-button:hover {
  z-index: 1;
}

.registry-values {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.values-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
  flex-shrink: 0;
  min-height: 50px;
  box-sizing: border-box;
}

.header-title {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.header-btn {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.value-name {
  display: flex;
  align-items: center;
  gap: 6px;
}

.value-icon {
  color: #909399;
}

.value-data {
  font-family: 'Consolas', 'Monaco', monospace;
  word-break: break-all;
}

.search-options {
  display: flex;
  gap: 10px;
}

.search-results {
  max-height: 500px;
  overflow-y: auto;
}

.results-summary {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__expand-icon) {
  padding: 4px;
}

/* 自定义滚动条样式 */
.tree-container::-webkit-scrollbar {
  width: 8px;
}

.tree-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.tree-container::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 4px;
  transition: background 0.2s;
}

.tree-container::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
}

.value-btn-group {
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.value-btn-group .el-button {
  margin: 0;
  border: 1px solid #dcdfe6;
  transition: all 0.2s ease;
}

.value-btn-group .el-button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.value-btn-group .el-button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.value-btn-group .el-button:hover {
  z-index: 1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}
</style> 