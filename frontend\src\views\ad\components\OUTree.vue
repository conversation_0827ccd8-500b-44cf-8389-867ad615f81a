<template>
  <div class="ou-tree-container">
    <el-tree
      ref="treeRef"
      :data="ouData"
      :props="defaultProps"
      node-key="dn"
      highlight-current
      @node-click="handleNodeClick"
    >
      <template #default="{ node, data }">
        <span class="custom-tree-node">
          <span>{{ node.label }}</span>
          <span class="actions">
            <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, data)">
              <el-icon><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <Authority permission="ad:ou:manage">
                    <el-dropdown-item command="add">
                      <el-icon><Plus /></el-icon>添加子OU
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:ou:manage">
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:ou:manage">
                    <el-dropdown-item command="move">
                      <el-icon><Position /></el-icon>移动
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="ad:ou:manage">
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </span>
        </span>
      </template>
    </el-tree>

    <!-- 添加/编辑OU对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加OU' : '编辑OU'"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="ouFormRef"
        :model="ouForm"
        :rules="rules"
        label-width="80px"
        status-icon
      >
        <el-form-item label="名称" prop="name">
          <el-input 
            v-model="ouForm.name" 
            :maxlength="64"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="ouForm.description"
            type="textarea"
            :rows="3"
            :maxlength="256"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <Authority permission="ad:ou:manage">
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ submitting ? '提交中...' : '确定' }}
            </el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>

    <!-- 添加测试结果抽屉 -->
    <el-drawer
      v-model="testResultVisible"
      title="测试结果"
      size="50%"
      :destroy-on-close="true"
    >
      <div class="test-result-content">
        <div v-if="testResult" class="result-section">
          <h3>连接信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="连接状态">
              <el-tag :type="testResult.connection?.bound ? 'success' : 'danger'">
                {{ testResult.connection?.bound ? '已连接' : '未连接' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="服务器">
              {{ testResult.connection?.server }}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
              {{ testResult.connection?.username }}
            </el-descriptions-item>
            <el-descriptions-item label="搜索基础">
              {{ testResult.connection?.search_base }}
            </el-descriptions-item>
          </el-descriptions>

          <h3 class="mt-4">搜索结果</h3>
          <template v-if="testResult.search_result">
            <div class="mb-2">
              <el-tag type="info">
                总数: {{ testResult.search_result.total_users || testResult.search_result.total_groups || 0 }}
              </el-tag>
            </div>
            
            <el-table
              v-if="testResult.search_result.users"
              :data="testResult.search_result.users"
              border
              stripe
              style="width: 100%"
            >
              <el-table-column prop="username" label="用户名" />
              <el-table-column prop="name" label="显示名称" />
              <el-table-column prop="email" label="邮箱" />
              <el-table-column prop="enabled" label="状态">
                <template #default="scope">
                  <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
                    {{ scope.row.enabled ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <el-table
              v-if="testResult.search_result.groups"
              :data="testResult.search_result.groups"
              border
              stripe
              style="width: 100%"
            >
              <el-table-column prop="name" label="组名" />
              <el-table-column prop="description" label="描述" />
              <el-table-column prop="memberCount" label="成员数" />
            </el-table>
          </template>

          <div v-if="testResult.raw_result" class="mt-4">
            <h3>原始结果</h3>
            <el-input
              type="textarea"
              :rows="4"
              v-model="testResult.search_result.raw_result"
              readonly
            />
          </div>
        </div>

        <div v-if="!testResult.success" class="error-section">
          <el-alert
            :title="testResult.message"
            type="error"
            :description="testResult.details?.args?.join('\n')"
            show-icon
          />
        </div>
      </div>
    </el-drawer>

    <!-- 移动OU对话框 -->
    <el-dialog
      v-model="moveOUVisible"
      title="移动组织单位"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      destroy-on-close
    >
      <div class="move-ou-content">
        <p class="mb-3">
          <el-alert
            type="info"
            :closable="false"
            show-icon
          >
            <b>当前OU:</b> {{ currentNode?.name }}
          </el-alert>
        </p>
        <p class="mb-3">请选择要移动到的目标OU:</p>
        <el-tree
          ref="targetOUTreeRef"
          :data="ouData"
          :props="defaultProps"
          node-key="dn"
          highlight-current
          @node-click="handleTargetOUSelect"
        >
          <template #default="{ node }">
            <span>{{ node.label }}</span>
          </template>
        </el-tree>
        <div class="selected-target-ou mt-3" v-if="targetOU">
          <el-alert
            type="success"
            :closable="false"
            show-icon
          >
            <b>已选目标OU:</b> {{ getOUNameByDN(targetOU) }}
          </el-alert>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveOUVisible = false">取消</el-button>
          <Authority permission="ad:ou:manage">
            <el-button 
              type="primary" 
              @click="confirmMoveOU"
              :loading="movingOU"
              :disabled="!targetOU"
            >
              确定
            </el-button>
          </Authority>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled, Monitor, Connection, Edit, Delete, Position } from '@element-plus/icons-vue'
import { getOUTree, createOU, updateOU, deleteOU, testGetUsers, testGetGroups, moveOU } from '@/api/ad'
import Authority from '@/components/Authority/index.vue'

const emit = defineEmits(['select'])

const ouData = ref([])
const defaultProps = {
  children: 'children',
  label: 'name',
  disabled: false
}

const dialogVisible = ref(false)
const dialogType = ref('add')
const ouFormRef = ref(null)
const currentNode = ref(null)

const ouForm = ref({
  name: '',
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入OU名称', trigger: 'blur' }
  ]
}

const testingUsers = ref(false)
const testingGroups = ref(false)

const testResultVisible = ref(false)
const testResult = ref(null)

const submitting = ref(false)

const moveOUVisible = ref(false)
const targetOU = ref('')
const movingOU = ref(false)

const fetchOUData = async () => {
  try {
    console.log('开始获取OU数据')
    const { data } = await getOUTree()
    console.log('获取到的OU数据:', data)
    ouData.value = Array.isArray(data) ? data : []
  } catch (error) {
    console.error('获取OU数据失败:', error)
    ElMessage.error('获取组织单位数据失败')
    ouData.value = [] // 确保失败时设置为空数组
  }
}

// 添加错误处理的watch
watch(ouData, (newVal) => {
  console.log('OU数据更新:', newVal)
}, { deep: true })

onMounted(() => {
  console.log('OUTree组件已加载')
  fetchOUData()
})

const handleNodeClick = (data) => {
  console.log('点击节点:', data)
  if (!data || !data.dn) {
    console.warn('节点数据不完整:', data)
    return
  }
  currentNode.value = data
  emit('select', data.dn)
}

const handleAddOU = () => {
  dialogType.value = 'add'
  ouForm.value = { name: '', description: '' }
  dialogVisible.value = true
}

const handleCommand = async (command, data) => {
  if (command === 'add') {
    dialogType.value = 'add'
    ouForm.value = { name: '', description: '' }
    dialogVisible.value = true
    currentNode.value = data
  } else if (command === 'edit') {
    dialogType.value = 'edit'
    ouForm.value = {
      name: data.name || '',
      description: data.description || ''
    }
    dialogVisible.value = true
    currentNode.value = data
  } else if (command === 'move') {
    showMoveOUDialog(data)
  } else if (command === 'delete') {
    ElMessageBox.confirm(
      `确定要删除OU "${data.name}"吗？${data.children && data.children.length > 0 ? '此操作将删除该OU下的所有子OU！' : ''}`,
      '确认删除',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(async () => {
        try {
          await deleteOU(data.dn)
          ElMessage.success('删除成功')
          fetchOUData()
        } catch (error) {
          console.error('删除OU失败:', error)
          ElMessage.error(`删除失败: ${error.message || '未知错误'}`)
        }
      })
      .catch(() => {
        // 取消删除
      })
  } else if (command === 'test-users') {
    testUsers()
  } else if (command === 'test-groups') {
    testGroups()
  }
}

const handleDialogClose = () => {
  ouFormRef.value?.resetFields()
  ouForm.value = { name: '', description: '' }
  dialogVisible.value = false
}

const handleSubmit = async () => {
  if (!ouFormRef.value) return
  
  try {
    await ouFormRef.value.validate()
    submitting.value = true
    
    const submitData = {
      name: ouForm.value.name.trim(),
      description: (ouForm.value.description || '').trim()
    }

    if (!submitData.name) {
      ElMessage.error('OU名称不能为空')
      return
    }

    if (dialogType.value === 'add') {
      if (!currentNode.value?.dn) {
        ElMessage.error('请选择父级OU')
        return
      }
      await createOU({
        ...submitData,
        parent_dn: currentNode.value.dn
      })
    } else {
      if (!currentNode.value?.dn) {
        ElMessage.error('当前节点DN不存在')
        return
      }
      await updateOU(currentNode.value.dn, submitData)
    }
    
    handleDialogClose()
    await fetchOUData()
    ElMessage({
      message: `${dialogType.value === 'add' ? '添加' : '更新'}成功`,
      type: 'success',
      showClose: true
    })
  } catch (error) {
    console.error('操作失败:', error)
    const errorMsg = error.response?.data?.detail || error.message || '操作失败'
    ElMessage({
      message: errorMsg,
      type: 'error',
      showClose: true
    })
  } finally {
    submitting.value = false
  }
}

const testUsers = async () => {
  if (!currentNode.value?.dn) return
  
  testingUsers.value = true
  try {
    const { data } = await testGetUsers(currentNode.value.dn)
    testResult.value = data
    testResultVisible.value = true
  } catch (error) {
    ElMessage.error('测试失败')
    testResult.value = {
      success: false,
      message: '测试执行失败',
      details: { args: [error.message] }
    }
    testResultVisible.value = true
  } finally {
    testingUsers.value = false
  }
}

const testGroups = async () => {
  if (!currentNode.value?.dn) return
  
  testingGroups.value = true
  try {
    const { data } = await testGetGroups(currentNode.value.dn)
    testResult.value = data
    testResultVisible.value = true
  } catch (error) {
    ElMessage.error('测试失败')
    testResult.value = {
      success: false,
      message: '测试执行失败',
      details: { args: [error.message] }
    }
    testResultVisible.value = true
  } finally {
    testingGroups.value = false
  }
}

// 添加刷新方法
const refresh = async () => {
  console.log('开始刷新OU树')
  await fetchOUData()
}

// 显示移动OU对话框
const showMoveOUDialog = (data) => {
  currentNode.value = data
  targetOU.value = ''
  moveOUVisible.value = true
}

// 处理目标OU选择
const handleTargetOUSelect = (data) => {
  targetOU.value = data.dn
}

// 根据DN获取OU名称
const getOUNameByDN = (dn) => {
  // 扁平化OU树以便于查找
  const flattenTree = (tree, result = []) => {
    for (const node of tree) {
      result.push(node)
      if (node.children && node.children.length > 0) {
        flattenTree(node.children, result)
      }
    }
    return result
  }
  
  const flatTree = flattenTree(ouData.value)
  const ou = flatTree.find(item => item.dn === dn)
  return ou ? ou.name : dn
}

// 确认移动OU
const confirmMoveOU = async () => {
  if (!currentNode.value || !targetOU.value) {
    ElMessage.warning('请选择目标OU')
    return
  }

  // 验证源OU和目标OU是否相同
  if (currentNode.value.dn === targetOU.value) {
    ElMessage.warning('不能将OU移动到其自身')
    return
  }

  // 验证目标OU不是源OU的子OU
  if (targetOU.value.includes(currentNode.value.dn)) {
    ElMessage.warning('不能将OU移动到其子OU中')
    return
  }

  try {
    movingOU.value = true
    await moveOU(currentNode.value.dn, targetOU.value)
    ElMessage.success('OU移动成功')
    moveOUVisible.value = false
    fetchOUData() // 刷新OU树
  } catch (error) {
    console.error('移动OU失败:', error)
    ElMessage.error(`移动OU失败: ${error.response?.data?.detail || error.message || '未知错误'}`)
  } finally {
    movingOU.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  refresh
})
</script>

<style scoped>
.ou-tree-container {
  height: 100%;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.custom-tree-node:hover .actions {
  opacity: 1;
}

/* 确保树组件填满容器 */
:deep(.el-tree) {
  height: 100%;
  background: transparent;
  padding: 20px;
}

/* 改进树节点样式 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #f0f7ff;
  color: #409eff;
}

.test-result-content {
  padding: 20px;
}

.result-section h3 {
  margin: 16px 0;
  color: #303133;
  font-size: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.error-section {
  margin-top: 20px;
}

:deep(.el-descriptions) {
  margin: 16px 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__wrapper) {
  max-width: 100%;
}

.move-ou-content {
  padding: 20px;
}

.selected-target-ou {
  margin-top: 16px;
}
</style> 