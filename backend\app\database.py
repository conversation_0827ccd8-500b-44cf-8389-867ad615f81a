from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from .config import get_settings

settings = get_settings()
SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL

# PostgreSQL连接参数
connect_args = {}

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args=connect_args,
    # PostgreSQL连接池配置 - 优化启动性能
    pool_size=20,        # 增加连接池大小
    max_overflow=30,     # 增加最大溢出连接
    pool_pre_ping=True,  # 连接前检查
    pool_recycle=3600,   # 连接回收时间
    echo=False,          # 禁用SQL日志输出
    future=True          # 使用SQLAlchemy 2.0模式
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 上下文管理器，用于非异步环境中的数据库会话
@contextmanager
def db_context():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()
