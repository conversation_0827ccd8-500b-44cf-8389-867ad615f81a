# 项目文件整理计划

## 背景
当前项目中测试代码和markdown文件分布较为凌乱，需要按功能模块进行重新组织。

## 整理方案
采用方案1：按功能模块分类整理

## 详细执行计划

### 阶段1：分析现有文件分布
- [x] 扫描所有测试文件位置
- [x] 扫描所有markdown文件位置
- [x] 分析文件功能归属

### 阶段2：创建目标目录结构
- [x] 创建 `docs/email/` - 邮箱管理相关文档
- [x] 创建 `docs/system/` - 系统功能文档
- [x] 检查并完善 `backend/tests/` 子目录

### 阶段3：移动测试文件
#### 根目录测试文件
- [x] `test_redis.py` → `backend/tests/utils/test_redis.py`

#### backend目录测试文件
- [x] `test_api.py` → `backend/tests/api/`
- [x] `test_auth.py` → `backend/tests/api/`
- [x] `test_middleware.py` → `backend/tests/middleware/`
- [x] `test_email_member.py` → `backend/tests/services/`
- [x] `test_function_api.py` → `backend/tests/api/`
- [x] `test_api_connection.py` → `backend/tests/api/`
- [x] `test_useroption_api.py` → `backend/tests/api/`
- [x] `test_ad_sync_log.py` → `backend/tests/services/`
- [x] `app/scripts/test_redis_cache.py` → `backend/tests/utils/`

### 阶段4：移动和整理markdown文件
#### 邮箱相关文档 → `docs/email/`
- [x] `邮箱登录权限管理功能实现总结.md`
- [x] `邮箱成员登录权限查看功能说明.md`
- [x] `邮箱管理模块修改总结.md`
- [x] `backend/邮箱管理模块问题修复总结.md`

#### 系统相关文档 → `docs/system/`
- [x] `backend/问题修复总结.md`

#### 保持现有位置
- [x] `README.md` - 根目录
- [x] `issues/` - 项目问题记录
- [x] `docs/终端管理功能*.md` - 已在合理位置

### 阶段5：清理和验证
- [x] 检查移动后的文件路径引用
- [x] 更新可能的import路径
- [x] 验证文件完整性

## 预期结果
- 测试文件按模块整齐归类在 `backend/tests/`
- 文档按功能模块分类在 `docs/` 对应子目录
- 项目结构更加清晰易维护

## 执行时间
预计执行时间：30-45分钟

## 执行完成总结

✅ **项目文件整理已完成！**

### 实际完成情况：
- **测试文件整理**：成功移动9个测试文件到`backend/tests/`对应模块
- **文档整理**：成功移动5个markdown文件到`docs/`对应功能目录
- **目录结构**：创建了完整的功能模块分类目录

### 最终目录结构：
```
backend/tests/
├── api/           # API相关测试 (5个文件)
├── services/      # 服务层测试 (2个文件)  
├── utils/         # 工具类测试 (2个文件)
├── middleware/    # 中间件测试 (1个文件)
├── crud/
├── models/
└── reports/

docs/
├── email/         # 邮箱管理文档 (4个文件)
├── system/        # 系统相关文档 (1个文件)
├── 终端管理功能开发计划.md
└── 终端管理功能需求文档.md
```

**执行时间**：约20分钟
**状态**：✅ 全部完成 