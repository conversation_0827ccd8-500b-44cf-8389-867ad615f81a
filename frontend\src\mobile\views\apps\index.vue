<template>
  <div class="mobile-apps">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索应用"
        shape="round"
        @search="handleSearch"
        @clear="handleClear"
      />
    </div>

    <!-- 应用网格 -->
    <div class="apps-content">
      <!-- 显示搜索结果 -->
      <div v-if="searchKeyword && filteredApps.length > 0" class="search-results">
        <div class="section-title">搜索结果</div>
        <div class="apps-grid">
          <div
            v-for="app in filteredApps"
            :key="app.id"
            class="app-item"
            @click="handleAppClick(app)"
          >
            <div class="app-icon">
              <van-icon :name="app.icon" size="32" :color="app.color" />
            </div>
            <div class="app-name">{{ app.name }}</div>
          </div>
        </div>
      </div>

      <!-- 显示搜索无结果 -->
      <div v-else-if="searchKeyword && filteredApps.length === 0" class="no-results">
        <van-empty description="未找到相关应用" />
      </div>

      <!-- 显示所有应用 -->
      <div v-else class="all-apps">
        <div class="apps-grid">
          <div
            v-for="app in permittedApps"
            :key="app.id"
            class="app-item"
            @click="handleAppClick(app)"
          >
            <div class="app-icon">
              <van-icon :name="app.icon" size="32" :color="app.color" />
            </div>
            <div class="app-name">{{ app.name }}</div>
          </div>
        </div>
      </div>

      <!-- 显示无权限应用提示 -->
      <div v-if="permittedApps.length === 0" class="no-permission">
        <van-empty description="暂无可用应用" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { getPermittedApps, type AppItem } from '@/mobile/config/apps'

const router = useRouter()
const userStore = useUserStore()

// 搜索关键词
const searchKeyword = ref('')

// 用户权限（这里先模拟，实际应该从用户store获取）
const userPermissions = computed(() => {
  // TODO: 从用户store获取实际权限
  return [
    'ad:view',
    'email:view',
    'asset:view',
    'terminal:view',
    'system:view'
  ]
})

// 获取用户有权限的应用
const permittedApps = computed((): AppItem[] => {
  return getPermittedApps(userPermissions.value)
})

// 搜索过滤的应用
const filteredApps = computed((): AppItem[] => {
  if (!searchKeyword.value) return []
  
  const keyword = searchKeyword.value.toLowerCase()
  
  return permittedApps.value.filter(app => 
    app.name.toLowerCase().includes(keyword) ||
    (app.description && app.description.toLowerCase().includes(keyword))
  )
})

// 应用点击处理
const handleAppClick = (app: AppItem) => {
  try {
    router.push(app.path)
  } catch (error) {
    console.error('导航失败:', error)
    showToast('页面跳转失败')
  }
}

// 搜索处理
const handleSearch = (value: string) => {
  console.log('搜索:', value)
}

// 清除搜索
const handleClear = () => {
  searchKeyword.value = ''
}

// 页面初始化
onMounted(() => {
  console.log('应用中心加载完成')
})
</script>

<style lang="scss" scoped>
@use '@/mobile/styles/theme.scss';

.mobile-apps {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 50px;

  .search-section {
    padding: 16px;
    background-color: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  .apps-content {
    padding: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24px;
      padding: 4px 0;
    }

    .search-results {
      .section-title {
        color: #1989fa;
      }
    }

    .no-results,
    .no-permission {
      padding: 48px 0;
      text-align: center;
    }
  }
}

// 应用网格布局
.apps-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  justify-items: center;

  .app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    width: 80px;
    min-height: 100px;
    padding: 16px 8px;
    border-radius: 12px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
    
    &:active {
      transform: scale(0.95);
      background-color: #f5f5f5;
    }

    .app-icon {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.8);
    }

    .app-name {
      font-size: 14px;
      color: #333333;
      text-align: center;
      line-height: 1.3;
      font-weight: 500;
      width: 100%;
      max-height: 36px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}

// 搜索框样式
:deep(.van-search) {
  background-color: transparent;
  padding: 0;
  
  .van-search__content {
    background-color: #f7f8fa;
    border-radius: 18px;
    border: none;
  }
  
  .van-field__control {
    font-size: 14px;
  }
}



// 响应式适配
@media (max-width: 375px) {
  .apps-content {
    padding: 16px;
  }
  
  .apps-grid {
    gap: 16px;
    
    .app-item {
      width: 70px;
      min-height: 90px;
      padding: 12px 6px;
      
      .app-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 6px;
      }
      
      .app-name {
        font-size: 12px;
        max-height: 32px;
      }
    }
  }
}

@media (min-width: 414px) {
  .apps-grid {
    gap: 32px;
    
    .app-item {
      width: 90px;
      min-height: 110px;
      
      .app-icon {
        width: 52px;
        height: 52px;
        margin-bottom: 10px;
      }
      
      .app-name {
        font-size: 15px;
        max-height: 40px;
      }
    }
  }
}

// 大屏幕适配 - 4列布局
@media (min-width: 480px) {
  .apps-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style> 