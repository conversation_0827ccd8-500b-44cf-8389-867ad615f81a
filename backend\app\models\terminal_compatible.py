#!/usr/bin/env python3
"""兼容现有数据库结构的终端模型"""

from sqlalchemy import <PERSON>ole<PERSON>, Column, String, Integer, ForeignKey, Text, DateTime, BigInteger, JSON
from sqlalchemy.orm import relationship
import datetime
from app.database import Base

class TerminalCompatible(Base):
    """兼容现有数据库结构的终端设备模型"""
    __tablename__ = "terminals"

    # 使用现有的integer主键
    id = Column(Integer, primary_key=True, index=True)
    hostname = Column(String(255), nullable=False)
    ip_address = Column(String(50), nullable=False)
    mac_address = Column(String(50), nullable=True)
    
    # 使用现有的字符串字段存储基本信息
    os_info = Column(String, nullable=True)  # 操作系统信息
    cpu_info = Column(String, nullable=True)  # CPU信息
    memory_info = Column(String, nullable=True)  # 内存信息
    
    # 使用现有的JSON字段存储复杂信息
    disk_info = Column(JSON, nullable=True)  # 磁盘信息
    network_info = Column(JSON, nullable=True)  # 网络信息
    system_info = Column(JSON, nullable=True)  # 系统信息
    installed_software = Column(JSON, nullable=True)  # 已安装软件
    
    # 状态和时间字段
    status = Column(String(50), default="offline")  # 终端状态
    last_heartbeat = Column(DateTime, nullable=True)  # 最后心跳时间
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    agent_version = Column(String(50), nullable=True)

    # 关联表，使用integer外键
    hardware_info_compat = relationship("HardwareInfoCompatible", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    os_info_compat = relationship("OSInfoCompatible", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    software_list_compat = relationship("SoftwareCompatible", back_populates="terminal", cascade="all, delete-orphan")
    network_info_compat = relationship("NetworkInfoCompatible", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    last_login_user_compat = relationship("UserLoginInfoCompatible", back_populates="terminal", uselist=False, cascade="all, delete-orphan")
    commands_compat = relationship("TerminalCommandCompatible", back_populates="terminal", cascade="all, delete-orphan")

    # 兼容性属性，映射到现有字段
    @property
    def online_status(self):
        """在线状态，基于status字段判断"""
        return self.status == "online"
    
    @online_status.setter
    def online_status(self, value):
        """设置在线状态"""
        self.status = "online" if value else "offline"
    
    @property
    def last_heartbeat_time(self):
        """心跳时间，映射到last_heartbeat字段"""
        return self.last_heartbeat
    
    @last_heartbeat_time.setter
    def last_heartbeat_time(self, value):
        """设置心跳时间"""
        self.last_heartbeat = value
    
    @property
    def unique_id(self):
        """唯一ID，基于ID生成"""
        return f"terminal_{self.id}"
    
    @property
    def registration_time(self):
        """注册时间，映射到created_at"""
        return self.created_at


class HardwareInfoCompatible(Base):
    """兼容版硬件信息模型"""
    __tablename__ = "hardware_info"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"), unique=True)
    cpu_model = Column(String(255))
    cpu_cores = Column(Integer)
    memory_total = Column(BigInteger)  # 内存大小，单位KB
    serial_number = Column(String(255))
    manufacturer = Column(String(255))
    model = Column(String(255))
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="hardware_info_compat")
    disks = relationship("DiskInfoCompatible", back_populates="hardware", cascade="all, delete-orphan")


class DiskInfoCompatible(Base):
    """兼容版磁盘信息模型"""
    __tablename__ = "disk_info"

    id = Column(Integer, primary_key=True)
    hardware_id = Column(Integer, ForeignKey("hardware_info.id", ondelete="CASCADE"))
    name = Column(String(50))
    total_space = Column(BigInteger)  # 总容量，单位KB
    free_space = Column(BigInteger)   # 可用容量，单位KB
    filesystem = Column(String(50))
    mount_point = Column(String(255))
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    hardware = relationship("HardwareInfoCompatible", back_populates="disks")


class OSInfoCompatible(Base):
    """兼容版操作系统信息模型"""
    __tablename__ = "os_info"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"), unique=True)
    name = Column(String(100))
    version = Column(String(100))
    build = Column(String(100))
    architecture = Column(String(20))
    install_date = Column(String(50))
    installed_updates = Column(JSON, default=list)  # 已安装更新列表
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="os_info_compat")
    security_info = relationship("SecurityInfoCompatible", back_populates="os_info", uselist=False, cascade="all, delete-orphan")


class SecurityInfoCompatible(Base):
    """兼容版安全信息模型"""
    __tablename__ = "security_info"

    id = Column(Integer, primary_key=True)
    os_info_id = Column(Integer, ForeignKey("os_info.id", ondelete="CASCADE"), unique=True)
    firewall_enabled = Column(Boolean, default=False)
    antivirus = Column(String(255))
    antivirus_enabled = Column(Boolean, default=False)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    os_info = relationship("OSInfoCompatible", back_populates="security_info")


class SoftwareCompatible(Base):
    """兼容版软件信息模型"""
    __tablename__ = "software"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"))
    name = Column(String(255))
    version = Column(String(100))
    publisher = Column(String(255))
    install_date = Column(String(50))
    size = Column(BigInteger)  # 软件大小，单位KB
    install_location = Column(String(255))
    is_compliant = Column(Boolean, default=True)  # 是否合规
    usage_notes = Column(Text, nullable=True)  # 软件用途备注
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="software_list_compat")


class NetworkInfoCompatible(Base):
    """兼容版网络信息模型"""
    __tablename__ = "network_info"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"), unique=True)
    hostname = Column(String(255))
    domain = Column(String(255))
    dns_servers = Column(JSON, default=list)  # DNS服务器列表
    default_gateway = Column(String(50))
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="network_info_compat")
    interfaces = relationship("NetworkInterfaceCompatible", back_populates="network_info", cascade="all, delete-orphan")


class NetworkInterfaceCompatible(Base):
    """兼容版网络接口模型"""
    __tablename__ = "network_interfaces"

    id = Column(Integer, primary_key=True)
    network_info_id = Column(Integer, ForeignKey("network_info.id", ondelete="CASCADE"))
    name = Column(String(255))
    mac_address = Column(String(50))
    ip_address = Column(String(50))
    subnet_mask = Column(String(50))
    dhcp_enabled = Column(Boolean, default=True)
    is_connected = Column(Boolean, default=True)
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    network_info = relationship("NetworkInfoCompatible", back_populates="interfaces")


class UserLoginInfoCompatible(Base):
    """兼容版用户登录信息模型"""
    __tablename__ = "user_login_info"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"), unique=True)
    username = Column(String(255))
    full_name = Column(String(255))
    login_time = Column(String(50))
    domain = Column(String(255))
    update_time = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="last_login_user_compat")


class TerminalCommandCompatible(Base):
    """兼容版终端命令模型"""
    __tablename__ = "terminal_commands"

    id = Column(Integer, primary_key=True)
    terminal_id = Column(Integer, ForeignKey("terminals.id", ondelete="CASCADE"))
    type = Column(String(50))  # 命令类型：COLLECT_INFO, UPGRADE_AGENT, CUSTOM_COMMAND
    content = Column(Text)     # 命令内容
    create_time = Column(DateTime, default=datetime.datetime.utcnow)
    sent_time = Column(DateTime, nullable=True)  # 指令下发时间
    execute_time = Column(DateTime, nullable=True)  # 指令执行时间
    timeout = Column(Integer, default=3600)  # 超时时间，单位秒
    status = Column(String(20), default="pending")  # 状态：pending, sent, executed, timeout, failed
    result = Column(Text, nullable=True)  # 执行结果
    error = Column(Text, nullable=True)  # 错误信息
    execution_duration = Column(Integer, nullable=True)  # 执行耗时，单位毫秒

    # 关联
    terminal = relationship("TerminalCompatible", back_populates="commands_compat")


# 创建别名，以便现有代码可以继续使用原来的名称
Terminal = TerminalCompatible
HardwareInfo = HardwareInfoCompatible
DiskInfo = DiskInfoCompatible
OSInfo = OSInfoCompatible
SecurityInfo = SecurityInfoCompatible
Software = SoftwareCompatible
NetworkInfo = NetworkInfoCompatible
NetworkInterface = NetworkInterfaceCompatible
UserLoginInfo = UserLoginInfoCompatible
TerminalCommand = TerminalCommandCompatible 