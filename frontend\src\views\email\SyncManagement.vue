<template>
  <div class="unified-sync-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Refresh /></el-icon>
        <h2 class="page-title">同步管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>同步管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 主要内容区域 -->
    <el-card class="box-card" shadow="hover">
      <el-tabs v-model="activeTab" class="tabs-container">
        <!-- 数据同步选项卡 -->
        <el-tab-pane label="数据同步" name="data-sync">
          <!-- 同步操作卡片 -->
          <el-row :gutter="20" class="sync-cards">
            <!-- 部门同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><OfficeBuilding /></el-icon>
                  <h3>部门同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">从企业邮箱API同步部门结构和信息</p>
                  <div class="sync-status">
                    <span class="status-label">上次同步：</span>
                    <span class="status-time">{{ lastSyncTimes.departments || '未同步' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:department:sync']">
                      <el-button 
                        type="primary" 
                        @click="handleDepartmentSync"
                        :loading="syncStatus.departments"
                        style="width: 100%; margin-bottom: 8px;"
                      >
                        <el-icon><Refresh /></el-icon>
                        同步部门
                      </el-button>
                      <el-button 
                        type="success" 
                        @click="showDepartmentStructureSyncDialog"
                        :loading="syncStatus.departmentStructure"
                        style="width: 100%;"
                      >
                        <el-icon><Connection /></el-icon>
                        从基础信息同步部门结构
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 成员同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><User /></el-icon>
                  <h3>成员同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">从企业邮箱API同步成员信息</p>
                  <div class="sync-status">
                    <span class="status-label">上次同步：</span>
                    <span class="status-time">{{ lastSyncTimes.members || '未同步' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:member:sync']">
                      <el-button 
                        type="primary" 
                        @click="handleMemberSync"
                        :loading="syncStatus.members"
                        style="width: 100%"
                      >
                        <el-icon><Refresh /></el-icon>
                        同步成员
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 群组同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><UserFilled /></el-icon>
                  <h3>群组同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">从企业邮箱API同步邮件群组信息</p>
                  <div class="sync-status">
                    <span class="status-label">上次同步：</span>
                    <span class="status-time">{{ lastSyncTimes.groups || '未同步' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:group:sync']">
                      <el-button 
                        type="primary" 
                        @click="handleGroupSync"
                        :loading="syncStatus.groups"
                        style="width: 100%"
                      >
                        <el-icon><Refresh /></el-icon>
                        同步群组
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 第二行 -->
          <el-row :gutter="20" class="sync-cards">
            <!-- 标签同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><CollectionTag /></el-icon>
                  <h3>标签同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">从企业邮箱API同步标签信息</p>
                  <div class="sync-status">
                    <span class="status-label">上次同步：</span>
                    <span class="status-time">{{ lastSyncTimes.tags || '未同步' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:tag:sync']">
                      <el-button 
                        type="primary" 
                        @click="handleTagSync"
                        :loading="syncStatus.tags"
                        style="width: 100%"
                      >
                        <el-icon><Refresh /></el-icon>
                        同步标签
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 全量同步 -->
            <el-col :span="8">
              <el-card class="sync-card full-sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Operation /></el-icon>
                  <h3>全量同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">一键同步所有数据（部门、成员、群组、标签）</p>
                  <div class="sync-status">
                    <span class="status-label">上次全量同步：</span>
                    <span class="status-time">{{ lastSyncTimes.full || '未同步' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:sync:all']">
                      <el-button 
                        type="danger" 
                        @click="handleFullSync"
                        :loading="syncStatus.full"
                        style="width: 100%"
                      >
                        <el-icon><Operation /></el-icon>
                        全量同步
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 同步配置 -->
            <el-col :span="8">
              <el-card class="sync-card config-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Setting /></el-icon>
                  <h3>同步配置</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">配置自动同步策略和同步参数</p>
                  <div class="sync-status">
                    <span class="status-label">自动同步：</span>
                    <span class="status-time">{{ autoSyncEnabled ? '已启用' : '已禁用' }}</span>
                  </div>
                  <div class="sync-actions">
                    <el-button 
                      type="warning" 
                      @click="handleSyncConfig"
                      :disabled="!hasConfigPermission"
                      style="width: 100%"
                      :title="hasConfigPermission ? '' : '无权限访问同步配置'"
                    >
                      <el-icon><Setting /></el-icon>
                      同步配置
                    </el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 工号补全选项卡 -->
        <el-tab-pane label="工号补全" name="extid-completion">
          <!-- 工号补全统计卡片 -->
          <el-card class="stats-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><DataBoard /></el-icon>
                  工号补全统计
                </span>
                <el-button type="primary" size="small" @click="loadStats" :loading="statsLoading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-icon total">
                    <el-icon><UserFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.total_members }}</div>
                    <div class="stat-label">总成员数</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-icon completed">
                    <el-icon><CircleCheckFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.has_extid }}</div>
                    <div class="stat-label">已有工号</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-icon missing">
                    <el-icon><WarningFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.missing_extid }}</div>
                    <div class="stat-label">缺少工号</div>
                  </div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-icon rate">
                    <el-icon><TrendCharts /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.completion_rate }}%</div>
                    <div class="stat-label">完成率</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>

          <!-- 操作面板 -->
          <el-card class="action-card" shadow="hover" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><Tools /></el-icon>
                  工号补全操作
                </span>
              </div>
            </template>
            
            <div class="action-panel">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="action-section">
                    <h4>数据备份</h4>
                    <p class="section-desc">在执行工号补全前，建议先备份重要数据</p>
                    <div class="action-buttons">
                      <el-button type="warning" @click="createBackup" :loading="backupLoading">
                        <el-icon><FolderAdd /></el-icon>
                        创建备份
                      </el-button>
                      <el-button type="info" @click="showBackupList">
                        <el-icon><Folder /></el-icon>
                        查看备份
                      </el-button>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="action-section">
                    <h4>工号补全</h4>
                    <p class="section-desc">分析并补全缺失的工号信息</p>
                    <div class="action-buttons">
                      <el-tooltip 
                        content="数据量较大时，分析过程可能需要几分钟时间，请耐心等待"
                        placement="top"
                      >
                        <el-button type="primary" @click="analyzeCandidates" :loading="analyzeLoading">
                          <el-icon><Search /></el-icon>
                          {{ analyzeLoading ? '分析中...' : '分析候选者' }}
                        </el-button>
                      </el-tooltip>
                      <el-tooltip 
                        content="数据量较大时，自动补全过程可能需要几分钟时间，请耐心等待"
                        placement="top"
                      >
                        <el-button type="success" @click="executeAutoCompletion" :loading="executeLoading" :disabled="!hasCandidates">
                          <el-icon><Star /></el-icon>
                          {{ executeLoading ? '执行中...' : '自动补全' }}
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                        content="清除分析缓存，强制重新分析所有候选者"
                        placement="top"
                      >
                        <el-button type="warning" @click="clearCache" :loading="clearCacheLoading">
                          <el-icon><Refresh /></el-icon>
                          清除缓存
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="action-section">
                    <h4>工号重新补齐</h4>
                    <p class="section-desc">重新分析并修正已有的工号匹配</p>
                    <div class="action-buttons">
                      <el-tooltip 
                        content="分析可能需要重新补齐的工号"
                        placement="top"
                      >
                        <el-button type="primary" @click="showRecompletionDialog" :loading="recompletionAnalyzing">
                          <el-icon><Search /></el-icon>
                          分析候选者
                        </el-button>
                      </el-tooltip>
                      <el-tooltip 
                        content="自动重新补齐问题工号"
                        placement="top"
                      >
                        <el-button type="success" @click="executeSmartRecompletion" :loading="recompletionExecuting">
                          <el-icon><Star /></el-icon>
                          自动补齐
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                        content="清除所有工号重新匹配"
                        placement="top"
                      >
                        <el-button type="danger" @click="executeFullReset" :loading="recompletionExecuting">
                          <el-icon><Refresh /></el-icon>
                          清除重新
                        </el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!-- 候选者列表 -->
          <el-card class="candidates-card" shadow="hover" style="margin-top: 20px;" v-if="hasCandidates">
            <template #header>
              <div class="card-header">
                <span class="header-title">
                  <el-icon><List /></el-icon>
                  候选者列表 ({{ candidatesData.total }})
                </span>
                <div class="header-actions">
                  <el-input
                    v-model="searchText"
                    placeholder="搜索姓名或邮箱"
                    style="width: 200px; margin-right: 10px;"
                    @keyup.enter="handleSearch"
                    clearable
                  />
                  <el-select
                    v-model="statusFilter"
                    placeholder="状态筛选"
                    style="width: 120px; margin-right: 10px;"
                    @change="handleStatusFilter"
                    clearable
                  >
                    <el-option label="已匹配" value="matched" />
                    <el-option label="需手动" value="manual" />
                    <el-option label="待处理" value="pending" />
                  </el-select>
                  <el-button type="primary" @click="handleSearch">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                </div>
              </div>
            </template>
            
            <el-table :data="candidatesData.items" style="width: 100%" stripe v-loading="analyzeLoading">
              <el-table-column prop="name" label="成员姓名" width="120" />
              <el-table-column prop="email" label="邮箱地址" width="200" />
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="匹配结果" min-width="200">
                <template #default="scope">
                  <div v-if="scope.row.auto_match">
                    <strong>{{ scope.row.auto_match.person_name }}</strong> ({{ scope.row.auto_match.job_number }})
                    <br>
                    <small class="text-muted">{{ scope.row.auto_match.dept_name }}</small>
                  </div>
                  <div v-else-if="scope.row.matches && scope.row.matches.length > 0">
                    <el-popover placement="bottom" width="400" trigger="click">
                      <template #reference>
                        <el-button type="text" size="small">
                          {{ scope.row.matches.length }} 个候选匹配
                        </el-button>
                      </template>
                      <div class="match-list">
                        <div v-for="match in scope.row.matches" :key="match.person_id" class="match-item">
                          <div class="match-info">
                            <strong>{{ match.person_name }}</strong> ({{ match.job_number }})
                            <br>
                            <small>{{ match.dept_name }} | 相似度: {{ (match.similarity * 100).toFixed(1) }}%</small>
                          </div>
                          <el-button type="primary" size="small" @click="confirmMatch(scope.row, match)">
                            选择
                          </el-button>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                  <span v-else class="text-muted">无匹配结果</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="scope">
                  <div class="action-buttons">
                    <el-button
                      v-if="scope.row.auto_match"
                      type="success"
                      size="small"
                      @click="confirmAutoMatch(scope.row)"
                      :loading="scope.row.confirming"
                    >
                      确认
                    </el-button>
                    <el-button
                      type="primary"
                      size="small"
                      @click="showManualSetDialog(scope.row)"
                    >
                      手动设置
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container" style="margin-top: 20px; text-align: center;">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="candidatesData.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-tab-pane>

        <!-- 人员同步选项卡 -->
        <el-tab-pane label="人员同步" name="personnel-sync">
          <!-- 人员同步操作卡片 -->
          <el-row :gutter="20" class="sync-cards">
            <!-- 增量同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Refresh /></el-icon>
                  <h3>增量同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">同步最近24小时内变更的人员信息到腾讯邮箱</p>
                  <div class="sync-status">
                    <span class="status-label">状态：</span>
                    <span class="status-time">{{ incrementalSyncLoading ? '同步中...' : '就绪' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:personnel:sync']">
                      <el-button 
                        type="primary" 
                        @click="triggerIncrementalSyncDryRun"
                        :loading="incrementalSyncLoading"
                        style="width: 100%; margin-bottom: 8px;"
                      >
                        <el-icon><Search /></el-icon>
                        试运行
                      </el-button>
                      <el-button 
                        type="success" 
                        @click="triggerIncrementalSync"
                        :loading="incrementalSyncLoading"
                        style="width: 100%"
                      >
                        <el-icon><Refresh /></el-icon>
                        正式执行
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 全量同步 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Operation /></el-icon>
                  <h3>全量同步</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">同步所有有工号的人员信息到腾讯邮箱</p>
                  <div class="sync-status">
                    <span class="status-label">状态：</span>
                    <span class="status-time">{{ fullSyncLoading ? '同步中...' : '就绪' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:personnel:sync']">
                      <el-button 
                        type="primary" 
                        @click="triggerFullSyncDryRun"
                        :loading="fullSyncLoading"
                        style="width: 100%; margin-bottom: 8px;"
                      >
                        <el-icon><Search /></el-icon>
                        试运行
                      </el-button>
                      <el-button 
                        type="danger" 
                        @click="triggerFullSync"
                        :loading="fullSyncLoading"
                        style="width: 100%"
                      >
                        <el-icon><Operation /></el-icon>
                        正式执行
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>

            <!-- 数据一致性检查 -->
            <el-col :span="8">
              <el-card class="sync-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Search /></el-icon>
                  <h3>数据一致性</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">检查人员信息与邮箱数据的一致性</p>
                  <div class="sync-status">
                    <span class="status-label">状态：</span>
                    <span class="status-time">{{ consistencyLoading ? '检查中...' : '就绪' }}</span>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:personnel:view']">
                      <el-button 
                        type="primary" 
                        @click="handleDataConsistencyCheck"
                        :loading="consistencyLoading"
                        style="width: 100%; margin-bottom: 8px;"
                      >
                        <el-icon><Search /></el-icon>
                        一致性检查
                      </el-button>
                      <el-button 
                        type="info" 
                        @click="activeTab = 'logs'"
                        style="width: 100%"
                      >
                        <el-icon><List /></el-icon>
                        查看日志
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 第二行：同步配置 -->
          <el-row :gutter="20" class="sync-cards">
            <!-- 人员同步配置 -->
            <el-col :span="24">
              <el-card class="sync-card config-card" shadow="hover">
                <div class="sync-card-header">
                  <el-icon class="sync-icon"><Setting /></el-icon>
                  <h3>人员同步配置</h3>
                </div>
                <div class="sync-card-content">
                  <p class="sync-description">配置人员同步的基本参数、自动化策略和同步源筛选</p>
                  <div class="config-info">
                    <div class="info-row">
                      <span class="info-label">自动同步：</span>
                      <el-tag :type="personnelSyncConfig?.enabled ? 'success' : 'info'" size="small">
                        {{ personnelSyncConfig?.enabled ? '已启用' : '已禁用' }}
                      </el-tag>
                    </div>
                    <div class="info-row" v-if="personnelSyncConfig?.filter_enabled">
                      <span class="info-label">同步筛选：</span>
                      <el-tag type="warning" size="small">已启用</el-tag>
                      <span class="filter-summary" v-if="filterStats">
                        (总人员：{{ filterStats.total_personnel }} → 筛选后：{{ filterStats.filtered_personnel }})
                      </span>
                    </div>
                  </div>
                  <div class="sync-actions">
                    <Authority :value="['email:personnel:config']">
                      <el-button 
                        type="primary" 
                        @click="openPersonnelSyncConfigDialog"
                        style="width: 200px; margin-right: 10px;"
                      >
                        <el-icon><Setting /></el-icon>
                        同步配置
                      </el-button>
                      <el-button 
                        type="info" 
                        @click="updateFilterPreview"
                        :loading="previewLoading"
                        style="width: 120px;"
                        size="default"
                      >
                        <el-icon><Refresh /></el-icon>
                        刷新统计
                      </el-button>
                    </Authority>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 同步日志选项卡 -->
        <el-tab-pane label="同步日志" name="logs">
          <el-card class="log-card">
            <template #header>
              <div class="card-header">
                <span>同步日志</span>
                <el-button type="primary" size="small" @click="refreshLogs">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            
            <el-table
              v-loading="logsLoading"
              :data="syncLogs"
              stripe
              style="width: 100%"
              max-height="400"
            >
              <el-table-column prop="type" label="同步类型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getSyncTypeColor(row.type)">
                    {{ getSyncTypeName(row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                    {{ row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="消息" min-width="200" show-overflow-tooltip />
              <el-table-column prop="count" label="同步数量" width="100" />
              <el-table-column prop="duration" label="耗时(秒)" width="100" />
              <el-table-column prop="created_at" label="同步时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="viewLogDetails(row)"
                    :loading="detailsLoading"
                  >
                    <el-icon><View /></el-icon>
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="logsPagination.page"
                v-model:page-size="logsPagination.size"
                :total="logsPagination.total"
                :page-sizes="[10, 20, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleLogsPageSizeChange"
                @current-change="handleLogsPageChange"
              />
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 同步配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="同步配置"
      width="600px"
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        label-width="120px"
      >
        <el-form-item label="自动同步">
          <el-switch
            v-model="configForm.autoSyncEnabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="同步间隔" v-if="configForm.autoSyncEnabled">
          <el-select v-model="configForm.syncInterval" style="width: 200px">
            <el-option label="每小时" value="1h" />
            <el-option label="每6小时" value="6h" />
            <el-option label="每12小时" value="12h" />
            <el-option label="每天" value="24h" />
          </el-select>
        </el-form-item>
        <el-form-item label="同步范围">
          <el-checkbox-group v-model="configForm.syncTypes">
            <el-checkbox label="departments">部门</el-checkbox>
            <el-checkbox label="members">成员</el-checkbox>
            <el-checkbox label="groups">群组</el-checkbox>
            <el-checkbox label="tags">标签</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="失败重试次数">
          <el-input-number
            v-model="configForm.retryCount"
            :min="0"
            :max="5"
            style="width: 200px"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveConfig">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 手动设置工号对话框 -->
    <el-dialog v-model="manualSetDialogVisible" title="手动设置工号" width="600px">
      <el-form :model="manualSetForm" label-width="120px">
        <el-form-item label="成员姓名">
          <el-input v-model="manualSetForm.name" disabled />
        </el-form-item>
        <el-form-item label="邮箱地址">
          <el-input v-model="manualSetForm.email" disabled />
        </el-form-item>
        <el-form-item label="选择人员">
          <el-select
            v-model="manualSetForm.selected_person"
            placeholder="请搜索并选择人员"
            filterable
            remote
            :remote-method="searchPersonnel"
            :loading="personnelLoading"
            reserve-keyword
            clearable
            style="width: 100%"
            popper-class="personnel-select-dropdown"
            @change="handlePersonnelSelect"
          >
            <el-option
              v-for="person in personnelOptions"
              :key="person.user_id || person.id"
              :label="`${person.user_name} (${person.job_number || '无工号'}) - ${person.dept_name || '无部门'}`"
              :value="person.user_id || person.id"
            >
              <div style="display: flex; align-items: center; width: 100%; padding: 8px 0;">
                <span style="font-weight: 500; color: #303133; margin-right: 12px; min-width: 80px;">
                  {{ person.user_name }}
                </span>
                <span style="color: #606266; margin-right: 12px; font-size: 13px;">
                  |
                </span>
                <span style="color: #409eff; margin-right: 12px; font-size: 13px; white-space: nowrap;">
                  {{ person.job_number ? `工号:${person.job_number}` : '工号:无' }}
                </span>
                <span style="color: #606266; margin-right: 12px; font-size: 13px;">
                  |
                </span>
                <span style="color: #67c23a; font-size: 13px; white-space: nowrap;">
                  {{ person.dept_name ? `部门:${person.dept_name}` : '部门:无' }}
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工号" required>
          <el-input
            v-model="manualSetForm.job_number"
            placeholder="请输入工号或选择人员自动填充"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="manualSetDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmManualSet"
            :loading="manualSetLoading"
          >
            确认设置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 备份列表对话框 -->
    <el-dialog v-model="backupDialogVisible" title="数据备份列表" width="800px">
      <el-table :data="backupList" style="width: 100%">
        <el-table-column prop="backup_id" label="备份ID" width="200" />
        <el-table-column prop="backup_time" label="备份时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.backup_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="danger" size="small" @click="restoreBackup(scope.row.backup_id)">
              恢复
            </el-button>
            <el-button type="info" size="small" @click="deleteBackup(scope.row.backup_id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 重新补齐对话框 -->
    <el-dialog v-model="recompletionDialogVisible" title="工号重新补齐" width="1200px" :close-on-click-modal="false">
      <div v-if="!recompletionAnalysisResult">
        <el-form :model="recompletionForm" label-width="120px">
          <el-form-item label="重新补齐策略">
            <el-radio-group v-model="recompletionForm.strategy_type">
              <el-radio label="smart_recompletion" style="margin-bottom: 10px;">
                <strong>智能重新补齐（推荐）</strong><br>
                <small class="text-muted">只重新补齐低可信度的匹配，保留正确的匹配结果</small>
              </el-radio>
              <el-radio label="full_reset" style="margin-bottom: 10px;">
                <strong>全量重置</strong><br>
                <small class="text-muted">清空所有工号重新匹配，适用于算法大幅优化后</small>
              </el-radio>
              <el-radio label="selective" style="margin-bottom: 10px;">
                <strong>选择性重新补齐</strong><br>
                <small class="text-muted">手动选择需要重新补齐的成员</small>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="相似度阈值">
            <el-slider v-model="recompletionForm.similarity_threshold" :min="0.6" :max="1.0" :step="0.1" show-tooltip />
            <small class="text-muted">阈值越高匹配越严格，建议保持0.8</small>
          </el-form-item>
          <el-form-item label="安全选项">
            <el-checkbox v-model="recompletionForm.backup_before_operation">操作前自动备份数据</el-checkbox>
          </el-form-item>
          <el-form-item label="批处理大小">
            <el-input-number v-model="recompletionForm.batch_size" :min="10" :max="100" />
            <small class="text-muted">每批处理的成员数量，建议保持50</small>
          </el-form-item>
        </el-form>
      </div>

      <div v-else>
        <el-alert
          :title="`分析完成：共${recompletionAnalysisResult.total_members_with_extid}个已有工号成员，发现${recompletionAnalysisResult.candidates_for_recompletion}个需要重新补齐`"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-descriptions :column="4" border style="margin-bottom: 20px;">
          <el-descriptions-item label="高可信度匹配">{{ recompletionAnalysisResult.high_confidence_matches }}</el-descriptions-item>
          <el-descriptions-item label="低可信度匹配">{{ recompletionAnalysisResult.low_confidence_matches }}</el-descriptions-item>
          <el-descriptions-item label="无匹配结果">{{ recompletionAnalysisResult.no_match_found }}</el-descriptions-item>
          <el-descriptions-item label="需要处理">{{ recompletionAnalysisResult.candidates_for_recompletion }}</el-descriptions-item>
        </el-descriptions>

        <el-table :data="recompletionAnalysisResult.candidates" style="width: 100%" max-height="400" stripe>
          <el-table-column type="selection" width="55" v-if="recompletionForm.strategy_type === 'selective'" />
          <el-table-column prop="name" label="成员姓名" width="120" />
          <el-table-column prop="email" label="邮箱地址" width="200" />
          <el-table-column prop="current_extid" label="当前工号" width="120" />
          <el-table-column prop="current_match_confidence" label="当前可信度" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.current_match_confidence > 0.8 ? 'success' : 'warning'">
                {{ (scope.row.current_match_confidence * 100).toFixed(1) }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="reason_for_recompletion" label="重新补齐原因" min-width="150" />
          <el-table-column prop="recommended_action" label="推荐操作" width="100">
            <template #default="scope">
              <el-tag :type="getRecommendedActionType(scope.row.recommended_action)">
                {{ getRecommendedActionText(scope.row.recommended_action) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="新匹配结果" min-width="200">
            <template #default="scope">
              <div v-if="scope.row.new_matches && scope.row.new_matches.length > 0">
                <div v-for="match in scope.row.new_matches.slice(0, 2)" :key="match.person_id" style="margin-bottom: 5px;">
                  <strong>{{ match.person_name }}</strong> ({{ match.job_number }})
                  <small> - {{ (match.similarity * 100).toFixed(1) }}%</small>
                </div>
                <small v-if="scope.row.new_matches.length > 2" class="text-muted">
                  +{{ scope.row.new_matches.length - 2 }}个更多匹配
                </small>
              </div>
              <span v-else class="text-muted">无匹配结果</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeRecompletionDialog">取消</el-button>
          <el-button v-if="!recompletionAnalysisResult" type="primary" @click="analyzeRecompletion" :loading="recompletionAnalyzing">
            分析候选者
          </el-button>
          <el-button v-if="recompletionAnalysisResult" type="info" @click="previewRecompletion" :loading="recompletionExecuting">
            预览结果 (试运行)
          </el-button>
          <el-button v-if="recompletionAnalysisResult" type="danger" @click="executeRecompletionAction" :loading="recompletionExecuting">
            正式执行
          </el-button>
        </span>
      </template>
    </el-dialog>



    <!-- 人员同步配置对话框 -->
    <el-dialog 
      v-model="personnelSyncConfigDialogVisible" 
      title="人员同步配置" 
      width="800px"
      :close-on-click-modal="false">
      <el-tabs type="border-card">
        <!-- 基本配置选项卡 -->
        <el-tab-pane label="基本配置" name="basic">
          <el-form :model="personnelSyncConfigForm" label-width="140px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="启用自动同步">
                  <el-switch v-model="personnelSyncConfigForm.enabled" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="同步间隔(小时)">
                  <el-input-number v-model="personnelSyncConfigForm.sync_interval" :min="1" :max="24" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="定时同步时间">
              <el-input v-model="personnelSyncConfigForm.sync_time" placeholder="例如: 08:00" style="width: 200px" />
              <span class="form-tip">24小时格式，例如：08:00</span>
            </el-form-item>
            
            <el-divider content-position="left">同步策略</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="自动创建用户">
                  <el-switch v-model="personnelSyncConfigForm.auto_create_users" />
                </el-form-item>
                <el-form-item label="自动更新用户">
                  <el-switch v-model="personnelSyncConfigForm.auto_update_users" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自动禁用离职用户">
                  <el-switch v-model="personnelSyncConfigForm.auto_disable_users" />
                </el-form-item>
                <el-form-item label="自动创建部门">
                  <el-switch v-model="personnelSyncConfigForm.auto_create_departments" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        
        <!-- 同步源筛选选项卡 -->
        <el-tab-pane label="同步源筛选" name="filter">
          <el-form :model="personnelSyncConfigForm" label-width="140px">
            <el-form-item label="启用筛选">
              <el-switch v-model="personnelSyncConfigForm.filter_enabled" />
              <span class="form-tip">启用后只同步符合条件的人员</span>
            </el-form-item>
            
                         <template v-if="personnelSyncConfigForm.filter_enabled">
               <el-divider content-position="left">筛选条件</el-divider>
               <el-form-item label="包含公司">
                 <el-select 
                   v-model="personnelSyncConfigForm.included_companies" 
                   multiple 
                   placeholder="请选择要同步的公司"
                   style="width: 100%"
                   collapse-tags
                   collapse-tags-tooltip
                   @change="onCompanySelectionChange">
                   <el-option v-for="company in availableCompanies" :key="company" :label="company" :value="company" />
                 </el-select>
                 <div class="form-tip">选择要包含在同步中的公司，留空表示包含所有公司。选择公司后，部门列表将自动过滤</div>
               </el-form-item>
               
               <el-form-item label="包含部门">
                 <el-select 
                   v-model="personnelSyncConfigForm.included_departments" 
                   multiple 
                   placeholder="请选择要同步的部门"
                   style="width: 100%"
                   collapse-tags
                   collapse-tags-tooltip>
                   <el-option v-for="department in availableDepartments" :key="department" :label="department" :value="department" />
                 </el-select>
                 <div class="form-tip">选择要包含在同步中的部门，留空表示包含所有部门</div>
               </el-form-item>
              
              <el-form-item label="包含职位">
                <el-select 
                  v-model="personnelSyncConfigForm.included_job_titles" 
                  multiple 
                  placeholder="请选择要同步的职位"
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip>
                  <el-option v-for="jobTitle in availableJobTitles" :key="jobTitle" :label="jobTitle" :value="jobTitle" />
                </el-select>
                <div class="form-tip">选择要包含在同步中的职位，留空表示包含所有职位</div>
              </el-form-item>
              
              <el-form-item label="排除职位">
                <el-select 
                  v-model="personnelSyncConfigForm.excluded_job_titles" 
                  multiple 
                  placeholder="请选择要排除的职位"
                  style="width: 100%"
                  collapse-tags
                  collapse-tags-tooltip>
                  <el-option v-for="jobTitle in availableJobTitles" :key="jobTitle" :label="jobTitle" :value="jobTitle" />
                </el-select>
                <div class="form-tip">选择要从同步中排除的职位</div>
              </el-form-item>
              
              <el-form-item label="筛选逻辑">
                <el-radio-group v-model="personnelSyncConfigForm.filter_logic">
                  <el-radio label="AND">与（AND）</el-radio>
                  <el-radio label="OR">或（OR）</el-radio>
                </el-radio-group>
                <div class="form-tip">
                  与（AND）：同时满足所有条件；或（OR）：满足任一条件即可
                </div>
              </el-form-item>
              
                                            <!-- 筛选效果预览 -->
               <el-form-item label="筛选效果">
                 <div class="filter-effect-container">
                   <div v-if="filterStats" class="filter-stats-display">
                     <div class="filter-stats-grid">
                       <div class="stat-item">
                         <span class="stat-label">总人员数</span>
                         <span class="stat-value">{{ filterStats.total_personnel }}</span>
                       </div>
                       <div class="stat-item">
                         <span class="stat-label">筛选后</span>
                         <span class="stat-value">{{ filterStats.filtered_personnel }}</span>
                       </div>
                       <div class="stat-item">
                         <span class="stat-label">减少比例</span>
                         <el-tag :type="filterStats.reduction_rate > 50 ? 'success' : 'warning'" size="small">
                           {{ filterStats.reduction_rate.toFixed(1) }}%
                         </el-tag>
                       </div>
                     </div>
                   </div>
                   <el-button 
                     type="primary" 
                     size="small" 
                     @click="updateFilterPreview" 
                     :loading="previewLoading"
                     class="update-preview-btn">
                     <el-icon><Refresh /></el-icon>
                     更新预览
                   </el-button>
                 </div>
               </el-form-item>
            </template>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="personnelSyncConfigDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePersonnelSyncConfig" :loading="saveFilterLoading">
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 同步日志详情对话框 -->
    <el-dialog 
      v-model="detailsDialogVisible" 
      title="同步日志详情" 
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="currentLogDetails" class="log-details-container">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataBoard /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">同步类型：</span>
                <el-tag :type="getSyncTypeColor(currentLogDetails.sync_type)">
                  {{ getSyncTypeName(currentLogDetails.sync_type) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <el-tag :type="currentLogDetails.status === 'success' ? 'success' : 'danger'">
                  {{ currentLogDetails.status === 'success' ? '成功' : '失败' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">操作员：</span>
                <span class="detail-value">{{ currentLogDetails.operator || '系统' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">耗时：</span>
                <span class="detail-value">{{ currentLogDetails.duration || '未知' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">开始时间：</span>
                <span class="detail-value">{{ formatDateTime(currentLogDetails.start_time) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <span class="detail-label">完成时间：</span>
                <span class="detail-value">{{ formatDateTime(currentLogDetails.completed_at) }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row v-if="currentLogDetails.message">
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">同步消息：</span>
                <div class="detail-message">{{ currentLogDetails.message }}</div>
              </div>
            </el-col>
          </el-row>
          <el-row v-if="currentLogDetails.error_message">
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">错误信息：</span>
                <div class="detail-error">{{ currentLogDetails.error_message }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 统计信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>统计信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ currentLogDetails.total_count || 0 }}</div>
                <div class="stat-label">总数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number success">{{ currentLogDetails.synced_count || 0 }}</div>
                <div class="stat-label">同步数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number info">{{ currentLogDetails.updated_count || 0 }}</div>
                <div class="stat-label">更新数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number warning">{{ currentLogDetails.created_count || 0 }}</div>
                <div class="stat-label">创建数量</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number danger">{{ currentLogDetails.error_count || 0 }}</div>
                <div class="stat-label">错误数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number disabled">{{ currentLogDetails.disabled_count || 0 }}</div>
                <div class="stat-label">禁用数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ currentLogDetails.processed_count || 0 }}</div>
                <div class="stat-label">处理数量</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 详细信息 -->
        <el-card v-if="currentLogDetails.details" class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>详细信息</span>
            </div>
          </template>
          
          <!-- 人员同步详情 -->
          <div v-if="currentLogDetails.sync_type === 'personnel' && currentLogDetails.details">
            <!-- 检测结果 -->
            <div v-if="currentLogDetails.details.detection_result" class="details-section">
              <h4>检测结果</h4>
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">总人员数：</span>
                    <span class="info-value">{{ currentLogDetails.details.detection_result.total_personnel }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">邮箱成员数：</span>
                    <span class="info-value">{{ currentLogDetails.details.detection_result.total_email_members }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">检测起始时间：</span>
                    <span class="info-value">{{ formatDateTime(currentLogDetails.details.detection_result.since_time) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 部门操作 -->
            <div v-if="currentLogDetails.details.department_operations" class="details-section">
              <h4>部门操作</h4>
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">创建部门数：</span>
                    <span class="info-value">{{ currentLogDetails.details.department_operations.created?.length || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">失败部门数：</span>
                    <span class="info-value">{{ currentLogDetails.details.department_operations.failed?.length || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">缓存命中数：</span>
                    <span class="info-value">{{ currentLogDetails.details.department_operations.cached?.length || 0 }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 操作结果 -->
            <div v-if="currentLogDetails.details.operation_results?.length" class="details-section">
              <h4>操作结果 (前10条)</h4>
              <el-table
                :data="currentLogDetails.details.operation_results"
                size="small"
                style="width: 100%"
                max-height="300"
              >
                <el-table-column prop="job_number" label="工号" width="100" />
                <el-table-column prop="change_type" label="操作类型" width="100">
                  <template #default="{ row }">
                    <el-tag size="small" :type="getChangeTypeColor(row.change_type)">
                      {{ getChangeTypeName(row.change_type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="success" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag size="small" :type="row.success ? 'success' : 'danger'">
                      {{ row.success ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="message" label="消息" min-width="200" show-overflow-tooltip />
              </el-table>
            </div>
          </div>

          <!-- 部门同步详情 -->
          <div v-else-if="currentLogDetails.sync_type === 'departments_from_personnel' && currentLogDetails.details">
            <!-- 同步请求参数 -->
            <div v-if="currentLogDetails.details.request" class="details-section">
              <h4>同步配置</h4>
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">同步源：</span>
                    <span class="info-value">{{ getDeptSyncSourceName(currentLogDetails.details.request.source) }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">同步模式：</span>
                    <span class="info-value">{{ getDeptSyncModeName(currentLogDetails.details.request.mode) }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">试运行：</span>
                    <el-tag :type="currentLogDetails.details.request.dry_run ? 'warning' : 'success'" size="small">
                      {{ currentLogDetails.details.request.dry_run ? '是' : '否' }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="16" style="margin-top: 12px;">
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">创建层级：</span>
                    <el-tag :type="currentLogDetails.details.request.create_hierarchy ? 'success' : 'info'" size="small">
                      {{ currentLogDetails.details.request.create_hierarchy ? '是' : '否' }}
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">覆盖已存在：</span>
                    <el-tag :type="currentLogDetails.details.request.overwrite_existing ? 'warning' : 'info'" size="small">
                      {{ currentLogDetails.details.request.overwrite_existing ? '是' : '否' }}
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="info-item">
                    <span class="info-label">跳过空部门：</span>
                    <el-tag :type="currentLogDetails.details.request.skip_empty_departments ? 'success' : 'info'" size="small">
                      {{ currentLogDetails.details.request.skip_empty_departments ? '是' : '否' }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
              <el-row v-if="currentLogDetails.details.request.company_id" :gutter="16" style="margin-top: 12px;">
                <el-col :span="24">
                  <div class="info-item">
                    <span class="info-label">指定公司ID：</span>
                    <span class="info-value">{{ currentLogDetails.details.request.company_id }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 统计信息 -->
            <div v-if="currentLogDetails.details.stats" class="details-section">
              <h4>同步统计</h4>
              <el-row :gutter="16">
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">总部门数：</span>
                    <span class="info-value">{{ currentLogDetails.details.stats.total_departments || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">创建数量：</span>
                    <span class="info-value success">{{ currentLogDetails.details.stats.created_departments || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">更新数量：</span>
                    <span class="info-value info">{{ currentLogDetails.details.stats.updated_departments || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">跳过数量：</span>
                    <span class="info-value warning">{{ currentLogDetails.details.stats.skipped_departments || 0 }}</span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="16" style="margin-top: 12px;">
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">失败数量：</span>
                    <span class="info-value danger">{{ currentLogDetails.details.stats.failed_departments || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">无效数量：</span>
                    <span class="info-value disabled">{{ currentLogDetails.details.stats.invalid_departments || 0 }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">成功率：</span>
                    <span class="info-value">{{ (currentLogDetails.details.stats.success_rate * 100).toFixed(1) }}%</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">操作数量：</span>
                    <span class="info-value">{{ currentLogDetails.details.operations_count || 0 }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 错误和警告 -->
            <div v-if="currentLogDetails.details.errors?.length || currentLogDetails.details.warnings?.length" class="details-section">
              <h4>错误和警告</h4>
              <div v-if="currentLogDetails.details.errors?.length" style="margin-bottom: 16px;">
                <h5 style="color: #f56c6c; margin-bottom: 8px;">错误信息 ({{ currentLogDetails.details.errors.length }})</h5>
                <el-alert
                  v-for="(error, index) in currentLogDetails.details.errors"
                  :key="`error-${index}`"
                  :title="error"
                  type="error"
                  :closable="false"
                  style="margin-bottom: 8px;"
                />
              </div>
              <div v-if="currentLogDetails.details.warnings?.length">
                <h5 style="color: #e6a23c; margin-bottom: 8px;">警告信息 ({{ currentLogDetails.details.warnings.length }})</h5>
                <el-alert
                  v-for="(warning, index) in currentLogDetails.details.warnings"
                  :key="`warning-${index}`"
                  :title="warning"
                  type="warning"
                  :closable="false"
                  style="margin-bottom: 8px;"
                />
              </div>
            </div>
          </div>

          <!-- 通用JSON展示 -->
          <div v-else class="json-details">
            <pre>{{ JSON.stringify(currentLogDetails.details, null, 2) }}</pre>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailsDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 部门结构同步对话框 -->
    <el-dialog
      v-model="departmentStructureSyncDialogVisible"
      title="从基础信息同步部门结构"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form :model="departmentSyncRequest" label-width="120px">
        <el-form-item label="同步范围">
          <el-radio-group v-model="departmentSyncRequest.source">
            <el-radio label="all">全部部门</el-radio>
            <el-radio label="company">按公司</el-radio>
            <el-radio label="department">按部门</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="departmentSyncRequest.source === 'company'" label="选择公司">
          <el-select
            v-model="departmentSyncRequest.company_id"
            placeholder="请选择公司"
            filterable
            :loading="companiesLoading"
            clearable
          >
            <el-option
              v-for="company in companies"
              :key="company.id"
              :label="company.name"
              :value="company.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item v-if="departmentSyncRequest.source === 'department'" label="选择部门">
          <el-select
            v-model="departmentSyncRequest.department_id"
            placeholder="请选择部门"
            filterable
            :loading="departmentsLoading"
            clearable
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="同步模式">
          <el-radio-group v-model="departmentSyncRequest.mode">
            <el-radio label="create_only">仅创建新部门</el-radio>
            <el-radio label="create_update">创建和更新</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="选项">
          <div class="checkbox-group">
            <el-checkbox v-model="departmentSyncRequest.create_hierarchy">创建完整部门层级</el-checkbox>
            <el-checkbox v-model="departmentSyncRequest.overwrite_existing" :disabled="departmentSyncRequest.mode === 'create_only'">
              覆盖已存在的部门
            </el-checkbox>
            <el-checkbox v-model="departmentSyncRequest.skip_empty_departments">
              跳过无在职人员的部门
            </el-checkbox>
          </div>
        </el-form-item>
      </el-form>
      
      <!-- 同步结果显示 -->
      <div v-if="departmentSyncResult" class="sync-result">
        <el-divider>同步结果</el-divider>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行状态">
            <el-tag :type="departmentSyncResult.success ? 'success' : 'danger'">
              {{ departmentSyncResult.success ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行耗时">
            {{ departmentSyncResult.duration.toFixed(2) }}秒
          </el-descriptions-item>
          <el-descriptions-item label="总部门数">
            {{ departmentSyncResult.stats.total_departments }}
          </el-descriptions-item>
          <el-descriptions-item label="成功创建">
            {{ departmentSyncResult.stats.created_departments }}
          </el-descriptions-item>
          <el-descriptions-item label="更新部门">
            {{ departmentSyncResult.stats.updated_departments }}
          </el-descriptions-item>
          <el-descriptions-item label="跳过部门">
            {{ departmentSyncResult.stats.skipped_departments }}
          </el-descriptions-item>
          <el-descriptions-item label="无效部门" v-if="departmentSyncResult.stats.invalid_departments > 0">
            <el-tooltip content="无在职人员或已弃用的部门" placement="top">
              <span>{{ departmentSyncResult.stats.invalid_departments }}</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="失败部门">
            {{ departmentSyncResult.stats.failed_departments }}
          </el-descriptions-item>
          <el-descriptions-item label="成功率">
            {{ (departmentSyncResult.stats.success_rate * 100).toFixed(1) }}%
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="result-message">
          <h4>执行消息</h4>
          <p>{{ departmentSyncResult.message }}</p>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="departmentSyncResult.errors.length > 0" class="error-messages">
          <h4>错误信息</h4>
          <el-alert
            v-for="(error, index) in departmentSyncResult.errors"
            :key="index"
            type="error"
            :title="error"
            :closable="false"
            style="margin-bottom: 8px;"
          />
        </div>
        
        <!-- 警告信息 -->
        <div v-if="departmentSyncResult.warnings.length > 0" class="warning-messages">
          <h4>警告信息</h4>
          <el-alert
            v-for="(warning, index) in departmentSyncResult.warnings"
            :key="index"
            type="warning"
            :title="warning"
            :closable="false"
            style="margin-bottom: 8px;"
          />
        </div>

        <!-- 详细操作信息 -->
        <div v-if="departmentSyncResult.operations && departmentSyncResult.operations.length > 0" class="operations-detail">
          <el-divider>部门操作详情</el-divider>
          <el-table
            :data="departmentSyncResult.operations"
            style="width: 100%"
            max-height="400"
            stripe
            border
          >
            <el-table-column prop="dept_info.dept_name" label="部门名称" width="200">
              <template #default="scope">
                <div style="display: flex; align-items: center;">
                  <span :style="{ marginLeft: (scope.row.dept_info.level - 1) * 20 + 'px' }">
                    {{ scope.row.dept_info.dept_name }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dept_info.level" label="层级" width="80" align="center" />
            <el-table-column prop="dept_info.company_name" label="所属公司" width="150" align="center">
              <template #default="scope">
                <span v-if="scope.row.dept_info.company_name" class="text-gray-600">
                  {{ scope.row.dept_info.company_name.split('（')[0] }}
                </span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="操作类型" width="100" align="center">
              <template #default="scope">
                <el-tag
                  :type="getOperationTagType(scope.row.operation)"
                  size="small"
                >
                  {{ getOperationText(scope.row.operation) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="parent_tencent_id" label="父部门ID" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.parent_tencent_id === 'DRY_RUN'" class="text-blue-500">
                  <el-tag type="info" size="small">预览模式</el-tag>
                </span>
                <span v-else-if="scope.row.parent_tencent_id && scope.row.parent_tencent_id !== '1'">
                  {{ scope.row.parent_tencent_id }}
                </span>
                <span v-else class="text-gray-400">根部门</span>
              </template>
            </el-table-column>
            <el-table-column prop="tencent_dept_id" label="腾讯部门ID" width="120" align="center">
              <template #default="scope">
                <span v-if="scope.row.tencent_dept_id === 'DRY_RUN'" class="text-blue-500">
                  <el-tag type="info" size="small">预览模式</el-tag>
                </span>
                <span v-else-if="scope.row.tencent_dept_id">{{ scope.row.tencent_dept_id }}</span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="操作结果" min-width="200">
              <template #default="scope">
                <span :class="scope.row.operation === 'error' ? 'text-red-500' : ''">
                  {{ scope.row.message }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="departmentStructureSyncDialogVisible = false">取消</el-button>
          <el-button type="info" @click="previewDepartmentStructureSync" :loading="departmentSyncLoading">
            预览
          </el-button>
          <el-button type="primary" @click="executeDepartmentStructureSync" :loading="departmentSyncLoading">
            执行同步
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 成员同步进度弹窗 -->
    <EmailSyncProgress
      v-model="showMemberSyncProgress"
      @completed="handleMemberSyncCompleted"
      @cancelled="handleMemberSyncCancelled"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { 
  Refresh, 
  OfficeBuilding, 
  User, 
  UserFilled, 
  CollectionTag, 
  Operation, 
  Setting,
  DataBoard,
  CircleCheckFilled,
  WarningFilled,
  TrendCharts,
  Tools,
  FolderAdd,
  Folder,
  Search,
  Star,
  List,
  Connection,
  Filter,
  Check,
  View
} from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'
import EmailSyncProgress from '@/components/EmailSyncProgress.vue'
import { hasPermission } from '@/utils/permission'
import { 
  syncDepartmentsFromApi,
  syncDepartmentsFromPersonnel,
  previewDepartmentsSyncFromPersonnel,
  getEcologyDepartmentStructure,
  type DepartmentSyncRequest,
  type DepartmentSyncResult
} from '@/api/email/department'
import { syncMembersFromApi } from '@/api/email/member'
import { syncGroupsFromApi } from '@/api/email/group'
import { syncTagsFromApi } from '@/api/email/tag'
import { emailSyncLogApi } from '@/api/email/sync'
import {
  getExtidCompletionStats,
  getExtidCompletionCandidates,
  executeAutoExtidCompletion,
  manualConfirmExtidMatch,
  manualSetExtid,
  clearCompletionCache,
  createDataBackup,
  getBackupList,
  restoreDataBackup,
  deleteDataBackup,
  analyzeRecompletionCandidates,
  executeRecompletion,
  triggerPersonnelSync,
  getPersonnelSyncStatus,
  checkDataConsistency,
  // 过滤相关导入
  getAvailableCompanies,
  getAvailableDepartments,
  getDepartmentsByCompany,
  getAvailableJobTitles,
  getFilterStats,
  previewFilterResults,
  getPersonnelSyncConfig,
  updatePersonnelSyncConfig,
  type ExtidCompletionStats,
  type ExtidCompletionResult,
  type ExtidCompletionPaginatedResponse,
  type RecompletionStrategy,
  type RecompletionAnalysisResult,
  type RecompletionResult,
  type PersonnelSyncResult,
  type PersonnelSyncStats,
  // 过滤相关类型
  type FilterPreviewRequest,
  type FilterPreviewResult,
  type PersonnelSyncConfigResponse,
  type PersonnelSyncConfigUpdate
} from '@/api/email/personnel-sync'
import { ecologyApi } from '@/api/ecology'
import type { EcologyUser } from '@/types/ecology'
import { formatDateTime } from '@/utils/date'

// 响应式数据
const activeTab = ref('data-sync')

// 权限相关
const hasConfigPermission = computed(() => hasPermission('email:sync:config'))
const logsLoading = ref(false)
const configDialogVisible = ref(false)
const autoSyncEnabled = ref(false)

// 日志详情相关
const detailsLoading = ref(false)
const detailsDialogVisible = ref(false)
const currentLogDetails = ref<any>(null)

// 工号补全相关
const statsLoading = ref(false)
const backupLoading = ref(false)
const analyzeLoading = ref(false)
const executeLoading = ref(false)
const manualSetLoading = ref(false)
const clearCacheLoading = ref(false)
const backupDialogVisible = ref(false)
const manualSetDialogVisible = ref(false)
const searchText = ref('')
const statusFilter = ref('')

// 人员选择相关
const personnelLoading = ref(false)
const personnelOptions = ref<EcologyUser[]>([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 重新补齐相关
const recompletionDialogVisible = ref(false)
const recompletionAnalyzing = ref(false)
const recompletionExecuting = ref(false)
const recompletionAnalysisResult = ref<RecompletionAnalysisResult | null>(null)
const recompletionForm = ref<RecompletionStrategy>({
  strategy_type: 'smart_recompletion',
  similarity_threshold: 0.8,
  backup_before_operation: true,
  batch_size: 50
})

// 人员信息同步相关
const incrementalSyncLoading = ref(false)
const fullSyncLoading = ref(false)
const consistencyLoading = ref(false)
const syncStatusLoading = ref(false)
const personnelSyncStatus = ref<any>(null)

// 过滤配置相关
const filterEnabled = ref(false)
const filterStats = ref<FilterPreviewResult | null>(null)
const filterStatsLoading = ref(false)
const previewLoading = ref(false)
const saveFilterLoading = ref(false)
const personnelSyncConfigDialogVisible = ref(false)
const personnelSyncConfig = ref<PersonnelSyncConfigResponse | null>(null)
const availableDepartments = ref<string[]>([])
const availableJobTitles = ref<string[]>([])
const availableCompanies = ref<string[]>([])
const personnelSyncConfigForm = ref<PersonnelSyncConfigUpdate & { included_companies?: string[] }>({
  enabled: false,
  sync_time: '',
  sync_interval: 24,
  auto_create_users: true,
  auto_update_users: true,
  auto_disable_users: true,
  auto_create_departments: true,
  filter_enabled: false,
  included_companies: [],
  included_departments: [],
  included_job_titles: [],
  excluded_job_titles: [],
  filter_logic: 'AND'
})

const stats = ref<ExtidCompletionStats>({
  total_members: 0,
  has_extid: 0,
  missing_extid: 0,
  completion_rate: 0
})

const candidatesData = ref<ExtidCompletionPaginatedResponse>({
  items: [],
  total: 0,
  page: 1,
  page_size: 20,
  total_pages: 0
})

const backupList = ref<any[]>([])

// 手动设置工号表单
const manualSetForm = ref({
  email_member_id: 0,
  name: '',
  email: '',
  job_number: '',
  selected_person: null as number | null
})

// 同步状态
const syncStatus = reactive({
  departments: false,
  departmentStructure: false,
  members: false,
  groups: false,
  tags: false,
  full: false
})

// 上次同步时间
const lastSyncTimes = reactive({
  departments: '',
  members: '',
  groups: '',
  tags: '',
  full: ''
})

// 同步日志
const syncLogs = ref([])

// 进度显示相关
const showMemberSyncProgress = ref(false)

// 日志分页
const logsPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 配置表单
const configForm = reactive({
  autoSyncEnabled: false,
  syncInterval: '24h',
  syncTypes: ['departments', 'members', 'groups', 'tags'],
  retryCount: 3
})

// 计算属性
const hasCandidates = computed(() => candidatesData.value.total > 0)

// 部门同步
const handleDepartmentSync = async () => {
  // 创建加载实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步部门数据，请耐心等待...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    syncStatus.departments = true
    
    // 调用API同步部门
    const response = await syncDepartmentsFromApi()
    
    // 显示成功信息，包含同步数量和耗时
    if (response && response.data) {
      const { message, synced_count, total_count, elapsed_time } = response.data
      ElMessage.success(message || `部门同步成功，共同步 ${synced_count}/${total_count} 个部门，耗时 ${elapsed_time}`)
    } else {
      ElMessage.success('部门同步成功')
    }
    
    // 刷新日志和同步时间
    await Promise.all([refreshLogs(), loadLatestSyncTimes()])
  } catch (error: any) {
    ElMessage.error('部门同步失败: ' + (error.message || '未知错误'))
  } finally {
    // 确保在任何情况下都关闭加载动画
    loadingInstance.close()
    syncStatus.departments = false
  }
}

// 成员同步
const handleMemberSync = async () => {
  try {
    syncStatus.members = true
    
    // 显示进度弹窗
    showMemberSyncProgress.value = true
    
    // 调用API同步成员
    const response = await syncMembersFromApi()
    
    // API调用成功，进度组件会通过SSE接收到完成消息
    // 这里不需要手动关闭进度窗口，由组件自己处理
    
  } catch (error: any) {
    // 如果API调用失败，关闭进度窗口并显示错误
    showMemberSyncProgress.value = false
    ElMessage.error('启动成员同步失败: ' + (error.message || '未知错误'))
  } finally {
    syncStatus.members = false
  }
}

// 处理成员同步完成
const handleMemberSyncCompleted = (result: any) => {
  // 同步完成后的处理
  ElMessage.success(result.message || '成员同步完成')
  
  // 刷新日志和同步时间
  refreshLogs()
  loadLatestSyncTimes()
  
  // 关闭进度窗口
  showMemberSyncProgress.value = false
}

// 处理成员同步取消
const handleMemberSyncCancelled = () => {
  // 同步取消后的处理
  ElMessage.info('成员同步已取消')
  showMemberSyncProgress.value = false
}

// 群组同步
const handleGroupSync = async () => {
  // 创建加载实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步群组数据，请耐心等待...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    syncStatus.groups = true
    
    // 调用API同步群组
    const response = await syncGroupsFromApi()
    
    // 显示成功信息
    if (response && response.data) {
      const { message } = response.data
      ElMessage.success(message || '群组同步成功')
    } else {
      ElMessage.success('群组同步成功')
    }
    
    // 刷新日志和同步时间
    await Promise.all([refreshLogs(), loadLatestSyncTimes()])
  } catch (error: any) {
    ElMessage.error('群组同步失败: ' + (error.message || '未知错误'))
  } finally {
    // 确保在任何情况下都关闭加载动画
    loadingInstance.close()
    syncStatus.groups = false
  }
}

// 标签同步
const handleTagSync = async () => {
  // 创建加载实例
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在同步标签数据，请耐心等待...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  
  try {
    syncStatus.tags = true
    
    // 调用API同步标签
    const response = await syncTagsFromApi()
    
    // 显示成功信息
    if (response && response.data) {
      const { message } = response.data
      ElMessage.success(message || '标签同步成功')
    } else {
      ElMessage.success('标签同步成功')
    }
    
    // 刷新日志和同步时间
    await Promise.all([refreshLogs(), loadLatestSyncTimes()])
  } catch (error: any) {
    ElMessage.error('标签同步失败: ' + (error.message || '未知错误'))
  } finally {
    // 确保在任何情况下都关闭加载动画
    loadingInstance.close()
    syncStatus.tags = false
  }
}

// 全量同步
const handleFullSync = async () => {
  try {
    await ElMessageBox.confirm(
      '全量同步将同步所有数据，可能需要较长时间，确定要继续吗？',
      '确认全量同步',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    syncStatus.full = true
    
    // 创建全局加载提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在进行全量同步，请耐心等待...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    // 记录开始时间
    const startTime = Date.now()
    
    try {
      // 1. 首先同步部门结构
      loadingInstance.setText('正在同步部门数据 (1/4)...')
      await syncDepartmentsFromApi()
      
      // 2. 然后同步成员信息
      loadingInstance.setText('正在同步成员数据 (2/4)...')
      await syncMembersFromApi()
      
      // 3. 同步群组信息
      loadingInstance.setText('正在同步群组数据 (3/4)...')
      await syncGroupsFromApi()
      
      // 4. 最后同步标签信息
      loadingInstance.setText('正在同步标签数据 (4/4)...')
      await syncTagsFromApi()
      
      // 计算总耗时
      const elapsedTime = ((Date.now() - startTime) / 1000).toFixed(2)
      
      // 关闭加载提示
      loadingInstance.close()
      
      ElMessage.success(`全量同步成功，总耗时 ${elapsedTime} 秒`)
      updateLastSyncTime('full')
      updateAllSyncTimes()
      refreshLogs()
    } catch (error) {
      // 关闭加载提示
      loadingInstance.close()
      throw error
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('全量同步失败: ' + (error.message || '未知错误'))
    }
  } finally {
    syncStatus.full = false
  }
}

// 同步配置
const handleSyncConfig = () => {
  configDialogVisible.value = true
}

// 保存配置
const handleSaveConfig = () => {
  autoSyncEnabled.value = configForm.autoSyncEnabled
  ElMessage.success('配置保存成功')
  configDialogVisible.value = false
}

// 更新同步时间
const updateLastSyncTime = (type: keyof typeof lastSyncTimes) => {
  lastSyncTimes[type] = formatDateTime(new Date())
}

// 更新所有同步时间
const updateAllSyncTimes = () => {
  const now = formatDateTime(new Date())
  lastSyncTimes.departments = now
  lastSyncTimes.members = now
  lastSyncTimes.groups = now
  lastSyncTimes.tags = now
}

// 获取同步类型名称
const getSyncTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    departments: '部门',
    members: '成员',
    groups: '群组',
    tags: '标签',
    full: '全量',
    personnel: '人员',
    departments_from_personnel: '部门同步'
  }
  return typeMap[type] || type
}

// 获取同步类型颜色
const getSyncTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    departments: 'primary',
    members: 'success',
    groups: 'warning',
    tags: 'info',
    full: 'danger',
    personnel: 'purple',
    departments_from_personnel: 'primary'
  }
  return colorMap[type] || 'default'
}

// 获取变更类型名称
const getChangeTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    CREATE: '创建',
    UPDATE: '更新',
    DISABLE: '禁用'
  }
  return typeMap[type] || type
}

// 获取变更类型颜色
const getChangeTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    CREATE: 'success',
    UPDATE: 'warning',
    DISABLE: 'info'
  }
  return colorMap[type] || 'default'
}

// 获取部门同步源名称
const getDeptSyncSourceName = (source: string) => {
  const sourceMap: Record<string, string> = {
    company: '按公司',
    department: '按部门',
    all: '全部部门'
  }
  return sourceMap[source] || source
}

// 获取部门同步模式名称
const getDeptSyncModeName = (mode: string) => {
  const modeMap: Record<string, string> = {
    create_only: '仅创建',
    create_update: '创建和更新'
  }
  return modeMap[mode] || mode
}

// 刷新日志
const refreshLogs = async () => {
  try {
    logsLoading.value = true
    const response = await emailSyncLogApi.getSyncLogs({
      page: logsPagination.page,
      size: logsPagination.size
    })
    
    if (response && response.data) {
      syncLogs.value = response.data.logs.map((log: any) => ({
        ...log,
        type: log.sync_type,
        count: log.synced_count
      }))
      logsPagination.total = response.data.total
    }
  } catch (error) {
    console.error('获取同步日志失败:', error)
    ElMessage.error('获取同步日志失败')
  } finally {
    logsLoading.value = false
  }
}

// 日志分页处理
const handleLogsPageSizeChange = (size: number) => {
  logsPagination.size = size
  logsPagination.page = 1
  refreshLogs()
}

const handleLogsPageChange = (page: number) => {
  logsPagination.page = page
  refreshLogs()
}

// 查看日志详情
const viewLogDetails = async (row: any) => {
  try {
    detailsLoading.value = true
    const response = await emailSyncLogApi.getSyncLog(row.id)
    if (response && response.data) {
      currentLogDetails.value = response.data
      detailsDialogVisible.value = true
    }
  } catch (error) {
    console.error('获取日志详情失败:', error)
    ElMessage.error('获取日志详情失败')
  } finally {
    detailsLoading.value = false
  }
}

// 获取最新同步时间
const loadLatestSyncTimes = async () => {
  try {
    const response = await emailSyncLogApi.getLatestSyncTimes()
    if (response && response.data) {
      Object.assign(lastSyncTimes, response.data)
    }
  } catch (error) {
    console.error('获取最新同步时间失败:', error)
  }
}

// 工号补全相关方法
const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await getExtidCompletionStats()
    stats.value = response.data
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

const createBackup = async () => {
  try {
    backupLoading.value = true
    await createDataBackup({
      tables: ['email_members', 'ecology_users'],
      description: `工号补全前数据备份 - ${new Date().toLocaleString('zh-CN')}`
    })
    ElMessage.success('数据备份创建成功')
  } catch (error) {
    console.error('创建备份失败:', error)
    ElMessage.error('创建备份失败')
  } finally {
    backupLoading.value = false
  }
}

const showBackupList = async () => {
  try {
    const response = await getBackupList()
    backupList.value = response.data
    backupDialogVisible.value = true
  } catch (error) {
    console.error('获取备份列表失败:', error)
    ElMessage.error('获取备份列表失败')
  }
}

const analyzeCandidates = async () => {
  try {
    analyzeLoading.value = true
    ElMessage.info('正在分析候选者，数据量较大请耐心等待...')
    await loadCandidates()
    ElMessage.success(`候选者分析完成，找到 ${candidatesData.value.total} 个候选者`)
  } catch (error: any) {
    console.error('分析候选者失败:', error)
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      ElMessage.error('分析超时，请稍后重试。如果数据量很大，这是正常现象。')
    } else {
      ElMessage.error('分析候选者失败')
    }
  } finally {
    analyzeLoading.value = false
  }
}

const loadCandidates = async () => {
  try {
    const response = await getExtidCompletionCandidates({
      similarity_threshold: 0.8,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchText.value || undefined,
      status_filter: statusFilter.value || undefined
    })
    candidatesData.value = response.data
  } catch (error) {
    console.error('加载候选者失败:', error)
    throw error
  }
}

const executeAutoCompletion = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要执行自动工号补全吗？此操作将更新腾讯企业邮箱数据。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (result === 'confirm') {
      executeLoading.value = true
      ElMessage.info('正在执行自动补全，可能需要几分钟时间，请耐心等待...')
      
      const response = await executeAutoExtidCompletion({
        similarity_threshold: 0.8,
        auto_confirm_exact_match: true,
        dry_run: false
      })

      const result = response.data
      ElMessage.success(
        `自动补全完成：自动匹配 ${result.auto_matched} 个，需手动处理 ${result.manual_required} 个，跳过 ${result.skipped} 个`
      )

      await loadStats()
      await loadCandidates()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行自动补全失败:', error)
      ElMessage.error('执行自动补全失败')
    }
  } finally {
    executeLoading.value = false
  }
}

// 分页相关方法
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize
  currentPage.value = 1
  loadCandidates()
}

const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage
  loadCandidates()
}

// 搜索和筛选相关方法
const handleSearch = () => {
  currentPage.value = 1
  loadCandidates()
}

const handleStatusFilter = () => {
  currentPage.value = 1
  loadCandidates()
}

// 手动设置工号相关方法
const showManualSetDialog = (candidate: ExtidCompletionResult) => {
  manualSetForm.value = {
    email_member_id: candidate.email_member_id,
    name: candidate.name,
    email: candidate.email,
    job_number: '',
    selected_person: null
  }
  
  // 清空人员选择选项，等待用户搜索
  personnelOptions.value = []
  
  manualSetDialogVisible.value = true
}

const confirmManualSet = async () => {
  try {
    if (!manualSetForm.value.job_number.trim()) {
      ElMessage.error('请输入工号')
      return
    }

    manualSetLoading.value = true

    await manualSetExtid({
      email_member_id: manualSetForm.value.email_member_id,
      job_number: manualSetForm.value.job_number.trim(),
      person_name: manualSetForm.value.name
    })

    ElMessage.success('工号设置成功')
    manualSetDialogVisible.value = false
    await loadStats()
    await loadCandidates()
  } catch (error) {
    console.error('设置工号失败:', error)
    ElMessage.error('设置工号失败')
  } finally {
    manualSetLoading.value = false
  }
}

// 清除缓存方法
const clearCache = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要清除分析缓存吗？清除后下次分析将重新计算所有数据。',
      '确认清除缓存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (result === 'confirm') {
      clearCacheLoading.value = true

      await clearCompletionCache()
      ElMessage.success('缓存已清除')

      // 清除缓存后重新加载数据
      await loadCandidates()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清除缓存失败:', error)
      ElMessage.error('清除缓存失败')
    }
  } finally {
    clearCacheLoading.value = false
  }
}

const confirmAutoMatch = async (candidate: ExtidCompletionResult) => {
  try {
    (candidate as any).confirming = true
    if (candidate.auto_match) {
      await manualConfirmExtidMatch({
        email_member_id: candidate.email_member_id,
        person_id: candidate.auto_match.person_id,
        job_number: candidate.auto_match.job_number
      })
      ElMessage.success('工号匹配确认成功')
      await loadStats()
      await loadCandidates()
    }
  } catch (error) {
    console.error('确认匹配失败:', error)
    ElMessage.error('确认匹配失败')
  } finally {
    (candidate as any).confirming = false
  }
}

const confirmMatch = async (candidate: ExtidCompletionResult, match: any) => {
  try {
    await manualConfirmExtidMatch({
      email_member_id: candidate.email_member_id,
      person_id: match.person_id,
      job_number: match.job_number
    })
    ElMessage.success('工号匹配确认成功')
    await loadStats()
    await loadCandidates()
  } catch (error) {
    console.error('确认匹配失败:', error)
    ElMessage.error('确认匹配失败')
  }
}

const restoreBackup = async (backupId: string) => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要恢复此备份吗？此操作将覆盖当前数据。',
      '确认恢复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (result === 'confirm') {
      await restoreDataBackup(backupId, {
        backup_id: backupId,
        confirm: true
      })
      ElMessage.success('数据恢复成功')
      backupDialogVisible.value = false
      await loadStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('恢复备份失败:', error)
      ElMessage.error('恢复备份失败')
    }
  }
}

const deleteBackup = async (backupId: string) => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要删除此备份吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (result === 'confirm') {
      await deleteDataBackup(backupId)
      ElMessage.success('备份删除成功')
      await showBackupList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error)
      ElMessage.error('删除备份失败')
    }
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'matched': return 'success'
    case 'manual': return 'warning'
    case 'pending': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'matched': return '已匹配'
    case 'manual': return '需手动'
    case 'pending': return '待处理'
    default: return '未知'
  }
}

// 搜索人员
const searchPersonnel = async (query: string) => {
  if (!query || query.length < 1) {
    // 如果查询为空，加载默认的少量数据
    try {
      personnelLoading.value = true
      const response = await ecologyApi.getLocalEcologyUsers({
        skip: 0,
        limit: 20
      })
      personnelOptions.value = response.data.filter(user => user.user_name && user.user_id)
    } catch (error) {
      console.error('加载默认人员数据失败:', error)
      ElMessage.error('加载人员数据失败')
    } finally {
      personnelLoading.value = false
    }
    return
  }

  try {
    personnelLoading.value = true
    const response = await ecologyApi.getLocalEcologyUsers({
      skip: 0,
      limit: 50,
      keyword: query,
      exact_match: /^\d+$/.test(query) // 如果是纯数字，使用精确匹配工号
    })
    
    // 只显示有用户名和用户ID的记录
    personnelOptions.value = response.data.filter(user => user.user_name && user.user_id)
  } catch (error) {
    console.error('搜索人员失败:', error)
    ElMessage.error('搜索人员失败')
  } finally {
    personnelLoading.value = false
  }
}

// 处理人员选择
const handlePersonnelSelect = (userId: number | null) => {
  if (!userId) {
    manualSetForm.value.job_number = ''
    return
  }

  const selectedPerson = personnelOptions.value.find(p => 
    (p.user_id === userId) || (p.id === userId)
  )
  
  if (selectedPerson && selectedPerson.job_number) {
    manualSetForm.value.job_number = selectedPerson.job_number
  }
}

// 重新补齐相关方法
const showRecompletionDialog = () => {
  recompletionDialogVisible.value = true
  recompletionAnalysisResult.value = null
}

const closeRecompletionDialog = () => {
  recompletionDialogVisible.value = false
  recompletionAnalysisResult.value = null
}

const analyzeRecompletion = async () => {
  try {
    recompletionAnalyzing.value = true
    ElMessage.info('正在分析重新补齐候选者，请稍等...')
    
    const response = await analyzeRecompletionCandidates({
      strategy_type: recompletionForm.value.strategy_type,
      similarity_threshold: recompletionForm.value.similarity_threshold,
      backup_before_operation: recompletionForm.value.backup_before_operation,
      batch_size: recompletionForm.value.batch_size
    })
    
    recompletionAnalysisResult.value = response.data
    ElMessage.success(`分析完成：发现 ${response.data.candidates_for_recompletion} 个需要重新补齐的候选者`)
  } catch (error) {
    console.error('分析重新补齐候选者失败:', error)
    ElMessage.error('分析失败')
  } finally {
    recompletionAnalyzing.value = false
  }
}

const previewRecompletion = async () => {
  try {
    recompletionExecuting.value = true
    ElMessage.info('正在预览重新补齐结果...')
    
    const response = await executeRecompletion({
      strategy: recompletionForm.value,
      dry_run: true
    })
    
    const result = response.data
    ElMessage.success(
      `预览完成：将保持 ${result.kept_unchanged} 个，更新 ${result.updated_extid} 个，清除 ${result.cleared_extid} 个`
    )
  } catch (error) {
    console.error('预览重新补齐失败:', error)
    ElMessage.error('预览失败')
  } finally {
    recompletionExecuting.value = false
  }
}

const executeRecompletionAction = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      '确定要执行重新补齐操作吗？此操作将修改已有的工号匹配。',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    if (confirmResult === 'confirm') {
      recompletionExecuting.value = true
      ElMessage.info('正在执行重新补齐，可能需要几分钟时间...')
      
      const response = await executeRecompletion({
        strategy: recompletionForm.value,
        dry_run: false
      })
      
      const result = response.data
      ElMessage.success(
        `重新补齐完成：保持 ${result.kept_unchanged} 个，更新 ${result.updated_extid} 个，清除 ${result.cleared_extid} 个，错误 ${result.errors} 个`
      )
      
      // 刷新统计数据和候选者列表
      await loadStats()
      if (hasCandidates.value) {
        await loadCandidates()
      }
      
      closeRecompletionDialog()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('执行重新补齐失败:', error)
      ElMessage.error('执行重新补齐失败')
    }
  } finally {
    recompletionExecuting.value = false
  }
}

const executeSmartRecompletion = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      '确定要执行智能重新补齐吗？这将自动修正低可信度的工号匹配。',
      '智能重新补齐',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )
    
    if (confirmResult === 'confirm') {
      recompletionExecuting.value = true
      ElMessage.info('正在执行智能重新补齐...')
      
      const response = await executeRecompletion({
        strategy: {
          strategy_type: 'smart_recompletion',
          similarity_threshold: 0.8,
          backup_before_operation: true,
          batch_size: 50
        },
        dry_run: false
      })
      
      const result = response.data
      ElMessage.success(
        `智能重新补齐完成：保持 ${result.kept_unchanged} 个，更新 ${result.updated_extid} 个，清除 ${result.cleared_extid} 个`
      )
      
      await loadStats()
      if (hasCandidates.value) {
        await loadCandidates()
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('智能重新补齐失败:', error)
      ElMessage.error('智能重新补齐失败')
    }
  } finally {
    recompletionExecuting.value = false
  }
}

const executeFullReset = async () => {
  try {
    const confirmResult = await ElMessageBox.confirm(
      '警告：全量重置将清空所有已有工号并重新匹配！此操作风险较高，确定要继续吗？',
      '全量重置确认',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'error',
      }
    )
    
    if (confirmResult === 'confirm') {
      recompletionExecuting.value = true
      ElMessage.info('正在执行全量重置，这可能需要较长时间...')
      
      const response = await executeRecompletion({
        strategy: {
          strategy_type: 'full_reset',
          similarity_threshold: 0.8,
          backup_before_operation: true,
          batch_size: 30  // 更小的批次更安全
        },
        dry_run: false
      })
      
      const result = response.data
      ElMessage.success(
        `全量重置完成：处理 ${result.total_processed} 个，更新 ${result.updated_extid} 个，清除 ${result.cleared_extid} 个`
      )
      
      await loadStats()
      if (hasCandidates.value) {
        await loadCandidates()
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('全量重置失败:', error)
      ElMessage.error('全量重置失败')
    }
  } finally {
    recompletionExecuting.value = false
  }
}

// 推荐操作相关辅助方法
const getRecommendedActionType = (action: string) => {
  switch (action) {
    case 'keep': return 'success'
    case 'update': return 'warning'
    case 'manual': return 'danger'
    default: return 'info'
  }
}

const getRecommendedActionText = (action: string) => {
  switch (action) {
    case 'keep': return '保持'
    case 'update': return '更新'
    case 'manual': return '手动'
    default: return '未知'
  }
}

// 人员信息同步相关方法
const triggerIncrementalSync = async () => {
  console.log('triggerIncrementalSync 被调用了')
  try {
    const result = await ElMessageBox.confirm(
      '确定要执行增量同步吗？这将同步最近24小时内变更的人员信息到腾讯企业邮箱。',
      '确认增量同步',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    if (result === 'confirm') {
      incrementalSyncLoading.value = true
      ElMessage.info('正在执行增量同步，请耐心等待...')

      const response = await triggerPersonnelSync({
        full_sync: false,
        dry_run: false,
        since_time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      })

      const syncResult = response.data
      if (syncResult.success) {
        // 构建包含部门操作的消息
        let message = `增量同步完成：处理 ${syncResult.stats?.processed_count || 0} 项变更，` +
          `创建 ${syncResult.stats?.created_count || 0} 个申请，` +
          `更新 ${syncResult.stats?.updated_count || 0} 个用户，` +
          `禁用 ${syncResult.stats?.disabled_count || 0} 个用户`
        
        // 添加部门操作信息
        if (syncResult.stats?.departments_created > 0 || syncResult.stats?.departments_failed > 0) {
          message += `，部门操作：`
          if (syncResult.stats.departments_created > 0) {
            message += `创建 ${syncResult.stats.departments_created} 个新部门`
          }
          if (syncResult.stats.departments_failed > 0) {
            if (syncResult.stats.departments_created > 0) message += `，`
            message += `${syncResult.stats.departments_failed} 个部门创建失败`
          }
        }
        
        ElMessage.success(message)
      } else {
        ElMessage.error(`增量同步失败: ${syncResult.error_message}`)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('增量同步失败:', error)
      ElMessage.error('增量同步失败')
    }
  } finally {
    incrementalSyncLoading.value = false
  }
}

const triggerIncrementalSyncDryRun = async () => {
  console.log('triggerIncrementalSyncDryRun 被调用了')
  try {
    incrementalSyncLoading.value = true
    ElMessage.info('正在执行增量同步试运行...')

    const response = await triggerPersonnelSync({
      full_sync: false,
      dry_run: true,
      since_time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    })

    const syncResult = response.data
    // 构建包含部门操作的消息
    let message = `增量同步试运行完成：预计处理 ${syncResult.stats?.processed_count || 0} 项变更，` +
      `创建 ${syncResult.stats?.created_count || 0} 个申请，` +
      `更新 ${syncResult.stats?.updated_count || 0} 个用户，` +
      `禁用 ${syncResult.stats?.disabled_count || 0} 个用户`
    
    // 添加部门操作信息
    if (syncResult.stats?.departments_created > 0 || syncResult.stats?.departments_failed > 0) {
      message += `，预计部门操作：`
      if (syncResult.stats.departments_created > 0) {
        message += `创建 ${syncResult.stats.departments_created} 个新部门`
      }
      if (syncResult.stats.departments_failed > 0) {
        if (syncResult.stats.departments_created > 0) message += `，`
        message += `${syncResult.stats.departments_failed} 个部门创建失败`
      }
    }
    
    ElMessage.success(message)
  } catch (error) {
    console.error('增量同步试运行失败:', error)
    ElMessage.error('增量同步试运行失败')
  } finally {
    incrementalSyncLoading.value = false
  }
}

const triggerFullSync = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要执行全量同步吗？这将同步所有有工号的人员信息到腾讯邮箱，可能需要较长时间。',
      '确认全量同步',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    if (result === 'confirm') {
      fullSyncLoading.value = true
      ElMessage.info('正在执行全量同步，可能需要较长时间，请耐心等待...')

      const response = await triggerPersonnelSync({
        full_sync: true,
        dry_run: false
      })

      const syncResult = response.data
      if (syncResult.success) {
        // 构建包含部门操作的消息
        let message = `全量同步完成：处理 ${syncResult.stats?.processed_count || 0} 项变更，` +
          `创建 ${syncResult.stats?.created_count || 0} 个申请，` +
          `更新 ${syncResult.stats?.updated_count || 0} 个用户，` +
          `禁用 ${syncResult.stats?.disabled_count || 0} 个用户，` +
          `耗时 ${syncResult.duration}`
        
        // 添加部门操作信息
        if (syncResult.stats?.departments_created > 0 || syncResult.stats?.departments_failed > 0) {
          message = message.replace('，耗时', '，部门操作：')
          if (syncResult.stats.departments_created > 0) {
            message += `创建 ${syncResult.stats.departments_created} 个新部门`
          }
          if (syncResult.stats.departments_failed > 0) {
            if (syncResult.stats.departments_created > 0) message += `，`
            message += `${syncResult.stats.departments_failed} 个部门创建失败`
          }
          message += `，耗时 ${syncResult.duration}`
        }
        
        ElMessage.success(message)
      } else {
        ElMessage.error(`全量同步失败: ${syncResult.error_message}`)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('全量同步失败:', error)
      ElMessage.error('全量同步失败')
    }
  } finally {
    fullSyncLoading.value = false
  }
}

const triggerFullSyncDryRun = async () => {
  try {
    fullSyncLoading.value = true
    ElMessage.info('正在执行全量同步试运行...')

    const response = await triggerPersonnelSync({
      full_sync: true,
      dry_run: true
    })

    const syncResult = response.data
    // 构建包含部门操作的消息
    let message = `全量同步试运行完成：预计处理 ${syncResult.stats?.processed_count || 0} 项变更，` +
      `创建 ${syncResult.stats?.created_count || 0} 个申请，` +
      `更新 ${syncResult.stats?.updated_count || 0} 个用户，` +
      `禁用 ${syncResult.stats?.disabled_count || 0} 个用户`
    
    // 添加部门操作信息
    if (syncResult.stats?.departments_created > 0 || syncResult.stats?.departments_failed > 0) {
      message += `，预计部门操作：`
      if (syncResult.stats.departments_created > 0) {
        message += `创建 ${syncResult.stats.departments_created} 个新部门`
      }
      if (syncResult.stats.departments_failed > 0) {
        if (syncResult.stats.departments_created > 0) message += `，`
        message += `${syncResult.stats.departments_failed} 个部门创建失败`
      }
    }
    
    ElMessage.success(message)
  } catch (error) {
    console.error('全量同步试运行失败:', error)
    ElMessage.error('全量同步试运行失败')
  } finally {
    fullSyncLoading.value = false
  }
}

const handleDataConsistencyCheck = async () => {
  try {
    consistencyLoading.value = true
    ElMessage.info('正在检查数据一致性...')

    const response = await checkDataConsistency()
    const result = response.data

    if (result.success) {
      const stats = result.stats
      ElMessage.success(
        `数据一致性检查完成：` +
        `人员总数 ${stats.total_personnel}，邮箱总数 ${stats.total_email_members}，` +
        `无邮箱人员 ${stats.personnel_without_email}，无人员邮箱 ${stats.email_without_personnel}，` +
        `数据不一致 ${stats.inconsistent_data}，离职但邮箱活跃 ${stats.departed_with_active_email}`
      )
    } else {
      ElMessage.error(`数据一致性检查失败: ${result.error_message}`)
    }
  } catch (error) {
    console.error('数据一致性检查失败:', error)
    ElMessage.error('数据一致性检查失败')
  } finally {
    consistencyLoading.value = false
  }
}



const loadFilterStats = async () => {
  try {
    filterStatsLoading.value = true
    const response = await getFilterStats()
    const data = response.data
    
    filterEnabled.value = data.current_filter_config.filter_enabled
    filterStats.value = data.preview_result
  } catch (error) {
    console.error('加载过滤统计失败:', error)
    ElMessage.error('加载统计失败')
  } finally {
    filterStatsLoading.value = false
  }
}

const openPersonnelSyncConfigDialog = async () => {
  try {
    // 加载可用的公司、部门和职位列表
    const [companyResponse, deptResponse, jobResponse, configResponse] = await Promise.all([
      getAvailableCompanies(),
      getAvailableDepartments(),
      getAvailableJobTitles(),
      getPersonnelSyncConfig()
    ])
    
    availableCompanies.value = companyResponse.data.companies
    availableDepartments.value = deptResponse.data.departments
    availableJobTitles.value = jobResponse.data.job_titles
    
    const config = configResponse.data
    personnelSyncConfig.value = config
    
    personnelSyncConfigForm.value = {
      enabled: config.enabled,
      sync_time: config.sync_time || '',
      sync_interval: config.sync_interval,
      auto_create_users: config.auto_create_users,
      auto_update_users: config.auto_update_users,
      auto_disable_users: config.auto_disable_users,
      auto_create_departments: config.auto_create_departments,
      filter_enabled: config.filter_enabled,
      included_companies: config.included_companies || [],
      included_departments: config.included_departments || [],
      included_job_titles: config.included_job_titles || [],
      excluded_job_titles: config.excluded_job_titles || [],
      filter_logic: config.filter_logic || 'AND'
    }
    
    personnelSyncConfigDialogVisible.value = true
  } catch (error) {
    console.error('打开人员同步配置失败:', error)
    ElMessage.error('加载配置失败')
  }
}

const savePersonnelSyncConfig = async () => {
  try {
    await updatePersonnelSyncConfig(personnelSyncConfigForm.value)
    personnelSyncConfigDialogVisible.value = false
    
    // 更新本地状态
    const configResponse = await getPersonnelSyncConfig()
    personnelSyncConfig.value = configResponse.data
    filterEnabled.value = configResponse.data.filter_enabled
    
    // 刷新统计
    await loadFilterStats()
    
    ElMessage.success('人员同步配置保存成功')
  } catch (error) {
    console.error('保存人员同步配置失败:', error)
    ElMessage.error('保存配置失败')
  }
}

// 更新过滤预览
const updateFilterPreview = async () => {
  try {
    previewLoading.value = true
    
    // 构建预览请求数据
    const previewRequest: FilterPreviewRequest = {
      filter_enabled: personnelSyncConfigForm.value.filter_enabled || false,
      included_companies: personnelSyncConfigForm.value.included_companies || [],
      included_departments: personnelSyncConfigForm.value.included_departments || [],
      included_job_titles: personnelSyncConfigForm.value.included_job_titles || [],
      excluded_job_titles: personnelSyncConfigForm.value.excluded_job_titles || [],
      filter_logic: (personnelSyncConfigForm.value.filter_logic as 'AND' | 'OR') || 'AND'
    }
    
    const response = await previewFilterResults(previewRequest)
    filterStats.value = response.data
    
    ElMessage.success('预览更新成功')
  } catch (error) {
    console.error('更新预览失败:', error)
    ElMessage.error('更新预览失败')
  } finally {
    previewLoading.value = false
  }
}

// 公司选择变化处理
const onCompanySelectionChange = async () => {
  try {
    const selectedCompanies = personnelSyncConfigForm.value.included_companies || []
    
    if (selectedCompanies.length === 0) {
      // 如果没有选择公司，显示所有部门
      const deptResponse = await getAvailableDepartments()
      availableDepartments.value = deptResponse.data.departments
    } else if (selectedCompanies.length === 1) {
      // 如果只选择了一个公司，显示该公司的部门
      const deptResponse = await getDepartmentsByCompany(selectedCompanies[0])
      availableDepartments.value = deptResponse.data.departments
      
      // 清理不属于当前公司的部门选择
      const currentDepts = personnelSyncConfigForm.value.included_departments || []
      const validDepts = currentDepts.filter((dept: string) => 
        availableDepartments.value.includes(dept)
      )
      personnelSyncConfigForm.value.included_departments = validDepts
      
      if (currentDepts.length > validDepts.length) {
        ElMessage.info(`已清理 ${currentDepts.length - validDepts.length} 个不属于所选公司的部门`)
      }
    } else {
      // 如果选择了多个公司，获取所有选中公司的部门的并集
      const allDepartments = new Set<string>()
      
      for (const company of selectedCompanies) {
        try {
          const deptResponse = await getDepartmentsByCompany(company)
          deptResponse.data.departments.forEach((dept: string) => allDepartments.add(dept))
        } catch (error) {
          console.warn(`获取公司 ${company} 的部门失败:`, error)
        }
      }
      
      availableDepartments.value = Array.from(allDepartments).sort()
      
             // 清理不属于所选公司的部门选择
       const currentDepts = personnelSyncConfigForm.value.included_departments || []
       const validDepts = currentDepts.filter((dept: string) => 
         availableDepartments.value.includes(dept)
       )
      personnelSyncConfigForm.value.included_departments = validDepts
      
      if (currentDepts.length > validDepts.length) {
        ElMessage.info(`已清理 ${currentDepts.length - validDepts.length} 个不属于所选公司的部门`)
      }
    }
  } catch (error) {
    console.error('更新部门列表失败:', error)
    ElMessage.error('更新部门列表失败')
  }
}



const refreshSyncStatus = async () => {
  console.log('refreshSyncStatus 被调用了')
  try {
    syncStatusLoading.value = true
    const response = await getPersonnelSyncStatus()
    personnelSyncStatus.value = response.data
    ElMessage.success('同步状态已刷新')
  } catch (error) {
    console.error('刷新同步状态失败:', error)
    ElMessage.error('刷新同步状态失败')
  } finally {
    syncStatusLoading.value = false
  }
}

// 部门结构同步相关状态
const departmentStructureSyncDialogVisible = ref(false)
const departmentSyncRequest = ref<DepartmentSyncRequest>({
  source: 'all',
  mode: 'create_only',
  dry_run: false,
  overwrite_existing: false,
  create_hierarchy: true,
  skip_empty_departments: true
})
const departmentSyncResult = ref<DepartmentSyncResult | null>(null)
const departmentSyncLoading = ref(false)

// 部门选择相关状态
const departments = ref<Array<{id: number, name: string, company_id: number}>>([])
const departmentsLoading = ref(false)

// 公司选择相关状态
const companies = ref<Array<{id: number, name: string}>>([])
const companiesLoading = ref(false)

// 获取公司列表
const fetchCompanies = async () => {
  try {
    companiesLoading.value = true
    const response = await ecologyApi.getCompanies()
    companies.value = response.data
  } catch (error) {
    console.error('获取公司列表失败:', error)
    ElMessage.error('获取公司列表失败')
  } finally {
    companiesLoading.value = false
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    departmentsLoading.value = true
    const response = await ecologyApi.getDepartments()
    departments.value = response.data
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  } finally {
    departmentsLoading.value = false
  }
}

// 显示部门结构同步对话框
const showDepartmentStructureSyncDialog = () => {
  departmentStructureSyncDialogVisible.value = true
  departmentSyncResult.value = null
  // 加载公司和部门列表
  fetchCompanies()
  fetchDepartments()
}

// 执行部门结构同步
const executeDepartmentStructureSync = async () => {
  console.log('开始执行部门结构同步')
  try {
    departmentSyncLoading.value = true
    console.log('设置加载状态为true')

    const result = await syncDepartmentsFromPersonnel(departmentSyncRequest.value)
    console.log('同步API调用成功，结果:', result)

    departmentSyncResult.value = result

    if (result && result.success) {
      ElMessage.success(result.message)
      // 刷新同步日志
      await refreshLogs()
      await loadLatestSyncTimes()
    } else {
      ElMessage.error(result?.message || '同步失败')
    }

  } catch (error: any) {
    console.error('同步失败:', error)
    ElMessage.error('部门结构同步失败: ' + (error.message || '未知错误'))
  } finally {
    console.log('设置加载状态为false')
    departmentSyncLoading.value = false
    console.log('当前加载状态:', departmentSyncLoading.value)

    // 确保UI更新
    await nextTick()
    console.log('nextTick后的加载状态:', departmentSyncLoading.value)
  }
}

// 预览部门结构同步
const previewDepartmentStructureSync = async () => {
  console.log('开始预览部门结构同步')
  try {
    departmentSyncLoading.value = true
    console.log('设置加载状态为true')

    const previewRequest = { ...departmentSyncRequest.value, dry_run: true }
    console.log('预览请求参数:', previewRequest)

    const result = await previewDepartmentsSyncFromPersonnel(previewRequest)
    console.log('预览API调用成功，结果:', result)

    departmentSyncResult.value = result

    if (result && result.stats) {
      ElMessage.info(`预览完成：将创建 ${result.stats.created_departments} 个部门`)
    } else {
      console.warn('预览结果格式异常:', result)
      ElMessage.warning('预览完成，但结果格式异常')
    }

  } catch (error: any) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败: ' + (error.message || '未知错误'))
  } finally {
    console.log('设置加载状态为false')
    departmentSyncLoading.value = false
    console.log('当前加载状态:', departmentSyncLoading.value)

    // 确保UI更新
    await nextTick()
    console.log('nextTick后的加载状态:', departmentSyncLoading.value)
  }
}

// 获取操作类型对应的标签类型
const getOperationTagType = (operation: string) => {
  switch (operation) {
    case 'create':
      return 'success'
    case 'update':
      return 'warning'
    case 'skip':
      return 'info'
    case 'error':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取操作类型的中文文本
const getOperationText = (operation: string) => {
  switch (operation) {
    case 'create':
      return '创建'
    case 'update':
      return '更新'
    case 'skip':
      return '跳过'
    case 'error':
      return '错误'
    default:
      return operation
  }
}

// 初始化
onMounted(() => {
  refreshLogs()
  loadLatestSyncTimes()
  loadStats()
  loadFilterStats()
})
</script>

<style scoped>
:deep(.personnel-select-dropdown) {
  min-width: 480px !important;
  max-width: 600px !important;
}

:deep(.personnel-select-dropdown .el-scrollbar__view) {
  padding: 6px 0 !important;
}

:deep(.personnel-select-dropdown .el-select-dropdown__item) {
  height: auto !important;
  min-height: 48px !important;
  padding: 12px 20px !important;
  white-space: nowrap !important;
  line-height: 1.5 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  box-sizing: border-box !important;
}

.unified-sync-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 24px;
  color: #409eff;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  margin-top: 20px;
}

.tabs-container {
  min-height: 500px;
}

.sync-cards {
  margin-bottom: 20px;
}

.sync-card {
  min-height: 210px;
  transition: all 0.3s ease;
}

.sync-card :deep(.el-card__body) {
  height: auto;
  overflow: visible;
}

.sync-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.sync-card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.sync-icon {
  font-size: 24px;
  color: #409eff;
}

.sync-card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.sync-card-content {
  min-height: 130px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: visible;
}

.sync-description {
  color: #606266;
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.sync-status {
  margin-bottom: 15px;
}

.status-label {
  color: #909399;
  font-size: 12px;
}

.status-time {
  color: #606266;
  font-size: 12px;
}

.full-sync-card .sync-icon {
  color: #f56c6c;
}

.config-card .sync-icon {
  color: #e6a23c;
}

.filter-config-card .sync-icon {
  color: #67c23a;
}

.log-card {
  border: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

/* 同步配置对话框样式 */
.form-tip {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
  line-height: 1.5;
}

.config-info .info-row {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.config-info .info-label {
  color: #909399;
  font-size: 12px;
  margin-right: 8px;
}

.filter-summary {
  color: #606266;
  font-size: 12px;
  margin-left: 8px;
}

.filter-effect-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-stats-display {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e4e7ed;
}

.filter-stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.filter-stats-grid .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.filter-stats-grid .stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.filter-stats-grid .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.update-preview-btn {
  align-self: flex-start;
  min-width: 100px;
}

.dialog-footer {
  text-align: right;
}



/* 统计卡片样式 */
.stats-card {
  border-radius: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  gap: 16px;
}

.stat-item:hover {
  background: #f0f9ff;
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: #5b9bd5;
}

.stat-icon.completed {
  background: #70ad47;
}

.stat-icon.missing {
  background: #e74c3c;
}

.stat-icon.rate {
  background: #9b59b6;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 操作面板样式 */
.action-card {
  border-radius: 8px;
}

.action-panel, .sync-panel {
  padding: 16px;
}

.action-section, .sync-section {
  padding: 20px;
  border-radius: 8px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.action-section h4, .sync-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-desc {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  flex: 1;
}

.action-buttons, .sync-buttons {
  display: flex;
  gap: 8px;
  flex-direction: column;
  margin-top: auto;
  min-height: 80px;
  justify-content: flex-end;
}

/* 候选者列表样式 */
.candidates-card {
  border-radius: 8px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.match-list {
  max-height: 300px;
  overflow-y: auto;
}

.match-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.match-item:last-child {
  border-bottom: none;
}

.match-info {
  flex: 1;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

/* 表格操作按钮样式 */
.candidates-card .action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

.filter-summary {
  margin-bottom: 12px;
}

.filter-stat {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.filter-stat:last-child {
  margin-bottom: 0;
}



.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  margin-right: 8px;
  font-weight: 500;
}

.info-data {
  color: #303133;
  font-size: 13px;
}

/* 过滤配置对话框样式 */
.filter-config-dialog .el-dialog__body {
  padding: 20px 30px;
  max-height: 70vh;
  overflow-y: auto;
}

.filter-config-form .el-form-item {
  margin-bottom: 24px;
}

.filter-config-form .el-select,
.filter-config-form .el-input {
  width: 100%;
}

.filter-logic-help {
  margin-top: 8px;
}

.filter-config-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 30px;
  border-top: 1px solid #ebeef5;
  margin: 0 -30px -20px -30px;
  background: #fafafa;
}

.filter-config-dialog .el-descriptions {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.filter-config-dialog .el-descriptions__label {
  font-weight: 600;
}

/* 日志详情对话框样式 */
.log-details-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.detail-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  white-space: nowrap;
}

.detail-value {
  color: #303133;
}

.detail-message {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  margin-top: 8px;
  line-height: 1.5;
}

.detail-error {
  background: #fef0f0;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #f56c6c;
  margin-top: 8px;
  color: #f56c6c;
  line-height: 1.5;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-number.success {
  color: #67c23a;
}

.stat-number.info {
  color: #409eff;
}

.stat-number.warning {
  color: #e6a23c;
}

.stat-number.danger {
  color: #f56c6c;
}

.stat-number.disabled {
  color: #909399;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  line-height: 1;
}

.details-section {
  margin-bottom: 24px;
}

.details-section:last-child {
  margin-bottom: 0;
}

.details-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  font-weight: 500;
  margin-right: 8px;
}

.info-value {
  color: #303133;
  font-weight: 400;
}

.info-value.success {
  color: #67c23a;
  font-weight: 600;
}

.info-value.info {
  color: #409eff;
  font-weight: 600;
}

.info-value.warning {
  color: #e6a23c;
  font-weight: 600;
}

.info-value.danger {
  color: #f56c6c;
  font-weight: 600;
}

.info-value.disabled {
  color: #c0c4cc;
  font-weight: 600;
}

.json-details {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.json-details pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 部门结构同步对话框样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sync-result {
  margin-top: 20px;
}

.operations-detail {
  margin-top: 16px;
}

.result-message {
  margin-top: 16px;
}

.result-message h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.result-message p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.error-messages,
.warning-messages {
  margin-top: 16px;
}

.error-messages h4,
.warning-messages h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}
</style> 