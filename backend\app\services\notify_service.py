import logging
import grpc
from app.grpc.terminal_pb.terminal_pb2 import CommandNotification
from app.grpc.terminal_pb.terminal_pb2_grpc import TerminalManagementStub
from app.config import settings

logger = logging.getLogger(__name__)

class NotifyService:
    """
    终端通知服务，用于在创建命令后立即通知终端
    """
    
    @staticmethod
    def notify_terminal(terminal_id: str, command_id: str = None):
        """
        向终端发送命令通知
        
        Args:
            terminal_id: 终端ID
            command_id: 可选，命令ID。如果不提供，表示通知终端检查所有命令
        
        Returns:
            (bool, str): 通知是否成功及消息
        """
        try:
            # 创建gRPC通道
            channel = grpc.insecure_channel(settings.GRPC_SERVER_ADDRESS)
            stub = TerminalManagementStub(channel)
            
            # 创建通知请求
            request = CommandNotification(terminal_id=terminal_id)
            if command_id:
                request.command_id = command_id
            
            # 发送通知
            try:
                response = stub.NotifyCommand(request, timeout=5)  # 5秒超时
                
                if response.received:
                    logger.info(f"成功通知终端 {terminal_id}")
                    return True, "通知发送成功"
                else:
                    logger.warning(f"通知终端 {terminal_id} 失败: {response.message}")
                    return False, response.message
                    
            except grpc.RpcError as rpc_err:
                logger.error(f"发送终端通知RPC调用失败: {rpc_err.code()}: {rpc_err.details()}")
                return False, f"RPC错误: {rpc_err.details()}"
                
        except Exception as e:
            logger.error(f"发送终端通知时发生错误: {str(e)}")
            return False, f"发送通知出错: {str(e)}"
        finally:
            # 关闭通道
            if 'channel' in locals():
                channel.close() 