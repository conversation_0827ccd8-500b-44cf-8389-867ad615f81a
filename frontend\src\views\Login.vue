<template>
  <div class="login-container">
    <!-- 动态背景 -->
    <div class="area">
      <ul class="circles">
        <li v-for="n in 10" :key="n"></li>
      </ul>
    </div>
    
    <div class="login-box">
      <div class="login-left">
        <div class="welcome-text">
          <h1>运维管理平台</h1>
          <p>欢迎使用运维管理系统，高效管理您的IT资产</p>
        </div>
      </div>
      <div class="login-right">
        <el-card class="login-card" shadow="never">
          <h2 class="title">账号登录</h2>
          
          <!-- 认证方式选择 -->
          <div class="auth-type-selector">
            <el-tabs v-model="authType" @tab-change="handleAuthTypeChange">
              <el-tab-pane label="本地登录" name="local"></el-tab-pane>
              <el-tab-pane label="LDAP登录" name="ldap"></el-tab-pane>
            </el-tabs>
          </div>

          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="rules"
            label-width="0px"
            class="login-form"
          >
            <!-- LDAP配置选择（仅LDAP登录时显示） -->
            <el-form-item v-if="authType === 'ldap'" prop="ldapConfig">
              <el-select
                v-model="loginForm.ldapConfig"
                placeholder="选择LDAP配置"
                size="large"
                style="width: 100%"
                :loading="ldapConfigsLoading"
              >
                <el-option
                  v-for="config in ldapConfigs"
                  :key="config.id"
                  :label="config.name"
                  :value="config.id"
                >
                  <div class="ldap-option">
                    <div class="option-main">
                      <span class="config-name">{{ config.name }}</span>
                      <el-tag
                        v-if="config.is_auto_selected"
                        type="success"
                        size="small"
                        effect="plain"
                        class="auto-tag"
                      >
                        智能推荐
                      </el-tag>
                      <el-tag
                        v-else-if="config.is_default"
                        type="primary"
                        size="small"
                        effect="plain"
                        class="default-tag"
                      >
                        默认
                      </el-tag>
                    </div>
                    <div v-if="config.is_auto_selected && config.match_reason" class="match-reason">
                      {{ config.match_reason }}
                    </div>
                    <div class="server-info">{{ config.server }}</div>
                  </div>
                </el-option>
              </el-select>
              
              <!-- 智能选择提示 -->
              <div v-if="autoSelectedConfig" class="auto-select-hint">
                <el-icon class="hint-icon"><InfoFilled /></el-icon>
                <span>已为您智能选择"{{ autoSelectedConfig.name }}"配置</span>
                <el-button type="primary" link size="small" @click="showConfigDetails = !showConfigDetails">
                  {{ showConfigDetails ? '隐藏' : '查看' }}详情
                </el-button>
              </div>
              
              <!-- 配置详情展示 -->
              <el-collapse-transition>
                <div v-if="showConfigDetails && configsInfo" class="config-details">
                  <div class="detail-item">
                    <span class="label">当前IP:</span>
                    <el-tag size="small">{{ configsInfo.client_ip }}</el-tag>
                  </div>
                  <div v-if="autoSelectedConfig" class="detail-item">
                    <span class="label">推荐原因:</span>
                    <span>{{ autoSelectedConfig.match_reason || '默认配置' }}</span>
                  </div>
                </div>
              </el-collapse-transition>
            </el-form-item>

            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                :placeholder="authType === 'ldap' ? '请输入LDAP用户名' : '请输入用户名'"
                :prefix-icon="User"
                size="large"
              />
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                :type="passwordVisible ? 'text' : 'password'"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                size="large"
                @keyup.enter="handleLogin"
              >
                <template #suffix>
                  <el-icon 
                    class="password-icon" 
                    @click="passwordVisible = !passwordVisible"
                  >
                    <View v-if="passwordVisible" />
                    <Hide v-else />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-if="authType === 'local'">
              <div class="login-options">
                <el-checkbox v-model="loginForm.remember">记住登录</el-checkbox>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                :loading="loading"
                type="primary"
                class="login-button"
                size="large"
                @click="handleLogin"
              >
                {{ authType === 'ldap' ? 'LDAP登录' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
import { User, Lock, View, Hide, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import request from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()
const passwordVisible = ref(false)
const authType = ref('local')

interface LdapConfig {
  id: number;
  name: string;
  server: string;
  is_default: boolean;
  priority?: number;
  auto_select_enabled?: boolean;
  is_auto_selected?: boolean;
  match_reason?: string;
  description?: string;
}

interface ConfigsInfo {
  configs: LdapConfig[];
  client_ip: string;
  auto_selected_config_id?: number;
}

const ldapConfigs = ref<LdapConfig[]>([])
const ldapConfigsLoading = ref(false)
const configsInfo = ref<ConfigsInfo | null>(null)
const showConfigDetails = ref(false)

interface LoginForm {
  username: string;
  password: string;
  remember: boolean;
  ldapConfig?: number;
}

const loginForm = ref<LoginForm>({
  username: '',
  password: '',
  remember: false,
  ldapConfig: undefined
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  ldapConfig: [{ required: true, message: '请选择LDAP配置', trigger: 'change' }]
}

const loading = ref(false)
const loginFormRef = ref<FormInstance>()

// 计算属性：获取自动选择的配置
const autoSelectedConfig = computed(() => {
  return ldapConfigs.value.find(config => config.is_auto_selected)
})

// 获取可用的LDAP配置
const loadLdapConfigs = async () => {
  try {
    ldapConfigsLoading.value = true
    const response = await request.get('/auth/ldap-configs')
    configsInfo.value = response.data
    ldapConfigs.value = response.data.configs || []
    
    // 只有在LDAP模式下才设置选中的配置
    if (authType.value === 'ldap') {
      // 如果有自动选择的配置，优先使用
      const autoSelected = ldapConfigs.value.find((config: LdapConfig) => config.is_auto_selected)
      if (autoSelected) {
        loginForm.value.ldapConfig = autoSelected.id
        console.log('自动选择LDAP配置:', autoSelected.name, autoSelected.match_reason)
      } else {
        // 如果没有自动选择，使用默认配置
        const defaultConfig = ldapConfigs.value.find((config: LdapConfig) => config.is_default)
        if (defaultConfig) {
          loginForm.value.ldapConfig = defaultConfig.id
        }
      }
    }
  } catch (error: any) {
    console.warn('获取LDAP配置失败:', error)
    ElMessage.warning('无法获取LDAP配置，请联系管理员')
  } finally {
    ldapConfigsLoading.value = false
  }
}

// 认证类型切换
const handleAuthTypeChange = (type: string) => {
  authType.value = type
  // 清空表单数据
  loginForm.value = {
    username: '',
    password: '',
    remember: false,
    ldapConfig: undefined
  }
  
  // 如果切换到LDAP登录，重新设置智能选择的配置
  if (type === 'ldap') {
    if (ldapConfigs.value.length === 0) {
      loadLdapConfigs()
    } else {
      // 配置已加载，重新设置智能选择的配置
      const autoSelected = ldapConfigs.value.find((config: LdapConfig) => config.is_auto_selected)
      if (autoSelected) {
        loginForm.value.ldapConfig = autoSelected.id
        console.log('重新选择LDAP配置:', autoSelected.name)
      } else {
        // 如果没有自动选择，使用默认配置
        const defaultConfig = ldapConfigs.value.find((config: LdapConfig) => config.is_default)
        if (defaultConfig) {
          loginForm.value.ldapConfig = defaultConfig.id
        }
      }
    }
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        
        if (authType.value === 'local') {
          // 本地登录
          const formData = new FormData()
          formData.append('username', loginForm.value.username)
          formData.append('password', loginForm.value.password)
          formData.append('remember', loginForm.value.remember.toString())
          formData.append('auth_type', 'local')
          
          await userStore.login(formData)
        } else {
          // LDAP登录
          const response = await request.post('/auth/ldap-login', {
            username: loginForm.value.username,
            password: loginForm.value.password,
            config_id: loginForm.value.ldapConfig
          })
          
          const { access_token, user } = response.data
          userStore.setToken(access_token)
          userStore.userInfo = user
          userStore.initialized = true
          
          // 处理权限
          if (user.roles) {
            userStore.extractPermissions(user)
          }
        }
        
        ElMessage.success('登录成功')
        router.push('/dashboard')
      } catch (error: any) {
        const errorMsg = error.response?.data?.detail || 
                        (authType.value === 'ldap' ? 'LDAP登录失败' : '登录失败')
        ElMessage.error(errorMsg)
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  // 页面加载时加载LDAP配置（为了确保配置可用）
  loadLdapConfigs()
})
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #4e54c8;
  overflow: hidden;
  position: relative;
}

.area {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(-45deg, #8f94fb, #4e54c8, #4353c1, #7579e7);
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.circles li {
  position: absolute;
  display: block;
  list-style: none;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  animation: animate 15s linear infinite;
  bottom: -150px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.circles li:nth-child(1) {
  left: 25%;
  width: 80px;
  height: 80px;
  animation-delay: 0s;
  animation-duration: 20s;
}

.circles li:nth-child(2) {
  left: 10%;
  width: 20px;
  height: 20px;
  animation-delay: 1s;
  animation-duration: 12s;
}

.circles li:nth-child(3) {
  left: 70%;
  width: 20px;
  height: 20px;
  animation-delay: 2s;
  animation-duration: 15s;
}

.circles li:nth-child(4) {
  left: 40%;
  width: 60px;
  height: 60px;
  animation-delay: 0s;
  animation-duration: 18s;
}

.circles li:nth-child(5) {
  left: 65%;
  width: 20px;
  height: 20px;
  animation-delay: 1s;
  animation-duration: 13s;
}

.circles li:nth-child(6) {
  left: 75%;
  width: 90px;
  height: 90px;
  animation-delay: 2s;
  animation-duration: 22s;
}

.circles li:nth-child(7) {
  left: 35%;
  width: 120px;
  height: 120px;
  animation-delay: 3s;
  animation-duration: 25s;
}

.circles li:nth-child(8) {
  left: 50%;
  width: 25px;
  height: 25px;
  animation-delay: 4s;
  animation-duration: 16s;
}

.circles li:nth-child(9) {
  left: 20%;
  width: 15px;
  height: 15px;
  animation-delay: 1s;
  animation-duration: 14s;
}

.circles li:nth-child(10) {
  left: 85%;
  width: 130px;
  height: 130px;
  animation-delay: 2s;
  animation-duration: 23s;
}

@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.8;
    border-radius: 50%;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(-1200px) rotate(360deg);
    opacity: 0;
    border-radius: 50%;
  }
}

.login-box {
  display: flex;
  width: 1000px;
  height: 600px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%);
  border-radius: 20px 0 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: white;
  position: relative;
  overflow: hidden;
}

.login-left::before {
  content: '';
  position: absolute;
  width: 150%;
  height: 150%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  animation: shine 8s linear infinite;
}

@keyframes shine {
  0% {
    transform: translate(-100%, -100%) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translate(100%, 100%) rotate(360deg);
    opacity: 0.5;
  }
}

.welcome-text {
  text-align: center;
  position: relative;
  z-index: 1;
}

.welcome-text h1 {
  font-size: 2.5em;
  margin-bottom: 20px;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.welcome-text p {
  font-size: 1.1em;
  opacity: 0.9;
  line-height: 1.6;
}

.login-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border: none;
  background: transparent;
}

.title {
  text-align: center;
  margin-bottom: 40px;
  font-size: 24px;
  color: #2c3e50;
  font-weight: 600;
}

.login-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e4e7ed;
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #4e54c8;
}

.login-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #4e54c8 !important;
}

.password-icon {
  cursor: pointer;
  color: #909399;
}

.password-icon:hover {
  color: #4e54c8;
}

.login-button {
  width: 100%;
  height: 45px;
  border-radius: 4px;
  margin-top: 20px;
  font-size: 16px;
  background: linear-gradient(135deg, #4e54c8 0%, #8f94fb 100%);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(78, 84, 200, 0.3);
}

@media screen and (max-width: 1024px) {
  .login-box {
    width: 90%;
    height: auto;
    flex-direction: column;
  }
  
  .login-left {
    border-radius: 20px 20px 0 0;
    padding: 30px;
  }
  
  .login-right {
    padding: 30px;
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.auth-type-selector {
  margin-bottom: 20px;
}

.auth-type-selector :deep(.el-tabs__nav-wrap::after) {
  display: none;
}

.auth-type-selector :deep(.el-tabs__item) {
  color: #909399;
  font-weight: 500;
}

.auth-type-selector :deep(.el-tabs__item.is-active) {
  color: #4e54c8;
}

.auth-type-selector :deep(.el-tabs__active-bar) {
  background-color: #4e54c8;
}

/* LDAP配置选择相关样式 */
.ldap-option {
  padding: 8px 0;
  line-height: 1.4;
  width: 100%;
}

.option-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.config-name {
  font-weight: 500;
  flex: 1;
  min-width: 0;
  word-break: break-all;
}

.auto-tag {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
  flex-shrink: 0;
}

.default-tag {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
  flex-shrink: 0;
}

.match-reason {
  font-size: 12px;
  color: #67c23a;
  margin-bottom: 4px;
  line-height: 1.3;
  word-break: break-word;
}

.server-info {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
  word-break: break-all;
}

.auto-select-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 14px;
  color: #409eff;
  width: 100%;
  box-sizing: border-box;
}

.hint-icon {
  color: #409eff;
}

.config-details {
  margin-top: 8px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  width: 100%;
  box-sizing: border-box;
  font-size: 13px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
  font-size: 13px;
}

/* 下拉选项容器样式优化 */
:deep(.el-select-dropdown__item) {
  height: auto !important;
  min-height: 44px;
  padding: 8px 12px !important;
  line-height: 1.4;
  white-space: normal;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item .ldap-option) {
  line-height: 1.4;
  width: 100%;
}
</style>
