"""
注册表操作相关的Schema定义
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class RegistryRootKey(str, Enum):
    """注册表根键枚举"""
    HKEY_CLASSES_ROOT = "HKEY_CLASSES_ROOT"
    HKEY_CURRENT_USER = "HKEY_CURRENT_USER"
    HKEY_LOCAL_MACHINE = "HKEY_LOCAL_MACHINE"
    HKEY_USERS = "HKEY_USERS"
    HKEY_CURRENT_CONFIG = "HKEY_CURRENT_CONFIG"


class RegistryValueType(str, Enum):
    """注册表值类型枚举"""
    REG_SZ = "REG_SZ"
    REG_EXPAND_SZ = "REG_EXPAND_SZ"
    REG_BINARY = "REG_BINARY"
    REG_DWORD = "REG_DWORD"
    REG_QWORD = "REG_QWORD"
    REG_MULTI_SZ = "REG_MULTI_SZ"


class RegistryOperationType(str, Enum):
    """注册表操作类型枚举"""
    READ = "REGISTRY_READ"
    WRITE = "REGISTRY_WRITE"
    DELETE = "REGISTRY_DELETE"
    CREATE_KEY = "REGISTRY_CREATE_KEY"
    DELETE_KEY = "REGISTRY_DELETE_KEY"
    ENUMERATE = "REGISTRY_ENUMERATE"
    SEARCH = "REGISTRY_SEARCH"
    BACKUP = "REGISTRY_BACKUP"
    RESTORE = "REGISTRY_RESTORE"


# === 基础数据模型 ===

class RegistryValue(BaseModel):
    """注册表值模型"""
    name: str = Field(..., description="值名称")
    type: RegistryValueType = Field(..., description="值类型")
    data: str = Field(..., description="值数据")
    size: Optional[int] = Field(None, description="数据大小(字节)")


class RegistryKey(BaseModel):
    """注册表键模型"""
    name: str = Field(..., description="键名称")
    full_path: str = Field(..., description="完整路径")
    sub_keys: List[str] = Field(default_factory=list, description="子键列表")
    values: List[RegistryValue] = Field(default_factory=list, description="值列表")
    last_modified: Optional[int] = Field(None, description="最后修改时间")
    sub_key_count: int = Field(0, description="子键数量")
    value_count: int = Field(0, description="值数量")
    
    # 分页信息
    current_page: Optional[int] = Field(None, description="当前页码")
    page_size: Optional[int] = Field(None, description="每页大小")
    total_sub_keys: Optional[int] = Field(None, description="总子键数量")
    has_more_sub_keys: Optional[bool] = Field(None, description="是否还有更多子键")


# === 操作请求模型 ===

class RegistryOperationRequest(BaseModel):
    """注册表操作请求"""
    terminal_id: int = Field(..., description="终端ID")
    operation: RegistryOperationType = Field(..., description="操作类型")
    root_key: RegistryRootKey = Field(..., description="根键")
    key_path: str = Field("", description="键路径")
    value_name: Optional[str] = Field(None, description="值名称")
    value_type: Optional[RegistryValueType] = Field(None, description="值类型")
    value_data: Optional[str] = Field(None, description="值数据")
    create_backup: bool = Field(False, description="是否创建备份")
    backup_reason: Optional[str] = Field(None, description="备份原因")
    
    # 分页参数（用于ENUMERATE操作）
    page: Optional[int] = Field(1, description="页码（从1开始）")
    page_size: Optional[int] = Field(1000, description="每页大小")
    search_filter: Optional[str] = Field("", description="搜索过滤器")


class RegistryBatchOperationRequest(BaseModel):
    """批量注册表操作请求"""
    terminal_id: int = Field(..., description="终端ID")
    operations: List[RegistryOperationRequest] = Field(..., description="操作列表")
    create_backup: bool = Field(True, description="是否为每个操作创建备份")
    batch_reason: Optional[str] = Field(None, description="批量操作原因")


# === 搜索相关模型 ===

class RegistrySearchRequest(BaseModel):
    """注册表搜索请求"""
    terminal_id: int = Field(..., description="终端ID")
    root_key: RegistryRootKey = Field(..., description="搜索根键")
    start_path: str = Field("", description="开始路径")
    search_pattern: str = Field(..., description="搜索模式")
    search_keys: bool = Field(True, description="是否搜索键名")
    search_values: bool = Field(True, description="是否搜索值名")
    search_data: bool = Field(True, description="是否搜索值数据")
    max_depth: int = Field(10, description="最大搜索深度")
    max_results: int = Field(100, description="最大结果数")


class RegistrySearchResult(BaseModel):
    """注册表搜索结果"""
    path: str = Field(..., description="路径")
    match_type: str = Field(..., description="匹配类型(key/value_name/value_data)")
    match_text: str = Field(..., description="匹配文本")
    value: Optional[RegistryValue] = Field(None, description="值信息(如果匹配的是值)")


# === 备份相关模型 ===

class RegistryBackupRequest(BaseModel):
    """注册表备份请求"""
    terminal_id: int = Field(..., description="终端ID")
    root_key: RegistryRootKey = Field(..., description="根键")
    key_path: str = Field("", description="键路径")
    backup_name: Optional[str] = Field(None, description="备份名称")
    reason: Optional[str] = Field(None, description="备份原因")
    description: Optional[str] = Field(None, description="备份描述")
    tags: Optional[List[str]] = Field(None, description="标签列表")


class RegistryRestoreRequest(BaseModel):
    """注册表还原请求"""
    terminal_id: int = Field(..., description="终端ID")
    backup_id: str = Field(..., description="备份ID")
    target_root_key: Optional[RegistryRootKey] = Field(None, description="目标根键(可选)")
    target_key_path: Optional[str] = Field(None, description="目标键路径(可选)")
    create_backup_before_restore: bool = Field(True, description="还原前是否创建备份")
    restore_reason: Optional[str] = Field(None, description="还原原因")


# === 响应模型 ===

class RegistryOperationResponse(BaseModel):
    """注册表操作响应"""
    success: bool = Field(..., description="操作是否成功")
    operation_id: Optional[int] = Field(None, description="操作记录ID")
    command_id: Optional[str] = Field(None, description="命令ID")
    error: Optional[str] = Field(None, description="错误信息")
    key_data: Optional[RegistryKey] = Field(None, description="键数据(查询操作)")
    value_data: Optional[RegistryValue] = Field(None, description="值数据(查询操作)")
    backup_id: Optional[str] = Field(None, description="备份ID")
    execution_duration: Optional[int] = Field(None, description="执行耗时(毫秒)")


class RegistryBatchOperationResponse(BaseModel):
    """批量注册表操作响应"""
    success: bool = Field(..., description="批量操作是否成功")
    total_operations: int = Field(..., description="总操作数")
    successful_operations: int = Field(..., description="成功操作数")
    failed_operations: int = Field(..., description="失败操作数")
    operation_results: List[RegistryOperationResponse] = Field(..., description="操作结果列表")
    batch_backup_id: Optional[str] = Field(None, description="批量备份ID")


class RegistrySearchResponse(BaseModel):
    """注册表搜索响应"""
    success: bool = Field(..., description="搜索是否成功")
    search_id: Optional[str] = Field(None, description="搜索ID")
    total_results: int = Field(0, description="搜索结果总数")
    results: List[RegistrySearchResult] = Field(default_factory=list, description="搜索结果")
    error: Optional[str] = Field(None, description="错误信息")
    execution_duration: Optional[int] = Field(None, description="执行耗时(毫秒)")


class RegistryBackupResponse(BaseModel):
    """注册表备份响应"""
    success: bool = Field(..., description="备份是否成功")
    backup_id: Optional[str] = Field(None, description="备份ID")
    backup_name: str = Field(..., description="备份名称")
    file_path: Optional[str] = Field(None, description="备份文件路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    file_hash: Optional[str] = Field(None, description="文件哈希值")
    error: Optional[str] = Field(None, description="错误信息")
    created_at: Optional[datetime] = Field(None, description="创建时间")


# === 历史记录和统计模型 ===

class RegistryOperationHistory(BaseModel):
    """注册表操作历史"""
    id: int = Field(..., description="记录ID")
    command_id: str = Field(..., description="命令ID")
    terminal_id: str = Field(..., description="终端ID")
    operation_type: str = Field(..., description="操作类型")
    root_key: str = Field(..., description="根键")
    key_path: str = Field(..., description="键路径")
    value_name: Optional[str] = Field(None, description="值名称")
    success: bool = Field(..., description="操作是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    backup_id: Optional[str] = Field(None, description="备份ID")
    execution_duration: Optional[int] = Field(None, description="执行耗时(毫秒)")
    created_at: datetime = Field(..., description="创建时间")


class RegistryBackupHistory(BaseModel):
    """注册表备份历史"""
    id: int = Field(..., description="记录ID")
    backup_id: str = Field(..., description="备份ID")
    backup_name: str = Field(..., description="备份名称")
    terminal_id: str = Field(..., description="终端ID")
    root_key: str = Field(..., description="根键")
    key_path: str = Field(..., description="键路径")
    file_size: Optional[int] = Field(None, description="文件大小")
    status: str = Field(..., description="备份状态")
    reason: Optional[str] = Field(None, description="备份原因")
    tags: Optional[str] = Field(None, description="标签")
    created_at: datetime = Field(..., description="创建时间")
    expire_at: Optional[datetime] = Field(None, description="过期时间")


class RegistryStatistics(BaseModel):
    """注册表操作统计"""
    total_operations: int = Field(0, description="总操作数")
    successful_operations: int = Field(0, description="成功操作数")
    failed_operations: int = Field(0, description="失败操作数")
    total_backups: int = Field(0, description="总备份数")
    active_backups: int = Field(0, description="活跃备份数")
    total_searches: int = Field(0, description="总搜索数")
    average_execution_time: Optional[float] = Field(None, description="平均执行时间(毫秒)")
    most_accessed_keys: List[Dict[str, Any]] = Field(default_factory=list, description="最常访问的键")


# === 查询参数模型 ===

class RegistryOperationQuery(BaseModel):
    """注册表操作查询参数"""
    terminal_id: Optional[int] = Field(None, description="终端ID")
    operation_type: Optional[str] = Field(None, description="操作类型")
    root_key: Optional[str] = Field(None, description="根键")
    success: Optional[bool] = Field(None, description="操作是否成功")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    search_term: Optional[str] = Field(None, description="搜索关键词")
    skip: int = Field(0, description="跳过记录数")
    limit: int = Field(100, description="限制记录数")


class RegistryBackupQuery(BaseModel):
    """注册表备份查询参数"""
    terminal_id: Optional[int] = Field(None, description="终端ID")
    root_key: Optional[str] = Field(None, description="根键")
    status: str = Field("active", description="备份状态")
    search_term: Optional[str] = Field(None, description="搜索关键词")
    skip: int = Field(0, description="跳过记录数")
    limit: int = Field(100, description="限制记录数") 