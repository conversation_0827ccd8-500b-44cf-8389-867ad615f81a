"""add_sync_time_to_ad_sync_config

Revision ID: b87c494d8394
Revises: e37c828bbd78
Create Date: 2023-03-18 13:25:02.123456

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b87c494d8394'
down_revision = 'e37c828bbd78'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('ad_sync_config', sa.Column('sync_time', sa.String(), nullable=True))
    

def downgrade():
    op.drop_column('ad_sync_config', 'sync_time') 