# 用户管理界面简化 - 移除冗余开关

## 任务背景
用户在用户管理的"编辑用户"对话框中发现了"状态"和"超级管理员"两个开关选项，这些功能与其他地方的操作重复，造成界面冗余。

## 问题分析
1. **状态开关重复**：编辑用户对话框中的"状态"开关与用户列表中的"启用/禁用"按钮功能重复
2. **超级管理员开关重复**：编辑用户对话框中的"超级管理员"开关与"分配角色"功能重复
3. **界面复杂**：过多的选项增加了用户操作的复杂度

## 解决方案
移除编辑用户对话框中的两个开关选项，简化界面专注于基本信息编辑：
- 用户名
- 邮箱  
- 密码

## 实施步骤

### 1. 前端界面修改
- [x] 移除 `frontend/src/views/system/UserManagement.vue` 中的"状态"开关
- [x] 移除 `frontend/src/views/system/UserManagement.vue` 中的"超级管理员"开关
- [x] 调整保存逻辑，编辑时保持原有状态，新建时使用默认值

### 2. 功能保持
- [x] 用户状态管理：通过用户列表中的"启用/禁用"按钮
- [x] 超级管理员权限：通过"分配角色"功能分配super_admin角色
- [x] 新用户默认值：默认启用且为普通用户

## 修改文件
- `frontend/src/views/system/UserManagement.vue`

## 测试要点
1. 创建新用户：应该默认启用且为普通用户
2. 编辑现有用户：基本信息更新正常，状态和权限保持不变
3. 状态管理：通过列表中的"启用/禁用"按钮正常工作
4. 权限管理：通过"分配角色"功能正常工作

## 预期效果
- 简化编辑用户界面，专注于基本信息编辑
- 避免功能重复，降低用户操作复杂度
- 保持原有功能完整性，通过其他更合适的界面进行操作 