<template>
  <div class="personnel-option">
    <div class="personnel-name-job">
      <span class="personnel-name">{{ option.field_value }}</span>
      <span class="personnel-job-number" v-if="option.job_number">{{ option.job_number }}</span>
    </div>
    <div class="personnel-detail" v-if="option.dept_name || detail">
      {{ option.dept_name || detail }}
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  detail: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.personnel-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.personnel-name-job {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.personnel-name {
  font-weight: 500;
}

.personnel-job-number {
  font-size: 13px;
  color: #409EFF;
  margin-left: 8px;
  border: 1px solid #d9ecff;
  background-color: #ecf5ff;
  border-radius: 3px;
  padding: 0 4px;
}

.personnel-detail {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style>
