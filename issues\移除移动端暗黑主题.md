# 移除移动端暗黑主题任务

## 任务背景
移动端的暗黑主题存在问题，需要完全移除暗黑主题功能和主题切换功能。

## 执行计划
1. 删除ThemeSwitch组件
2. 简化useTheme composable
3. 清理主题样式文件
4. 清理移动端布局组件
5. 清理其他移动端组件
6. 验证和测试

## 执行状态
- [x] 创建任务记录
- [x] 第1步：移除主题切换组件
  - [x] 删除 `ThemeSwitch.vue` 组件
  - [x] 从系统页面移除ThemeSwitch组件引用
- [x] 第2步：简化useTheme composable
  - [x] 移除暗黑主题相关逻辑
  - [x] 固定为浅色主题模式
  - [x] 移除主题模式切换功能
- [x] 第3步：清理主题样式文件
  - [x] 移除暗黑主题CSS变量和样式
  - [x] 保留浅色主题样式定义
- [x] 第4步：清理移动端布局组件
  - [x] 移除MobileLayout中的暗黑主题相关类名和逻辑
- [x] 第5步：清理其他移动端组件
  - [x] 清理Login.vue中的暗黑主题代码
  - [x] 清理apps/index.vue中的暗黑主题样式
  - [x] 清理PullRefresh.vue中的暗黑主题适配
  - [x] 清理MobileCard.vue中的暗黑主题适配
- [x] 第6步：验证和测试
  - [x] 确认所有暗黑主题相关代码已移除

## 完成总结
✅ 已成功移除移动端的所有暗黑主题功能：
- 删除了主题切换组件
- 简化了useTheme composable，只保留浅色主题
- 清理了所有样式文件中的暗黑主题代码
- 移除了所有组件中的暗黑主题适配代码
- 移动端现在只支持浅色主题，代码更加简洁 