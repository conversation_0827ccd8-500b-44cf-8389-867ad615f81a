import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, Set
from fastapi import Request
from fastapi.responses import StreamingResponse

logger = logging.getLogger(__name__)

class SSEConnection:
    """SSE连接包装类"""
    
    def __init__(self, connection_id: str, request: Request):
        self.connection_id = connection_id
        self.request = request
        self.created_at = time.time()
        self.last_activity = time.time()
        self.subscribed_terminals: Set[int] = set()
        self._queue = asyncio.Queue(maxsize=100)  # 消息队列，防止内存溢出
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """发送消息到客户端"""
        try:
            if await self.request.is_disconnected():
                return False
                
            # 如果队列满了，移除最旧的消息
            if self._queue.full():
                try:
                    self._queue.get_nowait()
                except asyncio.QueueEmpty:
                    pass
                    
            await self._queue.put(message)
            self.last_activity = time.time()
            return True
        except Exception as e:
            logger.error(f"发送SSE消息失败: {e}")
            return False
            
    async def get_message(self) -> Optional[Dict[str, Any]]:
        """获取待发送的消息"""
        try:
            return await asyncio.wait_for(self._queue.get(), timeout=30.0)
        except asyncio.TimeoutError:
            # 30秒没有消息，发送心跳
            return {"type": "ping", "timestamp": time.time()}
        except Exception:
            return None
            
    def is_connected(self) -> bool:
        """检查连接是否有效"""
        # 10分钟无活动认为连接失效
        return time.time() - self.last_activity < 600

class TerminalSSEManager:
    """终端状态SSE管理器"""
    
    def __init__(self):
        self.connections: Dict[str, SSEConnection] = {}
        self.terminal_status_cache: Dict[int, Dict[str, Any]] = {}
        self.email_sync_progress_cache: Dict[str, Dict[str, Any]] = {}  # 邮箱同步进度缓存
        self._cleanup_task: Optional[asyncio.Task] = None
        
    def _start_cleanup_task(self):
        """启动连接清理任务"""
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            if self._cleanup_task is None or self._cleanup_task.done():
                self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
                logger.info("SSE清理任务已启动")
        except RuntimeError:
            # 没有运行中的事件循环，跳过任务创建
            logger.debug("暂无事件循环，SSE清理任务将稍后启动")
            
    async def start_background_tasks(self):
        """启动后台任务（应用启动时调用）"""
        self._start_cleanup_task()
        
    async def _periodic_cleanup(self):
        """定期清理无效连接"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                await self._cleanup_stale_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"连接清理任务异常: {e}")
                
    async def _cleanup_stale_connections(self):
        """清理失效的连接"""
        stale_connections = [
            conn_id for conn_id, conn in self.connections.items()
            if not conn.is_connected()
        ]
        
        for conn_id in stale_connections:
            logger.info(f"清理失效连接: {conn_id}")
            del self.connections[conn_id]
            
        if stale_connections:
            logger.info(f"清理了 {len(stale_connections)} 个失效连接，当前活跃连接: {len(self.connections)}")
            
    async def register_connection(self, request: Request) -> SSEConnection:
        """注册新的SSE连接"""
        connection_id = str(uuid.uuid4())
        connection = SSEConnection(connection_id, request)
        self.connections[connection_id] = connection
        
        logger.info(f"新SSE连接注册: {connection_id}，当前连接数: {len(self.connections)}")
        return connection
        
    async def unregister_connection(self, connection_id: str):
        """注销SSE连接"""
        if connection_id in self.connections:
            del self.connections[connection_id]
            logger.info(f"SSE连接注销: {connection_id}，当前连接数: {len(self.connections)}")
            
    async def broadcast_terminal_status(self, terminal_id: int, status_data: Dict[str, Any]):
        """广播终端状态变化"""
        # 检查状态是否真的发生了变化
        cached_status = self.terminal_status_cache.get(terminal_id)
        current_online_status = status_data.get('online_status')
        
        # 只有在线状态变化时才推送（避免心跳造成的无效推送）
        if cached_status is not None and cached_status.get('online_status') == current_online_status:
            logger.debug(f"终端 {terminal_id} 状态未变化，跳过推送")
            return
            
        # 更新缓存
        self.terminal_status_cache[terminal_id] = status_data.copy()
        
        # 准备推送消息
        message = {
            "type": "terminal_status_update",
            "terminal_id": terminal_id,
            "data": status_data,
            "timestamp": time.time()
        }
        
        # 推送给所有活跃连接
        active_connections = len(self.connections)
        if active_connections == 0:
            logger.debug(f"没有活跃的SSE连接，跳过终端 {terminal_id} 状态推送")
            return
            
        success_count = 0
        failed_connections = []
        
        for conn_id, connection in self.connections.items():
            success = await connection.send_message(message)
            if success:
                success_count += 1
            else:
                failed_connections.append(conn_id)
                
        # 清理失败的连接
        for conn_id in failed_connections:
            logger.warning(f"连接 {conn_id} 发送失败，标记为清理")
            if conn_id in self.connections:
                del self.connections[conn_id]
                
        logger.info(f"终端 {terminal_id} 状态推送完成: {success_count}/{active_connections} 连接成功")
        
    async def broadcast_email_sync_progress(self, sync_id: str, progress_data: Dict[str, Any]):
        """广播邮箱同步进度变化"""
        # 更新缓存
        self.email_sync_progress_cache[sync_id] = progress_data.copy()
        
        # 准备推送消息
        message = {
            "type": "email_sync_progress",
            "sync_id": sync_id,
            "data": progress_data,
            "timestamp": time.time()
        }
        
        # 推送给所有活跃连接
        active_connections = len(self.connections)
        if active_connections == 0:
            logger.debug(f"没有活跃的SSE连接，跳过邮箱同步 {sync_id} 进度推送")
            return
            
        success_count = 0
        failed_connections = []
        
        for conn_id, connection in self.connections.items():
            success = await connection.send_message(message)
            if success:
                success_count += 1
            else:
                failed_connections.append(conn_id)
                
        # 清理失败的连接
        for conn_id in failed_connections:
            logger.warning(f"连接 {conn_id} 发送失败，标记为清理")
            if conn_id in self.connections:
                del self.connections[conn_id]
                
        logger.info(f"邮箱同步 {sync_id} 进度推送完成: {success_count}/{active_connections} 连接成功")
        
    async def send_personal_message(self, connection_id: str, message: Dict[str, Any]):
        """向特定连接发送消息"""
        if connection_id in self.connections:
            await self.connections[connection_id].send_message(message)
            
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.connections)
        
    def get_status_summary(self) -> Dict[str, Any]:
        """获取SSE管理器状态摘要"""
        return {
            "active_connections": len(self.connections),
            "cached_terminals": len(self.terminal_status_cache),
            "cached_email_syncs": len(self.email_sync_progress_cache),
            "cleanup_task_running": self._cleanup_task is not None and not self._cleanup_task.done()
        }
        
    async def shutdown(self):
        """关闭SSE管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        # 清理所有连接
        self.connections.clear()
        self.terminal_status_cache.clear()
        self.email_sync_progress_cache.clear()
        logger.info("SSE管理器已关闭")

# 全局SSE管理器实例
sse_manager = TerminalSSEManager()

# 供其他模块调用的广播函数
async def broadcast_terminal_status_change(terminal_id: int, status_data: Dict[str, Any]):
    """
    外部调用接口：广播终端状态变化
    
    Args:
        terminal_id: 终端ID
        status_data: 状态数据，应包含:
            - online_status: bool, 在线状态
            - last_heartbeat_time: str, 最后心跳时间
            - hostname: str, 主机名
            - ip_address: str, IP地址
    """
    await sse_manager.broadcast_terminal_status(terminal_id, status_data) 