from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta, time
import pyodbc
import os
from dotenv import load_dotenv
from sqlalchemy import or_

from app.models.ecology_user import EcologyUser, SyncConfig
from app.api.v1.ecology import get_ecology_db_connection, EcologyUser as EcologyUserSchema

# 加载环境变量
load_dotenv()

# 获取所有本地存储的泛微用户
def get_ecology_users(db: Session, skip: int = 0, limit: int = 100, keyword: Optional[str] = None, exact_match: bool = False):
    query = db.query(EcologyUser)

    # 如果有关键词，添加搜索条件
    if keyword:
        if exact_match:
            # 精确匹配工号
            query = query.filter(EcologyUser.job_number == keyword)
        else:
            # 模糊匹配多个字段
            search = f"%{keyword}%"
            query = query.filter(
                or_(
                    EcologyUser.user_name.ilike(search),
                    EcologyUser.job_number.ilike(search),
                    EcologyUser.dept_name.ilike(search),
                    EcologyUser.job_title_name.ilike(search),
                    EcologyUser.mobile.ilike(search),
                    EcologyUser.email.ilike(search)
                )
            )

    return query.offset(skip).limit(limit).all()

# 获取同步配置
def get_sync_config(db: Session) -> Optional[SyncConfig]:
    config = db.query(SyncConfig).first()
    if not config:
        config = SyncConfig(
            sync_hour=8,  # 默认早上8点同步
            sync_time="08:00",
            last_sync_time=None,
            next_sync_time=None,
            sync_status="未同步"
        )
        db.add(config)
        db.commit()
        db.refresh(config)

    # 确保 sync_time 字段有值
    if config.sync_time is None:
        config.sync_time = f"{config.sync_hour:02d}:00"
        db.commit()
        db.refresh(config)

    return config

# 更新同步配置
def update_sync_config(db: Session, sync_time: str) -> SyncConfig:
    config = get_sync_config(db)

    # 解析同步时间
    hour, minute = map(int, sync_time.split(':'))
    config.sync_hour = hour
    config.sync_time = sync_time

    # 计算下次同步时间
    now = datetime.now()
    sync_datetime = datetime.combine(now.date(), time(hour, minute))

    # 如果当前时间已经过了今天的同步时间，则下次同步时间为明天的同步时间
    if now > sync_datetime:
        sync_datetime = datetime.combine(now.date() + timedelta(days=1), time(hour, minute))

    config.next_sync_time = sync_datetime

    db.commit()
    db.refresh(config)
    return config

# 从泛微数据库同步用户数据到本地数据库
def sync_ecology_users(db: Session) -> dict:
    config = get_sync_config(db)

    # 更新同步状态
    config.sync_status = "同步中"
    db.commit()

    try:
        # 获取泛微数据库连接
        conn = get_ecology_db_connection()
        cursor = conn.cursor()

        # 使用SQL文件中的查询
        query = """
        WITH DepartmentCTE AS (
            -- 根部门
            SELECT
                id AS DeptID,
                departmentname AS DeptName,
                CAST(departmentname AS NVARCHAR(4000)) AS DeptHierarchy,
                supdepid AS ParentDeptID,
                1 AS Level,
                CAST(id AS VARCHAR(100)) AS DeptPath,
                subcompanyid1 AS CompanyID
            FROM hrmdepartment
            WHERE supdepid = 0  -- 根部门条件

            UNION ALL

            -- 子部门
            SELECT
                d.id,
                d.departmentname,
                CAST(c.DeptHierarchy + N' > ' + d.departmentname AS NVARCHAR(4000)),
                d.supdepid,
                c.Level + 1,
                CAST(c.DeptPath + ',' + CAST(d.id AS VARCHAR(20)) AS VARCHAR(100)),
                d.subcompanyid1
            FROM hrmdepartment d
            INNER JOIN DepartmentCTE c ON d.supdepid = c.DeptID
            WHERE d.canceled = '0' OR d.canceled IS NULL  -- 只查询未封存的部门
        )
        SELECT
            d.DeptID,
            d.DeptName,
            d.DeptHierarchy,
            d.Level,
            d.DeptPath,
            s.id AS CompanyID,
            s.subcompanyname AS CompanyName,
            h.id AS UserID,
            h.lastname AS UserName,
            h.workcode AS JobNumber,
            h.mobile AS Mobile,
            h.email AS Email,
            h.jobtitle AS JobTitle,
            j.jobtitlename AS JobTitleName,
            CASE h.sex
                WHEN 0 THEN N'男'
                WHEN 1 THEN N'女'
                ELSE CAST(h.sex AS NVARCHAR(10))
            END AS Gender,
            CASE h.status
                WHEN 0 THEN N'试用'
                WHEN 1 THEN N'正式'
                WHEN 2 THEN N'临时'
                WHEN 3 THEN N'试用延期'
                WHEN 4 THEN N'解聘'
                WHEN 5 THEN N'离职'
                WHEN 6 THEN N'退休'
                WHEN 7 THEN N'无效'
                ELSE N'未知'
            END AS Status
        FROM DepartmentCTE d
        LEFT JOIN hrmsubcompany s ON d.CompanyID = s.id
        LEFT JOIN hrmresource h ON d.DeptID = h.departmentid
        LEFT JOIN hrmjobtitles j ON h.jobtitle = j.id
        ORDER BY s.subcompanyname, d.DeptPath, d.DeptName, h.lastname;
        """

        cursor.execute(query)

        # 清空本地数据库中的用户数据
        db.query(EcologyUser).delete()

        # 插入新的用户数据
        count = 0
        for row in cursor.fetchall():
            # 移除只同步有用户ID的记录的限制，同步所有记录
            user = EcologyUser(
                user_id=row.UserID,
                dept_id=row.DeptID,
                dept_name=row.DeptName,
                dept_hierarchy=row.DeptHierarchy,
                level=row.Level,
                dept_path=row.DeptPath,
                company_id=row.CompanyID,
                company_name=row.CompanyName,
                user_name=row.UserName,
                job_number=row.JobNumber,
                mobile=row.Mobile,
                email=row.Email,
                job_title=row.JobTitle,
                job_title_name=row.JobTitleName,
                gender=row.Gender,
                status=row.Status
            )
            db.add(user)
            count += 1

        # 更新同步配置
        now = datetime.now()
        config.last_sync_time = now

        # 计算下次同步时间
        if config.sync_time:
            hour, minute = map(int, config.sync_time.split(':'))
        else:
            hour = config.sync_hour
            minute = 0
            config.sync_time = f"{hour:02d}:00"

        sync_datetime = datetime.combine(now.date(), time(hour, minute))

        # 如果当前时间已经过了今天的同步时间，则下次同步时间为明天的同步时间
        if now > sync_datetime:
            sync_datetime = datetime.combine(now.date() + timedelta(days=1), time(hour, minute))

        config.next_sync_time = sync_datetime
        config.sync_status = "同步成功"
        config.error_message = None

        db.commit()

        cursor.close()
        conn.close()

        return {"status": "success", "message": f"成功同步 {count} 条用户数据", "count": count}

    except Exception as e:
        # 同步失败，更新状态
        config.sync_status = "同步失败"
        config.error_message = str(e)
        db.commit()

        return {"status": "error", "message": f"同步失败: {str(e)}"}

# 检查是否需要同步
def check_sync_needed(db: Session) -> bool:
    config = get_sync_config(db)

    # 如果从未同步过，则需要同步
    if not config.last_sync_time:
        return True

    # 如果当前时间已经超过下次同步时间，则需要同步
    if config.next_sync_time and datetime.now() > config.next_sync_time:
        return True

    return False

# 获取同步状态
def get_sync_status(db: Session) -> SyncConfig:
    config = get_sync_config(db)
    return config