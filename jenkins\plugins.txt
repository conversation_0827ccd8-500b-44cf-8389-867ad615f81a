# OPS平台Jenkins必需插件列表
# 这些插件支持CI/CD流程的完整功能

# 核心插件
workflow-aggregator:latest
git:latest
credentials:latest
kubernetes:latest

# 构建和测试插件
pipeline-stage-view:latest
blueocean:latest
junit:latest
cobertura:latest
sonar:latest

# Docker相关插件
docker-plugin:latest
docker-workflow:latest
docker-build-step:latest

# 通知插件
email-ext:latest
dingding:latest
wechat:latest
slack:latest

# 安全插件
matrix-auth:latest
role-strategy:latest
ldap:latest

# 监控和报告插件
metrics:latest
monitoring:latest
build-timeout:latest
timestamper:latest

# 工具集成插件
maven-plugin:latest
gradle:latest
nodejs:latest
python:latest

# 部署插件
deploy:latest
deployment:latest
kubernetes-cli:latest

# 其他实用插件
copyartifact:latest
parameterized-trigger:latest
build-name-setter:latest
build-timestamp:latest
