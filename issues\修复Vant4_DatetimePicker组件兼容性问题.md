# 修复Vant 4 DatetimePicker组件兼容性问题

## 问题描述

移动端盘点任务页面在启动时出现以下错误：

```
Failed to resolve import "vant/es/datetime-picker/style/index" from "src/mobile/views/asset/InventoryTask.vue". Does the file exist?
```

## 问题原因

1. **核心问题**：项目中使用了 `van-datetime-picker` 组件，但 Vant 4 已经将该组件重构并移除
2. **架构变更**：Vant 4 将 `DatetimePicker` 组件拆分为三个独立组件：
   - `TimePicker` - 时间选择
   - `DatePicker` - 日期选择  
   - `PickerGroup` - 组合选择器
3. **路径错误**：`vant/es/datetime-picker/style/index` 路径在 Vant 4 中不存在

## 解决方案

### 方案选择
采用**方案1**：将 `van-datetime-picker` 替换为 `van-date-picker`
- ✅ 符合 Vant 4 最新架构
- ✅ 性能更好
- ✅ 代码改动不大（仅需要日期选择功能）

### 具体修改

#### 1. 组件替换
**文件**：`frontend/src/mobile/views/asset/InventoryTask.vue`

```diff
<!-- 自定义字段日期选择器 -->
<van-popup v-model:show="showCustomFieldDatePicker" position="bottom">
-  <van-datetime-picker
+  <van-date-picker
     v-model="customFieldDateValue"
-    type="date"
     :title="currentCustomField?.label"
     @confirm="onCustomFieldDateConfirm"
     @cancel="showCustomFieldDatePicker = false"
   />
</van-popup>
```

#### 2. 数据类型调整
```diff
// 自定义字段选择器相关
const currentCustomField = ref<CustomField | null>(null)
- const customFieldDateValue = ref('')
+ const customFieldDateValue = ref<string[]>([])
const customFieldSelectOptions = ref<any[]>([])
```

#### 3. 事件处理更新
```diff
// 日期选择确认
- const onCustomFieldDateConfirm = (value: string) => {
-   if (currentCustomField.value) {
-     customFieldData[currentCustomField.value.name] = value
-   }
-   showCustomFieldDatePicker.value = false
- }
+ const onCustomFieldDateConfirm = ({ selectedValues }: any) => {
+   if (currentCustomField.value && selectedValues) {
+     // DatePicker 返回的是数组格式 ['2023', '12', '15']，需要转换为日期字符串
+     const dateStr = selectedValues.join('-')
+     customFieldData[currentCustomField.value.name] = dateStr
+   }
+   showCustomFieldDatePicker.value = false
+ }
```

#### 4. 数据初始化逻辑
```diff
// 显示日期选择器
const showDatePicker = (field: CustomField) => {
  currentCustomField.value = field
-  customFieldDateValue.value = customFieldData[field.name] || ''
+  const dateStr = customFieldData[field.name] || ''
+  // 将日期字符串转换为DatePicker需要的数组格式
+  if (dateStr) {
+    customFieldDateValue.value = dateStr.split('-')
+  } else {
+    // 默认为当前日期
+    const now = new Date()
+    customFieldDateValue.value = [
+      now.getFullYear().toString(),
+      (now.getMonth() + 1).toString().padStart(2, '0'),
+      now.getDate().toString().padStart(2, '0')
+    ]
+  }
  showCustomFieldDatePicker.value = true
}
```

## 验证结果

- ✅ 移动端盘点任务页面正常加载
- ✅ 日期选择器功能正常工作
- ✅ 数据格式转换正确
- ✅ 无其他Vant组件兼容性问题

## 技术要点

### Vant 4 DatePicker vs Vant 3 DatetimePicker

| 特性 | Vant 3 DatetimePicker | Vant 4 DatePicker |
|------|----------------------|-------------------|
| 数据格式 | 字符串 `'2023-12-15'` | 数组 `['2023', '12', '15']` |
| 类型属性 | `type="date"` | 无需指定（专用组件） |
| 事件参数 | 直接返回值 | 事件对象 `{selectedValues}` |
| 样式导入 | 自动导入可能失效 | 新的导入路径 |

### 最佳实践

1. **升级准备**：在升级Vant版本前，先查阅官方迁移指南
2. **渐进式替换**：优先替换简单组件，复杂场景考虑兼容包
3. **数据格式统一**：建立数据转换工具函数，统一处理格式差异
4. **测试覆盖**：确保所有涉及的功能都经过测试验证

## 相关资源

- [Vant 4 升级指南](https://github.com/youzan/vant/blob/main/packages/vant/docs/markdown/migrate-from-v3.zh-CN.md)
- [Vant 4 DatePicker 文档](https://github.com/youzan/vant/blob/main/packages/vant/src/date-picker/README.md)
- [@vant/compat 兼容包](https://github.com/youzan/vant/tree/main/packages/vant-compat)

---

**修复时间**：2025-01-03  
**影响范围**：移动端盘点任务页面  
**测试状态**：已验证通过 