# 邮箱管理模块 userid 字段映射说明

## 概述

本文档说明了项目中 `userid` 字段与 `email` 字段的映射关系和使用规范。

## 字段定义

### 数据库模型
- **email**: 数据库中存储的邮箱地址字段
- **extid**: 企业内部工号字段
- **userid**: 模型属性，返回 email 字段值，用于腾讯API兼容

### 腾讯企业邮箱API
根据 [腾讯企业邮箱API文档](https://exmail.qq.com/qy_mng_logic/doc#10001)：
- **userid**: API参数，值为邮箱地址格式（如：<EMAIL>）
- **extid**: API可选参数，企业内部编号

## 映射关系

```
项目数据库字段     腾讯API字段      说明
email           userid         邮箱地址，作为用户的唯一标识
extid           extid          工号，企业内部编号（可选）
```

## 代码示例

### 模型定义
```python
class EmailMember(Base):
    email = Column(String(100), unique=True, index=True, 
                  comment="邮箱地址（对应腾讯企业邮箱API的userid字段）")
    extid = Column(String(50), unique=True, index=True, comment="工号")
    
    @property
    def userid(self) -> str:
        """返回腾讯企业邮箱API所需的userid值"""
        return self.email
```

### API调用
```python
# 创建成员时的API数据
api_data = {
    "userid": member.email,    # 腾讯API的userid参数值为邮箱地址
    "extid": member.extid,     # 腾讯API的可选字段：工号
    "name": member.name,
    # ... 其他字段
}
```

### CRUD操作
```python
# 根据腾讯API的userid查询成员
def get_by_userid(self, db: Session, userid: str) -> Optional[EmailMember]:
    """根据腾讯企业邮箱API的userid获取成员
    
    注意：腾讯API中的userid实际上是邮箱地址
    """
    return self.get_by_email(db, email=userid)
```

## 最佳实践

1. **API调用时**：始终使用 `email` 字段值作为腾讯API的 `userid` 参数
2. **日志记录时**：过滤敏感信息（如密码），避免泄露
3. **注释规范**：明确说明 userid 与 email 的映射关系
4. **命名规范**：函数名要明确表达功能，避免混淆

## 安全注意事项

1. **敏感信息过滤**：在日志记录时必须过滤密码等敏感信息
2. **访问控制**：确保只有授权用户可以访问邮箱管理API
3. **数据验证**：严格验证邮箱格式和必填字段

## 相关文档

- [腾讯企业邮箱API文档](https://exmail.qq.com/qy_mng_logic/doc#10001)
- 项目邮箱管理模块修改总结：`邮箱管理模块修改总结.md` 