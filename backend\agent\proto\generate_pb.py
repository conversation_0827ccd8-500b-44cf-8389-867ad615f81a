import os
import sys
import subprocess
from pathlib import Path

def generate_proto_code():
    """
    编译终端管理的 proto 文件生成 Python 代码
    """
    # 当前目录 (proto/)
    current_dir = Path(__file__).parent.absolute()
    
    # 输出目录
    output_dir = current_dir.parent / "terminal_pb"
    os.makedirs(output_dir, exist_ok=True)
    
    # proto 文件目录
    proto_dir = current_dir
    
    # 查找所有 proto 文件
    proto_files = list(proto_dir.glob("*.proto"))
    
    if not proto_files:
        print("No proto files found in", proto_dir)
        return False
    
    # 编译每个 proto 文件
    for proto_file in proto_files:
        print(f"Compiling {proto_file.name}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "grpc_tools.protoc",
                f"--proto_path={proto_dir}",
                f"--python_out={output_dir}",
                f"--grpc_python_out={output_dir}",
                str(proto_file)
            ], check=True, capture_output=True, text=True)
            
            print(f"Successfully compiled {proto_file.name}")
            
            # 修复导入路径
            fix_import_path(output_dir, proto_file.stem)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to compile {proto_file.name}")
            print(f"Error: {e.stderr}")
            return False

def fix_import_path(output_dir, proto_name):
    """修复生成的Python文件中的导入路径问题"""
    grpc_file = output_dir / f"{proto_name}_pb2_grpc.py"
    
    if not grpc_file.exists():
        return
    
    # 读取文件内容
    with open(grpc_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入路径
    fixed_content = content.replace(
        f"import {proto_name}_pb2", 
        f"from . import {proto_name}_pb2"
    )
    
    # 写回文件
    with open(grpc_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed import path in {grpc_file}")

if __name__ == "__main__":
    generate_proto_code() 