from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base


class CommandCategory(Base):
    """命令分类表"""
    __tablename__ = "command_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, comment="分类名称")
    description = Column(Text, comment="分类描述")
    required_permission = Column(String(100), nullable=False, comment="所需权限")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    commands = relationship("CommandWhitelist", back_populates="category", cascade="all, delete-orphan")


class CommandWhitelist(Base):
    """命令白名单表"""
    __tablename__ = "command_whitelist"

    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(Integer, ForeignKey("command_categories.id"), nullable=False, comment="分类ID")
    command = Column(String(500), nullable=False, comment="命令内容")
    name = Column(String(200), nullable=False, comment="命令名称")
    description = Column(Text, comment="命令描述")
    example = Column(Text, comment="使用示例")
    timeout = Column(Integer, default=30, comment="超时时间(秒)")
    admin_required = Column(Boolean, default=False, comment="是否需要管理员权限")
    is_active = Column(Boolean, default=True, comment="是否启用")
    security_level = Column(String(20), default="PUBLIC", comment="安全级别: PUBLIC, OPERATOR, ADMIN")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关联关系
    category = relationship("CommandCategory", back_populates="commands") 