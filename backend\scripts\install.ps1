# OPS Platform Backend - UV Installation Script
# 安装和配置uv环境

Write-Host "安装OPS Platform Backend依赖 (使用uv)..." -ForegroundColor Green

# 确保在backend目录
Set-Location $PSScriptRoot\..

# 检查uv是否安装
if (-not (Get-Command uv -ErrorAction SilentlyContinue)) {
    Write-Host "uv未安装，正在安装..." -ForegroundColor Yellow
    pip install uv
}

# 显示uv版本
Write-Host "uv版本:" -ForegroundColor Cyan
uv --version

# 创建虚拟环境（如果不存在）
if (-not (Test-Path ".venv-uv")) {
    Write-Host "创建虚拟环境..." -ForegroundColor Yellow
    uv venv .venv-uv
}

# 同步依赖
Write-Host "同步项目依赖..." -ForegroundColor Yellow
uv sync --index-url https://pypi.tuna.tsinghua.edu.cn/simple

Write-Host "安装完成！" -ForegroundColor Green
Write-Host "使用 'uv run python run.py' 启动服务器" -ForegroundColor Cyan 