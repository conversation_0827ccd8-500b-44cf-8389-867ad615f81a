<template>
  <div class="mobile-asset-list">
    <!-- 搜索栏 -->
    <div class="search-section">
      <integrated-search
        v-model="searchValue"
        v-model:search-field="searchField"
        @search="handleSearch"
        @clear="handleSearchClear"
      />
    </div>
    
    <!-- 筛选栏 -->
    <div class="filter-section">
      <van-dropdown-menu>
        <van-dropdown-item
          v-model="filters.status"
          :options="statusOptions"
          @change="handleFilterChange"
        />
        <van-dropdown-item
          v-model="filters.category"
          :options="categoryOptions"
          @change="handleFilterChange"
        />
        <van-dropdown-item
          v-model="filters.sort"
          :options="sortOptions"
          @change="handleFilterChange"
        />
      </van-dropdown-menu>
    </div>
    
    <!-- 统计信息 -->
    <mobile-card v-if="!searchValue" class="stats-card" :border="false">
      <van-grid :border="false" :column-num="3">
        <van-grid-item
          v-for="stat in stats"
          :key="stat.key"
          :text="stat.label"
        >
          <template #icon>
            <div class="stat-value" :class="stat.class">{{ stat.value }}</div>
          </template>
          <template #text>
            <div class="stat-label">{{ stat.label }}</div>
          </template>
        </van-grid-item>
      </van-grid>
    </mobile-card>
    
    <!-- 资产列表 -->
    <mobile-list
      :items="assetList"
      :loading="loading"
      :finished="finished"
      :error="error"
      item-key="id"
      @load="loadAssets"
      @refresh="refreshAssets"
      @item-click="handleAssetClick"
    >
      <template #default="{ items }">
        <div class="asset-cards">
          <mobile-card
            v-for="asset in items"
            :key="asset.id"
            class="asset-card"
            clickable
            @click="handleAssetClick(asset)"
          >
            <template #header>
              <div class="asset-header">
                <div class="asset-info">
                  <span class="asset-code">{{ asset.asset_number }}</span>
                  <van-tag
                    :type="getStatusTagType(asset.status)"
                  >
                    {{ getStatusText(asset.status) }}
                  </van-tag>
                </div>
                <van-icon name="arrow" />
              </div>
            </template>
            
            <div class="asset-content">
              <h3 class="asset-name" v-html="highlightText(asset.name)"></h3>
              <div class="asset-details">
                <div class="detail-item">
                  <van-icon name="location-o" />
                  <span v-html="highlightText(asset.location || '未设置')"></span>
                </div>
                <div class="detail-item">
                  <van-icon name="user-o" />
                  <span v-html="highlightText(asset.user || '未分配')"></span>
                </div>
                <div class="detail-item">
                  <van-icon name="calendar-o" />
                  <span>{{ formatDate(asset.purchase_date) }}</span>
                </div>
              </div>
            </div>
            
            <template #footer>
              <div class="asset-actions">
                <van-button
                  size="mini"
                  type="primary"
                  @click.stop="handleEdit(asset)"
                >
                  编辑
                </van-button>
                <van-button
                  size="mini"
                  @click.stop="handleTransfer(asset)"
                >
                  转移
                </van-button>
                <van-button
                  size="mini"
                  @click.stop="handleQRCode(asset)"
                >
                  二维码
                </van-button>
              </div>
            </template>
          </mobile-card>
        </div>
      </template>
    </mobile-list>
    
    <!-- 悬浮操作按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAdd"
    />
    
    <!-- 二维码弹窗 -->
    <van-popup
      v-model:show="showQRCode"
      position="center"
      :style="{ padding: '20px' }"
    >
      <div v-if="currentAsset" class="qr-content">
        <h3>{{ currentAsset.name }}</h3>
        <div class="qr-code">
          <!-- 这里可以集成二维码生成库 -->
          <van-loading size="50" />
          <p>生成二维码中...</p>
        </div>
        <van-button block @click="showQRCode = false">关闭</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { assetApi } from '@/api/asset'
import { fieldValueApi } from '@/api/field_value'
import MobileCard from '@mobile/components/business/MobileCard.vue'
import MobileList from '@mobile/components/business/MobileList.vue'
import IntegratedSearch from '@mobile/components/business/IntegratedSearch.vue'
import { formatDate } from '@/utils/date'
import { highlightKeyword } from '@/utils/highlight'

const router = useRouter()

// 数据状态
const loading = ref(false)
const finished = ref(false)
const error = ref(false)
const assetList = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)

// 搜索相关
const searchValue = ref('')
const searchField = ref('asset_number')

// 筛选条件
const filters = reactive({
  status: '',
  category: '',
  sort: 'created_desc'
})

// 二维码弹窗
const showQRCode = ref(false)
const currentAsset = ref<any>(null)

// 统计数据
const stats = ref([
  { key: 'total', label: '总资产', value: '0', class: 'primary' },
  { key: 'active', label: '在用', value: '0', class: 'success' },
  { key: 'maintenance', label: '维护中', value: '0', class: 'warning' }
])

// 筛选选项
const statusOptions = [
  { text: '全部状态', value: '' },
  { text: '在用', value: 'active' },
  { text: '闲置', value: 'idle' },
  { text: '维护中', value: 'maintenance' },
  { text: '报废', value: 'scrapped' }
]

const categoryOptions = ref([
  { text: '全部分类', value: '' }
])

const sortOptions = [
  { text: '最新创建', value: 'created_desc' },
  { text: '最早创建', value: 'created_asc' },
  { text: '资产编号', value: 'code_asc' },
  { text: '购买日期', value: 'purchase_desc' }
]

// 加载类别选项
const loadCategoryOptions = async () => {
  try {
    const response = await fieldValueApi.getFieldValues({
      field_name: 'category',
      limit: 100
    })
    const categories = response.data?.data || []
    categoryOptions.value = [
      { text: '全部分类', value: '' },
      ...categories.map((cat: any) => ({
        text: cat.field_value,
        value: cat.field_value
      }))
    ]
  } catch (error) {
    console.error('加载分类选项失败:', error)
  }
}

// 加载资产列表
const loadAssets = async (isRefresh = false) => {
  try {
    if (isRefresh) {
      currentPage.value = 1
      assetList.value = []
      finished.value = false
      error.value = false
    }
    
    loading.value = true
    
    // 构建API参数，匹配后端接口
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      keyword: searchValue.value || undefined,
      search_field: searchField.value || undefined,
      status: filters.status || undefined,
      category: filters.category || undefined,
      sort_by: getSortField(filters.sort),
      sort_order: getSortOrder(filters.sort)
    }
    
    // 调用真实的API
    const response = await assetApi.getAssets(params)
    
    const newAssets = response.data.data || []
    
    if (isRefresh) {
      assetList.value = newAssets
    } else {
      assetList.value.push(...newAssets)
    }
    
    totalCount.value = response.data.total || 0
    
    // 只有非刷新且有新数据时才增加页码
    if (!isRefresh && newAssets.length > 0) {
      currentPage.value++
    }
    
    // 更新统计数据
    updateStats()
    
    // 检查是否还有更多数据
    if (assetList.value.length >= totalCount.value) {
      finished.value = true
    }
    
  } catch (err: any) {
    console.error('加载资产失败:', err)
    error.value = true
    showToast('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

// 刷新资产列表
const refreshAssets = () => {
  loadAssets(true)
}

// 处理搜索
const handleSearch = async (value: string, field: string) => {
  searchValue.value = value
  searchField.value = field
  // 重置页码并刷新数据
  currentPage.value = 1
  await loadAssets(true)
}

// 处理搜索清空
const handleSearchClear = async () => {
  searchValue.value = ''
  searchField.value = 'asset_number' // 重置为默认搜索字段
  currentPage.value = 1
  await loadAssets(true)
}

// 高亮搜索关键词
const highlightText = (text: string) => {
  if (!searchValue.value || !text) {
    return text
  }
  return highlightKeyword(text, searchValue.value, 'search-highlight')
}

// 处理筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
  loadAssets(true)
}

// 处理资产点击
const handleAssetClick = (asset: any) => {
  router.push(`/m/asset/detail/${asset.id}`)
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, 'success' | 'warning' | 'primary' | 'danger' | 'default'> = {
    '使用中': 'success',
    'IN_USE': 'success',
    '闲置': 'warning', 
    'IDLE': 'warning',
    '维修中': 'primary',
    'MAINTENANCE': 'primary',
    '已报废': 'danger',
    'SCRAPPED': 'danger'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '使用中': '在用',
    'IN_USE': '在用',
    '闲置': '闲置',
    'IDLE': '闲置',
    '维修中': '维护中',
    'MAINTENANCE': '维护中',
    '已报废': '报废',
    'SCRAPPED': '报废',
    '已转移': '转移',
    'TRANSFERRED': '转移',
    '库存': '库存',
    'INVENTORY': '库存'
  }
  return statusMap[status] || status || '未知'
}

// 排序字段映射
const getSortField = (sort: string) => {
  const sortMap: Record<string, string> = {
    'created_desc': 'created_at',
    'created_asc': 'created_at',
    'code_asc': 'asset_number',
    'purchase_desc': 'purchase_date'
  }
  return sortMap[sort] || undefined
}

// 排序方向映射
const getSortOrder = (sort: string) => {
  if (sort.includes('_desc')) return 'desc'
  if (sort.includes('_asc')) return 'asc'
  return undefined
}

// 更新统计数据
const updateStats = () => {
  const total = totalCount.value
  const active = assetList.value.filter(item => item.status === '使用中' || item.status === 'IN_USE').length
  const maintenance = assetList.value.filter(item => item.status === '维修中' || item.status === 'MAINTENANCE').length
  
  stats.value = [
    { key: 'total', label: '总资产', value: total.toString(), class: 'primary' },
    { key: 'active', label: '在用', value: active.toString(), class: 'success' },
    { key: 'maintenance', label: '维护中', value: maintenance.toString(), class: 'warning' }
  ]
}

// 处理添加
const handleAdd = () => {
  router.push('/m/asset/add')
}

// 处理编辑
const handleEdit = (asset: any) => {
  router.push(`/m/asset/edit/${asset.id}`)
}

// 处理转移
const handleTransfer = async (asset: any) => {
  try {
    await showConfirmDialog({
      title: '资产转移',
      message: `确定要转移资产 ${asset.asset_code} 吗？`,
    })
    
    // 这里实现转移逻辑
    showToast('转移功能开发中...')
  } catch (error) {
    // 取消操作
  }
}

// 处理二维码
const handleQRCode = (asset: any) => {
  currentAsset.value = asset
  showQRCode.value = true
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategoryOptions()
  loadAssets(true)
})
</script>

<style lang="scss" scoped>
.mobile-asset-list {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.search-section {
  background: var(--van-background);
  padding: var(--van-padding-sm) 0;
}

.filter-section {
  background: var(--van-background);
  border-bottom: 1px solid var(--van-border-color);
}

.stats-card {
  margin: var(--van-padding-sm) 0;
  
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    
    &.primary { color: var(--van-primary-color); }
    &.success { color: var(--van-success-color); }
    &.warning { color: var(--van-warning-color); }
  }
  
  .stat-label {
    font-size: var(--van-font-size-sm);
    color: var(--van-text-color-2);
    margin-top: 4px;
  }
}

.asset-cards {
  padding: var(--van-padding-sm) 0;
  
  .asset-card {
    margin-bottom: var(--van-padding-sm);
  }
}

.asset-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.asset-info {
  display: flex;
  align-items: center;
  gap: var(--van-padding-sm);
}

.asset-code {
  font-size: var(--van-font-size-lg);
  font-weight: 500;
  color: var(--van-primary-color);
}

.asset-content {
  .asset-name {
    margin: 0 0 var(--van-padding-sm) 0;
    font-size: var(--van-font-size-lg);
    color: var(--van-text-color);
  }
}

.asset-details {
  .detail-item {
    display: flex;
    align-items: center;
    gap: var(--van-padding-xs);
    margin-bottom: var(--van-padding-xs);
    font-size: var(--van-font-size-sm);
    color: var(--van-text-color-2);
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.asset-actions {
  display: flex;
  gap: var(--van-padding-sm);
  justify-content: flex-end;
}

.qr-content {
  text-align: center;
  min-width: 250px;
  
  h3 {
    margin: 0 0 var(--van-padding-md) 0;
    color: var(--van-text-color);
  }
  
  .qr-code {
    padding: var(--van-padding-lg);
    border: 1px dashed var(--van-border-color);
    border-radius: var(--van-radius-md);
    margin-bottom: var(--van-padding-md);
    
    p {
      margin: var(--van-padding-sm) 0 0 0;
      color: var(--van-text-color-2);
    }
  }
}

// 搜索高亮样式
:deep(.search-highlight) {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}
</style> 