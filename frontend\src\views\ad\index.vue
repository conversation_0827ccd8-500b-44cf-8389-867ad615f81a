<template>
  <div class="ad-container">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Connection /></el-icon>
        <h2 class="page-title">AD管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>AD管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-row :gutter="12" class="h-full">
      <el-col :span="6" class="h-full">
        <div class="aside-container">
          <div class="aside-header">
            <span class="aside-title">组织单位(OU)树</span>
          </div>
          <div class="aside-content">
            <OUTree v-if="isReady" ref="ouTreeRef" @select="handleOUSelect" />
            <div v-else class="loading-placeholder">
              <el-skeleton :rows="10" animated />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18" class="h-full">
        <div class="main-container">
          <div class="main-header">
            <div class="header-left">
              <div class="tab-with-ou">
                <el-radio-group v-model="activeTab" size="large" class="view-switcher">
                  <el-radio-button label="users">用户管理</el-radio-button>
                  <el-radio-button label="groups">组管理</el-radio-button>
                </el-radio-group>
                <el-tag v-if="selectedOU" type="info" class="current-ou" size="large">
                  当前OU: {{ getOUNameByDN(selectedOU) }}
                </el-tag>
              </div>
            </div>
            <div class="header-center">
            </div>
            <div class="header-right">
              <!-- 添加搜索框，仅在用户管理标签页时显示 -->
              <el-input
                v-if="activeTab === 'users' && selectedOU"
                v-model="searchQuery"
                placeholder="搜索用户"
                class="search-input"
                clearable
                @update:model-value="handleSearch"
                :prefix-icon="Search"
              >
              </el-input>
              <!-- 添加搜索框，仅在组管理标签页时显示 -->
              <el-input
                v-if="activeTab === 'groups' && selectedOU"
                v-model="groupSearchQuery"
                placeholder="搜索组"
                class="search-input"
                clearable
                @update:model-value="handleGroupSearch"
                :prefix-icon="Search"
              >
              </el-input>
              <el-dropdown trigger="click">
                <el-button type="primary" plain class="menu-button">
                  <el-icon><Connection /></el-icon>
                  功能菜单
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <template v-if="activeTab === 'users'">
                      <Authority permission="ad:user:manage">
                        <el-dropdown-item @click="handleAddUser" :disabled="!selectedOU">
                          <el-icon><Plus /></el-icon>添加用户
                        </el-dropdown-item>
                      </Authority>
                      <Authority permission="ad:user:manage">
                        <el-dropdown-item @click="handleBulkImport" :disabled="!selectedOU">
                          <el-icon><Upload /></el-icon>批量导入用户
                        </el-dropdown-item>
                      </Authority>
                      <Authority permission="ad:view">
                        <el-dropdown-item divided @click="handleExport" :disabled="!selectedOU || exportingUsers">
                          <el-icon><Download /></el-icon>导出用户
                          <el-icon v-if="exportingUsers" class="is-loading"><Loading /></el-icon>
                        </el-dropdown-item>
                      </Authority>
                      <el-dropdown-item divided></el-dropdown-item>
                    </template>
                    <template v-if="activeTab === 'groups'">
                      <Authority permission="ad:group:manage">
                        <el-dropdown-item @click="handleAddGroup" :disabled="!selectedOU">
                          <el-icon><Plus /></el-icon>添加组
                        </el-dropdown-item>
                      </Authority>
                    </template>
                    <Authority permission="ad:view">
                      <el-dropdown-item @click="testUsers" :disabled="!selectedOU || testingUsers">
                        <el-icon><Monitor /></el-icon>
                        <span>测试用户</span>
                        <el-icon v-if="testingUsers" class="is-loading"><Loading /></el-icon>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="ad:view">
                      <el-dropdown-item @click="testGroups" :disabled="!selectedOU || testingGroups">
                        <el-icon><Connection /></el-icon>
                        <span>测试组</span>
                        <el-icon v-if="testingGroups" class="is-loading"><Loading /></el-icon>
                      </el-dropdown-item>
                    </Authority>
                    <Authority permission="ad:sync">
                      <el-dropdown-item @click="showSyncFromPersonnelDialog" :disabled="!selectedOU || syncingPersonnel">
                        <el-icon><RefreshRight /></el-icon>
                        <span>从人员信息同步</span>
                        <el-icon v-if="syncingPersonnel" class="is-loading"><Loading /></el-icon>
                      </el-dropdown-item>
                    </Authority>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          <div class="main-content">
            <UserList v-if="isReady && selectedOU && activeTab === 'users'" :ou-dn="selectedOU" ref="userListRef" />
            <GroupList v-if="isReady && selectedOU && activeTab === 'groups'" :ou-dn="selectedOU" ref="groupListRef" />
            <div v-if="!selectedOU" class="empty-state">
              <el-empty description="请选择一个组织单位" />
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>

  <!-- 测试结果抽屉 -->
  <el-drawer
    v-model="testResultVisible"
    :title="testTitle"
    size="50%"
    :destroy-on-close="true"
  >
    <div class="test-result-content">
      <div v-if="testResult" class="result-section">
        <h3>连接信息</h3>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="连接状态">
            <el-tag :type="testResult.connection?.bound ? 'success' : 'danger'">
              {{ testResult.connection?.bound ? '已连接' : '未连接' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="服务器">
            {{ testResult.connection?.server }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ testResult.connection?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="搜索基础">
            {{ testResult.connection?.search_base }}
          </el-descriptions-item>
        </el-descriptions>

        <h3 class="mt-4">搜索结果</h3>
        <template v-if="testResult.search_result">
          <div class="mb-2">
            <el-tag type="info">
              总数: {{ testResult.search_result.total_users || testResult.search_result.total_groups || 0 }}
            </el-tag>
          </div>

          <el-table
            v-if="testResult.search_result.users"
            :data="testResult.search_result.users"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="name" label="显示名称" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="enabled" label="状态">
              <template #default="scope">
                <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
                  {{ scope.row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>

          <el-table
            v-if="testResult.search_result.groups"
            :data="testResult.search_result.groups"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column prop="name" label="组名" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="memberCount" label="成员数" />
          </el-table>
        </template>

        <div v-if="testResult.raw_result" class="mt-4">
          <h3>原始结果</h3>
          <el-input
            type="textarea"
            :rows="4"
            v-model="testResult.raw_result"
            readonly
          />
        </div>
      </div>

      <div v-if="!testResult?.success" class="error-section">
        <el-alert
          :title="testResult?.message || '测试失败'"
          type="error"
          :description="testResult?.details?.args?.join('\n')"
          show-icon
        />
      </div>
    </div>
  </el-drawer>

  <!-- 从人员信息同步对话框 -->
  <el-dialog
    v-model="syncDialogVisible"
    title="从人员信息同步"
    width="550px"
  >
    <el-form ref="syncFormRef" :model="syncForm" :rules="syncRules" label-width="140px" class="sync-form">
      <el-form-item label="同步范围" prop="syncType">
        <el-radio-group v-model="syncForm.syncType">
          <el-radio label="all">所有人员</el-radio>
          <el-radio label="company">按公司</el-radio>
          <el-radio label="department">按部门</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="syncForm.syncType === 'company'" label="选择公司" prop="companyId">
        <el-select v-model="syncForm.companyId" placeholder="请选择公司" filterable>
          <el-option
            v-for="company in companies"
            :key="company.id"
            :label="company.name"
            :value="company.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="syncForm.syncType === 'department'" label="选择部门" prop="deptId">
        <el-select v-model="syncForm.deptId" placeholder="请选择部门" filterable>
          <el-option
            v-for="dept in departments"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="同步内容" prop="syncContent">
        <el-radio-group v-model="syncForm.syncContent">
          <el-radio label="both">组织结构和用户</el-radio>
          <el-radio label="structure">只同步组织结构</el-radio>
          <el-radio label="users">只同步用户</el-radio>
        </el-radio-group>
        <template v-if="syncForm.syncContent === 'both' || syncForm.syncContent === 'structure'">
          <span class="form-item-tip">将根据泛微系统的完整组织结构层级创建对应的OU</span>
        </template>
      </el-form-item>

      <div v-if="syncForm.syncContent === 'users'" class="info-box">
        <el-alert
          type="warning"
          show-icon
          :closable="false"
        >
          <template #title>
            <strong>注意：</strong> 选择"只同步用户"时，所有用户将直接放置在选择的父级OU下
          </template>
          <div>如果希望用户按照其所属部门组织结构放置，请选择"组织结构和用户"选项。</div>
        </el-alert>
      </div>

      <el-form-item label="首次修改密码" v-if="syncForm.syncContent !== 'structure'">
        <el-switch v-model="syncForm.changePasswordNextLogon" />
        <span class="form-item-tip">用户首次登录是否必须修改密码</span>
      </el-form-item>

      <el-form-item label="父级OU">
        <el-input v-model="syncForm.parentOUDN" disabled>
          <template #prepend>
            {{ getOUNameByDN(syncForm.parentOUDN) }}
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSyncFromPersonnel" :loading="syncingPersonnel">
          开始同步
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 同步结果对话框 -->
  <el-dialog v-model="syncResultVisible" title="同步结果" width="700px">
    <div v-if="syncResult">
      <div class="sync-message">{{ syncResult.message }}</div>

      <div v-if="syncResult.stats && syncResult.stats.new_users && syncResult.stats.new_users.length > 0" class="new-users-section">
        <div class="section-title">
          <span>新创建的用户 ({{ syncResult.stats.new_users.length }}个)</span>
          <el-button type="primary" size="small" @click="exportUsers">
            <el-icon class="el-icon--left"><Download /></el-icon> 导出用户
          </el-button>
        </div>

        <el-table :data="paginatedUsers" style="width: 100%" max-height="400">
          <el-table-column prop="username" label="用户名" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="department" label="部门" />
          <el-table-column prop="password" label="密码">
            <template #default="scope">
              <el-popover
                placement="top"
                :width="200"
                trigger="hover"
                content="出于安全考虑，请复制后妥善保存密码信息"
              >
                <template #reference>
                  <div class="password-display">
                    <span>{{ scope.row.password }}</span>
                    <el-button link @click="copyToClipboard(scope.row.password)">复制</el-button>
                  </div>
                </template>
              </el-popover>
            </template>
          </el-table-column>
        </el-table>

        <!-- 添加分页控件 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="syncResult.stats.new_users.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <div v-if="syncResult.stats && syncResult.stats.errors && syncResult.stats.errors.length > 0" class="errors-section">
        <div class="section-title">错误信息</div>
        <el-collapse>
          <el-collapse-item title="查看错误详情">
            <ul class="error-list">
              <li v-for="(error, index) in syncResult.stats.errors" :key="index">{{ error }}</li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </el-dialog>

  <!-- 导出用户对话框 -->
  <el-dialog
    v-model="exportDialogVisible"
    title="导出用户"
    width="500px"
  >
    <el-form :model="exportForm" label-width="100px">
      <el-form-item label="文件格式">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="excel">Excel文件</el-radio>
          <el-radio label="csv">CSV文件</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="导出字段">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="username">用户名</el-checkbox>
          <el-checkbox label="name">显示名称</el-checkbox>
          <el-checkbox label="email">邮箱</el-checkbox>
          <el-checkbox label="department">部门</el-checkbox>
          <el-checkbox label="title">职务</el-checkbox>
          <el-checkbox label="groups">所属组</el-checkbox>
          <el-checkbox label="enabled">状态</el-checkbox>
          <el-checkbox label="password_expiry_date">密码过期日期</el-checkbox>
          <el-checkbox label="password_expiry_status">密码过期状态</el-checkbox>
          <el-checkbox label="days_until_expiry">剩余天数</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="exportDialogVisible = false">取消</el-button>
      <el-button
        type="primary"
        @click="performCustomExport"
        :loading="exportingUsers"
      >
        开始导出
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import OUTree from './components/OUTree.vue'
import UserList from './components/UserList.vue'
import GroupList from './components/GroupList.vue'
import { testGetUsers, testGetGroups, syncFromPersonnel, syncOrganizationStructure as syncOrgStructureAPI } from '@/api/ad'
import { ecologyApi } from '@/api/ecology'
import { Monitor, Connection, Loading, ArrowDown, RefreshRight, Download, Plus, Upload, Search } from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'

// 定义接口
interface Company {
  id: number;
  name: string;
}

interface Department {
  id: number;
  name: string;
  company_id: number;
}

interface ADConnectionInfo {
  bound: boolean;
  server: string;
  username: string;
  search_base: string;
}

interface ADSearchResult {
  users?: Array<{
    username: string;
    name: string;
    email: string;
    enabled: boolean;
  }>;
  groups?: Array<{
    name: string;
    description: string;
    memberCount: number;
  }>;
  total_users?: number;
  total_groups?: number;
}

interface ADTestResult {
  success: boolean;
  message?: string;
  details?: {
    args: string[];
  };
  connection?: ADConnectionInfo;
  search_result?: ADSearchResult;
  raw_result?: string;
}

interface SyncStats {
  new_users: Array<{
    username: string;
    name: string;
    department: string;
    password: string;
  }>;
  errors?: string[];
}

interface SyncResult {
  success: boolean;
  message: string;
  stats?: SyncStats;
}

interface SyncParams {
  parent_ou_dn: string;
  change_password_next_logon: boolean;
  company_id?: number;
  dept_id?: number;
  create_ou: boolean;
  create_security_groups?: boolean;
  add_users_to_dept_groups?: boolean;
}

const userStore = useUserStore()
const activeTab = ref('users')
const selectedOU = ref('')
const isReady = computed(() => userStore.isInitialized && userStore.isLoggedIn)

const testingUsers = ref(false)
const testingGroups = ref(false)
const syncingPersonnel = ref(false)
const exportingUsers = ref(false)

const testResultVisible = ref(false)
const testResult = ref<ADTestResult | null>(null)
const testTitle = ref('')

const ouTreeRef = ref<any>(null)
const userListRef = ref<any>(null)
const groupListRef = ref<any>(null)

const syncDialogVisible = ref(false)
const syncFormRef = ref<any>(null)
const companies = ref<Company[]>([])
const departments = ref<Department[]>([])
const syncForm = ref({
  syncType: 'company',
  companyId: null as number | null,
  deptId: null as number | null,
  changePasswordNextLogon: true,
  parentOUDN: '',
  syncContent: 'both'
})
const syncRules = {
  syncType: [{ required: true, message: '请选择同步范围', trigger: 'change' }],
  companyId: [{
    required: true,
    message: '请选择公司',
    trigger: 'change',
    validator: (_: any, value: any, callback: any) => {
      if (syncForm.value.syncType === 'company' && !value) {
        callback(new Error('请选择公司'))
      } else {
        callback()
      }
    }
  }],
  deptId: [{
    required: true,
    message: '请选择部门',
    trigger: 'change',
    validator: (_: any, value: any, callback: any) => {
      if (syncForm.value.syncType === 'department' && !value) {
        callback(new Error('请选择部门'))
      } else {
        callback()
      }
    }
  }],
  syncContent: [{ required: true, message: '请选择同步内容', trigger: 'change' }]
}

const syncResultVisible = ref(false)
const syncResult = ref<SyncResult | null>(null)

const currentPage = ref(1)
const pageSize = ref(10)
const paginatedUsers = computed(() => {
  if (!syncResult.value || !syncResult.value.stats || !syncResult.value.stats.new_users) {
    return []
  }

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return syncResult.value.stats.new_users.slice(start, end)
})

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

// 根据DN获取OU名称
const getOUNameByDN = (dn: string): string => {
  if (!dn) return ''

  // 直接从DN字符串中提取OU名称
  // DN格式通常为: OU=名称,OU=父级,DC=域名,DC=com
  const parts = dn.split(',')
  if (parts.length > 0 && parts[0].startsWith('OU=')) {
    return parts[0].substring(3) // 去掉'OU='前缀
  }

  return dn // 如果无法解析，返回原始DN
}

const handleOUSelect = (ouDN: string) => {
  console.log('Selected OU:', ouDN)
  selectedOU.value = ouDN
}

const testUsers = async () => {
  if (!selectedOU.value) return
  testingUsers.value = true
  try {
    const res = await testGetUsers(selectedOU.value)
    testResult.value = res.data
    testTitle.value = '用户测试结果'
    testResultVisible.value = true
    if (res.data.success) {
      ElMessage.success('用户测试成功')
    } else {
      throw new Error(res.data.message)
    }
  } catch (error: any) {
    ElMessage.error(`用户测试失败: ${error.message}`)
    testResult.value = {
      success: false,
      message: '测试执行失败',
      details: { args: [error.message] }
    }
    testResultVisible.value = true
  } finally {
    testingUsers.value = false
  }
}

const testGroups = async () => {
  if (!selectedOU.value) return
  testingGroups.value = true
  try {
    const res = await testGetGroups(selectedOU.value)
    testResult.value = res.data
    testTitle.value = '组测试结果'
    testResultVisible.value = true
    if (res.data.success) {
      ElMessage.success('组测试成功')
    } else {
      throw new Error(res.data.message)
    }
  } catch (error: any) {
    ElMessage.error(`组测试失败: ${error.message}`)
    testResult.value = {
      success: false,
      message: '测试执行失败',
      details: { args: [error.message] }
    }
    testResultVisible.value = true
  } finally {
    testingGroups.value = false
  }
}

const showSyncFromPersonnelDialog = async () => {
  syncForm.value.parentOUDN = selectedOU.value

  try {
    const companyRes = await ecologyApi.getCompanies()
    companies.value = companyRes.data

    const deptRes = await ecologyApi.getDepartments()
    departments.value = deptRes.data

    syncDialogVisible.value = true
  } catch (error) {
    console.error('获取公司和部门列表失败', error)
    ElMessage.error('获取公司和部门列表失败')
  }
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success('已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const exportUsers = () => {
  if (!syncResult.value || !syncResult.value.stats || !syncResult.value.stats.new_users) {
    return
  }

  const users = syncResult.value.stats.new_users
  const headers = ['用户名', '姓名', '部门', '密码']
  const csvContent = [
    headers.join(','),
    ...users.map(user => [
      user.username,
      user.name,
      user.department,
      user.password
    ].map(item => `"${item || ''}"`).join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.setAttribute('href', url)
  link.setAttribute('download', `AD用户信息_${new Date().toISOString().split('T')[0]}.csv`)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const handleSyncFromPersonnel = async () => {
  if (!syncFormRef.value) return

  syncFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      syncingPersonnel.value = true
      try {
        const params: SyncParams = {
          parent_ou_dn: syncForm.value.parentOUDN,
          change_password_next_logon: syncForm.value.changePasswordNextLogon,
          create_ou: false
        }

        if (syncForm.value.syncType === 'company') {
          params.company_id = syncForm.value.companyId || undefined
        } else if (syncForm.value.syncType === 'department') {
          params.dept_id = syncForm.value.deptId || undefined
        }

        let res
        if (syncForm.value.syncContent === 'structure') {
          const orgParams = {
            parent_ou_dn: syncForm.value.parentOUDN
          }

          if (syncForm.value.syncType === 'company') {
            (orgParams as any).company_id = syncForm.value.companyId
          } else if (syncForm.value.syncType === 'department') {
            (orgParams as any).dept_id = syncForm.value.deptId
          }

          res = await syncOrgStructureAPI(orgParams)
          syncResult.value = res.data
          syncResultVisible.value = true
          ElMessage.success('组织结构同步成功')
        } else if (syncForm.value.syncContent === 'users') {
          params.create_ou = false
          res = await syncFromPersonnel(params)
          syncResult.value = res.data
          syncResultVisible.value = true
          ElMessage.success('用户同步成功')
        } else {
          params.create_ou = true
          res = await syncFromPersonnel(params)
          syncResult.value = res.data
          syncResultVisible.value = true
          ElMessage.success('同步成功')
        }

        syncDialogVisible.value = false

        if (ouTreeRef.value) {
          try {
            if (typeof ouTreeRef.value.refreshTree === 'function') {
              ouTreeRef.value.refreshTree()
            } else if (typeof ouTreeRef.value.refresh === 'function') {
              ouTreeRef.value.refresh()
            } else if (typeof ouTreeRef.value.fetchOUTree === 'function') {
              ouTreeRef.value.fetchOUTree()
            } else {
              console.log('无法自动刷新OU树，请手动刷新页面查看最新结果')
            }
          } catch (error) {
            console.error('刷新OU树结构失败:', error)
          }
        }
      } catch (error: any) {
        console.error(error)
        ElMessage.error(`同步失败: ${error.message}`)
      } finally {
        syncingPersonnel.value = false
      }
    }
  })
}

const handleAddUser = () => {
  if (userListRef.value) {
    userListRef.value.handleAdd()
  }
}

const handleBulkImport = () => {
  if (userListRef.value) {
    userListRef.value.showBulkImportDialog()
  }
}

const exportDialogVisible = ref(false)
const exportForm = ref({
  format: 'excel',
  fields: ['username', 'name', 'email', 'department', 'title', 'groups', 'enabled', 'password_expiry_status']
})

// 统一处理导出用户功能
const handleExport = () => {
  if (!selectedOU.value) return
  exportDialogVisible.value = true
}

const performCustomExport = async () => {
  if (!selectedOU.value) return

  // 检查字段选择，至少选择一个字段
  if (!exportForm.value.fields.length) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  exportingUsers.value = true
  try {
    if (userListRef.value) {
      // 使用子组件暴露的方法，处理自定义导出
      await userListRef.value.handleCustomExport(exportForm.value)
    }
    exportDialogVisible.value = false
    ElMessage.success('导出成功')
  } catch (error: any) {
    console.error('导出失败:', error)
    ElMessage.error(`导出失败: ${error.message || '未知错误'}`)
  } finally {
    exportingUsers.value = false
  }
}

const searchQuery = ref('')
const groupSearchQuery = ref('')

const handleSearch = () => {
  if (userListRef.value) {
    userListRef.value.handleSearch(searchQuery.value)
  }
}

const handleGroupSearch = () => {
  if (groupListRef.value) {
    groupListRef.value.handleSearch(groupSearchQuery.value)
  }
}

const handleAddGroup = () => {
  if (groupListRef.value) {
    groupListRef.value.handleAddGroup()
  }
}

onMounted(async () => {
  console.log('AD管理页面已加载')
  try {
    await userStore.initializeAuth()
  } catch (error) {
    console.error('初始化认证状态失败:', error)
  }
})
</script>

<style scoped>
.ad-container {
  padding: 20px;
  height: calc(100vh - 100px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.h-full {
  height: 100%;
}

.aside-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.aside-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.aside-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.aside-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.main-container {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.main-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.header-left {
  display: flex;
  align-items: center;
  min-width: 200px;
  flex-shrink: 0;
  gap: 5px; /* 减小元素之间的间距 */
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 20px;
  overflow: hidden;
  min-width: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.current-ou {
  font-weight: normal;
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 0;
}

/* 确保在小屏幕上也能正确显示选项卡 */
@media (max-width: 768px) {
  .main-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-left, .header-center, .header-right {
    width: 100%;
  }

  .header-center {
    justify-content: flex-start;
    margin: 0;
  }

  .header-left {
    flex-wrap: wrap;
    gap: 10px;
  }

  .tab-with-ou {
    flex-wrap: wrap;
    gap: 5px;
  }

  .button-group-container {
    flex-direction: column;
    height: auto;
    width: 100%;
  }

  .current-ou {
    margin-top: 5px;
    margin-left: 0;
    border-radius: 4px;
    width: fit-content;
  }

  :deep(.el-radio-button:last-child .el-radio-button__inner) {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-placeholder {
  padding: 20px;
}

.tab-with-ou {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.view-switcher {
  height: 32px;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  min-width: 200px;
  width: auto;
  margin-right: 0;
}

/* 添加深度选择器以确保能影响到组件内部 */
:deep(.el-radio-group) {
  display: inline-flex;
  flex-direction: row;
  white-space: nowrap;
}

:deep(.el-radio-button) {
  flex-shrink: 0;
}

.view-switcher .el-radio-button__inner {
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  display: inline-block;
  white-space: nowrap;
}

.el-divider--vertical {
  height: 24px;
  margin: 0 8px;
}

.test-result-content {
  padding: 20px;
}

.result-section h3 {
  margin: 16px 0;
  color: #303133;
  font-size: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-2 {
  margin-bottom: 8px;
}

.error-section {
  margin-top: 20px;
}

:deep(.el-descriptions) {
  margin: 16px 0;
}

:deep(.el-drawer__body) {
  padding: 0;
}

.table-container {
  position: relative;
  min-height: 200px;
  padding: 0 16px;
}

.toolbar {
  padding: 12px 16px;
}

.pagination-container {
  padding: 12px 16px;
}

.menu-button {
  display: inline-flex;
  align-items: center;
  height: 32px;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 14px;
  color: var(--el-button-text-color);
  border-color: var(--el-button-border-color);
  transition: all 0.3s;
}

.menu-button:hover {
  background-color: var(--el-color-primary) !important;
  color: white !important;
  border-color: var(--el-color-primary) !important;
}

.menu-button:hover .menu-icon,
.menu-button:hover .el-icon--right {
  color: white !important;
}

.menu-icon {
  margin-right: 5px;
  font-size: 16px;
}

.menu-button .el-icon--right {
  margin-left: 5px;
  margin-right: 0;
  font-size: 12px;
}

.form-item-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  white-space: nowrap;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.sync-message {
  font-size: 16px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin: 15px 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.new-users-section {
  margin-top: 20px;
}

.password-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.errors-section {
  margin-top: 20px;
}

.error-list {
  padding-left: 20px;
  color: #f56c6c;
}

.pagination-wrapper {
  margin-top: 15px;
}

.info-box {
  margin-top: 10px;
  margin-bottom: 10px;
}

.search-input {
  width: 250px;
  margin-right: 10px;
}
</style>