"""
测试部门有效性检查功能
"""

import pytest
from unittest.mock import Mock, patch
from app.services.department_structure_sync import DepartmentStructureSyncService
from app.schemas.department_sync import DepartmentSyncRequest, DepartmentInfo, DepartmentSyncSource


class TestDepartmentValidityCheck:
    """部门有效性检查测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.mock_db = Mock()
        self.sync_service = DepartmentStructureSyncService(self.mock_db)
    
    def test_filter_valid_departments_with_active_users(self):
        """测试过滤有在职人员的部门"""
        # 准备测试数据
        departments = [
            DepartmentInfo(
                dept_id=1,
                dept_name="技术部",
                level=1,
                company_id=1
            ),
            DepartmentInfo(
                dept_id=2,
                dept_name="人事部",
                level=1,
                company_id=1
            )
        ]
        
        # 部门在职人员统计（技术部有3个在职人员，人事部没有）
        dept_active_user_count = {
            "技术部#1#1": 3,
            "人事部#1#1": 0
        }
        
        request = DepartmentSyncRequest(skip_empty_departments=True)
        
        # 执行过滤
        valid_departments, skipped_departments = self.sync_service._filter_valid_departments(
            departments, dept_active_user_count, request
        )
        
        # 验证结果
        assert len(valid_departments) == 1
        assert valid_departments[0].dept_name == "技术部"
        assert len(skipped_departments) == 1
        assert skipped_departments[0]['dept_name'] == "人事部"
        assert "无在职人员" in skipped_departments[0]['reason']
    
    def test_filter_deprecated_departments(self):
        """测试过滤已弃用的部门"""
        # 准备测试数据
        departments = [
            DepartmentInfo(
                dept_id=1,
                dept_name="技术部",
                level=1,
                company_id=1
            ),
            DepartmentInfo(
                dept_id=2,
                dept_name="已弃用部门",
                level=1,
                company_id=1
            ),
            DepartmentInfo(
                dept_id=3,
                dept_name="停用的部门",
                level=1,
                company_id=1
            )
        ]
        
        # 所有部门都有在职人员
        dept_active_user_count = {
            "技术部#1#1": 3,
            "已弃用部门#1#1": 2,
            "停用的部门#1#1": 1
        }
        
        request = DepartmentSyncRequest(skip_empty_departments=True)
        
        # 执行过滤
        valid_departments, skipped_departments = self.sync_service._filter_valid_departments(
            departments, dept_active_user_count, request
        )
        
        # 验证结果
        assert len(valid_departments) == 1
        assert valid_departments[0].dept_name == "技术部"
        assert len(skipped_departments) == 2
        
        # 检查跳过的部门原因
        skipped_names = [dept['dept_name'] for dept in skipped_departments]
        assert "已弃用部门" in skipped_names
        assert "停用的部门" in skipped_names
        
        for skipped in skipped_departments:
            assert "弃用标识" in skipped['reason']
    
    def test_recursive_active_user_check(self):
        """测试递归检查子部门的在职人员"""
        # 准备测试数据 - 父部门没有直接人员，但子部门有
        departments = [
            DepartmentInfo(
                dept_id=1,
                dept_name="总部",
                level=1,
                company_id=1
            ),
            DepartmentInfo(
                dept_id=2,
                dept_name="技术部",
                level=2,
                company_id=1,
                parent_dept_id=1
            )
        ]
        
        # 只有子部门有在职人员
        dept_active_user_count = {
            "总部#1#1": 0,
            "技术部#2#1": 5
        }
        
        request = DepartmentSyncRequest(skip_empty_departments=True)
        
        # 执行过滤
        valid_departments, skipped_departments = self.sync_service._filter_valid_departments(
            departments, dept_active_user_count, request
        )
        
        # 验证结果 - 父部门应该被保留，因为子部门有在职人员
        assert len(valid_departments) == 2
        assert len(skipped_departments) == 0
    
    def test_skip_empty_departments_disabled(self):
        """测试禁用部门有效性检查时的行为"""
        departments = [
            DepartmentInfo(
                dept_id=1,
                dept_name="空部门",
                level=1,
                company_id=1
            )
        ]
        
        dept_active_user_count = {
            "空部门#1#1": 0
        }
        
        request = DepartmentSyncRequest(skip_empty_departments=False)
        
        # 执行过滤
        valid_departments, skipped_departments = self.sync_service._filter_valid_departments(
            departments, dept_active_user_count, request
        )
        
        # 验证结果 - 应该返回所有部门
        assert len(valid_departments) == 1
        assert len(skipped_departments) == 0


if __name__ == "__main__":
    pytest.main([__file__])
