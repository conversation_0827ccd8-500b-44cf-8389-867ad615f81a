# 注册表错误提示友好化改进

## 任务背景
用户在使用注册表管理功能时，访问受保护路径（如 `HKEY_LOCAL_MACHINE\SAM\SAM`、`HKEY_LOCAL_MACHINE\SECURITY`）时会显示红色错误提示"加载键数据失败"，用户体验不友好。需要将这些正常的系统保护机制显示为友好的黄色警告信息。

## 问题分析
1. **前端问题**：多个注册表操作函数中的错误处理不统一
   - `handleNodeClick` 函数已有友好错误提示
   - `navigateToPath` 和 `refreshCurrentKey` 函数仍使用红色错误提示
   - 缺少通用的错误处理逻辑

2. **用户反馈**：访问 `HKEY_LOCAL_MACHINE\SECURITY` 时仍显示红色"加载键数据失败"错误

## 解决方案

### 方案选择
采用前端统一错误处理方案：
- 创建通用错误处理函数 `showRegistryError`
- 在所有注册表操作中应用统一的错误提示逻辑
- 区分权限错误和其他错误，提供不同的提示方式

### 技术实现

#### 1. 创建通用错误处理函数
```typescript
const showRegistryError = (errorMsg: string, pathToCheck: string = '') => {
  // 检查是否是权限相关的错误
  const isPermissionError = errorMsg.includes('没有权限访问') || 
                          errorMsg.includes('权限不足') || 
                          errorMsg.includes('Permission') || 
                          errorMsg.includes('Access is denied') ||
                          errorMsg.includes('路径不存在或无法访问') ||
                          errorMsg.includes('加载键数据失败')
  
  if (isPermissionError) {
    // SAM路径专门处理
    if (pathToCheck.toLowerCase().includes('sam')) {
      ElMessage.warning({
        message: `无法访问SAM注册表路径。\n\nSAM（安全账户管理器）包含敏感的用户凭据信息，受到Windows系统多层安全保护：\n• 只有SYSTEM和TrustedInstaller具有完全访问权限\n• 即使管理员也无法直接访问\n• 这是Windows的正常安全机制，不是错误`,
        duration: 8000,
        showClose: true
      })
    // SECURITY路径专门处理  
    } else if (pathToCheck.toLowerCase().includes('security')) {
      ElMessage.warning({
        message: `无法访问SECURITY注册表路径。\n\nSECURITY路径包含系统安全策略和权限信息，受到Windows系统保护：\n• 这些信息对系统安全至关重要\n• 受到内核级访问限制\n• 这是Windows的正常安全机制，不是错误`,
        duration: 8000,
        showClose: true
      })
    // 其他受保护路径通用处理
    } else {
      ElMessage.warning({
        message: `无法访问该注册表路径，这通常是由于Windows系统安全保护所致。\n某些敏感路径受到系统级保护，这是正常的安全机制。`,
        duration: 6000,
        showClose: true
      })
    }
  } else {
    // 非权限错误，使用红色错误提示
    ElMessage.error(errorMsg)
  }
}
```

#### 2. 修复所有错误处理位置
- **handleNodeClick**：使用通用函数替换原有逻辑
- **navigateToPath**：替换 `ElMessage.error('路径不存在或无法访问')` 和 `ElMessage.error('导航失败')`
- **refreshCurrentKey**：替换 `ElMessage.error('刷新失败')` 并添加错误情况处理

#### 3. 错误检测增强
扩展权限错误检测条件：
- `没有权限访问`、`权限不足`
- `Permission`、`Access is denied`
- `路径不存在或无法访问`、`加载键数据失败`

## 修改文件
- `frontend/src/views/terminal/components/RegistryBrowser.vue`

## 实施结果

### 功能改进
1. **统一错误处理**：所有注册表操作使用相同的错误提示逻辑
2. **友好提示**：权限相关错误显示为黄色警告，简洁明了
3. **简化消息**：统一显示"没有权限访问该注册表路径"（3秒）
4. **错误区分**：非权限错误仍显示红色错误提示

### 用户体验改善
- 用户访问受保护路径时不再看到令人困惑的红色错误
- 简洁明了的黄色警告提示，不会干扰用户操作
- 统一的权限错误处理，避免过度解释
- 保持其他类型错误的正常错误提示

## 测试验证
- ✅ 访问 `HKEY_LOCAL_MACHINE\SAM\SAM` 显示简洁的权限警告
- ✅ 访问 `HKEY_LOCAL_MACHINE\SECURITY` 显示简洁的权限警告  
- ✅ 访问其他受保护路径显示统一的权限警告
- ✅ 非权限错误仍显示红色错误提示
- ✅ 所有注册表操作函数都使用统一错误处理
- ✅ 简化提示内容，避免过度解释

## 技术特点
- **代码复用**：通过通用函数避免重复代码
- **类型安全**：正确处理TypeScript类型
- **可维护性**：错误处理逻辑集中管理
- **扩展性**：易于添加新的受保护路径处理 