from app.database import Base
from app.models.user import User
from app.models.asset import Asset, AssetChangeLog
from app.models.asset_types import AssetType, AssetStatus, Asset<PERSON><PERSON>, AssetModel
from app.models.inventory import InventoryTask, InventoryRecord
from app.models.field_value import FieldValue
from app.models.ad import OrganizationalUnit, ADUser
from app.models.ad_config import ADConfig, ADSyncConfig
from app.models.ecology_user import EcologyUser, SyncConfig
from app.models.init_lock import InitializationLock
from .ad_sync_log import ADSyncLog
from app.models.permission import Permission
from app.models.role import Role, role_permission, user_role
from app.models.terminal import (
    Terminal, HardwareInfo, DiskInfo, OSInfo, SecurityInfo,
    Software, NetworkInfo, NetworkInterface, UserLoginInfo, TerminalCommand, AgentVersion
)
from app.models.email import (
    EmailDepartment, EmailMember, EmailTag, EmailGroup, EmailConfig, EmailSyncLog, PersonnelSyncConfig
)
from app.models.email_sync_lock import EmailSyncLock
from app.models.sync_lock import ADSyncLock
from app.models.asset_settings import AssetSettings
from .ldap_config import LdapConfig
from .registry_operation import RegistryOperation, RegistryBackup, RegistrySearchLog
from .command_whitelist import CommandCategory, CommandWhitelist
from .custom_field import CustomField, AssetCustomFieldValue, InventoryRecordCustomFieldValue

__all__ = [
    'Base',
    'User',
    'Asset',
    'AssetChangeLog',
    'AssetType',
    'AssetStatus',
    'AssetBrand',
    'AssetModel',
    'InventoryTask',
    'InventoryRecord',
    'FieldValue',
    'OrganizationalUnit',
    'ADUser',
    'ADConfig',
    'ADSyncConfig',
    'EcologyUser',
    'SyncConfig',
    'InitializationLock',
    'ADSyncLog',
    'Permission',
    'Role',
    # 终端管理相关模型
    'Terminal',
    'HardwareInfo',
    'DiskInfo',
    'OSInfo',
    'SecurityInfo',
    'Software',
    'NetworkInfo',
    'NetworkInterface',
    'UserLoginInfo',
    'TerminalCommand',
    'AgentVersion',
    # 邮箱管理相关模型
    'EmailDepartment',
    'EmailMember',
    'EmailTag',
    'EmailGroup',
    'EmailConfig',
    'EmailSyncLog',
    'PersonnelSyncConfig',
    'EmailSyncLock',
    'ADSyncLock',
    'AssetSettings',
    'LdapConfig',
    # 注册表管理相关模型
    'RegistryOperation',
    'RegistryBackup',
    'RegistrySearchLog',
    # 命令白名单相关模型
    'CommandCategory',
    'CommandWhitelist',
    # 自定义字段相关模型
    'CustomField',
    'AssetCustomFieldValue',
    'InventoryRecordCustomFieldValue'
] 