<template>
  <div class="ldap-config-management">
    <div class="table-header">
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          新增LDAP配置
        </el-button>
      </div>
    </div>

    <el-table :data="configs" v-loading="loading" stripe>
      <el-table-column prop="name" label="配置名称" min-width="120">
        <template #default="{ row }">
          <div class="config-name">
            {{ row.name }}
            <el-tag v-if="row.is_default" size="small" type="success">默认</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="server" label="服务器地址" min-width="150" />
      <el-table-column prop="port" label="端口" width="80" />
      <el-table-column prop="use_ssl" label="SSL" width="80">
        <template #default="{ row }">
          <el-tag :type="row.use_ssl ? 'success' : 'info'" size="small">
            {{ row.use_ssl ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="base_dn" label="Base DN" min-width="200" />
      <el-table-column prop="priority" label="优先级" width="80" align="center">
        <template #default="{ row }">
          <el-tag type="primary" size="small">{{ row.priority }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auto_select_enabled" label="智能选择" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.auto_select_enabled ? 'success' : 'info'" size="small">
            {{ row.auto_select_enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
            {{ row.is_active ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="success" size="small" @click="handleTestConnection(row)">
            测试
          </el-button>
          <el-button 
            v-if="!row.is_default" 
            type="warning" 
            size="small" 
            @click="handleSetDefault(row)"
          >
            设为默认
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleDelete(row)"
            :disabled="row.is_default && configs.length === 1"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑LDAP配置' : '新增LDAP配置'"
      width="600px"
      :close-on-click-modal="false"
      class="ldap-config-dialog"
    >
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="150px"
        class="ldap-form"
      >
        <!-- 使用折叠面板替换卡片布局 -->
        <el-collapse v-model="activeCollapse" class="ldap-config-collapse">
          <!-- 基本信息 -->
          <el-collapse-item title="基本信息" name="basic">
            <template #title>
              <div class="collapse-title">
                <el-icon><Setting /></el-icon>
                <span>基本信息</span>
              </div>
            </template>
            
            <el-form-item label="配置名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入配置名称" />
            </el-form-item>

            <el-form-item label="服务器地址" prop="server">
              <el-input v-model="form.server" placeholder="ldap://example.com" />
            </el-form-item>

            <el-form-item label="端口" prop="port">
              <el-input 
                v-model.number="form.port" 
                type="number" 
                placeholder="1-65535"
              />
            </el-form-item>

            <el-form-item label="使用SSL">
              <el-switch v-model="form.use_ssl" />
            </el-form-item>

            <el-form-item label="启用配置">
              <el-switch v-model="form.is_active" />
            </el-form-item>
          </el-collapse-item>

          <!-- 连接配置 -->
          <el-collapse-item title="连接配置" name="connection">
            <template #title>
              <div class="collapse-title">
                <el-icon><Link /></el-icon>
                <span>连接配置</span>
              </div>
            </template>
            
            <el-form-item label="Base DN" prop="base_dn">
              <el-input v-model="form.base_dn" placeholder="DC=example,DC=com" />
            </el-form-item>

            <el-form-item label="绑定用户DN">
              <el-input v-model="form.bind_dn" placeholder="CN=admin,DC=example,DC=com" />
            </el-form-item>

            <el-form-item label="绑定密码">
              <el-input 
                v-model="form.bind_password" 
                type="password" 
                placeholder="请输入绑定用户密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="用户搜索Base DN">
              <el-input v-model="form.user_search_base" placeholder="OU=Users,DC=example,DC=com" />
            </el-form-item>
          </el-collapse-item>

          <!-- 用户映射 -->
          <el-collapse-item title="用户映射" name="mapping">
            <template #title>
              <div class="collapse-title">
                <el-icon><User /></el-icon>
                <span>用户映射</span>
              </div>
            </template>
            
            <el-form-item label="用户搜索过滤器" prop="user_search_filter">
              <el-input 
                v-model="form.user_search_filter" 
                placeholder="(sAMAccountName={username})"
              />
              <div class="field-help">
                用于搜索用户的LDAP过滤器，{username}会被替换为实际用户名
              </div>
            </el-form-item>

            <el-form-item label="用户名属性">
              <el-input 
                v-model="form.user_name_attr" 
                placeholder="sAMAccountName"
              />
              <div class="field-help">用户名对应的LDAP属性</div>
            </el-form-item>

            <el-form-item label="邮箱属性">
              <el-input 
                v-model="form.user_email_attr" 
                placeholder="mail"
              />
              <div class="field-help">邮箱对应的LDAP属性</div>
            </el-form-item>

            <el-form-item label="显示名称属性">
              <el-input 
                v-model="form.user_display_name_attr" 
                placeholder="displayName"
              />
              <div class="field-help">显示名称对应的LDAP属性</div>
            </el-form-item>
          </el-collapse-item>

          <!-- 高级选项 -->
          <el-collapse-item title="高级选项" name="advanced">
            <template #title>
              <div class="collapse-title">
                <el-icon><Tools /></el-icon>
                <span>高级选项</span>
              </div>
            </template>
            
            <el-form-item label="自动创建用户">
              <div class="form-control-group">
                <el-switch v-model="form.auto_create_user" />
                <div class="field-help">LDAP认证成功后是否自动在系统中创建用户</div>
              </div>
            </el-form-item>

            <el-form-item label="默认角色">
              <div class="form-control-group">
                <el-select v-model="form.default_role" placeholder="选择默认角色">
                  <el-option label="普通用户" value="normal_user" />
                  <el-option label="资产管理员" value="asset_admin" />
                  <el-option label="超级管理员" value="super_admin" />
                </el-select>
                <div class="field-help">自动创建用户时分配的默认角色</div>
              </div>
            </el-form-item>

            <el-form-item label="设为默认配置">
              <div class="form-control-group">
                <el-switch v-model="form.is_default" />
                <div class="field-help">是否将此配置设为默认LDAP配置</div>
              </div>
            </el-form-item>

            <el-form-item label="优先级" prop="priority">
              <div class="form-control-group">
                <el-input-number 
                  v-model="form.priority" 
                  :min="1" 
                  :max="999" 
                  placeholder="优先级"
                  style="width: 100%"
                />
                <div class="field-help">数字越小优先级越高，用于智能选择时的排序</div>
              </div>
            </el-form-item>

            <el-form-item label="启用智能选择">
              <div class="form-control-group">
                <el-switch v-model="form.auto_select_enabled" />
                <div class="field-help">是否基于客户端IP自动选择此配置</div>
              </div>
            </el-form-item>

            <el-form-item label="IP网段配置" v-show="form.auto_select_enabled">
              <div class="form-control-group">
                <el-select
                  v-model="form.ip_ranges"
                  multiple
                  filterable
                  allow-create
                  default-first-option
                  :reserve-keyword="false"
                  placeholder="请输入IP网段（支持CIDR格式和IP范围）"
                  style="width: 100%"
                >
                  <template #empty>
                    <div class="ip-range-help">
                      <p>支持的格式：</p>
                      <ul>
                        <li>CIDR格式：***********/24</li>
                        <li>IP范围：***********-*************</li>
                        <li>单个IP：***********</li>
                      </ul>
                    </div>
                  </template>
                </el-select>
                <div class="field-help">
                  当客户端IP匹配这些网段时，会自动选择此配置进行LDAP认证
                  <br>留空表示不限制IP范围
                </div>
              </div>
            </el-form-item>

            <el-form-item label="配置描述">
              <el-input 
                v-model="form.description" 
                type="textarea" 
                :rows="3"
                placeholder="请输入配置描述"
              />
            </el-form-item>
          </el-collapse-item>

          <!-- 用户登录测试 -->
          <el-collapse-item title="用户登录测试" name="test">
            <template #title>
              <div class="collapse-title">
                <el-icon><Monitor /></el-icon>
                <span>用户登录测试</span>
              </div>
            </template>
            
            <el-form-item label="测试用户名">
              <el-input 
                v-model="loginTestForm.username" 
                placeholder="请输入测试用户名"
                clearable
              />
            </el-form-item>

            <el-form-item label="测试密码">
              <el-input 
                v-model="loginTestForm.password" 
                type="password" 
                placeholder="请输入测试密码"
                show-password
                clearable
              />
            </el-form-item>

            <el-form-item>
              <div class="test-buttons">
                <el-button 
                  type="success" 
                  @click="handleTestUserLogin" 
                  :loading="userLoginTestLoading"
                  :disabled="!loginTestForm.username || !loginTestForm.password"
                >
                  测试用户登录
                </el-button>
                <el-button 
                  type="info" 
                  @click="handleDiagnoseUserSearch" 
                  :loading="diagnosisLoading"
                  :disabled="!loginTestForm.username"
                >
                  诊断用户搜索
                </el-button>
              </div>
            </el-form-item>

            <!-- 测试结果展示 -->
            <div v-if="loginTestResult" class="test-result">
              <el-alert
                :type="loginTestResult.success ? 'success' : 'error'"
                :title="loginTestResult.message"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div v-if="loginTestResult.success && loginTestResult.user_info">
                    <p><strong>认证成功！用户信息：</strong></p>
                    <ul class="user-info-list">
                      <li v-if="loginTestResult.user_info.username">
                        <strong>用户名：</strong>{{ loginTestResult.user_info.username }}
                      </li>
                      <li v-if="loginTestResult.user_info.email">
                        <strong>邮箱：</strong>{{ loginTestResult.user_info.email }}
                      </li>
                      <li v-if="loginTestResult.user_info.display_name">
                        <strong>显示名称：</strong>{{ loginTestResult.user_info.display_name }}
                      </li>
                      <li v-if="loginTestResult.user_info.dn">
                        <strong>Distinguished Name：</strong>{{ loginTestResult.user_info.dn }}
                      </li>
                    </ul>
                  </div>
                  <div v-else-if="!loginTestResult.success">
                    <p><strong>错误类型：</strong>{{ getErrorTypeText(loginTestResult.error_type) }}</p>
                    <p v-if="loginTestResult.error_type === 'connection_error'">
                      <strong>建议：</strong>请先检查服务器连接配置
                    </p>
                    <p v-else-if="loginTestResult.error_type === 'auth_failed'">
                      <strong>建议：</strong>请检查用户名、密码或用户搜索配置
                    </p>
                  </div>
                </template>
              </el-alert>
            </div>

            <!-- 诊断结果展示 -->
            <div v-if="diagnosisResult" class="diagnosis-result">
              <el-alert
                :type="diagnosisResult.success ? 'success' : 'error'"
                title="用户搜索诊断结果"
                :closable="false"
                show-icon
              >
                <template #default>
                  <div v-if="diagnosisResult.success && diagnosisResult.diagnosis">
                    <div class="diagnosis-section">
                      <h4>搜索参数</h4>
                      <ul>
                        <li><strong>连接类型：</strong>{{ diagnosisResult.diagnosis.connection_type }}</li>
                        <li><strong>搜索基础DN：</strong>{{ diagnosisResult.diagnosis.search_parameters.actual_search_base }}</li>
                        <li><strong>搜索过滤器：</strong>{{ diagnosisResult.diagnosis.search_parameters.actual_search_filter }}</li>
                      </ul>
                    </div>

                    <div class="diagnosis-section">
                      <h4>诊断测试</h4>
                      <div v-for="(test, index) in diagnosisResult.diagnosis.tests" :key="index" class="test-item">
                        <el-tag :type="test.success ? 'success' : 'danger'" class="test-tag">
                          {{ test.name }}
                        </el-tag>
                        <p class="test-details">{{ test.details }}</p>
                        
                        <!-- 显示找到的用户 -->
                        <div v-if="test.found_users && test.found_users.length > 0" class="found-users">
                          <p><strong>找到的用户：</strong></p>
                          <ul>
                            <li v-for="user in test.found_users" :key="user.dn">
                              <strong>{{ user.sAMAccountName }}</strong> ({{ user.displayName }})
                              <br><small>{{ user.dn }}</small>
                            </li>
                          </ul>
                        </div>
                        
                        <!-- 显示示例用户 -->
                        <div v-if="test.sample_users && test.sample_users.length > 0" class="sample-users">
                          <p><strong>示例用户：</strong></p>
                          <ul>
                            <li v-for="user in test.sample_users.slice(0, 5)" :key="user.sAMAccountName">
                              {{ user.sAMAccountName }} ({{ user.displayName }})
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div v-if="diagnosisResult.diagnosis.suggestions && diagnosisResult.diagnosis.suggestions.length > 0" class="diagnosis-section">
                      <h4>建议</h4>
                      <ul class="suggestions-list">
                        <li v-for="suggestion in diagnosisResult.diagnosis.suggestions" :key="suggestion">
                          {{ suggestion }}
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div v-else>
                    <p>{{ diagnosisResult.message }}</p>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleTestFormConnection" :loading="testLoading">
            测试连接
          </el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Link, User, Tools, Monitor } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import request from '@/utils/request'

interface LdapConfig {
  id?: number
  name: string
  server: string
  port: number
  use_ssl: boolean
  base_dn: string
  bind_dn?: string
  bind_password?: string
  user_search_base?: string
  user_search_filter: string
  user_name_attr: string
  user_email_attr: string
  user_display_name_attr: string
  auto_create_user: boolean
  default_role: string
  is_active: boolean
  is_default: boolean
  description?: string
  ip_ranges?: string[]
  priority: number
  auto_select_enabled: boolean
  created_at?: string
  updated_at?: string
}

const configs = ref<LdapConfig[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const testLoading = ref(false)
const userLoginTestLoading = ref(false)
const diagnosisLoading = ref(false)
const formRef = ref<FormInstance>()
const activeCollapse = ref<string[]>(['basic', 'connection'])

// 用户登录测试相关
const loginTestForm = ref({
  username: '',
  password: ''
})

const loginTestResult = ref<{
  success: boolean
  message: string
  user_info?: {
    username?: string
    email?: string
    display_name?: string
    dn?: string
  }
  error_type?: string
} | null>(null)

const diagnosisResult = ref<{
  success: boolean
  message: string
  diagnosis?: {
    connection_type: string
    search_parameters: {
      actual_search_base: string
      actual_search_filter: string
    }
    tests: {
      name: string
      success: boolean
      details: string
      found_users?: {
        sAMAccountName: string
        displayName: string
        dn: string
      }[]
      sample_users?: {
        sAMAccountName: string
        displayName: string
      }[]
    }[]
    suggestions: string[]
  } | null
} | null>(null)

const form = ref<LdapConfig>({
  name: '',
  server: '',
  port: 389,
  use_ssl: false,
  base_dn: '',
  bind_dn: '',
  bind_password: '',
  user_search_base: '',
  user_search_filter: '(sAMAccountName={username})',
  user_name_attr: 'sAMAccountName',
  user_email_attr: 'mail',
  user_display_name_attr: 'displayName',
  auto_create_user: true,
  default_role: 'normal_user',
  is_active: true,
  is_default: false,
  description: '',
  ip_ranges: [],
  priority: 1,
  auto_select_enabled: true
})

const rules: FormRules = {
  name: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  server: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { 
      type: 'number', 
      min: 1, 
      max: 65535, 
      message: '端口号必须在1-65535之间', 
      trigger: 'blur' 
    }
  ],
  base_dn: [{ required: true, message: '请输入Base DN', trigger: 'blur' }],
  user_search_filter: [
    { required: true, message: '请输入用户搜索过滤器', trigger: 'blur' },
    { 
      validator: (rule: any, value: string, callback: Function) => {
        if (value && !value.includes('{username}')) {
          callback(new Error('用户搜索过滤器必须包含{username}占位符'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  priority: [
    { required: true, message: '请输入优先级', trigger: 'blur' },
    { 
      type: 'number', 
      min: 1, 
      message: '优先级必须大于0', 
      trigger: 'blur' 
    }
  ]
}

// 获取配置列表
const loadConfigs = async () => {
  try {
    loading.value = true
    const response = await request.get('/system/ldap-config/')
    configs.value = response.data
  } catch (error: any) {
    ElMessage.error('获取LDAP配置失败：' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    name: '',
    server: '',
    port: 389,
    use_ssl: false,
    base_dn: '',
    bind_dn: '',
    bind_password: '',
    user_search_base: '',
    user_search_filter: '(sAMAccountName={username})',
    user_name_attr: 'sAMAccountName',
    user_email_attr: 'mail',
    user_display_name_attr: 'displayName',
    auto_create_user: true,
    default_role: 'normal_user',
    is_active: true,
    is_default: false,
    description: '',
    ip_ranges: [],
    priority: 1,
    auto_select_enabled: true
  }
  // 重置用户登录测试表单
  loginTestForm.value = {
    username: '',
    password: ''
  }
  loginTestResult.value = null
  diagnosisResult.value = null
  // 重置折叠面板状态，默认展开基本信息和连接配置
  activeCollapse.value = ['basic', 'connection']
  formRef.value?.resetFields()
}

// 新增配置
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑配置
const handleEdit = (config: LdapConfig) => {
  isEdit.value = true
  form.value = { ...config }
  dialogVisible.value = true
}

// 删除配置
const handleDelete = async (config: LdapConfig) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置"${config.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await request.delete(`/system/ldap-config/${config.id}`)
    ElMessage.success('删除成功')
    await loadConfigs()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.response?.data?.detail || error.message))
    }
  }
}

// 设置默认配置
const handleSetDefault = async (config: LdapConfig) => {
  try {
    await request.post(`/system/ldap-config/${config.id}/set-default`)
    ElMessage.success('设置默认配置成功')
    await loadConfigs()
  } catch (error: any) {
    ElMessage.error('设置失败：' + (error.response?.data?.detail || error.message))
  }
}

// 测试连接
const handleTestConnection = async (config: LdapConfig) => {
  try {
    const response = await request.post(`/system/ldap-config/${config.id}/test-connection`)
    if (response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败')
    }
  } catch (error: any) {
    ElMessage.error('连接测试失败：' + (error.response?.data?.detail || error.message))
  }
}

// 测试表单中的连接
const handleTestFormConnection = async () => {
  if (!formRef.value) return
  
  const requiredFields = ['name', 'server', 'port', 'base_dn']
  const validationErrors: string[] = []
  
  for (const field of requiredFields) {
    if (!form.value[field as keyof LdapConfig]) {
      validationErrors.push(field)
    }
  }
  
  if (validationErrors.length > 0) {
    ElMessage.warning('请先填写必填字段')
    return
  }
  
  try {
    testLoading.value = true
    const response = await request.post('/system/ldap-config/test-connection', {
      server: form.value.server,
      port: form.value.port,
      use_ssl: form.value.use_ssl,
      base_dn: form.value.base_dn,
      bind_dn: form.value.bind_dn,
      bind_password: form.value.bind_password
    })
    
    if (response.data.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error('连接测试失败')
    }
  } catch (error: any) {
    ElMessage.error('连接测试失败：' + (error.response?.data?.detail || error.message))
  } finally {
    testLoading.value = false
  }
}

// 测试用户登录
const handleTestUserLogin = async () => {
  const requiredFields = ['name', 'server', 'port', 'base_dn']
  const validationErrors: string[] = []
  
  for (const field of requiredFields) {
    if (!form.value[field as keyof LdapConfig]) {
      validationErrors.push(field)
    }
  }
  
  if (validationErrors.length > 0) {
    ElMessage.warning('请先填写必填的配置字段')
    return
  }
  
  if (!loginTestForm.value.username || !loginTestForm.value.password) {
    ElMessage.warning('请输入测试用户名和密码')
    return
  }
  
  try {
    userLoginTestLoading.value = true
    loginTestResult.value = null
    
    const response = await request.post('/system/ldap-config/test-user-login', {
      // 配置信息
      server: form.value.server,
      port: form.value.port,
      use_ssl: form.value.use_ssl,
      base_dn: form.value.base_dn,
      bind_dn: form.value.bind_dn,
      bind_password: form.value.bind_password,
      user_search_base: form.value.user_search_base,
      user_search_filter: form.value.user_search_filter,
      user_name_attr: form.value.user_name_attr,
      user_email_attr: form.value.user_email_attr,
      user_display_name_attr: form.value.user_display_name_attr,
      // 测试凭据
      username: loginTestForm.value.username,
      password: loginTestForm.value.password
    })
    
    loginTestResult.value = response.data
    
    if (response.data.success) {
      ElMessage.success('用户登录测试成功')
    } else {
      ElMessage.error('用户登录测试失败')
    }
  } catch (error: any) {
    ElMessage.error('测试失败：' + (error.response?.data?.detail || error.message))
    loginTestResult.value = {
      success: false,
      message: '测试过程中发生错误',
      error_type: 'test_error'
    }
  } finally {
    userLoginTestLoading.value = false
  }
}

// 获取错误类型文本
const getErrorTypeText = (errorType?: string) => {
  switch (errorType) {
    case 'connection_error':
      return '连接错误'
    case 'auth_failed':
      return '认证失败'
    case 'test_error':
      return '测试错误'
    default:
      return '未知错误'
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        submitLoading.value = true
        
        if (isEdit.value) {
          await request.put(`/system/ldap-config/${form.value.id}`, form.value)
          ElMessage.success('更新成功')
        } else {
          await request.post('/system/ldap-config/', form.value)
          ElMessage.success('创建成功')
        }
        
        dialogVisible.value = false
        await loadConfigs()
      } catch (error: any) {
        ElMessage.error(
          (isEdit.value ? '更新' : '创建') + '失败：' + 
          (error.response?.data?.detail || error.message)
        )
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 诊断用户搜索
const handleDiagnoseUserSearch = async () => {
  const requiredFields = ['name', 'server', 'port', 'base_dn']
  const validationErrors: string[] = []
  
  for (const field of requiredFields) {
    if (!form.value[field as keyof LdapConfig]) {
      validationErrors.push(field)
    }
  }
  
  if (validationErrors.length > 0) {
    ElMessage.warning('请先填写必填的配置字段')
    return
  }
  
  if (!loginTestForm.value.username) {
    ElMessage.warning('请先输入测试用户名')
    return
  }
  
  try {
    diagnosisLoading.value = true
    diagnosisResult.value = null
    
    const response = await request.post('/system/ldap-config/diagnose-user-search', {
      // 配置信息
      server: form.value.server,
      port: form.value.port,
      use_ssl: form.value.use_ssl,
      base_dn: form.value.base_dn,
      bind_dn: form.value.bind_dn,
      bind_password: form.value.bind_password,
      user_search_base: form.value.user_search_base,
      user_search_filter: form.value.user_search_filter,
      user_name_attr: form.value.user_name_attr,
      // 要诊断的用户名
      username: loginTestForm.value.username
    })
    
    diagnosisResult.value = response.data
    
    if (response.data.success) {
      ElMessage.success('用户搜索诊断完成')
    } else {
      ElMessage.error('用户搜索诊断失败')
    }
  } catch (error: any) {
    ElMessage.error('诊断失败：' + (error.response?.data?.detail || error.message))
    diagnosisResult.value = {
      success: false,
      message: '诊断过程中发生错误',
      diagnosis: null
    }
  } finally {
    diagnosisLoading.value = false
  }
}

onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.ldap-config-management {
  padding: 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.config-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ldap-form {
  padding-right: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 2px;
}

.dialog-footer .el-button {
  min-width: 100px;
}

.test-buttons {
  display: flex;
  gap: 12px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}



:deep(.el-input-number) {
  width: 100%;
}

.ldap-config-dialog :deep(.el-dialog) {
  max-height: 85vh;
  margin-top: 5vh !important;
  margin-bottom: 10vh !important;
}

.ldap-config-dialog :deep(.el-dialog__body) {
  max-height: calc(85vh - 140px);
  overflow-y: auto;
  padding: 24px;
}

.ldap-config-dialog :deep(.el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
}

.ldap-config-dialog :deep(.el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #e4e7ed;
}

/* 折叠面板样式 */
.ldap-config-collapse {
  margin-bottom: 20px;
}

.ldap-config-collapse :deep(.el-collapse-item) {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.ldap-config-collapse :deep(.el-collapse-item__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.ldap-config-collapse :deep(.el-collapse-item__content) {
  padding: 20px;
  background-color: #fff;
}

.ldap-config-collapse :deep(.el-collapse-item__arrow) {
  margin-right: 8px;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.collapse-title .el-icon {
  font-size: 16px;
  color: #409eff;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.form-section :deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.form-section :deep(.el-card__body) {
  padding: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.field-help {
  font-size: 12px;
  color: #909399;
  margin-top: 6px;
  line-height: 1.4;
}

.form-control-group {
  width: 100%;
}

.form-control-group .field-help {
  margin-top: 8px;
}

/* 统一表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  line-height: 32px;
  height: 32px;
  font-weight: 500;
  color: #606266;
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea) {
  width: 100%;
}

/* 统一输入框高度 */
:deep(.el-input__wrapper) {
  min-height: 32px;
}

:deep(.el-switch) {
  height: 32px;
  line-height: 32px;
}

/* 三列布局对齐优化 */
.form-section .el-row .el-col :deep(.el-form-item__label) {
  text-align: right;
  padding-right: 12px;
}

/* 表单内容区域对齐 */
:deep(.el-form-item__content) {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 32px;
}

/* 测试结果样式 */
.test-result {
  margin-top: 16px;
}

.user-info-list {
  margin: 8px 0;
  padding-left: 16px;
}

.user-info-list li {
  margin-bottom: 4px;
  line-height: 1.5;
}

/* 诊断结果样式 */
.diagnosis-result {
  margin-top: 16px;
}

.diagnosis-section {
  margin-bottom: 16px;
}

.diagnosis-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.diagnosis-section ul {
  margin: 0;
  padding-left: 20px;
}

.test-item {
  margin-bottom: 16px;
}

.test-tag {
  margin-right: 8px;
}

.test-details {
  margin: 8px 0;
}

.found-users {
  margin-top: 8px;
}

.sample-users {
  margin-top: 8px;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
}

/* IP网段配置帮助样式 */
.ip-range-help {
  padding: 12px;
  text-align: left;
}

.ip-range-help p {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #606266;
}

.ip-range-help ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.ip-range-help li {
  margin-bottom: 4px;
  color: #909399;
  font-size: 12px;
}
</style> 