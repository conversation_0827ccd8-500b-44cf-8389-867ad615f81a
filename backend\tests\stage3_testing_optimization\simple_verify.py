"""
第三阶段简单验证脚本
验证第三阶段开发的核心文件和结构
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent  # 从backend/tests/stage3_testing_optimization回到项目根目录
sys.path.insert(0, str(project_root))

def check_file_exists(file_path: str, description: str) -> bool:
    """检查文件是否存在"""
    # 使用正斜杠并转换为Path对象
    normalized_path = file_path.replace('\\', '/')
    full_path = project_root / normalized_path
    exists = full_path.exists()
    status = "✅" if exists else "❌"
    print(f"{status} {description}: {normalized_path}")
    if not exists:
        print(f"    查找路径: {full_path}")
    return exists

def check_directory_exists(dir_path: str, description: str) -> bool:
    """检查目录是否存在"""
    # 使用正斜杠并转换为Path对象
    normalized_path = dir_path.replace('\\', '/')
    full_path = project_root / normalized_path
    exists = full_path.exists() and full_path.is_dir()
    status = "✅" if exists else "❌"
    print(f"{status} {description}: {normalized_path}")
    if not exists:
        print(f"    查找路径: {full_path}")
    return exists

def main():
    """主验证函数"""
    print("=" * 60)
    print("第三阶段开发成果验证")
    print("=" * 60)
    
    total_checks = 0
    passed_checks = 0
    
    # 检查测试目录结构
    print("\n1. 测试目录结构:")
    checks = [
        ("backend/tests/stage3_testing_optimization", "第三阶段测试目录"),
        ("backend/tests/stage3_testing_optimization/optimization", "性能优化目录"),
        ("backend/tests/stage3_testing_optimization/monitoring", "监控机制目录"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_directory_exists(path, desc):
            passed_checks += 1
    
    # 检查核心测试文件
    print("\n2. 核心测试文件:")
    checks = [
        ("backend/tests/stage3_testing_optimization/test_functionality_complete.py", "功能完整性测试"),
        ("backend/tests/stage3_testing_optimization/test_data_consistency.py", "数据一致性测试"),
        ("backend/tests/stage3_testing_optimization/test_performance.py", "性能测试"),
        ("backend/tests/stage3_testing_optimization/test_exception_handling.py", "异常处理测试"),
        ("backend/tests/stage3_testing_optimization/test_ui_usability.py", "界面易用性测试"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_file_exists(path, desc):
            passed_checks += 1
    
    # 检查优化模块
    print("\n3. 性能优化模块:")
    checks = [
        ("backend/tests/stage3_testing_optimization/optimization/sync_performance.py", "同步性能优化"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_file_exists(path, desc):
            passed_checks += 1
    
    # 检查监控模块
    print("\n4. 监控机制模块:")
    checks = [
        ("backend/tests/stage3_testing_optimization/monitoring/sync_status_monitor.py", "同步状态监控"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_file_exists(path, desc):
            passed_checks += 1
    
    # 检查工具脚本
    print("\n5. 工具脚本:")
    checks = [
        ("backend/tests/stage3_testing_optimization/run_stage3_tests.py", "测试运行器"),
        ("backend/tests/stage3_testing_optimization/quick_test.py", "快速测试脚本"),
        ("backend/tests/stage3_testing_optimization/analyze_test_results.py", "测试结果分析工具"),
        ("backend/tests/stage3_testing_optimization/test_config.py", "测试配置管理"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_file_exists(path, desc):
            passed_checks += 1
    
    # 检查文档
    print("\n6. 文档文件:")
    checks = [
        ("backend/tests/stage3_testing_optimization/README.md", "第三阶段测试说明"),
        ("docs/email/第三阶段开发总结.md", "第三阶段开发总结"),
    ]
    
    for path, desc in checks:
        total_checks += 1
        if check_file_exists(path, desc):
            passed_checks += 1
    
    # 检查代码内容
    print("\n7. 代码内容验证:")
    
    # 检查功能完整性测试文件内容
    functionality_test_file = project_root / "backend/tests/stage3_testing_optimization/test_functionality_complete.py"
    if functionality_test_file.exists():
        content = functionality_test_file.read_text(encoding='utf-8')
        if "TestFunctionalityComplete" in content:
            print("✅ 功能完整性测试类存在")
            passed_checks += 1
        else:
            print("❌ 功能完整性测试类缺失")
        total_checks += 1
    
    # 检查性能优化文件内容
    performance_file = project_root / "backend/tests/stage3_testing_optimization/optimization/sync_performance.py"
    if performance_file.exists():
        content = performance_file.read_text(encoding='utf-8')
        if "OptimizedPersonnelSyncService" in content:
            print("✅ 优化的同步服务类存在")
            passed_checks += 1
        else:
            print("❌ 优化的同步服务类缺失")
        total_checks += 1
    
    # 检查监控文件内容
    monitor_file = project_root / "backend/tests/stage3_testing_optimization/monitoring/sync_status_monitor.py"
    if monitor_file.exists():
        content = monitor_file.read_text(encoding='utf-8')
        if "SyncStatusMonitor" in content:
            print("✅ 同步状态监控类存在")
            passed_checks += 1
        else:
            print("❌ 同步状态监控类缺失")
        total_checks += 1
    
    # 输出总结
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"通过率: {passed_checks / total_checks * 100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 第三阶段开发成果验证完全通过！")
        print("所有核心文件和功能模块都已正确创建。")
    elif passed_checks >= total_checks * 0.8:
        print("\n✅ 第三阶段开发成果验证基本通过！")
        print("大部分核心文件和功能模块已正确创建。")
    else:
        print("\n⚠️ 第三阶段开发成果验证部分通过。")
        print("部分核心文件或功能模块可能缺失。")
    
    # 提供使用建议
    print("\n" + "=" * 60)
    print("使用建议")
    print("=" * 60)
    print("1. 运行完整测试:")
    print("   cd backend")
    print("   python tests/stage3_testing_optimization/run_stage3_tests.py")
    print()
    print("2. 运行快速验证:")
    print("   cd backend")
    print("   python tests/stage3_testing_optimization/quick_test.py")
    print()
    print("3. 运行特定测试:")
    print("   cd backend")
    print("   python -m pytest tests/stage3_testing_optimization/test_functionality_complete.py -v")
    print()
    print("4. 生成监控面板:")
    print("   cd backend")
    print("   python tests/stage3_testing_optimization/monitoring/sync_status_monitor.py")
    print()
    print("5. 性能优化验证:")
    print("   cd backend")
    print("   python tests/stage3_testing_optimization/optimization/sync_performance.py")
    
    return passed_checks == total_checks

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
