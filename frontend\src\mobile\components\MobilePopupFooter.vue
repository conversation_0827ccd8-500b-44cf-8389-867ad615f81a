<template>
  <div class="mobile-popup-footer" :class="{ 'safe-area': safeArea }">
    <div class="footer-buttons">
      <van-button
        v-for="(button, index) in buttons"
        :key="index"
        :type="button.type || 'default'"
        :loading="button.loading"
        :disabled="button.disabled"
        :size="button.size || 'large'"
        :class="['footer-btn', button.class]"
        @click="button.onClick"
      >
        {{ button.text }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ButtonConfig {
  text: string
  type?: 'primary' | 'default' | 'danger' | 'warning' | 'success'
  loading?: boolean
  disabled?: boolean
  size?: 'large' | 'normal' | 'small' | 'mini'
  class?: string
  onClick: () => void
}

interface Props {
  buttons: ButtonConfig[]
  safeArea?: boolean // 是否适配安全区域
  background?: string // 背景颜色
}

const props = withDefaults(defineProps<Props>(), {
  buttons: () => [],
  safeArea: true,
  background: 'white'
})
</script>

<style lang="scss" scoped>
.mobile-popup-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: v-bind(background);
  border-top: 1px solid #ebedf0;
  
  &.safe-area {
    padding-bottom: max(var(--mobile-safe-area-bottom, 0), 24px);
  }
  
  .footer-buttons {
    padding: 24px 16px;
    display: flex;
    gap: 12px;
    
    .footer-btn {
      flex: 1;
      min-height: 52px;
      
      // 当只有一个按钮时，占满宽度
      &:only-child {
        flex: 1;
      }
      
      // 当有两个按钮时，平分宽度
      &:nth-child(1):nth-last-child(2),
      &:nth-child(2):nth-last-child(1) {
        flex: 1;
      }
    }
  }
  
  // 在弹窗底部时的特殊样式
  .popup-content ~ & {
    margin-top: auto;
  }
}

// 为弹窗内容添加底部边距，避免被底部按钮遮挡
:deep(.popup-content) {
  padding-bottom: calc(52px + 48px + max(var(--mobile-safe-area-bottom, 0), 24px));
}
</style> 