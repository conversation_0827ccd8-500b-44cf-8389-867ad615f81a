"""Add app_name to EmailConfig

Revision ID: 7da725d812da
Revises: bb0c8d20bb6f
Create Date: 2025-05-26 15:17:33.238542

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '7da725d812da'
down_revision: Union[str, None] = 'bb0c8d20bb6f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_ad_sync_locks_lock_name', table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.add_column('email_configs', sa.Column('app_name', sa.String(length=100), nullable=True, comment='应用名称'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('email_configs', 'app_name')
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), nullable=True),
    sa.Column('locked_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index('ix_ad_sync_locks_lock_name', 'ad_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###
