#!/usr/bin/env python
"""
Agent安装包制作工具

使用NSIS创建Windows安装程序，实现简单的安装向导界面，支持静默安装等功能。
"""

import os
import sys
import argparse
import logging
import subprocess
import tempfile
import shutil
from pathlib import Path
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


def check_nsis():
    """检查NSIS是否已安装"""
    try:
        # 尝试查找makensis.exe
        if os.name == 'nt':  # Windows
            nsis_paths = [
                r'C:\Program Files\NSIS\makensis.exe',
                r'C:\Program Files (x86)\NSIS\makensis.exe'
            ]
            for path in nsis_paths:
                if os.path.exists(path):
                    return path

            # 尝试使用where命令查找
            try:
                result = subprocess.run(['where', 'makensis'],
                                      capture_output=True, text=True, check=True)
                return result.stdout.strip().splitlines()[0]
            except:
                pass
        else:  # Linux/Mac
            try:
                result = subprocess.run(['which', 'makensis'],
                                      capture_output=True, text=True, check=True)
                return result.stdout.strip()
            except:
                pass

        logger.error("未找到NSIS (makensis)，请先安装NSIS: https://nsis.sourceforge.io/Download")
        return None
    except Exception as e:
        logger.error(f"检查NSIS时发生错误: {e}")
        return None


def build_executable(build_dir, args):
    """使用PyInstaller构建可执行文件"""
    logger.info("正在使用PyInstaller构建可执行文件...")

    # 首先构建服务程序
    try:
        # 执行build_installer.py脚本（假设它已经存在）
        build_cmd = [
            sys.executable,
            'build_installer.py',
            '--output', str(build_dir),
            '--name', args.name,
            '--version', args.version,
            '--server', args.server
        ]

        if args.tls:
            build_cmd.append('--tls')

        if args.cert:
            build_cmd.extend(['--cert', args.cert])

        if args.icon:
            build_cmd.extend(['--icon', args.icon])

        subprocess.run(build_cmd, check=True)
        logger.info("可执行文件构建完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"构建可执行文件失败: {e}")
        return False


def create_nsis_script(temp_dir, args, nsis_vars):
    """创建NSIS安装脚本"""
    logger.info("正在创建NSIS安装脚本...")

    # 脚本路径
    nsis_script = temp_dir / "installer.nsi"

    # 创建NSIS脚本内容
    script_content = fr"""
; 终端管理Agent安装程序脚本
; 使用NSIS创建

Unicode true
SetCompressor /SOLID lzma

; 定义常量
!define PRODUCT_NAME "{args.name}"
!define PRODUCT_VERSION "{args.version}"
!define PRODUCT_PUBLISHER "{nsis_vars['publisher']}"
!define PRODUCT_WEB_SITE "{nsis_vars['website']}"
!define PRODUCT_DIR_REGKEY "Software\\Microsoft\\Windows\\CurrentVersion\\App Paths\\{args.name}.exe"
!define PRODUCT_UNINST_KEY "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{PRODUCT_NAME}}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; MUI设置
!include "MUI2.nsh"
!include "LogicLib.nsh"
!include "FileFunc.nsh"
!include "Time.nsh"
!include "x64.nsh"

; MUI页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "${{NSISDIR}}\\Docs\\Modern UI\\License.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

; 语言文件
!insertmacro MUI_LANGUAGE "SimpChinese"

; 安装程序属性
Name "${{PRODUCT_NAME}} ${{PRODUCT_VERSION}}"
OutFile "{args.output}\\{args.name}-{args.version}-Setup.exe"
InstallDir "$PROGRAMFILES\\{args.name}"
InstallDirRegKey HKLM "${{PRODUCT_DIR_REGKEY}}" ""
ShowInstDetails show
ShowUnInstDetails show

; 添加静默安装选项
SilentInstall normal

; 初始化函数
Function .onInit
  ; 检查命令行参数
  ${{If}} ${{Silent}} == ""
    ${{If}} "${{cmdline}}" contains "/S"
      SetSilent silent
    ${{EndIf}}
  ${{EndIf}}

  ; 检查是否已安装，如果已安装则先卸载
  ReadRegStr $R0 HKLM "${{PRODUCT_UNINST_KEY}}" "UninstallString"
  StrCmp $R0 "" done

  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION "发现已安装的 ${{PRODUCT_NAME}}，是否先卸载？" IDOK uninst
  Abort

  uninst:
    ; 运行卸载程序
    ClearErrors
    ExecWait '$R0 _?=$INSTDIR'

  done:
FunctionEnd

; 安装节
Section "MainSection" SEC01
  SetOutPath "$INSTDIR"
  SetOverwrite ifnewer

  ; 添加文件
  File "{temp_dir}\\{args.name}\\{args.name}.exe"

  ; 添加配置文件
  CreateDirectory "$PROGRAMDATA\\{args.name}"
  CreateDirectory "$PROGRAMDATA\\{args.name}\\logs"

  ; 创建默认配置
  File /oname=$PROGRAMDATA\\{args.name}\\agent_config.json "{temp_dir}\\{args.name}\\default_config.json"

  ; 创建程序目录
  CreateDirectory "$SMPROGRAMS\\{args.name}"
  CreateShortCut "$SMPROGRAMS\\{args.name}\\{args.name}.lnk" "$INSTDIR\\{args.name}.exe"

  ; 注册服务
  ExecWait '"$INSTDIR\\{args.name}.exe" install'

  ; 启动服务
  ExecWait 'net start {args.name}'
SectionEnd

; 安装完成后操作
Section -Post
  ; 写入注册表信息
  WriteUninstaller "$INSTDIR\\uninst.exe"
  WriteRegStr HKLM "${{PRODUCT_DIR_REGKEY}}" "" "$INSTDIR\\{args.name}.exe"
  WriteRegStr ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}" "DisplayName" "$(^Name)"
  WriteRegStr ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}" "UninstallString" "$INSTDIR\\uninst.exe"
  WriteRegStr ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}" "DisplayIcon" "$INSTDIR\\{args.name}.exe"
  WriteRegStr ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}" "DisplayVersion" "${{PRODUCT_VERSION}}"
  WriteRegStr ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}" "Publisher" "${{PRODUCT_PUBLISHER}}"
SectionEnd

; 卸载节
Section Uninstall
  ; 创建详细的卸载日志
  CreateDirectory "$TEMP\\${{PRODUCT_NAME}}_uninstall_logs"
  FileOpen $0 "$TEMP\\${{PRODUCT_NAME}}_uninstall_logs\\uninstall.log" w
  FileWrite $0 "开始卸载 ${{PRODUCT_NAME}} ${{PRODUCT_VERSION}}$\r$\n"
  FileWrite $0 "当前时间: "
  ${{GetTime}} "" "L" $1 $2 $3 $4 $5 $6 $7
  FileWrite $0 "$2/$1/$3 $4:$5:$6$\r$\n"
  FileWrite $0 "安装目录: $INSTDIR$\r$\n$\r$\n"

  ; 停止服务
  FileWrite $0 "正在停止服务...$\r$\n"
  DetailPrint "正在停止服务..."
  ClearErrors
  ExecWait 'net stop {args.name}' $R0
  ${{If}} $R0 != 0
    FileWrite $0 "警告: 服务停止返回代码 $R0，继续卸载过程...$\r$\n"
    DetailPrint "警告: 服务停止返回代码 $R0，继续卸载过程..."
  ${{EndIf}}
  Sleep 2000

  ; 删除服务
  FileWrite $0 "正在删除服务...$\r$\n"
  DetailPrint "正在删除服务..."
  ClearErrors
  ExecWait '"$INSTDIR\\{args.name}.exe" remove' $R0
  ${{If}} $R0 != 0
    ; 尝试使用SC命令删除服务
    FileWrite $0 "使用可执行文件移除服务失败，尝试使用SC命令...$\r$\n"
    DetailPrint "使用可执行文件移除服务失败，尝试使用SC命令..."
    ExecWait 'sc delete {args.name}' $R0
    ${{If}} $R0 != 0
      FileWrite $0 "警告: 服务删除返回代码 $R0，继续卸载过程...$\r$\n"
      DetailPrint "警告: 服务删除返回代码 $R0，继续卸载过程..."
    ${{EndIf}}
  ${{EndIf}}
  Sleep 2000

  ; 确保进程已关闭
  FileWrite $0 "确保进程已关闭...$\r$\n"
  DetailPrint "确保进程已关闭..."
  ExecWait 'taskkill /f /im "{args.name}.exe" /t' $R0
  ${{If}} $R0 != 0
    FileWrite $0 "警告: 进程终止返回代码 $R0，继续卸载过程...$\r$\n"
    DetailPrint "警告: 进程终止返回代码 $R0，继续卸载过程..."
  ${{EndIf}}
  Sleep 1000

  ; 删除程序文件
  FileWrite $0 "正在删除程序文件...$\r$\n"
  DetailPrint "正在删除程序文件..."
  ClearErrors
  Delete "$INSTDIR\\{args.name}.exe"
  ${{If}} ${{Errors}}
    FileWrite $0 "警告: 无法删除主程序文件，可能正在使用...$\r$\n"
    DetailPrint "警告: 无法删除主程序文件，可能正在使用..."
  ${{EndIf}}
  Delete "$INSTDIR\\uninst.exe"

  ; 删除程序目录和快捷方式
  FileWrite $0 "正在删除程序目录和快捷方式...$\r$\n"
  DetailPrint "正在删除程序目录和快捷方式..."
  Delete "$SMPROGRAMS\\{args.name}\\{args.name}.lnk"
  RMDir "$SMPROGRAMS\\{args.name}"

  ; 删除空目录
  FileWrite $0 "正在删除安装目录...$\r$\n"
  DetailPrint "正在删除安装目录..."
  RMDir /r "$INSTDIR"

  ; 询问是否删除配置和日志
  MessageBox MB_YESNO|MB_ICONQUESTION "是否同时删除配置和日志文件? $\n$\n选择'是'将完全删除所有数据。$\n选择'否'将保留配置文件以便将来重新安装。" IDNO skip_config_delete

  ; 删除配置文件
  FileWrite $0 "正在删除配置和日志文件...$\r$\n"
  DetailPrint "正在删除配置和日志文件..."
  RMDir /r "$PROGRAMDATA\\{args.name}"
  Goto config_delete_done

  skip_config_delete:
  FileWrite $0 "用户选择保留配置和日志文件$\r$\n"
  DetailPrint "用户选择保留配置和日志文件"

  config_delete_done:
  ; 删除注册表键
  FileWrite $0 "正在删除注册表键...$\r$\n"
  DetailPrint "正在删除注册表键..."
  DeleteRegKey ${{PRODUCT_UNINST_ROOT_KEY}} "${{PRODUCT_UNINST_KEY}}"
  DeleteRegKey HKLM "${{PRODUCT_DIR_REGKEY}}"

  ; 尝试清理系统中的残留
  FileWrite $0 "正在清理系统残留...$\r$\n"
  DetailPrint "正在清理系统残留..."
  ; 清理临时文件
  RMDir /r "$TEMP\\{args.name}"

  ; 完成卸载
  FileWrite $0 "卸载完成!$\r$\n"
  DetailPrint "卸载完成!"
  FileClose $0

  ; 复制卸载日志到桌面 (可选)
  CopyFiles "$TEMP\\${{PRODUCT_NAME}}_uninstall_logs\\uninstall.log" "$DESKTOP\\${{PRODUCT_NAME}}_uninstall.log"

  SetAutoClose true
SectionEnd
"""

    # 写入NSIS脚本文件
    with open(nsis_script, 'w', encoding='utf-8') as f:
        f.write(script_content)

    logger.info(f"NSIS安装脚本已创建: {nsis_script}")
    return nsis_script


def build_installer(nsis_path, nsis_script):
    """使用NSIS构建安装程序"""
    logger.info("正在使用NSIS构建安装程序...")

    try:
        subprocess.run([nsis_path, str(nsis_script)], check=True)
        logger.info("安装程序构建成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"构建安装程序失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="终端管理Agent安装包制作工具")
    parser.add_argument('--output', '-o', type=str, default='dist', help='输出目录')
    parser.add_argument('--name', '-n', type=str, default='TerminalAgent', help='安装包名称')
    parser.add_argument('--version', '-v', type=str, default='1.0.0', help='Agent版本')
    parser.add_argument('--server', '-s', type=str, default='localhost:50051', help='服务器地址')
    parser.add_argument('--tls', action='store_true', help='使用TLS加密')
    parser.add_argument('--cert', '-c', type=str, help='证书文件路径')
    parser.add_argument('--icon', '-i', type=str, help='图标文件路径')
    args = parser.parse_args()

    # 检查NSIS
    nsis_path = check_nsis()
    if not nsis_path:
        return 1

    # 准备临时目录
    with tempfile.TemporaryDirectory() as temp:
        temp_dir = Path(temp)
        build_dir = temp_dir / "build"
        os.makedirs(build_dir, exist_ok=True)

        try:
            # 构建可执行文件
            if not build_executable(build_dir, args):
                return 1

            # NSIS变量
            nsis_vars = {
                "publisher": "OPS Platform",
                "website": "https://example.com",
            }

            # 创建NSIS脚本
            nsis_script = create_nsis_script(temp_dir, args, nsis_vars)

            # 构建安装程序
            if not build_installer(nsis_path, nsis_script):
                return 1

            logger.info(f"安装程序已生成: {args.output}\\{args.name}-{args.version}-Setup.exe")
            return 0

        except Exception as e:
            logger.error(f"制作安装包时发生错误: {e}")
            return 1


if __name__ == "__main__":
    sys.exit(main())