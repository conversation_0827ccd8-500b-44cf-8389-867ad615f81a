from typing import Any, List
import logging
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from ... import schemas
from ...database import get_db
from ...api import deps
from ...crud.ldap_config import ldap_config_crud
from ...services.ldap_auth import LdapAuthService
from ...utils.ip_matcher import IPMatcher

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=List[schemas.LdapConfigResponse])
def get_ldap_configs(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """获取LDAP配置列表"""
    configs = ldap_config_crud.get_multi(db, skip=skip, limit=limit)
    return configs

@router.post("/", response_model=schemas.LdapConfigResponse)
def create_ldap_config(
    *,
    db: Session = Depends(get_db),
    config_in: schemas.LdapConfigCreate,
    current_user = Depends(deps.check_permissions(["ldap:config:manage"])),
) -> Any:
    """创建LDAP配置"""
    # 检查名称是否已存在
    existing_config = ldap_config_crud.get_by_name(db, name=config_in.name)
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置名称已存在"
        )
    
    config = ldap_config_crud.create(db, obj_in=config_in)
    return config

@router.get("/{config_id}", response_model=schemas.LdapConfigResponse)
def get_ldap_config(
    *,
    db: Session = Depends(get_db),
    config_id: int,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """获取LDAP配置详情"""
    config = ldap_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    return config

@router.put("/{config_id}", response_model=schemas.LdapConfigResponse)
def update_ldap_config(
    *,
    db: Session = Depends(get_db),
    config_id: int,
    config_in: schemas.LdapConfigUpdate,
    current_user = Depends(deps.check_permissions(["ldap:config:manage"])),
) -> Any:
    """更新LDAP配置"""
    config = ldap_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    
    # 如果更新名称，检查是否与其他配置冲突
    if config_in.name and config_in.name != config.name:
        existing_config = ldap_config_crud.get_by_name(db, name=config_in.name)
        if existing_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="配置名称已存在"
            )
    
    config = ldap_config_crud.update(db, db_obj=config, obj_in=config_in)
    return config

@router.delete("/{config_id}")
def delete_ldap_config(
    *,
    db: Session = Depends(get_db),
    config_id: int,
    current_user = Depends(deps.check_permissions(["ldap:config:manage"])),
) -> Any:
    """删除LDAP配置"""
    config = ldap_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    
    # 检查是否为默认配置
    if config.is_default:
        # 检查是否还有其他配置
        other_configs = ldap_config_crud.get_multi(db, skip=0, limit=2)
        if len(other_configs) > 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除默认配置，请先设置其他配置为默认"
            )
    
    ldap_config_crud.remove(db, id=config_id)
    return {"message": "配置删除成功"}

@router.post("/{config_id}/set-default", response_model=schemas.LdapConfigResponse)
def set_default_config(
    *,
    db: Session = Depends(get_db),
    config_id: int,
    current_user = Depends(deps.check_permissions(["ldap:config:manage"])),
) -> Any:
    """设置默认LDAP配置"""
    config = ldap_config_crud.set_default(db, config_id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    return config

@router.post("/{config_id}/test-connection")
def test_ldap_connection(
    *,
    db: Session = Depends(get_db),
    config_id: int,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """测试LDAP连接"""
    config = ldap_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    
    ldap_service = LdapAuthService(db)
    success = ldap_service.test_connection(config)
    
    return {
        "success": success,
        "message": "连接成功" if success else "连接失败"
    }

@router.post("/test-connection")
def test_ldap_connection_config(
    *,
    db: Session = Depends(get_db),
    connection_test: schemas.LdapConnectionTest,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """测试LDAP连接（使用临时配置）"""
    from ...models.ldap_config import LdapConfig
    
    # 创建临时配置对象用于测试
    temp_config = LdapConfig(
        server=connection_test.server,
        port=connection_test.port,
        use_ssl=connection_test.use_ssl,
        base_dn=connection_test.base_dn,
        bind_dn=connection_test.bind_dn,
        bind_password=connection_test.bind_password
    )
    
    ldap_service = LdapAuthService(db)
    success = ldap_service.test_connection(temp_config)
    
    return {
        "success": success,
        "message": "连接成功" if success else "连接失败"
    }

@router.post("/test-user-login")
def test_ldap_user_login(
    *,
    db: Session = Depends(get_db),
    login_test: schemas.LdapUserLoginTest,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """测试LDAP用户登录"""
    from ...models.ldap_config import LdapConfig
    
    # 创建临时配置对象用于测试
    temp_config = LdapConfig(
        server=login_test.server,
        port=login_test.port,
        use_ssl=login_test.use_ssl,
        base_dn=login_test.base_dn,
        bind_dn=login_test.bind_dn,
        bind_password=login_test.bind_password,
        user_search_base=login_test.user_search_base,
        user_search_filter=login_test.user_search_filter,
        user_name_attr=login_test.user_name_attr,
        user_email_attr=login_test.user_email_attr,
        user_display_name_attr=login_test.user_display_name_attr
    )
    
    ldap_service = LdapAuthService(db)
    
    try:
        # 首先测试连接
        connection_success = ldap_service.test_connection(temp_config)
        if not connection_success:
            return {
                "success": False,
                "message": "LDAP服务器连接失败",
                "error_type": "connection_error"
            }
        
        # 测试用户认证
        user_info = ldap_service.authenticate_user(
            username=login_test.username,
            password=login_test.password,
            config=temp_config
        )
        
        if user_info:
            return {
                "success": True,
                "message": "用户认证成功",
                "user_info": {
                    "username": user_info.get("username"),
                    "email": user_info.get("email"),
                    "display_name": user_info.get("display_name"),
                    "dn": user_info.get("dn")
                }
            }
        else:
            return {
                "success": False,
                "message": "用户认证失败，请检查用户名或密码",
                "error_type": "auth_failed"
            }
            
    except Exception as e:
        logger.error(f"LDAP用户登录测试失败: {str(e)}")
        return {
            "success": False,
            "message": f"测试过程中发生错误: {str(e)}",
            "error_type": "test_error"
        }

@router.post("/validate-ip-ranges")
def validate_ip_ranges(
    *,
    ip_ranges: List[str],
    current_user = Depends(deps.check_permissions(["ldap:config:edit"])),
) -> Any:
    """验证IP范围格式"""
    results = []
    all_valid = True
    
    for ip_range in ip_ranges:
        is_valid, error_msg = IPMatcher.validate_ip_range(ip_range)
        description = IPMatcher.get_range_description(ip_range) if is_valid else ""
        
        results.append({
            "ip_range": ip_range,
            "is_valid": is_valid,
            "error_message": error_msg,
            "description": description
        })
        
        if not is_valid:
            all_valid = False
    
    return {
        "all_valid": all_valid,
        "results": results
    }

@router.post("/preview-ip-match")
def preview_ip_match(
    *,
    request: Request,
    config_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """预览指定配置对当前IP的匹配情况"""
    client_ip = request.client.host
    
    # 获取配置
    config = ldap_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="LDAP配置不存在")
    
    # 检查匹配情况
    matches = []
    if config.ip_ranges:
        for ip_range in config.ip_ranges:
            is_match = IPMatcher.is_ip_in_range(client_ip, ip_range)
            matches.append({
                "ip_range": ip_range,
                "is_match": is_match,
                "description": IPMatcher.get_range_description(ip_range)
            })
    
    # 获取所有配置进行对比
    all_configs = ldap_config_crud.get_multi(db, limit=100)
    matching_configs = IPMatcher.find_matching_configs(client_ip, all_configs)
    
    return {
        "client_ip": client_ip,
        "config": {
            "id": config.id,
            "name": config.name,
            "server": config.server,
            "priority": config.priority,
            "auto_select_enabled": config.auto_select_enabled
        },
        "matches": matches,
        "would_be_selected": any(match[0].id == config_id for match in matching_configs),
        "current_priority": config.priority if any(match[0].id == config_id for match in matching_configs) else None,
        "competing_configs": [
            {
                "id": match[0].id,
                "name": match[0].name,
                "priority": match[0].priority,
                "matched_range": match[1]
            }
            for match in matching_configs
        ]
    }

@router.post("/diagnose-user-search")
def diagnose_ldap_user_search(
    *,
    db: Session = Depends(get_db),
    search_request: schemas.LdapUserSearchDiagnose,
    current_user = Depends(deps.check_permissions(["ldap:config:view"])),
) -> Any:
    """诊断LDAP用户搜索问题"""
    from ...models.ldap_config import LdapConfig
    from ldap3 import Server, Connection, ALL, SIMPLE, SUBTREE, BASE
    
    # 创建临时配置对象用于测试
    temp_config = LdapConfig(
        server=search_request.server,
        port=search_request.port,
        use_ssl=search_request.use_ssl,
        base_dn=search_request.base_dn,
        bind_dn=search_request.bind_dn,
        bind_password=search_request.bind_password,
        user_search_base=search_request.user_search_base,
        user_search_filter=search_request.user_search_filter,
        user_name_attr=search_request.user_name_attr
    )
    
    try:
        # 创建服务器连接
        server = Server(
            temp_config.server,
            port=temp_config.port,
            use_ssl=temp_config.use_ssl,
            get_info=ALL
        )
        
        # 建立连接
        if temp_config.bind_dn and temp_config.bind_password:
            conn = Connection(
                server,
                user=temp_config.bind_dn,
                password=temp_config.bind_password,
                authentication=SIMPLE,
                auto_bind=True
            )
            connection_type = "绑定用户连接"
        else:
            conn = Connection(server, auto_bind=True)
            connection_type = "匿名连接"
        
        diagnosis_result = {
            "connection_type": connection_type,
            "search_parameters": {
                "username": search_request.username,
                "base_dn": temp_config.base_dn,
                "user_search_base": temp_config.user_search_base,
                "user_search_filter": temp_config.user_search_filter,
                "actual_search_base": temp_config.user_search_base or temp_config.base_dn,
                "actual_search_filter": temp_config.user_search_filter.format(username=search_request.username)
            },
            "tests": []
        }
        
        # 测试1: 验证搜索基础DN
        search_base = temp_config.user_search_base or temp_config.base_dn
        base_test = {
            "name": "搜索基础DN验证",
            "success": False,
            "details": ""
        }
        
        try:
            base_success = conn.search(
                search_base=search_base,
                search_filter='(objectClass=*)',
                search_scope=BASE,
                attributes=['distinguishedName']
            )
            if base_success:
                base_test["success"] = True
                base_test["details"] = f"搜索基础DN '{search_base}' 存在且可访问"
            else:
                base_test["details"] = f"搜索基础DN '{search_base}' 不存在或无法访问"
        except Exception as e:
            base_test["details"] = f"访问搜索基础DN时出错: {str(e)}"
        
        diagnosis_result["tests"].append(base_test)
        
        # 测试2: 搜索目标用户
        user_test = {
            "name": "目标用户搜索",
            "success": False,
            "details": "",
            "found_users": []
        }
        
        search_filter = temp_config.user_search_filter.format(username=search_request.username)
        try:
            user_success = conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'sAMAccountName', 'userPrincipalName', 'displayName']
            )
            
            if user_success and conn.entries:
                user_test["success"] = True
                user_test["details"] = f"找到 {len(conn.entries)} 个匹配的用户"
                for entry in conn.entries:
                    user_info = {
                        "dn": entry.entry_dn,
                        "sAMAccountName": getattr(entry, 'sAMAccountName', {}).value if hasattr(entry, 'sAMAccountName') else "N/A",
                        "displayName": getattr(entry, 'displayName', {}).value if hasattr(entry, 'displayName') else "N/A"
                    }
                    user_test["found_users"].append(user_info)
            else:
                user_test["details"] = f"使用过滤器 '{search_filter}' 未找到任何用户"
        except Exception as e:
            user_test["details"] = f"搜索用户时出错: {str(e)}"
        
        diagnosis_result["tests"].append(user_test)
        
        # 测试3: 列出搜索基础DN下的示例用户
        sample_test = {
            "name": "示例用户列表",
            "success": False,
            "details": "",
            "sample_users": []
        }
        
        try:
            sample_success = conn.search(
                search_base=search_base,
                search_filter='(&(objectClass=user)(sAMAccountName=*))',
                search_scope=SUBTREE,
                attributes=['sAMAccountName', 'displayName'],
                size_limit=10
            )
            
            if sample_success and conn.entries:
                sample_test["success"] = True
                sample_test["details"] = f"在 '{search_base}' 下找到 {len(conn.entries)} 个示例用户"
                for entry in conn.entries:
                    user_info = {
                        "sAMAccountName": getattr(entry, 'sAMAccountName', {}).value if hasattr(entry, 'sAMAccountName') else "N/A",
                        "displayName": getattr(entry, 'displayName', {}).value if hasattr(entry, 'displayName') else "N/A"
                    }
                    sample_test["sample_users"].append(user_info)
            else:
                sample_test["details"] = f"在 '{search_base}' 下未找到任何用户"
        except Exception as e:
            sample_test["details"] = f"获取示例用户时出错: {str(e)}"
        
        diagnosis_result["tests"].append(sample_test)
        
        # 测试4: 如果有user_search_base，在更广泛范围内搜索
        if temp_config.user_search_base and temp_config.user_search_base != temp_config.base_dn:
            broad_test = {
                "name": "扩展范围搜索",
                "success": False,
                "details": "",
                "found_users": []
            }
            
            try:
                broad_success = conn.search(
                    search_base=temp_config.base_dn,
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=['distinguishedName', 'sAMAccountName', 'displayName']
                )
                
                if broad_success and conn.entries:
                    broad_test["success"] = True
                    broad_test["details"] = f"在更广泛范围 '{temp_config.base_dn}' 中找到 {len(conn.entries)} 个匹配用户"
                    for entry in conn.entries:
                        user_info = {
                            "dn": entry.entry_dn,
                            "sAMAccountName": getattr(entry, 'sAMAccountName', {}).value if hasattr(entry, 'sAMAccountName') else "N/A",
                            "displayName": getattr(entry, 'displayName', {}).value if hasattr(entry, 'displayName') else "N/A"
                        }
                        broad_test["found_users"].append(user_info)
                else:
                    broad_test["details"] = f"在整个域 '{temp_config.base_dn}' 中也未找到目标用户"
            except Exception as e:
                broad_test["details"] = f"扩展搜索时出错: {str(e)}"
            
            diagnosis_result["tests"].append(broad_test)
        
        # 生成建议
        suggestions = []
        
        # 基础DN访问问题
        if not diagnosis_result["tests"][0]["success"]:
            suggestions.append("检查搜索基础DN配置是否正确，确保DN存在且绑定用户有访问权限")
        
        # 搜索过滤器问题
        if not diagnosis_result["tests"][1]["success"] and diagnosis_result["tests"][2]["success"]:
            suggestions.append("用户存在于LDAP中但搜索过滤器不匹配，检查用户名格式或尝试其他过滤器")
            suggestions.append("建议的过滤器: (|(sAMAccountName={username})(userPrincipalName={username}))")
        
        # 用户搜索范围问题 - 修复逻辑错误
        if len(diagnosis_result["tests"]) > 3:
            user_found_in_search_base = diagnosis_result["tests"][1]["success"]
            user_found_in_broad_search = diagnosis_result["tests"][3]["success"]
            
            if not user_found_in_search_base and user_found_in_broad_search:
                suggestions.append("用户存在于域中但不在指定的user_search_base中，考虑调整搜索基础DN")
            elif user_found_in_search_base and user_found_in_broad_search:
                suggestions.append("✅ 用户搜索配置正确，用户已在指定的搜索范围内找到")
        
        # 用户完全不存在
        if not any(test["success"] for test in diagnosis_result["tests"][1:]):
            suggestions.append("用户可能不存在于LDAP中，或绑定用户权限不足")
        
        # 配置正常的情况
        if diagnosis_result["tests"][1]["success"]:
            suggestions.append("🎉 用户搜索成功！LDAP配置工作正常")
            if not suggestions or suggestions[-1] != "✅ 用户搜索配置正确，用户已在指定的搜索范围内找到":
                suggestions.append("✅ 当前配置可以正常找到目标用户，无需调整")
        
        diagnosis_result["suggestions"] = suggestions
        
        conn.unbind()
        
        return {
            "success": True,
            "diagnosis": diagnosis_result
        }
        
    except Exception as e:
        logger.error(f"LDAP用户搜索诊断失败: {str(e)}")
        return {
            "success": False,
            "message": f"诊断过程中发生错误: {str(e)}"
        } 