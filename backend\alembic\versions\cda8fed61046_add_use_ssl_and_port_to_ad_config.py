"""add_use_ssl_and_port_to_ad_config

Revision ID: cda8fed61046
Revises: f5cd9e95fc6c
Create Date: 2024-12-18 16:44:25.066869

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cda8fed61046'
down_revision: Union[str, None] = 'f5cd9e95fc6c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ad_config', sa.Column('use_ssl', sa.<PERSON>(), nullable=True))
    op.add_column('ad_config', sa.Column('port', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ad_config', 'port')
    op.drop_column('ad_config', 'use_ssl')
    # ### end Alembic commands ###
