 # AD管理功能修复 - PostgreSQL迁移问题解决

## 问题描述

从SQLite切换到PostgreSQL后，AD管理-用户与组功能无法读取数据，前端显示为空。

## 问题诊断

### 1. 缺少PostgreSQL驱动
- **现象**: `ModuleNotFoundError: No module named 'psycopg2'`
- **原因**: 虽然`psycopg2-binary`在依赖列表中，但在错误的Python环境中运行
- **解决**: 使用`uv run`确保在正确的虚拟环境中运行

### 2. 缺少数据库表
- **现象**: 缺少`ad_sync_locks`表
- **原因**: 迁移过程中某些表未正确创建
- **解决**: 手动创建缺失的表结构

### 3. AD认证方式错误（核心问题）
- **现象**: `invalidCredentials`错误，无法连接AD服务器
- **原因**: 
  - `connect`方法使用了错误的认证方式（SIMPLE而非NTLM）
  - 用户名格式错误（直接使用email格式而非domain\username格式）
  - 缺少端口和SSL配置

## 解决方案

### 1. 修复AD认证方式

**修改文件**: `backend/app/utils/ad_client.py`

**核心修改**:

1. **用户名格式处理**:
```python
# 构造正确的用户名格式 - NTLM需要domain\username格式
if '@' in self._config['username']:
    # 如果用户名是email格式，提取用户名部分
    actual_username = self._config['username'].split('@')[0]
    username = f"{self._config['domain']}\\{actual_username}"
else:
    # 否则构造域名\用户名格式
    username = f"{self._config['domain']}\\{self._config['username']}"
```

2. **认证方式改为NTLM**:
```python
self._conn = Connection(
    self._server,
    user=username,
    password=self._config['password'],
    authentication=NTLM,  # 改为NTLM认证
    read_only=False,
    auto_bind=False
)
```

3. **添加端口和SSL配置**:
```python
# 从数据库配置中获取端口和SSL设置
port = self._config.get('port', 389)
use_ssl = self._config.get('use_ssl', False)

self._server = Server(
    self._config['server'], 
    port=port,
    use_ssl=use_ssl,
    get_info=ALL
)
```

4. **完善配置加载**:
```python
self._config = {
    'server': config.server,
    'domain': config.domain,
    'username': config.username,
    'password': config.password,
    'search_base': config.search_base,
    'port': config.port,
    'use_ssl': config.use_ssl
}
```

### 2. 创建缺失的表

**SQL语句**:
```sql
CREATE TABLE IF NOT EXISTS ad_sync_locks (
    lock_name VARCHAR PRIMARY KEY,
    is_locked BOOLEAN DEFAULT FALSE,
    locked_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    locked_by VARCHAR
);
```

## 验证结果

✅ **PostgreSQL连接正常** - 数据库迁移成功
✅ **AD认证成功** - 使用正确的NTLM认证方式
✅ **缺失表已补齐** - `ad_sync_locks`表创建成功
✅ **配置加载正确** - 从数据库正确读取完整配置

## 技术要点

1. **AD认证方式选择**:
   - `SIMPLE`: 明文传输，安全性低
   - `NTLM`: Windows域环境标准认证，推荐使用

2. **用户名格式要求**:
   - NTLM认证需要: `domain\username`
   - UPN格式: `<EMAIL>`
   - 需要根据数据库中的格式正确转换

3. **PostgreSQL迁移注意事项**:
   - 字段顺序和约束可能与SQLite不同
   - 需要安装正确的数据库驱动
   - 表结构需要完整迁移

## 后续建议

1. **监控AD连接状态**: 定期检查AD连接健康状况
2. **完善错误处理**: 增加更详细的认证失败日志
3. **配置验证**: 在系统启动时验证AD配置完整性
4. **文档更新**: 更新部署文档，说明PostgreSQL迁移要点

## 影响范围

- ✅ AD管理-用户与组功能恢复正常
- ✅ AD同步功能可正常使用
- ✅ 权限管理功能不受影响
- ✅ 其他模块功能正常

**修复完成时间**: 2025-01-18
**测试状态**: 通过
**部署状态**: 可以部署