"""
Redis缓存配置和策略管理

提供分层TTL配置、缓存键管理和策略选择功能
"""
from enum import Enum
from typing import Dict, Optional, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class CacheDataType(Enum):
    """缓存数据类型枚举"""
    # 静态数据：配置信息、系统设置等，变化频率很低
    STATIC = "static"
    
    # 半静态数据：用户信息、权限数据等，变化频率低
    SEMI_STATIC = "semi_static"
    
    # 业务数据：列表数据、统计信息等，变化频率中等
    BUSINESS = "business"
    
    # 动态数据：同步状态、任务进度等，变化频率高
    DYNAMIC = "dynamic"
    
    # 实时数据：监控指标、在线状态等，变化频率极高
    REALTIME = "realtime"
    
    # 临时数据：会话数据、临时计算结果等
    TEMPORARY = "temporary"


class CacheStrategy(Enum):
    """缓存策略枚举"""
    # 默认策略：标准TTL，标准失效
    DEFAULT = "default"
    
    # 长期策略：长TTL，手动失效
    LONG_TERM = "long_term"
    
    # 短期策略：短TTL，快速失效  
    SHORT_TERM = "short_term"
    
    # 实时策略：极短TTL，立即失效
    REALTIME = "realtime"
    
    # 会话策略：基于用户会话的TTL
    SESSION = "session"


@dataclass
class CacheConfig:
    """单个缓存配置"""
    ttl: int  # 缓存时间（秒）
    data_type: CacheDataType
    strategy: CacheStrategy
    auto_refresh: bool = False  # 是否自动刷新
    dependency_patterns: list = None  # 依赖的缓存模式
    description: str = ""
    
    def __post_init__(self):
        if self.dependency_patterns is None:
            self.dependency_patterns = []


class CacheConfigManager:
    """缓存配置管理器"""
    
    def __init__(self):
        self._configs: Dict[CacheDataType, CacheConfig] = {}
        self._custom_configs: Dict[str, CacheConfig] = {}
        self._setup_default_configs()
    
    def _setup_default_configs(self):
        """设置默认配置"""
        # 静态数据：30分钟
        self._configs[CacheDataType.STATIC] = CacheConfig(
            ttl=1800,  # 30分钟
            data_type=CacheDataType.STATIC,
            strategy=CacheStrategy.LONG_TERM,
            description="系统配置、静态列表等变化频率极低的数据"
        )
        
        # 半静态数据：15分钟
        self._configs[CacheDataType.SEMI_STATIC] = CacheConfig(
            ttl=900,  # 15分钟
            data_type=CacheDataType.SEMI_STATIC,
            strategy=CacheStrategy.LONG_TERM,
            description="用户信息、权限数据等变化频率低的数据"
        )
        
        # 业务数据：5分钟
        self._configs[CacheDataType.BUSINESS] = CacheConfig(
            ttl=300,  # 5分钟
            data_type=CacheDataType.BUSINESS,
            strategy=CacheStrategy.DEFAULT,
            description="业务列表、统计数据等变化频率中等的数据"
        )
        
        # 动态数据：2分钟
        self._configs[CacheDataType.DYNAMIC] = CacheConfig(
            ttl=120,  # 2分钟
            data_type=CacheDataType.DYNAMIC,
            strategy=CacheStrategy.SHORT_TERM,
            description="同步状态、任务进度等变化频率高的数据"
        )
        
        # 实时数据：30秒
        self._configs[CacheDataType.REALTIME] = CacheConfig(
            ttl=30,  # 30秒
            data_type=CacheDataType.REALTIME,
            strategy=CacheStrategy.REALTIME,
            description="监控指标、在线状态等变化频率极高的数据"
        )
        
        # 临时数据：5分钟
        self._configs[CacheDataType.TEMPORARY] = CacheConfig(
            ttl=300,  # 5分钟
            data_type=CacheDataType.TEMPORARY,
            strategy=CacheStrategy.SESSION,
            description="会话数据、临时计算结果等"
        )
    
    def get_config(self, data_type: Union[CacheDataType, str]) -> CacheConfig:
        """获取缓存配置"""
        if isinstance(data_type, str):
            # 首先尝试自定义配置
            if data_type in self._custom_configs:
                return self._custom_configs[data_type]
            # 然后尝试转换为枚举
            try:
                data_type = CacheDataType(data_type)
            except ValueError:
                logger.warning(f"未知的缓存数据类型: {data_type}，使用默认配置")
                return self._configs[CacheDataType.BUSINESS]
        
        return self._configs.get(data_type, self._configs[CacheDataType.BUSINESS])
    
    def get_ttl(self, data_type: Union[CacheDataType, str]) -> int:
        """获取TTL"""
        return self.get_config(data_type).ttl
    
    def register_custom_config(self, key: str, config: CacheConfig):
        """注册自定义配置"""
        self._custom_configs[key] = config
        logger.info(f"注册自定义缓存配置: {key}, TTL: {config.ttl}秒")
    
    def get_all_configs(self) -> Dict[str, CacheConfig]:
        """获取所有配置"""
        result = {}
        # 添加默认配置
        for data_type, config in self._configs.items():
            result[data_type.value] = config
        # 添加自定义配置
        result.update(self._custom_configs)
        return result


class CacheKeyManager:
    """缓存键管理器"""
    
    # 缓存键前缀定义
    PREFIX_REQUEST = "request_cache"  # 请求缓存
    PREFIX_DATA = "data_cache"        # 数据缓存
    PREFIX_SESSION = "session_cache"  # 会话缓存
    PREFIX_TEMP = "temp_cache"        # 临时缓存
    
    # 业务模块前缀
    MODULE_AD = "ad"
    MODULE_EMAIL = "email"
    MODULE_ASSET = "asset"
    MODULE_TERMINAL = "terminal"
    MODULE_USER = "user"
    MODULE_SYSTEM = "system"
    
    @classmethod
    def build_key(cls, 
                  prefix: str, 
                  module: str, 
                  operation: str, 
                  identifier: str = None,
                  user: str = None) -> str:
        """构建标准化缓存键
        
        格式: {prefix}:{module}:{operation}[:{identifier}][:{user}]
        
        Args:
            prefix: 缓存前缀
            module: 业务模块
            operation: 操作类型
            identifier: 标识符（可选）
            user: 用户标识（可选）
        """
        key_parts = [prefix, module, operation]
        
        if identifier:
            key_parts.append(str(identifier))
        
        if user:
            key_parts.append(f"user:{user}")
            
        return ":".join(key_parts)
    
    @classmethod  
    def build_request_key(cls, module: str, operation: str, 
                         identifier: str = None, user: str = None) -> str:
        """构建请求缓存键"""
        return cls.build_key(cls.PREFIX_REQUEST, module, operation, identifier, user)
    
    @classmethod
    def build_data_key(cls, module: str, operation: str, 
                      identifier: str = None) -> str:
        """构建数据缓存键"""
        return cls.build_key(cls.PREFIX_DATA, module, operation, identifier)
    
    @classmethod
    def get_pattern(cls, prefix: str, module: str = None, 
                   operation: str = None) -> str:
        """获取缓存模式匹配字符串
        
        用于批量操作（删除、查询等）
        """
        parts = [prefix]
        if module:
            parts.append(module)
            if operation:
                parts.append(operation)
                parts.append("*")
            else:
                parts.append("*")
        else:
            parts.append("*")
        
        return ":".join(parts)


# 全局缓存配置管理器实例
cache_config_manager = CacheConfigManager()

# 预定义的业务数据类型映射
BUSINESS_DATA_TYPES = {
    # AD模块
    "ad_users": CacheDataType.SEMI_STATIC,
    "ad_groups": CacheDataType.SEMI_STATIC,
    "ad_sync_logs": CacheDataType.DYNAMIC,
    "ad_config": CacheDataType.STATIC,
    
    # 邮箱模块
    "email_members": CacheDataType.BUSINESS,
    "email_departments": CacheDataType.SEMI_STATIC,
    "email_sync_logs": CacheDataType.DYNAMIC,
    "email_config": CacheDataType.STATIC,
    
    # 资产模块
    "assets": CacheDataType.BUSINESS,
    "asset_types": CacheDataType.STATIC,
    "asset_locations": CacheDataType.SEMI_STATIC,
    
    # 终端模块
    "terminals": CacheDataType.BUSINESS,
    "terminal_status": CacheDataType.REALTIME,
    
    # 用户模块
    "users": CacheDataType.SEMI_STATIC,
    "user_permissions": CacheDataType.SEMI_STATIC,
    "user_sessions": CacheDataType.TEMPORARY,
    
    # 系统模块
    "system_config": CacheDataType.STATIC,
    "monitoring_metrics": CacheDataType.REALTIME,
}


def get_data_type_for_business(business_key: str) -> CacheDataType:
    """根据业务键获取数据类型"""
    return BUSINESS_DATA_TYPES.get(business_key, CacheDataType.BUSINESS)


def get_ttl_for_business(business_key: str) -> int:
    """根据业务键获取TTL"""
    data_type = get_data_type_for_business(business_key)
    return cache_config_manager.get_ttl(data_type) 