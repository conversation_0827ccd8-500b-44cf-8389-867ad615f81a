from typing import Any, Dict, Optional, Union
from fastapi import HTTPException
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.crud.base import CRUDBase
from app.models.asset_settings import AssetSettings
from app.schemas.asset_settings import AssetSettingsCreate, AssetSettingsUpdate

class CRUDAssetSettings(CRUDBase[AssetSettings, AssetSettingsCreate, AssetSettingsUpdate]):
    def get_by_company(self, db: Session, *, company: str) -> Optional[AssetSettings]:
        """通过公司获取资产设置"""
        return db.query(AssetSettings).filter(AssetSettings.company == company).first()

    def create(self, db: Session, *, obj_in: AssetSettingsCreate) -> AssetSettings:
        """创建资产设置"""
        # 检查公司是否已存在设置
        if self.get_by_company(db, company=obj_in.company):
            raise HTTPException(
                status_code=400,
                detail=f"公司 {obj_in.company} 的设置已存在"
            )

        try:
            # 将 Pydantic 模型转换为字典
            obj_in_data = obj_in.model_dump()
            db_obj = self.model(**obj_in_data)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            db.rollback()
            raise HTTPException(
                status_code=400,
                detail=f"公司 {obj_in.company} 的设置已存在"
            )

    def update(
        self, db: Session, *, db_obj: AssetSettings, obj_in: Union[AssetSettingsUpdate, Dict[str, Any]]
    ) -> AssetSettings:
        """更新资产设置"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        return super().update(db, db_obj=db_obj, obj_in=update_data)

asset_settings_crud = CRUDAssetSettings(AssetSettings) 