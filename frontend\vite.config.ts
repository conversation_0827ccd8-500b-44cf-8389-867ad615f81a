import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { VantResolver } from '@vant/auto-import-resolver'
import path from 'path'
import pkg from './package.json'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [
        ElementPlusResolver(),
        VantResolver()
      ],
      dts: true
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@mobile': path.resolve(__dirname, './src/mobile')
    },
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
  },
  css: {
    preprocessorOptions: {
      scss: {
        // 移除additionalData，因为CSS文件应该通过其他方式导入
      }
    },
    preprocessorMaxWorkers: true
  },
  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      },
      external: mode === 'production' ? ['@stagewise/toolbar-vue'] : []
    }
  },
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      'element-plus/es',
      '@element-plus/icons-vue',
      'vant',
      'axios',
      '@vueuse/core',
      'echarts',
      'exceljs',
      'file-saver',
      'papaparse',
      'vuedraggable',
      ...(mode === 'development' ? ['@stagewise/toolbar-vue'] : [])
    ],
    exclude: [
      // 排除小型ESM依赖以减少预构建时间
      '@types/node',
      '@types/file-saver',
      '@types/papaparse'
    ],
    force: false,
    holdUntilCrawlEnd: false
  },
  cacheDir: 'node_modules/.vite',
  server: {
    host: '0.0.0.0',
    port: 3000,
    warmup: {
      clientFiles: [
        './src/App.vue',
        './src/main.ts',
        './src/router/index.ts',
        './src/stores/user.ts',
        './src/api/auth.ts',
        './src/layout/index.vue',
        './src/views/dashboard/index.vue'
      ]
    }
  },
  ...(mode === 'development' && {
    define: {
      __DEV__: true
    }
  })
})) 