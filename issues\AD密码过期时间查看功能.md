# AD密码过期时间查看功能

## 任务概述
为当前项目中的AD管理用户与组管理功能，增加可以获取用户密码过期时间的功能，能够查看用户的密码过期时间。

## 实施计划

### 1. 后端核心功能开发 ✅
- 修改 `backend/app/utils/ad_client.py`
- 添加密码过期时间相关属性查询
- 新增密码过期信息计算方法

### 2. 数据模型更新 ✅
- 更新 `backend/app/schemas/ad.py`
- 添加密码过期时间相关字段

### 3. API接口增强 ✅
- 修改 `backend/app/api/v1/ad.py`
- 更新用户列表和详情接口
- 增强导出功能支持密码过期字段

### 4. 前端界面更新 ✅
- 更新用户列表组件
- 更新用户详情组件
- 添加筛选和导出功能

### 5. API接口定义 ✅
- 更新前端TypeScript类型定义

## 技术要点
- 使用AD属性：pwdLastSet, maxPwdAge, userAccountControl
- 计算密码过期时间和剩余天数
- 区分永不过期、已过期、即将过期状态
- 提供直观的视觉提示

## 进度记录
- 开始时间：[当前时间]
- 完成时间：[当前时间]
- 当前状态：已完成

## 实施摘要

### 后端更改
1. **AD客户端增强**：
   - 添加了 `_calculate_password_expiry()` 方法来计算密码过期信息
   - 添加了 `_get_domain_password_policy()` 方法获取域密码策略
   - 在所有用户查询方法中添加密码过期属性查询（pwdLastSet, msDS-UserPasswordExpiryTimeComputed, accountExpires）

2. **数据模型更新**：
   - 在 `ADUserResponse` schema中添加了密码过期相关字段
   - 支持永不过期、已过期、剩余天数等状态

3. **API接口增强**：
   - 用户导出功能现在包含密码过期日期、状态和剩余天数
   - 所有用户相关接口现在返回密码过期信息

### 前端更改
1. **用户列表界面**：
   - 添加了"密码过期"列，显示密码状态和剩余天数
   - 使用不同颜色的标签区分不同过期状态
   - 导出功能增加了密码过期相关字段选项

2. **用户详情界面**：
   - 在基本信息中显示密码过期时间和状态
   - 提供格式化的日期时间显示
   - 直观的状态标签显示

3. **类型定义**：
   - 更新了TypeScript接口定义，包含密码过期相关字段

### 功能特点
- **智能状态判断**：自动区分永不过期、已过期、即将过期（7天内）等状态
- **视觉提示**：使用颜色编码的标签提供直观的状态显示
- **灵活导出**：支持导出密码过期日期、状态和剩余天数
- **兼容性**：向后兼容现有功能，不影响原有用户管理流程

所有功能已实施完成，可以进行测试验证。 