<template>
  <div class="simple-search">
    <div class="search-container">
      <!-- 搜索字段选择器 -->
      <van-dropdown-menu class="search-field-selector">
        <van-dropdown-item
          v-model="searchField"
          :options="searchFieldOptions"
          @change="handleFieldChange"
        />
      </van-dropdown-menu>
      
      <!-- 搜索输入框 -->
      <van-search
        v-model="searchValue"
        :placeholder="getPlaceholder()"
        clearable
        @search="handleSearch"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="handleClear"
        class="search-input"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'

interface Props {
  modelValue?: string
  searchField?: string
  placeholder?: string
  debounceTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  searchField: 'asset_number',
  placeholder: '',
  debounceTime: 300
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:searchField': [field: string]
  'search': [value: string, field: string]
  'input': [value: string, field: string]
  'focus': []
  'blur': []
  'clear': []
}>()

const searchValue = ref(props.modelValue)
const searchField = ref(props.searchField)
const debounceTimer = ref<NodeJS.Timeout | null>(null)

// 搜索字段选项
const searchFieldOptions = [
  { text: '资产编号', value: 'asset_number' },
  { text: '资产名称', value: 'name' },
  { text: '资产类别', value: 'category' },
  { text: '规格型号', value: 'specification' },
  { text: '姓名', value: 'person_name' },
  { text: '工号', value: 'job_number' },
  { text: '部门', value: 'department' }
]

// 获取字段标签
const getFieldLabel = (field: string) => {
  const option = searchFieldOptions.find(opt => opt.value === field)
  return option ? option.text : field
}

// 获取占位符文本
const getPlaceholder = () => {
  if (props.placeholder) return props.placeholder
  
  const fieldLabel = getFieldLabel(searchField.value)
  return `请输入${fieldLabel}...`
}

// 监听搜索值变化
watch(searchValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听搜索字段变化
watch(searchField, (newVal) => {
  emit('update:searchField', newVal)
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  searchValue.value = newVal
})

// 监听外部搜索字段变化
watch(() => props.searchField, (newVal) => {
  searchField.value = newVal
})

// 处理字段变化
const handleFieldChange = (value: string) => {
  searchField.value = value
  // 如果有搜索内容，重新搜索
  if (searchValue.value.trim()) {
    handleSearch(searchValue.value.trim())
  }
}

// 处理输入事件（实时搜索）
const handleInput = (value: string) => {
  emit('input', value, searchField.value)
  
  // 清除之前的定时器
  if (debounceTimer.value) {
    clearTimeout(debounceTimer.value)
  }
  
  // 设置防抖
  debounceTimer.value = setTimeout(() => {
    if (value.trim()) {
      handleSearch(value.trim())
    } else {
      // 如果搜索框为空，触发搜索以显示所有结果
      emit('search', '', searchField.value)
    }
  }, props.debounceTime)
}

// 处理搜索事件
const handleSearch = (value: string) => {
  const trimmedValue = value.trim()
  emit('search', trimmedValue, searchField.value)
}

// 处理焦点事件
const handleFocus = () => {
  emit('focus')
}

// 处理失焦事件
const handleBlur = () => {
  emit('blur')
}

// 处理清空事件
const handleClear = () => {
  emit('clear')
  emit('search', '', searchField.value) // 清空时显示所有结果
}
</script>

<style lang="scss" scoped>
.simple-search {
  background: var(--van-background);
}

.search-container {
  display: flex;
  align-items: center;
  background: var(--van-background);
  gap: 8px;
  height: 40px; // 统一容器高度
}

.search-field-selector {
  flex-shrink: 0;
  width: 90px;
  height: 40px;
  
  :deep(.van-dropdown-menu) {
    height: 40px;
  }
  
  :deep(.van-dropdown-menu__bar) {
    height: 40px;
    box-shadow: none;
    padding: 0;
  }
  
  :deep(.van-dropdown-menu__item) {
    flex: none;
    min-width: 90px;
    height: 40px;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-dropdown-menu__title) {
    font-size: 13px;
    padding: 0 12px;
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: #f7f8fa;
    border: 1px solid #ebedf0;
    margin: 0;
    min-height: 40px;
    box-sizing: border-box;
  }
}

.search-input {
  flex: 1;
  height: 40px;
  
  :deep(.van-search) {
    padding: 0;
    background: transparent;
    height: 40px;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-search__content) {
    background: #f7f8fa;
    border-radius: 8px;
    height: 40px;
    border: 1px solid #ebedf0;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin: 0;
    padding: 0 12px;
  }
  
  :deep(.van-field) {
    background: transparent;
    padding: 0;
    height: 40px;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-field__body) {
    height: 40px;
    display: flex;
    align-items: center;
  }
  
  :deep(.van-field__control) {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding: 0;
    border: none;
    background: transparent;
  }
  
  :deep(.van-field__left-icon) {
    margin-right: 8px;
    display: flex;
    align-items: center;
    height: 40px;
  }
  
  :deep(.van-field__right-icon) {
    margin-left: 8px;
    display: flex;
    align-items: center;
    height: 40px;
  }
  
  :deep(.van-search__action) {
    padding-left: 8px;
    height: 40px;
    display: flex;
    align-items: center;
  }
}
</style> 