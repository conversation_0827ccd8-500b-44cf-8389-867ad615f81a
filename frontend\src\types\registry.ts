/**
 * 注册表管理相关的类型定义
 */

// === 枚举类型 ===

export enum RegistryRootKey {
  HKEY_CLASSES_ROOT = 'HKEY_CLASSES_ROOT',
  HKEY_CURRENT_USER = 'HKEY_CURRENT_USER',
  HKEY_LOCAL_MACHINE = 'HKEY_LOCAL_MACHINE',
  HKEY_USERS = 'HKEY_USERS',
  HKEY_CURRENT_CONFIG = 'HKEY_CURRENT_CONFIG'
}

export enum RegistryValueType {
  REG_SZ = 'REG_SZ',
  REG_EXPAND_SZ = 'REG_EXPAND_SZ',
  REG_BINARY = 'REG_BINARY',
  REG_DWORD = 'REG_DWORD',
  REG_QWORD = 'REG_QWORD',
  REG_MULTI_SZ = 'REG_MULTI_SZ'
}

export enum RegistryOperationType {
  READ = 'REGISTRY_READ',
  WRITE = 'REGISTRY_WRITE',
  DELETE = 'REGISTRY_DELETE',
  CREATE_KEY = 'REGISTRY_CREATE_KEY',
  DELETE_KEY = 'REGISTRY_DELETE_KEY',
  ENUMERATE = 'REGISTRY_ENUMERATE',
  SEARCH = 'REGISTRY_SEARCH',
  BACKUP = 'REGISTRY_BACKUP',
  RESTORE = 'REGISTRY_RESTORE'
}

// === 基础数据接口 ===

export interface RegistryValue {
  name: string
  type: RegistryValueType
  data: string
  size?: number
}

export interface RegistryKey {
  name: string
  full_path: string
  sub_keys: string[]
  values: RegistryValue[]
  last_modified?: number
  sub_key_count: number
  value_count: number
  
  // 分页信息
  current_page?: number
  page_size?: number
  total_sub_keys?: number
  has_more_sub_keys?: boolean
}

// === 操作请求接口 ===

export interface RegistryOperationRequest {
  operation: RegistryOperationType
  root_key: RegistryRootKey
  key_path?: string
  value_name?: string
  value_type?: RegistryValueType
  value_data?: string
  create_backup?: boolean
  backup_reason?: string
  terminal_id?: number  // 添加terminal_id字段，API客户端会自动填充
  
  // 分页参数（用于ENUMERATE操作）
  page?: number
  page_size?: number
  search_filter?: string
}

export interface RegistryBatchOperationRequest {
  operations: RegistryOperationRequest[]
  create_backup?: boolean
  batch_reason?: string
  terminal_id?: number  // 添加terminal_id字段，API客户端会自动填充
}

// === 搜索相关接口 ===

export interface RegistrySearchRequest {
  root_key: RegistryRootKey
  start_path?: string
  search_pattern: string
  search_keys?: boolean
  search_values?: boolean
  search_data?: boolean
  max_depth?: number
  max_results?: number
  terminal_id?: number  // 添加terminal_id字段，API客户端会自动填充
}

export interface RegistrySearchResult {
  path: string
  match_type: string
  match_text: string
  value?: RegistryValue
}

// === 备份相关接口 ===

export interface RegistryBackupRequest {
  root_key: RegistryRootKey
  key_path?: string
  backup_name?: string
  reason?: string
  description?: string
  tags?: string[]
  terminal_id?: number  // 添加terminal_id字段，API客户端会自动填充
}

export interface RegistryRestoreRequest {
  backup_id: string
  target_root_key?: RegistryRootKey
  target_key_path?: string
  create_backup_before_restore?: boolean
  restore_reason?: string
  terminal_id?: number  // 添加terminal_id字段，API客户端会自动填充
}

// === 响应接口 ===

export interface RegistryOperationResponse {
  success: boolean
  operation_id?: number
  command_id?: string
  error?: string
  key_data?: RegistryKey
  value_data?: RegistryValue
  backup_id?: string
  execution_duration?: number
}

export interface RegistryBatchOperationResponse {
  success: boolean
  total_operations: number
  successful_operations: number
  failed_operations: number
  operation_results: RegistryOperationResponse[]
  batch_backup_id?: string
}

export interface RegistrySearchResponse {
  success: boolean
  search_id?: string
  total_results: number
  results: RegistrySearchResult[]
  error?: string
  execution_duration?: number
}

export interface RegistryBackupResponse {
  success: boolean
  backup_id?: string
  backup_name: string
  file_path?: string
  file_size?: number
  file_hash?: string
  error?: string
  created_at?: string
}

// === 历史记录接口 ===

export interface RegistryOperationHistory {
  id: number
  command_id: string
  terminal_id: string
  operation_type: string
  root_key: string
  key_path: string
  value_name?: string
  success: boolean
  error_message?: string
  backup_id?: string
  execution_duration?: number
  created_at: string
}

export interface RegistryBackupHistory {
  id: number
  backup_id: string
  backup_name: string
  terminal_id: string
  root_key: string
  key_path: string
  file_size?: number
  status: string
  reason?: string
  tags?: string
  created_at: string
  expire_at?: string
}

export interface RegistryStatistics {
  total_operations: number
  successful_operations: number
  failed_operations: number
  total_backups: number
  active_backups: number
  total_searches: number
  average_execution_time?: number
  most_accessed_keys: Array<{ key: string; count: number }>
}

// === 查询参数接口 ===

export interface RegistryOperationQuery {
  terminal_id?: number
  operation_type?: string
  root_key?: string
  success?: boolean
  start_date?: string
  end_date?: string
  search_term?: string
  skip?: number
  limit?: number
}

export interface RegistryBackupQuery {
  terminal_id?: number
  root_key?: string
  status?: string
  search_term?: string
  skip?: number
  limit?: number
}

// === 前端组件相关接口 ===

export interface RegistryTreeNode {
  name: string
  path: string
  children?: RegistryTreeNode[]
  isLeaf?: boolean
}

export interface RegistryBrowserState {
  selectedRootKey: RegistryRootKey
  currentPath: string
  currentKeyData: RegistryKey | null
  currentValues: RegistryValue[]
  loading: boolean
  treeLoading: boolean
  valuesLoading: boolean
}

export interface RegistryValueForm {
  name: string
  type: RegistryValueType
  data: string
  isEdit: boolean
  binaryFormat?: 'hex' | 'text'
}

export interface RegistrySearchForm {
  rootKey: RegistryRootKey
  startPath: string
  pattern: string
  searchScope: string[]
  maxDepth: number
  maxResults: number
}

export interface RegistryBackupForm {
  rootKey: RegistryRootKey
  keyPath: string
  reason: string
  backupName?: string
  description?: string
  tags?: string[]
}

// === 权限相关接口 ===

export interface RegistryPermissions {
  read: boolean
  write: boolean
  delete: boolean
  create: boolean
  backup: boolean
  restore: boolean
  batch: boolean
  viewHistory: boolean
  viewBackups: boolean
  viewStats: boolean
  manageBackups: boolean
  verifyBackups: boolean
}

// === 错误处理接口 ===

export interface RegistryError {
  code: string
  message: string
  details?: any
} 