"""add_move_users_with_dept_to_ad_sync_config

Revision ID: 45b2cdbb6492
Revises: 9ea680a206d0
Create Date: 2025-03-20 14:22:50.195911

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '45b2cdbb6492'
down_revision: Union[str, None] = '9ea680a206d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('ad_sync_config', sa.Column('move_users_with_dept', sa.<PERSON>(), nullable=True, server_default='true'))
    # 更新现有行，默认为true
    op.execute("UPDATE ad_sync_config SET move_users_with_dept = true")


def downgrade() -> None:
    op.drop_column('ad_sync_config', 'move_users_with_dept')
