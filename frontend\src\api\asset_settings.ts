import request from '@/utils/request'
import type { AssetSettings, AssetSettingsCreate, AssetSettingsUpdate } from '@/types/asset_settings'

export const assetSettingsApi = {
  // 获取资产设置列表
  getAssetSettings: (params: {
    skip?: number
    limit?: number
  }) => {
    return request.get<AssetSettings[]>('/asset-settings', { params })
  },

  // 获取指定公司的资产设置
  getAssetSettingsByCompany: (company: string) => {
    return request.get<AssetSettings>(`/asset-settings/${company}`)
  },

  // 创建资产设置
  createAssetSettings: (data: AssetSettingsCreate) => {
    return request.post<AssetSettings>('/asset-settings', data)
  },

  // 更新资产设置
  updateAssetSettings: (company: string, data: AssetSettingsUpdate) => {
    return request.put<AssetSettings>(`/asset-settings/${company}`, data)
  },

  // 删除资产设置
  deleteAssetSettings: (company: string) => {
    return request.delete(`/asset-settings/${company}`)
  }
} 