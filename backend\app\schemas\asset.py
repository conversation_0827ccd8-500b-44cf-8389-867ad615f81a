from datetime import datetime
from typing import Optional, Union
from pydantic import BaseModel, Field, field_serializer, field_validator

class AssetBase(BaseModel):
    """固定资产基础模型"""
    company: str = Field(..., description="公司")
    name: str = Field(..., description="资产名称")
    status: str = Field(..., description="资产状态")
    category: Optional[str] = Field(None, description="资产类别")
    specification: str = Field(..., description="资产规格")
    purchase_date: datetime = Field(..., description="入账日期")
    retirement_date: Optional[datetime] = Field(None, description="销账日期")
    custodian: str = Field(..., description="领用人")
    custodian_job_number: Optional[str] = Field(None, description="领用人工号")
    user: str = Field(..., description="使用人")
    user_job_number: Optional[str] = Field(None, description="使用人工号")
    custodian_department: Optional[str] = None
    user_department: Optional[str] = None
    location: Optional[str] = None
    inspector: Optional[str] = None
    inspector_job_number: Optional[str] = Field(None, description="验收人工号")
    remarks: Optional[str] = Field(None, description="备注")
    production_number: Optional[str] = Field(None, description="生产编号")
    price: Optional[float] = Field(None, description="价格")
    supplier: Optional[str] = Field(None, description="供应商")
    manufacturer: Optional[str] = Field(None, description="制造商")
    purchaser: Optional[str] = Field(None, description="采购人")
    purchaser_job_number: Optional[str] = Field(None, description="采购人工号")

    @field_validator('purchase_date', 'retirement_date', mode='before')
    @classmethod
    def parse_date(cls, value):
        if isinstance(value, str):
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                try:
                    return datetime.fromisoformat(value)
                except ValueError:
                    raise ValueError("Invalid date format")
        return value

class AssetCreate(AssetBase):
    """创建固定资产模型"""
    asset_number: Optional[str] = Field(None, description="资产编号，如果不提供则自动生成")

class AssetUpdate(BaseModel):
    """更新固定资产模型"""
    company: Optional[str] = None
    name: Optional[str] = None
    status: Optional[str] = None
    category: Optional[str] = None
    specification: Optional[str] = None
    purchase_date: Optional[datetime] = None
    retirement_date: Optional[datetime] = None
    custodian: Optional[str] = None
    custodian_job_number: Optional[str] = None
    user: Optional[str] = None
    user_job_number: Optional[str] = None
    custodian_department: Optional[str] = None
    user_department: Optional[str] = None
    location: Optional[str] = None
    inspector: Optional[str] = None
    inspector_job_number: Optional[str] = None
    remarks: Optional[str] = None
    production_number: Optional[str] = None
    price: Optional[float] = None
    supplier: Optional[str] = None
    manufacturer: Optional[str] = None
    purchaser: Optional[str] = None
    purchaser_job_number: Optional[str] = None

    @field_validator('purchase_date', 'retirement_date', mode='before')
    @classmethod
    def parse_date(cls, value):
        if isinstance(value, str):
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                try:
                    return datetime.fromisoformat(value)
                except ValueError:
                    raise ValueError("Invalid date format")
        return value

class AssetResponse(AssetBase):
    """固定资产响应模型"""
    id: int
    asset_number: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @field_serializer('created_at', 'updated_at', 'purchase_date', 'retirement_date')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        if value is None:
            return None
        if _info.field_name in ('purchase_date', 'retirement_date'):
            return value.strftime("%Y-%m-%d")
        return value.strftime("%Y-%m-%d %H:%M:%S")

    class Config:
        from_attributes = True

# 为了兼容性，将Asset定义为AssetResponse的别名
Asset = AssetResponse

# 定义AssetInDB作为AssetResponse的别名
AssetInDB = AssetResponse
