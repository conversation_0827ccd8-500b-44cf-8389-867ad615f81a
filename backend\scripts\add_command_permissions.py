#!/usr/bin/env python3
"""
添加命令白名单相关权限

这个脚本会：
1. 添加命令白名单相关的权限
2. 将权限分配给超级管理员角色
3. 更新命令分类的权限要求
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.parent
sys.path.insert(0, str(current_dir))

from app.database import SessionLocal
from app.models import Permission, Role, User
from app.models.command_whitelist import CommandCategory
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def add_command_permissions():
    """添加命令白名单相关权限"""
    
    db = SessionLocal()
    
    try:
        logger.info("开始添加命令白名单权限...")
        
        # 需要添加的权限
        permissions_to_add = [
            {
                "code": "terminal:command:view",
                "name": "查看命令白名单",
                "description": "查看终端命令白名单配置",
                "module": "terminal"
            },
            {
                "code": "terminal:command:manage",
                "name": "管理命令白名单", 
                "description": "管理终端命令白名单，包括添加、编辑、删除",
                "module": "terminal"
            },
            {
                "code": "terminal:command:send",
                "name": "发送自定义命令",
                "description": "向终端发送自定义命令",
                "module": "terminal"
            },
            {
                "code": "terminal:command:basic",
                "name": "基础命令权限",
                "description": "执行基础系统查询命令",
                "module": "terminal"
            },
            {
                "code": "terminal:command:operator",
                "name": "操作员命令权限",
                "description": "执行进程和服务管理命令",
                "module": "terminal"
            }
        ]
        
        added_permissions = []
        
        # 添加权限
        for perm_data in permissions_to_add:
            existing_perm = db.query(Permission).filter(Permission.code == perm_data["code"]).first()
            if not existing_perm:
                new_permission = Permission(
                    code=perm_data["code"],
                    name=perm_data["name"],
                    description=perm_data["description"],
                    module=perm_data["module"]
                )
                db.add(new_permission)
                db.flush()  # 获取ID
                added_permissions.append(new_permission)
                logger.info(f"添加权限: {perm_data['code']} - {perm_data['name']}")
            else:
                added_permissions.append(existing_perm)
                logger.info(f"权限已存在: {perm_data['code']}")
        
        # 获取超级管理员角色
        admin_role = db.query(Role).filter(Role.name == "超级管理员").first()
        if not admin_role:
            logger.warning("未找到超级管理员角色，尝试查找其他管理员角色...")
            admin_role = db.query(Role).filter(Role.code == "super_admin").first()
        
        if admin_role:
            # 为超级管理员角色分配新权限
            for permission in added_permissions:
                if permission not in admin_role.permissions:
                    admin_role.permissions.append(permission)
                    logger.info(f"为{admin_role.name}角色分配权限: {permission.code}")
        else:
            logger.warning("未找到管理员角色，请手动分配权限")
        
        # 提交数据库更改
        db.commit()
        logger.info("权限添加完成!")
        
        # 更新命令分类的权限要求
        update_category_permissions(db)
        
        return True
        
    except Exception as e:
        logger.error(f"添加权限时出错: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def update_category_permissions(db):
    """更新命令分类的权限要求"""
    try:
        logger.info("更新命令分类权限要求...")
        
        # 更新分类权限映射
        category_permission_map = {
            "系统信息": "terminal:command:basic",
            "网络信息": "terminal:command:basic", 
            "进程服务": "terminal:command:operator"
        }
        
        for category_name, required_permission in category_permission_map.items():
            category = db.query(CommandCategory).filter(CommandCategory.name == category_name).first()
            if category:
                category.required_permission = required_permission
                logger.info(f"更新分类 {category_name} 权限要求为: {required_permission}")
        
        db.commit()
        logger.info("分类权限更新完成!")
        
    except Exception as e:
        logger.error(f"更新分类权限时出错: {str(e)}")
        db.rollback()
        raise


if __name__ == "__main__":
    try:
        success = add_command_permissions()
        if success:
            print("✓ 权限添加成功")
        else:
            print("✗ 权限添加失败")
    except Exception as e:
        print(f"✗ 执行失败: {e}") 