# Agent版本管理优化任务

## 背景
Agent打包脚本无法动态获取版本号，存在多个版本源且不一致：
- `pyproject.toml`中定义版本但未被使用
- Agent依赖`default_config.json`或硬编码版本
- 打包脚本通过命令行参数设置版本

## 问题
1. 版本信息分散，难以维护
2. 打包时需要手动指定版本号
3. Agent运行时版本获取逻辑复杂且不可靠

## 解决方案
基于pyproject.toml的中心化版本管理：
- 统一使用pyproject.toml作为版本源
- 打包脚本自动读取版本
- Agent运行时从打包注入的版本文件读取

## 实施计划
1. 修改打包脚本读取pyproject.toml版本
2. 创建版本常量文件机制
3. 修改Agent版本获取逻辑优先级
4. 测试和验证

## 预期效果
- 版本信息统一管理
- 打包过程自动化
- Agent版本显示准确
- 版本更新机制稳定 