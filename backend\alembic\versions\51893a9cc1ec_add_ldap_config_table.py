"""add_ldap_config_table

Revision ID: 51893a9cc1ec
Revises: bd393a92e5a7
Create Date: 2025-06-20 11:40:05.001896

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '51893a9cc1ec'
down_revision: Union[str, None] = 'bd393a92e5a7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ldap_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='配置名称'),
    sa.Column('server', sa.String(length=255), nullable=False, comment='LDAP服务器地址'),
    sa.Column('port', sa.Integer(), nullable=True, comment='LDAP服务器端口'),
    sa.Column('use_ssl', sa.Boolean(), nullable=True, comment='是否使用SSL'),
    sa.Column('base_dn', sa.String(length=500), nullable=False, comment='Base DN'),
    sa.Column('bind_dn', sa.String(length=500), nullable=True, comment='绑定用户DN'),
    sa.Column('bind_password', sa.String(length=255), nullable=True, comment='绑定用户密码'),
    sa.Column('user_search_base', sa.String(length=500), nullable=True, comment='用户搜索Base DN'),
    sa.Column('user_search_filter', sa.String(length=200), nullable=True, comment='用户搜索过滤器'),
    sa.Column('user_name_attr', sa.String(length=50), nullable=True, comment='用户名属性'),
    sa.Column('user_email_attr', sa.String(length=50), nullable=True, comment='邮箱属性'),
    sa.Column('user_display_name_attr', sa.String(length=50), nullable=True, comment='显示名称属性'),
    sa.Column('auto_create_user', sa.Boolean(), nullable=True, comment='自动创建用户'),
    sa.Column('default_role', sa.String(length=50), nullable=True, comment='默认角色'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('is_default', sa.Boolean(), nullable=True, comment='是否为默认配置'),
    sa.Column('description', sa.Text(), nullable=True, comment='配置描述'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ldap_config_id'), 'ldap_config', ['id'], unique=False)
    op.drop_table('ad_sync_locks')
    op.drop_index(op.f('ix_asset_settings_id'), table_name='asset_settings')
    op.drop_table('asset_settings')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='公司'),
    sa.Column('asset_number_rule', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False, comment='资产编号规则'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('asset_settings_pkey')),
    sa.UniqueConstraint('company', name=op.f('asset_settings_company_key'))
    )
    op.create_index(op.f('ix_asset_settings_id'), 'asset_settings', ['id'], unique=False)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
    sa.Column('locked_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('lock_name', name=op.f('ad_sync_locks_pkey'))
    )
    op.drop_index(op.f('ix_ldap_config_id'), table_name='ldap_config')
    op.drop_table('ldap_config')
    # ### end Alembic commands ###
