export interface AssetNumberRule {
  prefix: string
  number_length: number
  start_number: number
}

export interface AssetSettings {
  id: number
  company: string
  asset_number_rule: AssetNumberRule
  created_at: string
  updated_at: string
}

export type AssetSettingsCreate = Omit<AssetSettings, 'id' | 'created_at' | 'updated_at'>

export type AssetSettingsUpdate = Partial<AssetSettingsCreate>

export const ELEMENT_TYPE_OPTIONS = [
  { label: '固定值', value: 'fixed' },
  { label: '日期', value: 'date' },
  { label: '序号', value: 'sequence' },
  { label: '数字', value: 'number' },
  { label: '公司', value: 'company' },
  { label: '部门', value: 'department' }
] as const

export const DATE_FORMAT_OPTIONS = [
  { label: '年月日 (YYYYMMDD)', value: 'YYYYMMDD' },
  { label: '年月 (YYYYMM)', value: 'YYYYMM' },
  { label: '年 (YYYY)', value: 'YYYY' }
] as const 