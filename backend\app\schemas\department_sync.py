"""
部门结构同步相关的数据模式
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field


class DepartmentSyncSource(str, Enum):
    """部门同步数据源"""
    ALL = "all"           # 全部
    COMPANY = "company"   # 按公司
    DEPARTMENT = "department"  # 按部门


class DepartmentSyncMode(str, Enum):
    """部门同步模式"""
    CREATE_ONLY = "create_only"      # 仅创建新部门
    CREATE_UPDATE = "create_update"  # 创建和更新
    FULL_SYNC = "full_sync"          # 完全同步（包括删除）


class DepartmentSyncRequest(BaseModel):
    """部门结构同步请求"""
    source: DepartmentSyncSource = Field(default=DepartmentSyncSource.ALL, description="同步数据源")
    company_id: Optional[int] = Field(None, description="公司ID（当source为company时必需）")
    department_id: Optional[int] = Field(None, description="部门ID（当source为department时必需）")
    mode: DepartmentSyncMode = Field(default=DepartmentSyncMode.CREATE_ONLY, description="同步模式")
    dry_run: bool = Field(default=False, description="是否为试运行模式")
    overwrite_existing: bool = Field(default=False, description="是否覆盖已存在的部门")
    create_hierarchy: bool = Field(default=True, description="是否创建完整的部门层级")
    skip_empty_departments: bool = Field(default=True, description="是否跳过无在职人员的部门")


class DepartmentInfo(BaseModel):
    """部门信息"""
    dept_id: int = Field(..., description="部门ID")
    dept_name: str = Field(..., description="部门名称")
    dept_hierarchy: Optional[str] = Field(None, description="部门层级路径")
    company_id: Optional[int] = Field(None, description="公司ID")
    company_name: Optional[str] = Field(None, description="公司名称")
    parent_dept_id: Optional[int] = Field(None, description="父部门ID")
    level: int = Field(default=1, description="部门层级深度")


class DepartmentSyncOperation(BaseModel):
    """部门同步操作记录"""
    dept_info: DepartmentInfo = Field(..., description="部门信息")
    operation: str = Field(..., description="操作类型: create/update/skip/error")
    tencent_dept_id: Optional[str] = Field(None, description="腾讯企业邮箱部门ID")
    message: str = Field(..., description="操作结果消息")
    error_code: Optional[int] = Field(None, description="错误码")
    parent_tencent_id: Optional[str] = Field(None, description="父部门腾讯ID")


class DepartmentSyncStats(BaseModel):
    """部门同步统计信息"""
    total_departments: int = Field(default=0, description="总部门数")
    created_departments: int = Field(default=0, description="创建的部门数")
    updated_departments: int = Field(default=0, description="更新的部门数")
    skipped_departments: int = Field(default=0, description="跳过的部门数")
    failed_departments: int = Field(default=0, description="失败的部门数")
    invalid_departments: int = Field(default=0, description="无效部门数（无在职人员或已弃用）")
    total_companies: int = Field(default=0, description="涉及的公司数")
    success_rate: float = Field(default=0.0, description="成功率")


class DepartmentSyncResult(BaseModel):
    """部门结构同步结果"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    stats: DepartmentSyncStats = Field(..., description="统计信息")
    operations: List[DepartmentSyncOperation] = Field(default_factory=list, description="操作详情")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
    duration: float = Field(default=0.0, description="执行耗时（秒）")
    start_time: datetime = Field(default_factory=datetime.now, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")


class DepartmentHierarchy(BaseModel):
    """部门层级结构"""
    dept_id: int = Field(..., description="部门ID")
    dept_name: str = Field(..., description="部门名称")
    parent_id: Optional[int] = Field(None, description="父部门ID")
    children: List['DepartmentHierarchy'] = Field(default_factory=list, description="子部门列表")
    level: int = Field(default=1, description="层级深度")
    tencent_dept_id: Optional[str] = Field(None, description="腾讯企业邮箱部门ID")


class DepartmentMapping(BaseModel):
    """部门名称映射"""
    ecology_name: str = Field(..., description="泛微系统部门名称")
    tencent_name: str = Field(..., description="腾讯企业邮箱部门名称")
    reason: Optional[str] = Field(None, description="映射原因")


# 允许前向引用
DepartmentHierarchy.model_rebuild() 