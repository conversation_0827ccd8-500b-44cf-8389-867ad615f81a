<template>
  <div class="command-whitelist-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>白名单命令管理</h3>
          <div class="header-actions">
            <el-button type="success" @click="handleInitDefaultCommands" :loading="initLoading">
              <el-icon><Download /></el-icon>
              初始化默认命令
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab">
        <!-- 命令分类管理 -->
        <el-tab-pane label="命令分类" name="categories">
          <div class="tab-content">
            <div class="table-header">
              <div class="search-bar">
                <el-input
                  v-model="categoriesFilter.search"
                  placeholder="搜索分类名称"
                  style="width: 300px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click="fetchCategories" style="margin-left: 10px">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
              </div>
              <el-button type="primary" @click="showCreateCategoryDialog">
                <el-icon><Plus /></el-icon>
                新增分类
              </el-button>
            </div>

            <el-table :data="categories" v-loading="categoriesLoading" stripe>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="name" label="分类名称" min-width="150" />
              <el-table-column prop="description" label="描述" min-width="200" />
              <el-table-column prop="is_active" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.is_active ? 'success' : 'danger'">
                    {{ row.is_active ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="editCategory(row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteCategory(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 命令白名单管理 -->
        <el-tab-pane label="白名单命令" name="commands">
          <div class="tab-content">
            <div class="table-header">
              <div class="search-bar">
                <el-input
                  v-model="commandsFilter.search"
                  placeholder="搜索命令"
                  style="width: 250px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click="fetchCommands" style="margin-left: 10px">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
              </div>
              <el-button type="primary" @click="showCreateCommandDialog">
                <el-icon><Plus /></el-icon>
                新增命令
              </el-button>
            </div>

            <el-table :data="commands" v-loading="commandsLoading" stripe>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="command" label="命令" min-width="200" />
              <el-table-column prop="description" label="描述" min-width="180" />
              <el-table-column prop="security_level" label="安全级别" width="100">
                <template #default="{ row }">
                  <el-tag :type="getSecurityLevelType(row.security_level)">
                    {{ row.security_level }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="editCommand(row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteCommand(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="categoryDialog.visible"
      :title="categoryDialog.isEdit ? '编辑分类' : '新增分类'"
      width="500px"
    >
      <el-form :model="categoryDialog.form" label-width="100px">
        <el-form-item label="分类名称">
          <el-input v-model="categoryDialog.form.name" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="categoryDialog.form.description" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="categoryDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitCategory">确认</el-button>
      </template>
    </el-dialog>

    <!-- 命令编辑对话框 -->
    <el-dialog
      v-model="commandDialog.visible"
      :title="commandDialog.isEdit ? '编辑命令' : '新增命令'"
      width="700px"
    >
      <el-form :model="commandDialog.form" label-width="120px">
        <el-form-item label="命令内容">
          <el-input v-model="commandDialog.form.command" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="commandDialog.form.description" type="textarea" />
        </el-form-item>
        <el-form-item label="所属分类">
          <el-select v-model="commandDialog.form.category_id" style="width: 100%">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="安全级别">
          <el-select v-model="commandDialog.form.security_level" style="width: 100%">
            <el-option label="PUBLIC" value="PUBLIC" />
            <el-option label="OPERATOR" value="OPERATOR" />
            <el-option label="ADMIN" value="ADMIN" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="commandDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitCommand">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Search, Download } from '@element-plus/icons-vue'
import { commandWhitelistApi } from '@/api/command-whitelist'
import type { CommandCategory, CommandWhitelist } from '@/types/command'

// 响应式数据
const activeTab = ref('categories')
const initLoading = ref(false)

// 分类相关
const categories = ref<CommandCategory[]>([])
const categoriesLoading = ref(false)
const categoriesFilter = reactive({
  search: ''
})

// 命令相关  
const commands = ref<CommandWhitelist[]>([])
const commandsLoading = ref(false)
const commandsFilter = reactive({
  search: ''
})

// 分类对话框
const categoryDialog = reactive({
  visible: false,
  isEdit: false,
  form: {
    name: '',
    description: '',
    required_permission: 'command:category:manage'
  },
  editId: 0
})

// 命令对话框
const commandDialog = reactive({
  visible: false,
  isEdit: false,
  form: {
    category_id: 0,
    command: '',
    name: '',
    description: '',
    security_level: 'PUBLIC' as 'PUBLIC' | 'OPERATOR' | 'ADMIN'
  },
  editId: 0
})

// 方法
const fetchCategories = async () => {
  categoriesLoading.value = true
  try {
    const response = await commandWhitelistApi.getCategories()
    categories.value = response.data
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  } finally {
    categoriesLoading.value = false
  }
}

const fetchCommands = async () => {
  commandsLoading.value = true
  try {
    const response = await commandWhitelistApi.getCommands()
    commands.value = response.data
  } catch (error) {
    ElMessage.error('获取命令列表失败')
  } finally {
    commandsLoading.value = false
  }
}

const showCreateCategoryDialog = () => {
  categoryDialog.isEdit = false
  categoryDialog.form = { name: '', description: '', required_permission: 'command:category:manage' }
  categoryDialog.visible = true
}

const editCategory = (category: CommandCategory) => {
  categoryDialog.isEdit = true
  categoryDialog.editId = category.id
  categoryDialog.form = {
    name: category.name,
    description: category.description || '',
    required_permission: category.required_permission
  }
  categoryDialog.visible = true
}

const submitCategory = async () => {
  try {
    if (categoryDialog.isEdit) {
      await commandWhitelistApi.updateCategory(categoryDialog.editId, categoryDialog.form)
    } else {
      await commandWhitelistApi.createCategory(categoryDialog.form)
    }
    categoryDialog.visible = false
    await fetchCategories()
    ElMessage.success('操作成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteCategory = async (category: CommandCategory) => {
  try {
    await commandWhitelistApi.deleteCategory(category.id)
    await fetchCategories()
    ElMessage.success('删除成功')
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const showCreateCommandDialog = () => {
  commandDialog.isEdit = false
  commandDialog.form = {
    category_id: 0,
    command: '',
    name: '',
    description: '',
    security_level: 'PUBLIC'
  }
  commandDialog.visible = true
}

const editCommand = (command: CommandWhitelist) => {
  commandDialog.isEdit = true
  commandDialog.editId = command.id
  commandDialog.form = {
    category_id: command.category_id,
    command: command.command,
    name: command.name,
    description: command.description || '',
    security_level: command.security_level as 'PUBLIC' | 'OPERATOR' | 'ADMIN'
  }
  commandDialog.visible = true
}

const submitCommand = async () => {
  try {
    if (commandDialog.isEdit) {
      await commandWhitelistApi.updateCommand(commandDialog.editId, commandDialog.form)
    } else {
      await commandWhitelistApi.createCommand(commandDialog.form)
    }
    commandDialog.visible = false
    await fetchCommands()
    ElMessage.success('操作成功')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteCommand = async (command: CommandWhitelist) => {
  try {
    await commandWhitelistApi.deleteCommand(command.id)
    await fetchCommands()
    ElMessage.success('删除成功')
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const handleInitDefaultCommands = async () => {
  initLoading.value = true
  try {
    await commandWhitelistApi.initializeDefaultCommands()
    await Promise.all([fetchCategories(), fetchCommands()])
    ElMessage.success('默认命令初始化成功')
  } catch (error) {
    ElMessage.error('初始化失败')
  } finally {
    initLoading.value = false
  }
}

const getSecurityLevelType = (level: string) => {
  switch (level) {
    case 'PUBLIC': return 'success'
    case 'OPERATOR': return 'warning'
    case 'ADMIN': return 'danger'
    default: return 'info'
  }
}

// 生命周期
onMounted(() => {
  fetchCategories()
  fetchCommands()
})
</script>

<style scoped>
.command-whitelist-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.tab-content {
  padding-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
}
</style> 