from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, not_
from datetime import datetime, timedelta
import uuid
import logging

from app import models
from app.schemas import terminal as schemas

# 配置日志
logger = logging.getLogger(__name__)

def get_terminal(db: Session, terminal_id: int) -> Optional[models.Terminal]:
    """根据 ID 获取终端"""
    return db.query(models.Terminal).filter(models.Terminal.id == terminal_id).first()

def get_terminal_detail(db: Session, terminal_id: int) -> Optional[models.Terminal]:
    """根据 ID 获取终端详情，包含所有关联数据"""
    from sqlalchemy.orm import joinedload
    
    # 预加载所有关联关系
    return db.query(models.Terminal).options(
        joinedload(models.Terminal.hardware_info).joinedload(models.HardwareInfo.disks),
        joinedload(models.Terminal.os_info_detail).joinedload(models.OSInfo.security_info),
        joinedload(models.Terminal.network_info_detail).joinedload(models.NetworkInfo.interfaces),
        joinedload(models.Terminal.last_login_user)
    ).filter(models.Terminal.id == terminal_id).first()

def get_terminal_by_unique_id(db: Session, unique_id: str) -> Optional[models.Terminal]:
    """根据唯一标识获取终端"""
    # unique_id 格式为 "terminal_{id}"，提取 id 部分
    if unique_id.startswith("terminal_"):
        try:
            terminal_id = int(unique_id.replace("terminal_", ""))
            return db.query(models.Terminal).filter(models.Terminal.id == terminal_id).first()
        except ValueError:
            return None
    return None

def get_terminals(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    online_status: Optional[bool] = None,
    hostname: Optional[str] = None,
    os_name: Optional[str] = None,
    ip_address: Optional[str] = None
) -> List[models.Terminal]:
    """获取终端列表，支持分页和过滤"""
    from sqlalchemy.orm import joinedload
    
    # 预加载关联关系，确保可以访问os_info_detail
    query = db.query(models.Terminal).options(
        joinedload(models.Terminal.os_info_detail)
    )

    # 应用过滤条件
    if online_status is not None:
        status_value = "online" if online_status else "offline"
        query = query.filter(models.Terminal.status == status_value)

    if hostname:
        query = query.filter(models.Terminal.hostname.like(f"%{hostname}%"))

    if ip_address:
        query = query.filter(models.Terminal.ip_address.like(f"%{ip_address}%"))

    if os_name:
        query = query.join(models.OSInfo).filter(models.OSInfo.name.like(f"%{os_name}%"))

    return query.offset(skip).limit(limit).all()

def create_terminal(db: Session, terminal: schemas.TerminalCreate) -> models.Terminal:
    """创建新终端"""
    db_terminal = models.Terminal(
        hostname=terminal.hostname,
        mac_address=terminal.mac_address,
        ip_address=terminal.ip_address,
        agent_version=terminal.agent_version,
        status="online",
        last_heartbeat=datetime.utcnow()
    )
    db.add(db_terminal)
    db.commit()
    db.refresh(db_terminal)
    return db_terminal

def update_terminal(
    db: Session,
    terminal_id: int,
    terminal_update: schemas.TerminalUpdate
) -> Optional[models.Terminal]:
    """更新终端信息"""
    db_terminal = get_terminal(db, terminal_id)
    if not db_terminal:
        return None

    # 更新字段
    update_data = terminal_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_terminal, key, value)

    db.commit()
    db.refresh(db_terminal)
    return db_terminal

def delete_terminal(db: Session, terminal_id: int) -> bool:
    """删除终端"""
    db_terminal = get_terminal(db, terminal_id)
    if not db_terminal:
        return False

    db.delete(db_terminal)
    db.commit()
    return True

def update_terminal_online_status(db: Session) -> int:
    """
    更新终端在线状态
    将超过一定时间未心跳的终端标记为离线
    返回更新的终端数量
    """
    # 设置超时时间，如15分钟（心跳间隔300秒的3倍）
    timeout = datetime.utcnow() - timedelta(minutes=15)

    # 查找所有在线但心跳时间超过超时时间的终端
    terminals = db.query(models.Terminal).filter(
        models.Terminal.status == "online",
        models.Terminal.last_heartbeat < timeout
    ).all()

    # 更新为离线状态
    for terminal in terminals:
        terminal.status = "offline"
        terminal.last_offline_time = datetime.utcnow()  # 设置最后离线时间
        terminal.updated_at = datetime.utcnow()

    db.commit()
    return len(terminals)

def get_terminal_stats(db: Session) -> Dict[str, Any]:
    """获取终端统计信息"""
    # 获取终端总数
    total = db.query(func.count(models.Terminal.id)).scalar()

    # 获取在线和离线终端数
    online = db.query(func.count(models.Terminal.id)).filter(
        models.Terminal.status == "online"
    ).scalar()

    offline = total - online

    # 获取Windows终端数量
    windows_count = db.query(func.count(models.Terminal.id)).join(
        models.OSInfo
    ).filter(
        models.OSInfo.name.like("%Windows%")
    ).scalar()

    # 获取Windows版本分布
    windows_versions = {}
    versions = db.query(
        models.OSInfo.version,
        func.count(models.OSInfo.version)
    ).filter(
        models.OSInfo.name.like("%Windows%")
    ).group_by(
        models.OSInfo.version
    ).all()

    for version, count in versions:
        windows_versions[version] = count

    # 获取硬件统计信息
    # 例如：内存分布、CPU核心数分布等
    memory_stats = db.query(
        func.sum(models.HardwareInfo.memory_total)
    ).scalar()

    cpu_cores_distribution = {}
    cores = db.query(
        models.HardwareInfo.cpu_cores,
        func.count(models.HardwareInfo.cpu_cores)
    ).group_by(
        models.HardwareInfo.cpu_cores
    ).all()

    for core, count in cores:
        cpu_cores_distribution[str(core)] = count

    # 组装统计信息
    stats = {
        "total": total,
        "online": online,
        "offline": offline,
        "windows_count": windows_count,
        "windows_versions": windows_versions,
        "hardware_stats": {
            "total_memory_kb": memory_stats,
            "cpu_cores_distribution": cpu_cores_distribution
        }
    }

    return stats

def get_terminal_software(db: Session, terminal_id: int) -> List[models.Software]:
    """获取终端安装的软件列表"""
    # 查询数据库中该终端的所有软件
    all_software = db.query(models.Software).filter(
        models.Software.terminal_id == terminal_id
    ).all()

    # 按名称和版本对软件进行分组
    grouped_software = {}
    for software in all_software:
        key = (software.name, software.version or "")
        if key not in grouped_software:
            grouped_software[key] = software

    # 返回分组后的软件列表
    return list(grouped_software.values())

def create_terminal_command(
    db: Session,
    command: schemas.TerminalCommandCreate
) -> models.TerminalCommand:
    """创建终端命令"""
    db_command = models.TerminalCommand(
        # id会自动生成（使用integer主键）
        terminal_id=command.terminal_id,
        type=command.type,
        content=command.content,
        timeout=command.timeout,
        status="pending"
    )
    db.add(db_command)
    db.commit()
    db.refresh(db_command)

    # 在命令创建后，尝试通知终端
    from app.services.notify_service import NotifyService
    try:
        # 异步通知终端（不影响主流程）
        import threading
        thread = threading.Thread(
            target=NotifyService.notify_terminal,
            args=(str(command.terminal_id), str(db_command.id))
        )
        thread.daemon = True
        thread.start()
    except Exception as e:
        logger.warning(f"启动终端通知失败: {str(e)}")

    return db_command

def get_terminal_command(db: Session, command_id: int) -> Optional[models.TerminalCommand]:
    """根据ID获取命令"""
    return db.query(models.TerminalCommand).filter(
        models.TerminalCommand.id == command_id
    ).first()

def get_terminal_commands(
    db: Session,
    terminal_id: int,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[models.TerminalCommand]:
    """获取终端的命令列表"""
    query = db.query(models.TerminalCommand).filter(
        models.TerminalCommand.terminal_id == terminal_id
    )

    if status:
        query = query.filter(models.TerminalCommand.status == status)

    return query.order_by(models.TerminalCommand.create_time.desc()).offset(skip).limit(limit).all()

def get_command_stats(db: Session) -> Dict[str, int]:
    """获取命令统计信息"""
    total = db.query(func.count(models.TerminalCommand.id)).scalar()

    pending = db.query(func.count(models.TerminalCommand.id)).filter(
        models.TerminalCommand.status == "pending"
    ).scalar()

    sent = db.query(func.count(models.TerminalCommand.id)).filter(
        models.TerminalCommand.status == "sent"
    ).scalar()

    executed = db.query(func.count(models.TerminalCommand.id)).filter(
        models.TerminalCommand.status == "executed"
    ).scalar()

    failed = db.query(func.count(models.TerminalCommand.id)).filter(
        models.TerminalCommand.status == "failed"
    ).scalar()

    timeout = db.query(func.count(models.TerminalCommand.id)).filter(
        models.TerminalCommand.status == "timeout"
    ).scalar()

    return {
        "total": total,
        "pending": pending,
        "sent": sent,
        "executed": executed,
        "failed": failed,
        "timeout": timeout
    }

def get_agent_versions(
    db: Session,
    platform: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[models.AgentVersion]:
    """获取Agent版本列表"""
    query = db.query(models.AgentVersion)

    if platform:
        query = query.filter(models.AgentVersion.platform == platform)

    return query.order_by(models.AgentVersion.upload_time.desc()).offset(skip).limit(limit).all()

def get_agent_version(db: Session, version_id: int) -> Optional[models.AgentVersion]:
    """根据ID获取Agent版本"""
    return db.query(models.AgentVersion).filter(models.AgentVersion.id == version_id).first()

def create_agent_version(db: Session, version: schemas.AgentVersionCreate) -> models.AgentVersion:
    """创建新的Agent版本"""
    # 如果设置为当前版本，需要先将同平台的其他版本设置为非当前版本
    if getattr(version, 'is_current', False):
        db.query(models.AgentVersion).filter(
            models.AgentVersion.platform == version.platform,
            models.AgentVersion.is_current == True
        ).update({"is_current": False})

    db_version = models.AgentVersion(
        # id会自动生成（使用integer主键）
        version=version.version,
        platform=version.platform,
        file_name=version.file_name,
        file_size=version.file_size,
        download_url=version.download_url,
        release_notes=version.release_notes,
        is_current=getattr(version, 'is_current', False)
    )

    db.add(db_version)
    db.commit()
    db.refresh(db_version)
    return db_version

def set_current_agent_version(db: Session, version_id: int) -> Optional[models.AgentVersion]:
    """设置当前版本"""
    db_version = get_agent_version(db, version_id)
    if not db_version:
        return None

    # 将同平台的其他版本设置为非当前版本
    db.query(models.AgentVersion).filter(
        models.AgentVersion.platform == db_version.platform,
        models.AgentVersion.is_current == True
    ).update({"is_current": False})

    # 设置为当前版本
    db_version.is_current = True
    db.commit()
    db.refresh(db_version)
    return db_version

def get_current_agent_version(db: Session, platform: str) -> Optional[models.AgentVersion]:
    """获取指定平台的当前Agent版本"""
    return db.query(models.AgentVersion).filter(
        models.AgentVersion.platform == platform,
        models.AgentVersion.is_current == True
    ).first()

def delete_agent_version(db: Session, version_id: int) -> bool:
    """删除Agent版本"""
    db_version = get_agent_version(db, version_id)
    if not db_version:
        return False

    db.delete(db_version)
    db.commit()
    return True

def get_software_list(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    is_compliant: Optional[bool] = None
) -> Dict[str, Any]:
    """
    获取所有终端中的软件列表，并统计安装了该软件的终端数量
    """
    # 基础查询: 按软件名称和版本分组，并计算每种软件安装的终端数量（使用distinct去重）
    base_query = db.query(
        models.Software.name,
        models.Software.version,
        func.count(func.distinct(models.Software.terminal_id)).label("terminal_count"),
        # 对于布尔字段，使用bool_or聚合函数
        func.bool_or(models.Software.is_compliant).label("is_compliant"),
        func.max(models.Software.usage_notes).label("usage_notes")
    ).group_by(
        models.Software.name,
        models.Software.version
    )

    # 应用过滤条件
    if name:
        base_query = base_query.filter(models.Software.name.like(f"%{name}%"))

    if is_compliant is not None:
        base_query = base_query.having(func.bool_or(models.Software.is_compliant) == is_compliant)

    # 先获取总数
    total_count = base_query.count()

    # 执行带分页的查询并获取结果
    results = base_query.order_by(models.Software.name).offset(skip).limit(limit).all()

    # 转换为字典列表
    software_list = []
    for name, version, terminal_count, is_compliant, usage_notes in results:
        software_list.append({
            "name": name,
            "version": version,
            "terminal_count": terminal_count,
            "is_compliant": is_compliant,
            "usage_notes": usage_notes
        })

    return {
        "items": software_list,
        "total": total_count
    }

def get_software_detail(
    db: Session,
    name: str,
    version: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取特定软件的详细信息，包括安装该软件的终端列表
    """
    # 构建查询条件
    conditions = [models.Software.name == name]
    if version:
        conditions.append(models.Software.version == version)

    # 查询安装了此软件的终端ID列表
    terminal_ids = db.query(models.Software.terminal_id).filter(
        *conditions
    ).distinct().all()
    terminal_ids = [t[0] for t in terminal_ids]

    # 查询终端详细信息，预加载os_info_detail关联关系
    from sqlalchemy.orm import joinedload
    terminals = db.query(models.Terminal).options(
        joinedload(models.Terminal.os_info_detail)
    ).filter(
        models.Terminal.id.in_(terminal_ids)
    ).all()

    # 获取软件属性（取第一个匹配的软件记录）
    software = db.query(models.Software).filter(*conditions).first()
    is_compliant = software.is_compliant if software else True
    usage_notes = software.usage_notes if software else None

    # 构建结果
    result = {
        "name": name,
        "version": version,
        "terminals": terminals,
        "is_compliant": is_compliant,
        "usage_notes": usage_notes
    }

    return result