from datetime import datetime
from typing import Optional, List
from sqlalchemy import String, DateTime, Integer, Text, JSON, ForeignKey, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models import Base

class CustomField(Base):
    """自定义字段定义模型"""
    __tablename__ = "custom_fields"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), comment="字段名称")
    label: Mapped[str] = mapped_column(String(200), comment="字段显示标签")
    field_type: Mapped[str] = mapped_column(String(50), comment="字段类型")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="字段描述")
    is_required: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否必填")
    default_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="默认值")
    options: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="字段选项配置")
    validation_rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True, comment="验证规则")
    sort_order: Mapped[int] = mapped_column(Integer, default=0, comment="排序顺序")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否启用")
    
    # 字段适用范围：asset、inventory_record 或 both
    applies_to: Mapped[str] = mapped_column(String(50), default="both", comment="适用范围")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    # 关联的字段值
    asset_values = relationship("AssetCustomFieldValue", back_populates="custom_field", cascade="all, delete-orphan")
    inventory_values = relationship("InventoryRecordCustomFieldValue", back_populates="custom_field", cascade="all, delete-orphan")

class AssetCustomFieldValue(Base):
    """资产自定义字段值模型"""
    __tablename__ = "asset_custom_field_values"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    asset_id: Mapped[int] = mapped_column(ForeignKey("assets.id"), comment="资产ID")
    custom_field_id: Mapped[int] = mapped_column(ForeignKey("custom_fields.id"), comment="自定义字段ID")
    value: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="字段值")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    # 关联
    asset = relationship("Asset", lazy='joined')
    custom_field = relationship("CustomField", back_populates="asset_values")

class InventoryRecordCustomFieldValue(Base):
    """盘点记录自定义字段值模型"""
    __tablename__ = "inventory_record_custom_field_values"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    inventory_record_id: Mapped[Optional[int]] = mapped_column(ForeignKey("inventory_records.id"), nullable=True, comment="盘点记录ID")
    # 支持虚拟记录：task_id + asset_id 组合
    task_id: Mapped[Optional[int]] = mapped_column(ForeignKey("inventory_tasks.id"), nullable=True, comment="盘点任务ID")
    asset_id: Mapped[Optional[int]] = mapped_column(ForeignKey("assets.id"), nullable=True, comment="资产ID")
    custom_field_id: Mapped[int] = mapped_column(ForeignKey("custom_fields.id"), comment="自定义字段ID")
    value: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="字段值")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间"
    )

    # 关联
    inventory_record = relationship("InventoryRecord", lazy='joined', overlaps="custom_field_values")
    task = relationship("InventoryTask", lazy='joined', overlaps="virtual_custom_field_values")
    asset = relationship("Asset", lazy='joined')
    custom_field = relationship("CustomField", back_populates="inventory_values") 