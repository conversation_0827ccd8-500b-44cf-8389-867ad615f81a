from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, List
import logging
from app.models.email_sync_lock import EmailSyncLock

logger = logging.getLogger(__name__)

# 定义锁类型常量
LOCK_TYPES = {
    "CACHE_SYNC": "cache_sync",           # 缓存同步（腾讯→本地）
    "EXTID_COMPLETION": "extid_completion", # 工号补全
    "PERSONNEL_SYNC": "personnel_sync",   # 人员同步（泛微→腾讯）
    "CONSISTENCY_CHECK": "consistency_check", # 数据一致性检查
    "FULL_SYNC": "full_sync"             # 全量同步
}

async def acquire_lock(db: Session, lock_name: str, operation_type: str, lock_owner: str = "system", timeout_seconds: int = 1800) -> bool:
    """
    尝试获取邮箱同步锁，支持过期锁自动清理
    
    Args:
        db: 数据库会话
        lock_name: 锁名称
        operation_type: 操作类型
        lock_owner: 锁拥有者名称
        timeout_seconds: 锁超时时间（秒），默认30分钟
        
    Returns:
        是否成功获取锁
    """
    try:
        # 检查锁是否存在
        lock = db.query(EmailSyncLock).filter_by(lock_name=lock_name).first()
        
        # 如果锁不存在，创建一个新锁
        if not lock:
            lock = EmailSyncLock(
                lock_name=lock_name,
                is_locked=True,
                locked_at=datetime.now(timezone.utc),
                locked_by=lock_owner,
                operation_type=operation_type,
                timeout_seconds=timeout_seconds
            )
            db.add(lock)
            db.commit()
            logger.info(f"已创建并获取邮箱同步锁 {lock_name}，操作类型：{operation_type}，拥有者：{lock_owner}，超时时间：{timeout_seconds}秒")
            return True
        
        # 如果锁已被占用，检查是否过期
        if lock.is_locked:
            if lock.is_expired():
                # 锁已过期，自动清理并重新获取
                logger.info(f"邮箱同步锁 {lock_name} 已过期（原拥有者：{lock.locked_by}，原操作类型：{lock.operation_type}），自动清理并重新获取")
                lock.is_locked = True
                lock.locked_at = datetime.now(timezone.utc)
                lock.locked_by = lock_owner
                lock.operation_type = operation_type
                lock.timeout_seconds = timeout_seconds
                db.commit()
                logger.info(f"已清理过期邮箱同步锁并重新获取 {lock_name}，新拥有者：{lock_owner}，新操作类型：{operation_type}")
                return True
            else:
                # 锁仍然有效，无法获取
                remaining_time = lock.time_remaining()
                logger.warning(f"邮箱同步锁 {lock_name} 已被 {lock.locked_by} 占用（操作类型：{lock.operation_type}），剩余时间：{remaining_time}秒")
                return False
        
        # 如果锁存在但未被占用，更新锁状态
        lock.is_locked = True
        lock.locked_at = datetime.now(timezone.utc)
        lock.locked_by = lock_owner
        lock.operation_type = operation_type
        lock.timeout_seconds = timeout_seconds
        db.commit()
        logger.info(f"已获取邮箱同步锁 {lock_name}，操作类型：{operation_type}，拥有者：{lock_owner}，超时时间：{timeout_seconds}秒")
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"获取邮箱同步锁 {lock_name} 时出错: {str(e)}")
        return False

async def release_lock(db: Session, lock_name: str, lock_owner: str = "system") -> bool:
    """
    释放邮箱同步锁
    
    Args:
        db: 数据库会话
        lock_name: 锁名称
        lock_owner: 锁拥有者名称，用于验证
        
    Returns:
        是否成功释放锁
    """
    try:
        # 检查锁是否存在
        lock = db.query(EmailSyncLock).filter_by(lock_name=lock_name).first()
        
        # 如果锁不存在，返回True（没有锁需要释放）
        if not lock:
            logger.warning(f"邮箱同步锁 {lock_name} 不存在，无需释放")
            return True
        
        # 验证锁拥有者（如果owner不是system，则需要验证）
        if lock_owner != "system" and lock.locked_by != lock_owner:
            logger.warning(f"无法释放邮箱同步锁 {lock_name}：拥有者不匹配，当前拥有者为 {lock.locked_by}")
            return False
        
        # 释放锁
        lock.is_locked = False
        lock.locked_by = None
        lock.operation_type = None
        db.commit()
        logger.info(f"已释放邮箱同步锁 {lock_name}")
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"释放邮箱同步锁 {lock_name} 时出错: {str(e)}")
        return False

async def is_locked(db: Session, lock_name: str) -> bool:
    """
    检查邮箱同步锁是否被占用（考虑过期状态）
    
    Args:
        db: 数据库会话
        lock_name: 锁名称
        
    Returns:
        锁是否被占用
    """
    try:
        lock = db.query(EmailSyncLock).filter_by(lock_name=lock_name).first()
        if not lock:
            return False
        
        # 如果锁已过期，认为锁未被占用
        if lock.is_locked and lock.is_expired():
            logger.debug(f"邮箱同步锁 {lock_name} 已过期，认为未被占用")
            return False
            
        return lock.is_locked
    except Exception as e:
        logger.error(f"检查邮箱同步锁 {lock_name} 状态时出错: {str(e)}")
        return False

async def get_lock_info(db: Session, lock_name: str) -> Optional[Dict]:
    """
    获取锁信息
    
    Args:
        db: 数据库会话
        lock_name: 锁名称
        
    Returns:
        锁信息字典或None
    """
    try:
        lock = db.query(EmailSyncLock).filter_by(lock_name=lock_name).first()
        if lock:
            return {
                "lock_name": lock.lock_name,
                "is_locked": lock.is_locked,
                "locked_by": lock.locked_by,
                "operation_type": lock.operation_type,
                "locked_at": lock.locked_at.isoformat() if lock.locked_at else None,
                "updated_at": lock.updated_at.isoformat() if lock.updated_at else None,
                "timeout_seconds": lock.timeout_seconds,
                "is_expired": lock.is_expired(),
                "time_remaining": lock.time_remaining()
            }
        return None
    except Exception as e:
        logger.error(f"获取邮箱同步锁 {lock_name} 信息时出错: {str(e)}")
        return None

async def check_conflicting_operations(db: Session, operation_type: str) -> Optional[Dict]:
    """
    检查是否有冲突的操作正在进行
    
    Args:
        db: 数据库会话
        operation_type: 当前操作类型
        
    Returns:
        冲突信息字典或None
    """
    try:
        # 定义操作冲突规则
        conflict_rules = {
            LOCK_TYPES["CACHE_SYNC"]: [LOCK_TYPES["PERSONNEL_SYNC"], LOCK_TYPES["EXTID_COMPLETION"]],
            LOCK_TYPES["EXTID_COMPLETION"]: [LOCK_TYPES["CACHE_SYNC"], LOCK_TYPES["PERSONNEL_SYNC"]],
            LOCK_TYPES["PERSONNEL_SYNC"]: [LOCK_TYPES["CACHE_SYNC"], LOCK_TYPES["EXTID_COMPLETION"]],
            LOCK_TYPES["CONSISTENCY_CHECK"]: [],  # 一致性检查可以与其他操作并行
            LOCK_TYPES["FULL_SYNC"]: [LOCK_TYPES["CACHE_SYNC"], LOCK_TYPES["PERSONNEL_SYNC"], LOCK_TYPES["EXTID_COMPLETION"]]
        }
        
        conflicting_types = conflict_rules.get(operation_type, [])
        
        if not conflicting_types:
            return None
        
        # 查找冲突的锁（排除过期锁）
        conflicting_locks = []
        for lock in db.query(EmailSyncLock).filter(
            EmailSyncLock.is_locked == True,
            EmailSyncLock.operation_type.in_(conflicting_types)
        ).all():
            # 只考虑未过期的锁
            if not lock.is_expired():
                conflicting_locks.append(lock)
        
        if conflicting_locks:
            conflicts = []
            for lock in conflicting_locks:
                conflicts.append({
                    "lock_name": lock.lock_name,
                    "operation_type": lock.operation_type,
                    "locked_by": lock.locked_by,
                    "locked_at": lock.locked_at.isoformat() if lock.locked_at else None,
                    "time_remaining": lock.time_remaining()
                })
            return {
                "has_conflicts": True,
                "conflicts": conflicts
            }
        
        return None
        
    except Exception as e:
        logger.error(f"检查冲突操作时出错: {str(e)}")
        return None

async def cleanup_expired_locks(db: Session) -> int:
    """
    清理所有过期的邮箱同步锁
    
    Args:
        db: 数据库会话
        
    Returns:
        清理的锁数量
    """
    try:
        # 查找所有已锁定的记录
        locked_records = db.query(EmailSyncLock).filter(EmailSyncLock.is_locked == True).all()
        
        cleaned_count = 0
        for lock in locked_records:
            if lock.is_expired():
                logger.info(f"清理过期邮箱同步锁：{lock.lock_name}，原拥有者：{lock.locked_by}，操作类型：{lock.operation_type}，过期时间：{lock.locked_at + timedelta(seconds=lock.timeout_seconds)}")
                lock.is_locked = False
                lock.locked_by = None
                lock.operation_type = None
                cleaned_count += 1
        
        if cleaned_count > 0:
            db.commit()
            logger.info(f"已清理 {cleaned_count} 个过期的邮箱同步锁")
        
        return cleaned_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"清理过期邮箱同步锁时出错: {str(e)}")
        return 0

async def get_all_locks_status(db: Session) -> List[Dict]:
    """
    获取所有邮箱同步锁的状态信息
    
    Args:
        db: 数据库会话
        
    Returns:
        锁状态信息列表
    """
    try:
        locks = db.query(EmailSyncLock).all()
        
        lock_status = []
        for lock in locks:
            lock_status.append({
                "lock_name": lock.lock_name,
                "is_locked": lock.is_locked,
                "locked_by": lock.locked_by,
                "operation_type": lock.operation_type,
                "locked_at": lock.locked_at.isoformat() if lock.locked_at else None,
                "updated_at": lock.updated_at.isoformat() if lock.updated_at else None,
                "timeout_seconds": lock.timeout_seconds,
                "is_expired": lock.is_expired(),
                "time_remaining": lock.time_remaining()
            })
        
        return lock_status
        
    except Exception as e:
        logger.error(f"获取邮箱同步锁状态时出错: {str(e)}")
        return []

async def force_release_all_locks(db: Session, lock_owner: str = "system") -> int:
    """
    强制释放所有邮箱同步锁（紧急情况使用）
    
    Args:
        db: 数据库会话
        lock_owner: 执行强制释放的用户
        
    Returns:
        释放的锁数量
    """
    try:
        locked_count = db.query(EmailSyncLock).filter(EmailSyncLock.is_locked == True).count()
        
        if locked_count > 0:
            db.query(EmailSyncLock).filter(EmailSyncLock.is_locked == True).update({
                "is_locked": False,
                "locked_by": None,
                "operation_type": None
            })
            db.commit()
            logger.warning(f"强制释放了 {locked_count} 个邮箱同步锁，执行者：{lock_owner}")
        
        return locked_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"强制释放邮箱同步锁时出错: {str(e)}")
        return 0 