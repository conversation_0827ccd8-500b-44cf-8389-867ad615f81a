"""add_software_compliance_fields

Revision ID: 4e74cd204a44
Revises: e03debbd58bb
Create Date: 2025-04-01 23:59:36.854787

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4e74cd204a44'
down_revision: Union[str, None] = 'e03debbd58bb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加is_compliant字段，默认为True
    op.add_column('software', sa.Column('is_compliant', sa.<PERSON>(), nullable=True, server_default='1'))
    
    # 添加usage_notes字段
    op.add_column('software', sa.Column('usage_notes', sa.Text(), nullable=True))


def downgrade() -> None:
    # 回滚时删除字段
    op.drop_column('software', 'usage_notes')
    op.drop_column('software', 'is_compliant')
