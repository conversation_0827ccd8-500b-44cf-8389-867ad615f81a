<template>
  <div class="mobile-email-index">
    <!-- 功能菜单 -->
    <van-grid :column-num="2" :gutter="16">
      <van-grid-item
        icon="setting-o"
        text="邮箱配置"
        @click="goToConfig"
      />
      <van-grid-item
        icon="friends-o"
        text="邮箱成员"
        @click="goToMembers"
      />
    </van-grid>
    
    <!-- 统计信息 -->
    <van-cell-group title="统计信息" inset>
      <van-cell title="总成员数" :value="statistics.totalMembers" />
      <van-cell title="启用成员" :value="statistics.enabledMembers" />
      <van-cell title="部门数量" :value="statistics.departmentCount" />
    </van-cell-group>
    
    <!-- 快速操作 -->
    <van-cell-group title="快速操作" inset>
      <van-cell title="同步成员" is-link @click="syncMembers" />
      <van-cell title="导出成员" is-link @click="exportMembers" />
    </van-cell-group>
    
    <!-- 最近活动 -->
    <van-cell-group title="最近活动" inset>
      <van-cell title="最后同步时间" :value="lastSyncTime" />
      <van-cell title="同步状态" :value="syncStatus" />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'

const router = useRouter()

// 统计数据
const statistics = ref({
  totalMembers: '--',
  enabledMembers: '--',
  departmentCount: '--'
})

// 同步信息
const lastSyncTime = ref('--')
const syncStatus = ref('正常')

// 页面跳转
const goToConfig = () => {
  router.push('/m/email/config')
}

const goToMembers = () => {
  router.push('/m/email/members')
}

// 同步成员
const syncMembers = async () => {
  const loading = showLoadingToast({
    message: '同步中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    closeToast()
    showToast('同步完成')
    lastSyncTime.value = new Date().toLocaleString()
    loadStatistics()
  } catch (error) {
    closeToast()
    showToast('同步失败')
  }
}

// 导出成员
const exportMembers = async () => {
  const loading = showLoadingToast({
    message: '导出中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    closeToast()
    showToast('导出成功')
  } catch (error) {
    closeToast()
    showToast('导出失败')
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    // 模拟加载统计数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    statistics.value = {
      totalMembers: '89',
      enabledMembers: '76',
      departmentCount: '12'
    }
    lastSyncTime.value = '2024-01-15 14:30:00'
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.mobile-email-index {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .van-grid {
    margin-bottom: 16px;
  }
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}
</style> 