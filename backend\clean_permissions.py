from app.database import SessionLocal
from app.models.permission import Permission
from app.models.role import Role

def clean_permissions():
    db = SessionLocal()
    try:
        # 删除所有角色
        db.query(Role).delete()
        # 删除所有权限
        db.query(Permission).delete()
        db.commit()
        print("已清理所有权限和角色数据")
    except Exception as e:
        print(f"清理数据时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    clean_permissions() 