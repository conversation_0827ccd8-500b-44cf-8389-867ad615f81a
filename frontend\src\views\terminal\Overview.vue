<template>
  <div class="terminal-overview">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Monitor /></el-icon>
        <h2 class="page-title">终端概况</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>终端管理</el-breadcrumb-item>
        <el-breadcrumb-item>终端概况</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">终端状态统计</span>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon> 刷新数据
          </el-button>
        </div>
      </div>

      <el-row :gutter="20" v-loading="loading">
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">终端总数</div>
              <div class="stat-value">{{ stats.total }}</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stat-card">
            <div class="stat-icon online">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">在线终端</div>
              <div class="stat-value">{{ stats.online }}</div>
              <div class="stat-rate">{{ onlineRate }}%</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stat-card">
            <div class="stat-icon offline">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">离线终端</div>
              <div class="stat-value">{{ stats.offline }}</div>
              <div class="stat-rate">{{ offlineRate }}%</div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="12" :lg="6">
          <div class="stat-card">
            <div class="stat-icon windows">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">Windows系统</div>
              <div class="stat-value">{{ stats.windows_count }}</div>
              <div class="stat-rate">{{ windowsRate }}%</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="box-card">
          <template #header>
            <div class="card-header">
              <span class="section-title">终端状态分布</span>
            </div>
          </template>
          <div class="chart-container" v-loading="loading">
            <div ref="statusChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card shadow="hover" class="box-card">
          <template #header>
            <div class="card-header">
              <span class="section-title">Windows版本分布</span>
            </div>
          </template>
          <div class="chart-container" v-loading="loading">
            <div ref="windowsChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card shadow="hover" class="box-card">
      <template #header>
        <div class="card-header">
          <span class="section-title">命令执行情况</span>
        </div>
      </template>
      <div class="chart-container" v-loading="commandLoading">
        <div ref="commandChartRef" class="chart"></div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { 
  Monitor, 
  Connection, 
  CircleClose, 
  Grid, 
  Refresh 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { 
  PieChart, 
  BarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { 
  CanvasRenderer
} from 'echarts/renderers'
import { terminalApi } from '@/api/terminal'
import type { TerminalStats, TerminalCommandStats } from '@/types/terminal'

echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  BarChart,
  CanvasRenderer
])

const loading = ref(true)
const commandLoading = ref(true)
const stats = ref<TerminalStats>({
  total: 0,
  online: 0,
  offline: 0,
  windows_count: 0,
  windows_versions: {},
  hardware_stats: {}
})
const commandStats = ref<TerminalCommandStats>({
  total: 0,
  pending: 0,
  sent: 0,
  executed: 0,
  failed: 0,
  timeout: 0
})

const statusChartRef = ref<HTMLElement | null>(null)
const windowsChartRef = ref<HTMLElement | null>(null)
const commandChartRef = ref<HTMLElement | null>(null)

let statusChart: echarts.ECharts | null = null
let windowsChart: echarts.ECharts | null = null
let commandChart: echarts.ECharts | null = null

// 计算百分比
const onlineRate = computed(() => {
  return stats.value.total > 0 
    ? Math.round((stats.value.online / stats.value.total) * 100) 
    : 0
})

const offlineRate = computed(() => {
  return stats.value.total > 0 
    ? Math.round((stats.value.offline / stats.value.total) * 100) 
    : 0
})

const windowsRate = computed(() => {
  return stats.value.total > 0 
    ? Math.round((stats.value.windows_count / stats.value.total) * 100) 
    : 0
})

// 获取终端统计数据
const fetchTerminalStats = async () => {
  loading.value = true
  try {
    const response = await terminalApi.getTerminalStats()
    stats.value = response.data
    initCharts()
  } catch (error) {
    console.error('获取终端统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取命令统计数据
const fetchCommandStats = async () => {
  commandLoading.value = true
  try {
    const response = await terminalApi.getCommandStats()
    commandStats.value = response.data
    initCommandChart()
  } catch (error) {
    console.error('获取命令统计数据失败:', error)
  } finally {
    commandLoading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initStatusChart()
  initWindowsChart()
}

// 初始化终端状态图表
const initStatusChart = () => {
  if (!statusChartRef.value) return

  if (!statusChart) {
    statusChart = echarts.init(statusChartRef.value)
  }

  const option = {
    title: {
      text: '终端在线状态',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['在线', '离线']
    },
    series: [
      {
        name: '终端状态',
        type: 'pie',
        radius: '60%',
        data: [
          { value: stats.value.online, name: '在线', itemStyle: { color: '#67C23A' } },
          { value: stats.value.offline, name: '离线', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  statusChart.setOption(option)
}

// 初始化Windows版本图表
const initWindowsChart = () => {
  if (!windowsChartRef.value) return

  if (!windowsChart) {
    windowsChart = echarts.init(windowsChartRef.value)
  }

  const windowsVersions = stats.value.windows_versions
  const data = Object.keys(windowsVersions).map(key => ({
    value: windowsVersions[key],
    name: key
  }))

  const option = {
    title: {
      text: 'Windows版本分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: Object.keys(windowsVersions)
    },
    series: [
      {
        name: 'Windows版本',
        type: 'pie',
        radius: '60%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  windowsChart.setOption(option)
}

// 初始化命令状态图表
const initCommandChart = () => {
  if (!commandChartRef.value) return

  if (!commandChart) {
    commandChart = echarts.init(commandChartRef.value)
  }

  const option = {
    title: {
      text: '命令状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['数量'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['待发送', '已发送', '已执行', '失败', '超时']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: [
          {
            value: commandStats.value.pending,
            itemStyle: { color: '#409EFF' }
          },
          {
            value: commandStats.value.sent,
            itemStyle: { color: '#E6A23C' }
          },
          {
            value: commandStats.value.executed,
            itemStyle: { color: '#67C23A' }
          },
          {
            value: commandStats.value.failed,
            itemStyle: { color: '#F56C6C' }
          },
          {
            value: commandStats.value.timeout,
            itemStyle: { color: '#909399' }
          }
        ]
      }
    ]
  }

  commandChart.setOption(option)
}

// 刷新数据
const refreshData = () => {
  fetchTerminalStats()
  fetchCommandStats()
}

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
  statusChart?.resize()
  windowsChart?.resize()
  commandChart?.resize()
}

onMounted(() => {
  fetchTerminalStats()
  fetchCommandStats()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  statusChart?.dispose()
  windowsChart?.dispose()
  commandChart?.dispose()
})
</script>

<style scoped>
.terminal-overview {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.stat-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 40px;
  margin-right: 15px;
  color: #409EFF;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #ecf5ff;
}

.stat-icon.online {
  color: #67C23A;
  background-color: #f0f9eb;
}

.stat-icon.offline {
  color: #F56C6C;
  background-color: #fef0f0;
}

.stat-icon.windows {
  color: #E6A23C;
  background-color: #fdf6ec;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1.2;
}

.stat-rate {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-row {
  margin-top: 20px;
}
</style> 