<template>
  <div class="mobile-ad-index">
    <!-- 功能菜单 -->
    <van-grid :column-num="2" :gutter="16">
      <van-grid-item
        icon="setting-o"
        text="AD配置"
        @click="goToConfig"
      />
      <van-grid-item
        icon="refresh"
        text="AD同步"
        @click="goToSync"
      />
    </van-grid>
    
    <!-- 状态卡片 -->
    <van-cell-group title="连接状态" inset>
      <van-cell title="LDAP连接" :value="connectionStatus" />
      <van-cell title="最后同步时间" :value="lastSyncTime" />
      <van-cell title="用户总数" :value="userCount" />
    </van-cell-group>
    
    <!-- 快速操作 -->
    <van-cell-group title="快速操作" inset>
      <van-cell title="测试连接" is-link @click="testConnection" />
      <van-cell title="立即同步" is-link @click="startSync" />
    </van-cell-group>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'

const router = useRouter()

// 状态数据
const connectionStatus = ref('未连接')
const lastSyncTime = ref('--')
const userCount = ref('--')

// 页面跳转
const goToConfig = () => {
  router.push('/m/ad/config')
}

const goToSync = () => {
  router.push('/m/ad/sync')
}

// 测试连接
const testConnection = async () => {
  const loading = showLoadingToast({
    message: '测试连接中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    closeToast()
    showToast('连接成功')
    connectionStatus.value = '已连接'
  } catch (error) {
    closeToast()
    showToast('连接失败')
  }
}

// 开始同步
const startSync = async () => {
  const loading = showLoadingToast({
    message: '同步中...',
    forbidClick: true,
  })
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    closeToast()
    showToast('同步完成')
    lastSyncTime.value = new Date().toLocaleString()
  } catch (error) {
    closeToast()
    showToast('同步失败')
  }
}

// 加载初始数据
const loadData = async () => {
  try {
    // 模拟加载状态数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    connectionStatus.value = '已连接'
    lastSyncTime.value = '2024-01-15 10:30:00'
    userCount.value = '156'
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.mobile-ad-index {
  padding: 16px;
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .van-grid {
    margin-bottom: 16px;
  }
  
  .van-cell-group {
    margin-bottom: 16px;
  }
}
</style> 