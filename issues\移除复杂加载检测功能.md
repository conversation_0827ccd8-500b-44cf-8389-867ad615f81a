# 移除复杂加载检测功能

## 问题描述
当前复杂的加载检测功能导致路由切换时频繁检测，访问体验不友好。需要简化为只保留屏幕尺寸检测。

## 任务计划

### 第一阶段：简化设备检测
- [x] 简化 useDevice.ts - 只保留屏幕尺寸检测
- [x] 简化 usePlatform.ts - 移除复杂平台检测逻辑  
- [x] 移除 useResponsive.ts - 完全移除响应式检测功能

### 第二阶段：简化Loading页面
- [x] 简化 Loading.vue - 移除复杂状态机和重试机制

### 第三阶段：优化路由和应用启动
- [x] 优化路由守卫 - 减少检测频率
- [x] 简化应用启动逻辑 - 移除复杂初始化

## 执行记录
- 开始时间: 2025年1月27日
- 执行者: Claude AI Assistant
- **任务完成时间**: 2025年1月27日

## 修改总结

### 简化的功能
1. **useDevice.ts**: 移除了所有复杂设备能力检测，只保留屏幕尺寸和基本设备类型判断
2. **usePlatform.ts**: 移除了组件前缀、路由前缀等复杂功能，只保留基本平台判断
3. **useResponsive.ts**: 完全删除响应式检测功能
4. **Loading.vue**: 移除复杂状态机、重试机制和超时处理
5. **路由守卫**: 移除频繁的调试日志输出
6. **应用启动**: 移除复杂的用户状态初始化逻辑

### 保留的功能
- 屏幕尺寸检测 (screenWidth, screenHeight)
- 基本的移动端/桌面端判断
- 路由平台适配重定向
- 基本的用户认证检查

### 预期效果
- 路由切换更加流畅，无频繁检测
- 减少不必要的调试日志输出
- 提升整体访问友好性
- 保持核心的移动端适配功能 