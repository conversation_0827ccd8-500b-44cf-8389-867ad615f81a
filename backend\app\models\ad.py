from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship
from ..database import Base

class OrganizationalUnit(Base):
    __tablename__ = "organizational_units"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)
    dn = Column(String, unique=True, index=True)
    parent_dn = Column(String, nullable=True)

    # 移除自引用关系以避免外键约束问题
    # children = relationship(
    #     "OrganizationalUnit",
    #     backref="parent",
    #     remote_side=[dn],
    #     cascade="all",
    #     single_parent=True
    # )
    # 移除关系以避免外键约束问题
    # users = relationship("ADUser", back_populates="ou")

class ADUser(Base):
    __tablename__ = "ad_users"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    username = Column(String, unique=True, index=True)
    email = Column(String)
    dn = Column(String, unique=True)
    hashed_password = Column(String)
    enabled = Column(Boolean, default=True)
    ou_dn = Column(String, nullable=True)

    # 移除关系以避免外键约束问题
    # ou = relationship("OrganizationalUnit", back_populates="users")