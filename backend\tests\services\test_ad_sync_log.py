from sqlalchemy import create_engine, inspect
from sqlalchemy.orm import sessionmaker
from app.models.ad_sync_log import ADSyncLog
from app.database import SessionLocal
import json
from datetime import datetime

def test_ad_sync_log_model():
    # 使用应用的PostgreSQL数据库连接
    db = SessionLocal()
    
    try:
        # 检查AD同步日志表字段
        from app.database import engine
        inspector = inspect(engine)
        columns = inspector.get_columns('ad_sync_logs')
        column_names = [column['name'] for column in columns]
        
        print("AD同步日志表字段:")
        for column_name in column_names:
            print(f"- {column_name}")
        
        # 确认updated_groups字段存在
        if 'updated_groups' in column_names:
            print("\nupdated_groups字段存在于表中，检查通过!")
        else:
            print("\nupdated_groups字段不存在于表中，检查失败!")
        
        # 测试插入一条记录
        test_log = ADSyncLog(
            operator="测试操作员",
            source_type="all",
            source_id=None,
            sync_time=datetime.now(),
            total_users=100,
            created_users=10,
            skipped_users=5,
            disabled_users=2,
            moved_users=3,
            updated_users=20,
            created_ous=5,
            renamed_ous=2,
            created_groups=3,
            updated_groups=2,  # 测试更新安全组字段
            added_to_groups=15,
            removed_from_groups=5,
            errors=json.dumps(["测试错误1", "测试错误2"], ensure_ascii=False),
            details=json.dumps({
                "stats": {
                    "total": 100,
                    "created_users": 10,
                    "skipped_users": 5,
                    "disabled_users": 2,
                    "moved_users": 3, 
                    "updated_users": 20,
                    "created_ous": 5,
                    "renamed_ous": 2,
                    "created_groups": 3,
                    "updated_groups": 2,  # 测试更新安全组字段
                    "added_to_groups": 15,
                    "removed_from_groups": 5,
                    "errors": ["测试错误1", "测试错误2"],
                    "new_users": []
                }
            }, ensure_ascii=False)
        )
        
        # 添加到会话但不提交，仅用于测试模型结构
        db.add(test_log)
        print("\n可以成功创建包含updated_groups字段的AD同步日志记录!")
        
        # 回滚事务，不实际写入数据库
        db.rollback()
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    test_ad_sync_log_model() 