<template>
  <div class="virtual-scroll-container" ref="containerRef" @scroll="handleScroll">
    <div class="virtual-scroll-content" :style="{ height: totalHeight + 'px' }">
      <div 
        class="virtual-scroll-items" 
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, startIndex + index)"
          class="virtual-scroll-item"
          :style="{ height: itemHeight + 'px' }"
          @click="handleItemClick(item, startIndex + index)"
        >
          <slot 
            :item="item" 
            :index="startIndex + index"
            :is-loading="(item as any)?._isLoading"
          >
            {{ item }}
          </slot>
        </div>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="virtual-scroll-loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="totalItems === 0" class="virtual-scroll-empty">
      <el-empty :description="emptyText" />
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'

interface Props {
  // 数据相关
  items: T[]
  totalItems: number
  itemHeight: number
  
  // 分页相关
  pageSize?: number
  currentPage?: number
  hasMore?: boolean
  
  // 行为控制
  loading?: boolean
  keyField?: string
  emptyText?: string
  
  // 缓冲区大小（视口外渲染的额外项目数）
  bufferSize?: number
  
  // 预加载阈值（距离底部多少项目时开始加载）
  preloadThreshold?: number
  
  // 是否启用智能缓冲（根据设备性能调整）
  enableSmartBuffer?: boolean
}

interface Emits {
  (e: 'load-more'): void
  (e: 'item-click', item: T, index: number): void
  (e: 'scroll', scrollTop: number): void
}

const props = withDefaults(defineProps<Props>(), {
  pageSize: 50,
  currentPage: 1,
  hasMore: true,
  loading: false,
  keyField: 'id',
  emptyText: '暂无数据',
  bufferSize: 5,
  preloadThreshold: 10,
  enableSmartBuffer: true
})

const emit = defineEmits<Emits>()

// DOM引用
const containerRef = ref<HTMLElement>()

// 滚动状态
const scrollTop = ref(0)
const containerHeight = ref(400)

// 计算可见区域
const visibleCount = computed(() => Math.ceil(containerHeight.value / props.itemHeight))
const totalHeight = computed(() => props.totalItems * props.itemHeight)

// 智能缓冲区大小计算
const smartBufferSize = computed(() => {
  if (!props.enableSmartBuffer) return props.bufferSize
  
  // 根据设备性能调整缓冲区大小
  if (typeof navigator !== 'undefined') {
    const memory = (navigator as any).deviceMemory || 4
    const cores = navigator.hardwareConcurrency || 4
    
    if (memory >= 8 && cores >= 8) return props.bufferSize * 2 // 高性能设备
    if (memory >= 4 && cores >= 4) return props.bufferSize // 中等性能设备
    return Math.max(2, Math.floor(props.bufferSize / 2)) // 低性能设备
  }
  
  return props.bufferSize
})

// 计算起始和结束索引（包含智能缓冲区）
const startIndex = computed(() => {
  const index = Math.floor(scrollTop.value / props.itemHeight)
  return Math.max(0, index - smartBufferSize.value)
})

const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value + smartBufferSize.value * 2
  return Math.min(props.totalItems - 1, index)
})

// 可见项目
const visibleItems = computed(() => {
  const items = []
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    if (i < props.items.length) {
      items.push(props.items[i])
    } else {
      // 占位项，用于触发加载更多
      items.push({ _isLoading: true, _placeholder: true } as T)
    }
  }
  return items
})

// 偏移量
const offsetY = computed(() => startIndex.value * props.itemHeight)

// 获取项目唯一标识
const getItemKey = (item: T, index: number) => {
  if (item && typeof item === 'object' && props.keyField in item) {
    return (item as any)[props.keyField]
  }
  if (item && typeof item === 'object' && '_placeholder' in item) {
    return `placeholder-${index}`
  }
  return index
}

// 处理滚动事件
const handleScroll = () => {
  if (!containerRef.value) return
  
  scrollTop.value = containerRef.value.scrollTop
  emit('scroll', scrollTop.value)
  
  // 检查是否需要加载更多
  checkLoadMore()
}

// 检查是否需要加载更多数据
const checkLoadMore = () => {
  if (props.loading || !props.hasMore) return
  
  const remainingItems = props.totalItems - endIndex.value
  if (remainingItems <= props.preloadThreshold) {
    emit('load-more')
  }
}

// 处理项目点击
const handleItemClick = (item: T, index: number) => {
  if (item && typeof item === 'object' && '_placeholder' in item) {
    return // 忽略占位项的点击
  }
  emit('item-click', item, index)
}

// 滚动到指定位置
const scrollTo = (index: number) => {
  if (!containerRef.value) return
  
  const targetScrollTop = index * props.itemHeight
  containerRef.value.scrollTop = targetScrollTop
}

// 滚动到顶部
const scrollToTop = () => {
  scrollTo(0)
}

// 滚动到底部
const scrollToBottom = () => {
  scrollTo(props.totalItems - 1)
}

// 监听容器尺寸变化
const resizeObserver = ref<ResizeObserver>()

const updateContainerHeight = () => {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight
  }
}

onMounted(() => {
  nextTick(() => {
    updateContainerHeight()
    
    // 监听容器尺寸变化
    if (containerRef.value && window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(updateContainerHeight)
      resizeObserver.value.observe(containerRef.value)
    }
    
    // 初始检查是否需要加载更多
    checkLoadMore()
  })
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})

// 监听items变化，触发检查加载更多
watch(() => props.items.length, () => {
  nextTick(() => {
    checkLoadMore()
  })
}, { immediate: true })

// 暴露方法给父组件
defineExpose({
  scrollTo,
  scrollToTop,
  scrollToBottom,
  containerRef
})
</script>

<style scoped>
.virtual-scroll-container {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.virtual-scroll-content {
  position: relative;
  width: 100%;
}

.virtual-scroll-items {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.virtual-scroll-item {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.virtual-scroll-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.virtual-scroll-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 滚动条样式 */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 4px;
  transition: background-color 0.3s;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}
</style> 