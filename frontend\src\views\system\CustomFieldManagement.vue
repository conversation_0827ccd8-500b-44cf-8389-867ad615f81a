<template>
  <div class="custom-field-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>自定义字段管理</span>
          <div class="header-actions">
            <el-button type="success" @click="openPresetDialog()">
              <el-icon><Star /></el-icon>
              创建预设字段
            </el-button>
            <el-button type="primary" @click="openFieldDialog()">
              <el-icon><Plus /></el-icon>
              新增字段
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和过滤 -->
      <el-row :gutter="16" class="mb-4">
        <el-col :span="8">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索字段名称或标签"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.field_type"
            placeholder="字段类型"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="option in FIELD_TYPE_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <el-icon class="mr-2"><component :is="option.icon" /></el-icon>
              {{ option.label }}
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.applies_to"
            placeholder="适用范围"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="option in APPLIES_TO_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="searchForm.is_active"
            placeholder="状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-col>
      </el-row>

      <!-- 字段表格 -->
      <el-table
        v-loading="loading"
        :data="fieldList"
        row-key="id"
        @sort-change="handleSortChange"
      >
        <!-- 空状态 -->
        <template #empty>
          <div class="empty-state">
            <el-empty 
              :image-size="120"
              description="暂无自定义字段"
            >
              <template #description>
                <p>还没有创建任何自定义字段</p>
                <p style="color: #999; font-size: 12px;">您可以创建自定义字段来扩展资产和盘点功能</p>
              </template>
              <div class="empty-actions">
                <el-button type="primary" @click="openFieldDialog()">
                  <el-icon><Plus /></el-icon>
                  创建第一个字段
                </el-button>
                <el-button type="success" @click="openPresetDialog()">
                  <el-icon><Star /></el-icon>
                  使用预设字段
                </el-button>
              </div>
            </el-empty>
          </div>
        </template>
        <el-table-column prop="sort_order" label="排序" width="80" align="center">
          <template #default="{ row, $index }">
            <div class="sort-controls">
              <el-button
                size="small"
                type="text"
                :disabled="$index === 0"
                @click="moveField(row, 'up')"
              >
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <span class="sort-number">{{ row.sort_order }}</span>
              <el-button
                size="small"
                type="text"
                :disabled="$index === fieldList.length - 1"
                @click="moveField(row, 'down')"
              >
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="字段名称" min-width="120" />
        
        <el-table-column prop="label" label="字段标签" min-width="120" />
        
        <el-table-column prop="field_type" label="字段类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getFieldTypeColor(row.field_type)">
              {{ getFieldTypeLabel(row.field_type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="applies_to" label="适用范围" width="140" align="center">
          <template #default="{ row }">
            <el-tag :type="getAppliesToColor(row.applies_to)">
              {{ getAppliesToLabel(row.applies_to) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="is_required" label="必填" width="80" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.is_required" color="#67c23a"><Check /></el-icon>
            <el-icon v-else color="#c0c4cc"><Close /></el-icon>
          </template>
        </el-table-column>

        <el-table-column prop="is_active" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="toggleFieldStatus(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="openFieldDialog(row)">
              编辑
            </el-button>
            <el-popconfirm
              title="确定删除这个字段吗？删除后关联的字段值也会被删除。"
              @confirm="deleteField(row)"
            >
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="changePageSize"
          @current-change="changePage"
        />
      </div>
    </el-card>

    <!-- 字段配置对话框 -->
    <el-dialog
      v-model="fieldDialogVisible"
      :title="fieldDialogMode === 'create' ? '新增字段' : '编辑字段'"
      width="800px"
      :before-close="handleFieldDialogClose"
    >
      <CustomFieldForm
        ref="fieldFormRef"
        :initial-data="currentField"
        :mode="fieldDialogMode"
        @submit="handleFieldSubmit"
        @cancel="closeFieldDialog"
      />
    </el-dialog>

    <!-- 预设字段对话框 -->
    <el-dialog
      v-model="presetDialogVisible"
      title="创建预设字段"
      width="500px"
    >
      <el-form :model="presetForm" label-width="120px">
        <el-form-item label="预设类型" required>
          <el-select v-model="presetForm.preset_type" placeholder="请选择预设类型">
            <el-option
              v-for="preset in PRESET_TYPE_OPTIONS"
              :key="preset.value"
              :label="preset.label"
              :value="preset.value"
            >
              <div>
                <div>{{ preset.label }}</div>
                <div style="font-size: 12px; color: #999;">{{ preset.description }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="适用范围" required>
          <el-select v-model="presetForm.applies_to" placeholder="请选择适用范围">
            <el-option
              v-for="option in getAvailableAppliesToOptions()"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="closePresetDialog()">取消</el-button>
        <el-button
          type="primary"
          :loading="presetLoading"
          @click="createPresetFields"
        >
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { ElMessageBox } from 'element-plus'
import {
  Plus, Search, ArrowUp, ArrowDown, Check, Close, Star
} from '@element-plus/icons-vue'
import { useCustomFieldStore } from '@/stores/customField'
import CustomFieldForm from './components/CustomFieldForm.vue'
import type {
  CustomFieldCreate,
  CustomFieldUpdate
} from '@/types/custom_field'
import {
  FIELD_TYPE_OPTIONS,
  APPLIES_TO_OPTIONS,
  PRESET_TYPE_OPTIONS
} from '@/types/custom_field'
import { formatDateTime } from '@/utils/date'

// 使用Store
const customFieldStore = useCustomFieldStore()

// 响应式引用Store状态
const {
  fieldList,
  loading,
  searchForm,
  pagination,
  fieldDialogVisible,
  fieldDialogMode,
  currentField,
  presetDialogVisible,
  presetLoading,
  presetForm,
  hasFields,
  isLoading
} = storeToRefs(customFieldStore)

// 解构Store方法
const {
  fetchFields,
  searchFields,
  resetSearch,
  createField,
  updateField,
  deleteField,
  toggleFieldStatus,
  moveField,
  createPresetFields,
  openFieldDialog,
  closeFieldDialog,
  openPresetDialog,
  closePresetDialog,
  changePage,
  changePageSize,
  init
} = customFieldStore

// 表单引用
const fieldFormRef = ref()

// 搜索处理
const handleSearch = () => {
  searchFields()
}

// 排序处理
const handleSortChange = ({ prop, order }: any) => {
  // 这里可以添加排序逻辑
  console.log('排序:', prop, order)
}

// 处理字段对话框关闭
const handleFieldDialogClose = (done: () => void) => {
  ElMessageBox.confirm(
    '确定关闭吗？未保存的更改将会丢失。',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    closeFieldDialog()
    done()
  }).catch(() => {
    // 取消关闭
  })
}

// 处理字段提交
const handleFieldSubmit = async (data: CustomFieldCreate | CustomFieldUpdate) => {
  try {
    if (fieldDialogMode.value === 'create') {
      await createField(data as CustomFieldCreate)
    } else {
      await updateField(currentField.value!.id, data as CustomFieldUpdate)
    }
  } catch (error) {
    console.error('保存字段失败:', error)
  }
}

// 获取可用的适用范围选项
const getAvailableAppliesToOptions = () => {
  const selectedPreset = PRESET_TYPE_OPTIONS.find(p => p.value === presetForm.value.preset_type)
  if (!selectedPreset) return APPLIES_TO_OPTIONS
  
  return APPLIES_TO_OPTIONS.filter(option => 
    selectedPreset.applies_to.includes(option.value as any)
  )
}

// 工具函数
const getFieldTypeLabel = (type: string) => {
  const option = FIELD_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

const getFieldTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    text: 'info',
    textarea: 'info',
    number: 'warning',
    date: 'success',
    datetime: 'success',
    select: 'primary',
    radio: 'primary',
    checkbox: 'primary',
    file: 'danger'
  }
  return colors[type] || 'info'
}

const getAppliesToLabel = (appliesTo: string) => {
  const option = APPLIES_TO_OPTIONS.find(opt => opt.value === appliesTo)
  return option?.label || appliesTo
}

const getAppliesToColor = (appliesTo: string) => {
  const colors: Record<string, string> = {
    asset: 'success',
    inventory_record: 'warning',
    both: 'primary'
  }
  return colors[appliesTo] || 'primary'
}

const formatDate = (dateString: string) => {
  return formatDateTime(dateString)
}

onMounted(() => {
  init()
})
</script>

<style scoped>
.custom-field-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.sort-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.sort-number {
  font-size: 12px;
  color: #666;
  min-width: 20px;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-state {
  padding: 40px 20px;
}

.empty-actions {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-table .el-button--text) {
  padding: 0;
  min-height: auto;
}
</style> 