<template>
  <div class="mobile-ad-config">
    <!-- AD服务器配置卡片 -->
    <mobile-card
      title="AD服务器配置"
      icon="setting-o"
      :loading="loading"
      class="config-card"
    >
      <mobile-form
        v-model="configForm"
        :fields="configFields"
        :submitting="submitting"
        @submit="handleSave"
      />
    </mobile-card>

    <!-- 连接状态卡片 -->
    <mobile-card
      title="连接状态"
      icon="info-o"
      class="status-card"
    >
      <template #default>
        <div class="status-content">
          <div class="status-item">
            <van-icon
              :name="connectionStatus.connected ? 'success' : 'warning-o'"
              :color="connectionStatus.connected ? '#67c23a' : '#e6a23c'"
              size="20"
            />
            <span class="status-text">
              {{ connectionStatus.connected ? '已连接' : '未连接' }}
            </span>
          </div>
          <div v-if="connectionStatus.lastSync" class="sync-time">
            上次同步：{{ connectionStatus.lastSync }}
          </div>
        </div>
      </template>
    </mobile-card>

    <!-- 快速操作 -->
    <mobile-card title="快速操作" icon="fire-o">
      <van-cell-group>
        <van-cell
          title="测试连接"
          icon="exchange"
          is-link
          @click="testConnection"
        />
        <van-cell
          title="立即同步"
          icon="synchronous"
          is-link
          @click="syncNow"
        />
        <van-cell
          title="同步日志"
          icon="orders-o"
          is-link
          @click="viewSyncLogs"
        />
      </van-cell-group>
    </mobile-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast, showConfirmDialog } from 'vant'
import { getADConfig, updateADConfig, testADConfig } from '@/api/ad'
import MobileCard from '@mobile/components/business/MobileCard.vue'
import MobileForm from '@mobile/components/business/MobileForm.vue'
// 导入类型定义
interface FormField {
  name: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'switch' | 'radio' | 'checkbox' | 'upload'
  placeholder?: string
  rules?: any[]
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  inputType?: 'text' | 'number' | 'tel' | 'email' | 'password'
  maxlength?: number
  showWordLimit?: boolean
  clearable?: boolean
}

const router = useRouter()

const loading = ref(true)
const submitting = ref(false)

// 表单数据 - 使用与API接口匹配的字段名
const configForm = reactive({
  server: '',
  port: 389,
  username: '',
  password: '',
  base_dn: '',
  use_ssl: false
})

// 连接状态
const connectionStatus = reactive({
  connected: false,
  lastSync: '',
  lastTest: ''
})

// 表单字段配置
const configFields: FormField[] = [
  {
    name: 'server',
    label: '服务器地址',
    type: 'input',
    placeholder: '请输入AD服务器地址',
    required: true,
    rules: [
      { required: true, message: '请输入服务器地址' }
    ]
  },
  {
    name: 'port',
    label: '端口',
    type: 'input',
    inputType: 'number',
    placeholder: '389',
    required: true,
    rules: [
      { required: true, message: '请输入端口号' },
      { pattern: /^\d+$/, message: '端口号必须为数字' }
    ]
  },
  {
    name: 'username',
    label: '管理员账号',
    type: 'input',
    placeholder: '请输入管理员账号',
    required: true,
    rules: [
      { required: true, message: '请输入管理员账号' }
    ]
  },
  {
    name: 'password',
    label: '管理员密码',
    type: 'input',
    inputType: 'password',
    placeholder: '请输入管理员密码',
    required: true,
    rules: [
      { required: true, message: '请输入管理员密码' }
    ]
  },
  {
    name: 'base_dn',
    label: 'Base DN',
    type: 'input',
    placeholder: '请输入Base DN',
    required: true,
    rules: [
      { required: true, message: '请输入Base DN' }
    ]
  },
  {
    name: 'use_ssl',
    label: '启用SSL',
    type: 'switch'
  }
]

// 加载配置数据
const loadConfig = async () => {
  try {
    loading.value = true
    const response = await getADConfig()
    
    if (response.data) {
      const config = response.data
      Object.assign(configForm, {
        server: config.server || '',
        port: config.port || 389,
        username: config.username || '',
        password: '', // 密码不回显
        base_dn: config.base_dn || '',
        use_ssl: config.use_ssl || false
      })
      
      // 更新连接状态
      connectionStatus.connected = config.is_active || false
      connectionStatus.lastSync = config.last_sync || ''
    }
  } catch (error) {
    console.error('加载AD配置失败:', error)
    showToast('加载配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置
const handleSave = async (values: Record<string, any>) => {
  try {
    submitting.value = true
    
    await updateADConfig({
      server: values.server,
      port: Number(values.port),
      username: values.username,
      password: values.password,
      base_dn: values.base_dn,
      use_ssl: values.use_ssl
    })
    
    showToast('保存成功')
    
    // 重新加载配置
    await loadConfig()
  } catch (error: any) {
    console.error('保存AD配置失败:', error)
    showToast(error.response?.data?.detail || '保存失败')
  } finally {
    submitting.value = false
  }
}

// 测试连接
const testConnection = async () => {
  if (!configForm.server || !configForm.username) {
    showToast('请先填写完整的服务器配置')
    return
  }
  
  const loading = showLoadingToast({
    message: '测试连接中...',
    forbidClick: true,
  })
  
  try {
    const response = await testADConfig({
      server: configForm.server,
      port: configForm.port,
      username: configForm.username,
      password: configForm.password,
      base_dn: configForm.base_dn,
      use_ssl: configForm.use_ssl
    })
    
    closeToast()
    
    if (response.data?.status) {
      showToast('连接测试成功')
      connectionStatus.connected = true
      connectionStatus.lastTest = new Date().toLocaleString()
    } else {
      showToast('连接测试失败')
      connectionStatus.connected = false
    }
  } catch (error: any) {
    closeToast()
    console.error('连接测试失败:', error)
    showToast(error.response?.data?.detail || '连接测试失败')
    connectionStatus.connected = false
  }
}

// 立即同步
const syncNow = async () => {
  try {
    await showConfirmDialog({
      title: '确认同步',
      message: '确定要立即执行AD同步吗？',
    })
    
    const loading = showLoadingToast({
      message: '同步中...',
      forbidClick: true,
    })
    
    // 这里应该调用同步API
    // await syncADUsers()
    
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    closeToast()
    showToast('同步完成')
    connectionStatus.lastSync = new Date().toLocaleString()
  } catch (error) {
    if (error !== 'cancel') {
      closeToast()
      showToast('同步失败')
    }
  }
}

// 查看同步日志
const viewSyncLogs = () => {
  router.push('/m/ad/sync-logs')
}

// 初始化
onMounted(() => {
  loadConfig()
})
</script>

<style lang="scss" scoped>
.mobile-ad-config {
  padding: var(--van-padding-md);
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .config-card,
  .status-card {
    margin-bottom: var(--van-padding-md);
  }
}

.status-content {
  padding: var(--van-padding-sm) 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--van-padding-sm);
  margin-bottom: var(--van-padding-xs);
}

.status-text {
  font-size: var(--van-font-size-md);
  font-weight: 500;
}

.sync-time {
  font-size: var(--van-font-size-sm);
  color: var(--van-text-color-2);
}
</style> 