<template>
  <div class="group-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><UserFilled /></el-icon>
        <h2 class="page-title">群组管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/email' }">邮箱管理</el-breadcrumb-item>
        <el-breadcrumb-item>群组管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 主要内容 -->
    <el-card class="main-card">
      <!-- 工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <Authority :value="['email:group:create']">
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新增群组
            </el-button>
          </Authority>
          <Authority :value="['email:group:sync']">
            <el-button type="success" @click="handleSync" :loading="syncLoading">
              <el-icon><Refresh /></el-icon>
              同步群组
            </el-button>
          </Authority>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索群组名称、描述"
            style="width: 250px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="handleSearch" style="margin-left: 10px">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 群组表格 -->
      <el-table
        v-loading="loading"
        :data="groupList"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="groupid" label="群组ID" width="150" />
        <el-table-column prop="groupname" label="群组名称" width="200" />
        <el-table-column prop="groupdesc" label="群组描述" min-width="250" show-overflow-tooltip />
        <el-table-column prop="userlist" label="成员列表" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag v-if="row.userlist" size="small">
              {{ row.userlist.split(',').length }} 个成员
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <Authority :value="['email:group:view']">
              <el-button type="primary" size="small" @click.stop="handleView(row)">
                查看
              </el-button>
            </Authority>
            <Authority :value="['email:group:update']">
              <el-button type="warning" size="small" @click.stop="handleEdit(row)">
                编辑
              </el-button>
            </Authority>
            <Authority :value="['email:group:delete']">
              <el-button type="danger" size="small" @click.stop="handleDelete(row)">
                删除
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="群组ID" prop="groupid">
          <el-input
            v-model="form.groupid"
            placeholder="请输入群组ID"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="群组名称" prop="groupname">
          <el-input v-model="form.groupname" placeholder="请输入群组名称" />
        </el-form-item>
        <el-form-item label="群组描述" prop="groupdesc">
          <el-input
            v-model="form.groupdesc"
            type="textarea"
            :rows="3"
            placeholder="请输入群组描述"
          />
        </el-form-item>
        <el-form-item label="成员列表" prop="userlist">
          <div style="width: 100%">
            <el-select
              v-model="selectedMembers"
              multiple
              filterable
              placeholder="请选择群组成员"
              style="width: 100%"
              @change="handleMemberChange"
            >
              <el-option
                v-for="member in memberOptions"
                :key="member.email"
                :label="`${member.name} (${member.email})`"
                :value="member.email"
              />
            </el-select>
            <div class="member-tags" v-if="selectedMembers.length > 0">
              <el-tag
                v-for="email in selectedMembers"
                :key="email"
                closable
                @close="removeMember(email)"
                style="margin: 5px 5px 0 0"
              >
                {{ getMemberName(email) }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="form.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="群组详情"
      width="600px"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="群组ID">{{ viewData.groupid }}</el-descriptions-item>
        <el-descriptions-item label="群组名称">{{ viewData.groupname }}</el-descriptions-item>
        <el-descriptions-item label="群组描述">{{ viewData.groupdesc || '-' }}</el-descriptions-item>
        <el-descriptions-item label="成员列表">
          <div v-if="viewData.userlist">
            <el-tag
              v-for="email in viewData.userlist.split(',')"
              :key="email"
              style="margin: 2px"
              size="small"
            >
              {{ getMemberName(email) }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewData.is_active ? 'success' : 'danger'">
            {{ viewData.is_active ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(viewData.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDateTime(viewData.updated_at) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled, Plus, Refresh, Search } from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'
import { getGroups, createGroup, updateGroup, deleteGroup, syncGroupsFromApi } from '@/api/email/group'
import { getMembers } from '@/api/email/member'
import { formatDateTime } from '@/utils/date'
import type { EmailGroup, EmailGroupForm, Member, GroupSearchParams } from '@/types/email'

// 响应式数据
const loading = ref(false)
const syncLoading = ref(false)
const submitLoading = ref(false)
const groupList = ref<EmailGroup[]>([])
const memberOptions = ref<Member[]>([])
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const selectedMembers = ref<string[]>([])

// 搜索表单
const searchForm = reactive({
  search: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  groupid: '',
  groupname: '',
  groupdesc: '',
  userlist: '',
  is_active: true
})

// 查看数据
const viewData = ref<EmailGroup>({} as EmailGroup)

// 表单验证规则
const formRules = {
  groupid: [
    { required: true, message: '请输入群组ID', trigger: 'blur' }
  ],
  groupname: [
    { required: true, message: '请输入群组名称', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑群组' : '新增群组'
})

// 获取群组列表
const getGroupList = async () => {
  try {
    loading.value = true
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...searchForm
    }
    const response = await getGroups(params)
    groupList.value = response.data.items || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取群组列表失败')
  } finally {
    loading.value = false
  }
}

// 获取成员选项
const getMemberOptions = async () => {
  try {
    const response = await getMembers({ size: 100 })
    memberOptions.value = response.data.items || []
  } catch (error) {
    ElMessage.error('获取成员列表失败')
  }
}

// 获取成员名称
const getMemberName = (email: string) => {
  const member = memberOptions.value.find((m: any) => m.email === email)
  return member ? `${member.name} (${member.email})` : email
}

// 处理成员变化
const handleMemberChange = () => {
  form.userlist = selectedMembers.value.join(',')
}

// 移除成员
const removeMember = (email: string) => {
  const index = selectedMembers.value.indexOf(email)
  if (index > -1) {
    selectedMembers.value.splice(index, 1)
    handleMemberChange()
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getGroupList()
}

// 新增
const handleCreate = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑
const handleEdit = (row: any) => {
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(form, row)
  selectedMembers.value = row.userlist ? row.userlist.split(',') : []
}

// 查看
const handleView = (row: any) => {
  viewData.value = row
  viewDialogVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除群组 "${row.groupname}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteGroup(row.groupid)
    ElMessage.success('删除成功')
    getGroupList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 同步群组
const handleSync = async () => {
  try {
    syncLoading.value = true
    await syncGroupsFromApi()
    ElMessage.success('同步成功')
    getGroupList()
  } catch (error) {
    ElMessage.error('同步失败')
  } finally {
    syncLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      await updateGroup(form.groupid, form)
      ElMessage.success('更新成功')
    } else {
      await createGroup(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    getGroupList()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    groupid: '',
    groupname: '',
    groupdesc: '',
    userlist: '',
    is_active: true
  })
  selectedMembers.value = []
  formRef.value?.clearValidate()
}

// 对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 行点击
const handleRowClick = (row: any) => {
  handleView(row)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  getGroupList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  getGroupList()
}

// 初始化
onMounted(() => {
  getGroupList()
  getMemberOptions()
})
</script>

<style scoped>
.group-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  font-size: 24px;
  color: #409eff;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.main-card {
  min-height: calc(100vh - 200px);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

.member-tags {
  margin-top: 10px;
}

:deep(.el-table) {
  .el-table__row {
    cursor: pointer;
  }
  
  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}
</style> 