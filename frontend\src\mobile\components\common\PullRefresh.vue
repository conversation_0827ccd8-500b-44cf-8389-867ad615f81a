<template>
  <van-pull-refresh
    v-model="refreshing"
    :disabled="disabled"
    :success-text="successText"
    :success-duration="successDuration"
    :animation-duration="animationDuration"
    :head-height="headHeight"
    :pulling-text="pullingText"
    :loosing-text="loosingText"
    :loading-text="loadingText"
    @refresh="handleRefresh"
    class="mobile-pull-refresh"
  >
    <!-- 自定义下拉头部 -->
    <template #normal>
      <div class="pull-refresh-head">
        <van-icon name="arrow-down" class="pull-refresh-icon" />
        <span class="pull-refresh-text">{{ pullingText }}</span>
      </div>
    </template>
    
    <template #pulling="{ distance }">
      <div class="pull-refresh-head">
        <van-icon 
          name="arrow-down" 
          class="pull-refresh-icon"
          :style="{ transform: `rotate(${Math.min(distance / headHeight * 180, 180)}deg)` }"
        />
        <span class="pull-refresh-text">{{ pullingText }}</span>
      </div>
    </template>
    
    <template #loosing="{ distance }">
      <div class="pull-refresh-head">
        <van-icon 
          name="arrow-up" 
          class="pull-refresh-icon pull-refresh-icon--loosing"
        />
        <span class="pull-refresh-text">{{ loosingText }}</span>
      </div>
    </template>
    
    <template #loading>
      <div class="pull-refresh-head">
        <van-loading 
          type="spinner" 
          size="18" 
          color="var(--van-primary-color)"
          class="pull-refresh-loading"
        />
        <span class="pull-refresh-text">{{ loadingText }}</span>
      </div>
    </template>
    
    <template #success>
      <div class="pull-refresh-head">
        <van-icon 
          name="success" 
          class="pull-refresh-icon pull-refresh-icon--success"
        />
        <span class="pull-refresh-text">{{ successText }}</span>
      </div>
    </template>
    
    <!-- 内容区域 -->
    <div class="pull-refresh-content">
      <slot />
    </div>
  </van-pull-refresh>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useVibrate } from '@vueuse/core'

interface Props {
  // 下拉刷新控制
  loading?: boolean
  disabled?: boolean
  
  // 文本配置
  pullingText?: string
  loosingText?: string
  loadingText?: string
  successText?: string
  
  // 动画配置
  headHeight?: number
  successDuration?: number
  animationDuration?: number
  
  // 触觉反馈
  enableVibrate?: boolean
}

interface Emits {
  (e: 'refresh'): Promise<void> | void
  (e: 'update:loading', loading: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false,
  pullingText: '下拉即可刷新...',
  loosingText: '释放即可刷新...',
  loadingText: '加载中...',
  successText: '刷新成功',
  headHeight: 50,
  successDuration: 500,
  animationDuration: 300,
  enableVibrate: true
})

const emit = defineEmits<Emits>()

// 触觉反馈
const { vibrate, isSupported: isVibrateSupported } = useVibrate({
  pattern: [50] // 轻微震动50ms
})

// 内部刷新状态
const refreshing = ref(false)

// 处理下拉刷新
const handleRefresh = async () => {
  refreshing.value = true
  emit('update:loading', true)
  
  // 触觉反馈
  if (props.enableVibrate && isVibrateSupported.value) {
    vibrate()
  }
  
  try {
    await emit('refresh')
  } catch (error) {
    console.error('Refresh failed:', error)
  } finally {
    // 延迟关闭，确保用户能看到刷新完成状态
    setTimeout(() => {
      refreshing.value = false
      emit('update:loading', false)
    }, props.successDuration)
  }
}

// 计算样式
const headHeightStyle = computed(() => ({
  '--van-pull-refresh-head-height': `${props.headHeight}px`
}))
</script>

<style lang="scss" scoped>
.mobile-pull-refresh {
  height: 100%;
  
  // 自定义CSS变量
  --van-pull-refresh-head-height: v-bind('props.headHeight + "px"');
  --van-pull-refresh-head-font-size: 14px;
  --van-pull-refresh-head-text-color: var(--van-text-color-2);
  --van-pull-refresh-loading-icon-size: 18px;
}

.pull-refresh-head {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 16px;
}

.pull-refresh-icon {
  font-size: 20px;
  color: var(--van-text-color-2);
  margin-bottom: 4px;
  transition: all var(--theme-duration-base) ease;
  
  &--loosing {
    color: var(--van-primary-color);
    animation: pullRefreshBounce 0.3s ease;
  }
  
  &--success {
    color: var(--van-success-color);
    animation: pullRefreshSuccess 0.5s ease;
  }
}

.pull-refresh-text {
  font-size: 14px;
  color: var(--van-text-color-2);
  line-height: 1.4;
  text-align: center;
}

.pull-refresh-loading {
  margin-bottom: 4px;
}

.pull-refresh-content {
  min-height: 100%;
  background-color: var(--van-background);
}

// 动画效果
@keyframes pullRefreshBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes pullRefreshSuccess {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}



// 紧凑模式适配
.compact-mode {
  .mobile-pull-refresh {
    --van-pull-refresh-head-height: 40px;
    --van-pull-refresh-head-font-size: 12px;
  }
  
  .pull-refresh-icon {
    font-size: 18px;
  }
  
  .pull-refresh-text {
    font-size: 12px;
  }
}

// 横屏适配
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-pull-refresh {
    --van-pull-refresh-head-height: 35px;
  }
  
  .pull-refresh-head {
    flex-direction: row;
    padding: 0 12px;
  }
  
  .pull-refresh-icon {
    margin-bottom: 0;
    margin-right: 6px;
    font-size: 16px;
  }
  
  .pull-refresh-text {
    font-size: 12px;
  }
}

// 容器查询支持
@supports (container-type: inline-size) {
  .mobile-pull-refresh {
    container-type: inline-size;
  }
  
  @container (max-width: 320px) {
    .pull-refresh-text {
      font-size: 12px;
    }
    
    .pull-refresh-icon {
      font-size: 18px;
    }
  }
}
</style> 