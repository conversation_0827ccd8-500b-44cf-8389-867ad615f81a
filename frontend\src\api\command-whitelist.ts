import request from '@/utils/request'
import type {
  CommandCategory,
  CommandCategoryCreate,
  CommandCategoryUpdate,
  CommandWhitelist,
  CommandWhitelistCreate,
  CommandWhitelistUpdate,
  CommandTemplateResponse,
  CommandValidationRequest,
  CommandValidationResponse,
  PaginatedResponse
} from '@/types/command'

export const commandWhitelistApi = {
  // 命令分类管理
  getCategories: (params: {
    skip?: number
    limit?: number
    is_active?: boolean
  } = {}) => {
    return request.get<CommandCategory[]>('/command-whitelist/categories', { params })
  },

  createCategory: (data: CommandCategoryCreate) => {
    return request.post<CommandCategory>('/command-whitelist/categories', data)
  },

  getCategory: (id: number) => {
    return request.get<CommandCategory>(`/command-whitelist/categories/${id}`)
  },

  updateCategory: (id: number, data: CommandCategoryUpdate) => {
    return request.put<CommandCategory>(`/command-whitelist/categories/${id}`, data)
  },

  deleteCategory: (id: number) => {
    return request.delete(`/command-whitelist/categories/${id}`)
  },

  // 命令白名单管理
  getCommands: (params: {
    skip?: number
    limit?: number
    category_id?: number
    is_active?: boolean
    security_level?: string
    search?: string
  } = {}) => {
    return request.get<CommandWhitelist[]>('/command-whitelist', { params })
  },

  createCommand: (data: CommandWhitelistCreate) => {
    return request.post<CommandWhitelist>('/command-whitelist', data)
  },

  getCommand: (id: number) => {
    return request.get<CommandWhitelist>(`/command-whitelist/${id}`)
  },

  updateCommand: (id: number, data: CommandWhitelistUpdate) => {
    return request.put<CommandWhitelist>(`/command-whitelist/${id}`, data)
  },

  deleteCommand: (id: number) => {
    return request.delete(`/command-whitelist/${id}`)
  },

  // 命令模板和验证
  getTemplates: () => {
    return request.get<CommandTemplateResponse>('/command-whitelist/templates')
  },

  validateCommand: (data: CommandValidationRequest) => {
    return request.post<CommandValidationResponse>('/command-whitelist/validate', data)
  },

  getUserAvailableCommands: () => {
    return request.get<CommandWhitelist[]>('/command-whitelist/user/available')
  },

  // 批量操作
  batchImportCommands: (data: any[]) => {
    return request.post('/command-whitelist/batch/import', data)
  },

  initializeDefaultCommands: () => {
    return request.post('/command-whitelist/batch/default')
  }
} 