import requests
import json

def test_login_permissions_api():
    """测试登录权限API"""
    base_url = "http://localhost:8000"
    
    # 首先需要获取认证token
    # 使用默认的测试用户登录
    login_data = {
        "username": "admin",
        "password": "password"
    }
    
    try:
        # 登录获取token
        print("1. 尝试登录...")
        login_response = requests.post(f"{base_url}/api/v1/auth/login", data=login_data)
        print(f"登录响应状态: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"登录失败: {login_response.text}")
            return
        
        login_result = login_response.json()
        access_token = login_result.get("access_token")
        
        if not access_token:
            print("未获取到access_token")
            return
        
        print("登录成功!")
        
        # 设置认证头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试获取登录权限
        print("\n2. 测试获取登录权限...")
        userid = "<EMAIL>"
        url = f"{base_url}/api/v1/email/members/{userid}/login-permissions"
        
        response = requests.get(url, headers=headers)
        print(f"API响应状态: {response.status_code}")
        print(f"API响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 登录权限获取成功!")
            print(f"错误码: {result.get('errcode')}")
            print(f"错误消息: {result.get('errmsg')}")
            if result.get('option'):
                print("权限设置:")
                for option in result.get('option', []):
                    print(f"  类型{option.get('type')}: {option.get('value')}")
        else:
            print("❌ 登录权限获取失败!")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_login_permissions_api() 