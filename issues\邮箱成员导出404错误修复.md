# 邮箱成员导出404错误修复

## 问题描述
用户在使用邮箱成员导出功能时遇到404错误，请求路径：`/api/v1/email/members/export`

## 问题分析
经过调查发现，问题是FastAPI路由匹配顺序导致的：
1. 在 `backend/app/api/v1/email.py` 文件中，`@router.get("/members/{userid}")` 路由定义在第447行
2. `@router.get("/members/export")` 路由定义在第1416行
3. FastAPI按定义顺序匹配路由，当请求 `/members/export` 时，先匹配到 `/members/{userid}` 路由
4. 系统将 `export` 当作 `userid` 参数处理，导致404错误

## 解决方案
调整路由定义顺序，将具体路径 `/members/export` 移动到动态路径参数 `/members/{userid}` 之前。

## 修复步骤
1. 删除原位置（第1416行）的 `/members/export` 路由定义
2. 在 `/members/{userid}` 路由之前（第447行前）重新添加 `/members/export` 路由
3. 确保路由顺序为：
   - `/members` (列表)
   - `/members/export` (导出，具体路径)
   - `/members/{userid}` (详情，动态参数)

## 修复后的路由顺序
```
@router.get("/members") - 成员列表
@router.get("/members/export") - 成员导出
@router.get("/members/{userid}") - 成员详情
```

## 涉及文件
- `backend/app/api/v1/email.py` - 主要修改文件

## 预期结果
- API路径 `/api/v1/email/members/export` 能正确响应请求
- 用户能成功下载CSV或XLSX格式的成员列表
- 不影响其他成员相关的API功能

## 测试建议
1. 重启后端服务
2. 测试成员导出功能，确认返回200状态码
3. 验证导出的文件格式和内容正确
4. 确认其他成员API（列表、详情、创建、更新、删除）正常工作 