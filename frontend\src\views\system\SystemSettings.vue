<template>
  <div class="system-settings">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Setting /></el-icon>
        <h2 class="page-title">权限管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>系统设置</el-breadcrumb-item>
        <el-breadcrumb-item>权限管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="系统权限" name="permissions">
          <PermissionManagement v-if="activeTab === 'permissions'" />
        </el-tab-pane>
        <el-tab-pane label="角色管理" name="roles">
          <RoleManagement v-if="activeTab === 'roles'" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PermissionManagement from './components/PermissionManagement.vue'
import RoleManagement from './components/RoleManagement.vue'
import { Setting } from '@element-plus/icons-vue'

const activeTab = ref('permissions')

const handleTabClick = () => {
  // 确保标签切换时内容更新
  console.log('切换到标签：', activeTab.value)
}
</script>

<style scoped>
.system-settings {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}
</style> 