# AD同步日志字典比较类型错误修复

## 问题描述
在AD同步操作中，出现以下错误：
```
添加同步日志失败: '>' not supported between instances of 'dict' and 'int'
```

## 错误原因分析
在 `backend/app/services/ad.py` 的 `sync_from_personnel` 函数中，`stats` 字典存在重复键定义问题：

```python
stats = {
    "created_groups": 0,        # 数字类型
    # ... 其他字段 ...
    "created_groups": [],       # 列表类型，覆盖了前面的数字
}
```

当 `add_sync_log` 函数尝试进行数值比较时：
```python
stats.get("created_groups", 0) > 0  # 尝试用列表与整数比较
```

## 修复方案
将统计数据结构重新设计，分离计数字段和详细信息字段：

### 修复前：
```python
stats = {
    "created_groups": 0,
    "created_groups": [],  # 重复键，覆盖前面的值
}
```

### 修复后：
```python
stats = {
    "created_groups": 0,                    # 计数字段
    "created_groups_details": [],          # 详细信息字段
    "added_to_groups": 0,                  # 计数字段  
    "added_to_groups_details": [],         # 详细信息字段
    "removed_from_groups": 0,              # 计数字段
    "removed_from_groups_details": [],     # 详细信息字段
    "updated_groups_details": [],          # 更新组详细信息
    "created_ous_details": created_ous_data,    # OU详细信息
    "renamed_ous_details": renamed_ous_data,    # 重命名OU详细信息
}
```

## 涉及文件
- `backend/app/services/ad.py` - 修复stats字典定义和所有.append()调用
- `backend/app/services/ad_sync_config.py` - add_sync_log函数（无需修改，已使用数值字段）

## 测试验证
修复后，AD同步操作应该能够正常完成，不再出现类型比较错误。

## 修复日期
2025-01-15 