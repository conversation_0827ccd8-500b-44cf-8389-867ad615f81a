"""empty message

Revision ID: 1a68b24a3f6b
Revises: 51ee499983b7, 7bb71a229775
Create Date: 2025-03-20 17:08:29.198028

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a68b24a3f6b'
down_revision: Union[str, None] = ('51ee499983b7', '7bb71a229775')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
