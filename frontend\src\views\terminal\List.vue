<template>
  <div class="terminal-list">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Monitor /></el-icon>
        <h2 class="page-title">终端列表</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>终端管理</el-breadcrumb-item>
        <el-breadcrumb-item>终端列表</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <el-input
            v-model="searchQuery"
            clearable
            placeholder="搜索主机名/IP地址/操作系统"
            @keyup.enter="handleSearch"
            @clear="resetSearch"
            :prefix-icon="Search"
            class="search-input">
          </el-input>
        </div>
        <div class="action-buttons">
          <el-button type="success" @click="refreshData">
            <el-icon><Refresh /></el-icon> 刷新数据
          </el-button>
          <Authority permission="terminal:command:send">
            <el-button type="primary" @click="showBatchCommandDialog" v-if="selectedTerminals.length > 0">
              <el-icon><Document /></el-icon> 批量发送命令
            </el-button>
          </Authority>
          <!-- SSE连接状态指示器 -->
          <el-tag 
            :type="sseConnected ? 'success' : 'danger'" 
            size="large" 
            effect="dark"
            class="connection-status-tag">
            <el-icon v-if="sseConnected"><Connection /></el-icon>
            <el-icon v-else><Mute /></el-icon>
            {{ sseConnected ? '实时连接' : '连接断开' }}
          </el-tag>
        </div>
      </div>

      <!-- 状态汇总卡片 -->
      <div class="status-cards">
        <el-card class="status-card online" shadow="hover">
          <div class="status-info">
            <div class="status-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ getOnlineCount }}</div>
              <div class="status-label">在线终端</div>
            </div>
          </div>
        </el-card>
        <el-card class="status-card offline" shadow="hover">
          <div class="status-info">
            <div class="status-icon">
              <el-icon><Mute /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ getOfflineCount }}</div>
              <div class="status-label">离线终端</div>
            </div>
          </div>
        </el-card>
        <el-card class="status-card selected" shadow="hover">
          <div class="status-info">
            <div class="status-icon">
              <el-icon><Select /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ selectedTerminals.length }}</div>
              <div class="status-label">已选终端</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 终端列表 -->
      <el-table
        v-loading="loading"
        :data="terminalList"
        border
        highlight-current-row
        style="width: 100%"
        @selection-change="handleSelectionChange"
        class="terminal-table"
        row-class-name="terminal-table-row"
        header-row-class-name="terminal-table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="hostname" label="主机名" min-width="120" show-overflow-tooltip>
          <template #header>
            <div class="column-header">主机名</div>
          </template>
          <template #default="{ row }">
            <div class="hostname-cell">
              <el-tooltip effect="dark" content="查看详情" placement="top">
                <el-link type="primary" @click="viewDetail(row.id)">{{ row.hostname }}</el-link>
              </el-tooltip>
              <el-tag v-if="row.online_status" size="small" type="success" effect="plain" class="status-icon-tag">
                <el-icon><Connection /></el-icon>
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" min-width="120">
          <template #header>
            <div class="column-header">IP地址</div>
          </template>
          <template #default="{ row }">
            <el-tag size="small" effect="plain">{{ row.ip_address }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mac_address" label="MAC地址" min-width="150" show-overflow-tooltip>
          <template #header>
            <div class="column-header">MAC地址</div>
          </template>
        </el-table-column>
        <el-table-column prop="os_name" label="操作系统" min-width="150" show-overflow-tooltip>
          <template #header>
            <div class="column-header">操作系统</div>
          </template>
          <template #default="{ row }">
            <div class="os-info">
              <el-icon class="os-icon">
                <component :is="getOsIcon(row.os_name)" />
              </el-icon>
              <span>{{ row.os_name || '-' }} {{ row.os_version ? `(${row.os_version})` : '' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="online_status" label="在线状态" width="100">
          <template #header>
            <div class="column-header">在线状态</div>
          </template>
          <template #default="{ row }">
            <el-tag :type="row.online_status ? 'success' : 'danger'" size="small" effect="dark">
              {{ row.online_status ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_heartbeat_time" label="最后心跳时间" min-width="170">
          <template #header>
            <div class="column-header">最后心跳时间</div>
          </template>
          <template #default="{ row }">
            <el-tooltip effect="dark" :content="getTimeAgo(row.last_heartbeat_time)" placement="top">
              <span>{{ row.last_heartbeat_time ? formatTime(row.last_heartbeat_time) : '-' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="250">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="{ row }">
            <div class="action-buttons">
              <Authority permission="terminal:command:send">
                <el-tooltip effect="dark" content="采集信息" placement="top">
                  <el-button
                    type="primary"
                    circle
                    size="small"
                    :disabled="!row.online_status"
                    @click="collectInfo(row.id)"
                  >
                    <el-icon><Promotion /></el-icon>
                  </el-button>
                </el-tooltip>
              </Authority>
              <Authority permission="terminal:agent:manage">
                <el-tooltip effect="dark" content="升级Agent" placement="top">
                  <el-button
                    type="warning"
                    circle
                    size="small"
                    :disabled="!row.online_status"
                    @click="upgradeAgent(row)"
                  >
                    <el-icon><ArrowUp /></el-icon>
                  </el-button>
                </el-tooltip>
              </Authority>
              <Authority permission="terminal:terminal:delete">
                <el-tooltip effect="dark" content="删除终端" placement="top">
                  <el-button
                    type="danger"
                    circle
                    size="small"
                    @click="confirmDelete(row)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-tooltip>
              </Authority>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 批量发送命令对话框 -->
    <el-dialog
      v-model="batchCommandDialog.visible"
      title="批量发送命令"
      width="550px"
      destroy-on-close
    >
      <div class="dialog-body">
        <el-form ref="batchCommandFormRef" :model="batchCommandDialog.form" label-width="100px">
          <el-form-item label="命令类型" prop="command_type" required>
            <el-select v-model="batchCommandDialog.form.command_type" placeholder="请选择命令类型" class="full-width">
              <el-option label="采集信息" value="COLLECT_INFO" />
              <el-option label="自定义命令" value="CUSTOM_COMMAND" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="batchCommandDialog.form.command_type === 'CUSTOM_COMMAND'" label="命令内容" prop="content" required>
            <el-input
              v-model="batchCommandDialog.form.content"
              type="textarea"
              placeholder="请输入命令内容"
              rows="4"
              class="command-textarea"
            />
          </el-form-item>
          <el-form-item label="超时时间(秒)" prop="timeout">
            <el-input-number v-model="batchCommandDialog.form.timeout" :min="60" :max="86400" />
          </el-form-item>
          <el-form-item label="选中终端">
            <div class="selected-terminals">
              <div class="selection-header">
                已选择 <el-tag type="primary" effect="dark">{{ selectedTerminals.length }}</el-tag> 台终端
              </div>
              <div v-if="selectedTerminals.length > 0" class="terminals-list">
                <el-tag
                  v-for="terminal in selectedTerminals"
                  :key="terminal.id"
                  class="terminal-tag"
                  :type="terminal.online_status ? 'success' : 'danger'"
                  effect="light"
                >
                  <el-icon v-if="terminal.online_status"><Connection /></el-icon>
                  <el-icon v-else><Mute /></el-icon>
                  {{ terminal.hostname }}
                </el-tag>
              </div>
            </div>
            <div v-if="offlineSelected" class="warning-text">
              <el-icon><Warning /></el-icon> 注意：您选择了离线的终端，这些终端将在重新上线后收到命令
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchCommandDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="sendBatchCommand" :loading="batchCommandDialog.loading">
            <el-icon><Promotion /></el-icon> 发送命令
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 升级Agent对话框 -->
    <el-dialog
      v-model="upgradeDialog.visible"
      title="升级Agent"
      width="550px"
      destroy-on-close
    >
      <div class="dialog-body">
        <el-form ref="upgradeFormRef" :model="upgradeDialog.form" label-width="100px">
          <el-form-item label="当前版本">
            <el-tag effect="dark">{{ getAgentVersion(currentTerminal) }}</el-tag>
          </el-form-item>
          <el-form-item label="目标版本" prop="version" required>
            <el-input v-model="upgradeDialog.form.version" placeholder="请输入目标版本号" />
          </el-form-item>
          <el-form-item label="下载地址" prop="download_url" required>
            <el-input v-model="upgradeDialog.form.download_url" placeholder="请输入升级包下载地址" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="upgradeDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpgrade" :loading="upgradeDialog.loading">
            <el-icon><ArrowUp /></el-icon> 升级
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Refresh,
  Document,
  Monitor,
  Search,
  Connection,
  Mute,
  Select,
  Warning,
  Delete,
  Promotion,
  ArrowUp,
  Apple,
  ChromeFilled,
  Platform as PlatformIcon
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { terminalApi } from '@/api/terminal'
import type { TerminalSummary } from '@/types/terminal'
import Authority from '@/components/Authority/index.vue'
import { getTerminalSSEClient, type TerminalStatusUpdate } from '@/utils/sse'

const router = useRouter()

// 数据加载
const loading = ref(false)
const terminalList = ref<TerminalSummary[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedTerminals = ref<TerminalSummary[]>([])
const currentTerminal = ref<TerminalSummary | null>(null)

// SSE连接
const sseClient = getTerminalSSEClient()
const sseConnected = computed(() => sseClient.isConnected.value)

// 搜索表单
const searchQuery = ref('')

// 批量命令对话框
const batchCommandDialog = reactive({
  visible: false,
  loading: false,
  form: {
    command_type: 'COLLECT_INFO',
    content: '',
    timeout: 3600
  }
})

// 升级对话框
const upgradeDialog = reactive({
  visible: false,
  loading: false,
  form: {
    version: '',
    download_url: ''
  }
})

// 计算在线终端数量
const getOnlineCount = computed(() => {
  return terminalList.value.filter(terminal => terminal.online_status).length
})

// 计算离线终端数量
const getOfflineCount = computed(() => {
  return terminalList.value.filter(terminal => !terminal.online_status).length
})

// 计算是否选择了离线终端
const offlineSelected = computed(() => {
  return selectedTerminals.value.some(terminal => !terminal.online_status)
})

// 根据操作系统名称获取图标
const getOsIcon = (osName: string | undefined) => {
  if (!osName) return PlatformIcon

  const lowerOsName = osName.toLowerCase()
  if (lowerOsName.includes('windows')) return ChromeFilled
  if (lowerOsName.includes('mac') || lowerOsName.includes('darwin')) return Apple
  if (lowerOsName.includes('linux') || lowerOsName.includes('ubuntu') || lowerOsName.includes('centos')) return PlatformIcon

  return PlatformIcon
}

// 获取相对时间描述
const getTimeAgo = (time: string) => {
  if (!time) return '未知'

  const now = new Date()
  const past = new Date(time)
  const diff = Math.floor((now.getTime() - past.getTime()) / 1000)

  if (diff < 60) return `${diff}秒前`
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`
  if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`
  if (diff < 2592000) return `${Math.floor(diff / 86400)}天前`
  if (diff < 31536000) return `${Math.floor(diff / 2592000)}个月前`
  return `${Math.floor(diff / 31536000)}年前`
}

// 加载终端列表数据
const fetchTerminalList = async () => {
  loading.value = true
  try {
    const params = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      search: searchQuery.value
    }

    const response = await terminalApi.getTerminals(params)
    terminalList.value = response.data

    // 这里假设后端返回了总数，如果没有则使用当前列表长度
    // 由于API返回类型问题，我们使用数组长度
    total.value = terminalList.value.length
  } catch (error) {
    console.error('获取终端列表失败:', error)
    ElMessage.error('获取终端列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchTerminalList()
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTerminalList()
}

// 重置搜索
const resetSearch = () => {
  // 重置搜索表单
  searchQuery.value = ''

  // 重新加载第一页数据
  currentPage.value = 1
  fetchTerminalList()
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchTerminalList()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchTerminalList()
}

// 处理选择变化
const handleSelectionChange = (selection: TerminalSummary[]) => {
  selectedTerminals.value = selection
}

// 查看终端详情
const viewDetail = (id: string) => {
  router.push(`/terminal/detail/${id}`)
}

// 立即采集信息
const collectInfo = async (id: string) => {
  try {
    await terminalApi.sendCollectInfoCommand(id)
    ElMessage.success('已发送采集信息命令')
  } catch (error) {
    console.error('发送采集信息命令失败:', error)
    ElMessage.error('发送采集信息命令失败')
  }
}

// 升级Agent
const upgradeAgent = (terminal: TerminalSummary) => {
  currentTerminal.value = terminal
  upgradeDialog.visible = true
}

// 确认升级Agent
const confirmUpgrade = async () => {
  if (!currentTerminal.value) return

  upgradeDialog.loading = true
  try {
    await terminalApi.sendUpgradeAgentCommand(
      currentTerminal.value.id,
      upgradeDialog.form
    )
    ElMessage.success('已发送升级命令')
    upgradeDialog.visible = false
  } catch (error) {
    console.error('发送升级命令失败:', error)
    ElMessage.error('发送升级命令失败')
  } finally {
    upgradeDialog.loading = false
  }
}

// 确认删除终端
const confirmDelete = (terminal: TerminalSummary) => {
  ElMessageBox.confirm(
    `确定要删除终端 ${terminal.hostname} 吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 这里假设你有一个删除终端的API
      await terminalApi.deleteTerminal(terminal.id)
      ElMessage.success('删除成功')
      fetchTerminalList()
    } catch (error) {
      console.error('删除终端失败:', error)
      ElMessage.error('删除终端失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 显示批量命令对话框
const showBatchCommandDialog = () => {
  if (selectedTerminals.value.length === 0) {
    ElMessage.warning('请先选择终端')
    return
  }

  batchCommandDialog.visible = true
}

// 发送批量命令
const sendBatchCommand = async () => {
  if (selectedTerminals.value.length === 0) {
    ElMessage.warning('请先选择终端')
    return
  }

  if (batchCommandDialog.form.command_type === 'CUSTOM_COMMAND' && !batchCommandDialog.form.content) {
    ElMessage.warning('请输入命令内容')
    return
  }

  batchCommandDialog.loading = true
  try {
    const data = {
      terminal_ids: selectedTerminals.value.map(item => item.id),
      command_type: batchCommandDialog.form.command_type,
      content: batchCommandDialog.form.command_type === 'COLLECT_INFO'
        ? '批量采集终端信息'
        : batchCommandDialog.form.content,
      timeout: batchCommandDialog.form.timeout
    }

    await terminalApi.sendBatchCommand(data)
    ElMessage.success('批量命令发送成功')
    batchCommandDialog.visible = false
  } catch (error) {
    console.error('批量命令发送失败:', error)
    ElMessage.error('批量命令发送失败')
  } finally {
    batchCommandDialog.loading = false
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 获取Agent版本
const getAgentVersion = (terminal: TerminalSummary | null) => {
  return terminal ? (terminal.agent_version || '未知') : '未知'
}

// 处理终端状态更新
const handleTerminalStatusUpdate = (update: TerminalStatusUpdate) => {
  const { terminal_id, data } = update
  
  // 查找并更新对应的终端
  const terminalIndex = terminalList.value.findIndex(t => t.id === terminal_id.toString())
  if (terminalIndex !== -1) {
    const terminal = terminalList.value[terminalIndex]
    
    // 更新终端状态
    terminal.online_status = data.online_status
    terminal.last_heartbeat_time = data.last_heartbeat_time || undefined
    terminal.hostname = data.hostname
    terminal.ip_address = data.ip_address
    terminal.mac_address = data.mac_address
    
    console.log(`终端状态已更新: ${terminal.hostname} -> ${data.online_status ? '在线' : '离线'}`)
  }
}

// SSE连接状态处理
const handleSSEConnected = () => {
  console.log('SSE连接已建立，终端状态将实时更新')
}

const handleSSEDisconnected = (event: any) => {
  console.log('SSE连接已断开:', event)
}

const handleSSEError = (error: any) => {
  console.error('SSE连接错误:', error)
}

onMounted(() => {
  fetchTerminalList()
  
  // 建立SSE连接
  sseClient.connect()
  
  // 注册SSE事件监听器
  sseClient.on('terminal_status_update', handleTerminalStatusUpdate)
  sseClient.on('connected', handleSSEConnected)
  sseClient.on('disconnected', handleSSEDisconnected)
  sseClient.on('error', handleSSEError)
})

onUnmounted(() => {
  // 取消SSE事件监听器
  sseClient.off('terminal_status_update', handleTerminalStatusUpdate)
  sseClient.off('connected', handleSSEConnected)
  sseClient.off('disconnected', handleSSEDisconnected)
  sseClient.off('error', handleSSEError)
  
  // 断开SSE连接
  sseClient.disconnect()
})
</script>

<style scoped>
.terminal-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  width: 320px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 实时连接状态标签样式优化 */
.connection-status-tag {
  height: 32px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
}

.connection-status-tag .el-icon {
  margin-right: 4px;
}

.status-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.status-card {
  flex: 1;
  transition: transform 0.3s, box-shadow 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.status-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.status-card.online {
  border-left: 4px solid #67C23A;
}

.status-card.offline {
  border-left: 4px solid #F56C6C;
}

.status-card.selected {
  border-left: 4px solid #409EFF;
}

.status-info {
  display: flex;
  align-items: center;
  padding: 20px;
}

.status-icon {
  font-size: 36px;
  margin-right: 15px;
  color: #909399;
}

.status-card.online .status-icon {
  color: #67C23A;
}

.status-card.offline .status-icon {
  color: #F56C6C;
}

.status-card.selected .status-icon {
  color: #409EFF;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 5px;
}

.status-label {
  font-size: 14px;
  color: #909399;
}

.terminal-table {
  margin-bottom: 20px;
}

.terminal-table-row {
  transition: all 0.3s;
  height: 56px;
}

.terminal-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.terminal-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.hostname-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon-tag {
  margin-left: 5px;
}

.os-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.os-icon {
  font-size: 16px;
}

/* 对话框相关样式 */
.dialog-body {
  padding: 10px 0;
}

.selected-terminals {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
  max-height: 200px;
  overflow-y: auto;
}

.selection-header {
  margin-bottom: 10px;
}

.terminals-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.terminal-tag {
  display: flex;
  align-items: center;
  gap: 5px;
}

.warning-text {
  color: #E6A23C;
  font-size: 12px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.full-width {
  width: 100%;
}

.command-textarea {
  font-family: monospace;
}
</style>