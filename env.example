# OPS平台Docker环境配置
# 复制此文件为 .env 并根据实际情况修改

# PostgreSQL数据库配置
POSTGRES_DB=ops_platform
POSTGRES_USER=ops_user
POSTGRES_PASSWORD=ops_password

# Redis缓存配置
REDIS_PASSWORD=redis_password

# 应用配置
NODE_ENV=production
PYTHON_ENV=production

# 时区设置
TZ=Asia/Shanghai

# 日志级别
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# 外部服务配置（可选）
# 如果使用外部PostgreSQL和Redis，可以注释掉对应的服务
# EXTERNAL_POSTGRES_HOST=your-postgres-host
# EXTERNAL_POSTGRES_PORT=5432
# EXTERNAL_POSTGRES_USER=your-user
# EXTERNAL_POSTGRES_PASSWORD=your-password
# EXTERNAL_POSTGRES_DB=your-database

# EXTERNAL_REDIS_HOST=your-redis-host
# EXTERNAL_REDIS_PORT=6379
# EXTERNAL_REDIS_PASSWORD=your-redis-password
