# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: terminal.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eterminal.proto\x12\x08terminal\"\x87\x01\n\x0fRegisterRequest\x12\x10\n\x08hostname\x18\x01 \x01(\t\x12\x13\n\x0bmac_address\x18\x02 \x01(\t\x12\x12\n\nip_address\x18\x03 \x01(\t\x12\x15\n\ragent_version\x18\x04 \x01(\t\x12\x0f\n\x07os_info\x18\x05 \x01(\t\x12\x11\n\tunique_id\x18\x06 \x01(\t\"\x82\x01\n\x10RegisterResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12\x1a\n\x12heartbeat_interval\x18\x03 \x01(\x05\x12\x1b\n\x13\x63ollection_interval\x18\x04 \x01(\x05\x12\x0f\n\x07message\x18\x05 \x01(\t\":\n\x10HeartbeatRequest\x12\x13\n\x0bterminal_id\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\x03\"9\n\x11HeartbeatResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x13\n\x0bhas_command\x18\x02 \x01(\x08\"\x89\x02\n\x12TerminalInfoReport\x12\x13\n\x0bterminal_id\x18\x01 \x01(\t\x12(\n\x08hardware\x18\x02 \x01(\x0b\x32\x16.terminal.HardwareInfo\x12\x1c\n\x02os\x18\x03 \x01(\x0b\x32\x10.terminal.OSInfo\x12.\n\x12installed_software\x18\x04 \x03(\x0b\x32\x12.terminal.Software\x12&\n\x07network\x18\x05 \x01(\x0b\x32\x15.terminal.NetworkInfo\x12+\n\x0flast_login_user\x18\x06 \x01(\x0b\x32\x12.terminal.UserInfo\x12\x11\n\ttimestamp\x18\x07 \x01(\x03\"\xa9\x01\n\x0cHardwareInfo\x12\x11\n\tcpu_model\x18\x01 \x01(\t\x12\x11\n\tcpu_cores\x18\x02 \x01(\x05\x12\x14\n\x0cmemory_total\x18\x03 \x01(\x03\x12!\n\x05\x64isks\x18\x04 \x03(\x0b\x32\x12.terminal.DiskInfo\x12\x15\n\rserial_number\x18\x05 \x01(\t\x12\x14\n\x0cmanufacturer\x18\x06 \x01(\t\x12\r\n\x05model\x18\x07 \x01(\t\"j\n\x08\x44iskInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0btotal_space\x18\x02 \x01(\x03\x12\x12\n\nfree_space\x18\x03 \x01(\x03\x12\x12\n\nfilesystem\x18\x04 \x01(\t\x12\x13\n\x0bmount_point\x18\x05 \x01(\t\"\xa7\x01\n\x06OSInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\r\n\x05\x62uild\x18\x03 \x01(\t\x12\x14\n\x0c\x61rchitecture\x18\x04 \x01(\t\x12\x14\n\x0cinstall_date\x18\x05 \x01(\t\x12\x19\n\x11installed_updates\x18\x06 \x03(\t\x12(\n\x08security\x18\x07 \x01(\x0b\x32\x16.terminal.SecurityInfo\"V\n\x0cSecurityInfo\x12\x18\n\x10\x66irewall_enabled\x18\x01 \x01(\x08\x12\x11\n\tantivirus\x18\x02 \x01(\t\x12\x19\n\x11\x61ntivirus_enabled\x18\x03 \x01(\x08\"z\n\x08Software\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x11\n\tpublisher\x18\x03 \x01(\t\x12\x14\n\x0cinstall_date\x18\x04 \x01(\t\x12\x0c\n\x04size\x18\x05 \x01(\x03\x12\x18\n\x10install_location\x18\x06 \x01(\t\"\x8d\x01\n\x0bNetworkInfo\x12.\n\ninterfaces\x18\x01 \x03(\x0b\x32\x1a.terminal.NetworkInterface\x12\x10\n\x08hostname\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\x12\x13\n\x0b\x64ns_servers\x18\x04 \x03(\t\x12\x17\n\x0f\x64\x65\x66\x61ult_gateway\x18\x05 \x01(\t\"\x8a\x01\n\x10NetworkInterface\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bmac_address\x18\x02 \x01(\t\x12\x12\n\nip_address\x18\x03 \x01(\t\x12\x13\n\x0bsubnet_mask\x18\x04 \x01(\t\x12\x14\n\x0c\x64hcp_enabled\x18\x05 \x01(\x08\x12\x14\n\x0cis_connected\x18\x06 \x01(\x08\"S\n\x08UserInfo\x12\x10\n\x08username\x18\x01 \x01(\t\x12\x11\n\tfull_name\x18\x02 \x01(\t\x12\x12\n\nlogin_time\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x04 \x01(\t\"2\n\x0eReportResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"%\n\x0e\x43ommandRequest\x12\x13\n\x0bterminal_id\x18\x01 \x01(\t\"\xf9\x01\n\x07\x43ommand\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12+\n\x04type\x18\x02 \x01(\x0e\x32\x1d.terminal.Command.CommandType\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x13\n\x0b\x63reate_time\x18\x04 \x01(\x03\x12\x0f\n\x07timeout\x18\x05 \x01(\x05\"v\n\x0b\x43ommandType\x12\x10\n\x0c\x43OLLECT_INFO\x10\x00\x12\x11\n\rUPGRADE_AGENT\x10\x01\x12\x12\n\x0e\x43USTOM_COMMAND\x10\x02\x12\x16\n\x12UNINSTALL_SOFTWARE\x10\x03\x12\x16\n\x12REGISTRY_OPERATION\x10\x04\"\x9c\x01\n\rCommandResult\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x0e\n\x06output\x18\x04 \x01(\t\x12\r\n\x05\x65rror\x18\x05 \x01(\t\x12\x16\n\x0e\x65xecution_time\x18\x06 \x01(\x03\x12\x1a\n\x12\x65xecution_duration\x18\x07 \x01(\x05\":\n\x15\x43ommandResultResponse\x12\x10\n\x08received\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\">\n\x13\x43ommandNotification\x12\x13\n\x0bterminal_id\x18\x01 \x01(\t\x12\x12\n\ncommand_id\x18\x02 \x01(\t\"9\n\x14NotificationResponse\x12\x10\n\x08received\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xc1\x02\n\x18RegistryOperationRequest\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12\x32\n\toperation\x18\x03 \x01(\x0e\x32\x1f.terminal.RegistryOperationType\x12+\n\x08root_key\x18\x04 \x01(\x0e\x32\x19.terminal.RegistryRootKey\x12\x14\n\x0csub_key_path\x18\x05 \x01(\t\x12\x12\n\nvalue_name\x18\x06 \x01(\t\x12/\n\nvalue_type\x18\x07 \x01(\x0e\x32\x1b.terminal.RegistryValueType\x12\x12\n\nvalue_data\x18\x08 \x01(\t\x12\x15\n\rcreate_backup\x18\t \x01(\x08\x12\x15\n\rbackup_reason\x18\n \x01(\t\"d\n\rRegistryValue\x12\x0c\n\x04name\x18\x01 \x01(\t\x12)\n\x04type\x18\x02 \x01(\x0e\x32\x1b.terminal.RegistryValueType\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\t\x12\x0c\n\x04size\x18\x04 \x01(\x03\"\xac\x01\n\x0bRegistryKey\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfull_path\x18\x02 \x01(\t\x12\x10\n\x08sub_keys\x18\x03 \x03(\t\x12\'\n\x06values\x18\x04 \x03(\x0b\x32\x17.terminal.RegistryValue\x12\x15\n\rlast_modified\x18\x05 \x01(\x03\x12\x15\n\rsub_key_count\x18\x06 \x01(\x05\x12\x13\n\x0bvalue_count\x18\x07 \x01(\x05\"\xf6\x01\n\x19RegistryOperationResponse\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x0f\n\x07message\x18\x04 \x01(\t\x12\r\n\x05\x65rror\x18\x05 \x01(\t\x12\'\n\x08key_data\x18\x06 \x01(\x0b\x32\x15.terminal.RegistryKey\x12+\n\nvalue_data\x18\x07 \x01(\x0b\x32\x17.terminal.RegistryValue\x12\x11\n\tbackup_id\x18\x08 \x01(\t\x12\x16\n\x0eoperation_time\x18\t \x01(\x03\"\x82\x02\n\x15RegistrySearchRequest\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12+\n\x08root_key\x18\x03 \x01(\x0e\x32\x19.terminal.RegistryRootKey\x12\x12\n\nstart_path\x18\x04 \x01(\t\x12\x16\n\x0esearch_pattern\x18\x05 \x01(\t\x12\x13\n\x0bsearch_keys\x18\x06 \x01(\x08\x12\x15\n\rsearch_values\x18\x07 \x01(\x08\x12\x13\n\x0bsearch_data\x18\x08 \x01(\x08\x12\x11\n\tmax_depth\x18\t \x01(\x05\x12\x13\n\x0bmax_results\x18\n \x01(\x05\"t\n\x14RegistrySearchResult\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x12\n\nmatch_type\x18\x02 \x01(\t\x12\x12\n\nmatch_text\x18\x03 \x01(\t\x12&\n\x05value\x18\x04 \x01(\x0b\x32\x17.terminal.RegistryValue\"\xc1\x01\n\x16RegistrySearchResponse\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x13\n\x0bterminal_id\x18\x02 \x01(\t\x12\x0f\n\x07success\x18\x03 \x01(\x08\x12\x0f\n\x07message\x18\x04 \x01(\t\x12/\n\x07results\x18\x05 \x03(\x0b\x32\x1e.terminal.RegistrySearchResult\x12\x15\n\rtotal_results\x18\x06 \x01(\x05\x12\x14\n\x0cmore_results\x18\x07 \x01(\x08\"\xbd\x01\n\x0eRegistryBackup\x12\x11\n\tbackup_id\x18\x01 \x01(\t\x12\x13\n\x0b\x62\x61\x63kup_name\x18\x02 \x01(\t\x12+\n\x08root_key\x18\x03 \x01(\x0e\x32\x19.terminal.RegistryRootKey\x12\x10\n\x08key_path\x18\x04 \x01(\t\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x13\n\x0b\x63reate_time\x18\x06 \x01(\x03\x12\x0c\n\x04size\x18\x07 \x01(\x03\x12\x11\n\tfile_path\x18\x08 \x01(\t*\x8b\x01\n\x15RegistryOperationType\x12\x08\n\x04READ\x10\x00\x12\t\n\x05WRITE\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\x12\x0e\n\nCREATE_KEY\x10\x03\x12\x0e\n\nDELETE_KEY\x10\x04\x12\r\n\tENUMERATE\x10\x05\x12\n\n\x06\x45XPORT\x10\x06\x12\n\n\x06IMPORT\x10\x07\x12\n\n\x06\x42\x41\x43KUP\x10\x08*r\n\x11RegistryValueType\x12\n\n\x06REG_SZ\x10\x00\x12\x11\n\rREG_EXPAND_SZ\x10\x01\x12\x0e\n\nREG_BINARY\x10\x02\x12\r\n\tREG_DWORD\x10\x03\x12\r\n\tREG_QWORD\x10\x04\x12\x10\n\x0cREG_MULTI_SZ\x10\x05*\x80\x01\n\x0fRegistryRootKey\x12\x15\n\x11HKEY_CLASSES_ROOT\x10\x00\x12\x15\n\x11HKEY_CURRENT_USER\x10\x01\x12\x16\n\x12HKEY_LOCAL_MACHINE\x10\x02\x12\x0e\n\nHKEY_USERS\x10\x03\x12\x17\n\x13HKEY_CURRENT_CONFIG\x10\x04\x32\x90\x05\n\x12TerminalManagement\x12I\n\x10RegisterTerminal\x12\x19.terminal.RegisterRequest\x1a\x1a.terminal.RegisterResponse\x12\x44\n\tHeartbeat\x12\x1a.terminal.HeartbeatRequest\x1a\x1b.terminal.HeartbeatResponse\x12L\n\x12ReportTerminalInfo\x12\x1c.terminal.TerminalInfoReport\x1a\x18.terminal.ReportResponse\x12@\n\x0fReceiveCommands\x12\x18.terminal.CommandRequest\x1a\x11.terminal.Command0\x01\x12O\n\x13ReportCommandResult\x12\x17.terminal.CommandResult\x1a\x1f.terminal.CommandResultResponse\x12N\n\rNotifyCommand\x12\x1d.terminal.CommandNotification\x1a\x1e.terminal.NotificationResponse\x12\x63\n\x18PerformRegistryOperation\x12\".terminal.RegistryOperationRequest\x1a#.terminal.RegistryOperationResponse\x12S\n\x0eSearchRegistry\x12\x1f.terminal.RegistrySearchRequest\x1a .terminal.RegistrySearchResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'terminal_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_REGISTRYOPERATIONTYPE']._serialized_start=4021
  _globals['_REGISTRYOPERATIONTYPE']._serialized_end=4160
  _globals['_REGISTRYVALUETYPE']._serialized_start=4162
  _globals['_REGISTRYVALUETYPE']._serialized_end=4276
  _globals['_REGISTRYROOTKEY']._serialized_start=4279
  _globals['_REGISTRYROOTKEY']._serialized_end=4407
  _globals['_REGISTERREQUEST']._serialized_start=29
  _globals['_REGISTERREQUEST']._serialized_end=164
  _globals['_REGISTERRESPONSE']._serialized_start=167
  _globals['_REGISTERRESPONSE']._serialized_end=297
  _globals['_HEARTBEATREQUEST']._serialized_start=299
  _globals['_HEARTBEATREQUEST']._serialized_end=357
  _globals['_HEARTBEATRESPONSE']._serialized_start=359
  _globals['_HEARTBEATRESPONSE']._serialized_end=416
  _globals['_TERMINALINFOREPORT']._serialized_start=419
  _globals['_TERMINALINFOREPORT']._serialized_end=684
  _globals['_HARDWAREINFO']._serialized_start=687
  _globals['_HARDWAREINFO']._serialized_end=856
  _globals['_DISKINFO']._serialized_start=858
  _globals['_DISKINFO']._serialized_end=964
  _globals['_OSINFO']._serialized_start=967
  _globals['_OSINFO']._serialized_end=1134
  _globals['_SECURITYINFO']._serialized_start=1136
  _globals['_SECURITYINFO']._serialized_end=1222
  _globals['_SOFTWARE']._serialized_start=1224
  _globals['_SOFTWARE']._serialized_end=1346
  _globals['_NETWORKINFO']._serialized_start=1349
  _globals['_NETWORKINFO']._serialized_end=1490
  _globals['_NETWORKINTERFACE']._serialized_start=1493
  _globals['_NETWORKINTERFACE']._serialized_end=1631
  _globals['_USERINFO']._serialized_start=1633
  _globals['_USERINFO']._serialized_end=1716
  _globals['_REPORTRESPONSE']._serialized_start=1718
  _globals['_REPORTRESPONSE']._serialized_end=1768
  _globals['_COMMANDREQUEST']._serialized_start=1770
  _globals['_COMMANDREQUEST']._serialized_end=1807
  _globals['_COMMAND']._serialized_start=1810
  _globals['_COMMAND']._serialized_end=2059
  _globals['_COMMAND_COMMANDTYPE']._serialized_start=1941
  _globals['_COMMAND_COMMANDTYPE']._serialized_end=2059
  _globals['_COMMANDRESULT']._serialized_start=2062
  _globals['_COMMANDRESULT']._serialized_end=2218
  _globals['_COMMANDRESULTRESPONSE']._serialized_start=2220
  _globals['_COMMANDRESULTRESPONSE']._serialized_end=2278
  _globals['_COMMANDNOTIFICATION']._serialized_start=2280
  _globals['_COMMANDNOTIFICATION']._serialized_end=2342
  _globals['_NOTIFICATIONRESPONSE']._serialized_start=2344
  _globals['_NOTIFICATIONRESPONSE']._serialized_end=2401
  _globals['_REGISTRYOPERATIONREQUEST']._serialized_start=2404
  _globals['_REGISTRYOPERATIONREQUEST']._serialized_end=2725
  _globals['_REGISTRYVALUE']._serialized_start=2727
  _globals['_REGISTRYVALUE']._serialized_end=2827
  _globals['_REGISTRYKEY']._serialized_start=2830
  _globals['_REGISTRYKEY']._serialized_end=3002
  _globals['_REGISTRYOPERATIONRESPONSE']._serialized_start=3005
  _globals['_REGISTRYOPERATIONRESPONSE']._serialized_end=3251
  _globals['_REGISTRYSEARCHREQUEST']._serialized_start=3254
  _globals['_REGISTRYSEARCHREQUEST']._serialized_end=3512
  _globals['_REGISTRYSEARCHRESULT']._serialized_start=3514
  _globals['_REGISTRYSEARCHRESULT']._serialized_end=3630
  _globals['_REGISTRYSEARCHRESPONSE']._serialized_start=3633
  _globals['_REGISTRYSEARCHRESPONSE']._serialized_end=3826
  _globals['_REGISTRYBACKUP']._serialized_start=3829
  _globals['_REGISTRYBACKUP']._serialized_end=4018
  _globals['_TERMINALMANAGEMENT']._serialized_start=4410
  _globals['_TERMINALMANAGEMENT']._serialized_end=5066
# @@protoc_insertion_point(module_scope)
