"""add_updated_groups_to_ad_sync_log

Revision ID: a24c5f70b123
Revises: 22dc5f70b578
Create Date: 2023-03-21 14:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlite3
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = 'a24c5f70b123'
down_revision = '22dc5f70b578'
branch_labels = None
depends_on = None


def upgrade():
    # 检查updated_groups列是否存在
    conn = op.get_bind()
    
    # 检查表中是否存在指定的列
    result = conn.execute(text("PRAGMA table_info(ad_sync_logs)"))
    columns = [row[1] for row in result]
    
    # 如果列不存在才添加
    if 'updated_groups' not in columns:
        op.add_column('ad_sync_logs', sa.Column('updated_groups', sa.Integer(), nullable=True, server_default='0'))
    

def downgrade():
    # 删除updated_groups列
    op.drop_column('ad_sync_logs', 'updated_groups') 