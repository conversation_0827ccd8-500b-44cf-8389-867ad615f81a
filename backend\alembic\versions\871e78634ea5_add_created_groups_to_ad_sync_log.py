"""add_created_groups_to_ad_sync_log

Revision ID: 871e78634ea5
Revises: 1a68b24a3f6b
Create Date: 2025-03-20 17:08:40.056013

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '871e78634ea5'
down_revision: Union[str, None] = '1a68b24a3f6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加created_groups列，默认值为0
    op.add_column('ad_sync_logs', sa.Column('created_groups', sa.Integer(), nullable=True, server_default='0', comment='创建的部门安全组数'))


def downgrade() -> None:
    # 删除created_groups列
    op.drop_column('ad_sync_logs', 'created_groups')
