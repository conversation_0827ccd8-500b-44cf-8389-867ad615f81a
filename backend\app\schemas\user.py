from typing import Optional, List
from pydantic import BaseModel, EmailStr

class UserBase(BaseModel):
    username: str
    email: EmailStr
    is_active: bool = True
    is_superuser: bool = False
    is_builtin: bool = False

class UserCreate(UserBase):
    password: str

class UserUpdate(UserBase):
    password: Optional[str] = None

class UserInDBBase(UserBase):
    id: int

    class Config:
        from_attributes = True

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    hashed_password: str

# 包含完整角色信息的用户
class UserWithRoles(User):
    roles: List["RoleWithPermissions"] = []
    permissions: List["Permission"] = []

    class Config:
        from_attributes = True

class UserSuperuserUpdate(BaseModel):
    is_superuser: bool

# 用户状态更新（启用/禁用）
class UserActiveUpdate(BaseModel):
    is_active: bool

from .role import RoleWithPermissions
from .permission import Permission
UserWithRoles.model_rebuild()