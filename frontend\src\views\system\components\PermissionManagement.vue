<template>
  <div class="permission-management">
    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <span class="section-title">权限列表</span>
        </div>
        <div class="action-buttons">
          <Authority permission="system:permission:add">
            <el-button type="primary" @click="handleAddPermission">
              <el-icon><Plus /></el-icon> 添加权限
            </el-button>
          </Authority>
        </div>
      </div>

      <el-tabs v-model="activeModule" tab-position="left" @tab-change="loadPermissionsByModule">
        <el-tab-pane v-for="module in modules" :key="module" :label="getModuleLabel(module)" :name="module">
          <el-table
            :data="paginatedPermissions"
            border
            style="width: 100%"
            row-class-name="perm-table-row"
            header-row-class-name="perm-table-header"
            header-cell-class-name="table-header-cell"
          >
            <el-table-column prop="code" label="权限代码" width="180">
              <template #header>
                <div class="column-header">权限代码</div>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="权限名称" width="150">
              <template #header>
                <div class="column-header">权限名称</div>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述">
              <template #header>
                <div class="column-header">描述</div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="150">
              <template #header>
                <div class="column-header">操作</div>
              </template>
              <template #default="scope">
                <Authority permission="system:permission:edit">
                  <el-button type="primary" size="small" @click="handleEditPermission(scope.row)">编辑</el-button>
                </Authority>
                <Authority permission="system:permission:delete">
                  <el-button type="danger" size="small" @click="handleDeletePermission(scope.row)">删除</el-button>
                </Authority>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="filteredPermissions.length"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              background
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 权限表单对话框 -->
    <el-dialog v-model="dialogVisible" :title="formType === 'add' ? '添加权限' : '编辑权限'" width="500px">
      <el-form :model="permissionForm" :rules="rules" ref="permissionFormRef" label-width="100px">
        <el-form-item label="权限代码" prop="code">
          <el-input v-model="permissionForm.code" :disabled="formType === 'edit'" />
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" />
        </el-form-item>
        <el-form-item label="所属模块" prop="module">
          <el-select v-model="permissionForm.module" style="width: 100%">
            <el-option v-for="module in modules" :key="module" :label="getModuleLabel(module)" :value="module" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="permissionForm.description" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPermissionForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { systemApi } from '@/api/system'
import { Plus } from '@element-plus/icons-vue'

interface Permission {
  id: number
  code: string
  name: string
  description: string
  module: string
}

const permissionList = ref<Permission[]>([])
const modules = ref<string[]>([])
const activeModule = ref('')
const dialogVisible = ref(false)
const formType = ref<'add' | 'edit'>('add')
const permissionFormRef = ref<FormInstance>()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

const permissionForm = reactive({
  id: 0,
  code: '',
  name: '',
  description: '',
  module: ''
})

const rules = reactive<FormRules>({
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9:]+$/, message: '权限代码只能包含字母、数字和冒号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' }
  ],
  module: [
    { required: true, message: '请选择所属模块', trigger: 'change' }
  ]
})

// 模块名称映射
const moduleLabels: Record<string, string> = {
  'system': '系统管理',
  'asset': '资产管理',
  'inventory': '盘点管理',
  'ad': 'AD管理',
  'ecology': '泛微管理',
  'basic-info': '基础信息',
  'terminal': '终端管理'
}

const getModuleLabel = (moduleCode: string) => {
  return moduleLabels[moduleCode] || moduleCode
}

// 根据当前选中的模块过滤权限列表
const filteredPermissions = computed(() => {
  if (!activeModule.value) return []
  return permissionList.value.filter(p => p.module === activeModule.value)
})

// 根据分页设置计算当前页的数据
const paginatedPermissions = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  return filteredPermissions.value.slice(startIndex, startIndex + pageSize.value)
})

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

// 处理当前页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 加载模块列表
const loadModules = async () => {
  try {
    console.log('[PermissionManagement] 开始加载权限模块')
    const response = await systemApi.getPermissionModules()
    modules.value = response.data
    if (modules.value.length > 0) {
      activeModule.value = modules.value[0]
      loadPermissionsByModule()
    }
  } catch (error) {
    console.error('[PermissionManagement] 加载权限模块失败:', error)
    ElMessage.error('加载权限模块失败')
  }
}

// 加载权限列表
const loadPermissions = async () => {
  try {
    console.log(`[PermissionManagement] 开始加载所有权限列表`)
    const response = await systemApi.getPermissions()
    permissionList.value = response.data
    currentPage.value = 1 // 重置到第一页
  } catch (error) {
    console.error('[PermissionManagement] 加载权限列表失败:', error)
    ElMessage.error('加载权限列表失败')
  }
}

// 根据模块加载权限列表
const loadPermissionsByModule = async () => {
  if (!activeModule.value) return

  try {
    console.log(`[PermissionManagement] 切换到模块 ${activeModule.value}`)
    // 重置分页到第一页
    currentPage.value = 1
  } catch (error) {
    console.error('[PermissionManagement] 加载权限列表失败:', error)
    ElMessage.error('加载权限列表失败')
  }
}

// 添加权限
const handleAddPermission = () => {
  formType.value = 'add'
  permissionForm.id = 0
  permissionForm.code = ''
  permissionForm.name = ''
  permissionForm.description = ''
  permissionForm.module = activeModule.value
  dialogVisible.value = true
}

// 编辑权限
const handleEditPermission = (row: Permission) => {
  formType.value = 'edit'
  permissionForm.id = row.id
  permissionForm.code = row.code
  permissionForm.name = row.name
  permissionForm.description = row.description || ''
  permissionForm.module = row.module
  dialogVisible.value = true
}

// 删除权限
const handleDeletePermission = (row: Permission) => {
  ElMessageBox.confirm(
    `确定要删除权限 "${row.name}" 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await axios.delete(`/api/v1/system/permissions/${row.id}`)
      ElMessage.success('删除成功')
      loadPermissions()
    } catch (error) {
      console.error('删除权限失败:', error)
      ElMessage.error('删除权限失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 提交权限表单
const submitPermissionForm = async () => {
  if (!permissionFormRef.value) return

  await permissionFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formType.value === 'add') {
          await axios.post('/api/v1/system/permissions/', permissionForm)
          ElMessage.success('添加成功')
        } else {
          await axios.put(`/api/v1/system/permissions/${permissionForm.id}`, permissionForm)
          ElMessage.success('修改成功')
        }
        dialogVisible.value = false
        loadPermissions()
      } catch (error) {
        console.error('提交权限表单失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

onMounted(() => {
  loadModules()
  loadPermissions()
})
</script>

<style scoped>
.permission-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-form {
  flex: 1;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
}

.action-buttons {
  margin-left: 16px;
  display: flex;
  gap: 8px;
}

/* 表格样式统一 */
.table-header {
  background-color: var(--el-fill-color-light) !important;
}

.table-header-cell {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>