from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

class LdapConfigBase(BaseModel):
    name: str = Field(..., description="配置名称")
    server: str = Field(..., description="LDAP服务器地址")
    port: int = Field(default=389, description="LDAP服务器端口")
    use_ssl: bool = Field(default=False, description="是否使用SSL")
    base_dn: str = Field(..., description="Base DN")
    bind_dn: Optional[str] = Field(None, description="绑定用户DN")
    bind_password: Optional[str] = Field(None, description="绑定用户密码")
    user_search_base: Optional[str] = Field(None, description="用户搜索Base DN")
    user_search_filter: str = Field(default="(sAMAccountName={username})", description="用户搜索过滤器")
    user_name_attr: str = Field(default="sAMAccountName", description="用户名属性")
    user_email_attr: str = Field(default="mail", description="邮箱属性")
    user_display_name_attr: str = Field(default="displayName", description="显示名称属性")
    auto_create_user: bool = Field(default=True, description="自动创建用户")
    default_role: str = Field(default="user", description="默认角色")
    is_active: bool = Field(default=True, description="是否启用")
    is_default: bool = Field(default=False, description="是否为默认配置")
    description: Optional[str] = Field(None, description="配置描述")
    ip_ranges: Optional[List[str]] = Field(None, description="IP网段配置列表，支持CIDR格式（如***********/24）和范围格式（如***********-*************）")
    priority: int = Field(default=1, description="匹配优先级，数字越小优先级越高")
    auto_select_enabled: bool = Field(default=True, description="是否启用基于IP的自动选择")

    @validator('port')
    def validate_port(cls, v):
        if v < 1 or v > 65535:
            raise ValueError('端口号必须在1-65535之间')
        return v

    @validator('user_search_filter')
    def validate_search_filter(cls, v):
        if '{username}' not in v:
            raise ValueError('用户搜索过滤器必须包含{username}占位符')
        return v

    @validator('priority')
    def validate_priority(cls, v):
        if v < 1:
            raise ValueError('优先级必须大于0')
        return v

    @validator('ip_ranges')
    def validate_ip_ranges(cls, v):
        if v is None:
            return v
        
        import ipaddress
        import re
        
        for ip_range in v:
            # 检查CIDR格式
            if '/' in ip_range:
                try:
                    ipaddress.ip_network(ip_range, strict=False)
                    continue
                except ValueError:
                    pass
            
            # 检查IP范围格式 (IP1-IP2)
            if '-' in ip_range:
                try:
                    start_ip, end_ip = ip_range.split('-', 1)
                    ipaddress.ip_address(start_ip.strip())
                    ipaddress.ip_address(end_ip.strip())
                    continue
                except ValueError:
                    pass
            
            # 检查单个IP地址
            try:
                ipaddress.ip_address(ip_range)
                continue
            except ValueError:
                pass
            
            raise ValueError(f'无效的IP范围格式: {ip_range}，支持格式：***********/24、***********-*************、***********')
        
        return v

class LdapConfigCreate(LdapConfigBase):
    pass

class LdapConfigUpdate(BaseModel):
    name: Optional[str] = None
    server: Optional[str] = None
    port: Optional[int] = None
    use_ssl: Optional[bool] = None
    base_dn: Optional[str] = None
    bind_dn: Optional[str] = None
    bind_password: Optional[str] = None
    user_search_base: Optional[str] = None
    user_search_filter: Optional[str] = None
    user_name_attr: Optional[str] = None
    user_email_attr: Optional[str] = None
    user_display_name_attr: Optional[str] = None
    auto_create_user: Optional[bool] = None
    default_role: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    description: Optional[str] = None
    ip_ranges: Optional[List[str]] = None
    priority: Optional[int] = None
    auto_select_enabled: Optional[bool] = None

class LdapConfigResponse(LdapConfigBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class LdapConnectionTest(BaseModel):
    server: str
    port: int = 389
    use_ssl: bool = False
    base_dn: str
    bind_dn: Optional[str] = None
    bind_password: Optional[str] = None

class LdapAuthRequest(BaseModel):
    username: str = Field(..., description="LDAP用户名")
    password: str = Field(..., description="LDAP密码")
    config_id: Optional[int] = Field(None, description="指定LDAP配置ID，不指定则使用自动选择")

class LdapUserLoginTest(BaseModel):
    """LDAP用户登录测试模型"""
    server: str
    port: int = 389
    use_ssl: bool = False
    base_dn: str
    bind_dn: Optional[str] = None
    bind_password: Optional[str] = None
    user_search_base: Optional[str] = None
    user_search_filter: str = "(sAMAccountName={username})"
    user_name_attr: str = "sAMAccountName"
    user_email_attr: str = "mail"
    user_display_name_attr: str = "displayName"
    username: str = Field(..., description="测试用户名")
    password: str = Field(..., description="测试密码")

class LdapUserSearchDiagnose(BaseModel):
    """LDAP用户搜索诊断模型"""
    server: str
    port: int = 389
    use_ssl: bool = False
    base_dn: str
    bind_dn: Optional[str] = None
    bind_password: Optional[str] = None
    user_search_base: Optional[str] = None
    user_search_filter: str = "(sAMAccountName={username})"
    user_name_attr: str = "sAMAccountName"
    username: str = Field(..., description="要诊断的用户名")

class IPMatchResult(BaseModel):
    """IP匹配结果模型"""
    config_id: int
    config_name: str
    matched_range: Optional[str] = None
    match_reason: str
    priority: int 