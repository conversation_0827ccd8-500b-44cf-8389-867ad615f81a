<template>
  <div class="mobile-user-management">
    <van-search v-model="searchValue" placeholder="搜索用户" />
    
    <van-list>
      <van-cell
        v-for="user in users"
        :key="user.id"
        :title="user.name"
        :label="user.email"
        :value="user.role"
        is-link
        @click="showToast('功能开发中...')"
      />
    </van-list>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'

const searchValue = ref('')
const users = ref([
  { id: 1, name: '管理员', email: '<EMAIL>', role: '超级管理员' },
  { id: 2, name: '张三', email: 'zhang<PERSON>@company.com', role: '普通用户' },
  { id: 3, name: '李四', email: '<EMAIL>', role: '部门管理员' }
])
</script>

<style lang="scss" scoped>
.mobile-user-management {
  background-color: #f7f8fa;
  min-height: 100vh;
}
</style> 