<template>
  <div class="mobile-field-value-selector">
    <!-- 字段输入框 -->
    <van-field
      :model-value="displayValue"
      :name="name"
      :label="label"
      :placeholder="placeholder"
      :rules="rules"
      readonly
      is-link
      @click="showSelector = true"
    >
      <template #right-icon>
        <van-icon name="arrow-down" />
      </template>
    </van-field>

    <!-- 选择器弹窗 -->
    <van-popup 
      v-model:show="showSelector" 
      position="bottom" 
      :style="{ height: 'var(--mobile-popup-medium-height, 58svh)' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="selector-container">
        <!-- 标题栏 -->
        <div class="selector-header">
          <h3>{{ selectorTitle }}</h3>
        </div>

        <!-- 搜索框 -->
        <div class="search-section">
          <van-search
            v-model="searchKeyword"
            placeholder="搜索或输入新值"
            @search="handleSearch"
            @clear="handleSearch"
            show-action
            @cancel="handleSearch"
          />
        </div>

        <!-- 选项列表 -->
        <div class="options-section">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model:loading="loading"
              :finished="finished"
              finished-text="没有更多数据了"
              @load="onLoad"
            >
              <!-- 新增选项（当搜索词不在现有选项中时显示） -->
              <van-cell
                v-if="showAddOption"
                :title="`添加 &quot;${searchKeyword}&quot;`"
                left-icon="plus"
                is-link
                @click="handleAddNew"
                class="add-option-cell"
              />

              <!-- 现有选项列表 -->
              <van-cell
                v-for="option in filteredOptions"
                :key="option.id"
                :title="option.field_value"
                :label="option.description || ''"
                is-link
                @click="handleSelect(option)"
                :class="{ 'selected-option': selectedValue === option.field_value }"
              >
                <template #right-icon>
                  <van-icon 
                    v-if="selectedValue === option.field_value" 
                    name="success" 
                    color="#07c160" 
                  />
                </template>
              </van-cell>

              <!-- 空状态 -->
              <div v-if="filteredOptions.length === 0 && !loading && !showAddOption" class="empty-state">
                <van-empty description="暂无数据" />
              </div>
            </van-list>
          </van-pull-refresh>
        </div>

        <!-- 底部操作栏 -->
        <MobilePopupFooter :buttons="selectorFooterButtons" />
      </div>
    </van-popup>

    <!-- 新增字段值弹窗 -->
    <van-popup 
      v-model:show="showAddDialog" 
      position="bottom" 
      :style="{ height: 'var(--mobile-popup-small-height, 48svh)' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="add-dialog-container">
        <div class="add-dialog-header">
          <h3>新增字段值</h3>
        </div>
        
        <van-form @submit="handleSubmitNew" ref="addFormRef">
          <van-cell-group inset>
            <van-field
              v-model="newFieldValue.field_value"
              name="field_value"
              label="字段值"
              placeholder="请输入字段值"
              :rules="[{ required: true, message: '请输入字段值' }]"
            />
            
            <van-field
              v-model="newFieldValue.description"
              name="description"
              label="描述"
              type="textarea"
              placeholder="请输入描述（可选）"
              :rows="3"
              autosize
              show-word-limit
              :maxlength="200"
            />
          </van-cell-group>
        </van-form>

        <MobilePopupFooter :buttons="addDialogFooterButtons" />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { showToast, type FormInstance } from 'vant'
import { fieldValueApi } from '@/api/field_value'
import MobilePopupFooter from './MobilePopupFooter.vue'
import type { FieldValue, FieldName } from '@/types/field_value'
import { FIELD_NAME_LABELS } from '@/types/field_value'

interface Props {
  modelValue?: string
  fieldName: FieldName
  name?: string
  label?: string
  placeholder?: string
  rules?: any[]
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  name: '',
  label: '',
  placeholder: '请选择',
  rules: () => [],
  disabled: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string, option?: FieldValue]
}>()

// 响应式数据
const showSelector = ref(false)
const showAddDialog = ref(false)
const searchKeyword = ref('')
const selectedValue = ref(props.modelValue)
const options = ref<FieldValue[]>([])
const filteredOptions = ref<FieldValue[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const addLoading = ref(false)
const page = ref(1)
const pageSize = 20

// 表单引用
const addFormRef = ref<FormInstance>()

// 新增字段值表单
const newFieldValue = reactive({
  field_value: '',
  description: ''
})

// 计算属性
const displayValue = computed(() => props.modelValue)

const selectorTitle = computed(() => {
  return `选择${FIELD_NAME_LABELS[props.fieldName] || '字段值'}`
})

const showAddOption = computed(() => {
  if (!searchKeyword.value.trim()) return false
  return !filteredOptions.value.some(option => 
    option.field_value.toLowerCase() === searchKeyword.value.toLowerCase()
  )
})

// 获取字段值数据
const fetchFieldValues = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
  }

  try {
    loading.value = true
    const response = await fieldValueApi.getFieldValues({
      field_name: props.fieldName,
      keyword: searchKeyword.value,
      skip: (page.value - 1) * pageSize,
      limit: pageSize
    })

    const newData = response.data.data || []
    
    if (isRefresh) {
      options.value = newData
    } else {
      options.value.push(...newData)
    }

    // 判断是否还有更多数据
    if (newData.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }

    // 更新过滤选项
    updateFilteredOptions()
  } catch (error) {
    console.error('获取字段值列表失败:', error)
    showToast('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 更新过滤选项
const updateFilteredOptions = () => {
  if (!searchKeyword.value.trim()) {
    filteredOptions.value = options.value
  } else {
    const keyword = searchKeyword.value.toLowerCase()
    filteredOptions.value = options.value.filter(option =>
      option.field_value.toLowerCase().includes(keyword) ||
      (option.description && option.description.toLowerCase().includes(keyword))
    )
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchFieldValues(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (refreshing.value) return
  await fetchFieldValues()
}

// 搜索处理
const handleSearch = () => {
  updateFilteredOptions()
}

// 选择选项
const handleSelect = (option: FieldValue) => {
  selectedValue.value = option.field_value
  // 立即触发数据更新，而不是等待确认
  emit('update:modelValue', selectedValue.value)
  emit('change', selectedValue.value, option)
  showSelector.value = false
}

// 确认选择（现在已经不需要确认按钮，选择即确认）
const handleConfirm = () => {
  showSelector.value = false
}

// 添加新选项
const handleAddNew = () => {
  newFieldValue.field_value = searchKeyword.value
  newFieldValue.description = ''
  showAddDialog.value = true
}

// 提交新字段值
const handleSubmitNew = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()
    
    addLoading.value = true
    const response = await fieldValueApi.createFieldValue({
      field_name: props.fieldName,
      field_value: newFieldValue.field_value,
      description: newFieldValue.description
    })

    // 添加到选项列表
    options.value.unshift(response.data)
    updateFilteredOptions()

    // 自动选择新添加的值并确认
    selectedValue.value = newFieldValue.field_value
    emit('update:modelValue', newFieldValue.field_value)
    emit('change', newFieldValue.field_value, response.data)
    
    showToast('添加成功')
    showAddDialog.value = false
    showSelector.value = false
    
    // 重置表单
    newFieldValue.field_value = ''
    newFieldValue.description = ''
  } catch (error) {
    console.error('添加字段值失败:', error)
    showToast('添加失败')
  } finally {
    addLoading.value = false
  }
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  updateFilteredOptions()
})

// 底部按钮配置
const selectorFooterButtons = computed(() => [
  {
    text: '取消',
    type: 'default' as const,
    onClick: () => {
      showSelector.value = false
    }
  }
])

// 新增对话框底部按钮配置
const addDialogFooterButtons = computed(() => [
  {
    text: '取消',
    type: 'default' as const,
    onClick: () => {
      showAddDialog.value = false
    }
  },
  {
    text: '保存',
    type: 'primary' as const,
    loading: addLoading.value,
    onClick: handleSubmitNew
  }
])

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
})

// 组件挂载时获取数据
onMounted(() => {
  fetchFieldValues(true)
})
</script>

<style scoped>
.mobile-field-value-selector {
  width: 100%;
}

.selector-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  position: relative;
}

.selector-header {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #ebedf0;
  text-align: center;
}

.selector-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.search-section {
  background: white;
  padding-bottom: 8px;
}

.options-section {
  flex: 1;
  overflow: auto;
  background: white;
  margin-top: 8px;
  /* 确保有足够的空间显示选项，为标题、搜索、底部按钮预留空间 */
  min-height: 0;
  padding-bottom: 16px;
}

.add-option-cell {
  background: #f0f9ff;
  border-left: 3px solid #1989fa;
}

.add-option-cell :deep(.van-cell__title) {
  color: #1989fa;
  font-weight: 500;
}

.selected-option {
  background: #f0f9ff;
}

.selected-option :deep(.van-cell__title) {
  color: #1989fa;
  font-weight: 500;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.add-dialog-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.add-dialog-header {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #ebedf0;
  text-align: center;
}

.add-dialog-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-cell-group) {
  margin: 16px;
}

:deep(.van-field__label) {
  width: 80px;
  flex: none;
}
</style> 