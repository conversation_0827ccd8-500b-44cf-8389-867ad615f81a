<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    @close="handleCancel"
    width="850px"
    class="asset-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      label-width="100px"
      class="asset-form"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="公司" prop="company">
            <div class="select-with-icon">
              <el-select
                v-model="formState.company"
                placeholder="请选择公司"
                filterable
                :disabled="props.mode === 'edit'"
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.company"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产名称" prop="name">
            <div class="select-with-icon">
              <el-select
                v-model="formState.name"
                placeholder="请输入资产名称"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.name"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                />
              </el-select>
              <el-button
                v-if="formState.name && !fieldValueOptions.name.some(option => option.field_value === formState.name)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('name', formState.name)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资产编号" prop="asset_number">
            <div class="select-with-icon">
              <el-input
                v-model="formState.asset_number"
                :placeholder="props.mode === 'create' ? '选择公司后自动生成，或手动输入' : '资产编号不可修改'"
                :disabled="props.mode === 'edit' || useAutoNumber"
                :maxlength="50"
                show-word-limit
              />
              <el-button
                v-if="props.mode === 'create'"
                type="primary"
                link
                class="auto-number-toggle"
                @click="useAutoNumber = !useAutoNumber"
              >
                {{ useAutoNumber ? '手动输入' : '自动生成' }}
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资产状态" prop="status">
            <div class="select-with-icon">
              <el-select
                v-model="formState.status"
                placeholder="请选择资产状态"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.status"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="option.description">
                      {{ option.description }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.status && !fieldValueOptions.status.some(option => option.field_value === formState.status)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('status', formState.status)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资产类别" prop="category">
            <div class="select-with-icon">
              <el-select
                v-model="formState.category"
                placeholder="请选择资产类别"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.category"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="getPersonnelDetail(option.field_value)">
                      {{ getPersonnelDetail(option.field_value) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.category && !fieldValueOptions.category.some(option => option.field_value === formState.category)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('category', formState.category)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产编号" prop="production_number">
            <el-input
              v-model="formState.production_number"
              placeholder="请输入生产编号"
              :maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="价格" prop="price">
            <el-input-number
              v-model="formState.price"
              placeholder="请输入价格"
              :precision="2"
              :step="0.01"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplier">
            <div class="select-with-icon">
              <el-select
                v-model="formState.supplier"
                placeholder="请选择供应商"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.supplier"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="getPersonnelDetail(option.field_value)">
                      {{ getPersonnelDetail(option.field_value) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.supplier && !fieldValueOptions.supplier.some(option => option.field_value === formState.supplier)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('supplier', formState.supplier)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="制造商" prop="manufacturer">
            <div class="select-with-icon">
              <el-select
                v-model="formState.manufacturer"
                placeholder="请选择制造商"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.manufacturer"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="getPersonnelDetail(option.field_value)">
                      {{ getPersonnelDetail(option.field_value) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.manufacturer && !fieldValueOptions.manufacturer.some(option => option.field_value === formState.manufacturer)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('manufacturer', formState.manufacturer)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="资产规格" prop="specification">
            <div class="select-with-icon">
              <el-select
                v-model="formState.specification"
                placeholder="请输入资产规格"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.specification"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="getPersonnelDetail(option.field_value)">
                      {{ getPersonnelDetail(option.field_value) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.specification && !fieldValueOptions.specification.some(option => option.field_value === formState.specification)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('specification', formState.specification)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="入账日期" prop="purchase_date">
            <el-date-picker
              v-model="formState.purchase_date"
              type="date"
              placeholder="请选择入账日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="销账日期" prop="retirement_date">
            <el-date-picker
              v-model="formState.retirement_date"
              type="date"
              placeholder="请选择销账日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存放位置" prop="location">
            <div class="select-with-icon">
              <el-select
                v-model="formState.location"
                placeholder="请选择存放位置"
                filterable
                allow-create
                default-first-option
                style="width: 100%"
              >
                <el-option
                  v-for="option in fieldValueOptions.location"
                  :key="option.id"
                  :label="option.field_value"
                  :value="option.field_value"
                >
                  <div class="personnel-option">
                    <div>{{ option.field_value }}</div>
                    <div class="personnel-detail" v-if="getPersonnelDetail(option.field_value)">
                      {{ getPersonnelDetail(option.field_value) }}
                    </div>
                  </div>
                </el-option>
              </el-select>
              <el-button
                v-if="formState.location && !fieldValueOptions.location.some(option => option.field_value === formState.location)"
                type="primary"
                link
                class="add-icon"
                @click="handleQuickAdd('location', formState.location)"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 人员信息区域 -->
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="领用人" prop="custodian">
            <el-select
              ref="custodianSelectRef"
              v-model="formState.custodian"
              placeholder="请选择领用人"
              filterable
              remote
              :remote-method="createRemoteSearch(custodianSelectRef)"
              :loading="personnelLoading"
              reserve-keyword
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in fieldValueOptions.personnel"
                :key="option.id"
                :label="option.field_value"
                :value="option.display_value || option.field_value"
              >
                <PersonnelOptionTemplate
                  :option="option"
                  :detail="getPersonnelDetail(option.field_value)"
                />
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="领用人工号" prop="custodian_job_number">
            <el-input
              v-model="formState.custodian_job_number"
              placeholder="自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="领用人部门" prop="custodian_department">
            <el-input
              v-model="formState.custodian_department"
              placeholder="选择领用人后自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用人" prop="user">
            <el-select
              ref="userSelectRef"
              v-model="formState.user"
              placeholder="请选择使用人"
              filterable
              remote
              :remote-method="createRemoteSearch(userSelectRef)"
              :loading="personnelLoading"
              reserve-keyword
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in fieldValueOptions.personnel"
                :key="option.id"
                :label="option.field_value"
                :value="option.display_value || option.field_value"
              >
                <PersonnelOptionTemplate
                  :option="option"
                  :detail="getPersonnelDetail(option.field_value)"
                />
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="使用人工号" prop="user_job_number">
            <el-input
              v-model="formState.user_job_number"
              placeholder="自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用人部门" prop="user_department">
            <el-input
              v-model="formState.user_department"
              placeholder="选择使用人后自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="采购人" prop="purchaser">
            <el-select
              ref="purchaserSelectRef"
              v-model="formState.purchaser"
              placeholder="请选择采购人"
              filterable
              remote
              :remote-method="createRemoteSearch(purchaserSelectRef)"
              :loading="personnelLoading"
              reserve-keyword
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in fieldValueOptions.personnel"
                :key="option.id"
                :label="option.field_value"
                :value="option.display_value || option.field_value"
              >
                <PersonnelOptionTemplate
                  :option="option"
                  :detail="getPersonnelDetail(option.field_value)"
                />
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购人工号" prop="purchaser_job_number">
            <el-input
              v-model="formState.purchaser_job_number"
              placeholder="自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="验收人" prop="inspector">
            <el-select
              ref="inspectorSelectRef"
              v-model="formState.inspector"
              placeholder="请选择验收人"
              filterable
              remote
              :remote-method="createRemoteSearch(inspectorSelectRef)"
              :loading="personnelLoading"
              reserve-keyword
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in fieldValueOptions.personnel"
                :key="option.id"
                :label="option.field_value"
                :value="option.display_value || option.field_value"
              >
                <PersonnelOptionTemplate
                  :option="option"
                  :detail="getPersonnelDetail(option.field_value)"
                />
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="验收人工号" prop="inspector_job_number">
            <el-input
              v-model="formState.inspector_job_number"
              placeholder="自动填充"
              disabled
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remarks">
        <el-input
          v-model="formState.remarks"
          type="textarea"
          :rows="4"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <!-- 自定义字段区域 -->
      <el-divider content-position="left">
        <span style="color: #909399; font-size: 14px;">自定义字段</span>
      </el-divider>
      
      <DynamicForm
        ref="customFieldFormRef"
        applies-to="asset"
        :initial-data="customFieldData"
        :entity-id="props.mode === 'edit' ? props.initialValues?.id : null"
        :mode="props.mode"
        :label-width="'100px'"
        :show-actions="false"
        @change="handleCustomFieldChange"
        @ready="handleCustomFieldsReady"
      />
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { assetApi } from '@/api/asset'
import { fieldValueApi } from '@/api/field_value'
import { ecologyApi } from '@/api/ecology'
import type { EcologyUser } from '@/types/ecology'

import dayjs from 'dayjs'
import type { Asset, AssetCreate, AssetUpdate } from '@/types/asset'
import type { FieldValue } from '@/types/field_value'
import { assetSettingsApi } from '@/api/asset_settings'
import PersonnelOptionTemplate from './PersonnelOptionTemplate.vue'
import DynamicForm from '@/components/DynamicForm/index.vue'
import { customFieldApi } from '@/api/custom_field'
import type { CustomField, AssetCustomFieldValue } from '@/types/custom_field'
import { prepareCustomFieldValues, convertApiFieldValuesToFormData } from '@/utils/customField'

interface Props {
  visible: boolean
  mode: 'create' | 'edit'
  initialValues?: Asset | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'create',
  initialValues: null
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref<FormInstance>()
const customFieldFormRef = ref()

interface FormState {
  company: string
  name: string
  asset_number: string
  status: string
  category: string
  specification: string
  purchase_date: string
  retirement_date: string
  custodian: string
  custodian_job_number: string
  custodian_department: string
  user: string
  user_job_number: string
  user_department: string
  location: string
  inspector: string
  inspector_job_number: string
  remarks: string
  production_number: string
  price: number | undefined
  supplier: string
  manufacturer: string
  purchaser: string
  purchaser_job_number: string
}

const formState = reactive<FormState>({
  company: '',
  name: '',
  asset_number: '',
  status: '',
  category: '',
  specification: '',
  purchase_date: '',
  retirement_date: '',
  custodian: '',
  custodian_job_number: '',
  custodian_department: '',
  user: '',
  user_job_number: '',
  user_department: '',
  location: '',
  inspector: '',
  inspector_job_number: '',
  remarks: '',
  production_number: '',
  price: undefined,
  supplier: '',
  manufacturer: '',
  purchaser: '',
  purchaser_job_number: ''
})

interface CompanyOption {
  id: number
  field_value: string
}

interface EnhancedFieldValue extends FieldValue {
  job_number?: string
  dept_name?: string
  display_value?: string
}

interface FieldValueOptions {
  company: CompanyOption[]
  name: FieldValue[]
  specification: FieldValue[]
  personnel: EnhancedFieldValue[]
  department: FieldValue[]
  location: FieldValue[]
  supplier: FieldValue[]
  manufacturer: FieldValue[]
  category: FieldValue[]
  status: FieldValue[]
}

const personnelData = ref<EcologyUser[]>([])
const personnelLoading = ref(false)

const custodianSelectRef = ref()
const userSelectRef = ref()
const inspectorSelectRef = ref()
const purchaserSelectRef = ref()

const fieldValueOptions = reactive<FieldValueOptions>({
  company: [],
  name: [],
  specification: [],
  personnel: [],
  department: [],
  location: [],
  supplier: [],
  manufacturer: [],
  category: [],
  status: []
})

const rules = {
  company: [{ required: true, message: '请选择公司', trigger: 'change' }],
  name: [{ required: true, message: '请输入资产名称', trigger: 'change' }],
  status: [{ required: true, message: '请选择资产状态', trigger: 'change' }],
  category: [{ required: true, message: '请选择资产类别', trigger: 'change' }],
  specification: [{ required: true, message: '请输入资产规格', trigger: 'change' }],
  purchase_date: [{ required: true, message: '请选择入账日期', trigger: 'change' }],
  custodian: [{ required: false, message: '请选择领用人', trigger: 'change' }],
  user: [{ required: false, message: '请选择使用人', trigger: 'change' }],
  inspector: [{ required: false, message: '请选择验收人', trigger: 'change' }]
}

const useAutoNumber = ref(true)

// 自定义字段相关
const customFieldData = ref<Record<string, any>>({})
const customFields = ref<CustomField[]>([])
const assetCustomFieldValues = ref<AssetCustomFieldValue[]>([])

// 初始化表单数据
const initFormState = () => {
  formState.company = ''
  formState.name = ''
  formState.asset_number = ''
  formState.status = ''
  formState.category = ''
  formState.specification = ''
  formState.purchase_date = ''
  formState.retirement_date = ''
  formState.custodian = ''
  formState.custodian_job_number = ''
  formState.custodian_department = ''
  formState.user = ''
  formState.user_job_number = ''
  formState.user_department = ''
  formState.location = ''
  formState.inspector = ''
  formState.inspector_job_number = ''
  formState.remarks = ''
  formState.production_number = ''
  formState.price = undefined
  formState.supplier = ''
  formState.manufacturer = ''
  formState.purchaser = ''
  formState.purchaser_job_number = ''
}

// 加载字段选项
const loadFieldValueOptions = async () => {
  try {
    const fields = [
      'name',
      'specification',
      'department',
      'location',
      'supplier',
      'manufacturer',
      'category',
      'status'
    ] as const

    const promises = fields.map(field => fieldValueApi.getFieldValues({ field_name: field }))

    // 不在初始化时加载所有人员数据，改为按需加载
    // 检查是否已有缓存数据
    if (!personnelData.value.length) {
      // 只在没有缓存数据时才加载少量初始数据
      personnelLoading.value = true
      try {
        const personnelResult = await ecologyApi.getLocalEcologyUsers({
          skip: 0,
          limit: 20 // 减少初始加载数量
        })

        // 处理人员数据
        personnelData.value = personnelResult.data

        // 将人员数据转换为字段值格式 - 改进版本，包含工号和部门信息
        fieldValueOptions.personnel = personnelData.value.map(person => {
          const userName = person.user_name || person.UserName || '';
          const jobNumber = person.job_number || person.JobNumber || '';
          const deptName = person.dept_name || person.DeptName || '';

          return {
            id: person.id || person.UserID || 0,
            field_name: 'personnel',
            field_value: userName,
            job_number: jobNumber,
            dept_name: deptName,
            display_value: jobNumber ? `${userName} (${jobNumber})` : userName,
            created_at: '',
            updated_at: ''
          };
        }).filter(p => p.field_value) // 过滤掉没有名字的记录
      } catch (error) {
        console.error('加载人员数据失败:', error)
      } finally {
        personnelLoading.value = false
      }
    } else {
      // 使用缓存数据 - 改进版本，包含工号和部门信息
      fieldValueOptions.personnel = personnelData.value.map(person => {
        const userName = person.user_name || person.UserName || '';
        const jobNumber = person.job_number || person.JobNumber || '';
        const deptName = person.dept_name || person.DeptName || '';

        return {
          id: person.id || person.UserID || 0,
          field_name: 'personnel',
          field_value: userName,
          job_number: jobNumber,
          dept_name: deptName,
          display_value: jobNumber ? `${userName} (${jobNumber})` : userName,
          created_at: '',
          updated_at: ''
        };
      }).filter(p => p.field_value)
    }

    // 处理常规字段值
    const results = await Promise.all(promises)
    fields.forEach((field, index) => {
      fieldValueOptions[field] = results[index].data.data
    })

  } catch (error) {
    console.error('Failed to load field value options:', error)
    ElMessage.error('加载字段选项失败')
  }
}

// 远程搜索人员
const remoteSearchPersonnel = async (query: string, selectRef: any) => {
  if (query === '') {
    // 如果搜索词为空，使用缓存数据或加载少量默认数据
    if (fieldValueOptions.personnel.length > 0) {
      // 已有数据，不需要重新加载
      return
    }

    try {
      personnelLoading.value = true
      const response = await ecologyApi.getLocalEcologyUsers({
        skip: 0,
        limit: 20 // 减少加载数量
      })

      personnelData.value = response.data

      fieldValueOptions.personnel = personnelData.value.map(person => {
        const userName = person.user_name || person.UserName || '';
        const jobNumber = person.job_number || person.JobNumber || '';
        const deptName = person.dept_name || person.DeptName || '';
        // 为每个人员添加唯一标识，用于处理同名情况
        const uniqueId = person.id || person.UserID || 0;

        return {
          id: uniqueId,
          field_name: 'personnel',
          field_value: userName,
          job_number: jobNumber,
          dept_name: deptName,
          user_id: uniqueId, // 添加用户ID
          // 使用姓名加ID作为内部值，确保唯一性
          display_value: `${userName}#${uniqueId}`,
          created_at: '',
          updated_at: ''
        };
      }).filter(p => p.field_value)
      personnelLoading.value = false
    } catch (error) {
      console.error('加载默认人员数据失败:', error)
      personnelLoading.value = false
    }
    return
  }

  personnelLoading.value = true
  try {
    // 检查是否是工号精确匹配（纯数字）
    const isJobNumber = /^\d+$/.test(query)

    // 根据输入的关键词搜索人员（姓名或工号）
    const response = await ecologyApi.getLocalEcologyUsers({
      skip: 0,
      limit: 20,
      keyword: query,
      exact_match: isJobNumber // 如果是纯数字，则使用精确匹配
    })

    // 更新全局人员数据，确保监听函数能够获取到部门信息和工号
    // 合并新数据和现有数据，避免覆盖已有数据
    const newData = response.data
    const existingIds = new Set(personnelData.value.map(p => p.id || p.UserID))

    // 只添加不存在的数据
    newData.forEach(person => {
      const personId = person.id || person.UserID
      if (personId && !existingIds.has(personId)) {
        personnelData.value.push(person)
        existingIds.add(personId)
      }
    })

    // 更新人员数据 - 改进版本，包含工号和部门信息
    const newPersonnel = response.data.map(person => {
      const userName = person.user_name || person.UserName || '';
      const jobNumber = person.job_number || person.JobNumber || '';
      const deptName = person.dept_name || person.DeptName || '';
      const uniqueId = person.id || person.UserID || 0;

      return {
        id: uniqueId,
        field_name: 'personnel',
        field_value: userName,
        job_number: jobNumber,
        dept_name: deptName,
        user_id: uniqueId, // 添加用户ID
        // 使用姓名加ID作为内部值，确保唯一性
        display_value: `${userName}#${uniqueId}`,
        created_at: '',
        updated_at: ''
      };
    }).filter(p => p.field_value);

    // 更新下拉选项
    fieldValueOptions.personnel = newPersonnel

    // 如果是编辑模式，确保当前选中的值也在列表中
    if (selectRef && selectRef.value && selectRef.value.modelValue) {
      const currentValue = selectRef.value.modelValue
      if (!newPersonnel.some(p => p.field_value === currentValue || p.display_value === currentValue)) {
        fieldValueOptions.personnel.push({
          id: -1,
          field_name: 'personnel',
          field_value: currentValue,
          created_at: '',
          updated_at: ''
        })
      }
    }
    personnelLoading.value = false
  } catch (error) {
    console.error('搜索人员失败:', error)
    personnelLoading.value = false
  }
}

// 创建类型安全的包装函数
const createRemoteSearch = (selectRef: any) => {
  return (query: string) => remoteSearchPersonnel(query, selectRef)
}

// 快速添加字段值
const handleQuickAdd = async (field: keyof FieldValueOptions, value: string) => {
  try {
    const response = await fieldValueApi.createFieldValue({
      field_name: field,
      field_value: value
    })
    fieldValueOptions[field].push(response.data)
    ElMessage.success('添加成功')
  } catch (error) {
    console.error('Failed to add field value:', error)
    ElMessage.error('添加失败')
  }
}

// 监听初始值变化
watch(
  () => props.initialValues,
  (newVal) => {
    if (newVal) {
      const values = { ...newVal }
      formState.company = values.company
      formState.name = values.name
              formState.asset_number = values.asset_number
        formState.status = values.status
        formState.category = values.category || ''
        formState.specification = values.specification
      formState.purchase_date = values.purchase_date
        ? dayjs(values.purchase_date).format('YYYY-MM-DD')
        : ''
      formState.retirement_date = values.retirement_date
        ? dayjs(values.retirement_date).format('YYYY-MM-DD')
        : ''
      formState.custodian = values.custodian
      formState.custodian_job_number = values.custodian_job_number || ''
      formState.custodian_department = values.custodian_department || ''
      formState.user = values.user
      formState.user_job_number = values.user_job_number || ''
      formState.user_department = values.user_department || ''
      formState.location = values.location || ''
      formState.inspector = values.inspector
      formState.inspector_job_number = values.inspector_job_number || ''
      formState.remarks = values.remarks || ''
      formState.production_number = values.production_number || ''
      formState.price = values.price
      formState.supplier = values.supplier || ''
      formState.manufacturer = values.manufacturer || ''
      formState.purchaser = values.purchaser || ''
      formState.purchaser_job_number = values.purchaser_job_number || ''
      
      // 加载自定义字段值
      if (values.id) {
        loadAssetCustomFieldValues(values.id)
      }
    } else {
      initFormState()
      // 清空自定义字段数据
      customFieldData.value = {}
    }
  },
  { immediate: true }
)

// 获取资产设置列表
const fetchAssetSettings = async () => {
  try {
    const response = await assetSettingsApi.getAssetSettings({})
    fieldValueOptions.company = response.data.map((setting, index) => ({
      id: index,
      field_value: setting.company
    }))
  } catch (error) {
    ElMessage.error('获取公司列表失败')
  }
}

// 在组件挂载时获取资产设置和其他字段选项
onMounted(() => {
  fetchAssetSettings()
  loadFieldValueOptions()
})

const title = computed(() => props.mode === 'create' ? '新增资产' : '编辑资产')

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    const baseValues = {
      ...formState,
      purchase_date: formState.purchase_date
        ? dayjs(formState.purchase_date).format('YYYY-MM-DD')
        : undefined,
      retirement_date: formState.retirement_date
        ? dayjs(formState.retirement_date).format('YYYY-MM-DD')
        : undefined
    }

    if (props.mode === 'create') {
      const response = await assetApi.createAsset(baseValues as AssetCreate)
      const newAsset = response.data || response
      // 保存自定义字段值
      if (newAsset?.id) {
        try {
          await saveCustomFieldValues(newAsset.id)
        } catch (error) {
          // 如果自定义字段保存失败，提示用户但不回滚主要操作
          ElMessage.warning('资产创建成功，但自定义字段保存失败，请稍后重试编辑')
        }
      }
      ElMessage.success('创建成功')
    } else if (props.initialValues?.id) {
      // 编辑模式下，只提交实际修改过的字段
      const changedValues: Record<string, any> = {}
      const initialValues = props.initialValues

      // 比较并只收集发生变化的字段
      Object.keys(baseValues).forEach(key => {
        const initialValue = initialValues[key as keyof typeof initialValues]
        const currentValue = baseValues[key as keyof typeof baseValues]

        // 使用类型转换确保比较的是相同类型的值
        // 日期字段进行特殊处理（比较格式化后的字符串）
        let valuesAreDifferent = false

        if (key === 'purchase_date' || key === 'retirement_date') {
          const initialDate = initialValue ? dayjs(initialValue).format('YYYY-MM-DD') : undefined
          valuesAreDifferent = initialDate !== currentValue
        } else {
          valuesAreDifferent = String(initialValue) !== String(currentValue)
        }

        if (valuesAreDifferent) {
          changedValues[key] = currentValue
        }
      })

      // 只有在有字段变更时才发送请求
      if (Object.keys(changedValues).length > 0) {
        await assetApi.updateAsset(props.initialValues.id, changedValues as AssetUpdate)
        ElMessage.success('更新成功')
      } else {
        ElMessage.info('未检测到字段变更')
      }
      
      // 保存自定义字段值（编辑模式下总是保存）
      try {
        await saveCustomFieldValues(props.initialValues.id)
      } catch (error) {
        // 如果自定义字段保存失败，提示用户
        ElMessage.warning('基础信息更新成功，但自定义字段保存失败，请稍后重试')
      }
    }

    emit('success')
    emit('update:visible', false)
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error('表单提交失败')
  }
}

const handleCancel = () => {
  emit('update:visible', false)
}

const dateShortcuts = [
  {
    text: '今天',
    value: new Date()
  },
  {
    text: '昨天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    }
  },
  {
    text: '一周前',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    }
  }
]

// 获取公司当前最大资产编号
const getNextAssetNumber = async (company: string, rule: { prefix: string; number_length: number; start_number: number }) => {
  try {
    // 获取该公司的所有资产
    const { data: { data: assets } } = await assetApi.getAssets({
      company,
      limit: 1,
      sort_by: 'asset_number',
      sort_order: 'desc'
    })

    const { prefix, number_length, start_number } = rule
    let nextNumber = start_number

    // 如果有现有资产，则在最大编号基础上加1
    if (assets.length > 0) {
      const lastAsset = assets[0]
      const currentNumber = parseInt(lastAsset.asset_number.replace(prefix, ''))
      nextNumber = currentNumber + 1
    }

    return `${prefix}${String(nextNumber).padStart(number_length, '0')}`
  } catch (error) {
    console.error('获取最大资产编号失败:', error)
    return null
  }
}

// 监听公司变化，生成资产编号
watch(
  () => formState.company,
  async (newCompany) => {
    if (newCompany && props.mode === 'create' && useAutoNumber.value) {
      try {
        const response = await assetSettingsApi.getAssetSettingsByCompany(newCompany)
        const rule = response.data.asset_number_rule
        if (rule) {
          const newAssetNumber = await getNextAssetNumber(newCompany, rule)
          if (newAssetNumber) {
            formState.asset_number = newAssetNumber
          }
        }
      } catch (error) {
        console.error('获取资产编号规则失败:', error)
        ElMessage.error('获取资产编号规则失败')
      }
    }
  }
)

// 获取人员详细信息
const getPersonnelDetail = (name: string) => {
  // 如果名字中包含工号信息 (工号)，提取真实名称和工号
  let realName = name
  let jobNumber = ''
  
  if (name.includes('(') && name.includes(')')) {
    realName = name.substring(0, name.indexOf('(')).trim()
    jobNumber = name.substring(name.indexOf('(') + 1, name.indexOf(')')).trim()
  }
  
  // 先根据工号查找（如果有工号）
  let person
  if (jobNumber) {
    person = findPersonnelByJobNumber(jobNumber)
  }
  
  // 如果没找到，再根据名称查找
  if (!person) {
    person = findPersonnelByName(realName)
    // 如果找到了多个同名人员，这里简单处理，取第一个
    if (Array.isArray(person)) {
      person = person[0]
    }
  }
  
  if (person) {
    const deptName = person.dept_name || person.DeptName || ''
    const jobTitle = person.job_title_name || person.JobTitleName || ''
    return [deptName, jobTitle].filter(Boolean).join(' - ')
  }
  return ''
}

// 根据工号查找人员
const findPersonnelByJobNumber = (jobNumber: string) => {
  return personnelData.value.find(p =>
    (p.job_number === jobNumber || p.JobNumber === jobNumber)
  )
}

// 根据姓名查找人员，处理同名情况
const findPersonnelByName = (name: string) => {
  // 先尝试精确匹配姓名
  const exactMatches = personnelData.value.filter(p => 
    (p.user_name === name || p.UserName === name)
  )
  
  // 如果有多个同名的人，则返回所有匹配项
  if (exactMatches.length > 1) {
    return exactMatches
  }
  
  // 如果只有一个匹配项或没有匹配项，返回第一个匹配项或undefined
  return exactMatches[0]
}

// 根据用户ID查找人员，用于处理同名情况
const findPersonnelByUserId = (userId: number) => {
  return personnelData.value.find(p => 
    (p.id === userId || p.UserID === userId)
  )
}

// 监听领用人变化，自动填充领用人部门和工号
watch(
  () => formState.custodian,
  (newCustodian) => {
    if (newCustodian) {
      // 处理内部值可能的两种情况: 纯姓名 或 姓名#ID格式
      let realName = newCustodian
      let userId: number | null = null
      
      // 检查是否包含隐藏的用户ID (姓名#ID格式)
      if (newCustodian.includes('#')) {
        const parts = newCustodian.split('#')
        realName = parts[0]
        userId = parseInt(parts[1])
        // 更新显示值为纯姓名
        formState.custodian = realName
      }
      
      // 优先根据用户ID查找
      let person
      if (userId !== null) {
        person = findPersonnelByUserId(userId)
      } else {
        // 按名称查找
        person = findPersonnelByName(realName)
        // 对于同名情况，不再自动选择第一个
        if (Array.isArray(person)) {
          // 如果有多个同名人员但没有选择具体哪一个，不自动填充
          return
        }
      }

      if (person) {
        // 自动填充部门信息和工号
        formState.custodian_department = person.dept_name || person.DeptName || ''
        formState.custodian_job_number = person.job_number || person.JobNumber || ''
      }
    } else {
      // 如果清空了领用人，也清空部门和工号
      formState.custodian_department = ''
      formState.custodian_job_number = ''
    }
  }
)

// 监听使用人变化，自动填充使用人部门和工号
watch(
  () => formState.user,
  (newUser) => {
    if (newUser) {
      // 处理内部值可能的两种情况: 纯姓名 或 姓名#ID格式
      let realName = newUser
      let userId: number | null = null
      
      // 检查是否包含隐藏的用户ID (姓名#ID格式)
      if (newUser.includes('#')) {
        const parts = newUser.split('#')
        realName = parts[0]
        userId = parseInt(parts[1])
        // 更新显示值为纯姓名
        formState.user = realName
      }
      
      // 优先根据用户ID查找
      let person
      if (userId !== null) {
        person = findPersonnelByUserId(userId)
      } else {
        // 按名称查找
        person = findPersonnelByName(realName)
        // 对于同名情况，不再自动选择第一个
        if (Array.isArray(person)) {
          // 如果有多个同名人员但没有选择具体哪一个，不自动填充
          return
        }
      }

      if (person) {
        // 自动填充部门信息和工号
        formState.user_department = person.dept_name || person.DeptName || ''
        formState.user_job_number = person.job_number || person.JobNumber || ''
      }
    } else {
      // 如果清空了使用人，也清空部门和工号
      formState.user_department = ''
      formState.user_job_number = ''
    }
  }
)

// 监听验收人变化，自动填充验收人工号
watch(
  () => formState.inspector,
  (newInspector) => {
    if (newInspector) {
      // 处理内部值可能的两种情况: 纯姓名 或 姓名#ID格式
      let realName = newInspector
      let userId: number | null = null
      
      // 检查是否包含隐藏的用户ID (姓名#ID格式)
      if (newInspector.includes('#')) {
        const parts = newInspector.split('#')
        realName = parts[0]
        userId = parseInt(parts[1])
        // 更新显示值为纯姓名
        formState.inspector = realName
      }
      
      // 优先根据用户ID查找
      let person
      if (userId !== null) {
        person = findPersonnelByUserId(userId)
      } else {
        // 按名称查找
        person = findPersonnelByName(realName)
        // 对于同名情况，不再自动选择第一个
        if (Array.isArray(person)) {
          // 如果有多个同名人员但没有选择具体哪一个，不自动填充
          return
        }
      }

      if (person) {
        // 自动填充工号
        formState.inspector_job_number = person.job_number || person.JobNumber || ''
      }
    } else {
      // 如果清空了验收人，也清空工号
      formState.inspector_job_number = ''
    }
  }
)

// 监听采购人变化，自动填充采购人工号
watch(
  () => formState.purchaser,
  (newPurchaser) => {
    if (newPurchaser) {
      // 处理内部值可能的两种情况: 纯姓名 或 姓名#ID格式
      let realName = newPurchaser
      let userId: number | null = null
      
      // 检查是否包含隐藏的用户ID (姓名#ID格式)
      if (newPurchaser.includes('#')) {
        const parts = newPurchaser.split('#')
        realName = parts[0]
        userId = parseInt(parts[1])
        // 更新显示值为纯姓名
        formState.purchaser = realName
      }
      
      // 优先根据用户ID查找
      let person
      if (userId !== null) {
        person = findPersonnelByUserId(userId)
      } else {
        // 按名称查找
        person = findPersonnelByName(realName)
        // 对于同名情况，不再自动选择第一个
        if (Array.isArray(person)) {
          // 如果有多个同名人员但没有选择具体哪一个，不自动填充
          return
        }
      }

      if (person) {
        // 自动填充工号
        formState.purchaser_job_number = person.job_number || person.JobNumber || ''
      }
    } else {
      // 如果清空了采购人，也清空工号
      formState.purchaser_job_number = ''
    }
  }
)

// 自定义字段处理方法
const handleCustomFieldsReady = (fields: CustomField[]) => {
  customFields.value = fields
}

const handleCustomFieldChange = (fieldName: string, value: any, allData: Record<string, any>) => {
  customFieldData.value = { ...allData }
}

// 加载资产的自定义字段值
const loadAssetCustomFieldValues = async (assetId: number) => {
  try {
    const response = await customFieldApi.getAssetCustomFieldValues(assetId)
    assetCustomFieldValues.value = response.data || response || []
    
    // 使用工具函数转换数据
    customFieldData.value = convertApiFieldValuesToFormData(assetCustomFieldValues.value)
  } catch (error) {
    console.error('加载自定义字段值失败:', error)
  }
}

// 保存自定义字段值
const saveCustomFieldValues = async (assetId: number) => {
  try {
    if (Object.keys(customFieldData.value).length === 0) {
      return
    }

    // 使用工具函数准备数据
    const fieldValues = prepareCustomFieldValues(customFields.value, customFieldData.value)

    await customFieldApi.batchSetAssetCustomFieldValues(assetId, {
      values: fieldValues
    })
  } catch (error) {
    console.error('保存自定义字段值失败:', error)
    ElMessage.error('保存自定义字段值失败')
    throw error  // 重新抛出错误，阻止主流程继续
  }
}
</script>

<style lang="scss" scoped>
.asset-form-dialog {
  :deep(.el-dialog__header) {
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeefb;
    padding: 16px 20px;
    margin-right: 0;
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  :deep(.el-dialog__body) {
    padding: 24px 30px;
  }

  :deep(.el-dialog__footer) {
    border-top: 1px solid #eaeefb;
    padding: 12px 20px;
  }
}

.asset-form {
  .el-form-item {
    margin-bottom: 18px;
  }

  .el-row {
    margin-bottom: 0;
  }

  .el-select,
  .el-input-number,
  .el-date-picker {
    width: 100%;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input__inner),
  :deep(.el-select__input) {
    border-radius: 4px;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 16px 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.select-with-icon {
  display: flex;
  align-items: center;
  width: 100%;

  .el-input,
  .el-select {
    flex: 1;
  }

  .auto-number-toggle {
    padding: 0 8px;
    font-size: 14px;
    white-space: nowrap;
  }

  .add-icon {
    padding: 0 4px;
    margin-left: 4px;
  }
}



:deep(.el-form-item__content) {
  width: calc(100% - 100px);  // 100px是label的宽度
}
</style>
