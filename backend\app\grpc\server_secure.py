import grpc
import asyncio
import logging
import time
import datetime
import ssl
from concurrent import futures
from pathlib import Path
import os
from sqlalchemy.orm import Session
from app.database import SessionLocal
from app import models
import uuid

# 这里需要导入生成的gRPC代码
# from app.grpc.terminal_pb import terminal_pb2, terminal_pb2_grpc

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)


class SecureTerminalManagementService:
    """支持TLS加密的终端管理gRPC服务实现"""
    
    def __init__(self):
        """初始化服务"""
        pass
    
    async def RegisterTerminal(self, request, context):
        """
        处理终端注册请求
        """
        logger.info(f"收到终端注册请求: {request.hostname}, MAC: {request.mac_address}, IP: {request.ip_address}")
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 检查终端是否已注册
            existing_terminal = db.query(models.Terminal).filter(
                models.Terminal.unique_id == request.unique_id
            ).first()
            
            if existing_terminal:
                # 更新终端信息
                existing_terminal.hostname = request.hostname
                existing_terminal.mac_address = request.mac_address
                existing_terminal.ip_address = request.ip_address
                existing_terminal.agent_version = request.agent_version
                existing_terminal.online_status = True
                existing_terminal.last_online_time = datetime.datetime.utcnow()
                existing_terminal.last_heartbeat_time = datetime.datetime.utcnow()
                
                logger.info(f"更新已注册终端: {existing_terminal.id}")
                
                # 构建响应
                response = {
                    "success": True,
                    "terminal_id": existing_terminal.id,
                    "heartbeat_interval": existing_terminal.heartbeat_interval,
                    "collection_interval": existing_terminal.collection_interval,
                    "message": "终端信息已更新"
                }
            else:
                # 创建新终端
                new_terminal = models.Terminal(
                    id=str(uuid.uuid4()),
                    hostname=request.hostname,
                    unique_id=request.unique_id,
                    mac_address=request.mac_address,
                    ip_address=request.ip_address,
                    agent_version=request.agent_version,
                    online_status=True,
                    last_online_time=datetime.datetime.utcnow(),
                    last_heartbeat_time=datetime.datetime.utcnow(),
                    # 使用默认的心跳和采集间隔
                )
                
                db.add(new_terminal)
                logger.info(f"注册新终端: {new_terminal.id}")
                
                # 构建响应
                response = {
                    "success": True,
                    "terminal_id": new_terminal.id,
                    "heartbeat_interval": new_terminal.heartbeat_interval or 300,
                    "collection_interval": new_terminal.collection_interval or 86400,
                    "message": "终端注册成功"
                }
            
            # 提交事务
            db.commit()
            
            # 返回响应
            # 注意：实际使用时需要将response转换为terminal_pb2.RegisterResponse对象
            return response
            
        except Exception as e:
            db.rollback()
            logger.error(f"处理终端注册请求时发生错误: {str(e)}")
            
            # 构建错误响应
            error_response = {
                "success": False,
                "terminal_id": "",
                "heartbeat_interval": 300,  # 默认值
                "collection_interval": 86400,  # 默认值
                "message": f"注册失败: {str(e)}"
            }
            
            # 返回错误响应
            # 注意：实际使用时需要将error_response转换为terminal_pb2.RegisterResponse对象
            return error_response
            
        finally:
            db.close()
    
    # 其他方法与server.py中的实现相同，此处省略


async def serve():
    """启动gRPC服务器"""
    # 获取证书和密钥文件路径
    cert_dir = Path(__file__).parent / "certs"
    server_cert = str(cert_dir / "server.crt")
    server_key = str(cert_dir / "server.key")
    
    # 检查证书文件是否存在
    if not os.path.exists(server_cert) or not os.path.exists(server_key):
        logger.error(f"证书文件不存在: {server_cert} 或 {server_key}")
        return
    
    # 创建服务器凭证
    server_credentials = grpc.ssl_server_credentials(
        [(open(server_key, 'rb').read(), open(server_cert, 'rb').read())]
    )
    
    # 创建服务器
    server = grpc.aio.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # 注册服务
    # terminal_pb2_grpc.add_TerminalManagementServicer_to_server(
    #     SecureTerminalManagementService(), server
    # )
    
    # 添加安全端口
    server_address = '[::]:50052'  # 使用不同的端口，避免与非加密服务冲突
    server.add_secure_port(server_address, server_credentials)
    
    logger.info(f"启动安全gRPC服务器，监听地址: {server_address}")
    
    # 启动服务器
    await server.start()
    
    try:
        # 保持服务器运行
        await server.wait_for_termination()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务器...")
        await server.stop(0)
        logger.info("服务器已停止")


def generate_certificates():
    """生成自签名证书"""
    cert_dir = Path(__file__).parent / "certs"
    os.makedirs(cert_dir, exist_ok=True)
    
    server_key = cert_dir / "server.key"
    server_csr = cert_dir / "server.csr"
    server_crt = cert_dir / "server.crt"
    
    # 如果证书已存在，跳过生成
    if server_key.exists() and server_crt.exists():
        logger.info("证书文件已存在，跳过生成")
        return
    
    logger.info("正在生成自签名证书...")
    
    # 生成私钥
    os.system(f'openssl genrsa -out "{server_key}" 2048')
    
    # 生成证书签名请求
    os.system(f'openssl req -new -key "{server_key}" -out "{server_csr}" -subj "/C=CN/ST=Shanghai/L=Shanghai/O=OPS-Platform/OU=IT/CN=localhost"')
    
    # 自签名证书
    os.system(f'openssl x509 -req -days 365 -in "{server_csr}" -signkey "{server_key}" -out "{server_crt}"')
    
    # 删除CSR文件
    if server_csr.exists():
        os.remove(server_csr)
    
    logger.info(f"证书生成完成，保存在 {cert_dir}")


def main():
    """主函数"""
    try:
        # 生成证书
        generate_certificates()
        
        # 启动服务器
        asyncio.run(serve())
        
    except Exception as e:
        logger.error(f"启动服务器时发生错误: {str(e)}")


if __name__ == "__main__":
    main() 