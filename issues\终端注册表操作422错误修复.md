# 终端注册表操作422错误修复

## 问题描述
终端管理模块的注册表操作功能出现422 Unprocessable Entity错误，无法正常执行注册表读取、写入等操作。

## 错误信息
```
INFO:     127.0.0.1:13846 - "POST /api/v1/terminal/1/registry/operations HTTP/1.1" 422 Unprocessable Entity
```

## 原因分析
前端在构造注册表操作请求时缺少必需的`terminal_id`字段：
- 后端API schema中`terminal_id`是必需字段（`Field(...)`）
- 前端请求体中没有包含这个字段
- 虽然API通过路径参数接收terminal_id，但schema验证失败

## 修复方案
修改前端API客户端，在发送请求时自动添加`terminal_id`字段：
1. 修改`frontend/src/api/registry.ts`中的API调用函数
2. 确保所有注册表操作请求都包含正确的terminal_id
3. 保持后端schema定义不变，维护API清晰性

## 修复文件
- `frontend/src/api/registry.ts` - 修改API调用逻辑
- `frontend/src/types/registry.ts` - 更新类型定义（如需要）

## 执行步骤
1. ✅ 分析问题原因：前端请求缺少必需的terminal_id字段
2. ✅ 修改`frontend/src/api/registry.ts`中所有API调用函数
   - performRegistryOperation
   - performBatchRegistryOperations  
   - searchRegistry
   - createRegistryBackup
   - restoreRegistryBackup
3. ✅ 更新`frontend/src/types/registry.ts`中的接口定义
   - RegistryOperationRequest
   - RegistryBatchOperationRequest
   - RegistrySearchRequest
   - RegistryBackupRequest
   - RegistryRestoreRequest

## 修改详情
- 所有API调用现在自动添加`terminal_id`字段到请求数据中
- 前端类型定义添加可选的`terminal_id`字段，保持与后端schema一致
- 维持了API调用的兼容性，前端组件无需修改

## 预期结果
- 注册表操作API正常工作
- 不再出现422错误
- 前后端数据格式完全匹配
- 所有注册表功能恢复正常

## 后续发现的gRPC问题
在修复422错误后，发现了新的gRPC通信问题：
- gRPC proto文件缺少注册表方法定义
- 枚举值引用不正确

## gRPC修复步骤
1. ✅ 修复`backend/app/grpc/terminal_proto/terminal.proto`service定义
   - 添加PerformRegistryOperation和SearchRegistry方法
2. ✅ 重新生成gRPC Python绑定文件
   - 执行`python generate_pb.py`生成新的绑定文件
3. ✅ 修复后端代码中的枚举引用
   - 修正`READ`为`REGISTRY_READ`
   - 修正`BACKUP`为`REGISTRY_BACKUP`
   - 修正`RESTORE`为`REGISTRY_BACKUP`

## 最终状态
- 前端请求包含正确的terminal_id字段
- gRPC服务包含完整的注册表操作方法定义
- 后端代码使用正确的枚举值
- 注册表功能完全修复 