"""
注册表操作CRUD模块
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_
from datetime import datetime, timedelta

from app.models.registry_operation import RegistryOperation, RegistryBackup, RegistrySearchLog


def create_registry_operation(
    db: Session,
    command_id: str,
    terminal_id: str,
    operation_type: str,
    root_key: str,
    key_path: str,
    value_name: Optional[str] = None,
    value_type: Optional[str] = None,
    old_value_data: Optional[str] = None,
    new_value_data: Optional[str] = None,
    success: bool = False,
    error_message: Optional[str] = None,
    backup_id: Optional[str] = None,
    backup_reason: Optional[str] = None,
    operation_data: Optional[Dict] = None,
    execution_duration: Optional[int] = None
) -> RegistryOperation:
    """创建注册表操作记录"""
    db_operation = RegistryOperation(
        command_id=command_id,
        terminal_id=terminal_id,
        operation_type=operation_type,
        root_key=root_key,
        key_path=key_path,
        value_name=value_name,
        value_type=value_type,
        old_value_data=old_value_data,
        new_value_data=new_value_data,
        success=success,
        error_message=error_message,
        backup_id=backup_id,
        backup_reason=backup_reason,
        operation_data=operation_data,
        execution_duration=execution_duration
    )
    db.add(db_operation)
    db.commit()
    db.refresh(db_operation)
    return db_operation


def get_registry_operation(db: Session, operation_id: int) -> Optional[RegistryOperation]:
    """根据ID获取注册表操作记录"""
    return db.query(RegistryOperation).filter(RegistryOperation.id == operation_id).first()


def get_registry_operation_by_command_id(db: Session, command_id: str) -> Optional[RegistryOperation]:
    """根据命令ID获取注册表操作记录"""
    return db.query(RegistryOperation).filter(RegistryOperation.command_id == command_id).first()


def get_registry_operations(
    db: Session,
    terminal_id: Optional[str] = None,
    operation_type: Optional[str] = None,
    root_key: Optional[str] = None,
    success: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100
) -> List[RegistryOperation]:
    """获取注册表操作记录列表"""
    query = db.query(RegistryOperation)
    
    if terminal_id:
        query = query.filter(RegistryOperation.terminal_id == terminal_id)
    if operation_type:
        query = query.filter(RegistryOperation.operation_type == operation_type)
    if root_key:
        query = query.filter(RegistryOperation.root_key == root_key)
    if success is not None:
        query = query.filter(RegistryOperation.success == success)
    
    return query.order_by(desc(RegistryOperation.created_at)).offset(skip).limit(limit).all()


def search_registry_operations(
    db: Session,
    search_term: str,
    terminal_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[RegistryOperation]:
    """搜索注册表操作记录"""
    query = db.query(RegistryOperation)
    
    if terminal_id:
        query = query.filter(RegistryOperation.terminal_id == terminal_id)
    
    # 在多个字段中搜索
    search_filter = or_(
        RegistryOperation.key_path.ilike(f"%{search_term}%"),
        RegistryOperation.value_name.ilike(f"%{search_term}%"),
        RegistryOperation.operation_type.ilike(f"%{search_term}%"),
        RegistryOperation.error_message.ilike(f"%{search_term}%")
    )
    
    return query.filter(search_filter).order_by(desc(RegistryOperation.created_at)).offset(skip).limit(limit).all()


def get_registry_operations_count(
    db: Session,
    terminal_id: Optional[str] = None,
    operation_type: Optional[str] = None,
    success: Optional[bool] = None
) -> int:
    """获取注册表操作记录数量"""
    query = db.query(RegistryOperation)
    
    if terminal_id:
        query = query.filter(RegistryOperation.terminal_id == terminal_id)
    if operation_type:
        query = query.filter(RegistryOperation.operation_type == operation_type)
    if success is not None:
        query = query.filter(RegistryOperation.success == success)
    
    return query.count()


def delete_registry_operation(db: Session, operation_id: int) -> bool:
    """删除注册表操作记录"""
    operation = db.query(RegistryOperation).filter(RegistryOperation.id == operation_id).first()
    if operation:
        db.delete(operation)
        db.commit()
        return True
    return False


# ==================== 注册表备份相关 ====================

def create_registry_backup(
    db: Session,
    backup_id: str,
    backup_name: str,
    terminal_id: str,
    root_key: str,
    key_path: str,
    file_path: str,
    file_size: Optional[int] = None,
    file_hash: Optional[str] = None,
    reason: Optional[str] = None,
    description: Optional[str] = None,
    tags: Optional[str] = None,
    related_operation_id: Optional[int] = None,
    expire_at: Optional[datetime] = None
) -> RegistryBackup:
    """创建注册表备份记录"""
    db_backup = RegistryBackup(
        backup_id=backup_id,
        backup_name=backup_name,
        terminal_id=terminal_id,
        root_key=root_key,
        key_path=key_path,
        file_path=file_path,
        file_size=file_size,
        file_hash=file_hash,
        reason=reason,
        description=description,
        tags=tags,
        related_operation_id=related_operation_id,
        expire_at=expire_at
    )
    db.add(db_backup)
    db.commit()
    db.refresh(db_backup)
    return db_backup


def get_registry_backup(db: Session, backup_id: str) -> Optional[RegistryBackup]:
    """根据备份ID获取注册表备份记录"""
    return db.query(RegistryBackup).filter(RegistryBackup.backup_id == backup_id).first()


def get_registry_backup_by_id(db: Session, id: int) -> Optional[RegistryBackup]:
    """根据数据库ID获取注册表备份记录"""
    return db.query(RegistryBackup).filter(RegistryBackup.id == id).first()


def get_registry_backups(
    db: Session,
    terminal_id: Optional[str] = None,
    root_key: Optional[str] = None,
    status: str = "active",
    skip: int = 0,
    limit: int = 100
) -> List[RegistryBackup]:
    """获取注册表备份记录列表"""
    query = db.query(RegistryBackup)
    
    if terminal_id:
        query = query.filter(RegistryBackup.terminal_id == terminal_id)
    if root_key:
        query = query.filter(RegistryBackup.root_key == root_key)
    if status:
        query = query.filter(RegistryBackup.status == status)
    
    return query.order_by(desc(RegistryBackup.created_at)).offset(skip).limit(limit).all()


def search_registry_backups(
    db: Session,
    search_term: str,
    terminal_id: Optional[str] = None,
    skip: int = 0,
    limit: int = 100
) -> List[RegistryBackup]:
    """搜索注册表备份记录"""
    query = db.query(RegistryBackup)
    
    if terminal_id:
        query = query.filter(RegistryBackup.terminal_id == terminal_id)
    
    # 在多个字段中搜索
    search_filter = or_(
        RegistryBackup.backup_name.ilike(f"%{search_term}%"),
        RegistryBackup.key_path.ilike(f"%{search_term}%"),
        RegistryBackup.reason.ilike(f"%{search_term}%"),
        RegistryBackup.description.ilike(f"%{search_term}%"),
        RegistryBackup.tags.ilike(f"%{search_term}%")
    )
    
    return query.filter(search_filter).order_by(desc(RegistryBackup.created_at)).offset(skip).limit(limit).all()


def update_registry_backup_status(db: Session, backup_id: str, status: str) -> bool:
    """更新注册表备份状态"""
    backup = db.query(RegistryBackup).filter(RegistryBackup.backup_id == backup_id).first()
    if backup:
        backup.status = status
        backup.updated_at = datetime.utcnow()
        db.commit()
        return True
    return False


def verify_registry_backup(db: Session, backup_id: str, file_hash: Optional[str] = None) -> bool:
    """验证注册表备份"""
    backup = db.query(RegistryBackup).filter(RegistryBackup.backup_id == backup_id).first()
    if backup:
        backup.is_verified = True
        if file_hash:
            backup.file_hash = file_hash
        backup.updated_at = datetime.utcnow()
        db.commit()
        return True
    return False


def get_expired_backups(db: Session) -> List[RegistryBackup]:
    """获取已过期的备份"""
    now = datetime.utcnow()
    return db.query(RegistryBackup).filter(
        and_(
            RegistryBackup.expire_at.isnot(None),
            RegistryBackup.expire_at <= now,
            RegistryBackup.status == "active"
        )
    ).all()


def delete_registry_backup(db: Session, backup_id: str) -> bool:
    """删除注册表备份记录"""
    backup = db.query(RegistryBackup).filter(RegistryBackup.backup_id == backup_id).first()
    if backup:
        db.delete(backup)
        db.commit()
        return True
    return False


# ==================== 注册表搜索日志相关 ====================

def create_registry_search_log(
    db: Session,
    search_id: str,
    terminal_id: str,
    root_key: str,
    start_path: str,
    search_pattern: str,
    search_keys: bool = True,
    search_values: bool = True,
    search_data: bool = True,
    max_depth: int = 10,
    max_results: int = 100,
    total_results: int = 0,
    results_data: Optional[Dict] = None,
    success: bool = False,
    error_message: Optional[str] = None,
    execution_duration: Optional[int] = None
) -> RegistrySearchLog:
    """创建注册表搜索日志记录"""
    db_search_log = RegistrySearchLog(
        search_id=search_id,
        terminal_id=terminal_id,
        root_key=root_key,
        start_path=start_path,
        search_pattern=search_pattern,
        search_keys=search_keys,
        search_values=search_values,
        search_data=search_data,
        max_depth=max_depth,
        max_results=max_results,
        total_results=total_results,
        results_data=results_data,
        success=success,
        error_message=error_message,
        execution_duration=execution_duration
    )
    db.add(db_search_log)
    db.commit()
    db.refresh(db_search_log)
    return db_search_log


def get_registry_search_log(db: Session, search_id: str) -> Optional[RegistrySearchLog]:
    """根据搜索ID获取注册表搜索日志"""
    return db.query(RegistrySearchLog).filter(RegistrySearchLog.search_id == search_id).first()


def get_registry_search_logs(
    db: Session,
    terminal_id: Optional[str] = None,
    success: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100
) -> List[RegistrySearchLog]:
    """获取注册表搜索日志列表"""
    query = db.query(RegistrySearchLog)
    
    if terminal_id:
        query = query.filter(RegistrySearchLog.terminal_id == terminal_id)
    if success is not None:
        query = query.filter(RegistrySearchLog.success == success)
    
    return query.order_by(desc(RegistrySearchLog.created_at)).offset(skip).limit(limit).all()


def delete_old_search_logs(db: Session, days: int = 30) -> int:
    """删除指定天数之前的搜索日志"""
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    deleted_count = db.query(RegistrySearchLog).filter(
        RegistrySearchLog.created_at < cutoff_date
    ).count()
    
    db.query(RegistrySearchLog).filter(
        RegistrySearchLog.created_at < cutoff_date
    ).delete()
    
    db.commit()
    return deleted_count 