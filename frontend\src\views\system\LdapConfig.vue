<template>
  <div class="ldap-config">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Connection /></el-icon>
        <h2 class="page-title">LDAP配置</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>系统设置</el-breadcrumb-item>
        <el-breadcrumb-item>LDAP配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <LdapConfigManagement />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import LdapConfigManagement from './components/LdapConfigManagement.vue'
import { Connection } from '@element-plus/icons-vue'
</script>

<style scoped>
.ldap-config {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}
</style> 