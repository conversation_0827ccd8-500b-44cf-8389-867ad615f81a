from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
import logging

from app import schemas
from app.api import deps
from app.crud.permission import permission_crud

logger = logging.getLogger("permissions")

router = APIRouter()

@router.get("/", response_model=List[schemas.Permission])
def read_permissions(
    request: Request,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user = Depends(deps.check_permissions(["system:permission:view"])),
) -> Any:
    """
    获取权限列表
    """
    # 打印所有请求头，用于调试
    logger.debug(f"请求头: {dict(request.headers)}")
    
    logger.debug(f"用户 {current_user.username if current_user else 'None'} 请求获取权限列表")
    
    permissions = permission_crud.get_multi(db, skip=skip, limit=limit)
    logger.debug(f"获取权限列表成功，数量: {len(permissions)}")
    return permissions

@router.get("/modules", response_model=List[str])
def read_permission_modules(
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:permission:view"])),
) -> Any:
    """
    获取权限模块列表
    """
    # 打印所有请求头，用于调试
    logger.debug(f"请求头: {dict(request.headers)}")
    
    logger.debug(f"用户 {current_user.username if current_user else 'None'} 请求获取权限模块列表")
    
    modules = permission_crud.get_modules(db)
    logger.debug(f"获取权限模块列表成功，模块: {modules}")
    return modules

@router.get("/by-module/{module}", response_model=List[schemas.Permission])
def read_permissions_by_module(
    module: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.check_permissions(["system:permission:view"])),
) -> Any:
    """
    获取指定模块的权限列表
    """
    permissions = permission_crud.get_by_module(db, module=module)
    return permissions

@router.post("/", response_model=schemas.Permission)
def create_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_in: schemas.PermissionCreate,
    current_user = Depends(deps.check_permissions(["system:permission:add"])),
) -> Any:
    """
    创建权限
    """
    permission = permission_crud.get_by_code(db, code=permission_in.code)
    if permission:
        raise HTTPException(status_code=400, detail="权限代码已存在")
    permission = permission_crud.create(db, obj_in=permission_in)
    return permission

@router.put("/{permission_id}", response_model=schemas.Permission)
def update_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: int,
    permission_in: schemas.PermissionUpdate,
    current_user = Depends(deps.check_permissions(["system:permission:edit"])),
) -> Any:
    """
    更新权限
    """
    permission = permission_crud.get(db, id=permission_id)
    if not permission:
        raise HTTPException(status_code=404, detail="权限不存在")
    permission = permission_crud.update(db, db_obj=permission, obj_in=permission_in)
    return permission

@router.delete("/{permission_id}", response_model=schemas.Permission)
def delete_permission(
    *,
    db: Session = Depends(deps.get_db),
    permission_id: int,
    current_user = Depends(deps.check_permissions(["system:permission:delete"])),
) -> Any:
    """
    删除权限
    """
    permission = permission_crud.get(db, id=permission_id)
    if not permission:
        raise HTTPException(status_code=404, detail="权限不存在")
    permission = permission_crud.remove(db, id=permission_id)
    return permission 