from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, field_serializer, field_validator
from .asset import AssetResponse

# 盘点任务基础模型
class InventoryTaskBase(BaseModel):
    """盘点任务基础模型"""
    name: str = Field(..., description="盘点任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def parse_date(cls, value):
        if isinstance(value, str):
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                try:
                    return datetime.fromisoformat(value)
                except ValueError:
                    raise ValueError("Invalid date format")
        return value

    @field_serializer('start_date', 'end_date')
    def serialize_date(self, date: datetime, _info):
        if date:
            return date.strftime("%Y-%m-%d")
        return None

# 创建盘点任务模型
class InventoryTaskCreate(InventoryTaskBase):
    """创建盘点任务模型"""
    created_by: str = Field(..., description="创建人")

# 更新盘点任务模型
class InventoryTaskUpdate(BaseModel):
    """更新盘点任务模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    @field_validator('start_date', 'end_date', mode='before')
    @classmethod
    def parse_date(cls, value):
        if isinstance(value, str):
            try:
                return datetime.strptime(value, "%Y-%m-%d")
            except ValueError:
                try:
                    return datetime.fromisoformat(value)
                except ValueError:
                    raise ValueError("Invalid date format")
        return value

# 盘点记录基础模型
class InventoryRecordBase(BaseModel):
    """盘点记录基础模型"""
    task_id: int = Field(..., description="盘点任务ID")
    asset_id: int = Field(..., description="资产ID")
    status: str = Field(..., description="盘点状态")
    remarks: Optional[str] = Field(None, description="备注")

# 创建盘点记录模型
class InventoryRecordCreate(InventoryRecordBase):
    """创建盘点记录模型"""
    checked_by: Optional[str] = Field(None, description="盘点人")
    new_name: Optional[str] = None
    new_specification: Optional[str] = None
    new_status: Optional[str] = None
    new_custodian: Optional[str] = None
    new_custodian_department: Optional[str] = None
    new_user: Optional[str] = None
    new_user_department: Optional[str] = None
    new_location: Optional[str] = None
    new_company: Optional[str] = None
    new_remarks: Optional[str] = None
    new_production_number: Optional[str] = None
    new_price: Optional[float] = None
    new_supplier: Optional[str] = None
    new_manufacturer: Optional[str] = None
    new_purchaser: Optional[str] = None

# 更新盘点记录模型
class InventoryRecordUpdate(BaseModel):
    """更新盘点记录模型"""
    status: Optional[str] = None
    remarks: Optional[str] = None
    checked_by: Optional[str] = None
    new_name: Optional[str] = None
    new_specification: Optional[str] = None
    new_status: Optional[str] = None
    new_custodian: Optional[str] = None
    new_custodian_department: Optional[str] = None
    new_user: Optional[str] = None
    new_user_department: Optional[str] = None
    new_location: Optional[str] = None
    new_company: Optional[str] = None
    new_remarks: Optional[str] = None
    new_production_number: Optional[str] = None
    new_price: Optional[float] = None
    new_supplier: Optional[str] = None
    new_manufacturer: Optional[str] = None
    new_purchaser: Optional[str] = None

# 盘点记录响应模型
class InventoryRecordResponse(InventoryRecordBase):
    """盘点记录响应模型"""
    id: Optional[int] = None
    checked_by: Optional[str] = None
    checked_at: Optional[datetime] = None
    new_name: Optional[str] = None
    new_specification: Optional[str] = None
    new_status: Optional[str] = None
    new_custodian: Optional[str] = None
    new_custodian_department: Optional[str] = None
    new_user: Optional[str] = None
    new_user_department: Optional[str] = None
    new_location: Optional[str] = None
    new_company: Optional[str] = None
    new_remarks: Optional[str] = None
    new_production_number: Optional[str] = None
    new_price: Optional[float] = None
    new_supplier: Optional[str] = None
    new_manufacturer: Optional[str] = None
    new_purchaser: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    asset: Optional[AssetResponse] = None

    @field_serializer('checked_at', 'created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime, _info):
        if dt:
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True

# 盘点任务响应模型
class InventoryTaskResponse(InventoryTaskBase):
    """盘点任务响应模型"""
    id: int
    status: str
    created_by: str
    created_at: datetime
    updated_at: datetime
    inventory_records: List[InventoryRecordResponse]

    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime, _info):
        if dt:
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        return None

    class Config:
        from_attributes = True

# 盘点任务列表响应模型
class InventoryTaskListResponse(BaseModel):
    """盘点任务列表响应模型"""
    data: List[InventoryTaskResponse]
    total: int 

# 盘点记录列表响应模型
class InventoryRecordListResponse(BaseModel):
    """盘点记录列表响应模型"""
    data: List[InventoryRecordResponse]
    total: int 

# 为了兼容性，添加别名
InventoryTask = InventoryTaskResponse
InventoryRecord = InventoryRecordResponse 