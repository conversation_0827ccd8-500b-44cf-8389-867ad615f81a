/**
 * 注册表管理API客户端
 */

import request from '@/utils/request'
import type {
  RegistryOperationRequest,
  RegistryOperationResponse,
  RegistryBatchOperationRequest,
  RegistryBatchOperationResponse,
  RegistrySearchRequest,
  RegistrySearchResponse,
  RegistryBackupRequest,
  RegistryBackupResponse,
  RegistryRestoreRequest,
  RegistryOperationHistory,
  RegistryBackupHistory,
  RegistryStatistics,
  RegistryOperationQuery,
  RegistryBackupQuery
} from '@/types/registry'

const BASE_URL = '/registry'

// === 注册表基本操作 ===

/**
 * 执行注册表操作（读取、写入、删除等）
 */
export const performRegistryOperation = (
  terminalId: number,
  requestData: RegistryOperationRequest
): Promise<RegistryOperationResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/operations`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  }).then(response => {
    // 确保返回正确的数据结构
    const data = response.data || response
    console.log('Registry API Response:', data)
    return data
  })
}

/**
 * 执行注册表操作（支持分页）
 */
export const performRegistryOperationPaginated = (
  terminalId: number,
  requestData: RegistryOperationRequest & {
    page?: number
    page_size?: number
    search_filter?: string
  }
): Promise<RegistryOperationResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/operations`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  }).then(response => {
    const data = response.data || response
    console.log('Registry API Response (Paginated):', data)
    return data
  })
}

/**
 * 执行批量注册表操作
 */
export const performBatchRegistryOperations = (
  terminalId: number,
  requestData: RegistryBatchOperationRequest
): Promise<RegistryBatchOperationResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/batch-operations`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  })
}

// === 注册表搜索 ===

/**
 * 搜索注册表
 */
export const searchRegistry = (
  terminalId: number,
  requestData: RegistrySearchRequest
): Promise<RegistrySearchResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/search`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  })
}

// === 注册表备份和还原 ===

/**
 * 创建注册表备份
 */
export const createRegistryBackup = (
  terminalId: number,
  requestData: RegistryBackupRequest
): Promise<RegistryBackupResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/backups`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  })
}

/**
 * 还原注册表备份
 */
export const restoreRegistryBackup = (
  terminalId: number,
  backupId: string,
  requestData: RegistryRestoreRequest
): Promise<RegistryOperationResponse> => {
  return request({
    url: `/terminal/${terminalId}/registry/backups/${backupId}/restore`,
    method: 'post',
    data: {
      ...requestData,
      terminal_id: terminalId
    }
  })
}

/**
 * 获取注册表备份列表
 */
export const getRegistryBackups = (
  terminalId: number,
  params?: RegistryBackupQuery
): Promise<RegistryBackupHistory[]> => {
  return request({
    url: `/terminal/${terminalId}/registry/backups`,
    method: 'get',
    params
  })
}

/**
 * 删除注册表备份
 */
export const deleteRegistryBackup = (
  terminalId: number,
  backupId: string
): Promise<{ message: string }> => {
  return request({
    url: `/terminal/${terminalId}/registry/backups/${backupId}`,
    method: 'delete'
  })
}

/**
 * 验证注册表备份
 */
export const verifyRegistryBackup = (
  terminalId: number,
  backupId: string
): Promise<{ message: string; verified: boolean }> => {
  return request({
    url: `/terminal/${terminalId}/registry/backups/${backupId}/verify`,
    method: 'post'
  })
}

// === 历史记录和统计 ===

/**
 * 获取注册表操作历史
 */
export const getRegistryOperations = (
  terminalId: number,
  params?: RegistryOperationQuery
): Promise<RegistryOperationHistory[]> => {
  return request({
    url: `/terminal/${terminalId}/registry/operations`,
    method: 'get',
    params
  })
}

/**
 * 获取注册表操作统计
 */
export const getRegistryStatistics = (
  terminalId: number
): Promise<RegistryStatistics> => {
  return request({
    url: `/terminal/${terminalId}/registry/statistics`,
    method: 'get'
  })
}

// === 权限检查辅助函数 ===

/**
 * 检查用户是否有指定的注册表权限
 */
export const checkRegistryPermission = (permission: string): boolean => {
  // 这里应该从用户store中获取权限信息
  // 暂时返回true，实际应该集成权限系统
  return true
}

// === 错误处理辅助函数 ===

/**
 * 处理注册表API错误
 */
export const handleRegistryError = (error: any): string => {
  if (error.response?.data?.detail) {
    return error.response.data.detail
  }
  if (error.message) {
    return error.message
  }
  return '注册表操作失败，请稍后重试'
}

// === 注册表操作辅助函数 ===

/**
 * 格式化注册表值数据显示
 */
export const formatRegistryValueData = (data: string, type: string): string => {
  if (!data) return ''
  
  // 限制显示长度
  const maxLength = 100
  const truncated = data.length > maxLength ? data.substring(0, maxLength) + '...' : data
  
  switch (type) {
    case 'REG_MULTI_SZ':
      return truncated.replace(/\n/g, ' | ')
    case 'REG_BINARY':
      return truncated.toUpperCase()
    default:
      return truncated
  }
}

/**
 * 获取注册表值类型的标签样式
 */
export const getRegistryValueTypeTagType = (type: string): string => {
  switch (type) {
    case 'REG_SZ':
    case 'REG_EXPAND_SZ':
    case 'REG_MULTI_SZ':
      return 'primary'
    case 'REG_DWORD':
    case 'REG_QWORD':
      return 'success'
    case 'REG_BINARY':
      return 'warning'
    default:
      return 'info'
  }
}

/**
 * 获取匹配类型的中文标签
 */
export const getMatchTypeLabel = (type: string): string => {
  switch (type) {
    case 'key': return '键名'
    case 'value_name': return '值名'
    case 'value_data': return '值数据'
    default: return type
  }
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes?: number): string => {
  if (!bytes) return '-'
  
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

/**
 * 格式化执行时间
 */
export const formatExecutionTime = (milliseconds?: number): string => {
  if (!milliseconds) return '-'
  
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = Math.floor((milliseconds % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 验证注册表路径格式
 */
export const validateRegistryPath = (path: string): boolean => {
  // 基本的路径格式验证
  const rootKeys = [
    'HKEY_CLASSES_ROOT',
    'HKEY_CURRENT_USER', 
    'HKEY_LOCAL_MACHINE',
    'HKEY_USERS',
    'HKEY_CURRENT_CONFIG'
  ]
  
  if (!path) return true // 空路径也是有效的
  
  const parts = path.split('\\')
  if (parts.length === 0) return false
  
  return rootKeys.includes(parts[0])
}

/**
 * 分割注册表路径
 */
export const splitRegistryPath = (path: string): { rootKey: string; subPath: string } => {
  if (!path) return { rootKey: '', subPath: '' }
  
  const parts = path.split('\\')
  return {
    rootKey: parts[0] || '',
    subPath: parts.slice(1).join('\\')
  }
}

/**
 * 合并注册表路径
 */
export const joinRegistryPath = (rootKey: string, subPath: string): string => {
  if (!rootKey) return ''
  if (!subPath) return rootKey
  return `${rootKey}\\${subPath}`
}

/**
 * 检查是否为危险注册表路径
 */
export const isDangerousRegistryPath = (path: string): boolean => {
  const dangerousPaths = [
    'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet',
    'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion',
    'HKEY_LOCAL_MACHINE\\SECURITY',
    'HKEY_LOCAL_MACHINE\\SAM'
  ]
  
  const upperPath = path.toUpperCase()
  return dangerousPaths.some(dangerous => upperPath.includes(dangerous.toUpperCase()))
}

/**
 * 生成备份名称
 */
export const generateBackupName = (rootKey: string, keyPath: string): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
  const keyName = keyPath.replace(/\\/g, '_') || 'ROOT'
  return `${rootKey}_${keyName}_${timestamp}`
}

/**
 * 解析注册表值类型显示名称
 */
export const getRegistryValueTypeDisplayName = (type: string): string => {
  const typeNames: Record<string, string> = {
    'REG_SZ': '字符串',
    'REG_EXPAND_SZ': '可扩展字符串', 
    'REG_BINARY': '二进制',
    'REG_DWORD': '32位数值',
    'REG_QWORD': '64位数值',
    'REG_MULTI_SZ': '多字符串'
  }
  
  return typeNames[type] || type
} 