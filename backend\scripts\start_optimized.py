#!/usr/bin/env python3
"""
OPS平台优化启动脚本
提供更快的启动速度和更好的开发体验
"""

import os
import sys
import time
import subprocess
import threading
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class OptimizedStarter:
    """优化启动器"""
    
    def __init__(self):
        self.backend_dir = Path(__file__).parent.parent
        self.frontend_dir = self.backend_dir.parent / "frontend"
        self.backend_process = None
        self.frontend_process = None
        
    def start_backend(self):
        """启动后端服务"""
        logger.info("启动后端服务...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.backend_dir)
        
        # 启动后端
        cmd = [sys.executable, "run.py"]
        self.backend_process = subprocess.Popen(
            cmd,
            cwd=self.backend_dir,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8'
        )
        
        # 监控后端输出
        def monitor_backend():
            for line in iter(self.backend_process.stdout.readline, ''):
                if line.strip():
                    logger.info(f"[后端] {line.strip()}")
        
        threading.Thread(target=monitor_backend, daemon=True).start()
        logger.info("后端服务启动中...")
    
    def start_frontend(self):
        """启动前端服务"""
        logger.info("启动前端服务...")
        
        # 检查node_modules
        if not (self.frontend_dir / "node_modules").exists():
            logger.warning("前端依赖未安装，正在安装...")
            subprocess.run(["npm", "install"], cwd=self.frontend_dir, check=True)
        
        # 启动前端
        cmd = ["npm", "run", "dev"]
        self.frontend_process = subprocess.Popen(
            cmd,
            cwd=self.frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8'
        )
        
        # 监控前端输出
        def monitor_frontend():
            for line in iter(self.frontend_process.stdout.readline, ''):
                if line.strip():
                    logger.info(f"[前端] {line.strip()}")
        
        threading.Thread(target=monitor_frontend, daemon=True).start()
        logger.info("前端服务启动中...")
    
    def wait_for_services(self, timeout=60):
        """等待服务启动完成"""
        import requests
        
        backend_ready = False
        frontend_ready = False
        start_time = time.time()
        
        logger.info("等待服务启动完成...")
        
        while time.time() - start_time < timeout:
            # 检查后端
            if not backend_ready:
                try:
                    response = requests.get("http://localhost:8000/", timeout=2)
                    if response.status_code == 200:
                        backend_ready = True
                        logger.info("✅ 后端服务就绪")
                except:
                    pass
            
            # 检查前端
            if not frontend_ready:
                try:
                    response = requests.get("http://localhost:3000", timeout=2)
                    if response.status_code == 200:
                        frontend_ready = True
                        logger.info("✅ 前端服务就绪")
                except:
                    pass
            
            if backend_ready and frontend_ready:
                total_time = time.time() - start_time
                logger.info(f"🎉 所有服务启动完成，总耗时: {total_time:.2f}秒")
                return True
            
            time.sleep(1)
        
        logger.error("⚠️ 服务启动超时")
        return False
    
    def start(self):
        """启动所有服务"""
        logger.info("=== OPS平台优化启动 ===")
        start_time = time.time()
        
        try:
            # 并行启动服务
            backend_thread = threading.Thread(target=self.start_backend)
            frontend_thread = threading.Thread(target=self.start_frontend)
            
            backend_thread.start()
            frontend_thread.start()
            
            # 等待启动线程完成
            backend_thread.join()
            frontend_thread.join()
            
            # 等待服务就绪
            if self.wait_for_services():
                total_time = time.time() - start_time
                logger.info(f"🚀 启动完成，总耗时: {total_time:.2f}秒")
                logger.info("访问地址:")
                logger.info("  - 前端: http://localhost:3000")
                logger.info("  - 后端: http://localhost:8000")
                logger.info("按 Ctrl+C 停止服务")
                
                # 等待用户中断
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("正在停止服务...")
                    self.stop()
            else:
                logger.error("启动失败")
                self.stop()
                
        except Exception as e:
            logger.error(f"启动出错: {e}")
            self.stop()
    
    def stop(self):
        """停止所有服务"""
        logger.info("停止服务...")
        
        if self.backend_process:
            self.backend_process.terminate()
            logger.info("后端服务已停止")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            logger.info("前端服务已停止")

def main():
    """主函数"""
    starter = OptimizedStarter()
    starter.start()

if __name__ == "__main__":
    main() 