"""
异常情况处理测试
测试系统在各种异常情况下的处理能力
"""

import pytest
import asyncio
import logging
from typing import Dict, List, Any
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.database import get_db
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember
from app.services.email_extid_completion import EmailExtidCompletionService
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.services.email_api import TencentEmailAPIService
from app.schemas.email_personnel_sync import ExtidCompletionStats, PersonnelSyncStats

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class TestExceptionHandling:
    """异常情况处理测试类"""
    
    @pytest.fixture(scope="class")
    def db_session(self):
        """数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture(scope="class")
    def test_data(self, db_session: Session):
        """准备测试数据"""
        # 清理现有测试数据
        db_session.query(EcologyUser).filter(
            EcologyUser.job_number.like("EXC_%")
        ).delete(synchronize_session=False)
        
        db_session.query(EmailMember).filter(
            EmailMember.extid.like("EXC_%")
        ).delete(synchronize_session=False)
        
        # 创建异常测试数据
        test_users = [
            EcologyUser(
                user_id=3001,
                user_name="异常测试用户1",
                job_number="EXC_001",
                dept_name="异常测试部门",
                job_title_name="测试工程师",
                mobile="13800138001",
                status="active"
            ),
            EcologyUser(
                user_id=3002,
                user_name="异常测试用户2",
                job_number="EXC_002",
                dept_name="异常测试部门",
                job_title_name="测试工程师",
                mobile="13800138002",
                status="active"
            )
        ]
        
        test_email_members = [
            EmailMember(
                extid="EXC_001",
                email="<EMAIL>",
                name="异常测试用户1",
                department_id="exc_dept",
                position="测试工程师",
                mobile="13800138001",
                is_active=True
            )
        ]
        
        # 添加到数据库
        for user in test_users:
            db_session.merge(user)
        for member in test_email_members:
            db_session.merge(member)
        
        db_session.commit()
        
        return {
            "users": test_users,
            "email_members": test_email_members
        }
    
    def test_database_connection_failure(self, test_data: Dict):
        """测试数据库连接失败的处理"""
        logger.info("=== 测试数据库连接失败的处理 ===")
        
        # 模拟数据库连接失败
        with patch('app.database.get_db') as mock_get_db:
            mock_get_db.side_effect = SQLAlchemyError("数据库连接失败")
            
            # 测试服务初始化时的异常处理
            try:
                db = next(get_db())
                completion_service = EmailExtidCompletionService(db)
                # 这里应该抛出异常或者有适当的错误处理
                assert False, "应该抛出数据库连接异常"
            except SQLAlchemyError:
                logger.info("✅ 正确捕获数据库连接异常")
            except Exception as e:
                logger.info(f"✅ 捕获到其他异常: {type(e).__name__}: {e}")
    
    def test_invalid_parameters_handling(self, db_session: Session, test_data: Dict):
        """测试无效参数的处理"""
        logger.info("=== 测试无效参数的处理 ===")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 测试无效页码
        try:
            matches = completion_service.find_name_matches(page=-1, page_size=10)
            # 应该处理无效页码，返回合理的结果或抛出异常
            assert matches.page >= 1, "页码应该被修正为有效值"
            logger.info("✅ 无效页码被正确处理")
        except (ValueError, TypeError) as e:
            logger.info(f"✅ 正确抛出参数异常: {e}")
        
        # 测试无效页面大小
        try:
            matches = completion_service.find_name_matches(page=1, page_size=0)
            assert matches.page_size > 0, "页面大小应该被修正为有效值"
            logger.info("✅ 无效页面大小被正确处理")
        except (ValueError, TypeError) as e:
            logger.info(f"✅ 正确抛出参数异常: {e}")
        
        # 测试过大的页面大小
        try:
            matches = completion_service.find_name_matches(page=1, page_size=10000)
            assert matches.page_size <= 1000, "页面大小应该有上限限制"
            logger.info("✅ 过大页面大小被正确处理")
        except (ValueError, TypeError) as e:
            logger.info(f"✅ 正确抛出参数异常: {e}")
    
    def test_api_failure_handling(self, db_session: Session, test_data: Dict):
        """测试API调用失败的处理"""
        logger.info("=== 测试API调用失败的处理 ===")
        
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 模拟腾讯API调用失败
        with patch.object(TencentEmailAPIService, 'get_member_list') as mock_api:
            # 模拟API返回错误
            mock_response = Mock()
            mock_response.errcode = 40001
            mock_response.errmsg = "access_token过期"
            mock_response.data = None
            mock_api.return_value = mock_response
            
            try:
                # 执行需要调用API的操作
                result = asyncio.run(sync_service.check_data_consistency())
                # 应该有适当的错误处理，不应该崩溃
                logger.info("✅ API失败时系统正常处理")
            except Exception as e:
                # 如果抛出异常，应该是可预期的异常类型
                logger.info(f"✅ API失败时抛出可预期异常: {type(e).__name__}: {e}")
    
    def test_data_integrity_violation(self, db_session: Session, test_data: Dict):
        """测试数据完整性违反的处理"""
        logger.info("=== 测试数据完整性违反的处理 ===")
        
        # 尝试创建重复的工号
        duplicate_member = EmailMember(
            extid="EXC_001",  # 重复的工号
            email="<EMAIL>",
            name="重复工号用户",
            department_id="exc_dept",
            position="测试工程师",
            mobile="13800138999",
            is_active=True
        )
        
        try:
            db_session.add(duplicate_member)
            db_session.commit()
            assert False, "应该抛出完整性约束异常"
        except IntegrityError:
            db_session.rollback()
            logger.info("✅ 正确捕获数据完整性违反异常")
        except Exception as e:
            db_session.rollback()
            logger.info(f"✅ 捕获到其他数据库异常: {type(e).__name__}: {e}")
    
    def test_memory_overflow_handling(self, db_session: Session):
        """测试内存溢出的处理"""
        logger.info("=== 测试内存溢出的处理 ===")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 测试请求过大的数据量
        try:
            # 尝试获取非常大的页面大小
            matches = completion_service.find_name_matches(page=1, page_size=100000)
            # 应该有合理的限制或分页处理
            assert matches.page_size <= 1000, "应该限制最大页面大小"
            logger.info("✅ 大数据量请求被正确限制")
        except (ValueError, MemoryError) as e:
            logger.info(f"✅ 正确处理内存相关异常: {e}")
    
    def test_concurrent_modification_handling(self, db_session: Session, test_data: Dict):
        """测试并发修改的处理"""
        logger.info("=== 测试并发修改的处理 ===")
        
        # 获取测试用户
        test_member = db_session.query(EmailMember).filter(
            EmailMember.extid == "EXC_001"
        ).first()
        
        if test_member:
            original_position = test_member.position
            
            # 模拟并发修改场景
            # 在一个事务中修改数据
            test_member.position = "修改后的职位1"
            
            # 在另一个会话中也修改同一条数据
            db2 = next(get_db())
            try:
                test_member2 = db2.query(EmailMember).filter(
                    EmailMember.extid == "EXC_001"
                ).first()
                
                if test_member2:
                    test_member2.position = "修改后的职位2"
                    
                    # 先提交第一个修改
                    db_session.commit()
                    
                    # 再提交第二个修改，可能会有冲突
                    try:
                        db2.commit()
                        logger.info("✅ 并发修改正常处理")
                    except Exception as e:
                        db2.rollback()
                        logger.info(f"✅ 正确处理并发修改冲突: {e}")
                
            finally:
                db2.close()
                
            # 恢复原始状态
            test_member.position = original_position
            db_session.commit()
    
    def test_network_timeout_handling(self, db_session: Session):
        """测试网络超时的处理"""
        logger.info("=== 测试网络超时的处理 ===")
        
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 模拟网络超时
        with patch.object(TencentEmailAPIService, 'get_member_list') as mock_api:
            mock_api.side_effect = asyncio.TimeoutError("网络请求超时")
            
            try:
                result = asyncio.run(sync_service.check_data_consistency())
                logger.info("✅ 网络超时时系统正常处理")
            except asyncio.TimeoutError:
                logger.info("✅ 正确捕获网络超时异常")
            except Exception as e:
                logger.info(f"✅ 捕获到其他网络相关异常: {type(e).__name__}: {e}")
    
    def test_resource_exhaustion_handling(self, db_session: Session):
        """测试资源耗尽的处理"""
        logger.info("=== 测试资源耗尽的处理 ===")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 模拟数据库连接池耗尽
        with patch('app.database.engine.pool') as mock_pool:
            mock_pool.side_effect = Exception("连接池已满")
            
            try:
                stats = completion_service.get_completion_stats()
                logger.info("✅ 资源耗尽时系统正常处理")
            except Exception as e:
                logger.info(f"✅ 正确处理资源耗尽异常: {type(e).__name__}: {e}")
    
    def test_data_corruption_handling(self, db_session: Session, test_data: Dict):
        """测试数据损坏的处理"""
        logger.info("=== 测试数据损坏的处理 ===")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 模拟数据库返回损坏的数据
        with patch.object(db_session, 'query') as mock_query:
            mock_result = Mock()
            mock_result.all.return_value = [None, "invalid_data", 12345]
            mock_query.return_value = mock_result
            
            try:
                stats = completion_service.get_completion_stats()
                # 应该有适当的数据验证和错误处理
                logger.info("✅ 数据损坏时系统正常处理")
            except Exception as e:
                logger.info(f"✅ 正确处理数据损坏异常: {type(e).__name__}: {e}")
    
    def teardown_method(self, method):
        """清理测试数据"""
        db = next(get_db())
        try:
            # 清理测试数据
            db.query(EcologyUser).filter(
                EcologyUser.job_number.like("EXC_%")
            ).delete(synchronize_session=False)
            
            db.query(EmailMember).filter(
                EmailMember.extid.like("EXC_%")
            ).delete(synchronize_session=False)
            
            db.commit()
            logger.info("异常测试数据清理完成")
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")
            db.rollback()
        finally:
            db.close()

if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v"])
