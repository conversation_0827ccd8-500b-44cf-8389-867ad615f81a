/**
 * 日期格式化工具函数
 */

/**
 * 格式化日期时间
 * @param date 日期对象、时间戳或日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(
  date: Date | string | number | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!date) return '-'
  
  let dateObj: Date
  
  if (typeof date === 'string') {
    dateObj = new Date(date)
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else if (date instanceof Date) {
    dateObj = date
  } else {
    return '-'
  }
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '-'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期（不包含时间）
 * @param date 日期对象、时间戳或日期字符串
 * @returns 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDate(date: Date | string | number | null | undefined): string {
  return formatDateTime(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间（不包含日期）
 * @param date 日期对象、时间戳或日期字符串
 * @returns 格式化后的时间字符串 HH:mm:ss
 */
export function formatTime(date: Date | string | number | null | undefined): string {
  return formatDateTime(date, 'HH:mm:ss')
}

/**
 * 获取相对时间描述
 * @param date 日期对象、时间戳或日期字符串
 * @returns 相对时间描述，如 "刚刚"、"5分钟前"、"2小时前" 等
 */
export function getRelativeTime(date: Date | string | number | null | undefined): string {
  if (!date) return '-'
  
  let dateObj: Date
  
  if (typeof date === 'string') {
    dateObj = new Date(date)
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else if (date instanceof Date) {
    dateObj = date
  } else {
    return '-'
  }
  
  if (isNaN(dateObj.getTime())) {
    return '-'
  }
  
  const now = new Date()
  const diff = now.getTime() - dateObj.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天，显示具体日期
  return formatDate(dateObj)
}

/**
 * 判断是否为今天
 * @param date 日期对象、时间戳或日期字符串
 * @returns 是否为今天
 */
export function isToday(date: Date | string | number | null | undefined): boolean {
  if (!date) return false
  
  let dateObj: Date
  
  if (typeof date === 'string') {
    dateObj = new Date(date)
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else if (date instanceof Date) {
    dateObj = date
  } else {
    return false
  }
  
  if (isNaN(dateObj.getTime())) {
    return false
  }
  
  const today = new Date()
  return (
    dateObj.getFullYear() === today.getFullYear() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getDate() === today.getDate()
  )
}

/**
 * 获取日期范围描述
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围描述
 */
export function getDateRange(
  startDate: Date | string | number | null | undefined,
  endDate: Date | string | number | null | undefined
): string {
  const start = formatDate(startDate)
  const end = formatDate(endDate)
  
  if (start === '-' && end === '-') {
    return '-'
  } else if (start === '-') {
    return `至 ${end}`
  } else if (end === '-') {
    return `${start} 至今`
  } else {
    return `${start} 至 ${end}`
  }
}

/**
 * 解析中文日期格式
 * @param chineseDate 中文日期字符串，如 "2025年06月25日 21:31:58"
 * @returns Date对象或null
 */
export function parseChineseDate(chineseDate: string): Date | null {
  if (!chineseDate || typeof chineseDate !== 'string') {
    return null
  }
  
  // 匹配中文日期格式：YYYY年MM月DD日 HH:mm:ss
  const regex = /^(\d{4})年(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{1,2}):(\d{1,2})$/
  const match = chineseDate.match(regex)
  
  if (!match) {
    return null
  }
  
  const [, year, month, day, hour, minute, second] = match
  
  // 创建Date对象（月份需要减1，因为JavaScript中月份是0-11）
  const date = new Date(
    parseInt(year),
    parseInt(month) - 1,
    parseInt(day),
    parseInt(hour),
    parseInt(minute),
    parseInt(second)
  )
  
  // 验证日期是否有效
  if (isNaN(date.getTime())) {
    return null
  }
  
  return date
}

/**
 * 智能格式化日期时间 - 支持中文格式
 * @param date 日期对象、时间戳、标准日期字符串或中文日期字符串
 * @param format 格式化模板，默认为 'YYYY/MM/DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDateTimeIntelligent(
  date: Date | string | number | null | undefined,
  format: string = 'YYYY/MM/DD HH:mm:ss'
): string {
  if (!date) return '-'
  
  let dateObj: Date
  
  if (typeof date === 'string') {
    // 首先尝试解析中文格式
    const chineseDate = parseChineseDate(date)
    if (chineseDate) {
      dateObj = chineseDate
    } else {
      // 回退到标准解析
      dateObj = new Date(date)
    }
  } else if (typeof date === 'number') {
    dateObj = new Date(date)
  } else if (date instanceof Date) {
    dateObj = date
  } else {
    return '-'
  }
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '-'
  }
  
  const year = dateObj.getFullYear()
  const month = String(dateObj.getMonth() + 1).padStart(2, '0')
  const day = String(dateObj.getDate()).padStart(2, '0')
  const hours = String(dateObj.getHours()).padStart(2, '0')
  const minutes = String(dateObj.getMinutes()).padStart(2, '0')
  const seconds = String(dateObj.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
} 