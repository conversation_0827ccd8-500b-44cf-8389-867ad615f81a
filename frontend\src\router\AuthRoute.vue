<template>
  <slot v-if="hasPermission" />
  <PageForbidden v-else />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { hasAnyPermission } from '@/utils/permission';
import PageForbidden from '@/views/error/403.vue';

const route = useRoute();

const hasPermission = computed(() => {
  const requiredPermissions = route.meta.permissions as string[] | undefined;
  
  // 如果路由没有定义权限要求，则允许访问
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true;
  }
  
  // 检查用户是否拥有所需的任一权限
  return hasAnyPermission(requiredPermissions);
});
</script> 