from ldap3 import (
    Server,
    Connection,
    ALL,
    SUBTREE,
    MODIFY_REPLACE,
    MODIFY_ADD,
    MODIFY_DELETE,
    SIMPLE,
    BASE,
    LEVEL,
    NTLM
)
import logging
from typing import Optional, List, Dict, Any, Tuple
import asyncio
from ..database import SessionLocal
from .. import models
import time
import re
import ldap3
from .redis_cache import RedisCache

logger = logging.getLogger(__name__)

class ADClient:
    def __init__(self):
        self._config = None
        self._conn_pool = {}  # 连接池
        self._conn = None     # 当前活动连接
        self._server = None
        self._logger = logging.getLogger(__name__)
        self._last_connect_time = 0
        self._connection_timeout = 300  # 5分钟超时
        self._max_pool_size = 5  # 最大连接池大小
        self._retry_count = 3  # 连接重试次数
        self._retry_delay = 1  # 重试延迟(秒)
        self._max_retries = 3  # 最大重试次数
        self._query_timeout = 30  # 查询超时时间(秒)
        self._health_check_interval = 60  # 健康检查间隔(秒)
        self._last_health_check = 0  # 上次健康检查时间
        self._metrics = {  # 性能指标
            'queries': 0,
            'errors': 0,
            'connection_errors': 0,
            'retry_count': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        self._cache_ttl = 300  # 缓存有效期(秒)
        # 使用Redis缓存替换内存缓存
        self._cache = RedisCache()

    def _get_config_from_db(self) -> Optional[Dict[str, Any]]:
        """从数据库获取AD配置"""
        if self._config:
            return self._config

        db = SessionLocal()
        try:
            config = db.query(models.ADConfig).first()
            if not config:
                self._logger.error("数据库中未找到AD配置")
                return None

            self._config = {
                'server': config.server,
                'domain': config.domain,
                'username': config.username,
                'password': config.password,
                'search_base': config.search_base,
                'port': config.port,
                'use_ssl': config.use_ssl
            }
            self._logger.info(f"已从数据库加载AD配置: server={config.server}")
            return self._config
        except Exception as e:
            self._logger.error(f"获取AD配置失败: {str(e)}")
            return None
        finally:
            db.close()

    async def ensure_connected(self) -> bool:
        """确保AD连接可用，并进行定期健康检查

        该方法在每次进行任何AD操作前都会被调用，以确保连接处于有效状态。
        它会执行以下操作：
        1. 定期进行健康检查，验证连接是否正常
        2. 如果连接失效，尝试重新连接
        3. 管理连接池，清理过期连接

        Returns:
            bool: 连接是否可用
        """
        try:
            current_time = time.time()

            # 定期健康检查，默认每60秒检查一次
            if current_time - self._last_health_check > self._health_check_interval:
                self._logger.debug("执行定期健康检查")
                if not await self._check_connection_health():
                    self._logger.warning("健康检查失败,尝试重新连接")
                    # 先清理失效连接，释放资源
                    self._cleanup_pool()
                    # 关闭当前连接，防止资源泄漏
                    if self._conn:
                        try:
                            self._conn.unbind()
                        except Exception as e:
                            self._logger.warning(f"关闭失效连接时出错: {str(e)}")
                        self._conn = None
                    # 重新建立连接
                    return await self.connect()
                # 更新最后健康检查时间
                self._last_health_check = current_time

            # 检查现有连接
            if self._conn and self._conn.bound:
                pool_key = self._get_connection_key()
                if pool_key in self._conn_pool:
                    self._conn_pool[pool_key]['last_used'] = current_time
                    return True

            # 需要重新连接
            return await self.connect()

        except Exception as e:
            self._metrics['connection_errors'] += 1
            self._logger.error(f"检查连接状态时出错: {str(e)}")
            return False

    async def _check_connection_health(self) -> bool:
        """检查连接健康状态

        该方法通过执行一个简单的LDAP查询来检查连接是否健康。
        它会捕获各种LDAP异常，并返回连接状态。

        Returns:
            bool: 连接是否健康
        """
        try:
            # 首先检查连接是否存在并已绑定
            if not self._conn or not self._conn.bound:
                self._logger.warning("连接不存在或未绑定")
                return False

            # 执行简单查询测试连接
            try:
                # 查询域对象，这是一个轻量级查询，不会对服务器造成负担
                success = self._conn.search(
                    search_base=self._config['search_base'],
                    search_filter='(objectClass=domain)',
                    search_scope=BASE,  # 只查询基础对象，不递归
                    attributes=['distinguishedName'],  # 只获取基本属性
                    time_limit=5  # 5秒超时，防止长时间阻塞
                )

                # 检查搜索是否成功
                if not success:
                    error_result = self._conn.result
                    error_desc = error_result.get('description', '')
                    error_msg = error_result.get('message', '')
                    self._logger.warning(f"健康检查搜索失败: {error_desc} - {error_msg}")
                    return False

                # 检查返回结果是否有效
                if not hasattr(self._conn, 'entries') or not self._conn.entries:
                    self._logger.warning("健康检查搜索返回空结果，可能是域配置错误")
                    return False

                self._logger.debug("连接健康检查通过")
                return True

            except ldap3.core.exceptions.LDAPSocketReceiveError:
                # 套接字接收错误通常意味着连接已断开
                self._logger.error("健康检查时发生套接字接收错误，连接可能已断开")
                return False
            except ldap3.core.exceptions.LDAPSocketSendError:
                # 套接字发送错误通常意味着网络问题
                self._logger.error("健康检查时发生套接字发送错误，连接可能已断开")
                return False
            except ldap3.core.exceptions.LDAPResponseTimeoutError:
                # 响应超时可能意味着服务器负载过高或网络拙塑
                self._logger.error("健康检查时发生响应超时，服务器可能不可用或负载过高")
                return False

        except Exception as e:
            # 捕获所有其他异常
            self._logger.error(f"健康检查失败: {str(e)}")
            return False

    async def _execute_with_retry(self, operation, *args, **kwargs):
        """通用的重试执行方法"""
        for attempt in range(self._max_retries):
            try:
                return await operation(*args, **kwargs)
            except Exception as e:
                self._metrics['errors'] += 1
                self._metrics['retry_count'] += 1

                if attempt < self._max_retries - 1:
                    retry_delay = self._retry_delay * (attempt + 1)  # 指数退避
                    self._logger.warning(
                        f"操作失败,{retry_delay}秒后重试 ({attempt + 1}/{self._max_retries}): {str(e)}"
                    )
                    await asyncio.sleep(retry_delay)
                else:
                    raise

    def _get_cache_key(self, operation: str, *args, **kwargs) -> str:
        """生成缓存键"""
        return f"ad:{operation}:{hash(str(args))}:{hash(str(kwargs))}"

    def _get_from_cache(self, cache_key: str):
        """从缓存获取数据"""
        data = self._cache.get(cache_key)
        if data is None:
            self._metrics['cache_misses'] += 1
            return None
        
        self._metrics['cache_hits'] += 1
        return data

    def _set_cache(self, cache_key: str, data):
        """设置缓存数据"""
        self._cache.set(cache_key, data, self._cache_ttl)

    # 清除所有AD相关缓存的方法
    async def _clear_cache(self):
        """清除所有AD相关缓存"""
        self._cache.clear_pattern("ad:*")
        self._logger.info("已清除所有AD相关缓存")
    
    async def get_metrics(self) -> dict:
        """获取性能指标"""
        return {
            **self._metrics,
            'cache_size': 'N/A',  # Redis中的缓存数量无法直接获取
            'pool_size': len(self._conn_pool),
            'last_health_check': self._last_health_check
        }

    async def connect(self, config=None) -> bool:
        """连接到AD服务器"""
        start_time = time.time()
        try:
            if config:
                self._config = config
            elif not self._config:
                self._config = self._get_config_from_db()
                if not self._config:
                    self._logger.error("无法获取AD配置")
                    return False

            # 获取连接池键值
            pool_key = self._get_connection_key()
            if not pool_key:
                self._logger.error("无法生成连接池键值")
                return False

            # 尝试从连接池获取现有连接
            existing_conn = self._get_connection_from_pool(pool_key)
            if existing_conn:
                self._conn = existing_conn
                self._last_connect_time = time.time()
                connect_time = time.time() - start_time
                self._logger.debug(f"复用连接成功，耗时: {connect_time:.2f}秒")
                return True

            # 清理过期连接
            self._cleanup_pool()  # 清理过期连接

            # 检查连接池大小
            if len(self._conn_pool) >= self._max_pool_size:
                self._logger.warning(f"连接池已满({self._max_pool_size})，尝试强制清理非活动连接")

                # 强制清理非活动连接
                self._cleanup_pool(force_cleanup=True)  # 强制清理

                # 如果仍然没有足够的空间，记录警告
                if len(self._conn_pool) >= self._max_pool_size:
                    self._logger.warning("即使强制清理后，连接池仍然已满，可能需要增加最大连接池大小")

            self._logger.info(f"正在连接AD服务器: {self._config['server']}")

            if not self._server:
                try:
                    # 从数据库配置中获取端口和SSL设置
                    port = self._config.get('port', 389)
                    use_ssl = self._config.get('use_ssl', False)
                    
                    self._server = Server(
                        self._config['server'], 
                        port=port,
                        use_ssl=use_ssl,
                        get_info=ALL
                    )
                except Exception as e:
                    self._logger.error(f"创建Server对象失败: {str(e)}")
                    return False

            # 重试连接逻辑
            for attempt in range(self._retry_count):
                try:
                    # 确保旧连接已关闭
                    if self._conn:
                        try:
                            self._conn.unbind()
                        except:
                            pass
                        self._conn = None

                    # 构造正确的用户名格式 - NTLM需要domain\username格式
                    if '@' in self._config['username']:
                        # 如果用户名是email格式，提取用户名部分
                        actual_username = self._config['username'].split('@')[0]
                        username = f"{self._config['domain']}\\{actual_username}"
                    else:
                        # 否则构造域名\用户名格式
                        username = f"{self._config['domain']}\\{self._config['username']}"
                    
                    self._conn = Connection(
                        self._server,
                        user=username,
                        password=self._config['password'],
                        authentication=NTLM,  # 改为NTLM认证
                        read_only=False,
                        auto_bind=False
                    )

                    if self._conn.bind():
                        # 添加到连接池
                        self._conn_pool[pool_key] = {
                            'connection': self._conn,
                            'last_used': time.time()
                        }
                        self._last_connect_time = time.time()
                        connect_time = time.time() - start_time
                        self._logger.info(f"AD服务器连接成功，耗时: {connect_time:.2f}秒")
                        return True
                    else:
                        error_msg = self._conn.result.get('description', '未知错误')
                        self._logger.error(f"AD服务器连接失败: {error_msg}")
                        if attempt < self._retry_count - 1:
                            self._logger.info(f"等待{self._retry_delay}秒后重试... (尝试 {attempt + 1}/{self._retry_count})")
                            await asyncio.sleep(self._retry_delay)
                except Exception as e:
                    self._logger.error(f"连接尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt < self._retry_count - 1:
                        self._logger.info(f"等待{self._retry_delay}秒后重试... (尝试 {attempt + 1}/{self._retry_count})")
                        await asyncio.sleep(self._retry_delay)

            connect_time = time.time() - start_time
            self._logger.error(f"连接AD服务器失败，已重试{self._retry_count}次，总耗时: {connect_time:.2f}秒")
            # 确保出错时连接被清理
            if self._conn:
                try:
                    self._conn.unbind()
                except:
                    pass
                self._conn = None
            return False

        except Exception as e:
            connect_time = time.time() - start_time
            self._logger.error(f"连接AD服务器时发生错误: {str(e)}, 耗时: {connect_time:.2f}秒")
            # 确保出错时连接被清理
            if self._conn:
                try:
                    self._conn.unbind()
                except:
                    pass
                self._conn = None
            return False

    async def test_connection(self, config: Dict[str, Any]) -> bool:
        """测试AD连接"""
        try:
            server = Server(
                config['server'],
                port=config['port'],
                use_ssl=config['use_ssl'],
                get_info=ALL
            )

            conn = Connection(
                server,
                user=f"{config['domain']}\\{config['username']}",
                password=config['password'],
                authentication=NTLM,
                auto_bind=True
            )

            # 尝试搜索以验证连接
            conn.search(
                config['search_base'],
                '(objectClass=*)',
                search_scope=SUBTREE,
                attributes=['*'],
                size_limit=1
            )

            conn.unbind()
            return True
        except Exception as e:
            logger.error(f"AD连接测试失败: {str(e)}")
            return False

    async def get_ou_tree(self) -> Optional[List[Dict]]:
        """获取OU树结构"""
        if not await self.ensure_connected():
            return []

        try:
            # 使用异步运行同步代码
            return await asyncio.to_thread(self._get_ou_tree)
        except Exception as e:
            logger.error(f"获取OU树失败: {str(e)}")
            return []

    def _get_ou_tree(self) -> List[Dict]:
        """实际的OU树取操作（同步）"""
        try:
            search_base = self._config['search_base']
            search_filter = '(objectClass=organizationalUnit)'
            attributes = ['name', 'distinguishedName', 'description']

            self._logger.info(f"开始搜索OU: base={search_base}, filter={search_filter}")

            success = self._conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=attributes
            )

            if not success:
                self._logger.error("搜索OU失败")
                return []

            ous = []
            for entry in self._conn.entries:
                ou = {
                    'name': entry.name.value if hasattr(entry, 'name') else '',
                    'dn': entry.distinguishedName.value,
                    'description': entry.description.value if hasattr(entry, 'description') else ''
                }
                ous.append(ou)

            self._logger.info(f"搜索到 {len(ous)} 个OU，现在开始构建OU树")

            tree = self._build_ou_tree(ous)
            self._logger.info(f"OU树构建完成，共有 {len(tree)} 个根级OU")

            return tree
        except Exception as e:
            self._logger.error(f"获取OU树失败: {str(e)}")
            return []

    def _build_ou_tree(self, ous: List[Dict]) -> List[Dict]:
        """构建OU树结构"""
        ou_dict = {ou['dn']: {**ou, 'children': []} for ou in ous}
        root_ous = []

        for dn, ou in ou_dict.items():
            parent_dn = self._get_parent_dn(dn)
            if parent_dn in ou_dict:
                ou_dict[parent_dn]['children'].append(ou)
            else:
                root_ous.append(ou)

        return root_ous

    def _get_parent_dn(self, dn: str) -> str:
        """��取父OU的DN"""
        parts = dn.split(',')
        return ','.join(parts[1:]) if len(parts) > 1 else None

    async def get_users(
        self,
        ou_dn: str,
        page: int = 1,
        page_size: int = 20,
        search: str = None,
        read_only: bool = False
    ) -> tuple[List[Dict], int]:
        """获取指定OU下的用户列表(分页)"""
        try:
            if not await self.ensure_connected():
                return [], 0

            # 使用异步运行同步代码
            return await asyncio.to_thread(self._get_users, ou_dn, page, page_size, search, read_only)

        except Exception as e:
            self._logger.error(f"获取用户列表时出错: {str(e)}")
            return [], 0

    def _get_users(self, ou_dn: str, page: int = 1, page_size: int = 20, search: str = None, read_only: bool = False) -> tuple[List[Dict], int]:
        """实际的（同步）"""
        try:
            # 构建搜索条件
            search_filter = '(&(objectClass=user)(objectCategory=person)'
            if search:
                search = search.lower()
                search_filter += f'(|(cn=*{search}*)(sAMAccountName=*{search}*)(mail=*{search}*)(department=*{search}*)(title=*{search}*))'
            search_filter += ')'

            # 记录搜索条件，但不记录详细数据
            self._logger.info(f"开始AD用户搜索: base={ou_dn}, filter={search_filter}, page={page}, page_size={page_size}")

            attributes = [
                'cn',
                'sAMAccountName',
                'userPrincipalName',
                'displayName',
                'mail',
                'department',
                'title',
                'userAccountControl',
                'memberOf',
                'primaryGroupID',
                'pwdLastSet',
                'msDS-UserPasswordExpiryTimeComputed',
                'accountExpires'
            ]

            # 使用Simple Paged Results控制获取所有条目
            all_entries = []
            cookie = None

            # 记录搜索开始时间
            start_time = time.time()

            while True:
                self._conn.search(
                    search_base=ou_dn,
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=attributes,
                    paged_size=1000,  # 每页1000条
                    paged_cookie=cookie
                )

                all_entries.extend(self._conn.entries)

                # 获取下一页的cookie
                cookie = self._conn.result['controls']['1.2.840.113556.1.4.319']['value']['cookie']
                if not cookie:
                    break

            # 记录搜索结果总数和耗时
            total_entries = len(all_entries)
            search_time = time.time() - start_time
            self._logger.info(f"AD查询返回: 共{total_entries}条结果, 耗时: {search_time:.2f}秒")

            # 计算当前页的起始和结束索引
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size

            # 获取当前页的条目
            page_entries = all_entries[start_idx:end_idx]
            self._logger.info(f"当前页({page})包含: {len(page_entries)}条结果")

            # 获取域SID（用于构建组SID）
            domain_sid = None if read_only else self._get_domain_sid()

            # 处理当前页的数据
            users = []

            # 记录处理开始时间
            process_start_time = time.time()

            for entry in page_entries:
                # 构建用户基本信息
                # 优先使用displayName，如果没有则使用cn，都没有则使用sAMAccountName
                display_name = None
                if hasattr(entry, 'displayName') and entry.displayName.value:
                    display_name = entry.displayName.value
                elif hasattr(entry, 'cn') and entry.cn.value:
                    display_name = entry.cn.value
                else:
                    display_name = entry.sAMAccountName.value

                # 计算密码过期信息
                password_expiry_info = self._calculate_password_expiry(entry)

                user = {
                    'username': entry.sAMAccountName.value,
                    'name': display_name,
                    'email': entry.mail.value if hasattr(entry, 'mail') else '',
                    'department': entry.department.value if hasattr(entry, 'department') else '',
                    'title': entry.title.value if hasattr(entry, 'title') else '',
                    'enabled': not bool(int(entry.userAccountControl.value) & 2),
                    'groups': [],
                    'dn': entry.entry_dn,
                    'password_expiry_date': password_expiry_info['expiry_date'],
                    'password_never_expires': password_expiry_info['never_expires'],
                    'days_until_expiry': password_expiry_info['days_until_expiry'],
                    'password_expired': password_expiry_info['expired']
                }

                # 只在非只读模式下处理组信息
                if not read_only:
                    groups = []
                    # 1. 处理memberOf属性中的组
                    if hasattr(entry, 'memberOf'):
                        for group_dn in entry.memberOf.values:
                            try:
                                group_success = self._conn.search(
                                    search_base=group_dn,
                                    search_filter='(objectClass=group)',
                                    search_scope=BASE,
                                    attributes=['sAMAccountName']
                                )
                                if group_success and self._conn.entries:
                                    group_name = self._conn.entries[0].sAMAccountName.value
                                    groups.append(group_name)
                            except Exception as e:
                                self._logger.error(f"处理组 {group_dn} 时出错: {str(e)}")

                    # 2. 处理主要组（Primary Group）
                    try:
                        if hasattr(entry, 'primaryGroupID') and domain_sid:
                            primary_group_rid = entry.primaryGroupID.value
                            primary_group_sid = f"{domain_sid}-{primary_group_rid}"

                            group_success = self._conn.search(
                                search_base=self._config['search_base'],
                                search_filter=f'(objectSid={primary_group_sid})',
                                search_scope=SUBTREE,
                                attributes=['sAMAccountName']
                            )

                            if group_success and self._conn.entries:
                                primary_group_name = self._conn.entries[0].sAMAccountName.value
                                if primary_group_name not in groups:
                                    groups.append(primary_group_name)
                    except Exception as e:
                        self._logger.error(f"处理用户 {entry.sAMAccountName.value} 的主要组时出错: {str(e)}")

                    user['groups'] = groups

                users.append(user)

            # 记录数据处理耗时
            process_time = time.time() - process_start_time
            self._logger.info(f"处理用户数据耗时: {process_time:.2f}秒")
            self._logger.info(f"获取用户列表完成: 共返回{len(users)}条结果, 总数={total_entries}")

            # 不再输出完整用户数据，只输出统计信息
            return users, total_entries

        except Exception as e:
            self._logger.error(f"获取用户列表时出错: {str(e)}")
            return [], 0

    def _get_domain_sid(self) -> Optional[str]:
        """获取域SID"""
        try:
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter='(objectClass=domain)',
                search_scope=BASE,
                attributes=['objectSid']
            )
            if success and self._conn.entries:
                return str(self._conn.entries[0].objectSid.value)
        except Exception as e:
            self._logger.error(f"获取域SID失败: {str(e)}")
        return None

    def _calculate_password_expiry(self, entry) -> Dict[str, Any]:
        """计算用户密码过期信息"""
        try:
            from datetime import datetime, timedelta
            
            # 检查用户账户控制标志
            user_account_control = int(entry.userAccountControl.value) if hasattr(entry, 'userAccountControl') else 0
            
            # 检查密码是否永不过期 (0x10000 = DONT_EXPIRE_PASSWORD)
            password_never_expires = bool(user_account_control & 0x10000)
            
            if password_never_expires:
                return {
                    'expiry_date': None,
                    'never_expires': True,
                    'days_until_expiry': None,
                    'expired': False
                }
            
            # 优先使用 msDS-UserPasswordExpiryTimeComputed 属性（如果可用）
            if hasattr(entry, 'msDS-UserPasswordExpiryTimeComputed') and entry['msDS-UserPasswordExpiryTimeComputed'].value:
                # 这是一个计算属性，直接包含过期时间
                computed_expiry = entry['msDS-UserPasswordExpiryTimeComputed'].value
                if computed_expiry and str(computed_expiry) != '9223372036854775807':  # 最大值表示永不过期
                    
                    # 检查返回值类型并相应处理
                    if isinstance(computed_expiry, datetime):
                        # 如果已经是datetime对象，直接使用
                        expiry_date = computed_expiry
                        current_time = datetime.now()
                        
                        days_until_expiry = (expiry_date - current_time).days
                        expired = expiry_date < current_time
                        
                        return {
                            'expiry_date': expiry_date.isoformat(),
                            'never_expires': False,
                            'days_until_expiry': days_until_expiry,
                            'expired': expired
                        }
                    else:
                        # 如果是数字，按原逻辑处理（AD时间戳）
                        try:
                            expiry_timestamp = int(computed_expiry)
                            if expiry_timestamp > 0:
                                # 转换为Python datetime
                                expiry_date = datetime(1601, 1, 1) + timedelta(microseconds=expiry_timestamp/10)
                                current_time = datetime.now()
                                
                                days_until_expiry = (expiry_date - current_time).days
                                expired = expiry_date < current_time
                                
                                return {
                                    'expiry_date': expiry_date.isoformat(),
                                    'never_expires': False,
                                    'days_until_expiry': days_until_expiry,
                                    'expired': expired
                                }
                        except (ValueError, TypeError) as e:
                            self._logger.warning(f"无法处理密码过期时间戳: {computed_expiry}, 错误: {str(e)}")
            
            # 如果没有计算属性，尝试使用 pwdLastSet 和域策略
            if hasattr(entry, 'pwdLastSet') and entry.pwdLastSet.value:
                try:
                    # 安全地转换 pwdLastSet 值
                    pwd_last_set_value = entry.pwdLastSet.value
                    if isinstance(pwd_last_set_value, datetime):
                        # 如果是datetime对象，转换为AD时间戳
                        from datetime import timezone
                        epoch = datetime(1601, 1, 1, tzinfo=timezone.utc)
                        # 确保两个datetime都有时区信息
                        if pwd_last_set_value.tzinfo is None:
                            pwd_last_set_value = pwd_last_set_value.replace(tzinfo=timezone.utc)
                        delta = pwd_last_set_value - epoch
                        pwd_last_set = int(delta.total_seconds() * 10**7)
                    else:
                        pwd_last_set = int(pwd_last_set_value)
                except (ValueError, TypeError) as e:
                    self._logger.warning(f"无法处理pwdLastSet值: {entry.pwdLastSet.value}, 错误: {str(e)}")
                    return {
                        'expiry_date': None,
                        'never_expires': False,
                        'days_until_expiry': None,
                        'expired': False
                    }
                
                # pwdLastSet = 0 表示用户必须在下次登录时更改密码
                if pwd_last_set == 0:
                    return {
                        'expiry_date': None,
                        'never_expires': False,
                        'days_until_expiry': 0,
                        'expired': True
                    }
                
                # 获取域密码策略（简化版本，使用默认值）
                # 在实际环境中，应该查询域策略以获取准确的maxPwdAge
                try:
                    # 尝试查询域策略
                    domain_policy = self._get_domain_password_policy()
                    max_pwd_age_days = domain_policy.get('max_pwd_age_days', 90)  # 默认90天
                except:
                    max_pwd_age_days = 90  # 默认90天
                
                # 计算密码设置时间
                pwd_set_time = datetime(1601, 1, 1) + timedelta(microseconds=pwd_last_set/10)
                
                # 计算过期时间
                expiry_date = pwd_set_time + timedelta(days=max_pwd_age_days)
                current_time = datetime.now()
                
                days_until_expiry = (expiry_date - current_time).days
                expired = expiry_date < current_time
                
                return {
                    'expiry_date': expiry_date.isoformat(),
                    'never_expires': False,
                    'days_until_expiry': days_until_expiry,
                    'expired': expired
                }
            
            # 如果无法确定密码过期信息
            return {
                'expiry_date': None,
                'never_expires': False,
                'days_until_expiry': None,
                'expired': False
            }
            
        except Exception as e:
            self._logger.error(f"计算密码过期信息时出错: {str(e)}")
            return {
                'expiry_date': None,
                'never_expires': False,
                'days_until_expiry': None,
                'expired': False
            }

    def _get_domain_password_policy(self) -> Dict[str, Any]:
        """获取域密码策略"""
        try:
            # 查询域对象获取密码策略
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter='(objectClass=domain)',
                search_scope=BASE,
                attributes=['maxPwdAge', 'minPwdAge', 'minPwdLength', 'pwdHistoryLength']
            )
            
            if success and self._conn.entries:
                domain_entry = self._conn.entries[0]
                
                # maxPwdAge 是以100纳秒为单位的负数
                max_pwd_age = 90  # 默认值
                if hasattr(domain_entry, 'maxPwdAge') and domain_entry.maxPwdAge.value:
                    max_pwd_age_raw = int(domain_entry.maxPwdAge.value)
                    if max_pwd_age_raw < 0:
                        # 转换为天数
                        max_pwd_age = abs(max_pwd_age_raw) / (10**7) / 86400
                
                return {
                    'max_pwd_age_days': int(max_pwd_age),
                    'min_pwd_length': int(domain_entry.minPwdLength.value) if hasattr(domain_entry, 'minPwdLength') else 0,
                    'pwd_history_length': int(domain_entry.pwdHistoryLength.value) if hasattr(domain_entry, 'pwdHistoryLength') else 0
                }
            
        except Exception as e:
            self._logger.error(f"获取域密码策略失败: {str(e)}")
        
        # 返回默认策略
        return {
            'max_pwd_age_days': 90,
            'min_pwd_length': 8,
            'pwd_history_length': 12
        }

    async def add_user_to_groups(self, username: str, group_dns: List[str]) -> tuple[bool, bool]:
        """将用户添加到指定的组中

        Args:
            username: 用户名（sAMAccountName）
            group_dns: 组DN列表

        Returns:
            元组 (成功标志, 实际添加标志)
        """
        try:
            if not await self.ensure_connected():
                return False, False

            # 获取用户DN
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'memberOf']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False, False

            user_entry = self._conn.entries[0]
            user_dn = user_entry.distinguishedName.value
            self._logger.info(f"找到用户DN: {user_dn}")

            # 一次性获取用户已经所属的所有组
            current_memberships = []
            if hasattr(user_entry, 'memberOf'):
                current_memberships = [dn.lower() for dn in user_entry.memberOf.values]
                self._logger.debug(f"用户 {username} 当前属于 {len(current_memberships)} 个组")

            # 首先过滤掉用户已经所属的组
            groups_to_verify = []
            for group_dn in group_dns:
                if group_dn.lower() in current_memberships:
                    self._logger.debug(f"用户 {username} 已在组 {group_dn} 中，跳过添加")
                else:
                    groups_to_verify.append(group_dn)

            if not groups_to_verify:
                self._logger.debug(f"用户 {username} 已在所有指定的组中，无需添加")
                return True, False

            # 构建批量查询的过滤器，一次性验证所有组是否存在
            if len(groups_to_verify) == 1:
                escaped_dn = self.escape_dn_for_filter(groups_to_verify[0])
                search_filter = f'(distinguishedName={escaped_dn})'
            else:
                search_filter = '(|' + ''.join([f'(distinguishedName={self.escape_dn_for_filter(dn)})' for dn in groups_to_verify]) + ')'

            self._logger.debug(f"批量验证组过滤器: {search_filter}")

            # 批量查询所有组
            valid_groups = {}
            try:
                success = self._conn.search(
                    search_base=self._config['search_base'],
                    search_filter=f'(&(objectClass=group){search_filter})',
                    search_scope=SUBTREE,
                    attributes=['distinguishedName', 'cn']
                )

                if success and self._conn.entries:
                    for entry in self._conn.entries:
                        dn = entry.distinguishedName.value
                        name = entry.cn.value if hasattr(entry, 'cn') else "未知组"
                        valid_groups[dn] = name
                        self._logger.debug(f"验证组 {dn} 存在: {name}")
            except Exception as e:
                self._logger.error(f"批量验证组时出错: {str(e)}")
                # 如果批量查询失败，我们仍然可以继续尝试单个添加

            # 确定最终要添加的组
            groups_to_add = []
            for group_dn in groups_to_verify:
                if group_dn in valid_groups:
                    groups_to_add.append((group_dn, valid_groups[group_dn]))
                else:
                    self._logger.warning(f"无效的组DN: {group_dn}")

            if not groups_to_add:
                self._logger.debug(f"没有有效的组需要添加用户 {username}")
                return True, False

            self._logger.debug(f"需要将用户 {username} 添加到 {len(groups_to_add)} 个组中")

            # 逐个添加用户到组
            actually_added = False
            for group_dn, group_name in groups_to_add:
                try:
                    success = self._conn.modify(
                        group_dn,
                        {'member': [(MODIFY_ADD, [user_dn])]}
                    )
                    if not success:
                        self._logger.error(f"添加用户到组失败: {group_name} ({group_dn}), 错误: {self._conn.result}")
                        return False, actually_added
                    else:
                        self._logger.debug(f"成功将用户 {username} 添加到组 {group_name}")
                        actually_added = True
                except Exception as e:
                    self._logger.error(f"添加用户到组时出错: {group_name} ({group_dn}), 错误: {str(e)}")
                    return False, actually_added

            return True, actually_added

        except Exception as e:
            self._logger.error(f"添加用户到组时出错: {str(e)}")
            return False, False

    async def remove_user_from_groups(self, username: str, group_dns: List[str]) -> bool:
        """从指定的组中移除用户"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            user_dn = self._conn.entries[0].distinguishedName.value
            self._logger.info(f"找到用户DN: {user_dn}")

            # 使用正确的参数名称
            success = self._conn.extend.microsoft.remove_members_from_groups(
                members=[user_dn],
                groups=group_dns
            )

            if not success:
                self._logger.error(f"从组中移除用户失败: {self._conn.result}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"组中移除用户时出错: {str(e)}")
            return False

    async def get_groups(self, ou_dn: str, page: int = 1, page_size: int = 20, search: str = None) -> tuple[List[Dict], int]:
        """获取指定OU下的组列表(分页)"""
        try:
            if not await self.ensure_connected():
                return [], 0

            # 使用异步运行同步代码
            return await asyncio.to_thread(self._get_groups, ou_dn, page, page_size, search)

        except Exception as e:
            self._logger.error(f"获取组列表时出错: {str(e)}")
            return [], 0

    def _get_groups(self, ou_dn: str, page: int = 1, page_size: int = 20, search: str = None) -> tuple[List[Dict], int]:
        """实际的组获取操作（同步）"""
        try:
            # 构建更精确的搜索过滤器
            search_filter = '(objectClass=group)'
            if search:
                search = search.lower().strip()
                # 使用更精确的搜索条件
                search_filter = f'(&{search_filter}(|(sAMAccountName=*{search}*)(cn=*{search}*)))'

            self._logger.info(f"开始搜索组: OU={ou_dn}, 过滤器={search_filter}")

            attributes = [
                'sAMAccountName',
                'displayName',
                'description',
                'member',
                'distinguishedName',
                'groupType'
            ]

            search_base = ou_dn if ou_dn else self._config['search_base']

            # 使用paged search
            cookie = None
            all_entries = []
            total_entries = 0

            while True:
                self._conn.search(
                    search_base=search_base,
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=attributes,
                    paged_size=1000,
                    paged_cookie=cookie
                )

                all_entries.extend(self._conn.entries)
                total_entries = len(all_entries)

                cookie = self._conn.result['controls']['1.2.840.113556.1.4.319']['value']['cookie']
                if not cookie:
                    break

            # 计算分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_entries = all_entries[start_idx:end_idx]

            # 处理返回数据
            groups = []
            for entry in page_entries:
                try:
                    group_type = entry.groupType.value if hasattr(entry, 'groupType') else 0
                    is_builtin = bool(group_type & 0x00000001)
                    is_security = bool(group_type & 0x80000000)

                    group = {
                        'name': entry.sAMAccountName.value,
                        'displayName': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                        'description': entry.description.value if hasattr(entry, 'description') else '',
                        'memberCount': len(entry.member) if hasattr(entry, 'member') else 0,
                        'dn': entry.distinguishedName.value,
                        'isBuiltin': is_builtin,
                        'isSecurity': is_security
                    }

                    groups.append(group)

                except Exception as e:
                    self._logger.error(f"处理组条目时出错: {str(e)}")
                    continue

            return groups, total_entries

        except Exception as e:
            self._logger.error(f"获取组列表失败: {str(e)}", exc_info=True)
            return [], 0

    async def create_user(self, user_data: Dict[str, Any]) -> bool:
        """创建AD用户"""
        try:
            if not await self.ensure_connected():
                return False

            # 构建用户DN
            cn = user_data['name']
            if 'ou_dn' not in user_data:
                self._logger.error("缺少必要参数: ou_dn")
                raise ValueError("必须指定组织单位(OU)")
            user_dn = f"CN={cn},{user_data['ou_dn']}"

            self._logger.info(f"开始创建用户: {user_dn}")

            # 检查密码复杂度
            password = user_data.get('password')
            is_valid, error_msg = self._check_password_complexity(password)
            if not is_valid:
                self._logger.error(f"密码复杂度检查失败: {error_msg}")
                raise ValueError(error_msg)

            # 准备用户属性
            user_attrs = {
                'objectClass': ['top', 'person', 'organizationalPerson', 'user'],
                'cn': cn,
                'displayName': cn,
                'sAMAccountName': user_data['username'],
                'userPrincipalName': f"{user_data['username']}@{self._config['domain']}",
                'userAccountControl': '544'  # 启用账户但需要修改密码
            }

            # 智能处理姓名
            if cn:
                # 检测是否是中文名
                if any('\u4e00' <= char <= '\u9fff' for char in cn):
                    # 中文名：第一个字作为姓，其余作为名
                    if len(cn) > 1:
                        user_attrs['sn'] = cn[0]
                        user_attrs['givenName'] = cn[1:]
                    else:
                        user_attrs['sn'] = cn
                else:
                    # 英文名或其他：尝试按空格分割
                    name_parts = cn.split(' ', 1)
                    if len(name_parts) > 1:
                        user_attrs['givenName'] = name_parts[0]  # 名在前
                        user_attrs['sn'] = name_parts[1]  # 姓在后
                    else:
                        user_attrs['sn'] = cn  # 只有一个部分时，全部作为姓

            # 添加可选属性
            if user_data.get('email'):
                user_attrs['mail'] = user_data['email']
            if user_data.get('department'):
                user_attrs['department'] = user_data['department']
            if user_data.get('title'):
                user_attrs['title'] = user_data['title']

            self._logger.debug(f"用户属性: {user_attrs}")

            # 创建用户
            success = self._conn.add(user_dn, attributes=user_attrs)

            if success:
                self._logger.info("用户基本信息创建成功，开始设置密码")
                try:
                    # 确保密码是字符串类型
                    if not isinstance(password, str):
                        password = str(password)

                    # 使用 SSL/TLS 连接
                    if not self._conn.server.ssl:
                        self._logger.warning("当前未使用 SSL/TLS 连接，尝试重新建立安全连接")
                        try:
                            self._conn.unbind()
                            self._server = Server(
                                self._config['server'],
                                use_ssl=True,
                                get_info=ALL
                            )
                            self._conn = Connection(
                                self._server,
                                user=self._config['username'],
                                password=self._config['password'],
                                authentication=SIMPLE,
                                auto_bind=True
                            )
                        except Exception as e:
                            raise Exception(f"建立 SSL/TLS 连接失败: {str(e)}")

                    # 尝试使用不同的密码修改方法
                    methods = [
                        # 方法1: Microsoft 扩展
                        lambda: self._conn.extend.microsoft.modify_password(user_dn, password),
                        # 方法2: 直接修改 unicodePwd
                        lambda: self._conn.modify(user_dn, {
                            'unicodePwd': [(MODIFY_REPLACE, [f'"{password}"'.encode('utf-16-le')])]
                        }),
                        # 方法3: 使用 LDAP_SERVER_PASSWORD_POLICIES_OID 控制
                        lambda: self._conn.modify(user_dn, {
                            'userPassword': [(MODIFY_REPLACE, [password])],
                            'pwdLastSet': [(MODIFY_REPLACE, ['0'])]
                        })
                    ]

                    success = False
                    last_error = None
                    for i, method in enumerate(methods, 1):
                        try:
                            self._logger.debug(f"尝试密码设置方法 {i}")
                            if method():
                                self._logger.info(f"使用方法 {i} 成功设置密码")
                                success = True
                                break
                            else:
                                error_msg = self._conn.result.get('message', '未知错误')
                                self._logger.warning(f"方法 {i} 失败: {error_msg}")
                                last_error = error_msg
                        except Exception as e:
                            self._logger.warning(f"方法 {i} 出错: {str(e)}")
                            last_error = str(e)
                            continue

                    if not success:
                        raise Exception(f"所有密码设置方法都失败: {last_error}")

                    self._logger.info(f"用户创建完成: {user_dn}")
                    return True

                except Exception as e:
                    # 如果设置密码失败，删除已创建的用户
                    self._logger.error(f"密码设置失败，正在删除用户: {str(e)}")
                    try:
                        delete_success = self._conn.delete(user_dn)
                        if delete_success:
                            self._logger.info(f"成功删除失败的用户: {user_dn}")
                        else:
                            self._logger.error(f"删除失败的用户时出错: {self._conn.result}")
                    except Exception as delete_error:
                        self._logger.error(f"删除失败的用户时出错: {str(delete_error)}")
                    raise ValueError(f"用户创建失败: 密码设置错误 - {str(e)}")
            else:
                error_result = self._conn.result
                if error_result.get('result') == 68:  # entryAlreadyExists
                    raise ValueError(f"用户已存在: {user_data['username']}")
                self._logger.error(f"创建用户失败: {error_result}")
                return False

        except ValueError as e:
            self._logger.error(f"创建用户参数错误: {str(e)}")
            raise
        except Exception as e:
            self._logger.error(f"创建用户时发生错误: {str(e)}")
            return False

    def _check_password_complexity(self, password: str) -> tuple[bool, str]:
        """检查密码复杂性

        AD密码策略通常要求：
        1. 最小长度8个字符
        2. 必须包含以下四类中的至少三类：
           - 大写字母 (A-Z)
           - 小写字母 (a-z)
           - 数字 (0-9)
           - 特殊字符 (!@#$%^&*()_+-=[]{}|;:,.<>?)
        3. 不能包含用户名或显示名称
        4. 不能是最近使用过的密码
        """
        try:
            if not password or len(password) < 8:
                error_msg = "密码长度必须至少为8个字符"
                self._logger.error(error_msg)
                return False, error_msg

            # 检查密码强度
            criteria_met = 0
            if any(c.isupper() for c in password):
                criteria_met += 1
            if any(c.islower() for c in password):
                criteria_met += 1
            if any(c.isdigit() for c in password):
                criteria_met += 1
            if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
                criteria_met += 1

            if criteria_met < 3:
                error_msg = "密码必须包含大写字母、小写字母、数字和特殊字符中的至少三种"
                self._logger.error(error_msg)
                return False, error_msg

            # 检查常见的弱密码模式
            common_patterns = [
                r'password',
                r'123456',
                r'qwerty',
                r'admin',
                r'welcome'
            ]

            for pattern in common_patterns:
                if pattern in password.lower():
                    error_msg = f"密码不能包含常见的弱密码模式: {pattern}"
                    self._logger.error(error_msg)
                    return False, error_msg

            # 检查连续字符
            for i in range(len(password) - 2):
                if (ord(password[i+1]) == ord(password[i]) + 1 and
                    ord(password[i+2]) == ord(password[i]) + 2):
                    error_msg = "密码不能包含连续的字符序列"
                    self._logger.error(error_msg)
                    return False, error_msg

            # 检查重复字符
            for i in range(len(password) - 2):
                if password[i] == password[i+1] == password[i+2]:
                    error_msg = "密码不能包含连续重复的字符"
                    self._logger.error(error_msg)
                    return False, error_msg

            return True, ""

        except Exception as e:
            error_msg = f"检查密码复杂度时出错: {str(e)}"
            self._logger.error(error_msg)
            return False, error_msg

    async def get_user_groups(self, username: str) -> List[str]:
        """获取用户所属的组"""
        try:
            if not await self.ensure_connected():
                return []

            # 使用sAMAccountName搜索用户
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['memberOf', 'primaryGroupID']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return []

            entry = self._conn.entries[0]
            group_dns = []

            # 获取直接成员关系
            if hasattr(entry, 'memberOf'):
                group_dns.extend(entry.memberOf.values)

            # 获取主要组
            try:
                if hasattr(entry, 'primaryGroupID'):
                    primary_group_rid = int(entry.primaryGroupID.value)
                    domain_sid = self._get_domain_sid()
                    if domain_sid:
                        primary_group_sid = f"{domain_sid}-{primary_group_rid}"
                        group_success = self._conn.search(
                            search_base=self._config['search_base'],
                            search_filter=f'(objectSid={primary_group_sid})',
                            search_scope=SUBTREE,
                            attributes=['distinguishedName']
                        )
                        if group_success and self._conn.entries:
                            primary_group_dn = self._conn.entries[0].distinguishedName.value
                            if primary_group_dn not in group_dns:
                                group_dns.append(primary_group_dn)
            except Exception as e:
                self._logger.error(f"获取主要组时出错: {str(e)}")

            # 直接返回组DN列表,不再转换为组名
            return group_dns

        except Exception as e:
            self._logger.error(f"获取用户组失败: {str(e)}")
            return []

    async def update_user(self, username: str, attributes: Dict[str, Any]) -> bool:
        """更新用户属性"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN
            user_dn = await self._get_user_dn(username)
            if not user_dn:
                self._logger.error(f"未找到用户: {username}")
                return False

            self._logger.debug(f"找到用户DN: {user_dn}")
            self._logger.debug(f"准备更新的属性: {attributes}")

            # 分离密码和其他属性
            password = attributes.pop('password', None)
            changes = {}

            # AD属性映射
            ad_attribute_map = {
                'name': 'displayName',
                'email': 'mail',
                'department': 'department',
                'title': 'title',
                'enabled': 'userAccountControl'
            }

            # 处理每个属性
            for attr, value in attributes.items():
                if attr in ad_attribute_map:
                    ad_attr = ad_attribute_map[attr]
                    self._logger.debug(f"处理属性 {attr} -> {ad_attr}: {value}")

                    # 如果值为空字符串或None，则使用MODIFY_DELETE操作
                    if value is None or value == '':
                        self._logger.debug(f"属性 {ad_attr} 为空，将删除该属性")
                        changes[ad_attr] = [(MODIFY_DELETE, [])]
                    else:
                        changes[ad_attr] = [(MODIFY_REPLACE, [value])]

            # 如果有属性需要更新
            if changes:
                self._logger.debug(f"更新用户属性: {changes}")
                success = self._conn.modify(user_dn, changes)
                if not success:
                    error_msg = self._conn.result.get('description', '未知错误')
                    self._logger.error(f"更新用户属性失败: {error_msg}")
                    raise Exception(f"更新用户属性失败: {error_msg}")

            # 如果需要更新密码
            if password:
                try:
                    # 确保密码是字符串类型
                    if not isinstance(password, str):
                        password = str(password)

                    # 检查密码复杂度
                    is_valid, error_msg = self._check_password_complexity(password)
                    if not is_valid:
                        raise Exception(error_msg)

                    # 使用 SSL/TLS 连接
                    if not self._conn.server.ssl:
                        self._logger.warning("当前未使用 SSL/TLS 连接，尝试重新建立安全连接")
                        try:
                            self._conn.unbind()
                            self._server = Server(
                                self._config['server'],
                                use_ssl=True,
                                get_info=ALL
                            )
                            self._conn = Connection(
                                self._server,
                                user=self._config['username'],
                                password=self._config['password'],
                                authentication=SIMPLE,
                                auto_bind=True
                            )
                        except Exception as e:
                            raise Exception(f"建立 SSL/TLS 连接失败: {str(e)}")

                    # 尝试使用不同的密码修改方法
                    methods = [
                        # 方法1: Microsoft 扩展
                        lambda: self._conn.extend.microsoft.modify_password(user_dn, password),
                        # 方法2: 直接修改 unicodePwd
                        lambda: self._conn.modify(user_dn, {
                            'unicodePwd': [(MODIFY_REPLACE, [f'"{password}"'.encode('utf-16-le')])]
                        }),
                        # 方法3: 使用 LDAP_SERVER_PASSWORD_POLICIES_OID 控制
                        lambda: self._conn.modify(user_dn, {
                            'userPassword': [(MODIFY_REPLACE, [password])],
                            'pwdLastSet': [(MODIFY_REPLACE, ['0'])]
                        })
                    ]

                    success = False
                    last_error = None
                    for i, method in enumerate(methods, 1):
                        try:
                            self._logger.debug(f"尝试密码修改方法 {i}")
                            if method():
                                self._logger.info(f"使用方法 {i} 成功修改密码")
                                success = True
                                break
                            else:
                                error_msg = self._conn.result.get('message', '未知错误')
                                self._logger.warning(f"方法 {i} 失败: {error_msg}")
                                last_error = error_msg
                        except Exception as e:
                            self._logger.warning(f"方法 {i} 出错: {str(e)}")
                            last_error = str(e)
                            continue

                    if not success:
                        raise Exception(f"所有密码修改方法都失败: {last_error}")

                except Exception as e:
                    raise Exception(f"修改密码失败: {str(e)}")

            return True

        except Exception as e:
            self._logger.error(f"更新用户属性失败: {str(e)}")
            raise Exception(str(e))

    async def get_user(self, username: str, no_cache: bool = False) -> Optional[Dict]:
        """获取单个用户详细信息"""
        try:
            # 确保连接可用
            if not await self.ensure_connected():
                self._logger.error("无法获取用户信息: AD连接失败")
                return None
                
            # 生成缓存键并从缓存中获取
            cache_key = self._get_cache_key("get_user", username)
            
            # 如果未设置no_cache参数，尝试从缓存获取
            if not no_cache:
                cached_data = self._get_from_cache(cache_key)
                if cached_data:
                    self._logger.debug(f"从缓存获取用户信息: {username}")
                    return cached_data
            
            self._metrics['queries'] += 1
            
            # 获取用户DN
            user_dn = await self._get_user_dn(username)
            if not user_dn:
                self._logger.error(f"未找到用户: {username}")
                return None
                
            # 从AD加载用户详细信息
            self._conn.search(
                search_base=user_dn,
                search_filter='(objectClass=*)',
                search_scope=BASE,
                attributes=[
                    'displayName', 'cn', 'sAMAccountName', 'mail', 
                    'department', 'title', 'userAccountControl',
                    'pwdLastSet', 'msDS-UserPasswordExpiryTimeComputed', 'accountExpires'
                ]
            )
            
            # 检查搜索结果
            if not self._conn.entries or len(self._conn.entries) == 0:
                self._logger.error(f"在AD中找到用户DN但未能加载详细信息: {username}")
                return None
                
            # 获取用户对象
            user_obj = self._conn.entries[0]
            
            # 处理用户数据 - 转换为字典
            user_data = {}
            
            # 基本信息
            user_data['username'] = username
            user_data['dn'] = user_dn
            
            # 从LDAP属性获取信息
            if hasattr(user_obj, 'displayName'):
                user_data['name'] = user_obj.displayName.value
            elif hasattr(user_obj, 'cn'):
                user_data['name'] = user_obj.cn.value
            else:
                user_data['name'] = username
                
            # 获取邮箱信息
            if hasattr(user_obj, 'mail'):
                user_data['email'] = user_obj.mail.value
            else:
                user_data['email'] = ""
                
            # 获取部门信息
            if hasattr(user_obj, 'department'):
                user_data['department'] = user_obj.department.value
            else:
                user_data['department'] = ""
                
            # 获取职位信息
            if hasattr(user_obj, 'title'):
                user_data['title'] = user_obj.title.value
            else:
                user_data['title'] = ""
                
            # 判断用户是否已启用
            if hasattr(user_obj, 'userAccountControl'):
                # 0x2 是禁用标志
                user_data['enabled'] = (int(user_obj.userAccountControl.value) & 2) == 0
            else:
                user_data['enabled'] = True

            # 计算密码过期信息
            password_expiry_info = self._calculate_password_expiry(user_obj)
            user_data.update({
                'password_expiry_date': password_expiry_info['expiry_date'],
                'password_never_expires': password_expiry_info['never_expires'],
                'days_until_expiry': password_expiry_info['days_until_expiry'],
                'password_expired': password_expiry_info['expired']
            })
                
            # 缓存用户数据，但仅当no_cache为False时
            if not no_cache:
                self._set_cache(cache_key, user_data)
                
            return user_data

        except Exception as e:
            self._logger.error(f"获取用户信息失败: {str(e)}")
            return None

    async def get_users_batch(self, usernames: List[str], no_cache: bool = False) -> Dict[str, Optional[Dict]]:
        """批量获取多个用户的信息

        Args:
            usernames: 要查询的用户名列表
            no_cache: 是否禁用缓存，True表示不使用缓存

        Returns:
            字典，键为用户名，值为用户信息或None（如果用户不存在）
        """
        try:
            if not await self.ensure_connected() or not usernames:
                return {}

            # 构建OR搜索过滤器，一次查询所有用户
            username_filters = [f"(sAMAccountName={username})" for username in usernames]
            search_filter = f"(&(objectClass=user)(objectCategory=person)(|{''.join(username_filters)}))"

            self._logger.info(f"批量查询 {len(usernames)} 个用户")

            # 执行搜索
            self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=[
                    'displayName',
                    'cn',
                    'sAMAccountName',
                    'userPrincipalName',
                    'mail',
                    'department',
                    'title',
                    'userAccountControl',
                    'distinguishedName',
                    'pwdLastSet',
                    'msDS-UserPasswordExpiryTimeComputed',
                    'accountExpires'
                ]
            )

            # 构建结果字典，先用None填充所有用户
            result = {username: None for username in usernames}

            # 填充找到的用户信息
            for entry in self._conn.entries:
                if hasattr(entry, 'sAMAccountName'):
                    username = entry.sAMAccountName.value

                    # 如果这个用户在我们要查询的列表中
                    if username in result:
                        # 优先使用displayName，如果没有则使用cn，都没有则使用sAMAccountName
                        display_name = None
                        if hasattr(entry, 'displayName') and entry.displayName.value:
                            display_name = entry.displayName.value
                        elif hasattr(entry, 'cn') and entry.cn.value:
                            display_name = entry.cn.value
                        else:
                            display_name = username

                        # 计算密码过期信息
                        password_expiry_info = self._calculate_password_expiry(entry)

                        result[username] = {
                            'username': username,
                            'name': display_name,
                            'email': entry.mail.value if hasattr(entry, 'mail') else '',
                            'department': entry.department.value if hasattr(entry, 'department') else '',
                            'title': entry.title.value if hasattr(entry, 'title') else '',
                            'enabled': not bool(int(entry.userAccountControl.value) & 2),
                            'dn': entry.distinguishedName.value,
                            'password_expiry_date': password_expiry_info['expiry_date'],
                            'password_never_expires': password_expiry_info['never_expires'],
                            'days_until_expiry': password_expiry_info['days_until_expiry'],
                            'password_expired': password_expiry_info['expired']
                        }

            # 记录未找到的用户
            not_found = [username for username, info in result.items() if info is None]
            if not_found:
                self._logger.info(f"未找到以下用户: {', '.join(not_found)}")

            return result

        except Exception as e:
            self._logger.error(f"批量获取用户信息时出错: {str(e)}")
            return {}

    async def toggle_user_status(self, username: str, force_enable: bool = None) -> bool:
        """切换用户状态（启用/禁用）

        Args:
            username: 用户名
            force_enable: 如果提供，则强制设置为指定状态（True=启用，False=禁用）；
                        如果不提供，则切换当前状态

        Returns:
            操作是否成功
        """
        try:
            # 检查是否是特殊用户名，这些用户可能是系统账户或受保护账户
            protected_users = ['Administrator', 'Guest', 'krbtgt', 'admin']
            if username.lower() in [u.lower() for u in protected_users]:
                self._logger.warning(f"尝试修改受保护的系统账户: {username}")
                return False

            if not await self.ensure_connected():
                return False

            # 获取用户信息
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'userAccountControl', 'objectSid']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            user = self._conn.entries[0]
            user_dn = user.distinguishedName.value
            current_uac = int(user.userAccountControl.value)

            # 检查是否是系统账户
            # 系统账户通常有特殊的UAC标志
            is_system_account = bool(current_uac & 0x00000800)  # SERVER_TRUST_ACCOUNT
            is_system_account |= bool(current_uac & 0x00001000)  # WORKSTATION_TRUST_ACCOUNT
            is_system_account |= bool(current_uac & 0x00002000)  # INTERDOMAIN_TRUST_ACCOUNT

            if is_system_account:
                self._logger.warning(f"尝试修改系统账户: {username}, UAC: {current_uac}")
                return False

            # ACCOUNTDISABLE = 2
            is_disabled = bool(current_uac & 2)

            # 确定目标状态
            if force_enable is not None:
                # 如果指定了force_enable，则使用指定的状态
                should_disable = not force_enable
            else:
                # 否则切换当前状态
                should_disable = not is_disabled

            # 如果当前状态已经是目标状态，则不需要修改
            if is_disabled == should_disable:
                self._logger.info(f"用户 {username} 已经是{'禁用' if is_disabled else '启用'}状态，无需修改")
                return True

            # 保留其他UAC标志，只修改ACCOUNTDISABLE标志
            new_uac = current_uac
            if should_disable:
                new_uac |= 2  # 设置ACCOUNTDISABLE标志
            else:
                new_uac &= ~2  # 清除ACCOUNTDISABLE标志

            # 设置状态
            modify_attrs = {
                'userAccountControl': [(MODIFY_REPLACE, [new_uac])]
            }

            self._logger.debug(f"准备修改用户 {username} 的UAC: {current_uac} -> {new_uac}")
            success = self._conn.modify(user_dn, modify_attrs)

            if not success:
                result = self._conn.result
                error_code = result.get('result')
                error_desc = result.get('description', '')
                error_msg = result.get('message', '')

                self._logger.error(f"修改用户状态失败: {username}, 错误代码: {error_code}, 描述: {error_desc}, 消息: {error_msg}")

                # 如果是权限问题，返回更具体的错误信息
                if error_code == 53 and 'unwillingToPerform' in error_desc:  # unwillingToPerform
                    self._logger.error(f"服务器拒绝执行操作，可能是系统保护的账户或权限不足: {username}")
                    return False

                return False

            self._logger.info(f"用户状态已切换: {username}, 当前状态: {'禁用' if should_disable else '启用'}")
            
            # 清除相关缓存
            try:
                # 清除用户详细信息缓存
                user_cache_key = self._get_cache_key("get_user", username)
                self._cache.delete(user_cache_key)
                
                # 清除用户批量查询缓存
                batch_cache_pattern = f"ad:get_users_batch:*{username}*"
                self._cache.clear_pattern(batch_cache_pattern)
                
                self._logger.debug(f"已清除用户 {username} 的相关缓存")
            except Exception as cache_error:
                self._logger.warning(f"清除用户缓存失败: {str(cache_error)}")
            
            return True

        except Exception as e:
            self._logger.error(f"切换用户状态时出错: {str(e)}")
            return False

    async def toggle_users_status_batch(self, usernames: List[str], disable: bool = True) -> Dict[str, bool]:
        """批量修改多个用户的状态（启用/禁用）

        Args:
            usernames: 要修改状态的用户名列表
            disable: True表示禁用用户，False表示启用用户

        Returns:
            字典，键为用户名，值为操作是否成功
        """
        result = {}

        try:
            if not await self.ensure_connected() or not usernames:
                return {}

            self._logger.info(f"批量{'禁用' if disable else '启用'} {len(usernames)} 个用户")

            # 先批量查询所有用户获取DN和当前状态
            users_info = await self.get_users_batch(usernames)

            # 对每个用户执行状态更新
            for username, user_info in users_info.items():
                if not user_info:
                    result[username] = False
                    continue

                # 判断用户当前状态是否已经是目标状态
                current_disabled = not user_info.get('enabled', True)
                if current_disabled == disable:
                    # 状态已经符合预期，无需修改
                    result[username] = True
                    continue

                try:
                    # 获取用户DN
                    user_dn = user_info.get('dn')
                    if not user_dn:
                        result[username] = False
                        continue

                    # 执行修改操作
                    # ACCOUNTDISABLE = 2
                    modify_attrs = {
                        'userAccountControl': [(MODIFY_REPLACE, [514] if disable else [512])]
                    }

                    success = self._conn.modify(user_dn, modify_attrs)
                    result[username] = success

                    if success:
                        self._logger.info(f"用户 {username} {'禁用' if disable else '启用'}成功")
                    else:
                        self._logger.error(f"用户 {username} {'禁用' if disable else '启用'}失败: {self._conn.result}")

                except Exception as e:
                    self._logger.error(f"处理用户 {username} 时出错: {str(e)}")
                    result[username] = False

            return result

        except Exception as e:
            self._logger.error(f"批量{'禁用' if disable else '启用'}用户时出错: {str(e)}")
            return {username: False for username in usernames}

    async def disable_user(self, username: str) -> bool:
        """禁用AD用户账号

        Args:
            username: 要禁用的用户名

        Returns:
            操作是否成功
        """
        try:
            self._logger.info(f"禁用用户账号: {username}")
            return await self.toggle_user_status(username, force_enable=False)
        except Exception as e:
            self._logger.error(f"禁用用户账号时出错: {str(e)}")
            return False

    async def enable_user(self, username: str) -> bool:
        """启用AD用户账号

        Args:
            username: 要启用的用户名

        Returns:
            操作是否成功
        """
        try:
            self._logger.info(f"启用用户账号: {username}")
            return await self.toggle_user_status(username, force_enable=True)
        except Exception as e:
            self._logger.error(f"启用用户账号时出错: {str(e)}")
            return False

    async def delete_user(self, username: str) -> bool:
        """删除AD用户"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            user_dn = self._conn.entries[0].distinguishedName.value
            self._logger.info(f"找到用户DN: {user_dn}")

            # 删除用户
            success = self._conn.delete(user_dn)

            if not success:
                self._logger.error(f"删除用户失败: {self._conn.result}")
                return False

            self._logger.info(f"用户删除成功: {username}")
            return True

        except Exception as e:
            self._logger.error(f"删除用户时出错: {str(e)}")
            return False

    async def get_all_groups(self, ou_dn: str = None) -> List[Dict]:
        """获取所有组列表

        Args:
            ou_dn: 可选。如果提供，只返回该OU下的组

        Returns:
            包含组信息的字典列表
        """
        try:
            if not await self.ensure_connected():
                return []
            return self._get_all_groups(ou_dn)

        except Exception as e:
            self._logger.error(f"获取组列表失败: {str(e)}")
            return []

    def _get_all_groups(self, ou_dn: str = None) -> List[Dict]:
        """实际的组获取操作（同步）"""
        try:
            search_filter = '(objectClass=group)'
            attributes = [
                'sAMAccountName',
                'displayName',
                'description',
                'distinguishedName',
                'groupType',
                'objectCategory',
                'objectSid',
                'systemFlags'
            ]

            search_base = self._config['search_base']
            self._logger.info(f"开始搜索组: base={search_base}, filter={search_filter}")

            self._conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=attributes
            )

            groups = []
            builtin_count = 0
            security_count = 0

            for entry in self._conn.entries:
                try:
                    group_type = entry.groupType.value if hasattr(entry, 'groupType') else 0
                    is_builtin = bool(group_type & 0x00000001)
                    is_security = bool(group_type & 0x80000000)

                    if is_builtin:
                        builtin_count += 1
                    if is_security:
                        security_count += 1

                    group = {
                        'name': entry.sAMAccountName.value,
                        'displayName': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                        'description': entry.description.value if hasattr(entry, 'description') else '',
                        'dn': entry.distinguishedName.value,
                        'shortName': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                        'groupType': group_type,
                        'isBuiltin': is_builtin,
                        'isSecurity': is_security
                    }

                    # 只添加属于当前OU的组
                    if ou_dn and not group['dn'].endswith(ou_dn):
                        continue

                    groups.append(group)

                except Exception as e:
                    self._logger.error(f"处理组条目时出错: {str(e)}")
                    continue

            # 统输出组信息摘要
            self._logger.info(f"组搜索完成: 共找到 {len(groups)} 个组")
            self._logger.debug(
                f"组类型统计:\n"
                f"- 内置组: {builtin_count}\n"
                f"- 安全组: {security_count}\n"
                f"- 分布组: {len(groups) - security_count}"
            )

            return groups

        except Exception as e:
            self._logger.error(f"获取组列表失败: {str(e)}", exc_info=True)
            return []

    async def create_group(self, name: str, ou_dn: str, display_name: str = None, description: str = None) -> bool:
        """创建AD组"""
        try:
            if not await self.ensure_connected():
                return False

            # 构建组的DN
            group_dn = f"CN={name},{ou_dn}"

            # 准备属性
            attributes = {
                'objectClass': ['top', 'group'],
                'sAMAccountName': name,
                'displayName': display_name or name,
            }

            if description:
                attributes['description'] = description

            self._logger.info(f"开始创建组: {group_dn}")

            # 创建组
            success = self._conn.add(
                dn=group_dn,
                object_class=['top', 'group'],
                attributes=attributes
            )

            if not success:
                self._logger.error(f"创建组失败: {self._conn.result}")
                return False

            self._logger.info(f"组创建成功: {name}")
            return True

        except Exception as e:
            self._logger.error(f"创建组时出错: {str(e)}")
            return False

    async def update_group(self, name: str, display_name: str = None, description: str = None) -> bool:
        """更新AD组"""
        try:
            if not await self.ensure_connected():
                return False

            # 查找组
            search_filter = f'(&(objectClass=group)(sAMAccountName={name}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到组: {name}")
                return False

            group_dn = self._conn.entries[0].distinguishedName.value

            # 准备更新的属性
            changes = {}
            if display_name is not None:
                changes['displayName'] = [(MODIFY_REPLACE, [display_name])]
            if description is not None:
                changes['description'] = [(MODIFY_REPLACE, [description])]

            if not changes:
                return True  # 没有需要更新的属性

            # 更新组
            success = self._conn.modify(
                dn=group_dn,
                changes=changes
            )

            if not success:
                self._logger.error(f"更新组失败: {self._conn.result}")
                return False

            self._logger.info(f"组更新成功: {name}")
            return True

        except Exception as e:
            self._logger.error(f"更新组时出错: {str(e)}")
            return False

    async def delete_group(self, name: str) -> bool:
        """删除AD组"""
        try:
            if not await self.ensure_connected():
                return False

            # 查找组
            search_filter = f'(&(objectClass=group)(sAMAccountName={name}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到组: {name}")
                return False

            group_dn = self._conn.entries[0].distinguishedName.value

            # 删除组
            success = self._conn.delete(group_dn)

            if not success:
                self._logger.error(f"删除组失败: {self._conn.result}")
                return False

            self._logger.info(f"组删除成功: {name}")
            return True

        except Exception as e:
            self._logger.error(f"删除组时出错: {str(e)}")
            return False

    async def get_group_member_dns(self, group_dn: str) -> List[str]:
        """获取安全组的所有成员DN

        Args:
            group_dn: 安全组的DN

        Returns:
            成员DN列表
        """
        try:
            if not await self.ensure_connected():
                return []

            # 搜索安全组
            search_filter = f'(&(objectClass=group)(distinguishedName={self.escape_dn_for_filter(group_dn)}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['member']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到安全组: {group_dn}")
                return []

            entry = self._conn.entries[0]
            members = []

            # 获取成员列表
            if hasattr(entry, 'member'):
                members.extend(entry.member.values)

            return members

        except Exception as e:
            self._logger.error(f"获取安全组成员失败: {str(e)}")
            return []

    async def add_member_to_group(self, member_dn: str, group_dn: str) -> bool:
        """将成员添加到安全组

        Args:
            member_dn: 成员的DN
            group_dn: 安全组的DN

        Returns:
            是否添加成功
        """
        try:
            if not await self.ensure_connected():
                return False

            # 检查成员是否已在组中
            members = await self.get_group_member_dns(group_dn)
            if member_dn in members:
                self._logger.debug(f"成员 {member_dn} 已在安全组 {group_dn} 中")
                return True

            # 添加成员到组
            success = self._conn.modify(
                group_dn,
                {'member': [(MODIFY_ADD, [member_dn])]}
            )

            if not success:
                error_msg = self._conn.result.get('description', '未知错误')
                self._logger.error(f"添加成员到安全组失败: {error_msg}")
                return False

            self._logger.info(f"成功将成员 {member_dn} 添加到安全组 {group_dn}")
            return True

        except Exception as e:
            self._logger.error(f"添加成员到安全组时出错: {str(e)}")
            return False

    async def get_group_members(self, group_name: str, page: int = 1, page_size: int = 20, search: str = None) -> tuple[List[Dict], int]:
        """获取组成员列表(分页)

        Args:
            group_name: 组名称
            page: 页码
            page_size: 每页数量
            search: 搜索关键词，支持按用户名、显示名称、部门、职位搜索

        Returns:
            成员列表和总数
        """
        try:
            if not await self.ensure_connected():
                return [], 0

            # 查找组
            search_filter = f'(&(objectClass=group)(sAMAccountName={group_name}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['member']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到组: {group_name}")
                return [], 0

            entry = self._conn.entries[0]
            if not hasattr(entry, 'member'):
                return [], 0

            # 获取所有成员
            all_members = []
            for member_dn in entry.member.values:
                try:
                    # 获取成员详细信息
                    member_success = self._conn.search(
                        search_base=member_dn,
                        search_filter='(objectClass=user)',
                        search_scope=BASE,
                        attributes=[
                            'sAMAccountName',
                            'displayName',
                            'mail',
                            'department',
                            'title',
                            'userAccountControl'
                        ]
                    )

                    if member_success and self._conn.entries:
                        member = self._conn.entries[0]
                        member_info = {
                            'username': member.sAMAccountName.value,
                            'name': member.displayName.value if hasattr(member, 'displayName') else member.sAMAccountName.value,
                            'email': member.mail.value if hasattr(member, 'mail') else '',
                            'department': member.department.value if hasattr(member, 'department') else '',
                            'title': member.title.value if hasattr(member, 'title') else '',
                            'enabled': not bool(int(member.userAccountControl.value) & 2) if hasattr(member, 'userAccountControl') else True
                        }
                        all_members.append(member_info)
                except Exception as e:
                    self._logger.warning(f"获取成员 {member_dn} 信息失败: {str(e)}")
                    continue

            # 如果有搜索关键词，过滤成员列表
            filtered_members = all_members
            if search and search.strip():
                search_term = search.lower().strip()
                filtered_members = []
                for member in all_members:
                    # 搜索用户名、显示名称、部门、职位
                    if (search_term in member['username'].lower() or
                        search_term in (member['name'] or '').lower() or
                        search_term in (member['department'] or '').lower() or
                        search_term in (member['title'] or '').lower()):
                        filtered_members.append(member)

                self._logger.info(f"搜索结果: 关键词='{search_term}', 匹配数={len(filtered_members)}, 总数={len(all_members)}")

            # 分页处理
            total_entries = len(filtered_members)
            start_idx = (page - 1) * page_size
            end_idx = min(start_idx + page_size, total_entries)
            page_entries = filtered_members[start_idx:end_idx]

            self._logger.info(
                f"组成员获取完成: 总数={total_entries}, 当前页={len(page_entries)}, "
                f"页码={page}, 每页={page_size}"
            )

            return page_entries, total_entries

        except Exception as e:
            self._logger.error(f"获取组成员列表失败: {str(e)}", exc_info=True)
            return [], 0

    async def delete_group(self, group_dn: str) -> bool:
        """删除安全组

        Args:
            group_dn: 安全组的DN

        Returns:
            是否删除成功
        """
        try:
            if not await self.ensure_connected():
                return False

            # 删除安全组
            success = self._conn.delete(group_dn)

            if not success:
                error_msg = self._conn.result.get('description', '未知错误')
                self._logger.error(f"删除安全组失败: {error_msg}")
                return False

            self._logger.info(f"成功删除安全组 {group_dn}")
            return True

        except Exception as e:
            self._logger.error(f"删除安全组时出错: {str(e)}")
            return False

    async def search_users(self, search: str = None, page: int = 1, page_size: int = 20) -> Tuple[List[Dict], int]:
        """全局搜索用户"""
        try:
            if not await self.ensure_connected():
                return [], 0

            # 构建搜索过滤器
            if search:
                # 同时搜索用户名和显示名称
                search_filter = (
                    f'(&(objectClass=user)(|(sAMAccountName=*{search}*)(displayName=*{search}*)))'
                )
            else:
                search_filter = '(objectClass=user)'

            # 使用Simple Paged Results控制获取所有条目
            all_entries = []
            cookie = None

            while True:
                self._conn.search(
                    search_base=self._config['search_base'],
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=[
                        'sAMAccountName',
                        'displayName',
                        'mail',
                        'department',
                        'title',
                        'userAccountControl',
                        'pwdLastSet',
                        'msDS-UserPasswordExpiryTimeComputed',
                        'accountExpires'
                    ],
                    paged_size=1000,  # 每页1000条
                    paged_cookie=cookie
                )

                all_entries.extend(self._conn.entries)

                # 获取下一页的cookie
                cookie = self._conn.result['controls']['1.2.840.113556.1.4.319']['value']['cookie']
                if not cookie:
                    break

            total = len(all_entries)

            # 计算分页
            start = (page - 1) * page_size
            # 应用分页
            entries = all_entries[start:start + page_size]

            # 转换结果
            users = []
            for entry in entries:
                # 计算密码过期信息
                password_expiry_info = self._calculate_password_expiry(entry)

                user = {
                    'username': entry.sAMAccountName.value,
                    'name': entry.displayName.value if hasattr(entry, 'displayName') else '',
                    'email': entry.mail.value if hasattr(entry, 'mail') else '',
                    'department': entry.department.value if hasattr(entry, 'department') else '',
                    'title': entry.title.value if hasattr(entry, 'title') else '',
                    'enabled': not bool(entry.userAccountControl.value & 2) if hasattr(entry, 'userAccountControl') else True,
                    'dn': entry.entry_dn,
                    'password_expiry_date': password_expiry_info['expiry_date'],
                    'password_never_expires': password_expiry_info['never_expires'],
                    'days_until_expiry': password_expiry_info['days_until_expiry'],
                    'password_expired': password_expiry_info['expired']
                }
                users.append(user)

            return users, total

        except Exception as e:
            self._logger.error(f"搜索用户时出错: {str(e)}")
            return [], 0

    async def get_group_dn(self, group_name: str) -> Optional[str]:
        """通过组名获取组DN"""
        try:
            if not await self.ensure_connected():
                return None

            # 使用sAMAccountName搜索组
            search_filter = f'(&(objectClass=group)(sAMAccountName={group_name}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到组: {group_name}")
                return None

            group_dn = self._conn.entries[0].distinguishedName.value
            self._logger.info(f"找到组DN: {group_dn}")
            return group_dn

        except Exception as e:
            self._logger.error(f"获取组DN失败: {str(e)}")
            return None

    async def add_members_to_group(self, group_dn: str, members: List[str]) -> bool:
        """添加成员到组"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取组当前成员DN列表
            current_members = await self.get_group_member_dns(group_dn)
            self._logger.debug(f"组 {group_dn} 当前有 {len(current_members)} 个成员")

            # 获取用户DN列表
            user_dns = []
            user_dns_map = {}  # 用于记录用户名和DN的映射
            for username in members:
                search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
                success = self._conn.search(
                    search_base=self._config['search_base'],
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=['distinguishedName']
                )

                if success and self._conn.entries:
                    user_dn = self._conn.entries[0].distinguishedName.value
                    user_dns_map[username] = user_dn
                    self._logger.debug(f"找到用户 {username} 的DN: {user_dn}")
                else:
                    self._logger.warning(f"未找到用户: {username}")

            if not user_dns_map:
                self._logger.error("未找到任何有效用户")
                return False

            # 过滤掉已经在组中的用户
            users_to_add = []
            for username, user_dn in user_dns_map.items():
                if user_dn in current_members:
                    self._logger.debug(f"用户 {username} 已在组 {group_dn} 中，跳过添加")
                else:
                    users_to_add.append(user_dn)

            # 如果没有需要添加的用户，直接返回成功
            if not users_to_add:
                self._logger.info(f"所有用户已在组 {group_dn} 中，无需添加")
                return True

            # 添加用户到组
            success = self._conn.modify(
                group_dn,
                {'member': [(MODIFY_ADD, users_to_add)]}
            )

            if success:
                self._logger.info(f"已将 {len(users_to_add)} 个用户添加到组 {group_dn}")
                return True
            else:
                self._logger.error(f"添加用户到组失败: {self._conn.result}")
                return False

        except Exception as e:
            self._logger.error(f"添加用户到组时出错: {str(e)}", exc_info=True)
            return False

    async def remove_members_from_group(self, group_dn: str, members: List[str]) -> bool:
        """从组中移除成员"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN列表
            user_dns = []
            for username in members:
                search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
                success = self._conn.search(
                    search_base=self._config['search_base'],
                    search_filter=search_filter,
                    search_scope=SUBTREE,
                    attributes=['distinguishedName']
                )

                if success and self._conn.entries:
                    user_dns.append(self._conn.entries[0].distinguishedName.value)
                else:
                    self._logger.warning(f"未找到用户: {username}")

            if not user_dns:
                self._logger.error("没有找到任何有效用户")
                return False

            # 从组中移除指定的用户
            success = self._conn.modify(
                group_dn,
                {'member': [(MODIFY_DELETE, user_dns)]}  # 使用MODIFY_DELETE而不是MODIFY_REPLACE
            )

            if not success:
                self._logger.error(f"从组中移除成员失败: {self._conn.result}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"从组中移除成员时出错: {str(e)}")
            return False

    async def create_ou(self, parent_dn: str, name: str, description: str = None) -> bool:
        """创建组织单位"""
        try:
            if not await self.ensure_connected():
                return False

            # 检查父OU是否存在
            if parent_dn:
                parent_exists = await self.check_ou_exists(parent_dn)
                if not parent_exists:
                    self._logger.error(f"父OU不存在: {parent_dn}")
                    raise ValueError(f"父OU不存在: {parent_dn}")

            # 构建新OU的DN
            ou_dn = f"OU={name},{parent_dn}"

            # 检查OU是否已存在
            ou_exists = await self.check_ou_exists(ou_dn)
            if ou_exists:
                self._logger.error(f"OU已存在: {ou_dn}")
                raise ValueError(f"OU已存在: {ou_dn}")

            # 准备OU属性
            attributes = {
                'objectClass': ['organizationalUnit', 'top'],
                'ou': [name]
            }

            if description:
                attributes['description'] = [description]

            # 创建OU
            success = self._conn.add(
                dn=ou_dn,
                object_class=['organizationalUnit', 'top'],
                attributes=attributes
            )

            if not success:
                error_result = self._conn.result
                error_desc = error_result.get('description', '')
                error_msg = error_result.get('message', '')
                self._logger.error(f"创建OU失败: {error_desc} - {error_msg}")

                if error_result.get('result') == 68:  # entryAlreadyExists
                    raise ValueError(f"OU已存在: {name}")
                return False

            self._logger.info(f"OU创建成功: {ou_dn}")
            return True

        except ValueError as e:
            self._logger.error(f"创建OU参数错误: {str(e)}")
            raise
        except Exception as e:
            self._logger.error(f"创建OU时出错: {str(e)}")
            return False

    async def check_ou_exists(self, ou_dn: str) -> bool:
        """检查OU是否存在"""
        try:
            if not await self.ensure_connected():
                return False

            # 执行搜索
            success = self._conn.search(
                search_base=ou_dn,
                search_filter='(objectClass=organizationalUnit)',
                search_scope=BASE,
                attributes=['ou']
            )

            return success and len(self._conn.entries) > 0

        except Exception as e:
            self._logger.error(f"检查OU存在时出错: {str(e)}")
            return False

    async def set_user_must_change_password(self, username: str) -> bool:
        """设置用户下次登录必须修改密码"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            user_dn = self._conn.entries[0].distinguishedName.value

            # 设置pwdLastSet属性为0，强制用户下次登录时修改密码
            success = self._conn.modify(
                user_dn,
                {'pwdLastSet': [('MODIFY_REPLACE', [0])]},
            )

            if not success:
                self._logger.error(f"设置用户下次登录修改密码失败: {username}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"设置用户下次登录修改密码时出错: {str(e)}")
            return False

    async def update_ou(self, ou_dn: str, attributes: Dict[str, Any]) -> bool:
        """更新OU信息"""
        try:
            if not await self.ensure_connected():
                return False

            self._logger.info(f"开始更新OU: {ou_dn}")
            self._logger.info(f"更新属性: {attributes}")

            # 如果包含name属性,需要先重命名
            if 'name' in attributes:
                new_name = attributes['name']
                self._logger.info(f"需要重命名OU，新名称: {new_name}")
                # 从DN中提取父DN
                old_rdn = ou_dn.split(',')[0]  # 获取第一部分 (OU=xxx)
                parent_dn = ou_dn[len(old_rdn) + 1:]  # 剩余部分就是父DN

                # 构建新的RDN
                new_rdn = f"OU={new_name}"
                self._logger.info(f"新RDN: {new_rdn}, 父DN: {parent_dn}")

                # 执行重命名操作
                success = self._conn.modify_dn(
                    ou_dn,
                    new_rdn,
                    new_superior=None  # 保持在同一父OU下
                )

                if not success:
                    self._logger.error(f"重命名OU失败: {self._conn.result}")
                    return False

                # 更新OU DN以便后续操作
                ou_dn = f"{new_rdn},{parent_dn}"
                self._logger.info(f"重命名成功，新DN: {ou_dn}")

            # 准备其他属性的更新
            changes = {}
            if 'description' in attributes:
                description = attributes['description']
                self._logger.info(f"准备更新description属性: {description}")
                # 确保description是字符串类型
                if description is not None:
                    description = str(description)
                changes['description'] = [(MODIFY_REPLACE, [description] if description else [])]

            # 如果有其他属性需要更新
            if changes:
                self._logger.info(f"准备更新的属性变更: {changes}")
                success = self._conn.modify(
                    dn=ou_dn,
                    changes=changes
                )

                if not success:
                    self._logger.error(f"更新OU属性失败: {self._conn.result}")
                    return False

            return True

        except Exception as e:
            self._logger.error(f"更新OU时出错: {str(e)}")
            return False

    async def delete_ou(self, ou_dn: str, recursive: bool = False) -> bool:
        """删除OU"""
        try:
            if not await self.ensure_connected():
                return False

            # 检查OU是否为空
            search_filter = '(objectClass=*)'
            success = self._conn.search(
                search_base=ou_dn,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if success and len(self._conn.entries) > 1:  # >1 因为会包含OU本身
                if not recursive:
                    raise ValueError("OU不为空，无法删除。如需递归删除，请设置recursive=True")

                # 递归删除所有子对象
                for entry in reversed(self._conn.entries[1:]):  # 跳过OU本身，反序以确保先删除子对象
                    success = self._conn.delete(entry.distinguishedName.value)
                    if not success:
                        self._logger.error(f"删除子对象失败: {entry.distinguishedName.value}")
                        return False

            # 删除OU本身
            success = self._conn.delete(ou_dn)

            if not success:
                self._logger.error(f"删除OU失败: {ou_dn}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"删除OU时出错: {str(e)}")
            return False

    async def rename_ou(self, dn: str, new_name: str) -> bool:
        """
        重命名组织单位
        :param dn: 要重命名的OU的DN
        :param new_name: 新的OU名称
        :return: 操作是否成功
        """
        try:
            await self.ensure_connected()

            # 解析DN的各部分
            dn_parts = dn.split(',')
            if not dn_parts or not dn_parts[0].startswith('OU='):
                self._logger.error(f"无效的OU DN: {dn}")
                return False

            # 计算新的DN
            new_dn = f"OU={new_name},{','.join(dn_parts[1:])}"

            # 使用LDAP的rename操作
            self._logger.info(f"重命名OU: {dn} -> {new_dn}")
            # 构建新的RDN
            new_rdn = f"OU={new_name}"
            result = self._conn.modify_dn(
                dn,
                new_rdn,
                new_superior=None  # 保持在同一父OU下
            )

            if not result:
                self._logger.error(f"重命名OU失败: {self._conn.result}")
                return False

            self._logger.info(f"重命名OU成功: {dn} -> {new_dn}")
            return True

        except Exception as e:
            self._logger.error(f"重命名OU出错: {str(e)}")
            return False

    async def modify_ou(self, dn: str, attributes: Dict[str, List[str]]) -> bool:
        """
        修改组织单位的属性
        :param dn: OU的DN
        :param attributes: 要修改的属性字典，格式为 {属性名: [属性值列表]}
        :return: 操作是否成功
        """
        try:
            await self.ensure_connected()

            # 准备修改操作
            changes = {}
            for attr_name, attr_values in attributes.items():
                changes[attr_name] = [(MODIFY_REPLACE, attr_values)]

            # 执行修改
            self._logger.info(f"修改OU属性: {dn}, 属性: {attributes}")
            result = self._conn.modify(dn, changes)

            if not result:
                self._logger.error(f"修改OU属性失败: {self._conn.result}")
                return False

            self._logger.info(f"修改OU属性成功: {dn}")
            return True

        except Exception as e:
            self._logger.error(f"修改OU属性出错: {str(e)}")
            return False

    async def move_user(self, username: str, target_ou_dn: str) -> bool:
        """移动用户到指定OU"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户DN和CN
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'cn']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            user_dn = self._conn.entries[0].distinguishedName.value
            user_cn = self._conn.entries[0].cn.value

            # 移动用户，保持原有CN
            success = self._conn.modify_dn(user_dn, f"CN={user_cn}", new_superior=target_ou_dn)

            if not success:
                self._logger.error(f"移动用户失败: {self._conn.result}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"移动用户时出错: {str(e)}")
            return False

    async def move_ou(self, ou_dn: str, target_ou_dn: str) -> bool:
        """移动OU到指定目标OU"""
        try:
            if not await self.ensure_connected():
                return False

            # 提取OU的名称
            ou_parts = ou_dn.split(',')
            if not ou_parts or not ou_parts[0].startswith('OU='):
                self._logger.error(f"无效的OU DN: {ou_dn}")
                return False

            ou_name = ou_parts[0][3:]  # 去除 'OU=' 前缀

            # 移动OU到目标位置
            success = self._conn.modify_dn(ou_dn, f"OU={ou_name}", new_superior=target_ou_dn)

            if not success:
                self._logger.error(f"移动OU失败: {self._conn.result}")
                return False

            return True

        except Exception as e:
            self._logger.error(f"移动OU时出错: {str(e)}")
            return False

    async def search(self, search_base: str, search_filter: str, search_scope: str = 'sub', attributes: List[str] = None) -> List[Dict]:
        """执行LDAP搜索并返回结果（异步包装）"""
        try:
            if not await self.ensure_connected():
                return []

            scope_map = {
                'base': BASE,
                'level': LEVEL,
                'one': LEVEL,  # one level alias
                'sub': SUBTREE,
                'subtree': SUBTREE  # subtree alias
            }

            # 检查搜索范围参数
            if search_scope not in scope_map:
                self._logger.error(f"无效的搜索范围: {search_scope}")
                return []

            # 执行搜索
            self._conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=scope_map[search_scope],
                attributes=attributes or ['*']
            )

            # 处理结果
            results = []
            for entry in self._conn.entries:
                entry_dict = {'dn': entry.entry_dn}
                entry_dict['attributes'] = {}
                for attr in entry.entry_attributes:
                    if isinstance(entry[attr].value, list):
                        entry_dict['attributes'][attr] = entry[attr].values
                    else:
                        entry_dict['attributes'][attr] = [entry[attr].value] if entry[attr].value is not None else []
                results.append(entry_dict)

            return results

        except Exception as e:
            self._logger.error(f"LDAP搜索失败: {str(e)}")
            return []

    def _get_connection_key(self, config=None):
        """生成连接池键值"""
        cfg = config or self._config
        if not cfg:
            return None
        return f"{cfg['server']}:{cfg['username']}"

    def _get_connection_from_pool(self, key):
        """从连接池获取连接"""
        if not key or key not in self._conn_pool:
            return None

        conn_info = self._conn_pool[key]
        conn = conn_info['connection']
        last_used = conn_info['last_used']

        # 检查连接是否仍然有效
        if (conn and conn.bound and
            time.time() - last_used < self._connection_timeout):
            self._logger.debug(f"从连接池复用连接: {key}")
            conn_info['last_used'] = time.time()  # 更新最后使用时间
            return conn

        # 连接已失效，从池中移除
        self._logger.info(f"连接已失效，从池中移除: {key}")
        try:
            conn.unbind()
        except:
            pass
        del self._conn_pool[key]
        return None

    def _cleanup_pool(self, force_cleanup=False):
        """清理过期的连接

        Args:
            force_cleanup (bool): 如果为True，则强制清理所有非活动连接
        """
        current_time = time.time()
        expired_keys = []
        current_conn_key = self._get_connection_key()

        # 收集过期的连接键
        for key, conn_info in self._conn_pool.items():
            # 跳过当前正在使用的连接
            if key == current_conn_key and not force_cleanup:
                continue

            # 检查连接是否过期
            if current_time - conn_info['last_used'] >= self._connection_timeout:
                expired_keys.append(key)
            # 如果强制清理，添加所有非活动连接
            elif force_cleanup and key != current_conn_key:
                expired_keys.append(key)

        # 清理过期连接
        cleaned_count = 0
        for key in expired_keys:
            try:
                conn_info = self._conn_pool[key]
                if conn_info['connection']:
                    # 检查连接是否仍然有效
                    if conn_info['connection'].bound:
                        try:
                            conn_info['connection'].unbind()
                            self._logger.debug(f"成功关闭连接: {key}")
                        except Exception as e:
                            self._logger.warning(f"关闭连接时出错: {str(e)}")
                    else:
                        self._logger.debug(f"连接已经关闭: {key}")
            except Exception as e:
                self._logger.warning(f"清理过期连接时出错: {str(e)}")
            finally:
                if key in self._conn_pool:  # 确保键仍然存在
                    del self._conn_pool[key]
                    cleaned_count += 1

        if cleaned_count > 0:
            self._logger.info(f"已清理 {cleaned_count} 个连接 (强制清理: {force_cleanup})")

        return cleaned_count

    def __del__(self):
        """析构函数,清理资源"""
        try:
            # 清理连接池
            for conn_info in self._conn_pool.values():
                if conn_info['connection']:
                    try:
                        conn_info['connection'].unbind()
                    except:
                        pass
            # 清理缓存
            self._clear_cache()
        except:
            pass

    async def _get_user_dn(self, username: str) -> Optional[str]:
        """获取用户DN"""
        try:
            if not await self.ensure_connected():
                return None

            # 搜索用户
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return None

            return self._conn.entries[0].distinguishedName.value

        except Exception as e:
            self._logger.error(f"获取用户DN失败: {str(e)}")
            return None

    async def fix_user_cn(self, username: str) -> bool:
        """修复用户的CN为正确的显示名称"""
        try:
            if not await self.ensure_connected():
                return False

            # 获取用户信息
            search_filter = f'(&(objectClass=user)(sAMAccountName={username}))'
            success = self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['distinguishedName', 'displayName', 'cn']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到用户: {username}")
                return False

            entry = self._conn.entries[0]

            # 如果没有displayName，则无法修复
            if not hasattr(entry, 'displayName') or not entry.displayName.value:
                self._logger.warning(f"用户 {username} 没有displayName属性，无法修复CN")
                return False

            # 获取当前DN和新CN
            current_dn = entry.distinguishedName.value
            new_cn = entry.displayName.value

            # 如果CN已经正确，则不需要修改
            if hasattr(entry, 'cn') and entry.cn.value == new_cn:
                self._logger.info(f"用户 {username} 的CN已经正确")
                return True

            # 从当前DN中提取父DN
            parent_dn = ','.join(current_dn.split(',')[1:])

            # 在修改之前先检查是否存在同名CN
            search_filter = f'(&(objectClass=user)(cn={new_cn}))'
            success = self._conn.search(
                search_base=parent_dn,
                search_filter=search_filter,
                search_scope=LEVEL,
                attributes=['cn']
            )

            if success and self._conn.entries:
                # 已存在同名CN，尝试添加数字后缀
                self._logger.info(f"CN {new_cn} 已存在，尝试添加数字后缀")

                # 获取所有已存在的CN
                search_filter = f'(&(objectClass=user)(cn={new_cn}*))'
                success = self._conn.search(
                    search_base=parent_dn,
                    search_filter=search_filter,
                    search_scope=LEVEL,
                    attributes=['cn']
                )

                existing_cns = [entry.cn.value for entry in self._conn.entries]

                # 找到一个未使用的后缀
                suffix = 1
                new_cn_with_suffix = new_cn
                while new_cn_with_suffix in existing_cns:
                    new_cn_with_suffix = f"{new_cn} ({suffix})"
                    suffix += 1

                new_cn = new_cn_with_suffix
                self._logger.info(f"使用新的CN: {new_cn}")

            # 尝试修改用户的DN
            try:
                success = self._conn.modify_dn(
                    current_dn,
                    f"CN={new_cn}",
                    new_superior=None  # 保持在同一位置
                )

                if success:
                    self._logger.info(f"成功修复用户 {username} 的CN: {new_cn}")
                    return True
                else:
                    self._logger.error(f"修复用户CN失败: {self._conn.result}")
                    return False

            except Exception as e:
                self._logger.error(f"修改用户CN时出错: {str(e)}")
                return False

        except Exception as e:
            self._logger.error(f"修复用户CN时出错: {str(e)}")
            return False

    async def fix_all_users_cn(self, ou_dn: str = None) -> dict:
        """批量修复指定OU下所有用户的CN"""
        try:
            if not await self.ensure_connected():
                return {"success": 0, "failed": 0, "skipped": 0}

            # 构建搜索条件
            search_filter = '(&(objectClass=user)(objectCategory=person))'
            search_base = ou_dn if ou_dn else self._config['search_base']

            success = self._conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['sAMAccountName']
            )

            if not success:
                self._logger.error("搜索用户失败")
                return {"success": 0, "failed": 0, "skipped": 0}

            results = {"success": 0, "failed": 0, "skipped": 0}

            # 对每个用户执行修复
            for entry in self._conn.entries:
                try:
                    username = entry.sAMAccountName.value
                    fix_result = await self.fix_user_cn(username)

                    if fix_result:
                        results["success"] += 1
                    else:
                        results["failed"] += 1

                except Exception as e:
                    self._logger.error(f"处理用户 {username} 时出错: {str(e)}")
                    results["failed"] += 1

            self._logger.info(f"CN修复完成: {results}")
            return results

        except Exception as e:
            self._logger.error(f"批量修复用户CN时出错: {str(e)}")
            return {"success": 0, "failed": 0, "skipped": 0}

    async def set_connection_timeout(self, timeout_seconds: int = 15) -> None:
        """设置连接超时时间"""
        try:
            # 如果当前连接存在，更新其超时设置
            if hasattr(self, '_conn') and self._conn:
                self._conn.socket_timeout = timeout_seconds
                self._logger.debug(f"已将连接超时设置为 {timeout_seconds} 秒")

            # 更新配置，以便新创建的连接也使用新的超时值
            self._timeout = timeout_seconds
        except Exception as e:
            self._logger.error(f"设置连接超时时间失败: {str(e)}")

    async def check_ou_exists(self, ou_dn: str) -> bool:
        """
        检查指定的OU是否存在

        Args:
            ou_dn: 组织单位的DN

        Returns:
            OU是否存在
        """
        try:
            if not await self.ensure_connected():
                return False

            # 转义特殊字符
            escaped_dn = self.escape_dn_for_filter(ou_dn)

            # 构建搜索过滤器 - 查找特定DN的条目
            search_filter = f'(distinguishedName={escaped_dn})'

            # 执行搜索
            self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=ldap3.SUBTREE,
                attributes=['objectClass']
            )

            # 检查是否找到结果
            if self._conn.entries:
                self._logger.debug(f"OU存在: {ou_dn}")
                return True
            else:
                self._logger.warning(f"OU不存在: {ou_dn}")
                return False

        except Exception as e:
            self._logger.error(f"检查OU存在性时出错: {str(e)}")
            return False

    async def get_all_users_in_ou(self, ou_dn: str = None) -> List[Dict[str, str]]:
        """获取指定OU下的所有用户"""
        try:
            if not await self.ensure_connected():
                return []

            search_base = ou_dn if ou_dn else self._config['search_base']
            search_filter = '(&(objectClass=user)(objectCategory=person))'

            self._conn.search(
                search_base=search_base,
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=['sAMAccountName', 'displayName', 'mail', 'userAccountControl', 'distinguishedName']
            )

            users = []
            for entry in self._conn.entries:
                user = {
                    'username': entry.sAMAccountName.value,
                    'name': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                    'email': entry.mail.value if hasattr(entry, 'mail') else '',
                    'enabled': not bool(int(entry.userAccountControl.value) & 2),
                    'dn': entry.distinguishedName.value
                }
                users.append(user)

            return users
        except Exception as e:
            self._logger.error(f"获取OU下所有用户失败: {str(e)}")
            return []

    async def get_group_by_name(self, group_name: str) -> Optional[Dict]:
        """通过名称查找安全组

        Args:
            group_name: 组的sAMAccountName

        Returns:
            找到的组信息，或者None
        """
        try:
            if not await self.ensure_connected():
                return None

            self._logger.info(f"通过名称查找安全组: {group_name}")

            # 构建搜索过滤器
            search_filter = f"(&(objectClass=group)(sAMAccountName={self.escape_dn_for_filter(group_name)}))"

            # 执行搜索
            self._conn.search(
                search_base=self._config['search_base'],
                search_filter=search_filter,
                search_scope=SUBTREE,
                attributes=[
                    'cn',
                    'sAMAccountName',
                    'displayName',
                    'description',
                    'distinguishedName',
                    'groupType'
                ]
            )

            if not self._conn.entries:
                self._logger.info(f"未找到名为 {group_name} 的安全组")
                return None

            # 如果找到多个，使用第一个
            if len(self._conn.entries) > 1:
                self._logger.warning(f"找到多个名为 {group_name} 的安全组，使用第一个")

            entry = self._conn.entries[0]
            group_type = entry.groupType.value if hasattr(entry, 'groupType') else 0

            group = {
                'name': entry.sAMAccountName.value,
                'displayName': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                'description': entry.description.value if hasattr(entry, 'description') else '',
                'dn': entry.distinguishedName.value,
                'isBuiltin': bool(group_type & 0x00000001),
                'isSecurity': bool(group_type & 0x80000000)
            }

            self._logger.info(f"找到安全组: {group['name']} (DN: {group['dn']})")
            return group

        except Exception as e:
            self._logger.error(f"查找安全组时出错: {str(e)}")
            return None

    async def get_group_by_dn(self, group_dn: str) -> Optional[Dict]:
        """通过DN获取组信息

        Args:
            group_dn: 组的DN

        Returns:
            组信息或None（如果未找到）
        """
        try:
            if not await self.ensure_connected():
                return None

            # 执行搜索
            success = self._conn.search(
                search_base=group_dn,
                search_filter="(objectClass=group)",
                search_scope=BASE,
                attributes=[
                    'cn',
                    'sAMAccountName',
                    'displayName',
                    'description',
                    'distinguishedName',
                    'groupType',
                    'member'
                ]
            )

            if not success or not self._conn.entries:
                self._logger.error(f"未找到组: {group_dn}")
                return None

            entry = self._conn.entries[0]
            group_type = entry.groupType.value if hasattr(entry, 'groupType') else 0

            group = {
                'name': entry.sAMAccountName.value,
                'displayName': entry.displayName.value if hasattr(entry, 'displayName') else entry.sAMAccountName.value,
                'description': entry.description.value if hasattr(entry, 'description') else '',
                'dn': entry.distinguishedName.value,
                'isBuiltin': bool(group_type & 0x00000001),
                'isSecurity': bool(group_type & 0x80000000),
                'members': entry.member.values if hasattr(entry, 'member') else []
            }

            self._logger.debug(f"通过DN找到组: {group['name']}")
            return group

        except Exception as e:
            self._logger.error(f"通过DN获取组信息时出错: {str(e)}")
            return None

    async def update_group(self, group_dn: str, attributes: Dict[str, Any]) -> bool:
        """更新安全组属性

        Args:
            group_dn: 安全组的DN
            attributes: 要更新的属性，如{'displayName': '新名称', 'description': '新描述'}

        Returns:
            是否成功
        """
        try:
            if not await self.ensure_connected():
                return False

            self._logger.info(f"更新安全组: {group_dn}")

            # 准备更新的属性
            modify_attrs = {}
            for attr_name, attr_value in attributes.items():
                if attr_name == 'name' or attr_name == 'sAMAccountName':
                    modify_attrs['sAMAccountName'] = [(MODIFY_REPLACE, [attr_value])]
                    # 同时更新cn
                    modify_attrs['cn'] = [(MODIFY_REPLACE, [attr_value])]
                elif attr_name == 'displayName':
                    modify_attrs['displayName'] = [(MODIFY_REPLACE, [attr_value])]
                elif attr_name == 'description':
                    modify_attrs['description'] = [(MODIFY_REPLACE, [attr_value])]

            # 执行更新
            success = self._conn.modify(group_dn, modify_attrs)

            if success:
                self._logger.info(f"安全组更新成功: {group_dn}")
            else:
                self._logger.error(f"安全组更新失败: {self._conn.result}")

            return success

        except Exception as e:
            self._logger.error(f"更新安全组时出错: {str(e)}")
            return False

    async def find_group_by_dept_id(self, dept_id: int) -> Optional[Dict]:
        """通过部门ID查找对应的安全组

        Args:
            dept_id: 部门ID

        Returns:
            安全组信息或None（如果未找到）
        """
        try:
            if not await self.ensure_connected():
                return None

            self._logger.info(f"通过部门ID查找安全组: {dept_id}")

            # 获取所有安全组
            all_groups = await self.get_all_groups()

            # 从描述中查找包含部门ID的安全组
            for group in all_groups:
                if 'description' in group:
                    extracted_id = extract_dept_id_from_group(group['description'])
                    if extracted_id == dept_id:
                        self._logger.info(f"找到部门ID {dept_id} 对应的安全组: {group['name']}")
                        return group

            self._logger.info(f"未找到部门ID {dept_id} 对应的安全组")
            return None

        except Exception as e:
            self._logger.error(f"通过部门ID查找安全组时出错: {str(e)}")
            return None

    def escape_dn_for_filter(self, dn: str) -> str:
        """转义DN中的特殊字符，使其可以安全地用于LDAP过滤器

        LDAP过滤器中的特殊字符: *()\\&|=!><~
        参考: https://ldap.com/ldap-filters/
        """
        if not dn:
            return dn

        # 需要转义的字符及其转义序列
        escape_map = {
            '*': '\\2a',
            '(': '\\28',
            ')': '\\29',
            '\\': '\\5c',
            '\0': '\\00',
            '/': '\\2f',
            '&': '\\26',
            '|': '\\7c',
            '=': '\\3d',
            '!': '\\21',
            '>': '\\3e',
            '<': '\\3c',
            '~': '\\7e'
        }

        # 转义每个特殊字符
        result = dn
        for char, escaped in escape_map.items():
            result = result.replace(char, escaped)

        return result

    def _ldap_entry_to_dict(self, entry) -> Dict:
        """将LDAP条目转换为Python字典

        Args:
            entry: LDAP条目对象

        Returns:
            转换后的字典
        """
        result = {}

        # 处理所有属性
        for attr_name in entry.entry_attributes:
            # 获取属性值
            attr = entry[attr_name]

            # 单值属性作为标量，多值属性作为列表
            if isinstance(attr.value, list):
                result[attr_name] = attr.values
            else:
                result[attr_name] = attr.value

        # 添加DN属性
        result['distinguishedName'] = entry.entry_dn

        return result

    async def move_object(self, object_dn: str, target_ou_dn: str) -> bool:
        """
        移动AD对象（如用户、安全组等）到指定的OU
        :param object_dn: 要移动的对象的DN
        :param target_ou_dn: 目标OU的DN
        :return: 操作是否成功
        """
        try:
            if not await self.ensure_connected():
                return False

            # 检查目标OU是否存在
            if not await self.check_ou_exists(target_ou_dn):
                self._logger.error(f"目标OU不存在: {target_ou_dn}")
                return False

            # 从对象DN中获取RDN（相对可分辨名称，如CN=组名称）
            # 使用更安全的方式分割DN，处理可能包含逗号的特殊情况
            dn_parts = object_dn.split(',')
            rdn = dn_parts[0]

            # 检查DN格式是否有效
            if not rdn or not ('=' in rdn):
                self._logger.error(f"无效的对象DN格式: {object_dn}")
                return False

            # 确保对象存在
            success = self._conn.search(
                search_base=object_dn,
                search_filter='(objectClass=*)',
                search_scope=BASE,
                attributes=['objectClass']
            )

            if not success or not self._conn.entries:
                self._logger.error(f"找不到要移动的对象: {object_dn}")
                return False

            # 确保目标不是对象自身
            if target_ou_dn.startswith(object_dn):
                self._logger.error(f"不能将对象移动到其自身或子对象中: {object_dn} -> {target_ou_dn}")
                return False

            # 检查对象类型，确保可以移动
            object_classes = self._conn.entries[0].objectClass.values

            # 某些内置或系统对象可能不允许移动
            if 'builtinDomain' in object_classes or 'configuration' in object_classes:
                self._logger.error(f"不能移动系统内置对象: {object_dn}")
                return False

            # 构建新DN
            new_dn = f"{rdn},{target_ou_dn}"

            # 执行移动操作
            self._logger.info(f"准备移动对象: {object_dn} -> {new_dn}")
            success = self._conn.modify_dn(
                object_dn,
                rdn,
                new_superior=target_ou_dn
            )

            if not success:
                result = self._conn.result
                error_desc = result.get('description', '')
                error_msg = result.get('message', '')
                self._logger.error(f"移动对象失败: {error_desc} - {error_msg}")
                return False

            self._logger.info(f"对象移动成功: {object_dn} -> {new_dn}")
            return True

        except Exception as e:
            self._logger.error(f"移动对象时出错: {str(e)}")
            return False


def extract_dept_id_from_group(group_desc: str) -> Optional[int]:
    """从安全组描述中提取部门ID

    Args:
        group_desc: 安全组描述

    Returns:
        部门ID或None（如果未找到）
    """
    # 确保输入是字符串类型
    if group_desc is None:
        return None

    if not isinstance(group_desc, str):
        try:
            group_desc = str(group_desc)
        except:
            return None

    if not group_desc:
        return None

    # 使用更健壮的正则表达式，支持中英文冒号和可选空格
    # 匹配格式：部门ID: 123、部门ID:123、部门ID： 123、部门ID：123
    match = re.search(r'部门ID[：:]\s*(\d+)', group_desc)
    if match:
        try:
            return int(match.group(1))
        except ValueError:
            return None
    return None


# 创建单例实例
ad_client = ADClient()


async def test_ad_connection(config: Dict[str, Any]) -> bool:
    """测试AD连接"""
    try:
        server = Server(
            config['server'],
            port=config['port'],
            use_ssl=config['use_ssl'],
            get_info=ALL
        )

        conn = Connection(
            server,
            user=f"{config['domain']}\\{config['username']}",
            password=config['password'],
            authentication=NTLM,
            auto_bind=True
        )

        # 尝试搜索以验证连接
        conn.search(
            config['search_base'],
            '(objectClass=*)',
            search_scope=SUBTREE,
            attributes=['*'],
            size_limit=1
        )

        conn.unbind()
        return True
    except Exception as e:
        logger.error(f"AD连接测试失败: {str(e)}")
        return False


__all__ = ['ad_client', 'test_ad_connection']