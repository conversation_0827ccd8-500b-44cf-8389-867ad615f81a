#!/usr/bin/env python
"""
Redis缓存和日志功能测试脚本

此脚本用于测试Redis缓存功能和日志记录功能。
"""

import sys
import os
import time
import random
import string
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入Redis缓存类
from app.utils.redis_cache import RedisCache

def random_string(length=10):
    """生成随机字符串"""
    return ''.join(random.choice(string.ascii_letters) for _ in range(length))

def test_basic_operations():
    """测试基本缓存操作"""
    print("\n=== 测试基本缓存操作 ===")
    
    # 创建Redis缓存实例
    cache = RedisCache()
    
    # 生成测试键名
    test_key = f"test:basic:{random_string()}"
    test_value = {"name": "测试数据", "timestamp": datetime.now().isoformat(), "number": random.randint(1, 1000)}
    
    print(f"设置缓存: {test_key} = {test_value}")
    set_result = cache.set(test_key, test_value, 60)  # 60秒过期
    print(f"设置结果: {set_result}")
    
    # 获取缓存数据
    print(f"获取缓存: {test_key}")
    cached_value = cache.get(test_key)
    print(f"缓存值: {cached_value}")
    
    # 验证缓存命中
    if cached_value == test_value:
        print("缓存命中验证: 成功")
    else:
        print(f"缓存命中验证: 失败 - 预期 {test_value}, 实际 {cached_value}")
    
    # 删除缓存
    print(f"删除缓存: {test_key}")
    delete_result = cache.delete(test_key)
    print(f"删除结果: {delete_result}")
    
    # 验证缓存已删除
    deleted_value = cache.get(test_key)
    if deleted_value is None:
        print("缓存删除验证: 成功")
    else:
        print(f"缓存删除验证: 失败 - 缓存值仍存在: {deleted_value}")

def test_pattern_operations():
    """测试模式操作"""
    print("\n=== 测试模式操作 ===")
    
    # 创建Redis缓存实例
    cache = RedisCache()
    
    # 生成测试模式
    pattern_base = f"test:pattern:{random_string(5)}"
    
    # 创建多个测试键
    keys = []
    for i in range(5):
        key = f"{pattern_base}:{i}"
        value = f"值 {i}"
        cache.set(key, value, 300)  # 5分钟过期
        keys.append(key)
    
    print(f"已创建5个测试键: {keys}")
    
    # 使用模式匹配删除所有键
    pattern = f"{pattern_base}:*"
    print(f"使用模式删除: {pattern}")
    count = cache.clear_pattern(pattern)
    print(f"删除键数量: {count}")
    
    # 验证所有键都已删除
    all_deleted = True
    for key in keys:
        value = cache.get(key)
        if value is not None:
            all_deleted = False
            print(f"键 {key} 未被删除，值: {value}")
    
    if all_deleted:
        print("模式删除验证: 成功 - 所有键已删除")
    else:
        print("模式删除验证: 失败 - 部分键未被删除")

def test_performance():
    """测试缓存性能"""
    print("\n=== 测试缓存性能 ===")
    
    # 创建Redis缓存实例
    cache = RedisCache()
    
    # 重置统计信息
    cache.reset_stats()
    
    # 生成测试键前缀
    perf_prefix = f"test:perf:{random_string(5)}"
    
    # 定义测试参数
    num_keys = 100
    get_ops = 500
    miss_ratio = 0.3  # 30%的请求应该未命中
    
    # 创建测试键
    print(f"创建 {num_keys} 个测试键...")
    for i in range(num_keys):
        key = f"{perf_prefix}:{i}"
        value = {"id": i, "data": random_string(20)}
        cache.set(key, value, 600)  # 10分钟过期
    
    # 执行获取操作
    print(f"执行 {get_ops} 次随机获取操作...")
    hits = 0
    start_time = time.time()
    
    for i in range(get_ops):
        # 随机决定是否命中缓存
        if random.random() >= miss_ratio:
            # 应该命中
            key_id = random.randint(0, num_keys - 1)
            key = f"{perf_prefix}:{key_id}"
        else:
            # 应该未命中
            key = f"{perf_prefix}:nonexistent:{random_string()}"
        
        value = cache.get(key)
        if value is not None:
            hits += 1
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"性能测试完成: {duration:.2f}秒")
    print(f"命中次数: {hits}, 命中率: {hits/get_ops*100:.2f}%")
    print(f"每秒操作数: {get_ops/duration:.2f} ops/sec")
    
    # 获取缓存统计信息
    stats = cache.get_cache_stats()
    print("\n缓存统计信息:")
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.6f}")
        else:
            print(f"  {key}: {value}")
    
    # 清理测试键
    print(f"\n清理测试键...")
    cache.clear_pattern(f"{perf_prefix}:*")

def test_metrics():
    """测试获取Redis指标"""
    print("\n=== 测试Redis指标 ===")
    
    cache = RedisCache()
    metrics = cache.get_metrics()
    
    print("Redis服务器指标:")
    for key, value in metrics.items():
        print(f"  {key}: {value}")

def main():
    """主函数"""
    print("=== Redis缓存测试脚本 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建Redis缓存实例
    cache = RedisCache()
    
    # 测试连接
    try:
        metrics = cache.get_metrics()
        print(f"Redis连接成功: {metrics.get('connected_clients')} 个客户端连接")
    except Exception as e:
        print(f"Redis连接失败: {str(e)}")
        return
    
    # 运行测试
    test_basic_operations()
    test_pattern_operations()
    test_performance()
    test_metrics()
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 