from fastapi import APIRouter
from app.api.v1 import assets, asset_settings, field_values, ecology, ad, ad_config, ad_sync_config, auth, inventory, users, ecology_sync
from app.api.v1 import permissions, roles, users_system, terminal, monitoring, email, email_personnel_sync, personnel_email_sync, email_creation_requests, ldap_config, command_whitelist, custom_fields
# registry router 已在 main.py 中单独导入注册
# from .registry import router as registry_router

api_router = APIRouter()
api_router.include_router(assets.router, prefix="/assets", tags=["assets"])
api_router.include_router(asset_settings.router, prefix="/asset-settings", tags=["asset-settings"])
api_router.include_router(field_values.router, prefix="/field-values", tags=["field-values"])
# custom_fields已在main.py中单独注册，避免重复注册
# api_router.include_router(custom_fields.router, prefix="/custom-fields", tags=["custom-fields"])
api_router.include_router(ecology.router, prefix="/ecology", tags=["ecology"])
api_router.include_router(ecology_sync.router, prefix="/ecology-sync", tags=["ecology-sync"])
api_router.include_router(ad.router, prefix="/ad", tags=["ad"])
api_router.include_router(ad_config.router, prefix="/ad-config", tags=["ad-config"])
api_router.include_router(ad_sync_config.router, prefix="/ad-sync-config", tags=["ad-sync-config"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(inventory.router, prefix="/inventory", tags=["inventory"])
api_router.include_router(users_system.router, prefix="/users", tags=["users"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
api_router.include_router(email.router, prefix="/email", tags=["email"])
api_router.include_router(personnel_email_sync.router, prefix="/personnel-email-sync", tags=["personnel-email-sync"])
api_router.include_router(email_creation_requests.router, prefix="/email-creation-requests", tags=["email-creation-requests"])
api_router.include_router(ldap_config.router, prefix="/ldap-config", tags=["ldap-config"])
api_router.include_router(command_whitelist.router, prefix="/command-whitelist", tags=["command-whitelist"])
# email_personnel_sync路由已在main.py中单独注册，避免重复注册
# api_router.include_router(email_personnel_sync.router, prefix="/email-personnel-sync", tags=["email-personnel-sync"])
# 终端管理路由需要单独注册到顶级路由，不应包含在这里
# api_router.include_router(terminal.router, prefix="/terminal", tags=["terminal"])

# 注册表管理路由需要单独注册到顶级路由，不应包含在这里
# api_router.include_router(registry_router, prefix="/registry", tags=["注册表管理"])
