from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, validator
import time
import io
import pandas as pd
from fastapi.responses import StreamingResponse

from app.api.deps import get_db, check_permissions
from app.utils import get_current_user
from app import models
from app.crud import ecology_user as crud
from app.models.ecology_user import EcologyUser as EcologyUserModel
from app.utils.redis_cache import RedisCache

router = APIRouter()

# 定义数据模型
class EcologyUser(BaseModel):
    id: int
    user_id: Optional[int] = None
    dept_id: int
    dept_name: str
    dept_hierarchy: str
    level: int
    dept_path: str
    company_id: Optional[int] = None
    company_name: Optional[str] = None
    user_name: Optional[str] = None
    job_number: Optional[str] = None
    mobile: Optional[str] = None
    email: Optional[str] = None
    job_title: Optional[int] = None
    job_title_name: Optional[str] = None
    gender: Optional[str] = None
    status: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class SyncConfig(BaseModel):
    sync_hour: int
    sync_time: Optional[str] = None
    last_sync_time: Optional[datetime] = None
    next_sync_time: Optional[datetime] = None
    sync_status: str
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

class SyncTimeUpdate(BaseModel):
    sync_time: str

    @validator('sync_time')
    def validate_sync_time(cls, v):
        try:
            hour, minute = map(int, v.split(':'))
            if hour < 0 or hour > 23 or minute < 0 or minute > 59:
                raise ValueError("时间格式不正确")
        except Exception:
            raise ValueError("时间格式必须为 HH:MM")
        return v

# 创建Redis缓存实例
user_cache = RedisCache()

# 获取本地存储的泛微用户数据
@router.get("/users", response_model=List[EcologyUser])
def get_local_ecology_users(
    response: Response,
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = None,
    exact_match: bool = False,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["basic-info:personnel:view"]))
):
    """
    获取本地存储的泛微用户数据
    - **skip**: 跳过记录数
    - **limit**: 返回记录数
    - **keyword**: 搜索关键词（可选），将搜索用户名、工号、部门名称、职位名称、手机号和邮箱
    - **exact_match**: 是否精确匹配工号（默认为False）
    """
    # 检查是否需要自动同步
    if crud.check_sync_needed(db):
        try:
            # 在后台执行同步
            crud.sync_ecology_users(db)
            # 清除缓存
            user_cache.clear_pattern("ecology:users:*")
        except Exception as e:
            # 如果同步失败，记录错误但不影响返回本地数据
            pass

    # 生成缓存键
    cache_key = f"ecology:users:{skip}:{limit}:{keyword}:{exact_match}"

    # 尝试从缓存获取数据
    cached_data = user_cache.get(cache_key)
    if cached_data:
        # 设置缓存控制头
        response.headers["X-Cache"] = "HIT"
        return cached_data

    # 从数据库获取数据
    users = crud.get_ecology_users(db, skip=skip, limit=limit, keyword=keyword, exact_match=exact_match)

    # 存入缓存
    user_cache.set(cache_key, users)
    response.headers["X-Cache"] = "MISS"

    return users

# 手动触发同步
@router.post("/sync", response_model=dict)
def sync_ecology_users_endpoint(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["basic-info:personnel:edit"]))
):
    """
    手动触发同步泛微用户数据
    """
    # 获取同步配置
    config = crud.get_sync_config(db)

    # 检查是否已经在同步中
    if config.sync_status == "同步中":
        return {"status": "error", "message": "同步已在进行中，请稍后再试"}

    # 更新同步状态
    config.sync_status = "同步中"
    db.commit()

    # 在后台执行同步
    background_tasks.add_task(crud.sync_ecology_users, db)

    # 清除缓存
    user_cache.clear_pattern("ecology:users:*")

    return {"status": "success", "message": "同步已开始，请稍后查看结果"}

# 获取同步状态
@router.get("/sync/status", response_model=SyncConfig)
def get_sync_status(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["basic-info:personnel:view"]))
):
    """
    获取同步状态
    """
    return crud.get_sync_status(db)

# 更新同步时间
@router.put("/sync/time", response_model=SyncConfig)
def update_sync_time(
    sync_time: dict,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["basic-info:personnel:edit"]))
):
    """
    更新同步时间
    """
    return crud.update_sync_time(db, sync_time.get("sync_time"))

# 导出人员数据
@router.get("/users/export")
def export_ecology_users(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["basic-info:personnel:view"]))
):
    """
    导出人员数据为Excel文件
    """
    # 获取所有用户数据，不分页
    users = db.query(EcologyUserModel).all()

    # 转换为DataFrame
    user_data = []
    for user in users:
        if user.user_name:  # 只导出有用户名的数据
            user_data.append({
                "姓名": user.user_name,
                "工号": user.job_number,
                "公司": user.company_name,
                "部门": user.dept_name,
                "部门层级": user.dept_hierarchy,
                "职位": user.job_title_name,
                "性别": user.gender,
                "手机号": user.mobile,
                "邮箱": user.email,
                "状态": user.status
            })

    df = pd.DataFrame(user_data)

    # 创建一个BytesIO对象
    output = io.BytesIO()

    # 将DataFrame保存为Excel
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='人员信息', index=False)
        worksheet = writer.sheets['人员信息']

        # 设置列宽
        for i, col in enumerate(df.columns):
            column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
            worksheet.set_column(i, i, column_width)

    # 设置文件指针到开始
    output.seek(0)

    # 创建响应
    headers = {
        'Content-Disposition': 'attachment; filename="personnel_data.xlsx"'
    }

    return StreamingResponse(
        output,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers=headers
    )