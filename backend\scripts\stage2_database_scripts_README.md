# 阶段二数据库脚本说明

本目录包含阶段二开发过程中创建的数据库相关脚本。

## 脚本文件说明

### 1. create_tables.py
**通用表创建脚本**
- 使用SQLAlchemy模型创建人员邮箱同步相关表
- 支持PersonnelSyncConfig和PersonnelSyncLog表创建
- 包含表结构验证功能

**使用方法：**
```bash
cd backend
python scripts/create_tables.py
```

### 2. create_email_request_table.py
**邮箱创建申请表脚本**
- 专门创建email_creation_requests表
- 包含索引创建
- 直接使用SQL语句创建，兼容性更好

**使用方法：**
```bash
cd backend
python scripts/create_email_request_table.py
```

### 3. migrate_personnel_sync_tables.py
**表结构迁移脚本**
- 用于更新现有表结构
- 添加缺失的字段
- 兼容现有数据

**使用方法：**
```bash
cd backend
python scripts/migrate_personnel_sync_tables.py
```

### 4. check_tables.py
**数据库表检查脚本**
- 检查数据库中的表结构
- 显示表字段信息
- 验证表是否存在

**使用方法：**
```bash
cd backend
python scripts/check_tables.py
```

### 5. check_specific_user.py
**特定用户检查脚本**
- 检查特定工号的用户信息
- 分析人员信息与邮箱数据的对应关系
- 用于调试同步逻辑

**使用方法：**
```bash
cd backend
python scripts/check_specific_user.py
```

## 执行顺序建议

### 首次部署
1. `create_email_request_table.py` - 创建邮箱申请表
2. `migrate_personnel_sync_tables.py` - 更新现有表结构
3. `check_tables.py` - 验证表结构

### 调试和维护
1. `check_specific_user.py` - 检查特定用户问题
2. `check_tables.py` - 验证数据库状态

## 注意事项

### 数据库备份
在运行任何数据库脚本前，建议备份数据库：
```bash
cp backend/sql_app.db backend/sql_app.db.backup
```

### 权限要求
- 确保对数据库文件有读写权限
- 确保Python环境包含必要的依赖

### 错误处理
- 所有脚本都包含错误处理机制
- 失败时会回滚更改
- 详细的日志输出便于调试

## 脚本特点

### create_email_request_table.py
```sql
CREATE TABLE IF NOT EXISTS email_creation_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_number VARCHAR(50) NOT NULL,
    user_name VARCHAR(100) NOT NULL,
    dept_name VARCHAR(200),
    job_title_name VARCHAR(100),
    mobile VARCHAR(20),
    status VARCHAR(20) DEFAULT 'pending',
    reason TEXT,
    requested_by VARCHAR(100) DEFAULT '系统自动检测',
    approved_by VARCHAR(100),
    approved_at DATETIME,
    created_email VARCHAR(100),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### migrate_personnel_sync_tables.py
- 智能检测现有表结构
- 只添加缺失的字段
- 保持现有数据完整性

### check_specific_user.py
- 支持检查任意工号
- 分析人员信息与邮箱数据的匹配情况
- 提供详细的分析报告

## 相关表结构

### personnel_sync_config
- 人员邮箱同步配置表
- 存储同步设置和调度信息

### personnel_sync_logs
- 人员邮箱同步日志表
- 记录每次同步的详细信息

### email_creation_requests
- 邮箱创建申请表
- 管理邮箱创建的审批流程

## 故障排除

### 常见错误

1. **数据库锁定**
   ```
   database is locked
   ```
   - 确保没有其他进程在使用数据库
   - 关闭所有数据库连接

2. **表已存在**
   ```
   table already exists
   ```
   - 使用`IF NOT EXISTS`语句
   - 检查表结构是否正确

3. **权限错误**
   ```
   permission denied
   ```
   - 检查文件权限
   - 确保有写入权限

### 调试技巧

1. **查看表结构**
   ```bash
   python scripts/check_tables.py
   ```

2. **检查特定数据**
   ```bash
   python scripts/check_specific_user.py
   ```

3. **验证迁移结果**
   ```bash
   python scripts/migrate_personnel_sync_tables.py
   ```

## 维护建议

1. **定期备份**：在执行脚本前备份数据库
2. **测试环境**：先在测试环境验证脚本
3. **版本控制**：保留脚本的版本历史
4. **文档更新**：及时更新脚本说明文档
