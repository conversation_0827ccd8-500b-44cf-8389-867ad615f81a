// 移动端样式变量定义

// 颜色变量 (支持Element Plus主题)
$primary-color: var(--el-color-primary, #409eff);
$success-color: var(--el-color-success, #67c23a);
$warning-color: var(--el-color-warning, #e6a23c);
$danger-color: var(--el-color-danger, #f56c6c);

// Vant 4主题变量
:root {
  --van-primary-color: #{$primary-color};
  --van-success-color: #{$success-color};
  --van-warning-color: #{$warning-color};
  --van-danger-color: #{$danger-color};
  
  // 自定义CSS变量
  --mobile-header-height: 50px;
  --mobile-tabbar-height: 50px;
  --mobile-safe-area-bottom: env(safe-area-inset-bottom);
  
  // 移动端弹窗高度变量（修复底部按钮不可见问题）
  // 使用更保守的高度值，确保在任何情况下底部按钮都完全可见
  --mobile-popup-max-height: 68svh;
  --mobile-popup-medium-height: 58svh;
  --mobile-popup-small-height: 48svh;
  --mobile-popup-large-height: 72svh;
}

// 间距变量
$padding-xs: 4px;
$padding-sm: 8px;
$padding-md: 16px;
$padding-lg: 24px;
$padding-xl: 32px;

// 字体变量
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;

// 圆角变量
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// 阴影变量
$box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
$box-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.15);
$box-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.2);

// 动画变量
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease; 