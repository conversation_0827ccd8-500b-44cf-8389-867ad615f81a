#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查腾讯企业邮箱API Token状态脚本
用于诊断和修复token相关问题
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))

from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService
from app.models.email import EmailConfig


async def check_email_configs():
    """检查所有邮箱配置"""
    print("=== 检查邮箱配置 ===")
    
    db = SessionLocal()
    try:
        configs = db.query(EmailConfig).filter(EmailConfig.is_active == True).all()
        
        if not configs:
            print("❌ 未找到任何活跃的邮箱配置")
            return []
        
        print(f"✅ 找到 {len(configs)} 个活跃配置:")
        for config in configs:
            print(f"   - 应用: {config.app_name}")
            print(f"     企业ID: {config.corp_id}")
            print(f"     Token: {'已设置' if config.access_token else '未设置'}")
            print(f"     过期时间: {config.token_expires_at}")
            print(f"     状态: {'活跃' if config.is_active else '非活跃'}")
            print()
        
        return configs
    finally:
        db.close()


async def test_token_validity(config: EmailConfig):
    """测试单个配置的token有效性"""
    print(f"\n=== 测试配置: {config.app_name} ===")
    
    db = SessionLocal()
    try:
        api_service = TencentEmailAPIService(db, app_name=config.app_name)
        
        # 1. 检查当前token状态
        print("1. 检查当前token状态...")
        if config.access_token:
            print(f"   当前token: {config.access_token[:20]}...")
            if config.token_expires_at:
                if config.token_expires_at > datetime.now(timezone.utc):
                    print(f"   ✅ Token未过期 (过期时间: {config.token_expires_at})")
                else:
                    print(f"   ⚠️ Token已过期 (过期时间: {config.token_expires_at})")
            else:
                print("   ⚠️ 未设置过期时间")
        else:
            print("   ❌ 未设置token")
        
        # 2. 尝试获取新token
        print("\n2. 尝试获取访问令牌...")
        try:
            token = await api_service.get_access_token()
            print(f"   ✅ 成功获取token: {token[:20]}...")
        except Exception as e:
            print(f"   ❌ 获取token失败: {str(e)}")
            return False
        
        # 3. 测试API调用
        print("\n3. 测试API调用...")
        try:
            result = await api_service.get_department_list()
            if result.errcode == 0:
                print("   ✅ API调用成功")
                return True
            else:
                print(f"   ❌ API调用失败: {result.errmsg} (错误码: {result.errcode})")
                return False
        except Exception as e:
            print(f"   ❌ API调用异常: {str(e)}")
            return False
            
    finally:
        db.close()


async def refresh_all_tokens():
    """刷新所有配置的token"""
    print("\n=== 刷新所有Token ===")
    
    db = SessionLocal()
    try:
        configs = db.query(EmailConfig).filter(EmailConfig.is_active == True).all()
        
        for config in configs:
            print(f"\n刷新配置: {config.app_name}")
            try:
                # 清除现有token
                config.access_token = None
                config.token_expires_at = None
                db.commit()
                
                # 重新获取
                api_service = TencentEmailAPIService(db, app_name=config.app_name)
                token = await api_service.get_access_token()
                print(f"   ✅ 成功刷新token: {token[:20]}...")
                
            except Exception as e:
                print(f"   ❌ 刷新失败: {str(e)}")
                
    finally:
        db.close()


async def diagnose_issue():
    """诊断常见问题"""
    print("\n=== 问题诊断 ===")
    
    # 检查配置
    configs = await check_email_configs()
    if not configs:
        return
    
    # 测试每个配置
    all_success = True
    for config in configs:
        success = await test_token_validity(config)
        if not success:
            all_success = False
    
    # 给出建议
    print("\n=== 诊断结果与建议 ===")
    if all_success:
        print("✅ 所有配置都正常工作")
    else:
        print("❌ 发现问题，建议:")
        print("   1. 检查企业ID和密钥是否正确")
        print("   2. 确认网络连接正常")
        print("   3. 验证腾讯企业邮箱应用权限")
        print("   4. 尝试刷新所有token")


async def main():
    """主函数"""
    print("腾讯企业邮箱API Token检查工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "check":
            await diagnose_issue()
        elif command == "refresh":
            await refresh_all_tokens()
        elif command == "config":
            await check_email_configs()
        else:
            print("未知命令，支持的命令: check, refresh, config")
    else:
        print("用法:")
        print("  python check_email_token.py check    # 完整诊断")
        print("  python check_email_token.py refresh  # 刷新所有token")
        print("  python check_email_token.py config   # 查看配置")


if __name__ == "__main__":
    asyncio.run(main()) 