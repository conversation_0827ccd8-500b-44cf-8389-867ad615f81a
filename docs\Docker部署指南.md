# OPS平台Docker部署指南

## 概述

本文档介绍如何使用Docker部署OPS平台，包括后端API服务、前端应用、PostgreSQL数据库和Redis缓存服务。

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少4GB可用内存
- 至少10GB可用磁盘空间

## 快速开始

### 1. 克隆项目

```bash
git clone <your-repository-url>
cd OPS-Platform
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp env.example .env

# 编辑配置文件，根据实际情况修改
vim .env
```

### 3. 启动服务

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x scripts/docker-start.sh

# 启动服务
./scripts/docker-start.sh
```

#### Windows PowerShell
```powershell
# 启动服务
.\scripts\docker-start.ps1
```

### 4. 访问应用

- 前端应用: http://localhost
- 后端API: http://localhost:8000
- 数据库: localhost:5432
- Redis: localhost:6379

## 详细配置

### 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| POSTGRES_DB | PostgreSQL数据库名 | ops_platform |
| POSTGRES_USER | PostgreSQL用户名 | ops_user |
| POSTGRES_PASSWORD | PostgreSQL密码 | ops_password |
| REDIS_PASSWORD | Redis密码 | redis_password |
| TZ | 时区设置 | Asia/Shanghai |

### 服务配置

#### 后端服务 (backend)
- 端口: 8000
- 基础镜像: Python 3.11-slim
- 健康检查: /health端点
- 用户: 非root用户 (opsuser)

#### 前端服务 (frontend)
- 端口: 80
- 基础镜像: nginx:alpine
- 构建工具: Node.js 18-alpine
- 用户: 非root用户 (nginxuser)

#### 数据库服务 (postgres)
- 端口: 5432
- 基础镜像: postgres:15-alpine
- 数据持久化: postgres_data卷
- 健康检查: pg_isready

#### 缓存服务 (redis)
- 端口: 6379
- 基础镜像: redis:7-alpine
- 数据持久化: redis_data卷
- 密码保护: 启用

## 常用命令

### 服务管理

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 数据库操作

```bash
# 进入PostgreSQL容器
docker-compose exec postgres psql -U ops_user -d ops_platform

# 运行数据库迁移
docker-compose run --rm migration

# 备份数据库
docker-compose exec postgres pg_dump -U ops_user ops_platform > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U ops_user -d ops_platform < backup.sql
```

### 镜像管理

```bash
# 重新构建镜像
docker-compose build --no-cache

# 查看镜像大小
docker images | grep ops

# 清理未使用的镜像
docker image prune -f
```

## 生产环境部署

### 1. 安全配置

```bash
# 修改默认密码
vim .env
POSTGRES_PASSWORD=your-strong-password
REDIS_PASSWORD=your-strong-redis-password
SECRET_KEY=your-very-long-secret-key
```

### 2. 网络配置

```bash
# 修改端口映射（避免使用默认端口）
vim docker-compose.yml
ports:
  - "8080:8000"  # 后端API
  - "8081:80"    # 前端应用
  - "5433:5432"  # 数据库
  - "6380:6379"  # Redis
```

### 3. 资源限制

```yaml
# 在docker-compose.yml中添加资源限制
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
```

### 4. 日志管理

```yaml
# 配置日志轮转
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 监控和维护

### 健康检查

所有服务都配置了健康检查，可以通过以下命令查看：

```bash
# 查看健康状态
docker-compose ps

# 查看健康检查日志
docker inspect ops_backend | grep -A 10 Health
```

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df

# 查看网络使用情况
docker network ls
```

### 备份策略

```bash
# 创建备份脚本
vim scripts/backup.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"

mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec -T postgres pg_dump -U ops_user ops_platform > $BACKUP_DIR/db_$DATE.sql

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz ./backend/uploads/

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细错误信息
docker-compose logs [服务名]

# 检查端口占用
netstat -tulpn | grep :8000

# 检查磁盘空间
df -h
```

#### 2. 数据库连接失败

```bash
# 检查PostgreSQL状态
docker-compose exec postgres pg_isready -U ops_user

# 检查网络连接
docker-compose exec backend ping postgres

# 查看数据库日志
docker-compose logs postgres
```

#### 3. 前端无法访问

```bash
# 检查nginx配置
docker-compose exec frontend nginx -t

# 检查静态文件
docker-compose exec frontend ls -la /usr/share/nginx/html/

# 查看nginx日志
docker-compose logs frontend
```

### 日志分析

```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看特定时间段的日志
docker-compose logs --since="2024-01-01T00:00:00" backend

# 搜索错误日志
docker-compose logs backend | grep -i error
```

## 扩展和优化

### 1. 负载均衡

```yaml
# 使用nginx作为负载均衡器
services:
  nginx-lb:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
      - frontend
```

### 2. 缓存优化

```yaml
# 添加Redis集群
services:
  redis-cluster:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000
    ports:
      - "7000-7005:7000-7005"
```

### 3. 数据库优化

```yaml
# PostgreSQL性能调优
services:
  postgres:
    environment:
      POSTGRES_INITDB_ARGS: "--data-checksums"
      POSTGRES_SHARED_PRELOAD_LIBRARIES: "pg_stat_statements"
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
```

## 联系和支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查Docker和Docker Compose版本
3. 查看服务日志获取详细错误信息
4. 联系开发团队获取技术支持

---

**注意**: 生产环境部署前，请务必：
- 修改所有默认密码
- 配置防火墙规则
- 设置SSL证书
- 配置监控和告警
- 制定备份和恢复策略
