# 邮箱同步故障排除指南

## 常见错误：602005 权限错误

### 错误描述
```
errcode:602005, errmsg: no privilege to access app
API调用失败: no privilege to access app
```

### 错误原因
这个错误表示"功能设置"应用缺少对特定用户的查看权限。腾讯企业邮箱的用户选项获取API需要应用对目标用户有明确的查看权限。

### 解决步骤

#### 1. 运行权限检查脚本
首先运行我们提供的权限检查脚本来诊断问题：

```bash
cd backend
python scripts/check_email_permissions.py
```

该脚本会：
- 检查数据库中的邮箱配置
- 测试"功能设置"应用的基础权限
- 对几个用户进行权限获取测试
- 提供详细的诊断信息

#### 2. 登录腾讯企业邮箱管理后台
1. 访问 [腾讯企业邮箱管理后台](https://exmail.qq.com/manager/)
2. 使用管理员账号登录

#### 3. 配置"功能设置"应用权限
1. 进入 **管理工具** → **第三方应用**
2. 找到名为 **"功能设置"** 的应用
3. 点击 **管理** 或 **编辑**

#### 4. 检查并配置权限
确保"功能设置"应用具有以下权限：

##### 基础权限
- ✅ **通讯录管理权限**
- ✅ **成员管理权限**
- ✅ **部门管理权限**

##### 关键权限（针对602005错误）
- ✅ **用户选项管理权限**
- ✅ **查看指定成员权限**
- ✅ **成员详细信息访问权限**

#### 5. 配置用户访问范围
在应用配置中，确保"功能设置"应用的访问范围包含：
- **全部成员** 或
- **指定部门/成员**（包含所有需要同步的用户）

#### 6. 保存配置并等待生效
- 点击 **保存** 按钮
- 等待 5-10 分钟让配置生效
- 重新运行权限检查脚本验证

### 权限配置详细说明

#### 应用权限类型说明
| 权限名称 | 权限说明 | 是否必需 |
|---------|---------|----------|
| 通讯录管理权限 | 获取部门和成员列表 | ✅ 必需 |
| 用户选项管理权限 | 获取用户登录权限等选项 | ✅ 必需 |
| 查看指定成员权限 | 查看特定成员的详细信息 | ✅ 必需 |
| 成员管理权限 | 创建、编辑、删除成员 | ⚠️ 可选 |

#### 访问范围配置
- **全部成员**：推荐设置，确保应用能访问所有用户
- **指定部门**：仅访问特定部门的用户
- **指定成员**：仅访问特定的用户列表

### 验证配置是否正确

#### 方法1：使用检查脚本
```bash
cd backend
python scripts/check_email_permissions.py
```

#### 方法2：手动触发同步
1. 登录OPS平台
2. 进入 **邮箱管理** → **成员管理**
3. 点击 **同步** 按钮
4. 查看同步日志

#### 方法3：检查API响应
在同步过程中，系统会记录详细的错误信息：
```
2024-01-20 10:30:15 - ERROR - 获取用户权限失败: userid=user001, errcode=602005, errmsg=no privilege to access app
```

### 其他注意事项

#### 1. 权限生效时间
- 腾讯企业邮箱权限修改后，通常需要 5-10 分钟生效
- 如果仍有问题，可以尝试重新保存应用配置

#### 2. 应用状态检查
确保"功能设置"应用状态为：
- ✅ **启用状态**
- ✅ **正常运行**
- ✅ **未被暂停**

#### 3. Corp ID和Secret检查
确保数据库中的配置信息正确：
```sql
SELECT id, app_name, corp_id, is_active 
FROM email_configs 
WHERE app_name = '功能设置' AND is_active = true;
```

#### 4. 网络连接检查
确保服务器能正常访问腾讯企业邮箱API：
```bash
curl -I https://api.exmail.qq.com/
```

### 联系支持

如果按照上述步骤仍无法解决问题，请收集以下信息：

1. **权限检查脚本输出**
2. **应用配置截图**
3. **同步错误日志**
4. **数据库配置信息**（隐藏敏感信息）

## 其他常见问题

### 错误：access_token获取失败
**原因**：Corp ID或Secret配置错误
**解决**：检查数据库中的邮箱配置信息

### 错误：部门列表获取失败
**原因**：应用缺少通讯录管理权限
**解决**：在应用配置中启用通讯录管理权限

### 错误：连接超时
**原因**：网络连接问题或API服务异常
**解决**：检查网络连接，确认API服务状态

### 同步部分成功
**情况**：部分用户同步成功，部分失败
**原因**：应用对部分用户有权限，对部分用户无权限
**解决**：调整应用的访问范围配置 