#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_api():
    # 登录获取token
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = requests.post(f'{BASE_URL}/api/v1/auth/login', data=login_data)
    
    if response.status_code != 200:
        print(f'登录失败: {response.text}')
        return
    
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print("登录成功")
    
    # 测试统计API
    print("\n=== 测试邮箱创建申请统计 ===")
    stats_response = requests.get(f'{BASE_URL}/api/v1/email-creation-requests/requests/stats', headers=headers)
    print(f'状态码: {stats_response.status_code}')
    
    if stats_response.status_code == 200:
        stats = stats_response.json()
        print("统计信息:")
        print(f"  待审批申请: {stats.get('pending_count', 0)}")
        print(f"  已批准申请: {stats.get('approved_count', 0)}")
        print(f"  已拒绝申请: {stats.get('rejected_count', 0)}")
        print(f"  已创建申请: {stats.get('created_count', 0)}")
        print(f"  总申请数: {stats.get('total_requests', 0)}")
        print(f"  当前活跃邮箱: {stats.get('active_email_count', 0)}")
        print(f"  邮箱配额: {stats.get('email_quota', 0)}")
        print(f"  可用配额: {stats.get('available_quota', 0)}")
    else:
        print(f'错误: {stats_response.text}')
    
    # 测试同步配置
    print("\n=== 测试同步配置 ===")
    config_response = requests.get(f'{BASE_URL}/api/v1/personnel-email-sync/sync/config', headers=headers)
    print(f'状态码: {config_response.status_code}')
    
    if config_response.status_code == 200:
        config = config_response.json()
        print(f"同步配置: 启用={config.get('enabled')}, 状态={config.get('sync_status')}")
    else:
        print(f'错误: {config_response.text}')

if __name__ == "__main__":
    test_api()
