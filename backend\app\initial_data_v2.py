#!/usr/bin/env python3
"""
OPS Platform 标准化数据库初始化系统 v2.0
安全、幂等、生产就绪的数据库初始化
"""

from app.database import SessionLocal
from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role
from app.crud.user import user_crud
from app.crud.permission import permission_crud
from app.crud.role import role_crud
from app.schemas.user import UserCreate
from app.schemas.permission import PermissionCreate
from app.schemas.role import RoleCreate
from app.core.security import get_password_hash
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_permissions(db):
    """创建标准权限体系（幂等操作）"""
    logger.info("📋 开始创建/检查权限...")
    
    # 定义完整的权限体系
    standard_permissions = [
        # 系统管理模块 (system)
        PermissionCreate(code="system:view", name="查看系统设置", module="system", description="查看系统设置页面和基本信息"),
        PermissionCreate(code="system:config", name="配置系统参数", module="system", description="修改系统配置参数"),
        PermissionCreate(code="system:user:view", name="查看用户管理", module="system", description="查看用户列表和详情"),
        PermissionCreate(code="system:user:add", name="添加用户", module="system", description="创建新用户"),
        PermissionCreate(code="system:user:edit", name="编辑用户", module="system", description="编辑现有用户信息"),
        PermissionCreate(code="system:user:delete", name="删除用户", module="system", description="删除用户"),
        PermissionCreate(code="system:role:view", name="查看角色", module="system", description="查看角色列表和详情"),
        PermissionCreate(code="system:role:add", name="添加角色", module="system", description="创建新角色"),
        PermissionCreate(code="system:role:edit", name="编辑角色", module="system", description="编辑现有角色信息"),
        PermissionCreate(code="system:role:delete", name="删除角色", module="system", description="删除角色"),
        PermissionCreate(code="system:permission:view", name="查看权限", module="system", description="查看权限列表和详情"),
        PermissionCreate(code="system:permission:add", name="添加权限", module="system", description="创建新权限"),
        PermissionCreate(code="system:permission:edit", name="编辑权限", module="system", description="编辑现有权限信息"),
        PermissionCreate(code="system:permission:delete", name="删除权限", module="system", description="删除权限"),
        PermissionCreate(code="system:permission:assign", name="分配权限", module="system", description="分配权限给角色"),

        # 资产管理模块 (asset)
        PermissionCreate(code="asset:view", name="查看资产", module="asset", description="查看资产列表和详情"),
        PermissionCreate(code="asset:add", name="添加资产", module="asset", description="创建新资产"),
        PermissionCreate(code="asset:edit", name="编辑资产", module="asset", description="编辑现有资产信息"),
        PermissionCreate(code="asset:delete", name="删除资产", module="asset", description="删除资产"),
        PermissionCreate(code="asset:import", name="导入资产", module="asset", description="从外部导入资产数据"),
        PermissionCreate(code="asset:export", name="导出资产", module="asset", description="导出资产数据"),
        PermissionCreate(code="asset:field:manage", name="管理资产字段", module="asset", description="管理资产自定义字段"),

        # 盘点管理模块 (inventory)
        PermissionCreate(code="inventory:view", name="查看盘点任务", module="inventory", description="查看盘点任务列表和详情"),
        PermissionCreate(code="inventory:add", name="创建盘点任务", module="inventory", description="创建新的盘点任务"),
        PermissionCreate(code="inventory:edit", name="编辑盘点任务", module="inventory", description="编辑现有盘点任务"),
        PermissionCreate(code="inventory:delete", name="删除盘点任务", module="inventory", description="删除盘点任务"),
        PermissionCreate(code="inventory:record:view", name="查看盘点记录", module="inventory", description="查看盘点记录"),
        PermissionCreate(code="inventory:record:edit", name="编辑盘点记录", module="inventory", description="编辑盘点记录"),
        PermissionCreate(code="inventory:report", name="查看盘点报告", module="inventory", description="查看盘点统计报告"),

        # AD管理模块 (ad)
        PermissionCreate(code="ad:view", name="查看AD用户", module="ad", description="查看AD用户列表和详情"),
        PermissionCreate(code="ad:sync", name="AD同步", module="ad", description="AD同步配置和执行"),
        PermissionCreate(code="ad:config", name="AD配置", module="ad", description="AD服务器配置管理"),
        PermissionCreate(code="ad:user:manage", name="管理AD用户", module="ad", description="管理AD用户账号"),
        PermissionCreate(code="ad:group:manage", name="管理AD组", module="ad", description="管理AD安全组"),

        # 泛微集成模块 (ecology)
        PermissionCreate(code="ecology:view", name="查看泛微用户", module="ecology", description="查看泛微用户和组织数据"),
        PermissionCreate(code="ecology:sync", name="同步泛微数据", module="ecology", description="同步泛微用户和组织数据"),
        PermissionCreate(code="ecology:config", name="配置泛微连接", module="ecology", description="配置泛微系统连接设置"),

        # 基础信息模块 (basic-info)
        PermissionCreate(code="basic-info:view", name="查看基础信息", module="basic-info", description="查看基础信息页面"),
        PermissionCreate(code="basic-info:personnel:view", name="查看人员信息", module="basic-info", description="查看人员基本信息"),
        PermissionCreate(code="basic-info:personnel:edit", name="编辑人员信息", module="basic-info", description="编辑人员基本信息"),

        # 终端管理模块 (terminal)
        PermissionCreate(code="terminal:view", name="查看终端", module="terminal", description="查看终端列表和详情"),
        PermissionCreate(code="terminal:add", name="添加终端", module="terminal", description="手动添加终端"),
        PermissionCreate(code="terminal:edit", name="编辑终端", module="terminal", description="编辑终端信息"),
        PermissionCreate(code="terminal:delete", name="删除终端", module="terminal", description="删除终端"),
        PermissionCreate(code="terminal:command:send", name="发送命令", module="terminal", description="向终端发送命令"),
        PermissionCreate(code="terminal:command:view", name="查看命令", module="terminal", description="查看终端命令历史"),
        PermissionCreate(code="terminal:software:view", name="查看软件", module="terminal", description="查看终端软件列表"),
        PermissionCreate(code="terminal:software:manage", name="管理软件", module="terminal", description="管理软件合规性和备注"),
        PermissionCreate(code="terminal:agent:view", name="查看Agent", module="terminal", description="查看Agent版本列表"),
        PermissionCreate(code="terminal:agent:manage", name="管理Agent", module="terminal", description="管理Agent版本和升级"),

        # 邮箱管理模块 (email)
        PermissionCreate(code="email:view", name="查看邮箱管理", module="email", description="查看邮箱管理页面和基本信息"),
        PermissionCreate(code="email:config:view", name="查看邮箱配置", module="email", description="查看邮箱API配置列表"),
        PermissionCreate(code="email:config:create", name="创建邮箱配置", module="email", description="创建新的邮箱API配置"),
        PermissionCreate(code="email:config:update", name="更新邮箱配置", module="email", description="编辑现有邮箱API配置"),
        PermissionCreate(code="email:config:delete", name="删除邮箱配置", module="email", description="删除邮箱API配置"),
        PermissionCreate(code="email:department:view", name="查看邮箱部门", module="email", description="查看邮箱部门列表和详情"),
        PermissionCreate(code="email:department:create", name="创建邮箱部门", module="email", description="创建新的邮箱部门"),
        PermissionCreate(code="email:department:update", name="更新邮箱部门", module="email", description="编辑现有邮箱部门信息"),
        PermissionCreate(code="email:department:delete", name="删除邮箱部门", module="email", description="删除邮箱部门"),
        PermissionCreate(code="email:department:sync", name="同步邮箱部门", module="email", description="与腾讯企业邮箱同步部门数据"),
        PermissionCreate(code="email:member:view", name="查看邮箱成员", module="email", description="查看邮箱成员列表和详情"),
        PermissionCreate(code="email:member:create", name="创建邮箱成员", module="email", description="创建新的邮箱成员"),
        PermissionCreate(code="email:member:update", name="更新邮箱成员", module="email", description="编辑现有邮箱成员信息"),
        PermissionCreate(code="email:member:delete", name="删除邮箱成员", module="email", description="删除邮箱成员"),
        PermissionCreate(code="email:member:sync", name="同步邮箱成员", module="email", description="与腾讯企业邮箱同步成员数据"),
        PermissionCreate(code="email:member:permissions", name="管理成员登录权限", module="email", description="管理邮箱成员的登录权限设置"),
        PermissionCreate(code="email:member:export", name="导出邮箱成员", module="email", description="导出邮箱成员数据"),
        PermissionCreate(code="email:group:view", name="查看邮箱群组", module="email", description="查看邮箱群组列表和详情"),
        PermissionCreate(code="email:group:create", name="创建邮箱群组", module="email", description="创建新的邮箱群组"),
        PermissionCreate(code="email:group:update", name="更新邮箱群组", module="email", description="编辑现有邮箱群组信息"),
        PermissionCreate(code="email:group:delete", name="删除邮箱群组", module="email", description="删除邮箱群组"),
        PermissionCreate(code="email:group:sync", name="同步邮箱群组", module="email", description="与腾讯企业邮箱同步群组数据"),
        PermissionCreate(code="email:tag:view", name="查看邮箱标签", module="email", description="查看邮箱标签列表和详情"),
        PermissionCreate(code="email:tag:create", name="创建邮箱标签", module="email", description="创建新的邮箱标签"),
        PermissionCreate(code="email:tag:update", name="更新邮箱标签", module="email", description="编辑现有邮箱标签信息"),
        PermissionCreate(code="email:tag:delete", name="删除邮箱标签", module="email", description="删除邮箱标签"),
        PermissionCreate(code="email:tag:sync", name="同步邮箱标签", module="email", description="与腾讯企业邮箱同步标签数据"),
        PermissionCreate(code="email:sync:view", name="查看邮箱同步", module="email", description="查看邮箱数据同步状态和日志"),
        PermissionCreate(code="email:sync:all", name="执行全量同步", module="email", description="执行邮箱数据全量同步"),
        PermissionCreate(code="email:sync:config", name="配置邮箱同步", module="email", description="配置邮箱数据同步策略和参数"),

        # LDAP认证模块 (ldap)
        PermissionCreate(code="ldap:config:view", name="查看LDAP配置", module="ldap", description="查看LDAP认证配置列表和详情"),
        PermissionCreate(code="ldap:config:manage", name="管理LDAP配置", module="ldap", description="创建、编辑、删除LDAP认证配置"),
        PermissionCreate(code="ldap:config:test", name="测试LDAP连接", module="ldap", description="测试LDAP服务器连接状态"),
    ]

    # 幂等创建权限
    created_permissions = []
    updated_permissions = []
    
    for perm_data in standard_permissions:
        existing_perm = permission_crud.get_by_code(db, code=perm_data.code)
        
        if existing_perm:
            # 更新现有权限（以防有变更）
            update_data = {
                "name": perm_data.name,
                "description": perm_data.description,
                "module": perm_data.module
            }
            updated_perm = permission_crud.update(db, db_obj=existing_perm, obj_in=update_data)
            updated_permissions.append(updated_perm)
        else:
            # 创建新权限
            new_perm = permission_crud.create(db, obj_in=perm_data)
            created_permissions.append(new_perm)
    
    logger.info(f"  ✅ 权限检查完成: 新建{len(created_permissions)}个, 更新{len(updated_permissions)}个")
    
    # 获取所有权限用于返回
    all_permissions = permission_crud.get_multi(db, limit=1000)
    return all_permissions

def create_roles(db, all_permissions):
    """创建标准角色体系（幂等操作）"""
    logger.info("👥 开始创建/检查角色...")
    
    # 按权限代码映射权限ID
    perm_map = {perm.code: perm.id for perm in all_permissions}
    
    # 定义标准角色
    standard_roles = [
        {
            "code": "super_admin",
            "name": "超级管理员",
            "description": "拥有所有权限的管理员角色",
            "is_default": 1,
            "permissions": [perm.code for perm in all_permissions]  # 所有权限
        },
        {
            "code": "asset_admin",
            "name": "资产管理员",
            "description": "管理资产和盘点的角色",
            "is_default": 1,
            "permissions": [
                "asset:view", "asset:add", "asset:edit", "asset:delete", 
                "asset:import", "asset:export", "asset:field:manage",
                "inventory:view", "inventory:add", "inventory:edit", 
                "inventory:record:view", "inventory:record:edit", "inventory:report",
                "basic-info:view", "basic-info:personnel:view"
            ]
        },
        {
            "code": "normal_user",
            "name": "普通用户",
            "description": "普通用户角色",
            "is_default": 1,
            "permissions": [
                "asset:view", "inventory:record:view", "inventory:record:edit",
                "basic-info:view"
            ]
        }
    ]
    
    created_roles = []
    updated_roles = []
    
    for role_info in standard_roles:
        existing_role = role_crud.get_by_code(db, code=role_info["code"])
        
        # 获取权限ID列表
        permission_ids = []
        for perm_code in role_info["permissions"]:
            if perm_code in perm_map:
                permission_ids.append(perm_map[perm_code])
        
        if existing_role:
            # 更新现有角色
            update_data = {
                "name": role_info["name"],
                "description": role_info["description"],
                "is_default": role_info["is_default"]
            }
            updated_role = role_crud.update_with_permissions(
                db, db_obj=existing_role, obj_in=update_data, permission_ids=permission_ids
            )
            updated_roles.append(updated_role)
            logger.info(f"  📝 更新角色: {role_info['name']} ({len(permission_ids)}个权限)")
        else:
            # 创建新角色
            role_data = RoleCreate(
                code=role_info["code"],
                name=role_info["name"],
                description=role_info["description"],
                is_default=role_info["is_default"]
            )
            new_role = role_crud.create_with_permissions(
                db, obj_in=role_data, permission_ids=permission_ids
            )
            created_roles.append(new_role)
            logger.info(f"  ✨ 创建角色: {role_info['name']} ({len(permission_ids)}个权限)")
    
    logger.info(f"  ✅ 角色检查完成: 新建{len(created_roles)}个, 更新{len(updated_roles)}个")
    return created_roles + updated_roles

def create_admin_user(db, roles):
    """创建管理员用户（幂等操作）"""
    logger.info("👤 开始创建/检查管理员用户...")
    
    # 查找是否已有admin用户
    admin_user = user_crud.get_by_username(db, username="admin")
    
    # 查找super_admin角色
    super_admin_role = None
    for role in roles:
        if role.code == "super_admin":
            super_admin_role = role
            break
    
    if not super_admin_role:
        logger.error("❌ 未找到super_admin角色，无法创建管理员用户")
        return None
    
    if admin_user:
        # 检查用户是否有super_admin角色
        has_super_admin = any(role.id == super_admin_role.id for role in admin_user.roles)
        
        if not has_super_admin:
            # 添加super_admin角色
            role_crud.assign_roles_to_user(db, user_id=admin_user.id, role_ids=[super_admin_role.id])
            logger.info("  📝 为现有admin用户添加super_admin角色")
        else:
            logger.info("  ✅ admin用户已存在且具有正确角色")
        
        return admin_user
    else:
        # 创建新的admin用户
        admin_data = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            is_active=True,
            is_superuser=True
        )
        new_admin = user_crud.create(db, obj_in=admin_data)
        
        # 分配super_admin角色
        role_crud.assign_roles_to_user(db, user_id=new_admin.id, role_ids=[super_admin_role.id])
        
        logger.info("  ✨ 创建admin用户并分配super_admin角色")
        logger.info("  🔑 默认登录: admin / admin123")
        
        return new_admin

def cleanup_deprecated_roles(db):
    """清理废弃的角色（可选操作）"""
    logger.info("🧹 检查废弃角色...")
    
    # 查找旧的admin角色（不是super_admin）
    deprecated_roles = ["admin"]  # 添加其他需要清理的角色代码
    
    for role_code in deprecated_roles:
        old_role = role_crud.get_by_code(db, code=role_code)
        if old_role:
            # 检查是否有用户使用此角色
            users_with_role = []
            for user in user_crud.get_multi(db):
                if any(r.id == old_role.id for r in user.roles):
                    users_with_role.append(user.username)
            
            if users_with_role:
                logger.info(f"  ⚠️ 角色 {role_code} 仍被使用: {', '.join(users_with_role)}")
                logger.info(f"    请手动迁移这些用户到新角色后再删除")
            else:
                # 安全删除无用户的废弃角色
                role_crud.remove(db, id=old_role.id)
                logger.info(f"  🗑️ 删除废弃角色: {old_role.name}")

def verify_initialization(db):
    """验证初始化结果"""
    logger.info("🔍 验证初始化结果...")
    
    # 统计权限
    permissions = permission_crud.get_multi(db, limit=1000)
    perm_by_module = {}
    for perm in permissions:
        module = perm.module
        perm_by_module[module] = perm_by_module.get(module, 0) + 1
    
    logger.info(f"  📋 权限总数: {len(permissions)}")
    for module, count in perm_by_module.items():
        logger.info(f"    {module}: {count}个")
    
    # 统计角色
    roles = role_crud.get_multi(db)
    logger.info(f"  👥 角色总数: {len(roles)}")
    for role in roles:
        perm_count = len(role.permissions) if role.permissions else 0
        logger.info(f"    {role.name} ({role.code}): {perm_count}个权限")
    
    # 检查管理员用户
    admin_user = user_crud.get_by_username(db, username="admin")
    if admin_user:
        role_names = [role.name for role in admin_user.roles]
        logger.info(f"  👤 管理员用户: admin (角色: {', '.join(role_names)})")
    
    logger.info("  ✅ 验证完成")

def init_database_v2(skip_cleanup=False):
    """
    标准化数据库初始化 v2.0
    
    Args:
        skip_cleanup: 是否跳过废弃角色清理（默认False）
    """
    logger.info("🚀 开始标准化数据库初始化 v2.0")
    logger.info("=" * 60)
    
    db = SessionLocal()
    try:
        # 第一步：创建/检查权限
        all_permissions = create_permissions(db)
        
        # 第二步：创建/检查角色
        roles = create_roles(db, all_permissions)
        
        # 第三步：创建/检查管理员用户
        admin_user = create_admin_user(db, roles)
        
        # 第四步：清理废弃角色（可选）
        if not skip_cleanup:
            cleanup_deprecated_roles(db)
        
        # 第五步：验证结果
        verify_initialization(db)
        
        db.commit()
        
        logger.info("=" * 60)
        logger.info("🎉 数据库初始化完成！")
        logger.info("💡 系统已准备就绪，可以正常使用")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 初始化失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="OPS Platform 数据库初始化 v2.0")
    parser.add_argument("--skip-cleanup", action="store_true", help="跳过废弃角色清理")
    args = parser.parse_args()
    
    success = init_database_v2(skip_cleanup=args.skip_cleanup)
    exit(0 if success else 1) 