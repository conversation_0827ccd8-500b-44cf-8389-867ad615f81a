from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class EmailDepartment(Base):
    """邮箱部门模型"""
    __tablename__ = "email_departments"

    id = Column(Integer, primary_key=True, index=True)
    dept_id = Column(String(50), unique=True, index=True, comment="部门ID")
    name = Column(String(100), nullable=False, comment="部门名称")
    parent_id = Column(String(50), nullable=True, comment="父部门ID")
    order = Column(Integer, default=0, comment="排序")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    members = relationship("EmailMember", back_populates="department", cascade="all, delete-orphan")

class EmailMember(Base):
    """邮箱成员模型"""
    __tablename__ = "email_members"

    id = Column(Integer, primary_key=True, index=True)
    extid = Column(String(50), unique=True, index=True, comment="工号")
    email = Column(String(100), unique=True, index=True, comment="邮箱地址（对应腾讯企业邮箱API的userid字段）")
    name = Column(String(100), nullable=False, comment="姓名")
    department_id = Column(String(50), ForeignKey("email_departments.dept_id"), nullable=False, comment="部门ID")
    position = Column(String(100), nullable=True, comment="职位")
    mobile = Column(String(50), nullable=True, comment="手机号")
    tel = Column(String(30), nullable=True, comment="固定电话")
    cpwd_login = Column(Integer, default=1, comment="是否允许使用密码登录")
    force_secure_login = Column(Integer, default=0, comment="强制启用安全登录 (0:关闭, 1:开启)")
    imap_smtp_enabled = Column(Integer, default=1, comment="IMAP/SMTP服务 (0:关闭, 1:开启)")
    pop_smtp_enabled = Column(Integer, default=1, comment="POP/SMTP服务 (0:关闭, 1:开启)")
    secure_login_enabled = Column(Integer, default=0, comment="是否启用安全登录 (0:关闭, 1:开启)")
    extattr = Column(JSON, nullable=True, comment="扩展属性")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    department = relationship("EmailDepartment", back_populates="members")
    
    @property
    def userid(self) -> str:
        """
        返回腾讯企业邮箱API所需的userid值
        
        注意：腾讯企业邮箱API中的userid字段值为邮箱地址格式
        详见：https://exmail.qq.com/qy_mng_logic/doc#10001
        """
        return self.email

class EmailTag(Base):
    """邮箱标签模型"""
    __tablename__ = "email_tags"

    id = Column(Integer, primary_key=True, index=True)
    tagid = Column(Integer, unique=True, index=True, comment="标签ID")
    tagname = Column(String(100), nullable=False, comment="标签名称")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

class EmailGroup(Base):
    """邮件群组模型"""
    __tablename__ = "email_groups"

    id = Column(Integer, primary_key=True, index=True)
    groupid = Column(String(100), unique=True, index=True, comment="群组ID")
    groupname = Column(String(100), nullable=False, comment="群组名称")
    userlist = Column(Text, nullable=True, comment="成员列表")
    groupdesc = Column(Text, nullable=True, comment="群组描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

class EmailConfig(Base):
    """邮箱配置模型"""
    __tablename__ = "email_configs"

    id = Column(Integer, primary_key=True, index=True)
    corp_id = Column(String(100), nullable=False, comment="企业ID")
    corp_secret = Column(String(200), nullable=False, comment="企业密钥")
    app_name = Column(String(100), nullable=True, comment="应用名称")
    app_key = Column(String(50), nullable=True, comment="应用标识")
    access_token = Column(String(200), nullable=True, comment="访问令牌")
    token_expires_at = Column(DateTime(timezone=True), nullable=True, comment="令牌过期时间")
    api_base_url = Column(String(200), default="https://api.exmail.qq.com/cgi-bin", comment="API基础URL")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

class EmailSyncLog(Base):
    """邮箱同步日志模型（统一同步日志）"""
    __tablename__ = "email_sync_logs"

    id = Column(Integer, primary_key=True, index=True)
    sync_type = Column(String(50), nullable=False, comment="同步类型(departments/members/groups/tags/full/personnel)")
    sync_category = Column(String(20), default="email", comment="同步分类(email/personnel)")
    sync_id = Column(String(50), nullable=True, comment="同步ID")
    operator = Column(String(100), nullable=True, comment="操作员")
    status = Column(String(20), nullable=False, comment="状态(success/failed/running)")
    message = Column(Text, nullable=True, comment="同步消息")
    synced_count = Column(Integer, default=0, comment="同步数量")
    updated_count = Column(Integer, default=0, comment="更新数量")
    created_count = Column(Integer, default=0, comment="创建数量")
    disabled_count = Column(Integer, default=0, comment="禁用数量")
    error_count = Column(Integer, default=0, comment="错误数量")
    processed_count = Column(Integer, default=0, comment="处理数量")
    total_count = Column(Integer, default=0, comment="总数量")
    duration = Column(String(20), nullable=True, comment="耗时")
    details = Column(JSON, nullable=True, comment="详细信息")
    error_message = Column(Text, nullable=True, comment="错误信息")
    start_time = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")


class PersonnelSyncConfig(Base):
    """人员邮箱同步配置模型"""
    __tablename__ = "personnel_sync_config"

    id = Column(Integer, primary_key=True, index=True)
    enabled = Column(Boolean, default=False, comment="是否启用同步")
    sync_time = Column(String(10), nullable=True, comment="同步时间(HH:MM格式)")
    sync_interval = Column(Integer, default=24, comment="同步间隔(小时)")
    auto_create_users = Column(Boolean, default=True, comment="自动创建用户")
    auto_update_users = Column(Boolean, default=True, comment="自动更新用户")
    auto_disable_users = Column(Boolean, default=True, comment="自动禁用离职用户")
    auto_create_departments = Column(Boolean, default=True, comment="自动创建部门")
    
    # 过滤配置字段
    filter_enabled = Column(Boolean, default=False, comment="是否启用过滤功能")
    included_companies = Column(JSON, nullable=True, comment="包含的公司列表(JSON数组)")
    included_departments = Column(JSON, nullable=True, comment="包含的部门列表(JSON数组)")
    included_job_titles = Column(JSON, nullable=True, comment="包含的职位列表(JSON数组)")
    excluded_job_titles = Column(JSON, nullable=True, comment="排除的职位列表(JSON数组)")
    filter_logic = Column(String(10), default="AND", comment="过滤逻辑(AND/OR)")
    
    last_sync_time = Column(String(20), nullable=True, comment="最后同步时间")
    next_sync_time = Column(String(20), nullable=True, comment="下次同步时间")
    sync_status = Column(String(20), default="未同步", comment="同步状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")


class PersonnelSyncLog(Base):
    """人员邮箱同步日志模型"""
    __tablename__ = "personnel_sync_logs"

    id = Column(Integer, primary_key=True, index=True)
    sync_id = Column(String(50), nullable=True, comment="同步ID")  # 兼容现有表结构
    sync_type = Column(String(50), nullable=False, comment="同步类型(manual/auto/incremental/full)")
    operator = Column(String(100), nullable=True, comment="操作员")  # 兼容现有表结构
    status = Column(String(20), nullable=False, comment="状态(success/failed/running)")
    message = Column(Text, nullable=True, comment="同步消息")
    processed_count = Column(Integer, default=0, comment="处理数量")
    created_count = Column(Integer, default=0, comment="创建数量")
    updated_count = Column(Integer, default=0, comment="更新数量")
    disabled_count = Column(Integer, default=0, comment="禁用数量")
    error_count = Column(Integer, default=0, comment="错误数量")
    total_count = Column(Integer, default=0, comment="总数量")
    duration = Column(String(20), nullable=True, comment="耗时")
    details = Column(JSON, nullable=True, comment="详细信息")
    error_message = Column(Text, nullable=True, comment="错误信息")
    start_time = Column(DateTime(timezone=True), nullable=True, comment="开始时间")  # 兼容现有表结构
    end_time = Column(DateTime(timezone=True), nullable=True, comment="结束时间")  # 兼容现有表结构
    started_at = Column(DateTime(timezone=True), server_default=func.now(), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")


class EmailCreationRequest(Base):
    """邮箱创建申请模型"""
    __tablename__ = "email_creation_requests"

    id = Column(Integer, primary_key=True, index=True)
    job_number = Column(String(50), nullable=False, index=True, comment="工号")
    user_name = Column(String(100), nullable=False, comment="姓名")
    dept_name = Column(String(200), nullable=True, comment="部门名称")
    job_title_name = Column(String(100), nullable=True, comment="职位")
    mobile = Column(String(20), nullable=True, comment="手机号")
    status = Column(String(20), default="pending", comment="申请状态(pending/approved/rejected/created)")
    reason = Column(Text, nullable=True, comment="申请原因")
    requested_by = Column(String(100), default="系统自动检测", comment="申请人")
    approved_by = Column(String(100), nullable=True, comment="审批人")
    approved_at = Column(DateTime(timezone=True), nullable=True, comment="审批时间")
    created_email = Column(String(100), nullable=True, comment="创建的邮箱地址")
    notes = Column(Text, nullable=True, comment="备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")