#!/usr/bin/env python3
"""
人员同步日志数据迁移脚本
将 personnel_sync_logs 表的数据迁移到 email_sync_logs 表
"""

import sys
import os
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.email import EmailSyncLog, PersonnelSyncLog
from app.database import SessionLocal

def migrate_personnel_sync_logs():
    """迁移人员同步日志数据"""
    db = SessionLocal()
    
    try:
        print("开始迁移人员同步日志数据...")
        
        # 查询所有人员同步日志
        personnel_logs = db.query(PersonnelSyncLog).all()
        
        if not personnel_logs:
            print("没有找到需要迁移的人员同步日志数据")
            return
        
        print(f"找到 {len(personnel_logs)} 条人员同步日志记录")
        
        migrated_count = 0
        
        for log in personnel_logs:
            # 检查是否已经迁移过
            existing = db.query(EmailSyncLog).filter(
                EmailSyncLog.sync_category == "personnel",
                EmailSyncLog.sync_id == log.sync_id,
                EmailSyncLog.created_at == log.created_at
            ).first()
            
            if existing:
                print(f"跳过已迁移的记录: ID={log.id}")
                continue
            
            # 创建新的EmailSyncLog记录
            email_sync_log = EmailSyncLog(
                sync_type="personnel",
                sync_category="personnel",
                sync_id=log.sync_id,
                operator=log.operator,
                status=log.status,
                message=log.message,
                synced_count=log.processed_count,  # 映射处理数量到同步数量
                updated_count=log.updated_count,
                created_count=log.created_count,
                disabled_count=log.disabled_count,
                error_count=log.error_count,
                processed_count=log.processed_count,
                total_count=log.total_count,
                duration=log.duration,
                details=log.details,
                error_message=log.error_message,
                start_time=log.start_time or log.started_at,
                completed_at=log.completed_at,
                created_at=log.created_at
            )
            
            db.add(email_sync_log)
            migrated_count += 1
            
            print(f"迁移记录: ID={log.id}, 同步类型={log.sync_type}, 状态={log.status}")
        
        # 提交事务
        db.commit()
        print(f"成功迁移 {migrated_count} 条人员同步日志记录")
        
        # 验证迁移结果
        personnel_count = db.query(EmailSyncLog).filter(
            EmailSyncLog.sync_category == "personnel"
        ).count()
        print(f"迁移后统一日志表中人员同步记录总数: {personnel_count}")
        
    except Exception as e:
        print(f"迁移过程中发生错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def verify_migration():
    """验证迁移结果"""
    db = SessionLocal()
    
    try:
        # 统计原表记录数
        original_count = db.query(PersonnelSyncLog).count()
        
        # 统计迁移后的记录数
        migrated_count = db.query(EmailSyncLog).filter(
            EmailSyncLog.sync_category == "personnel"
        ).count()
        
        print(f"\n迁移验证结果:")
        print(f"原人员同步日志表记录数: {original_count}")
        print(f"迁移到统一日志表的记录数: {migrated_count}")
        
        if original_count == migrated_count:
            print("✅ 迁移验证成功，记录数一致")
        else:
            print("❌ 迁移验证失败，记录数不一致")
            
        # 显示一些示例记录
        sample_records = db.query(EmailSyncLog).filter(
            EmailSyncLog.sync_category == "personnel"
        ).limit(3).all()
        
        if sample_records:
            print(f"\n示例迁移记录:")
            for record in sample_records:
                print(f"- ID: {record.id}, 类型: {record.sync_type}, 状态: {record.status}, 时间: {record.created_at}")
                
    except Exception as e:
        print(f"验证过程中发生错误: {str(e)}")
    finally:
        db.close()

def main():
    """主函数"""
    print("人员同步日志数据迁移工具")
    print("=" * 50)
    
    # 确认操作
    confirm = input("确认要执行数据迁移吗？(y/N): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    try:
        # 执行迁移
        migrate_personnel_sync_logs()
        
        # 验证迁移结果
        verify_migration()
        
        print("\n迁移完成！")
        print("注意：迁移完成后，请确认数据正确性，然后可以考虑删除原 personnel_sync_logs 表")
        
    except Exception as e:
        print(f"迁移失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 