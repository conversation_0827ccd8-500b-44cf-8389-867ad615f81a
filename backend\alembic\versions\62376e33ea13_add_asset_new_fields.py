"""add_asset_new_fields

Revision ID: 62376e33ea13
Revises: d433861367b8
Create Date: 2025-02-26 09:21:32.298279

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '62376e33ea13'
down_revision: Union[str, None] = 'd433861367b8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('production_number', sa.String(length=100), nullable=True, comment='生产编号'))
    op.add_column('assets', sa.Column('price', sa.Float(), nullable=True, comment='价格'))
    op.add_column('assets', sa.Column('supplier', sa.String(length=100), nullable=True, comment='供应商'))
    op.add_column('assets', sa.Column('manufacturer', sa.String(length=100), nullable=True, comment='制造商'))
    op.add_column('assets', sa.Column('purchaser', sa.String(length=50), nullable=True, comment='采购人'))
    op.add_column('inventory_records', sa.Column('new_production_number', sa.String(length=100), nullable=True, comment='新生产编号'))
    op.add_column('inventory_records', sa.Column('new_price', sa.Float(), nullable=True, comment='新价格'))
    op.add_column('inventory_records', sa.Column('new_supplier', sa.String(length=100), nullable=True, comment='新供应商'))
    op.add_column('inventory_records', sa.Column('new_manufacturer', sa.String(length=100), nullable=True, comment='新制造商'))
    op.add_column('inventory_records', sa.Column('new_purchaser', sa.String(length=50), nullable=True, comment='新采购人'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('inventory_records', 'new_purchaser')
    op.drop_column('inventory_records', 'new_manufacturer')
    op.drop_column('inventory_records', 'new_supplier')
    op.drop_column('inventory_records', 'new_price')
    op.drop_column('inventory_records', 'new_production_number')
    op.drop_column('assets', 'purchaser')
    op.drop_column('assets', 'manufacturer')
    op.drop_column('assets', 'supplier')
    op.drop_column('assets', 'price')
    op.drop_column('assets', 'production_number')
    # ### end Alembic commands ###
