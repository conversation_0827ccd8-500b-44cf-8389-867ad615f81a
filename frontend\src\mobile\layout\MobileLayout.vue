<template>
  <!-- Vant主题配置容器 -->
  <van-config-provider 
    :theme-vars="vantThemeVars"
  >
    <div 
      class="mobile-layout theme-transition" 
      :class="[responsiveClass, layoutClasses]" 
      :style="responsiveStyle"
    >
      <!-- 移动端头部导航 -->
      <mobile-header 
        v-if="!hideGlobalHeader"
        :title="pageTitle" 
        :show-back="showBack"
        @back="handleBack"
      />
      
      <!-- 主要内容区 -->
      <main class="mobile-main mobile-safe-area mobile-scroll-optimize" :class="{ 'no-header': hideGlobalHeader }">
        <router-view />
      </main>
    </div>
  </van-config-provider>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDevice } from '@/composables/useDevice'
import { useTheme } from '@/composables/useTheme'
import MobileHeader from './components/MobileHeader.vue'

const route = useRoute()
const router = useRouter()
const { isMobile } = useDevice()

// 简化的响应式类名和样式
const responsiveClass = computed(() => ({
  'is-mobile': isMobile.value,
  'mobile-container': true
}))

const responsiveStyle = computed(() => ({
  '--screen-width': '100%'
}))

// 主题管理
const { 
  themeConfig, 
  vantThemeVars,
  applyTheme 
} = useTheme()

// 页面标题
const pageTitle = computed((): string => {
  return (route.meta?.title as string) || 'OPS平台'
})

// 是否隐藏全局头部
const hideGlobalHeader = computed(() => {
  return route.meta?.hideGlobalHeader === true
})

// 是否显示返回按钮
const showBack = computed(() => {
  return route.path !== '/m/apps'
})

// 布局类名
const layoutClasses = computed(() => ({
  'mobile-layout--compact': themeConfig.value.compactMode,
  'mobile-container': true,
  'mobile-responsive': true
}))

// 返回处理
const handleBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/m/apps')
  }
}

// 初始化主题
onMounted(() => {
  applyTheme()
})
</script>

<style lang="scss" scoped>
// 导入主题样式
@use '@/mobile/styles/theme.scss';

.mobile-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--theme-bg-page);
  color: var(--theme-text-primary);
  
  // 容器查询支持
  container-type: inline-size;
  
  // 主题切换动画
  transition: background-color var(--theme-duration-base),
              color var(--theme-duration-base);
  
  // 紧凑模式
  &--compact {
    --mobile-header-height: 44px;
    
    .mobile-main {
      padding-left: var(--theme-space-xs);
      padding-right: var(--theme-space-xs);
      // 紧凑模式下减少底部间距
      padding-bottom: var(--theme-space-xs, 4px);
    }
  }
}

.mobile-main {
  flex: 1;
  overflow-y: auto;
  position: relative;
  
  // 为固定头部留出空间
  padding-top: var(--mobile-header-height, 50px);
  // 底部使用更大的安全间距，确保内容可以完全滚动
  padding-bottom: max(var(--mobile-safe-area-bottom, 20px), 20px);
  padding-left: var(--theme-space-sm, 8px);
  padding-right: var(--theme-space-sm, 8px);
  
  // 背景色
  background-color: var(--theme-bg-page);
  
  // 当没有全局头部时，不需要预留空间
  &.no-header {
    padding-top: 0;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .mobile-layout {
    --mobile-header-height: 50px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .mobile-layout {
    --mobile-header-height: 56px;
  }
}

// 容器查询
@container (min-width: 768px) {
  .mobile-main {
    padding-left: var(--theme-space-md);
    padding-right: var(--theme-space-md);
  }
}

@container (min-width: 414px) {
  .mobile-layout {
    --mobile-header-height: 56px;
  }
  
  .mobile-main {
    padding-left: var(--theme-space-md);
    padding-right: var(--theme-space-md);
  }
}

// 全局滚动条样式（Webkit浏览器）
.mobile-main::-webkit-scrollbar {
  width: 4px;
}

.mobile-main::-webkit-scrollbar-track {
  background: transparent;
}

.mobile-main::-webkit-scrollbar-thumb {
  background: var(--theme-border-base);
  border-radius: 2px;
}

.mobile-main::-webkit-scrollbar-thumb:hover {
  background: var(--theme-border-dark);
}

// 移动端滚动优化
.mobile-main {
  // 移除 overscroll-behavior: contain，避免过度限制滚动
  // overscroll-behavior: contain; 
  
  // 使用更温和的滚动优化
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  
  // 确保滚动容器正确工作
  min-height: 0; // 重要：允许flex子元素收缩
  
  // 调试：确保滚动区域可见（生产环境可移除）
  &::-webkit-scrollbar {
    width: 4px;
    background: rgba(0, 0, 0, 0.1);
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 2px;
  }
}

// 横屏适配
@media screen and (orientation: landscape) and (max-height: 500px) {
  .mobile-layout {
    --mobile-header-height: 44px;
  }
  
  .mobile-main {
    padding-left: var(--theme-space-xs);
    padding-right: var(--theme-space-xs);
    // 横屏模式下减少底部间距
    padding-bottom: var(--theme-space-sm, 8px);
  }
}
</style> 