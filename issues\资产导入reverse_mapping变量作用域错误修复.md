# 资产导入reverse_mapping变量作用域错误修复

## 问题描述
资产导入功能报错：`cannot access local variable 'reverse_mapping' where it is not associated with a value`

## 问题分析
在 `backend/app/api/v1/assets.py` 的 `import_assets` 函数中，`reverse_mapping` 变量只在缺少必填字段的条件分支内定义（第274行），但在检查空值的循环中被使用（第284行）。当没有缺少必填字段时，程序会跳过 `reverse_mapping` 的定义，导致在后续使用时出现变量未定义的错误。

## 修复方案
将 `reverse_mapping` 变量的定义移到条件语句外部，确保在所有需要使用的地方都能访问到该变量。

## 修复内容
**文件**: `backend/app/api/v1/assets.py`

**修改前**:
```python
# 验证必填字段
required_fields = ['company', 'name', 'asset_number', 'status', 'specification', 'custodian', 'user', 'inspector', 'purchase_date']
missing_fields = [field for field in required_fields if field not in df.columns]
if missing_fields:
    # 将字段名转换回中文进行提示
    reverse_mapping = {v: k.replace('(*)', '') for k, v in field_mapping.items() if not k.endswith('(*)')}
    missing_fields_zh = [reverse_mapping.get(field, field) for field in missing_fields]
    error_msg = f"缺少必填字段: {', '.join(missing_fields_zh)}"
    print(f"Error: {error_msg}")
    raise HTTPException(status_code=400, detail=error_msg)
```

**修改后**:
```python
# 创建字段名中英文反向映射表，用于错误提示
reverse_mapping = {v: k.replace('(*)', '') for k, v in field_mapping.items() if not k.endswith('(*)')}

# 验证必填字段
required_fields = ['company', 'name', 'asset_number', 'status', 'specification', 'custodian', 'user', 'inspector', 'purchase_date']
missing_fields = [field for field in required_fields if field not in df.columns]
if missing_fields:
    # 将字段名转换回中文进行提示
    missing_fields_zh = [reverse_mapping.get(field, field) for field in missing_fields]
    error_msg = f"缺少必填字段: {', '.join(missing_fields_zh)}"
    print(f"Error: {error_msg}")
    raise HTTPException(status_code=400, detail=error_msg)
```

## 修复结果
- ✅ 资产导入功能正常工作，无论是否缺少必填字段
- ✅ 错误提示能正确显示中文字段名
- ✅ 不会再出现 "cannot access local variable 'reverse_mapping'" 错误
- ✅ 空值检查功能正常，能够正确提示缺失字段的中文名称

## 测试验证
修复后的代码能够正确处理以下场景：
1. 导入文件包含所有必填字段的情况
2. 导入文件缺少必填字段的情况
3. 导入文件字段完整但某些必填字段为空的情况

## 技术细节
- **问题类型**: 变量作用域错误
- **影响范围**: 资产导入功能
- **修复方式**: 调整变量定义位置
- **向后兼容**: 是，不影响现有功能

## 追加修复：规格型号字段改为可选

### 问题描述
用户反馈规格型号字段被要求必填，但实际业务需求中该字段应为可选。

### 修复内容
1. 从必填字段列表中移除 `specification`
2. 修改资产创建时的字段处理，使用 `get()` 方法处理可能为空的规格型号
3. 更新导入模板中的字段标记，将 `'规格型号(*)'` 改为 `'规格型号'`

### 修复代码
**第1处修改** - 必填字段列表：
```python
# 修改前
required_fields = ['company', 'name', 'asset_number', 'status', 'specification', 'custodian', 'user', 'inspector', 'purchase_date']

# 修改后
required_fields = ['company', 'name', 'asset_number', 'status', 'custodian', 'user', 'inspector', 'purchase_date']
```

**第2处修改** - 资产创建时的字段处理：
```python
# 修改前
specification=asset_data['specification'],

# 修改后
specification=asset_data.get('specification', None),
```

**第3处修改** - 导入模板字段映射：
```python
# 修改前
'specification': '规格型号(*)',

# 修改后
'specification': '规格型号',
```

## 追加修复：允许自定义资产状态

### 问题描述
用户反馈资产状态验证过于严格，系统只允许特定的状态值（使用中、闲置、维修中、已报废），但实际业务中需要更多自定义的状态值，如"库存"、"待处理"、"库存（不可用）"等。

### 修复内容
1. 移除硬编码的资产状态验证逻辑
2. 允许用户在导入时自定义任意状态值
3. 更新导入模板说明，明确说明状态值可以自定义

### 修复代码
**移除状态验证逻辑**：
```python
# 修改前
# 验证资产状态的值
valid_statuses = ['使用中', '闲置', '维修中', '已报废']
invalid_statuses = df[df['status'].notna() & ~df['status'].isin(valid_statuses)]['status'].unique()
if len(invalid_statuses) > 0:
    error_msg = f"包含无效的资产状态: {', '.join(invalid_statuses)}"
    print(f"Error: {error_msg}")
    raise HTTPException(status_code=400, detail=error_msg)

# 修改后
# 验证资产状态不为空（状态值可以自定义）
# 不再限制特定的状态值，允许用户自定义资产状态
```

**更新模板说明**：
```python
# 修改前
['3. 资产状态可选值：使用中、闲置、维修中、已报废'],

# 修改后
['3. 资产状态：可自定义状态值，如使用中、闲置、维修中、已报废等'],
```

### 修复效果
- ✅ 支持用户自定义资产状态值
- ✅ 不再限制特定的状态选项
- ✅ 保持状态字段为必填项的要求
- ✅ 更新了导入模板的说明文档

## 追加修复：字段缺失时的KeyError错误

### 问题描述
当导入文件中缺少某些可选字段（如retirement_date、remarks）时，代码直接访问字典键会引发KeyError错误。

### 修复内容
将所有可选字段的访问方式从直接字典访问改为使用`.get()`方法，避免字段不存在时的KeyError。

### 修复代码
**修复retirement_date字段**：
```python
# 修改前
retirement_date=asset_data['retirement_date'] if asset_data['retirement_date'] else None,

# 修改后
retirement_date=asset_data.get('retirement_date', None) if asset_data.get('retirement_date', '') else None,
```

**修复remarks字段**：
```python
# 修改前
remarks=asset_data['remarks'] if asset_data['remarks'] else None

# 修改后
remarks=asset_data.get('remarks', None) if asset_data.get('remarks', '') else None
```

### 修复效果
- ✅ 避免字段缺失时的KeyError错误
- ✅ 支持导入不完整的数据文件
- ✅ 提高导入功能的健壮性

## 修复日期
2024-12-19 