from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, UploadFile, Form, Request
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas import terminal as schemas
from app.crud import terminal as crud
from app import models
from app.api import deps
import logging
import os
from fastapi.responses import JSONResponse, Response
import csv
import io
import pandas as pd
import json
from datetime import datetime, timedelta
from sqlalchemy import and_

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取终端统计信息
@router.get("/stats", response_model=schemas.TerminalStats)
def get_terminal_stats(
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:view"]))
):
    """
    获取终端统计信息，包括终端总数、在线/离线数量、操作系统分布等
    """
    return crud.get_terminal_stats(db)

# 获取命令统计信息
@router.get("/commands/stats", response_model=schemas.TerminalCommandStats)
def get_command_stats(
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """
    获取命令统计信息，包括命令总数、各状态的命令数量
    """
    return crud.get_command_stats(db)

# 获取终端列表
@router.get("/", response_model=List[schemas.TerminalSummary])
def get_terminals(
    skip: int = 0,
    limit: int = 100,
    online_status: Optional[bool] = Query(None, description="过滤在线/离线状态"),
    hostname: Optional[str] = Query(None, description="按主机名过滤"),
    os_name: Optional[str] = Query(None, description="按操作系统名称过滤"),
    ip_address: Optional[str] = Query(None, description="按IP地址过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:view"]))
):
    """
    获取终端列表，支持分页和过滤
    """
    # 更新终端在线状态
    crud.update_terminal_online_status(db)

    terminals = crud.get_terminals(
        db,
        skip=skip,
        limit=limit,
        online_status=online_status,
        hostname=hostname,
        os_name=os_name,
        ip_address=ip_address
    )

    # 转换为摘要模型
    result = []
    for t in terminals:
        summary = schemas.TerminalSummary(
            id=t.id,
            hostname=t.hostname,
            unique_id=t.unique_id,
            mac_address=t.mac_address,
            ip_address=t.ip_address,
            online_status=t.online_status,
            last_heartbeat_time=t.last_heartbeat_time,
            os_name=t.os_info_detail.name if t.os_info_detail else None,
            os_version=t.os_info_detail.version if t.os_info_detail else None
        )
        result.append(summary)

    return result

# 软件管理接口 - 放在终端ID路由之前，避免被误识别为终端ID
@router.get("/sw", response_model=Dict[str, Any])
def get_software_list(
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = Query(None, description="根据软件名称过滤"),
    is_compliant: Optional[bool] = Query(None, description="根据合规性过滤"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:view"]))
):
    """
    获取所有终端中的软件列表，并统计安装了该软件的终端数量
    用于 软件管理 页面展示
    """
    return crud.get_software_list(db, skip=skip, limit=limit, name=name, is_compliant=is_compliant)

@router.get("/sw/export")
def export_software_list(
    name: Optional[str] = Query(None, description="根据软件名称过滤"),
    is_compliant: Optional[bool] = Query(None, description="根据合规性过滤"),
    format: str = Query("json", description="导出格式: json, csv, xlsx"),
    include_terminals: bool = Query(False, description="是否包含主机信息"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:manage"]))
):
    """
    导出软件列表（不分页，返回所有匹配条件的软件）
    支持JSON、CSV和XLSX格式
    include_terminals参数可选择是否包含主机信息
    """
    # 获取软件数据
    result = crud.get_software_list(db, skip=0, limit=10000, name=name, is_compliant=is_compliant)
    data = result["items"]

    # 如果需要包含主机信息，则获取每个软件的终端列表
    if include_terminals:
        for item in data:
            if isinstance(item, dict):
                software_name = item.get("name")
                software_version = item.get("version")
            else:
                software_name = item.name if hasattr(item, 'name') else ""
                software_version = item.version if hasattr(item, 'version') and item.version else ""

            # 获取安装了此软件的终端列表
            software_detail = crud.get_software_detail(db, name=software_name, version=software_version)

            # 添加终端信息
            if isinstance(item, dict):
                item["terminals"] = software_detail.get("terminals", [])
            else:
                setattr(item, "terminals", software_detail.get("terminals", []))

    # 根据请求的格式返回不同的响应
    if format.lower() == "json":
        return data

    # CSV和Excel格式处理
    if format.lower() == "csv":
        # 创建CSV数据
        csv_data = []
        for item in data:
            # 确保item是字典类型
            if isinstance(item, dict):
                item_dict = item
            else:
                # 如果是ORM模型，转换为字典
                item_dict = {
                    "name": item.name if hasattr(item, 'name') else "",
                    "version": item.version if hasattr(item, 'version') and item.version else "",
                    "terminal_count": item.terminal_count if hasattr(item, 'terminal_count') else 0,
                    "is_compliant": item.is_compliant if hasattr(item, 'is_compliant') else True,
                    "usage_notes": item.usage_notes if hasattr(item, 'usage_notes') and item.usage_notes else ""
                }

                # 添加终端信息（如果有）
                if include_terminals and hasattr(item, 'terminals'):
                    item_dict["terminals"] = item.terminals

            # 转换为CSV友好的格式
            if include_terminals and "terminals" in item_dict and item_dict["terminals"]:
                # 为每个终端创建一行数据
                terminals = item_dict.get("terminals", [])
                for terminal in terminals:
                    if hasattr(terminal, 'hostname'):
                        # ORM对象
                        host_info = {
                            "主机名": terminal.hostname if hasattr(terminal, 'hostname') else "",
                            "IP地址": terminal.ip_address if hasattr(terminal, 'ip_address') else "",
                            "MAC地址": terminal.mac_address if hasattr(terminal, 'mac_address') else "",
                            "操作系统": f"{terminal.os_info_detail.name} {terminal.os_info_detail.version}" if hasattr(terminal, 'os_info_detail') and terminal.os_info_detail else ""
                        }
                    else:
                        # 字典
                        host_info = {
                            "主机名": terminal.get("hostname", ""),
                            "IP地址": terminal.get("ip_address", ""),
                            "MAC地址": terminal.get("mac_address", ""),
                            "操作系统": f"{terminal.get('os_name', '')} {terminal.get('os_version', '')}"
                        }

                    csv_row = {
                        "软件名称": item_dict.get("name", ""),
                        "版本号": item_dict.get("version", "") or "",  # 将None转为空字符串
                        "合规状态": "合规" if item_dict.get("is_compliant", True) else "不合规",
                        "用途备注": item_dict.get("usage_notes", "") or "",  # 将None转为空字符串
                        **host_info  # 添加主机信息
                    }
                    csv_data.append(csv_row)
            else:
                # 没有终端信息，或者不需要包含终端信息
                csv_row = {
                    "软件名称": item_dict.get("name", ""),
                    "版本号": item_dict.get("version", "") or "",  # 将None转为空字符串
                    "安装终端数量": item_dict.get("terminal_count", 0),
                    "合规状态": "合规" if item_dict.get("is_compliant", True) else "不合规",
                    "用途备注": item_dict.get("usage_notes", "") or ""  # 将None转为空字符串
                }
                csv_data.append(csv_row)

        # 创建CSV输出
        output = io.StringIO()

        # 根据是否包含终端信息确定表头
        if include_terminals:
            fieldnames = ["软件名称", "版本号", "合规状态", "用途备注", "主机名", "IP地址", "MAC地址", "操作系统"]
        else:
            fieldnames = ["软件名称", "版本号", "安装终端数量", "合规状态", "用途备注"]

        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_data)

        # 添加BOM标记，解决Excel打开中文乱码
        csv_content = '\ufeff' + output.getvalue()

        # 返回CSV响应
        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename=software_list_{pd.Timestamp.now().strftime('%Y%m%d')}.csv"
            }
        )

    elif format.lower() == "xlsx":
        try:
            # 创建Excel数据
            excel_data = []
            for item in data:
                # 确保item是字典类型
                if isinstance(item, dict):
                    item_dict = item
                else:
                    # 如果是ORM模型，转换为字典
                    item_dict = {
                        "name": item.name if hasattr(item, 'name') else "",
                        "version": item.version if hasattr(item, 'version') and item.version else "",
                        "terminal_count": item.terminal_count if hasattr(item, 'terminal_count') else 0,
                        "is_compliant": item.is_compliant if hasattr(item, 'is_compliant') else True,
                        "usage_notes": item.usage_notes if hasattr(item, 'usage_notes') and item.usage_notes else ""
                    }

                    # 添加终端信息（如果有）
                    if include_terminals and hasattr(item, 'terminals'):
                        item_dict["terminals"] = item.terminals

                # 转换为Excel友好的格式
                if include_terminals and "terminals" in item_dict and item_dict["terminals"]:
                    # 为每个终端创建一行数据
                    terminals = item_dict.get("terminals", [])
                    for terminal in terminals:
                        if hasattr(terminal, 'hostname'):
                            # ORM对象
                            host_info = {
                                "主机名": terminal.hostname if hasattr(terminal, 'hostname') else "",
                                "IP地址": terminal.ip_address if hasattr(terminal, 'ip_address') else "",
                                "MAC地址": terminal.mac_address if hasattr(terminal, 'mac_address') else "",
                                "操作系统": f"{terminal.os_info_detail.name} {terminal.os_info_detail.version}" if hasattr(terminal, 'os_info_detail') and terminal.os_info_detail else ""
                            }
                        else:
                            # 字典
                            host_info = {
                                "主机名": terminal.get("hostname", ""),
                                "IP地址": terminal.get("ip_address", ""),
                                "MAC地址": terminal.get("mac_address", ""),
                                "操作系统": f"{terminal.get('os_name', '')} {terminal.get('os_version', '')}"
                            }

                        excel_row = {
                            "软件名称": item_dict.get("name", ""),
                            "版本号": item_dict.get("version", "") or "",  # 将None转为空字符串
                            "合规状态": "合规" if item_dict.get("is_compliant", True) else "不合规",
                            "用途备注": item_dict.get("usage_notes", "") or "",  # 将None转为空字符串
                            **host_info  # 添加主机信息
                        }
                        excel_data.append(excel_row)
                else:
                    # 没有终端信息，或者不需要包含终端信息
                    excel_row = {
                        "软件名称": item_dict.get("name", ""),
                        "版本号": item_dict.get("version", "") or "",  # 将None转为空字符串
                        "安装终端数量": item_dict.get("terminal_count", 0),
                        "合规状态": "合规" if item_dict.get("is_compliant", True) else "不合规",
                        "用途备注": item_dict.get("usage_notes", "") or ""  # 将None转为空字符串
                    }
                    excel_data.append(excel_row)

            # 创建DataFrame
            df = pd.DataFrame(excel_data)

            # 创建BytesIO对象来存储Excel数据
            output = io.BytesIO()

            # 使用Pandas ExcelWriter
            with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
                # 将DataFrame写入Excel
                df.to_excel(writer, sheet_name="软件列表", index=False)

                # 获取xlsxwriter工作表对象
                workbook = writer.book
                worksheet = writer.sheets["软件列表"]

                # 调整列宽
                if include_terminals:
                    worksheet.set_column(0, 0, 30)  # 软件名称
                    worksheet.set_column(1, 1, 15)  # 版本号
                    worksheet.set_column(2, 2, 10)  # 合规状态
                    worksheet.set_column(3, 3, 40)  # 用途备注
                    worksheet.set_column(4, 4, 20)  # 主机名
                    worksheet.set_column(5, 5, 15)  # IP地址
                    worksheet.set_column(6, 6, 20)  # MAC地址
                    worksheet.set_column(7, 7, 20)  # 操作系统
                else:
                    worksheet.set_column(0, 0, 30)  # 软件名称
                    worksheet.set_column(1, 1, 15)  # 版本号
                    worksheet.set_column(2, 2, 15)  # 安装终端数量
                    worksheet.set_column(3, 3, 10)  # 合规状态
                    worksheet.set_column(4, 4, 40)  # 用途备注

                # 添加表头格式
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#D7E4BC',
                    'border': 1
                })

                # 应用表头格式
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)

            # 重置文件指针到开始
            output.seek(0)

            # 返回Excel文件响应
            return Response(
                content=output.getvalue(),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename=software_list_{pd.Timestamp.now().strftime('%Y%m%d')}.xlsx"
                }
            )
        except Exception as e:
            import traceback
            print(f"Excel导出错误: {str(e)}")
            print(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"导出Excel失败: {str(e)}")

    # 默认返回JSON数据
    return data

@router.get("/sw/{name}", response_model=schemas.SoftwareDetail)
def get_software_detail(
    name: str,
    version: Optional[str] = Query(None, description="软件版本"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:view"]))
):
    """
    获取特定软件的详细信息，包括安装该软件的终端列表
    """
    result = crud.get_software_detail(db, name=name, version=version)
    return result

@router.put("/sw/{name}", response_model=schemas.SoftwareDetail)
def update_software_info(
    name: str,
    software_update: schemas.SoftwareUpdate,
    version: Optional[str] = Query(None, description="软件版本"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:manage"]))
):
    """
    更新软件信息（合规性和用途备注）
    """
    # 构建查询条件
    conditions = [models.Software.name == name]
    if version:
        conditions.append(models.Software.version == version)

    # 更新所有匹配的软件记录
    db.query(models.Software).filter(*conditions).update({
        "is_compliant": software_update.is_compliant,
        "usage_notes": software_update.usage_notes
    })
    db.commit()

    # 返回更新后的软件详情
    return crud.get_software_detail(db, name=name, version=version)

# 获取单个终端详情
@router.get("/{terminal_id}", response_model=schemas.TerminalResponse)
def get_terminal(
    terminal_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:view"]))
):
    """
    根据ID获取终端详情
    """
    terminal = crud.get_terminal_detail(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")
    return terminal

# 创建终端（手动添加，实际上终端通常是通过 gRPC 注册的）
@router.post("/", response_model=schemas.TerminalResponse)
def create_terminal(
    terminal: schemas.TerminalCreate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:add"]))
):
    """
    手动创建终端记录
    """
    db_terminal = crud.get_terminal_by_unique_id(db, terminal.unique_id)
    if db_terminal:
        raise HTTPException(status_code=400, detail="终端唯一标识已存在")
    return crud.create_terminal(db, terminal)

# 更新终端
@router.put("/{terminal_id}", response_model=schemas.TerminalResponse)
def update_terminal(
    terminal_id: int,
    terminal: schemas.TerminalUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:edit"]))
):
    """
    更新终端信息
    """
    db_terminal = crud.update_terminal(db, terminal_id, terminal)
    if not db_terminal:
        raise HTTPException(status_code=404, detail="终端不存在")
    return db_terminal

# 删除终端
@router.delete("/{terminal_id}")
def delete_terminal(
    terminal_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:delete"]))
):
    """
    删除终端
    """
    success = crud.delete_terminal(db, terminal_id)
    if not success:
        raise HTTPException(status_code=404, detail="终端不存在")
    return {"detail": "终端已删除"}

# 获取终端软件列表
@router.get("/{terminal_id}/software", response_model=List[schemas.SoftwareResponse])
def get_terminal_software(
    terminal_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:view"]))
):
    """
    获取终端安装的软件列表
    """
    terminal = crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    return crud.get_terminal_software(db, terminal_id)

# 查询终端命令
@router.get(
    "/{terminal_id}/commands",
    response_model=List[schemas.TerminalCommandResponse]
)
def get_terminal_commands(
    terminal_id: int,
    status: Optional[str] = Query(None, description="按状态过滤"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """
    获取终端的命令列表
    """
    terminal = crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    return crud.get_terminal_commands(
        db,
        terminal_id=terminal_id,
        status=status,
        skip=skip,
        limit=limit
    )

# 创建终端命令
@router.post("/commands", response_model=schemas.TerminalCommandResponse)
def create_terminal_command(
    command: schemas.TerminalCommandCreate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:send"]))
):
    """
    创建终端命令
    """
    terminal = crud.get_terminal(db, command.terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    # 如果是自定义命令，需要进行白名单验证
    if command.type == "CUSTOM_COMMAND":
        from app.crud import command_whitelist as cmd_crud
        validation_result = cmd_crud.validate_command(db, command.content)
        
        if not validation_result["is_valid"]:
            raise HTTPException(
                status_code=400, 
                detail=f"命令验证失败: {validation_result['message']}"
            )
        
        # 检查用户权限
        if validation_result["required_permission"]:
            # 获取用户权限
            user_permissions = []
            if not current_user.is_superuser:
                for role in current_user.roles:
                    for permission in role.permissions:
                        user_permissions.append(permission.code)
                
                if validation_result["required_permission"] not in user_permissions:
                    raise HTTPException(
                        status_code=403,
                        detail=f"没有执行此命令的权限，需要权限: {validation_result['required_permission']}"
                    )

    return crud.create_terminal_command(db, command)

# 获取命令详情
@router.get("/commands/{command_id}", response_model=schemas.TerminalCommandResponse)
def get_command(
    command_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:view"]))
):
    """
    根据ID获取命令详情
    """
    command = crud.get_terminal_command(db, command_id)
    if not command:
        raise HTTPException(status_code=404, detail="命令不存在")

    return command

# 发送立即采集信息的命令
@router.post("/{terminal_id}/collect-info", response_model=schemas.TerminalCommandResponse)
def send_collect_info_command(
    terminal_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:send"]))
):
    """
    向终端发送立即采集信息的命令
    """
    terminal = crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端当前离线，无法发送命令")

    # 创建采集信息命令
    command = schemas.TerminalCommandCreate(
        terminal_id=terminal_id,
        type="COLLECT_INFO",
        content="立即采集终端信息",
        timeout=300  # 5分钟超时
    )

    return crud.create_terminal_command(db, command)

# 发送升级Agent的命令
@router.post("/{terminal_id}/upgrade-agent", response_model=schemas.TerminalCommandResponse)
def send_upgrade_agent_command(
    terminal_id: int,
    version: str = Query(..., description="目标版本号"),
    download_url: str = Query(..., description="升级包下载地址"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:manage"]))
):
    """
    向终端发送升级Agent的命令
    """
    terminal = crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端当前离线，无法发送命令")

    # 构建升级内容JSON
    content = f'{{"version": "{version}", "url": "{download_url}"}}'

    # 创建升级命令
    command = schemas.TerminalCommandCreate(
        terminal_id=terminal_id,
        type="UPGRADE_AGENT",
        content=content,
        timeout=600  # 10分钟超时
    )

    return crud.create_terminal_command(db, command)

# 发送卸载软件的命令
@router.post("/{terminal_id}/uninstall-software", response_model=schemas.TerminalCommandResponse)
def send_uninstall_software_command(
    terminal_id: int,
    software: schemas.SoftwareUninstall,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:software:manage"]))
):
    """
    向终端发送卸载软件的命令
    """
    terminal = crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(status_code=404, detail="终端不存在")

    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端当前离线，无法发送命令")

    # 构建卸载软件内容JSON
    content = f'{{"name": "{software.name}", "version": "{software.version or ""}"}}'

    # 创建卸载软件命令
    command = schemas.TerminalCommandCreate(
        terminal_id=terminal_id,
        type="UNINSTALL_SOFTWARE",
        content=content,
        timeout=600  # 10分钟超时
    )

    return crud.create_terminal_command(db, command)

# 批量向终端发送指令
@router.post("/batch-command", response_model=Dict[str, Any])
def send_batch_command(
    command: schemas.BatchCommandCreate,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:command:send"]))
):
    """
    批量向多个终端发送命令
    """
    # 验证终端存在且在线
    success_terminals = []
    failed_terminals = []

    for terminal_id in command.terminal_ids:
        terminal = crud.get_terminal(db, terminal_id)
        if not terminal or not terminal.online_status:
            failed_terminals.append(terminal_id)
            continue

        # 创建命令
        terminal_command = schemas.TerminalCommandCreate(
            terminal_id=terminal_id,
            type=command.command_type,
            content=command.content,
            timeout=command.timeout if command.timeout else 3600
        )

        try:
            crud.create_terminal_command(db, terminal_command)
            success_terminals.append(terminal_id)
        except Exception as e:
            logger.error(f"为终端 {terminal_id} 创建命令失败: {str(e)}")
            failed_terminals.append(terminal_id)

    # 返回结果
    return {
        "total": len(command.terminal_ids),
        "success": len(success_terminals),
        "failed": len(failed_terminals),
        "failed_terminals": failed_terminals
    }

# Agent版本管理 API

# 获取Agent版本列表
@router.get("/agent/versions", response_model=List[schemas.AgentVersionResponse])
def get_agent_versions(
    platform: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:view"]))
):
    """
    获取Agent版本列表，支持按平台过滤
    """
    versions = crud.get_agent_versions(
        db,
        platform=platform,
        skip=skip,
        limit=limit
    )
    return versions

# 获取当前Agent版本
@router.get("/agent/current-version", response_model=schemas.AgentVersionResponse)
def get_current_agent_version(
    request: Request,
    platform: str = Query(..., description="平台类型：windows, linux, macos"),
    db: Session = Depends(get_db),
    agent_verified: bool = Depends(deps.verify_agent_token)
):
    """
    获取指定平台的当前Agent版本（供Agent客户端使用，需要系统Token认证）
    """
    version = crud.get_current_agent_version(db, platform=platform)
    if not version:
        raise HTTPException(status_code=404, detail=f"未找到平台 {platform} 的当前版本")
    return version

# 上传新的Agent版本
@router.post("/agent/versions", response_model=schemas.AgentVersionResponse)
async def upload_agent_version(
    file: UploadFile,
    version: str = Form(...),
    platform: str = Form(...),
    release_notes: Optional[str] = Form(None),
    is_current: bool = Form(False),
    auto_upgrade: bool = Form(False),
    upgrade_strategy: str = Form("all"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:manage"]))
):
    """
    上传新的Agent版本
    """
    # 保存文件到特定目录
    file_name = file.filename
    file_path = f"uploads/agent/{platform}/{file_name}"

    # 确保目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # 保存文件
    contents = await file.read()
    with open(file_path, "wb") as f:
        f.write(contents)

    # 计算文件大小
    file_size = os.path.getsize(file_path)

    # 创建下载URL
    download_url = f"/downloads/agent/{platform}/{file_name}"

    # 创建Agent版本记录
    agent_version = schemas.AgentVersionCreate(
        version=version,
        platform=platform,
        file_name=file_name,
        file_size=file_size,
        download_url=download_url,
        release_notes=release_notes,
        is_current=is_current
    )

    # 如果设置为当前版本并启用自动升级，添加相关参数
    if is_current and auto_upgrade:
        agent_version.auto_upgrade = True
        agent_version.upgrade_strategy = upgrade_strategy

    return crud.create_agent_version(db, agent_version)

# 设置当前版本
@router.put("/agent/versions/{version_id}/set-current", response_model=schemas.AgentVersionResponse)
def set_current_agent_version(
    version_id: int,
    auto_upgrade: bool = Query(False, description="是否自动向所有终端下发升级命令"),
    upgrade_strategy: str = Query("all", description="升级策略: all(所有终端), online(仅在线终端)"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:manage"]))
):
    """
    设置指定Agent版本为当前版本
    """
    version = crud.set_current_agent_version(db, version_id)
    if not version:
        raise HTTPException(status_code=404, detail="Agent版本不存在")

    # 如果启用自动升级，向符合条件的终端下发升级命令
    if auto_upgrade:
        # 获取符合条件的终端
        if upgrade_strategy == "online":
            # 仅在线终端
            terminals = crud.get_terminals(db, online_status=True)
        else:
            # 所有终端
            terminals = crud.get_terminals(db)

        # 记录成功和失败的终端数量
        success_count = 0
        failed_count = 0

        # 批量发送升级命令
        for terminal in terminals:
            try:
                # 只向匹配平台的终端发送升级命令
                # 获取终端的操作系统信息
                os_info = db.query(models.OSInfo).filter(models.OSInfo.terminal_id == terminal.id).first()
                if not os_info:
                    failed_count += 1
                    continue

                # 判断终端平台是否与版本平台匹配
                terminal_platform = "windows"
                if "linux" in os_info.name.lower():
                    terminal_platform = "linux"
                elif "mac" in os_info.name.lower() or "darwin" in os_info.name.lower():
                    terminal_platform = "macos"

                # 如果平台不匹配，跳过
                if terminal_platform != version.platform:
                    continue

                # 创建升级命令
                command = schemas.TerminalCommandCreate(
                    terminal_id=terminal.id,
                    type="UPGRADE_AGENT",
                    content=f'{{"version": "{version.version}", "url": "{version.download_url}"}}',
                    timeout=600  # 10分钟超时
                )

                # 只有在线终端才立即发送命令，离线终端创建待执行命令
                crud.create_terminal_command(db, command)
                success_count += 1

            except Exception as e:
                logger.error(f"向终端 {terminal.id} 发送升级命令失败: {str(e)}")
                failed_count += 1

        # 在响应中添加升级命令发送结果
        setattr(version, 'upgrade_command_sent', True)
        setattr(version, 'upgrade_command_success', success_count)
        setattr(version, 'upgrade_command_failed', failed_count)

    return version

# 删除Agent版本
@router.delete("/agent/versions/{version_id}")
def delete_agent_version(
    version_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:manage"]))
):
    """
    删除指定的Agent版本
    """
    success = crud.delete_agent_version(db, version_id)
    if not success:
        raise HTTPException(status_code=404, detail="Agent版本不存在")

    return {"detail": "Agent版本已删除"}

# Agent升级监控 API

@router.get("/agent/upgrade-status", response_model=List[schemas.AgentUpgradeStatus])
def get_agent_upgrade_status(
    status: Optional[str] = Query(None, description="按状态过滤: pending, downloading, installing, completed, failed, rolled_back"),
    terminal_id: Optional[int] = Query(None, description="按终端ID过滤"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:view"]))
):
    """
    获取Agent升级状态列表
    """
    # 查询UPGRADE_AGENT类型的命令作为升级状态
    query = db.query(models.TerminalCommand).filter(
        models.TerminalCommand.type == "UPGRADE_AGENT"
    )
    
    if status:
        # 状态映射
        status_mapping = {
            "pending": "pending",
            "downloading": "sent", 
            "installing": "executed",
            "completed": "completed",
            "failed": "failed",
            "rolled_back": "failed"  # 回滚状态暂时映射为失败
        }
        mapped_status = status_mapping.get(status)
        if mapped_status:
            query = query.filter(models.TerminalCommand.status == mapped_status)
    
    if terminal_id:
        query = query.filter(models.TerminalCommand.terminal_id == terminal_id)
    
    # 按创建时间倒序排列
    commands = query.order_by(models.TerminalCommand.create_time.desc()).offset(skip).limit(limit).all()
    
    # 转换为升级状态格式
    upgrade_statuses = []
    for cmd in commands:
        try:
            content = json.loads(cmd.content) if cmd.content else {}
            target_version = content.get("version", "未知")
        except:
            target_version = "未知"
        
        # 获取终端当前版本
        terminal = db.query(models.Terminal).filter(models.Terminal.id == cmd.terminal_id).first()
        current_version = terminal.agent_version if terminal else "未知"
        
        # 计算进度
        progress = 0
        if cmd.status == "completed":
            progress = 100
        elif cmd.status == "executed":
            progress = 80
        elif cmd.status == "sent":
            progress = 20
        elif cmd.status == "failed":
            progress = -1
        
        # 计算耗时
        duration = None
        completed_time = cmd.execute_time if cmd.execute_time else None
        if completed_time and cmd.create_time:
            duration = (completed_time - cmd.create_time).total_seconds()
        
        upgrade_status = schemas.AgentUpgradeStatus(
            command_id=str(cmd.id),
            terminal_id=cmd.terminal_id,
            target_version=target_version,
            current_version=current_version,
            status=status or cmd.status.lower(),
            progress=progress,
            message=cmd.result or "",
            error_details=cmd.error if hasattr(cmd, 'error') else None,
            started_at=cmd.create_time,
            completed_at=completed_time,
            duration=duration
        )
        upgrade_statuses.append(upgrade_status)
    
    return upgrade_statuses

@router.get("/agent/upgrade-progress/{command_id}", response_model=schemas.AgentUpgradeProgress)
def get_agent_upgrade_progress(
    command_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:view"]))
):
    """
    获取特定升级命令的进度详情
    """
    try:
        cmd_id = int(command_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的命令ID格式")
    
    command = db.query(models.TerminalCommand).filter(
        and_(
            models.TerminalCommand.id == cmd_id,
            models.TerminalCommand.type == "UPGRADE_AGENT"
        )
    ).first()
    
    if not command:
        raise HTTPException(status_code=404, detail="升级命令不存在")
    
    # 计算进度和状态
    progress = 0
    message = "升级命令已创建"
    
    if command.status == "pending":
        progress = 0
        message = "等待发送升级命令"
    elif command.status == "sent":
        progress = 20
        message = "升级命令已发送，等待终端响应"
    elif command.status == "executed":
        progress = 80
        message = "升级正在进行中"
    elif command.status == "completed":
        progress = 100
        message = "升级已完成"
    elif command.status == "failed":
        progress = -1
        message = command.result or "升级失败"
    elif command.status == "timeout":
        progress = -1
        message = "升级超时"
    
    return schemas.AgentUpgradeProgress(
        command_id=command_id,
        progress=progress,
        message=message,
        timestamp=command.execute_time or command.sent_time or command.create_time
    )

@router.get("/agent/upgrade-history", response_model=List[schemas.AgentUpgradeHistory])
def get_agent_upgrade_history(
    terminal_id: Optional[int] = Query(None, description="按终端ID过滤"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:view"]))
):
    """
    获取Agent升级历史记录
    """
    # 查询UPGRADE_AGENT类型的已完成命令
    query = db.query(models.TerminalCommand).filter(
        models.TerminalCommand.type == "UPGRADE_AGENT"
    )
    
    if terminal_id:
        query = query.filter(models.TerminalCommand.terminal_id == terminal_id)
    
    if status:
        # 状态映射为小写格式
        status_mapping = {
            "pending": "pending",
            "downloading": "sent", 
            "installing": "executed",
            "completed": "completed",
            "failed": "failed",
            "timeout": "timeout"
        }
        mapped_status = status_mapping.get(status, status)
        query = query.filter(models.TerminalCommand.status == mapped_status)
    
    # 日期过滤
    if start_date:
        try:
            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(models.TerminalCommand.create_time >= start_datetime)
        except ValueError:
            raise HTTPException(status_code=400, detail="开始日期格式错误，请使用 YYYY-MM-DD")
    
    if end_date:
        try:
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)
            query = query.filter(models.TerminalCommand.create_time <= end_datetime)
        except ValueError:
            raise HTTPException(status_code=400, detail="结束日期格式错误，请使用 YYYY-MM-DD")
    
    # 按创建时间倒序
    commands = query.order_by(models.TerminalCommand.create_time.desc()).offset(skip).limit(limit).all()
    
    history_records = []
    for cmd in commands:
        try:
            content = json.loads(cmd.content) if cmd.content else {}
            to_version = content.get("version", "未知")
        except:
            to_version = "未知"
        
        # 获取终端信息
        terminal = db.query(models.Terminal).filter(models.Terminal.id == cmd.terminal_id).first()
        from_version = terminal.agent_version if terminal else "未知"
        
        # 计算耗时
        duration = None
        completed_time = cmd.execute_time if cmd.execute_time else None
        if completed_time and cmd.create_time:
            duration = (completed_time - cmd.create_time).total_seconds()
        
        history_record = schemas.AgentUpgradeHistory(
            id=cmd.id,
            terminal_id=cmd.terminal_id,
            from_version=from_version,
            to_version=to_version,
            status=cmd.status.lower(),
            started_at=cmd.create_time,
            completed_at=completed_time,
            duration=duration,
            error_message=cmd.result if cmd.status == "failed" else None
        )
        history_records.append(history_record)
    
    return history_records

@router.get("/agent/upgrade-stats", response_model=Dict[str, Any])
def get_agent_upgrade_stats(
    days: int = Query(7, description="统计天数，默认7天"),
    db: Session = Depends(get_db),
    current_user = Depends(deps.check_permissions(["terminal:agent:view"]))
):
    """
    获取Agent升级统计数据
    """
    # 计算起始时间
    start_date = datetime.now() - timedelta(days=days)
    
    # 查询指定时间范围内的升级命令
    query = db.query(models.TerminalCommand).filter(
        and_(
            models.TerminalCommand.type == "UPGRADE_AGENT",
            models.TerminalCommand.create_time >= start_date
        )
    )
    
    # 统计各状态数量
    status_stats = {}
    for status in ["PENDING", "SENT", "EXECUTED", "COMPLETED", "FAILED", "TIMEOUT"]:
        count = query.filter(models.TerminalCommand.status == status).count()
        status_stats[status.lower()] = count
    
    # 计算成功率
    total_upgrades = query.count()
    completed_upgrades = status_stats.get("completed", 0)
    success_rate = (completed_upgrades / total_upgrades * 100) if total_upgrades > 0 else 0
    
    # 统计每日升级数量
    daily_stats = []
    for i in range(days):
        day_start = start_date + timedelta(days=i)
        day_end = day_start + timedelta(days=1)
        
        day_count = query.filter(
            and_(
                models.TerminalCommand.create_time >= day_start,
                models.TerminalCommand.create_time < day_end
            )
        ).count()
        
        daily_stats.append({
            "date": day_start.strftime("%Y-%m-%d"),
            "count": day_count
        })
    
    # 获取活跃的升级任务（进行中的）
    active_upgrades = query.filter(
        models.TerminalCommand.status.in_(["PENDING", "SENT", "EXECUTED"])
    ).count()
    
    return {
        "total_upgrades": total_upgrades,
        "success_rate": round(success_rate, 2),
        "active_upgrades": active_upgrades,
        "status_distribution": status_stats,
        "daily_stats": daily_stats,
        "period_days": days
    }