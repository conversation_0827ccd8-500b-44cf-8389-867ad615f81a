"""
人员邮箱同步API接口
提供手动触发同步、查询同步状态、管理同步配置等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime

from app.database import get_db
from app.utils import get_current_user
from app.models.user import User
from app.api import deps
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.services.personnel_sync_config import PersonnelSyncConfigService
from app.schemas.email_personnel_sync import (
    PersonnelSyncRequest,
    PersonnelSyncResult,
    PersonnelSyncConfigUpdate,
    PersonnelSyncConfigResponse,
    PersonnelSyncLogResponse
)

router = APIRouter()


@router.post("/sync/trigger", response_model=PersonnelSyncResult)
async def trigger_personnel_sync(
    request: PersonnelSyncRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:sync"]))
):
    """手动触发人员邮箱同步"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 导入锁服务
    from app.services.email_sync_lock import acquire_lock, release_lock, check_conflicting_operations, LOCK_TYPES
    
    # 检查是否有冲突的操作正在进行
    conflicts = await check_conflicting_operations(db, LOCK_TYPES["PERSONNEL_SYNC"])
    if conflicts:
        conflict_info = ", ".join([f"{c['operation_type']}({c['locked_by']})" for c in conflicts["conflicts"]])
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"无法执行人员同步，有冲突的操作正在进行：{conflict_info}"
        )
    
    # 尝试获取锁
    lock_name = "email_personnel_sync"
    lock_acquired = await acquire_lock(db, lock_name, LOCK_TYPES["PERSONNEL_SYNC"], current_user.username)
    
    if not lock_acquired:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="人员邮箱同步正在进行中，请稍后再试"
        )
    
    try:
        sync_service = PersonnelEmailSyncService(db)
        
        # 解析起始时间
        since_time = None
        if request.since_time:
            try:
                since_time = datetime.fromisoformat(request.since_time)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的时间格式，请使用ISO格式"
                )
        
        # 执行同步
        result = await sync_service.sync_personnel_to_email(
            full_sync=request.full_sync,
            since_time=since_time,
            dry_run=request.dry_run
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )
    finally:
        # 无论成功还是失败，都要释放锁
        await release_lock(db, lock_name, current_user.username)


@router.get("/sync/status")
async def get_sync_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取同步状态和统计信息"""
    try:
        config_service = PersonnelSyncConfigService(db)
        stats = await config_service.get_sync_stats()
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步状态失败: {str(e)}"
        )


@router.get("/sync/config", response_model=PersonnelSyncConfigResponse)
async def get_sync_config(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取同步配置"""
    try:
        config_service = PersonnelSyncConfigService(db)
        config = await config_service.get_sync_config()
        return config
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步配置失败: {str(e)}"
        )


@router.put("/sync/config", response_model=PersonnelSyncConfigResponse)
async def update_sync_config(
    config_update: PersonnelSyncConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """更新同步配置"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        config_service = PersonnelSyncConfigService(db)
        
        # 转换为字典，过滤None值
        config_data = {k: v for k, v in config_update.dict().items() if v is not None}
        
        config = await config_service.update_sync_config(config_data)
        return config
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新同步配置失败: {str(e)}"
        )


@router.get("/sync/logs", response_model=List[PersonnelSyncLogResponse])
async def get_sync_logs(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取同步日志列表"""
    try:
        config_service = PersonnelSyncConfigService(db)
        logs = await config_service.get_sync_logs(skip=skip, limit=limit)
        return logs
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步日志失败: {str(e)}"
        )


@router.get("/sync/logs/{log_id}", response_model=PersonnelSyncLogResponse)
async def get_sync_log_detail(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取同步日志详情"""
    try:
        config_service = PersonnelSyncConfigService(db)
        log = await config_service.get_sync_log_by_id(log_id)
        
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="同步日志不存在"
            )
        
        return log
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步日志详情失败: {str(e)}"
        )


@router.post("/sync/check-consistency")
async def check_data_consistency(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """检查数据一致性"""
    try:
        sync_service = PersonnelEmailSyncService(db)
        result = await sync_service.check_data_consistency()
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据一致性检查失败: {str(e)}"
        )


@router.post("/sync/enable")
async def enable_sync(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """启用自动同步"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        config_service = PersonnelSyncConfigService(db)
        config = await config_service.update_sync_config({"enabled": True})
        return {"message": "自动同步已启用", "config": config}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启用自动同步失败: {str(e)}"
        )


@router.post("/sync/disable")
async def disable_sync(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:config"]))
):
    """禁用自动同步"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    try:
        config_service = PersonnelSyncConfigService(db)
        config = await config_service.update_sync_config({"enabled": False})
        return {"message": "自动同步已禁用", "config": config}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"禁用自动同步失败: {str(e)}"
        )


@router.get("/sync/is-due")
async def check_sync_due(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """检查是否到了同步时间"""
    try:
        config_service = PersonnelSyncConfigService(db)
        is_due = await config_service.is_sync_due()
        return {"is_due": is_due}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查同步时间失败: {str(e)}"
        )


@router.get("/filter/companies")
async def get_available_companies(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取可用的公司列表"""
    try:
        config_service = PersonnelSyncConfigService(db)
        companies = await config_service.get_available_companies()
        return {
            "companies": companies,
            "total": len(companies)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公司列表失败: {str(e)}"
        )


@router.get("/filter/departments")
async def get_available_departments(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取可用的部门列表"""
    try:
        config_service = PersonnelSyncConfigService(db)
        departments = await config_service.get_available_departments()
        return {
            "departments": departments,
            "total": len(departments)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取部门列表失败: {str(e)}"
        )


@router.get("/filter/departments/by-company/{company_name}")
async def get_departments_by_company(
    company_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """根据公司名获取该公司下的部门列表"""
    try:
        config_service = PersonnelSyncConfigService(db)
        departments = await config_service.get_departments_by_company(company_name)
        return {
            "departments": departments,
            "total": len(departments),
            "company_name": company_name
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取公司 '{company_name}' 的部门列表失败: {str(e)}"
        )


@router.get("/filter/job-titles")
async def get_available_job_titles(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取可用的职位列表"""
    try:
        config_service = PersonnelSyncConfigService(db)
        job_titles = await config_service.get_available_job_titles()
        return {
            "job_titles": job_titles,
            "total": len(job_titles)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取职位列表失败: {str(e)}"
        )


@router.post("/filter/preview")
async def preview_filter_results(
    filter_request: dict,  # 使用dict来接收灵活的过滤配置
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """预览过滤结果"""
    try:
        config_service = PersonnelSyncConfigService(db)
        preview_result = await config_service.preview_filter_results(filter_request)
        return preview_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"过滤预览失败: {str(e)}"
        )


@router.get("/filter/stats")
async def get_filter_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:personnel:view"]))
):
    """获取过滤统计信息"""
    try:
        config_service = PersonnelSyncConfigService(db)
        
        # 获取当前配置
        config = await config_service.get_sync_config()
        
        # 准备过滤配置
        filter_config = {
            "filter_enabled": config.filter_enabled,
            "included_departments": config.included_departments or [],
            "included_job_titles": config.included_job_titles or [],
            "excluded_job_titles": config.excluded_job_titles or [],
            "filter_logic": config.filter_logic
        }
        
        # 获取预览结果
        preview_result = await config_service.preview_filter_results(filter_config)
        
        return {
            "current_filter_config": filter_config,
            "preview_result": preview_result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取过滤统计失败: {str(e)}"
        )
