import { defineStore } from 'pinia'
import request from '@/utils/request'

interface UserInfo {
  id: number
  username: string
  email: string
  is_superuser?: boolean
  roles?: Array<{
    id: number
    name: string
    code: string
    permissions: Array<{
      id: number
      code: string
      name: string
    }>
  }>
  [key: string]: any
}

interface State {
  token: string
  userInfo: UserInfo | null
  initialized: boolean
  permissions: string[]
  isLoading: boolean
  lastInitTime: number | null
}

interface LoginResponse {
  access_token: string
  user: UserInfo
}

export const useUserStore = defineStore('user', {
  state: (): State => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
    initialized: false,
    permissions: [],
    isLoading: false,
    lastInitTime: null
  }),
  
  actions: {
    async login(formData: FormData): Promise<LoginResponse> {
      try {
        const response = await request<LoginResponse>({
          url: '/auth/login',
          method: 'post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        const { access_token, user } = response.data
        this.setToken(access_token)
        this.userInfo = user
        this.initialized = true
        
        if (user.roles) {
          this.extractPermissions(user)
        }
        
        return Promise.resolve(response.data)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    setToken(token: string): void {
      if (!token) return
      
      const cleanToken = token.trim() 
      this.token = cleanToken
      localStorage.setItem('token', cleanToken)
    },

    logout(): void {
      this.token = ''
      this.userInfo = null
      this.permissions = []
      this.initialized = false
      this.isLoading = false
      this.lastInitTime = null
      localStorage.removeItem('token')
    },

    extractPermissions(user: UserInfo): void {
      const permissionSet = new Set<string>()
      
      if (user.roles?.length) {
        user.roles.forEach(role => {
          role.permissions?.forEach(permission => {
            permissionSet.add(permission.code)
          })
        })
      }
      
      this.permissions = Array.from(permissionSet)
    },

    async getUserInfo(): Promise<UserInfo> {
      // 如果已经初始化且距离上次初始化时间不超过2分钟，直接返回
      const now = Date.now()
      if (this.userInfo && this.initialized && this.lastInitTime && 
          (now - this.lastInitTime < 2 * 60 * 1000)) {
        return Promise.resolve(this.userInfo)
      }

      // 防重复请求机制
      if (this.isLoading) {
        let attempts = 0
        while (this.isLoading && attempts < 20) { // 减少等待时间到1秒
          await new Promise(resolve => setTimeout(resolve, 50))
          attempts++
        }
        if (this.userInfo && this.initialized) {
          return this.userInfo
        }
        if (this.isLoading) {
          throw new Error('获取用户信息超时')
        }
      }

      try {
        this.isLoading = true
        const response = await request<UserInfo>({
          url: '/system/users/me',
          method: 'get',
          headers: {
            'Authorization': `Bearer ${this.token}`
          }
        })

        if (!response.data?.id || !response.data?.username) {
          throw new Error('用户信息不完整')
        }

        this.userInfo = response.data
        this.initialized = true
        this.lastInitTime = Date.now()
        this.extractPermissions(response.data)
        
        return response.data
      } catch (error) {
        this.userInfo = null
        this.permissions = []
        this.initialized = false
        this.lastInitTime = null
        return Promise.reject(error)
      } finally {
        this.isLoading = false
      }
    },

    async initializeAuth(): Promise<void> {
      // 如果已经初始化且用户信息完整，跳过重复初始化（缩短缓存时间为1分钟）
      if (this.initialized && this.userInfo?.id && this.lastInitTime && 
          (Date.now() - this.lastInitTime < 60 * 1000)) {
        return
      }

      const storedToken = localStorage.getItem('token')?.trim()
      if (!storedToken) {
        console.log('[UserStore] 无token，清除状态')
        this.logout()
        return
      }

      // 确保token状态一致
      if (storedToken !== this.token) {
        console.log('[UserStore] 更新token状态')
        this.token = storedToken
      }

      // 防止并发初始化
      if (this.isLoading) {
        let attempts = 0
        while (this.isLoading && attempts < 30) {
          await new Promise(resolve => setTimeout(resolve, 50))
          attempts++
        }
        if (this.initialized && this.userInfo?.id) {
          return
        }
      }

      try {
        await this.getUserInfo()
        if (!this.userInfo?.id) {
          throw new Error('用户信息不完整')
        }
      } catch (error) {
        this.logout()
        throw error
      }
    }
  },
  
  getters: {
    isLoggedIn: (state): boolean => !!state.token,
    isInitialized: (state): boolean => state.initialized,
    isSuperUser: (state): boolean => state.userInfo?.is_superuser || false,
    hasPermission: (state) => (permissionCode: string): boolean => {
      // 超级管理员拥有所有权限
      if (state.userInfo?.is_superuser) {
        return true
      }
      return state.permissions.includes(permissionCode)
    },
    isReady: (state): boolean => {
      // 进一步简化：只检查token和用户ID的存在
      const hasToken = !!state.token
      const hasUserInfo = !!state.userInfo?.id
      
      return hasToken && hasUserInfo
    }
  }
}) 