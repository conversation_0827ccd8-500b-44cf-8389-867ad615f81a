"""
Windows 注册表管理器
提供安全的注册表操作接口，包括备份、还原、权限验证等功能
"""

import winreg
import os
import json
import logging
import time
import tempfile
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass
from enum import Enum
import uuid

# 配置日志
logger = logging.getLogger(__name__)

class RegistryOperationType(Enum):
    """注册表操作类型"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    CREATE_KEY = "create_key"
    DELETE_KEY = "delete_key"
    ENUMERATE = "enumerate"
    EXPORT = "export"
    IMPORT = "import"
    BACKUP = "backup"

class RegistryValueType(Enum):
    """注册表值类型"""
    REG_SZ = winreg.REG_SZ                      # 字符串
    REG_EXPAND_SZ = winreg.REG_EXPAND_SZ        # 可扩展字符串
    REG_BINARY = winreg.REG_BINARY              # 二进制数据
    REG_DWORD = winreg.REG_DWORD                # 32位数值
    REG_QWORD = winreg.REG_QWORD                # 64位数值
    REG_MULTI_SZ = winreg.REG_MULTI_SZ          # 多字符串

class RegistryRootKey(Enum):
    """注册表根键"""
    HKEY_CLASSES_ROOT = winreg.HKEY_CLASSES_ROOT
    HKEY_CURRENT_USER = winreg.HKEY_CURRENT_USER
    HKEY_LOCAL_MACHINE = winreg.HKEY_LOCAL_MACHINE
    HKEY_USERS = winreg.HKEY_USERS
    HKEY_CURRENT_CONFIG = winreg.HKEY_CURRENT_CONFIG

@dataclass
class RegistryValue:
    """注册表值信息"""
    name: str
    type: RegistryValueType
    data: Any
    size: int

@dataclass
class RegistryKey:
    """注册表键信息"""
    name: str
    full_path: str
    sub_keys: List[str]
    values: List[RegistryValue]
    last_modified: int
    sub_key_count: int
    value_count: int

@dataclass
class RegistryBackup:
    """注册表备份信息"""
    backup_id: str
    backup_name: str
    root_key: RegistryRootKey
    key_path: str
    reason: str
    create_time: int
    size: int
    file_path: str

class RegistryManager:
    """注册表管理器"""
    
    def __init__(self, backup_dir: Optional[str] = None):
        """
        初始化注册表管理器
        
        Args:
            backup_dir: 备份目录路径，默认为临时目录下的registry_backups
        """
        # 设置备份目录
        if backup_dir is None:
            backup_dir = Path(tempfile.gettempdir()) / "registry_backups"
        
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 操作日志
        self.operation_log: List[Dict] = []
        
        # 危险区域保护
        self.protected_keys = {
            RegistryRootKey.HKEY_LOCAL_MACHINE: [
                r"SYSTEM\CurrentControlSet\Services",
                r"SYSTEM\CurrentControlSet\Control",
                r"SOFTWARE\Microsoft\Windows NT\CurrentVersion",
                r"SECURITY",
                r"SAM"
            ],
            RegistryRootKey.HKEY_CURRENT_USER: [
                r"Software\Microsoft\Windows\CurrentVersion\Run",
                r"Software\Microsoft\Windows\CurrentVersion\RunOnce"
            ]
        }
    
    def _log_operation(self, operation: str, root_key: RegistryRootKey, 
                      key_path: str, success: bool, error: Optional[str] = None, 
                      **kwargs):
        """记录操作日志"""
        log_entry = {
            "timestamp": int(time.time()),
            "operation": operation,
            "root_key": root_key.name,
            "key_path": key_path,
            "success": success,
            "error": error,
            **kwargs
        }
        self.operation_log.append(log_entry)
        
        if success:
            logger.info(f"注册表操作成功: {operation} - {root_key.name}\\{key_path}")
        else:
            logger.error(f"注册表操作失败: {operation} - {root_key.name}\\{key_path} - {error}")
    
    def _is_protected_key(self, root_key: RegistryRootKey, key_path: str) -> bool:
        """检查是否为受保护的注册表键"""
        if root_key not in self.protected_keys:
            return False
        
        key_path = key_path.lower()
        for protected_path in self.protected_keys[root_key]:
            if key_path.startswith(protected_path.lower()):
                return True
        return False
    
    def _get_registry_key_handle(self, root_key: RegistryRootKey, 
                                key_path: str, access: int = winreg.KEY_READ):
        """获取注册表键句柄"""
        try:
            return winreg.OpenKey(root_key.value, key_path, 0, access)
        except FileNotFoundError:
            return None
        except PermissionError:
            logger.error(f"访问注册表键 {root_key.name}\\{key_path} 权限不足")
            return None
    
    def _convert_value_to_string(self, value: Any, value_type: RegistryValueType) -> str:
        """将注册表值转换为字符串"""
        try:
            if value_type == RegistryValueType.REG_SZ:
                return str(value)
            elif value_type == RegistryValueType.REG_EXPAND_SZ:
                return str(value)
            elif value_type == RegistryValueType.REG_DWORD:
                return str(value)
            elif value_type == RegistryValueType.REG_QWORD:
                return str(value)
            elif value_type == RegistryValueType.REG_BINARY:
                return value.hex() if isinstance(value, bytes) else str(value)
            elif value_type == RegistryValueType.REG_MULTI_SZ:
                return '\n'.join(value) if isinstance(value, list) else str(value)
            else:
                return str(value)
        except Exception as e:
            logger.warning(f"转换注册表值失败: {e}")
            return str(value)
    
    def _convert_string_to_value(self, value_str: str, 
                                value_type: RegistryValueType) -> Any:
        """将字符串转换为注册表值"""
        try:
            if value_type == RegistryValueType.REG_SZ:
                return value_str
            elif value_type == RegistryValueType.REG_EXPAND_SZ:
                return value_str
            elif value_type == RegistryValueType.REG_DWORD:
                return int(value_str)
            elif value_type == RegistryValueType.REG_QWORD:
                return int(value_str)
            elif value_type == RegistryValueType.REG_BINARY:
                return bytes.fromhex(value_str) if value_str else b''
            elif value_type == RegistryValueType.REG_MULTI_SZ:
                return value_str.split('\n') if value_str else []
            else:
                return value_str
        except Exception as e:
            logger.error(f"转换字符串到注册表值失败: {e}")
            raise ValueError(f"无法转换值 '{value_str}' 到类型 {value_type.name}")
    
    def read_key(self, root_key: RegistryRootKey, key_path: str) -> Optional[RegistryKey]:
        """
        读取注册表键信息
        
        Args:
            root_key: 根键
            key_path: 键路径
            
        Returns:
            RegistryKey对象或None
        """
        try:
            handle = self._get_registry_key_handle(root_key, key_path)
            if handle is None:
                return None
            
            # 获取键信息
            info = winreg.QueryInfoKey(handle)
            sub_key_count = info[0]
            value_count = info[1]
            last_modified = info[9]
            
            # 枚举子键
            sub_keys = []
            for i in range(sub_key_count):
                try:
                    sub_key_name = winreg.EnumKey(handle, i)
                    sub_keys.append(sub_key_name)
                except Exception:
                    break
            
            # 枚举值
            values = []
            for i in range(value_count):
                try:
                    value_name, value_data, value_type = winreg.EnumValue(handle, i)
                    
                    # 转换值类型
                    reg_value_type = RegistryValueType(value_type)
                    
                    # 计算数据大小
                    if isinstance(value_data, bytes):
                        size = len(value_data)
                    elif isinstance(value_data, str):
                        size = len(value_data.encode('utf-8'))
                    elif isinstance(value_data, list):
                        size = sum(len(item.encode('utf-8')) for item in value_data)
                    else:
                        size = len(str(value_data).encode('utf-8'))
                    
                    registry_value = RegistryValue(
                        name=value_name,
                        type=reg_value_type,
                        data=value_data,
                        size=size
                    )
                    values.append(registry_value)
                except Exception as e:
                    logger.warning(f"枚举注册表值失败: {e}")
                    continue
            
            winreg.CloseKey(handle)
            
            result = RegistryKey(
                name=os.path.basename(key_path) or key_path,
                full_path=f"{root_key.name}\\{key_path}",
                sub_keys=sub_keys,
                values=values,
                last_modified=last_modified,
                sub_key_count=sub_key_count,
                value_count=value_count
            )
            
            self._log_operation("read_key", root_key, key_path, True)
            return result
            
        except Exception as e:
            self._log_operation("read_key", root_key, key_path, False, str(e))
            return None
    
    def read_value(self, root_key: RegistryRootKey, key_path: str, 
                  value_name: str) -> Optional[RegistryValue]:
        """
        读取注册表值
        
        Args:
            root_key: 根键
            key_path: 键路径
            value_name: 值名称
            
        Returns:
            RegistryValue对象或None
        """
        try:
            handle = self._get_registry_key_handle(root_key, key_path)
            if handle is None:
                return None
            
            value_data, value_type = winreg.QueryValueEx(handle, value_name)
            winreg.CloseKey(handle)
            
            # 计算数据大小
            if isinstance(value_data, bytes):
                size = len(value_data)
            elif isinstance(value_data, str):
                size = len(value_data.encode('utf-8'))
            elif isinstance(value_data, list):
                size = sum(len(item.encode('utf-8')) for item in value_data)
            else:
                size = len(str(value_data).encode('utf-8'))
            
            result = RegistryValue(
                name=value_name,
                type=RegistryValueType(value_type),
                data=value_data,
                size=size
            )
            
            self._log_operation("read_value", root_key, key_path, True, 
                              value_name=value_name)
            return result
            
        except Exception as e:
            self._log_operation("read_value", root_key, key_path, False, str(e),
                              value_name=value_name)
            return None
    
    def create_backup(self, root_key: RegistryRootKey, key_path: str, 
                     reason: str = "手动备份") -> Optional[RegistryBackup]:
        """
        创建注册表备份
        
        Args:
            root_key: 根键
            key_path: 键路径
            reason: 备份原因
            
        Returns:
            RegistryBackup对象或None
        """
        try:
            backup_id = str(uuid.uuid4())
            timestamp = int(time.time())
            # 预处理键路径，替换反斜杠为下划线
            safe_key_path = key_path.replace('\\', '_')
            backup_name = f"{root_key.name}_{safe_key_path}_{timestamp}"
            backup_file = self.backup_dir / f"{backup_name}.reg"
            
            # 使用reg export命令导出注册表
            full_key_path = f"{root_key.name}\\{key_path}"
            cmd = ["reg", "export", full_key_path, str(backup_file), "/y"]
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8')
            
            if result.returncode != 0:
                logger.error(f"注册表备份失败: {result.stderr}")
                return None
            
            # 获取备份文件大小
            backup_size = backup_file.stat().st_size if backup_file.exists() else 0
            
            backup = RegistryBackup(
                backup_id=backup_id,
                backup_name=backup_name,
                root_key=root_key,
                key_path=key_path,
                reason=reason,
                create_time=timestamp,
                size=backup_size,
                file_path=str(backup_file)
            )
            
            self._log_operation("create_backup", root_key, key_path, True,
                              backup_id=backup_id, backup_file=str(backup_file))
            return backup
            
        except Exception as e:
            self._log_operation("create_backup", root_key, key_path, False, str(e))
            return None
    
    def write_value(self, root_key: RegistryRootKey, key_path: str, 
                   value_name: str, value_data: str, 
                   value_type: RegistryValueType,
                   create_backup: bool = True) -> bool:
        """
        写入注册表值
        
        Args:
            root_key: 根键
            key_path: 键路径
            value_name: 值名称
            value_data: 值数据(字符串形式)
            value_type: 值类型
            create_backup: 是否创建备份
            
        Returns:
            是否成功
        """
        try:
            # 检查权限保护
            if self._is_protected_key(root_key, key_path):
                logger.warning(f"尝试修改受保护的注册表键: {root_key.name}\\{key_path}")
                # 可以选择拒绝操作或要求更高权限
            
            # 创建备份
            backup_id = None
            if create_backup:
                backup = self.create_backup(root_key, key_path, 
                                         f"写入值前备份: {value_name}")
                if backup:
                    backup_id = backup.backup_id
            
            # 转换值数据
            converted_value = self._convert_string_to_value(value_data, value_type)
            
            # 写入值
            handle = self._get_registry_key_handle(root_key, key_path, 
                                                 winreg.KEY_SET_VALUE)
            if handle is None:
                return False
            
            winreg.SetValueEx(handle, value_name, 0, value_type.value, 
                            converted_value)
            winreg.CloseKey(handle)
            
            self._log_operation("write_value", root_key, key_path, True,
                              value_name=value_name, value_type=value_type.name,
                              backup_id=backup_id)
            return True
            
        except Exception as e:
            self._log_operation("write_value", root_key, key_path, False, str(e),
                              value_name=value_name)
            return False
    
    def delete_value(self, root_key: RegistryRootKey, key_path: str, 
                    value_name: str, create_backup: bool = True) -> bool:
        """
        删除注册表值
        
        Args:
            root_key: 根键
            key_path: 键路径
            value_name: 值名称
            create_backup: 是否创建备份
            
        Returns:
            是否成功
        """
        try:
            # 检查权限保护
            if self._is_protected_key(root_key, key_path):
                logger.warning(f"尝试删除受保护的注册表键值: {root_key.name}\\{key_path}\\{value_name}")
            
            # 创建备份
            backup_id = None
            if create_backup:
                backup = self.create_backup(root_key, key_path, 
                                         f"删除值前备份: {value_name}")
                if backup:
                    backup_id = backup.backup_id
            
            # 删除值
            handle = self._get_registry_key_handle(root_key, key_path, 
                                                 winreg.KEY_SET_VALUE)
            if handle is None:
                return False
            
            winreg.DeleteValue(handle, value_name)
            winreg.CloseKey(handle)
            
            self._log_operation("delete_value", root_key, key_path, True,
                              value_name=value_name, backup_id=backup_id)
            return True
            
        except Exception as e:
            self._log_operation("delete_value", root_key, key_path, False, str(e),
                              value_name=value_name)
            return False
    
    def create_key(self, root_key: RegistryRootKey, key_path: str) -> bool:
        """
        创建注册表键
        
        Args:
            root_key: 根键
            key_path: 键路径
            
        Returns:
            是否成功
        """
        try:
            handle = winreg.CreateKey(root_key.value, key_path)
            winreg.CloseKey(handle)
            
            self._log_operation("create_key", root_key, key_path, True)
            return True
            
        except Exception as e:
            self._log_operation("create_key", root_key, key_path, False, str(e))
            return False
    
    def delete_key(self, root_key: RegistryRootKey, key_path: str, 
                  create_backup: bool = True) -> bool:
        """
        删除注册表键
        
        Args:
            root_key: 根键
            key_path: 键路径
            create_backup: 是否创建备份
            
        Returns:
            是否成功
        """
        try:
            # 检查权限保护
            if self._is_protected_key(root_key, key_path):
                logger.error(f"拒绝删除受保护的注册表键: {root_key.name}\\{key_path}")
                return False
            
            # 创建备份
            backup_id = None
            if create_backup:
                backup = self.create_backup(root_key, key_path, "删除键前备份")
                if backup:
                    backup_id = backup.backup_id
            
            # 递归删除键
            winreg.DeleteKey(root_key.value, key_path)
            
            self._log_operation("delete_key", root_key, key_path, True,
                              backup_id=backup_id)
            return True
            
        except Exception as e:
            self._log_operation("delete_key", root_key, key_path, False, str(e))
            return False
    
    def search_registry(self, root_key: RegistryRootKey, start_path: str,
                       search_pattern: str, search_keys: bool = True,
                       search_values: bool = True, search_data: bool = True,
                       max_depth: int = 10, max_results: int = 100) -> List[Dict]:
        """
        搜索注册表
        
        Args:
            root_key: 根键
            start_path: 开始路径
            search_pattern: 搜索模式
            search_keys: 是否搜索键名
            search_values: 是否搜索值名
            search_data: 是否搜索值数据
            max_depth: 最大搜索深度
            max_results: 最大结果数
            
        Returns:
            搜索结果列表
        """
        results = []
        pattern = search_pattern.lower()
        
        def search_recursive(current_path: str, depth: int):
            if depth > max_depth or len(results) >= max_results:
                return
            
            try:
                handle = self._get_registry_key_handle(root_key, current_path)
                if handle is None:
                    return
                
                # 获取键信息
                info = winreg.QueryInfoKey(handle)
                sub_key_count = info[0]
                value_count = info[1]
                
                # 搜索子键
                if search_keys:
                    for i in range(sub_key_count):
                        try:
                            sub_key_name = winreg.EnumKey(handle, i)
                            if pattern in sub_key_name.lower():
                                results.append({
                                    "path": f"{current_path}\\{sub_key_name}",
                                    "match_type": "key",
                                    "match_text": sub_key_name,
                                    "value": None
                                })
                                if len(results) >= max_results:
                                    break
                        except Exception:
                            continue
                
                # 搜索值
                if (search_values or search_data) and len(results) < max_results:
                    for i in range(value_count):
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(handle, i)
                            
                            # 搜索值名
                            if search_values and pattern in value_name.lower():
                                registry_value = RegistryValue(
                                    name=value_name,
                                    type=RegistryValueType(value_type),
                                    data=value_data,
                                    size=len(str(value_data))
                                )
                                results.append({
                                    "path": current_path,
                                    "match_type": "value_name",
                                    "match_text": value_name,
                                    "value": registry_value
                                })
                                if len(results) >= max_results:
                                    break
                            
                            # 搜索值数据
                            if search_data and pattern in str(value_data).lower():
                                registry_value = RegistryValue(
                                    name=value_name,
                                    type=RegistryValueType(value_type),
                                    data=value_data,
                                    size=len(str(value_data))
                                )
                                results.append({
                                    "path": current_path,
                                    "match_type": "value_data",
                                    "match_text": str(value_data),
                                    "value": registry_value
                                })
                                if len(results) >= max_results:
                                    break
                        except Exception:
                            continue
                
                # 递归搜索子键
                if depth < max_depth and len(results) < max_results:
                    for i in range(sub_key_count):
                        try:
                            sub_key_name = winreg.EnumKey(handle, i)
                            sub_path = f"{current_path}\\{sub_key_name}"
                            search_recursive(sub_path, depth + 1)
                            if len(results) >= max_results:
                                break
                        except Exception:
                            continue
                
                winreg.CloseKey(handle)
                
            except Exception as e:
                logger.warning(f"搜索注册表路径 {current_path} 失败: {e}")
        
        try:
            search_recursive(start_path, 0)
            self._log_operation("search_registry", root_key, start_path, True,
                              pattern=search_pattern, results_count=len(results))
        except Exception as e:
            self._log_operation("search_registry", root_key, start_path, False, str(e))
        
        return results
    
    def get_operation_log(self) -> List[Dict]:
        """获取操作日志"""
        return self.operation_log.copy()
    
    def clear_operation_log(self):
        """清空操作日志"""
        self.operation_log.clear() 