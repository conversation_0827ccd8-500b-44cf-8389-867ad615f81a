import redis

try:
    # 创建Redis连接
    r = redis.Redis(
        host='**************', 
        port=6379, 
        password='redis_w4PNJy',
        socket_timeout=5
    )
    
    # 测试连接
    if r.ping():
        print("成功连接到Redis服务器!")
        
        # 测试基本操作
        r.set('test_key', 'Hello Redis')
        value = r.get('test_key')
        print(f"从Redis获取的值: {value}")
        
        # 清理测试数据
        r.delete('test_key')
        print("测试完成，已清理测试数据")
    else:
        print("无法连接到Redis服务器！")
except Exception as e:
    print(f"连接Redis时出错: {str(e)}") 