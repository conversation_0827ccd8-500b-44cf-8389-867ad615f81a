# 邮箱成员登录权限查看功能说明

## 功能概述

为邮箱管理模块的"部门与成员管理"页面增加了成员登录权限查看功能，用户可以在成员详情中查看该成员的各种登录权限设置。

## 功能特性

### 1. 成员详情页面增强
- 将原有的成员详情对话框重新设计，分为"基本信息"和"登录权限设置"两个部分
- 对话框宽度从600px增加到700px，提供更好的显示效果

### 2. 登录权限信息展示
在成员详情中新增"登录权限设置"卡片，显示以下权限信息：

#### POP/SMTP 登录
- 显示是否允许使用 POP3/SMTP 协议收发邮件
- 状态：已启用（绿色）/ 已禁用（红色）

#### IMAP/SMTP 登录  
- 显示是否允许使用 IMAP/SMTP 协议收发邮件
- 状态：已启用（绿色）/ 已禁用（红色）

#### 安全登录
- 显示是否启用安全验证登录
- 状态：已启用（绿色）/ 已禁用（红色）
- 说明：启用后需要通过安全验证才能登录

#### 强制安全登录
- 显示是否强制要求安全验证
- 状态：已强制（橙色）/ 未强制（灰色）
- 说明：强制要求所有登录都必须通过安全验证

### 3. 权限数据刷新
- 提供"刷新权限"按钮，可以实时从API获取最新的权限设置
- 支持加载状态显示，提升用户体验
- 当权限数据获取失败时，显示友好的空状态提示

## 技术实现

### 前端实现
1. **组件结构优化**
   - 使用 `el-card` 组件分离基本信息和权限信息
   - 采用 `el-descriptions` 组件展示权限详情
   - 使用 `el-tag` 组件显示权限状态

2. **数据管理**
   - 新增 `memberPermissions` 响应式变量存储权限数据
   - 新增 `permissionsLoading` 控制加载状态
   - 实现 `loadMemberPermissions` 方法获取权限数据
   - 实现 `refreshMemberPermissions` 方法刷新权限

3. **API集成**
   - 调用 `getMemberLoginPermissions` API获取权限数据
   - 解析API返回的权限选项数组格式
   - 将数字格式权限值转换为布尔值供前端使用

### API数据格式
API返回的权限数据格式：
```json
{
  "data": {
    "option": [
      {"type": 1, "value": "1"},  // 强制安全登录
      {"type": 2, "value": "1"},  // IMAP/SMTP登录
      {"type": 3, "value": "1"},  // POP/SMTP登录
      {"type": 4, "value": "0"}   // 安全登录
    ]
  }
}
```

权限类型映射：
- type 1: 强制安全登录 (force_secure_login)
- type 2: IMAP/SMTP登录 (imap_smtp_enabled)  
- type 3: POP/SMTP登录 (pop_smtp_enabled)
- type 4: 安全登录 (secure_login_enabled)

## 使用方法

1. 进入"企业邮箱管理" -> "部门与成员管理"页面
2. 从左侧部门树选择要查看的部门
3. 在右侧成员列表中点击"查看"按钮或直接点击成员行
4. 在弹出的成员详情对话框中查看"登录权限设置"部分
5. 如需获取最新权限信息，点击"刷新权限"按钮

## 样式说明

- 权限状态使用不同颜色的标签区分：
  - 绿色：功能已启用
  - 红色：功能已禁用  
  - 橙色：强制模式
  - 灰色：未强制
- 每个权限项都有详细的功能说明
- 支持加载状态和空状态的友好提示

## 注意事项

1. 权限信息需要从腾讯企业邮箱API实时获取
2. 如果API调用失败，会显示"暂无权限信息"
3. 权限数据的获取可能需要一定时间，请耐心等待
4. 该功能仅用于查看权限，不支持在此页面修改权限设置 