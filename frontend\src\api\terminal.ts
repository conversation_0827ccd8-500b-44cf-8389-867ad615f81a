import request from '@/utils/request'
import type {
  TerminalSummary,
  TerminalDetail,
  TerminalStats,
  TerminalCommandStats,
  TerminalCommand,
  Software,
  AgentVersion,
  SoftwareSummary,
  SoftwareDetail
} from '@/types/terminal'

export const terminalApi = {
  // 获取终端统计信息
  getTerminalStats: () => {
    return request.get<TerminalStats>('/terminal/stats')
  },

  // 获取命令统计信息
  getCommandStats: () => {
    return request.get<TerminalCommandStats>('/terminal/commands/stats')
  },

  // 获取终端列表
  getTerminals: (params: {
    skip?: number
    limit?: number
    online_status?: boolean
    hostname?: string
    os_name?: string
    ip_address?: string
  }) => {
    return request.get<TerminalSummary[]>('/terminal', { params })
  },

  // 获取终端详情
  getTerminalDetail: (id: string) => {
    return request.get<TerminalDetail>(`/terminal/${id}`)
  },

  // 获取终端软件列表
  getTerminalSoftware: (id: string) => {
    return request.get<Software[]>(`/terminal/${id}/software`)
  },

  // 获取终端命令
  getTerminalCommands: (id: string, params: {
    status?: string
    skip?: number
    limit?: number
  }) => {
    return request.get<TerminalCommand[]>(`/terminal/${id}/commands`, { params })
  },

  // 创建终端命令
  createTerminalCommand: (data: {
    terminal_id: string
    type: string
    content: string
    timeout?: number
  }) => {
    return request.post<TerminalCommand>('/terminal/commands', data)
  },

  // 发送采集信息命令
  sendCollectInfoCommand: (terminalId: string) => {
    return request.post<TerminalCommand>(`/terminal/${terminalId}/collect-info`)
  },

  // 发送升级Agent命令
  sendUpgradeAgentCommand: (terminalId: string, params: {
    version: string
    download_url: string
  }) => {
    return request.post<TerminalCommand>(
      `/terminal/${terminalId}/upgrade-agent`,
      null,
      { params }
    )
  },

  // 发送卸载软件命令
  sendUninstallSoftwareCommand: (terminalId: string, data: {
    name: string
    version?: string
  }) => {
    return request.post<TerminalCommand>(
      `/terminal/${terminalId}/uninstall-software`,
      data
    )
  },

  // 批量发送命令
  sendBatchCommand: (data: {
    terminal_ids: string[]
    command_type: string
    content: string
    timeout?: number
  }) => {
    return request.post('/terminal/batch-command', data)
  },

  // 获取Agent版本列表
  getAgentVersions: (params: {
    platform?: string
    skip?: number
    limit?: number
  }) => {
    return request.get<AgentVersion[]>('/terminal/agent/versions', { params })
  },

  // 上传新的Agent版本
  uploadAgentVersion: (formData: FormData) => {
    return request.post<AgentVersion>('/terminal/agent/versions', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 设置当前版本
  setCurrentAgentVersion: (id: string, auto_upgrade?: boolean, upgrade_strategy?: string) => {
    const params: Record<string, any> = {}
    if (auto_upgrade !== undefined) {
      params.auto_upgrade = auto_upgrade
    }
    if (upgrade_strategy) {
      params.upgrade_strategy = upgrade_strategy
    }
    return request.put<AgentVersion>(`/terminal/agent/versions/${id}/set-current`, null, { params })
  },

  // 删除Agent版本
  deleteAgentVersion: (id: string) => {
    return request.delete(`/terminal/agent/versions/${id}`)
  },

  // 获取软件列表(所有终端的软件汇总)
  getSoftwareList: (params: {
    skip?: number
    limit?: number
    name?: string
    is_compliant?: boolean
  }) => {
    return request.get<{items: SoftwareSummary[], total: number}>('/terminal/sw', { params })
  },

  // 获取软件详情(包含安装该软件的终端列表)
  getSoftwareDetail: (name: string, params?: {
    version?: string
  }) => {
    return request.get<SoftwareDetail>(`/terminal/sw/${name}`, { params })
  },

  // 更新软件信息(合规性和用途备注)
  updateSoftwareInfo: (name: string, data: {
    is_compliant: boolean
    usage_notes?: string
  }, params?: {
    version?: string
  }) => {
    return request.put<SoftwareDetail>(`/terminal/sw/${name}`, data, { params })
  },

  // 导出软件列表
  exportSoftwareList: (params?: {
    name?: string
    is_compliant?: boolean
    format?: 'json' | 'csv' | 'xlsx'
    include_terminals?: boolean
  }) => {
    return request.get('/terminal/sw/export', {
      params,
      responseType: params?.format && params.format !== 'json' ? 'blob' : 'json'
    })
  },

  // 删除终端
  deleteTerminal: (id: string) => {
    return request.delete(`/terminal/${id}`)
  },

  // === Agent升级监控 API ===

  // 获取升级状态列表
  getAgentUpgradeStatus: (params?: {
    status?: string
    terminal_id?: number
    skip?: number
    limit?: number
  }) => {
    return request.get('/terminal/agent/upgrade-status', { params })
  },

  // 获取特定升级进度
  getAgentUpgradeProgress: (commandId: string) => {
    return request.get(`/terminal/agent/upgrade-progress/${commandId}`)
  },

  // 获取升级历史记录
  getAgentUpgradeHistory: (params?: {
    terminal_id?: number
    status?: string
    start_date?: string
    end_date?: string
    skip?: number
    limit?: number
  }) => {
    return request.get('/terminal/agent/upgrade-history', { params })
  },

  // 获取升级统计数据
  getAgentUpgradeStats: (days?: number) => {
    const params = days ? { days } : {}
    return request.get('/terminal/agent/upgrade-stats', { params })
  }
}