# 注册表API用户对象属性访问错误修复

## 问题描述
在注册表API (`backend/app/api/v1/registry.py`) 中，代码尝试对 `current_user` 对象使用字典访问语法 `.get()` 方法，但 `current_user` 是 SQLAlchemy 的 User 模型对象，不支持该方法。

## 错误信息
```
AttributeError: 'User' object has no attribute 'get'
```

## 错误位置
- `backend/app/api/v1/registry.py` 第 325 行
- `backend/app/api/v1/registry.py` 第 714 行

## 错误代码
```python
# 错误写法
if not current_user.get("is_superuser", False):
    raise HTTPException(status_code=403, detail="此操作需要管理员权限")
```

## 修复方案
将字典访问语法改为对象属性访问：

```python
# 正确写法
if not current_user.is_superuser:
    raise HTTPException(status_code=403, detail="此操作需要管理员权限")
```

## 修复内容
1. **第一处修复** (第325行) - `perform_registry_operation` 函数中的危险键权限检查
2. **第二处修复** (第714行) - `restore_registry_backup` 函数中的管理员权限检查

## 根本原因
这个错误通常发生在认证系统重构后，`current_user` 从字典类型改为了 SQLAlchemy 模型对象，但部分代码没有相应更新。

## 验证方法
1. 启动后端服务
2. 访问注册表相关API端点
3. 确认不再出现 AttributeError 错误

## 修复时间
2024年12月19日

## 状态
✅ 已修复 