"""add_create_security_groups_to_ad_sync_

Revision ID: 51ee499983b7
Revises: 9ea680a206d0
Create Date: 2025-03-20 16:46:44.399404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '51ee499983b7'
down_revision: Union[str, None] = '9ea680a206d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加create_security_groups列，默认值为False
    op.add_column('ad_sync_config', sa.Column('create_security_groups', sa.<PERSON>(), nullable=True, server_default='0'))


def downgrade() -> None:
    # 删除create_security_groups列
    op.drop_column('ad_sync_config', 'create_security_groups')
