"""add_command_whitelist_and_categories_tables

Revision ID: 0edde6415892
Revises: b36f49bee95b
Create Date: 2025-06-26 18:02:00.906502

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0edde6415892'
down_revision: Union[str, None] = 'b36f49bee95b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_terminals_last_offline_time', table_name='terminals')
    op.drop_index('ix_terminals_last_online_time', table_name='terminals')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_terminals_last_online_time', 'terminals', ['last_online_time'], unique=False)
    op.create_index('ix_terminals_last_offline_time', 'terminals', ['last_offline_time'], unique=False)
    # ### end Alembic commands ###
