<template>
  <div class="group-list-container">
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleAddGroup" :disabled="!props.ouDn">
          <el-icon><Plus /></el-icon>添加组
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索组"
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="groups"
        style="width: 100%"
      >
        <el-table-column prop="name" label="组名">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleShowDetail(row)">
              {{ row.name }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="memberCount" label="成员数" width="100" align="center" />
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleViewMembers(row)">
                    <el-icon><View /></el-icon>查看成员
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleEditGroup(row)">
                    <el-icon><Edit /></el-icon>编辑信息
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleDeleteGroup(row)" style="color: var(--el-color-danger)">
                    <el-icon><Delete /></el-icon>删除组
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <Authority permission="ad:view">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </Authority>
    </div>

    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加组' : '编辑组'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="组名" prop="name">
          <el-input 
            v-model="form.name"
            :disabled="dialogType === 'edit'"
            placeholder="请输入组名"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="membersDialogVisible"
      :title="`${getGroupDisplayName(currentGroup)} - 成员列表`"
      width="800px"
    >
      <div class="dialog-toolbar">
        <Authority permission="ad:group:manage">
          <el-button type="primary" @click="handleAddMembers">
            添加成员
          </el-button>
        </Authority>
      </div>

      <el-table
        v-loading="loadingMembers"
        :data="groupMembers"
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="name" label="显示名称" />
        <el-table-column prop="department" label="部门" />
        <el-table-column prop="title" label="职位" />
        <el-table-column prop="enabled" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
              {{ scope.row.enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <Authority permission="ad:group:manage">
              <el-button
                type="danger"
                link
                @click="handleRemoveMember(scope.row)"
              >
                移除
              </el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <Authority permission="ad:view">
          <el-pagination
            v-model:current-page="memberCurrentPage"
            v-model:page-size="memberPageSize" 
            :page-sizes="[10, 20, 50, 100]"
            :total="memberTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleMemberSizeChange"
            @current-change="handleMemberCurrentChange"
          />
        </Authority>
      </div>
    </el-dialog>

    <el-dialog
      v-model="addMembersVisible"
      title="添加成员"
      width="600px"
      append-to-body
    >
      <el-form :model="memberForm" label-width="80px">
        <el-form-item label="选择用户">
          <el-select
            v-model="memberForm.members"
            multiple
            filterable
            remote
            :remote-method="searchUsers"
            :loading="loadingUsers"
            style="width: 100%"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.username"
              :label="user.name"
              :value="user.username"
            >
              <span>{{ user.name }}</span>
              <small style="color: #8492a6">({{ user.username }})</small>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addMembersVisible = false">取消</el-button>
        <Authority permission="ad:group:manage">
          <el-button type="primary" @click="confirmAddMembers">确定</el-button>
        </Authority>
      </template>
    </el-dialog>

    <el-dialog
      v-model="detailVisible"
      title="组详情"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
      class="group-detail-dialog"
    >
      <group-detail :group="currentGroup" v-if="detailVisible" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'
import { 
  getGroups, 
  createGroup, 
  updateGroup, 
  deleteGroup, 
  getGroupMembers, 
  addGroupMembers, 
  removeGroupMembers, 
  getUsers, 
  searchUsers as searchUsersAPI
} from '@/api/ad'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Edit, Delete, UserFilled, ArrowDown, View } from '@element-plus/icons-vue'
import GroupDetail from './GroupDetail.vue'
import { debounce } from 'lodash-es'
import Authority from '@/components/Authority/index.vue'

const props = defineProps({
  ouDn: {
    type: String,
    required: true
  }
})

const loading = ref(false)
const groups = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')

console.log('GroupList组件初始化, ouDn:', props.ouDn)

// 添加搜索缓存
const searchCache = new Map()

// 添加清除缓存的函数
const clearCache = () => {
  searchCache.clear()
}

// 添加防抖的fetchGroups函数
const debouncedFetchGroups = debounce(async () => {
  if (!props.ouDn) return
  
  loading.value = true
  try {
    const { data } = await getGroups({
      ou_dn: props.ouDn,
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value
    })
    
    if (Array.isArray(data.items)) {
      groups.value = data.items
      total.value = data.total
    } else {
      throw new Error('获取组列表失败：返回数据格式错误')
    }
  } catch (error) {
    console.error('获取组列表失败:', error)
    ElMessage.error(error.message || '获取组列表失败')
    groups.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}, 300)

// 替换原有的fetchGroups函数
const fetchGroups = () => {
  return debouncedFetchGroups()
}

// 监听OU变化
watch(() => props.ouDn, (newVal) => {
  if (newVal) {
    currentPage.value = 1
    fetchGroups()
  } else {
    groups.value = []
    total.value = 0
  }
}, { immediate: true })

// 监听搜索条件变化
watch(searchQuery, () => {
  currentPage.value = 1
  fetchGroups()
}, { immediate: true })

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchGroups()
})

onMounted(() => {
  if (props.ouDn) {
    fetchGroups()
  }
})

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchGroups()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchGroups()
}

const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const formRef = ref(null)
const form = ref({
  name: '',
  description: ''
})

const rules = {
  name: [
    { required: true, message: '请输入组名', trigger: 'blur' },
    { max: 256, message: '组名不能超过256个字符', trigger: 'blur' }
  ],
  description: [
    { max: 255, message: '描述不能超过255个字符', trigger: 'blur' }
  ]
}

const handleAddGroup = () => {
  dialogType.value = 'add'
  form.value = {
    name: '',
    description: ''
  }
  dialogVisible.value = true
}

const handleEditGroup = (row) => {
  dialogType.value = 'edit'
  form.value = {
    name: row.name,
    description: row.description
  }
  dialogVisible.value = true
}

const handleDeleteGroup = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除组"${row.name}"吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteGroup(row.name)
    ElMessage.success('删除成功')
    // 重置到第一页并刷新数据
    currentPage.value = 1
    await fetchGroups()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除组失败:', error)
      ElMessage.error(error.response?.data?.detail || '删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (dialogType.value === 'add') {
      await createGroup({
        ...form.value,
        ou_dn: props.ouDn
      })
      ElMessage.success('添加成功')
    } else {
      await updateGroup(form.value.name, {
        description: form.value.description
      })
      ElMessage.success('更新成功')
    }
    
    dialogVisible.value = false
    // 重置表单
    form.value = {
      name: '',
      description: ''
    }
    // 重置页码到第一页
    currentPage.value = 1
    // 刷新列表数据
    await fetchGroups()
  } catch (error) {
    console.error('保存组失败:', error)
    ElMessage.error(error.response?.data?.detail || '保存失败')
  }
}

const membersDialogVisible = ref(false)
const loadingMembers = ref(false)
const groupMembers = ref([])
const currentGroup = ref(null)

const memberCurrentPage = ref(1)
const memberPageSize = ref(20)
const memberTotal = ref(0)

const handleViewMembers = async (row) => {
  currentGroup.value = row
  membersDialogVisible.value = true
  memberCurrentPage.value = 1 // 重置页码
  await fetchGroupMembers()
}

const addMembersVisible = ref(false)
const memberForm = ref({
  members: []
})
const availableUsers = ref([])
const loadingUsers = ref(false)

const handleAddMembers = () => {
  memberForm.value.members = []
  addMembersVisible.value = true
}

const searchUsers = async (query) => {
  if (query.length < 2) return
  
  loadingUsers.value = true
  try {
    const { data } = await searchUsersAPI({  // 使用重命名后的API
      search: query,
      page: 1,
      page_size: 20
    })
    availableUsers.value = data.items
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    loadingUsers.value = false
  }
}

const confirmAddMembers = async () => {
  if (!memberForm.value.members.length) {
    ElMessage.warning('请选择要添加的成员')
    return
  }

  try {
    await addGroupMembers(currentGroup.value.name, memberForm.value.members)
    ElMessage.success('添加成员成功')
    addMembersVisible.value = false
    await fetchGroupMembers()
    // 刷新组列表以更新成员数量
    await fetchGroups()
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error(error.response?.data?.detail || '添加成员失败')
  }
}

const handleRemoveMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要从组"${getGroupDisplayName(currentGroup.value)}"中移除用户"${member.name || member.username}"吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await removeGroupMembers(currentGroup.value.name, [member.username])
    ElMessage.success('移除成员成功')
    await fetchGroupMembers() // 刷新成员列表
    clearCache() // 清除缓存
    await fetchGroups() // 刷新组列表以更新成员数量
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error(error.response?.data?.detail || '移除成员失败')
    }
  }
}

const fetchGroupMembers = async () => {
  if (!currentGroup.value) return
  
  loadingMembers.value = true
  try {
    const { data } = await getGroupMembers(currentGroup.value.name, {
      page: memberCurrentPage.value,
      page_size: memberPageSize.value
    })
    groupMembers.value = data.items || []
    memberTotal.value = data.total || 0
  } catch (error) {
    console.error('获取组成员失败:', error)
    ElMessage.error('获取组成员失败')
    groupMembers.value = []
    memberTotal.value = 0
  } finally {
    loadingMembers.value = false
  }
}

const handleMemberCurrentChange = (val) => {
  memberCurrentPage.value = val
  fetchGroupMembers()
}

const handleMemberSizeChange = (val) => {
  memberPageSize.value = val
  memberCurrentPage.value = 1
  fetchGroupMembers()
}

const getGroupDisplayName = (group) => {
  if (!group) return ''
  return group.name || '未命名组'
}

// 添加组详情相关的响应式变量
const detailVisible = ref(false)

// 添加显示详情的方法
const handleShowDetail = (group) => {
  currentGroup.value = group
  detailVisible.value = true
}

// 清理缓存
onUnmounted(() => {
  searchCache.clear()
})

// 暴露方法给父组件
defineExpose({
  handleAddGroup,
  handleSearch: (query) => {
    searchQuery.value = query
    fetchGroups()
  }
})
</script>

<style scoped>
.group-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.toolbar {
  display: none; /* 隐藏工具栏 */
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
}

.table-container {
  flex: 1;
  overflow: auto;
  padding: 0 16px;
}

.pagination-container {
  padding: 12px 16px;
  background: #fff;
  border-top: 1px solid #ebeef5;
  flex-shrink: 0;
}

.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-track {
  background: #f5f5f5;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-icon) {
  margin-right: 4px;
}

:deep(.el-button.el-button--primary.is-link) {
  padding: 0;
  height: auto;
  font-size: inherit;
}

:deep(.group-detail-dialog) {
  .el-dialog__body {
    padding: 0;
  }
}
</style> 