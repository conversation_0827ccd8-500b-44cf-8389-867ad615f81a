import requests
import json
import time

def test_ldap_endpoints():
    base_url = "http://localhost:8000/api/v1"
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(5)
    
    try:
        # 1. 测试获取LDAP配置
        print("\n1. 测试获取LDAP配置...")
        response = requests.get(f"{base_url}/auth/ldap-configs")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            configs = response.json()
            print(f"LDAP配置数量: {len(configs)}")
            for config in configs:
                print(f"  - {config.get('name', 'N/A')}: {config.get('server', 'N/A')}")
        else:
            print(f"错误: {response.text}")
    
    except requests.exceptions.ConnectionError:
        print("无法连接到服务器，请确保后端服务正在运行")
    except Exception as e:
        print(f"测试出错: {e}")

if __name__ == "__main__":
    test_ldap_endpoints() 