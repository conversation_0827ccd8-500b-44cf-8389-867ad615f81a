"""
IP地址匹配工具类
支持CIDR格式（如***********/24）和IP范围格式（如***********-*************）
"""
import ipaddress
import logging
from typing import List, Optional, Tuple
from ..models.ldap_config import LdapConfig

logger = logging.getLogger(__name__)

class IPMatcher:
    """IP地址匹配器"""
    
    @staticmethod
    def is_ip_in_range(ip: str, ip_range: str) -> bool:
        """
        检查IP地址是否在指定范围内
        
        Args:
            ip: 要检查的IP地址
            ip_range: IP范围，支持CIDR格式和范围格式
            
        Returns:
            bool: 是否匹配
        """
        try:
            target_ip = ipaddress.ip_address(ip)
            
            # CIDR格式 (如***********/24)
            if '/' in ip_range:
                network = ipaddress.ip_network(ip_range, strict=False)
                return target_ip in network
            
            # IP范围格式 (如***********-*************)
            elif '-' in ip_range:
                start_ip_str, end_ip_str = ip_range.split('-', 1)
                start_ip = ipaddress.ip_address(start_ip_str.strip())
                end_ip = ipaddress.ip_address(end_ip_str.strip())
                return start_ip <= target_ip <= end_ip
            
            # 单个IP地址
            else:
                range_ip = ipaddress.ip_address(ip_range)
                return target_ip == range_ip
                
        except Exception as e:
            logger.error(f"IP范围匹配失败: {ip} vs {ip_range}, 错误: {str(e)}")
            return False
    
    @staticmethod
    def find_matching_configs(client_ip: str, configs: List[LdapConfig]) -> List[Tuple[LdapConfig, str, str]]:
        """
        查找匹配客户端IP的LDAP配置
        
        Args:
            client_ip: 客户端IP地址
            configs: LDAP配置列表
            
        Returns:
            List[Tuple[LdapConfig, str, str]]: 匹配的配置列表，包含(配置对象, 匹配的IP范围, 匹配原因)
        """
        matching_configs = []
        
        for config in configs:
            # 只处理启用自动选择的配置
            if not config.auto_select_enabled or not config.is_active:
                continue
            
            # 检查IP范围匹配
            if config.ip_ranges:
                for ip_range in config.ip_ranges:
                    if IPMatcher.is_ip_in_range(client_ip, ip_range):
                        match_reason = f"匹配IP范围: {ip_range}"
                        matching_configs.append((config, ip_range, match_reason))
                        logger.info(f"配置 '{config.name}' 匹配客户端IP {client_ip} (范围: {ip_range})")
                        break  # 找到匹配的范围就跳出
        
        # 按优先级排序（数字越小优先级越高）
        matching_configs.sort(key=lambda x: x[0].priority)
        
        return matching_configs
    
    @staticmethod
    def get_best_config(client_ip: str, configs: List[LdapConfig]) -> Optional[Tuple[LdapConfig, str]]:
        """
        获取最佳匹配的LDAP配置
        
        Args:
            client_ip: 客户端IP地址
            configs: LDAP配置列表
            
        Returns:
            Optional[Tuple[LdapConfig, str]]: 最佳配置和匹配原因，如果没有匹配则返回None
        """
        matching_configs = IPMatcher.find_matching_configs(client_ip, configs)
        
        if matching_configs:
            best_config, matched_range, match_reason = matching_configs[0]
            logger.info(f"为客户端IP {client_ip} 选择配置: {best_config.name} (优先级: {best_config.priority})")
            return best_config, match_reason
        
        logger.warning(f"未找到匹配客户端IP {client_ip} 的LDAP配置")
        return None
    
    @staticmethod
    def get_fallback_config(configs: List[LdapConfig]) -> Optional[LdapConfig]:
        """
        获取备用配置（默认配置或第一个启用的配置）
        
        Args:
            configs: LDAP配置列表
            
        Returns:
            Optional[LdapConfig]: 备用配置
        """
        # 优先选择默认配置
        for config in configs:
            if config.is_default and config.is_active:
                return config
        
        # 如果没有默认配置，选择第一个启用的配置
        for config in configs:
            if config.is_active:
                return config
        
        return None
    
    @staticmethod
    def validate_ip_range(ip_range: str) -> Tuple[bool, str]:
        """
        验证IP范围格式是否正确
        
        Args:
            ip_range: IP范围字符串
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # CIDR格式
            if '/' in ip_range:
                ipaddress.ip_network(ip_range, strict=False)
                return True, ""
            
            # IP范围格式
            elif '-' in ip_range:
                start_ip_str, end_ip_str = ip_range.split('-', 1)
                start_ip = ipaddress.ip_address(start_ip_str.strip())
                end_ip = ipaddress.ip_address(end_ip_str.strip())
                
                if start_ip > end_ip:
                    return False, "起始IP地址不能大于结束IP地址"
                
                return True, ""
            
            # 单个IP地址
            else:
                ipaddress.ip_address(ip_range)
                return True, ""
                
        except Exception as e:
            return False, f"IP范围格式错误: {str(e)}"
    
    @staticmethod
    def get_range_description(ip_range: str) -> str:
        """
        获取IP范围的描述性文本
        
        Args:
            ip_range: IP范围字符串
            
        Returns:
            str: 描述性文本
        """
        try:
            if '/' in ip_range:
                network = ipaddress.ip_network(ip_range, strict=False)
                return f"网络段 {network} (包含 {network.num_addresses} 个地址)"
            
            elif '-' in ip_range:
                start_ip_str, end_ip_str = ip_range.split('-', 1)
                start_ip = ipaddress.ip_address(start_ip_str.strip())
                end_ip = ipaddress.ip_address(end_ip_str.strip())
                count = int(end_ip) - int(start_ip) + 1
                return f"IP范围 {start_ip} 到 {end_ip} (包含 {count} 个地址)"
            
            else:
                return f"单个IP地址 {ip_range}"
                
        except Exception:
            return f"IP范围: {ip_range}" 