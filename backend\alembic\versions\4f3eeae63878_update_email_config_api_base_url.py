"""update email config api base url

Revision ID: 4f3eeae63878
Revises: 0f75da6ad9e4
Create Date: 2025-05-23 11:24:26.515423

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4f3eeae63878'
down_revision: Union[str, None] = '0f75da6ad9e4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 更新现有邮箱配置的API基础URL
    connection = op.get_bind()
    
    # 检查表是否存在
    inspector = sa.inspect(connection)
    if 'email_configs' in inspector.get_table_names():
        # 更新api_base_url字段，将'https://api.exmail.qq.com'更新为'https://api.exmail.qq.com/cgi-bin'
        connection.execute(
            sa.text("""
                UPDATE email_configs 
                SET api_base_url = 'https://api.exmail.qq.com/cgi-bin' 
                WHERE api_base_url = 'https://api.exmail.qq.com' 
                   OR api_base_url IS NULL
            """)
        )


def downgrade() -> None:
    # 如果需要回滚，将URL改回去
    connection = op.get_bind()
    
    # 检查表是否存在
    inspector = sa.inspect(connection)
    if 'email_configs' in inspector.get_table_names():
        connection.execute(
            sa.text("""
                UPDATE email_configs 
                SET api_base_url = 'https://api.exmail.qq.com' 
                WHERE api_base_url = 'https://api.exmail.qq.com/cgi-bin'
            """)
        )
