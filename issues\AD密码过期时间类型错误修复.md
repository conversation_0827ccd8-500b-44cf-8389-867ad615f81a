# AD密码过期时间类型错误修复

## 问题描述
AD用户密码过期时间查询功能出现类型错误：
```
int() argument must be a string, a bytes-like object or a real number, not 'datetime.datetime'
```

## 错误分析
- **错误位置**：`backend/app/utils/ad_client.py` 第698行
- **错误原因**：`msDS-UserPasswordExpiryTimeComputed` 属性返回 `datetime.datetime` 对象，但代码尝试用 `int()` 转换
- **影响范围**：所有AD用户列表查询和密码过期状态显示功能

## 修复方案
在 `_calculate_password_expiry` 方法中添加类型检查：
1. 检查 `computed_expiry` 的类型
2. 如果是 `datetime` 对象，直接使用
3. 如果是数字，按原逻辑进行AD时间戳转换
4. 添加异常处理确保代码robust性

## 实施步骤
1. ✅ 修改 `_calculate_password_expiry` 方法
2. ✅ 添加类型检查逻辑
3. ✅ 保持向后兼容性
4. ✅ 添加详细的错误日志

## 技术细节
- 处理两种可能的返回类型：`datetime.datetime` 和数字时间戳
- 保持现有业务逻辑不变
- 添加 warning 级别日志记录类型转换问题

## 测试验证
修复后应验证：
- AD用户列表正常加载
- 密码过期状态正确显示
- 不再出现类型转换错误

## 完成状态
- ✅ 问题修复
- ✅ 代码提交
- ✅ 文档记录 