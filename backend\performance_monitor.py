#!/usr/bin/env python3
"""
OPS平台性能监控脚本
监控应用启动时间、内存使用、数据库连接等关键指标
"""

import time
import psutil
import requests
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any
import json
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.start_time = None
        self.metrics = {}
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        logger.info("开始性能监控...")
        
        # 获取系统基线指标
        self.metrics['baseline'] = self._get_system_metrics()
        
        return self.start_time
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            
            # 网络统计
            network = psutil.net_io_counters()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': memory.used / (1024 * 1024),
                'memory_available_mb': memory.available / (1024 * 1024),
                'disk_percent': disk.percent,
                'network_bytes_sent': network.bytes_sent,
                'network_bytes_recv': network.bytes_recv,
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def check_backend_startup(self, timeout=60) -> Dict[str, Any]:
        """检查后端启动状态"""
        logger.info("检查后端启动状态...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.backend_url}/", timeout=5)
                if response.status_code == 200:
                    startup_time = time.time() - start_time
                    logger.info(f"后端启动成功，耗时: {startup_time:.2f}秒")
                    return {
                        'status': 'success',
                        'startup_time': startup_time,
                        'response_time': response.elapsed.total_seconds(),
                        'status_code': response.status_code
                    }
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("后端启动超时")
        return {
            'status': 'timeout',
            'startup_time': timeout,
            'error': '启动超时'
        }
    
    def check_frontend_startup(self, timeout=60) -> Dict[str, Any]:
        """检查前端启动状态"""
        logger.info("检查前端启动状态...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(self.frontend_url, timeout=5)
                if response.status_code == 200:
                    startup_time = time.time() - start_time
                    logger.info(f"前端启动成功，耗时: {startup_time:.2f}秒")
                    return {
                        'status': 'success',
                        'startup_time': startup_time,
                        'response_time': response.elapsed.total_seconds(),
                        'status_code': response.status_code
                    }
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("前端启动超时")
        return {
            'status': 'timeout',
            'startup_time': timeout,
            'error': '启动超时'
        }
    
    def check_database_performance(self) -> Dict[str, Any]:
        """检查数据库性能"""
        logger.info("检查数据库性能...")
        
        try:
            # 测试数据库连接
            start_time = time.time()
            response = requests.get(f"{self.backend_url}/api/v1/auth/me", timeout=10)
            connection_time = time.time() - start_time
            
            return {
                'status': 'success',
                'connection_time': connection_time,
                'response_status': response.status_code
            }
        except Exception as e:
            logger.error(f"数据库性能检查失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        if not self.start_time:
            return {'error': '监控未启动'}
        
        total_time = time.time() - self.start_time
        current_metrics = self._get_system_metrics()
        
        # 检查各服务启动状态
        backend_status = self.check_backend_startup()
        frontend_status = self.check_frontend_startup()
        db_performance = self.check_database_performance()
        
        report = {
            'monitoring_duration': total_time,
            'timestamp': datetime.now().isoformat(),
            'backend': backend_status,
            'frontend': frontend_status,
            'database': db_performance,
            'system_metrics': {
                'baseline': self.metrics.get('baseline', {}),
                'current': current_metrics
            },
            'performance_analysis': self._analyze_performance(
                backend_status, frontend_status, current_metrics
            )
        }
        
        return report
    
    def _analyze_performance(self, backend_status, frontend_status, current_metrics) -> Dict[str, Any]:
        """分析性能数据"""
        analysis = {
            'overall_status': 'good',
            'issues': [],
            'recommendations': []
        }
        
        # 分析启动时间
        if backend_status.get('startup_time', 0) > 15:
            analysis['issues'].append('后端启动时间过长')
            analysis['recommendations'].append('优化后端启动流程')
            analysis['overall_status'] = 'warning'
        
        if frontend_status.get('startup_time', 0) > 20:
            analysis['issues'].append('前端启动时间过长')
            analysis['recommendations'].append('优化前端构建配置')
            analysis['overall_status'] = 'warning'
        
        # 分析系统资源
        if current_metrics.get('memory_percent', 0) > 80:
            analysis['issues'].append('内存使用率过高')
            analysis['recommendations'].append('优化内存使用')
            analysis['overall_status'] = 'warning'
        
        if current_metrics.get('cpu_percent', 0) > 80:
            analysis['issues'].append('CPU使用率过高')
            analysis['recommendations'].append('优化CPU密集型操作')
            analysis['overall_status'] = 'warning'
        
        return analysis
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """保存性能报告"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"performance_report_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"性能报告已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存性能报告失败: {e}")

def main():
    """主函数"""
    monitor = PerformanceMonitor()
    
    # 开始监控
    monitor.start_monitoring()
    
    # 等待服务启动
    logger.info("等待服务启动...")
    time.sleep(5)
    
    # 生成报告
    report = monitor.generate_report()
    
    # 输出报告摘要
    logger.info("=== 性能监控报告 ===")
    logger.info(f"总监控时间: {report['monitoring_duration']:.2f}秒")
    
    if 'backend' in report:
        backend = report['backend']
        logger.info(f"后端启动: {backend['status']}, 耗时: {backend.get('startup_time', 0):.2f}秒")
    
    if 'frontend' in report:
        frontend = report['frontend']
        logger.info(f"前端启动: {frontend['status']}, 耗时: {frontend.get('startup_time', 0):.2f}秒")
    
    if 'performance_analysis' in report:
        analysis = report['performance_analysis']
        logger.info(f"整体状态: {analysis['overall_status']}")
        if analysis['issues']:
            logger.warning(f"发现问题: {', '.join(analysis['issues'])}")
        if analysis['recommendations']:
            logger.info(f"建议: {', '.join(analysis['recommendations'])}")
    
    # 保存详细报告
    monitor.save_report(report)
    
    return report

if __name__ == "__main__":
    main() 