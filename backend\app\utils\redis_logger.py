import logging
import time
import json
import os
from datetime import datetime
from functools import wraps

# 创建Redis日志文件目录
os.makedirs("logs", exist_ok=True)

# 配置Redis日志记录器
redis_logger = logging.getLogger("redis")
redis_logger.setLevel(logging.DEBUG)

# 创建文件处理器
log_filename = f"logs/redis_{datetime.now().strftime('%Y%m%d')}.log"
file_handler = logging.FileHandler(log_filename, encoding="utf-8")
file_handler.setLevel(logging.DEBUG)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# 将处理器添加到日志记录器
redis_logger.addHandler(file_handler)

# 控制台处理器(可选)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)
redis_logger.addHandler(console_handler)

class RedisStatsTracker:
    """跟踪Redis缓存操作统计信息"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.stats = {
            "get_count": 0,
            "get_miss_count": 0,
            "get_hit_count": 0,
            "set_count": 0,
            "delete_count": 0, 
            "clear_pattern_count": 0,
            "clear_all_count": 0,
            "total_operation_time": 0,
            "operation_count": 0,
            "errors": 0
        }
    
    def record_get(self, key, hit, duration):
        """记录get操作"""
        self.stats["get_count"] += 1
        self.stats["operation_count"] += 1
        self.stats["total_operation_time"] += duration
        
        if hit:
            self.stats["get_hit_count"] += 1
        else:
            self.stats["get_miss_count"] += 1
            
        # 记录详细日志
        redis_logger.debug(f"GET {key} - {'HIT' if hit else 'MISS'} - {duration:.6f}s")
    
    def record_set(self, key, duration):
        """记录set操作"""
        self.stats["set_count"] += 1
        self.stats["operation_count"] += 1
        self.stats["total_operation_time"] += duration
        
        # 记录详细日志
        redis_logger.debug(f"SET {key} - {duration:.6f}s")
    
    def record_delete(self, key, count, duration):
        """记录delete操作"""
        self.stats["delete_count"] += 1
        self.stats["operation_count"] += 1
        self.stats["total_operation_time"] += duration
        
        # 记录详细日志
        redis_logger.debug(f"DEL {key} - {count} keys - {duration:.6f}s")
    
    def record_clear_pattern(self, pattern, count, duration):
        """记录clear_pattern操作"""
        self.stats["clear_pattern_count"] += 1
        self.stats["operation_count"] += 1
        self.stats["total_operation_time"] += duration
        
        # 记录详细日志
        redis_logger.info(f"CLEAR PATTERN {pattern} - {count} keys - {duration:.6f}s")
    
    def record_clear_all(self, success, duration):
        """记录clear_all操作"""
        self.stats["clear_all_count"] += 1
        self.stats["operation_count"] += 1
        self.stats["total_operation_time"] += duration
        
        # 记录详细日志
        redis_logger.warning(f"CLEAR ALL - {'SUCCESS' if success else 'FAILURE'} - {duration:.6f}s")
    
    def record_error(self, operation, error):
        """记录错误"""
        self.stats["errors"] += 1
        
        # 记录详细日志
        redis_logger.error(f"ERROR {operation} - {str(error)}")
    
    def get_stats(self):
        """获取统计信息"""
        stats = self.stats.copy()
        if stats["operation_count"] > 0:
            stats["avg_operation_time"] = stats["total_operation_time"] / stats["operation_count"]
        else:
            stats["avg_operation_time"] = 0
            
        if stats["get_count"] > 0:
            stats["hit_ratio"] = stats["get_hit_count"] / stats["get_count"]
        else:
            stats["hit_ratio"] = 0
            
        return stats
            
# 创建全局统计跟踪器
stats_tracker = RedisStatsTracker()

def log_redis_operation(operation_type):
    """装饰器函数，用于记录Redis操作性能"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # 获取第一个参数(self)后的第一个参数，通常是key
                key = args[1] if len(args) > 1 else "Unknown"
                
                # 根据操作类型记录统计信息
                if operation_type == "get":
                    stats_tracker.record_get(key, result is not None, duration)
                elif operation_type == "set":
                    stats_tracker.record_set(key, duration)
                elif operation_type == "delete":
                    stats_tracker.record_delete(key, 1 if result else 0, duration)
                elif operation_type == "clear_pattern":
                    stats_tracker.record_clear_pattern(key, result or 0, duration)
                elif operation_type == "clear_all":
                    stats_tracker.record_clear_all(result, duration)
                
                return result
            except Exception as e:
                stats_tracker.record_error(operation_type, e)
                raise
                
        return wrapper
    return decorator 