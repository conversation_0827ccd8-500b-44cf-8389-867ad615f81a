"""Add email management tables

Revision ID: 0f75da6ad9e4
Revises: 059e147ac2bb
Create Date: 2025-05-23 10:26:39.663989

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '0f75da6ad9e4'
down_revision: Union[str, None] = '059e147ac2bb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('corp_id', sa.String(length=100), nullable=False, comment='企业ID'),
    sa.Column('corp_secret', sa.String(length=200), nullable=False, comment='企业密钥'),
    sa.Column('access_token', sa.String(length=200), nullable=True, comment='访问令牌'),
    sa.Column('token_expires_at', sa.DateTime(timezone=True), nullable=True, comment='令牌过期时间'),
    sa.Column('api_base_url', sa.String(length=200), nullable=True, comment='API基础URL'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_configs_id'), 'email_configs', ['id'], unique=False)
    op.create_table('email_departments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('dept_id', sa.String(length=50), nullable=True, comment='部门ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='部门名称'),
    sa.Column('parent_id', sa.String(length=50), nullable=True, comment='父部门ID'),
    sa.Column('order', sa.Integer(), nullable=True, comment='排序'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_departments_dept_id'), 'email_departments', ['dept_id'], unique=True)
    op.create_index(op.f('ix_email_departments_id'), 'email_departments', ['id'], unique=False)
    op.create_table('email_groups',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('groupid', sa.String(length=100), nullable=True, comment='群组ID'),
    sa.Column('groupname', sa.String(length=100), nullable=False, comment='群组名称'),
    sa.Column('userlist', sa.Text(), nullable=True, comment='成员列表'),
    sa.Column('groupdesc', sa.Text(), nullable=True, comment='群组描述'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_groups_groupid'), 'email_groups', ['groupid'], unique=True)
    op.create_index(op.f('ix_email_groups_id'), 'email_groups', ['id'], unique=False)
    op.create_table('email_tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tagid', sa.Integer(), nullable=True, comment='标签ID'),
    sa.Column('tagname', sa.String(length=100), nullable=False, comment='标签名称'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_tags_id'), 'email_tags', ['id'], unique=False)
    op.create_index(op.f('ix_email_tags_tagid'), 'email_tags', ['tagid'], unique=True)
    op.create_table('email_members',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('userid', sa.String(length=50), nullable=True, comment='用户ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='姓名'),
    sa.Column('department_id', sa.String(length=50), nullable=False, comment='部门ID'),
    sa.Column('position', sa.String(length=100), nullable=True, comment='职位'),
    sa.Column('mobile', sa.String(length=20), nullable=True, comment='手机号'),
    sa.Column('tel', sa.String(length=30), nullable=True, comment='固定电话'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='邮箱'),
    sa.Column('cpwd_login', sa.Integer(), nullable=True, comment='是否允许使用密码登录'),
    sa.Column('extattr', sa.JSON(), nullable=True, comment='扩展属性'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['department_id'], ['email_departments.dept_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_members_id'), 'email_members', ['id'], unique=False)
    op.create_index(op.f('ix_email_members_userid'), 'email_members', ['userid'], unique=True)
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.drop_index('ix_ad_sync_locks_lock_name', table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    op.drop_column('assets', 'inspection_date')
    op.drop_column('assets', 'inspection_status')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('inspection_status', sa.VARCHAR(length=20), nullable=True))
    op.add_column('assets', sa.Column('inspection_date', sa.DATETIME(), nullable=True))
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), nullable=True),
    sa.Column('locked_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index('ix_ad_sync_locks_lock_name', 'ad_sync_locks', ['lock_name'], unique=False)
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    op.drop_index(op.f('ix_email_members_userid'), table_name='email_members')
    op.drop_index(op.f('ix_email_members_id'), table_name='email_members')
    op.drop_table('email_members')
    op.drop_index(op.f('ix_email_tags_tagid'), table_name='email_tags')
    op.drop_index(op.f('ix_email_tags_id'), table_name='email_tags')
    op.drop_table('email_tags')
    op.drop_index(op.f('ix_email_groups_id'), table_name='email_groups')
    op.drop_index(op.f('ix_email_groups_groupid'), table_name='email_groups')
    op.drop_table('email_groups')
    op.drop_index(op.f('ix_email_departments_id'), table_name='email_departments')
    op.drop_index(op.f('ix_email_departments_dept_id'), table_name='email_departments')
    op.drop_table('email_departments')
    op.drop_index(op.f('ix_email_configs_id'), table_name='email_configs')
    op.drop_table('email_configs')
    # ### end Alembic commands ###
