# 移动端仪表板简化和主题设置迁移

## 任务描述
根据用户需求，对移动端仪表板进行简化，去掉不必要的功能模块，并将主题设置迁移到系统管理模块中。

## 具体修改内容

### 1. 移动端仪表板简化
**文件**: `frontend/src/mobile/views/dashboard/index.vue`

#### 移除的功能模块
- ✅ **快速入口部分**：移除"应用中心"快速入口
- ✅ **系统设置部分**：移除主题切换功能
- ✅ **最近活动部分**：完全移除最近活动列表

#### 保留的功能
- ✅ **统计卡片**：保留AD用户、邮箱账号、资产设备、在线终端的统计展示
- ✅ **统计卡片点击跳转**：保持原有的跳转到对应模块功能

#### 代码清理
- ✅ 移除 `ThemeSwitch` 组件导入
- ✅ 移除 `goToApps()` 函数
- ✅ 移除 `recentActivities` 响应式数据
- ✅ 简化模板结构

### 2. 系统管理模块增强
**文件**: `frontend/src/mobile/views/system/index.vue`

#### 新增功能
- ✅ **外观设置分组**：添加"外观设置"分组
- ✅ **主题切换集成**：将主题切换功能迁移到系统管理

#### 功能布局
```vue
<!-- 功能菜单 -->
<van-grid :column-num="2" :gutter="16">
  <!-- 原有的4个功能项 -->
</van-grid>

<!-- 外观设置 -->
<van-cell-group title="外观设置" inset>
  <theme-switch />
</van-cell-group>

<!-- 原有的统计信息等 -->
```

## 技术实现

### 组件迁移
- 从仪表板移除 `ThemeSwitch` 组件
- 在系统管理页面导入并使用 `ThemeSwitch` 组件
- 保持组件功能不变，只是改变位置

### 布局优化
- 仪表板布局更加简洁，专注于数据展示
- 系统管理页面功能更加集中，包含所有系统相关设置

## 用户体验改进

### 仪表板优化
- ✅ **简化界面**：去掉冗余功能，界面更清爽
- ✅ **专注数据**：突出统计数据的展示
- ✅ **快速导航**：通过统计卡片直接跳转到对应模块

### 系统管理增强
- ✅ **功能集中**：所有系统设置功能集中在一个页面
- ✅ **逻辑清晰**：外观设置单独分组，便于用户查找
- ✅ **操作便捷**：主题切换更容易找到和使用

## 影响评估

### 正面影响
1. **界面简化**：仪表板更加简洁，减少认知负担
2. **功能归类**：主题设置归属到系统管理，逻辑更清晰
3. **维护性提升**：减少仪表板复杂度，便于后续维护

### 注意事项
1. **用户习惯**：用户可能需要适应主题设置的新位置
2. **功能发现**：确保用户能够在系统管理中找到主题设置
3. **一致性**：保持与桌面端系统管理的功能对应关系

## 后续计划

### 短期优化
- 考虑在系统管理中添加更多外观设置选项
- 完善系统管理页面的其他功能模块

### 长期规划
- 统一移动端和桌面端的系统设置结构
- 考虑添加个性化设置选项

## 相关文件
- `frontend/src/mobile/views/dashboard/index.vue` - 仪表板主页面
- `frontend/src/mobile/views/system/index.vue` - 系统管理页面
- `frontend/src/mobile/components/common/ThemeSwitch.vue` - 主题切换组件 