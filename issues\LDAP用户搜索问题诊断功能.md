# LDAP用户搜索问题诊断功能开发

## 任务背景
系统日志显示"未找到用户: *********"警告，用户确认使用sAMAccountName但仍无法找到。需要开发诊断工具来帮助管理员快速定位LDAP用户搜索配置问题。

## 问题分析
1. **搜索范围限制**：user_search_base配置可能过于具体
2. **绑定用户权限不足**：搜索用户可能没有查询目标OU的权限
3. **搜索过滤器问题**：当前只搜索sAMAccountName，没有备用方案
4. **缺少调试信息**：无法看到实际的搜索参数和结果

## 实施方案

### ✅ 第一步：增强LDAP搜索调试功能
**修改文件**: `backend/app/services/ldap_auth.py`
- 在`_find_user_dn`方法中添加详细的搜索参数日志记录
- 记录实际使用的搜索基础DN、搜索过滤器和LDAP服务器信息
- 输出搜索结果数量和找到的用户详情
- 新增`_diagnose_search_issue`方法进行自动问题诊断

### ✅ 第二步：创建LDAP诊断工具接口
**修改文件**: `backend/app/api/v1/ldap_config.py`
- 新增`POST /system/ldap-config/diagnose-user-search`接口
- 实现多重诊断测试：
  - 搜索基础DN验证
  - 目标用户搜索
  - 示例用户列表
  - 扩展范围搜索
- 提供智能化配置建议

**修改文件**: `backend/app/schemas/ldap_config.py`
- 新增`LdapUserSearchDiagnose`模型
- 包含完整的LDAP配置参数和目标用户名

### ✅ 第三步：前端诊断界面
**修改文件**: `frontend/src/views/system/components/LdapConfigManagement.vue`
- 在用户登录测试区域添加"诊断用户搜索"按钮
- 实现诊断结果的友好展示：
  - 搜索参数详情
  - 各项测试结果（成功/失败状态）
  - 找到的用户列表
  - 示例用户展示
  - 配置建议列表
- 添加对应的CSS样式美化

## 功能特性

### 诊断测试项目
1. **搜索基础DN验证** - 检查配置的DN是否存在且可访问
2. **目标用户搜索** - 使用配置的过滤器搜索特定用户
3. **示例用户列表** - 显示搜索基础DN下的现有用户示例
4. **扩展范围搜索** - 在更广泛的域范围内搜索目标用户

### 智能建议
- 基于测试结果提供针对性的配置建议
- 识别常见问题并给出解决方案
- 推荐更合适的搜索过滤器和基础DN配置

## 使用方法
1. 在LDAP配置页面填写基本配置信息
2. 在"用户登录测试"区域输入要诊断的用户名
3. 点击"诊断用户搜索"按钮
4. 查看详细的诊断结果和建议

## 预期效果
- 管理员可以快速识别LDAP用户搜索配置问题
- 通过详细的日志和诊断信息加快问题定位
- 减少LDAP集成配置的试错时间
- 提供专业的配置建议和最佳实践指导

## 技术细节
- 使用ldap3库进行多层次的LDAP查询测试
- 实现异常安全的诊断流程
- 提供结构化的诊断结果数据
- 支持实时配置验证，无需保存配置即可测试

## 问题修复记录

### ✅ 修复1：Schema导入错误
**问题**：`AttributeError: module 'app.schemas' has no attribute 'LdapUserSearchDiagnose'`
**解决方案**：在`backend/app/schemas/__init__.py`中添加了缺失的导入声明

### ✅ 修复2：诊断算法逻辑错误  
**问题**：错误提示"用户存在于域中但不在指定的user_search_base中"
**解决方案**：修复了建议生成逻辑，正确识别用户搜索成功的情况

### ✅ 修复3：LDAP搜索范围常量错误
**问题**：`获取用户信息失败: invalid scope type`
**原因**：缺少BASE常量导入，search_scope参数使用了字符串'base'而非常量
**解决方案**：
- 在导入中添加BASE常量：`from ldap3 import Server, Connection, ALL, SIMPLE, SUBTREE, BASE`
- 修复_get_user_info和_diagnose_search_issue方法中的search_scope参数 