from typing import List, Optional
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.permission import Permission
from app.schemas.permission import PermissionCreate, PermissionUpdate

class CRUDPermission(CRUDBase[Permission, PermissionCreate, PermissionUpdate]):
    def get_by_code(self, db: Session, code: str) -> Optional[Permission]:
        """通过权限码获取权限"""
        return db.query(Permission).filter(Permission.code == code).first()
    
    def get_by_module(self, db: Session, module: str) -> List[Permission]:
        """获取指定模块的所有权限"""
        return db.query(Permission).filter(Permission.module == module).all()
    
    def get_multi_by_codes(self, db: Session, codes: List[str]) -> List[Permission]:
        """通过权限码列表获取权限列表"""
        return db.query(Permission).filter(Permission.code.in_(codes)).all()
    
    def get_modules(self, db: Session) -> List[str]:
        """获取所有模块名称"""
        result = db.query(Permission.module).distinct().all()
        return [r[0] for r in result]

permission_crud = CRUDPermission(Permission) 