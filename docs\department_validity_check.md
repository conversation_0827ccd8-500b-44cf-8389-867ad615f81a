# 部门同步功能优化

## 功能概述

对从基础信息同步部门结构功能进行了全面优化，包括：
1. **部门有效性检查**：智能过滤掉无效的部门，避免创建空部门或已弃用的部门
2. **公司信息显示**：在同步结果中显示部门所属的公司信息
3. **重名部门处理**：自动为来自不同公司的同名部门添加公司前缀以区分

## 功能特性

### 1. 在职人员检查
- 检查部门是否有在职人员（状态为：试用、正式、临时、试用延期）
- 支持递归检查：如果父部门没有直接人员，但子部门有在职人员，父部门仍会被保留
- 无在职人员的部门将被跳过，不会在腾讯企业邮箱中创建

### 2. 弃用部门检查
- 自动识别包含弃用标识的部门名称
- 弃用关键词包括：已弃用、停用、废弃、注销、删除
- 包含这些关键词的部门将被自动跳过

### 3. 可配置选项
- 通过 `skip_empty_departments` 参数控制是否启用部门有效性检查
- 默认值为 `true`，即默认启用检查
- 可以在前端界面中通过复选框控制

## 使用方法

### 前端界面
1. 打开"邮箱管理" -> "同步管理"
2. 点击"从基础信息同步部门结构"
3. 在选项中可以看到"跳过无在职人员的部门"复选框
4. 勾选此选项即可启用部门有效性检查

### API调用
```json
{
  "source": "all",
  "mode": "create_only",
  "skip_empty_departments": true,
  "create_hierarchy": true
}
```

## 公司信息显示

### 前端界面改进
- 在部门操作详情表格中新增"所属公司"列
- 显示每个部门所属的公司名称（去掉括号部分）
- 便于识别和管理来自不同公司的部门

### 重名部门处理
- 自动检测来自不同公司的同名部门
- 为重名部门添加公司前缀，格式：`公司名-部门名`
- 公司名过长时自动截取前10个字符
- 确保部门名称的唯一性和可识别性

## 统计信息

同步结果中会显示以下统计信息：
- **总部门数**: 从基础信息中提取的部门总数
- **有效部门数**: 通过有效性检查的部门数量
- **无效部门数**: 被过滤掉的无效部门数量（包含原因）
- **创建部门数**: 实际在腾讯企业邮箱中创建的部门数量
- **所属公司**: 每个部门所属的公司信息

## 日志记录

系统会详细记录部门有效性检查的过程：
- 记录每个被跳过的部门及其原因
- 记录在职人员统计信息
- 记录递归检查的结果

## 示例场景

### 场景1：空部门过滤
```
部门A: 技术部 (5个在职人员) ✓ 保留
部门B: 人事部 (0个在职人员) ✗ 跳过 - 无在职人员
```

### 场景2：弃用部门过滤
```
部门A: 销售部 (3个在职人员) ✓ 保留
部门B: 已弃用的市场部 (2个在职人员) ✗ 跳过 - 包含弃用标识
```

### 场景3：层级检查
```
总部 (0个直接人员)
├── 技术部 (5个在职人员) ✓ 保留
└── 财务部 (3个在职人员) ✓ 保留

结果：总部也会被保留，因为其子部门有在职人员
```

## 注意事项

1. **递归检查**: 父部门的保留取决于其所有子部门的人员情况
2. **关键词匹配**: 弃用关键词检查是基于部门名称的字符串匹配
3. **性能影响**: 启用检查会增加一些处理时间，但能显著减少无效部门的创建
4. **数据一致性**: 建议在同步前确保基础信息中的人员状态数据是最新的

## 技术实现

- **后端**: `DepartmentStructureSyncService._filter_valid_departments()` 方法
- **前端**: Vue3 组件中的复选框控制
- **测试**: 完整的单元测试覆盖各种场景
- **API**: 兼容现有的部门同步API，通过参数控制功能开关
