declare module 'qrcode' {
  interface QRCodeOptions {
    version?: number
    errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
    width?: number
    height?: number
    margin?: number
    scale?: number
    color?: {
      dark?: string
      light?: string
    }
    type?: string
    quality?: number
    maskPattern?: number
    toSJISFunc?: (codePoint: string) => number
  }

  interface QRCode {
    toCanvas(
      canvas: HTMLCanvasElement,
      text: string,
      options?: QRCodeOptions
    ): Promise<void>
    toDataURL(text: string, options?: QRCodeOptions): Promise<string>
    toString(text: string, options?: QRCodeOptions): Promise<string>
    toFile(
      path: string,
      text: string,
      options?: QRCodeOptions
    ): Promise<void>
    toFileStream(
      stream: NodeJS.WriteStream,
      text: string,
      options?: QRCodeOptions
    ): Promise<void>
    toBuffer(text: string, options?: QRCodeOptions): Promise<Buffer>
  }

  const QRCode: QRCode
  export default QRCode
} 