import request from '@/utils/request'
import type { EcologyUser, SyncConfig } from '@/types/ecology'

// 直接导出的函数
export function getCompanies() {
  return request({
    url: '/ecology/companies',
    method: 'get'
  })
}

export function getDepartments() {
  return request({
    url: '/ecology/departments',
    method: 'get'
  })
}

export const ecologyApi = {
  // 获取泛微用户数据（直接从泛微数据库获取）
  getEcologyUsers: () => {
    return request.get<EcologyUser[]>('/ecology/users')
  },

  // 获取公司列表
  getCompanies: () => {
    return request.get<Array<{id: number, name: string}>>('/ecology/companies')
  },

  // 获取部门列表
  getDepartments: () => {
    return request.get<Array<{id: number, name: string, company_id: number}>>('/ecology/departments')
  },

  // 按公司ID获取用户
  getUsersByCompany: (companyId: number) => {
    return request.get<EcologyUser[]>(`/ecology/users/by-company/${companyId}`)
  },

  // 按部门ID获取用户
  getUsersByDepartment: (deptId: number) => {
    return request.get<EcologyUser[]>(`/ecology/users/by-department/${deptId}`)
  },

  // 获取本地存储的泛微用户数据
  getLocalEcologyUsers: (params: {
    skip?: number
    limit?: number
    keyword?: string
    exact_match?: boolean
  }) => {
    return request.get<EcologyUser[]>('/ecology-sync/users', { params })
  },

  // 手动触发同步
  syncEcologyUsers: () => {
    return request.post<{ status: string, message: string }>('/ecology-sync/sync')
  },

  // 获取同步状态
  getSyncStatus: () => {
    return request.get<SyncConfig>('/ecology-sync/sync/status')
  },

  // 更新同步时间
  updateSyncTime: (syncTime: string) => {
    return request.put<SyncConfig>('/ecology-sync/sync/time', {
      sync_time: syncTime
    })
  },

  // 导出人员数据
  exportEcologyUsers: () => {
    return request.get('/ecology-sync/users/export', {
      responseType: 'blob'
    })
  }
}