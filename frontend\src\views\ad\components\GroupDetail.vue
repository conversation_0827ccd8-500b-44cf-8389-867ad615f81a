<template>
  <div class="group-detail-container">
    <div class="header">
      <div class="group-icon">
        <el-avatar :size="64" :icon="UserFilled" />
      </div>
      <div class="group-info">
        <h2>{{ group.name }}</h2>
        <p class="description">{{ group.description || '暂无描述' }}</p>
      </div>
    </div>

    <el-divider />

    <div class="content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="组名">{{ group.name }}</el-descriptions-item>
        <el-descriptions-item label="成员数">{{ group.memberCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="类型">
          <el-tag :type="isSystemGroup ? 'info' : 'success'">
            {{ isSystemGroup ? '系统组' : '安全组' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="描述">{{ group.description || '-' }}</el-descriptions-item>
      </el-descriptions>

      <div class="section">
        <div class="section-header">
          <h3>成员列表</h3>
          <div class="section-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索成员"
              clearable
              @input="handleSearch"
              @clear="handleClearSearch"
              style="width: 220px; margin-right: 10px;"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-dropdown @command="handleExportCommand" style="margin-right: 10px;">
              <el-button type="success" :loading="exporting">
                <el-icon><Download /></el-icon>导出
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="xlsx">Excel格式</el-dropdown-item>
                  <el-dropdown-item command="csv">CSV格式</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <Authority permission="ad:group:manage">
              <el-button type="primary" @click="handleAddMembers" :loading="addingMembers">
                <el-icon><Plus /></el-icon>添加成员
              </el-button>
            </Authority>
          </div>
        </div>
        <div class="members-container">
          <el-table v-loading="loadingMembers" :data="members" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="name" label="显示名称" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="title" label="职位" />
            <el-table-column prop="enabled" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'danger'" size="small">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right" align="center">
              <template #default="{ row }">
                <Authority permission="ad:group:manage">
                  <el-button type="danger" link @click="handleRemoveMember(row)">
                    移除
                  </el-button>
                </Authority>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 添加成员对话框 -->
    <el-dialog
      v-model="addMembersVisible"
      title="添加成员"
      width="600px"
      append-to-body
    >
      <el-form :model="memberForm" label-width="80px">
        <el-form-item label="选择用户">
          <el-select
            v-model="memberForm.members"
            multiple
            filterable
            remote
            :remote-method="searchUsers"
            :loading="loadingUsers"
            style="width: 100%"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.username"
              :label="user.name"
              :value="user.username"
            >
              <span>{{ user.name }}</span>
              <small style="color: #8492a6">({{ user.username }})</small>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addMembersVisible = false">取消</el-button>
        <Authority permission="ad:group:manage">
          <el-button type="primary" @click="confirmAddMembers">确定</el-button>
        </Authority>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getGroupMembers, addGroupMembers, removeGroupMembers, exportGroupMembers, searchUsers as searchUsersAPI } from '@/api/ad'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UserFilled, Plus, Search, Download } from '@element-plus/icons-vue'
import { saveAs } from 'file-saver'
import Authority from '@/components/Authority/index.vue'

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const members = ref([])
const loadingMembers = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const searchKeyword = ref('')
const exporting = ref(false)

const isSystemGroup = computed(() => {
  const name = props.group.name.toLowerCase()
  return name.includes('domain') || name.includes('builtin')
})

const fetchMembers = async () => {
  loadingMembers.value = true
  try {
    const { data } = await getGroupMembers(props.group.name, {
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchKeyword.value
    })
    members.value = data.items || []
    total.value = data.total || 0
  } catch (error) {
    console.error('获取组成员失败:', error)
    ElMessage.error('获取组成员失败')
  } finally {
    loadingMembers.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  fetchMembers()
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchMembers()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchMembers()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchMembers()
}

// 添加成员相关
const addMembersVisible = ref(false)
const addingMembers = ref(false)
const memberForm = ref({
  members: []
})
const availableUsers = ref([])
const loadingUsers = ref(false)

const handleAddMembers = () => {
  memberForm.value.members = []
  addMembersVisible.value = true
}

const searchUsers = async (query) => {
  if (query.length < 2) return

  loadingUsers.value = true
  try {
    const { data } = await searchUsersAPI({
      search: query,
      page: 1,
      page_size: 20
    })
    availableUsers.value = data.items
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    loadingUsers.value = false
  }
}

const confirmAddMembers = async () => {
  if (!memberForm.value.members.length) {
    ElMessage.warning('请选择要添加的成员')
    return
  }

  addingMembers.value = true
  try {
    await addGroupMembers(props.group.name, memberForm.value.members)
    ElMessage.success('添加成员成功')
    addMembersVisible.value = false
    await fetchMembers()
  } catch (error) {
    console.error('添加成员失败:', error)
    ElMessage.error(error.response?.data?.detail || '添加成员失败')
  } finally {
    addingMembers.value = false
  }
}

const handleRemoveMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要从组"${props.group.name}"中移除用户"${member.name || member.username}"吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await removeGroupMembers(props.group.name, [member.username])
    ElMessage.success('移除成员成功')
    await fetchMembers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
      ElMessage.error(error.response?.data?.detail || '移除成员失败')
    }
  }
}

// 处理导出命令
const handleExportCommand = async (command) => {
  if (!props.group || !props.group.name) {
    ElMessage.warning('无法导出组成员')
    return
  }

  exporting.value = true
  try {
    const response = await exportGroupMembers(
      props.group.name,
      command,
      searchKeyword.value
    )

    // 生成文件名
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `组_${props.group.name}_成员_${timestamp}.${command}`

    // 创建Blob并下载
    const blob = new Blob([response.data], {
      type: command === 'xlsx'
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv;charset=utf-8-sig'
    })
    saveAs(blob, filename)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

onMounted(() => {
  fetchMembers()
})
</script>

<style scoped>
.group-detail-container {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-info h2 {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.description {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-actions {
  display: flex;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

.members-container {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
}

.pagination {
  padding: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

:deep(.el-descriptions) {
  padding: 16px;
  background: var(--el-fill-color-blank);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

:deep(.el-descriptions__label) {
  width: 120px;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
}
</style>