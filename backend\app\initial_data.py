from app.database import SessionLocal
from app.models.asset import Asset
from app.models.inventory import InventoryTask, InventoryRecord
from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role
from app.crud.asset import asset_crud
from app.crud.inventory import inventory_task_crud, inventory_record_crud
from app.crud.user import user_crud
from app.crud.permission import permission_crud
from app.crud.role import role_crud
from app.schemas.asset import AssetCreate
from app.schemas.inventory import InventoryTaskCreate, InventoryRecordCreate
from app.schemas.user import UserCreate
from app.schemas.permission import PermissionCreate
from app.schemas.role import RoleCreate
from datetime import datetime, timedelta
from app.core.security import get_password_hash

def init_db():
    db = SessionLocal()
    try:
        # 检查是否已经有数据
        if db.query(User).first():
            print("Database already initialized, skipping...")
            return

        # 创建管理员用户
        admin_user = UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            is_active=True,
            is_superuser=True
        )
        admin = user_crud.create(db, obj_in=admin_user)
        print("Created admin user: admin/admin123")
        
        # 创建完整的权限体系 - 基于前端菜单需求
        default_permissions = [
            # 1. 基础信息模块 (basic-info)
            PermissionCreate(code="basic-info:view", name="查看基础信息", module="basic-info", description="查看基础信息模块"),
            PermissionCreate(code="basic-info:personnel:view", name="查看人员信息", module="basic-info", description="查看人员信息页面"),
            PermissionCreate(code="basic-info:personnel:edit", name="编辑人员信息", module="basic-info", description="编辑人员信息"),
            
            # 2. AD管理模块 (ad)
            PermissionCreate(code="ad:view", name="查看AD管理", module="ad", description="查看AD用户和组"),
            PermissionCreate(code="ad:config", name="AD服务器配置", module="ad", description="配置AD服务器连接"),
            PermissionCreate(code="ad:sync", name="AD人员同步", module="ad", description="同步AD人员数据"),
            PermissionCreate(code="ad:user:manage", name="管理AD用户", module="ad", description="管理AD用户账户"),
            PermissionCreate(code="ad:group:manage", name="管理AD组", module="ad", description="管理AD用户组"),
            
            # 3. 邮箱管理模块 (email)
            PermissionCreate(code="email:view", name="查看邮箱管理", module="email", description="查看邮箱管理首页"),
            PermissionCreate(code="email:config:view", name="查看邮箱配置", module="email", description="查看邮箱配置信息"),
            PermissionCreate(code="email:config:edit", name="编辑邮箱配置", module="email", description="编辑邮箱配置设置"),
            PermissionCreate(code="email:department:view", name="查看邮箱部门", module="email", description="查看邮箱部门管理"),
            PermissionCreate(code="email:department:edit", name="编辑邮箱部门", module="email", description="编辑邮箱部门信息"),
            PermissionCreate(code="email:member:view", name="查看邮箱成员", module="email", description="查看邮箱成员管理"),
            PermissionCreate(code="email:member:edit", name="编辑邮箱成员", module="email", description="编辑邮箱成员信息"),
            PermissionCreate(code="email:group:view", name="查看邮箱群组", module="email", description="查看邮箱群组管理"),
            PermissionCreate(code="email:group:edit", name="编辑邮箱群组", module="email", description="编辑邮箱群组"),
            PermissionCreate(code="email:tag:view", name="查看邮箱标签", module="email", description="查看邮箱标签管理"),
            PermissionCreate(code="email:tag:edit", name="编辑邮箱标签", module="email", description="编辑邮箱标签"),
            PermissionCreate(code="email:sync:view", name="查看邮箱同步", module="email", description="查看邮箱同步管理"),
            PermissionCreate(code="email:sync:execute", name="执行邮箱同步", module="email", description="执行邮箱同步操作"),
            PermissionCreate(code="email:request:view", name="查看邮箱申请", module="email", description="查看邮箱申请管理"),
            PermissionCreate(code="email:request:handle", name="处理邮箱申请", module="email", description="处理邮箱申请"),
            
            # 4. 资产管理模块 (asset)
            PermissionCreate(code="asset:view", name="查看资产", module="asset", description="查看资产列表和详情"),
            PermissionCreate(code="asset:add", name="添加资产", module="asset", description="添加新资产"),
            PermissionCreate(code="asset:edit", name="编辑资产", module="asset", description="编辑现有资产"),
            PermissionCreate(code="asset:delete", name="删除资产", module="asset", description="删除资产"),
            PermissionCreate(code="asset:import", name="导入资产", module="asset", description="批量导入资产"),
            PermissionCreate(code="asset:export", name="导出资产", module="asset", description="导出资产数据"),
            PermissionCreate(code="asset:field:manage", name="资产字段管理", module="asset", description="管理资产字段和设置"),
            
            # 5. 盘点管理模块 (inventory)
            PermissionCreate(code="inventory:view", name="查看盘点", module="inventory", description="查看盘点任务和记录"),
            PermissionCreate(code="inventory:add", name="创建盘点", module="inventory", description="创建盘点任务"),
            PermissionCreate(code="inventory:edit", name="编辑盘点", module="inventory", description="编辑盘点任务和记录"),
            PermissionCreate(code="inventory:delete", name="删除盘点", module="inventory", description="删除盘点任务"),
            PermissionCreate(code="inventory:execute", name="执行盘点", module="inventory", description="执行盘点操作"),
            PermissionCreate(code="inventory:report", name="盘点报告", module="inventory", description="生成和查看盘点报告"),
            
            # 6. 系统设置模块 (system)
            PermissionCreate(code="system:view", name="查看系统设置", module="system", description="查看系统设置"),
            PermissionCreate(code="system:config", name="系统配置", module="system", description="配置系统参数"),
            PermissionCreate(code="system:user:view", name="查看用户管理", module="system", description="查看用户列表"),
            PermissionCreate(code="system:user:add", name="添加用户", module="system", description="添加新用户"),
            PermissionCreate(code="system:user:edit", name="编辑用户", module="system", description="编辑用户信息"),
            PermissionCreate(code="system:user:delete", name="删除用户", module="system", description="删除用户"),
            PermissionCreate(code="system:role:view", name="查看角色", module="system", description="查看角色列表和详情"),
            PermissionCreate(code="system:role:add", name="添加角色", module="system", description="添加新角色"),
            PermissionCreate(code="system:role:edit", name="编辑角色", module="system", description="编辑现有角色"),
            PermissionCreate(code="system:role:delete", name="删除角色", module="system", description="删除角色"),
            PermissionCreate(code="system:permission:view", name="查看权限", module="system", description="查看权限列表和详情"),
            PermissionCreate(code="system:permission:assign", name="分配权限", module="system", description="分配权限到角色"),
            
            # 7. 终端管理模块 (terminal)
            PermissionCreate(code="terminal:view", name="查看终端管理", module="terminal", description="查看终端概况和列表"),
            PermissionCreate(code="terminal:detail", name="查看终端详情", module="terminal", description="查看终端详细信息"),
            PermissionCreate(code="terminal:software:view", name="查看终端软件", module="terminal", description="查看终端软件管理"),
            PermissionCreate(code="terminal:software:manage", name="管理终端软件", module="terminal", description="管理终端软件"),
            PermissionCreate(code="terminal:agent:manage", name="管理Agent", module="terminal", description="管理终端Agent"),
            PermissionCreate(code="terminal:control", name="终端控制", module="terminal", description="远程控制终端"),
            
            # 8. 注册表管理模块 (registry)
            PermissionCreate(code="registry:read", name="读取注册表", module="registry", description="读取和浏览注册表键值"),
            PermissionCreate(code="registry:write", name="写入注册表", module="registry", description="创建和修改注册表键值"),
            PermissionCreate(code="registry:delete", name="删除注册表", module="registry", description="删除注册表键值"),
            PermissionCreate(code="registry:create", name="创建注册表键", module="registry", description="创建新的注册表键"),
            PermissionCreate(code="registry:backup", name="备份注册表", module="registry", description="创建注册表备份"),
            PermissionCreate(code="registry:restore", name="还原注册表", module="registry", description="从备份还原注册表"),
            PermissionCreate(code="registry:batch", name="批量操作注册表", module="registry", description="执行批量注册表操作"),
            PermissionCreate(code="registry:view_history", name="查看操作历史", module="registry", description="查看注册表操作历史记录"),
            PermissionCreate(code="registry:view_backups", name="查看备份记录", module="registry", description="查看注册表备份历史"),
            PermissionCreate(code="registry:view_stats", name="查看统计信息", module="registry", description="查看注册表操作统计"),
            PermissionCreate(code="registry:manage_backups", name="管理备份", module="registry", description="管理和删除注册表备份"),
            PermissionCreate(code="registry:verify_backups", name="验证备份", module="registry", description="验证注册表备份完整性"),
        ]
        
        # 添加权限
        created_permissions = []
        for permission_data in default_permissions:
            permission = permission_crud.create(db, obj_in=permission_data)
            created_permissions.append(permission)
        print(f"Created {len(created_permissions)} default permissions")
        
        # 按模块统计权限数量
        modules_count = {}
        for permission in created_permissions:
            module = permission.module
            modules_count[module] = modules_count.get(module, 0) + 1
        
        print("\n权限模块统计:")
        for module, count in modules_count.items():
            print(f"  {module}: {count}个权限")
        
        # 创建标准角色体系
        roles_config = [
            {
                "code": "super_admin",
                "name": "超级管理员",
                "description": "拥有所有权限的管理员角色",
                "permissions": [p.id for p in created_permissions]  # 所有权限
            },
            {
                "code": "asset_admin",
                "name": "资产管理员",
                "description": "管理资产和盘点的角色",
                "permissions": [
                    p.id for p in created_permissions 
                    if p.module in ["asset", "inventory", "basic-info"]
                ]
            },
            {
                "code": "normal_user",
                "name": "普通用户",
                "description": "普通用户角色",
                "permissions": [
                    p.id for p in created_permissions 
                    if p.code in ["asset:view", "basic-info:view"]
                ]
            }
        ]
        
        created_roles = []
        for role_config in roles_config:
            role_data = RoleCreate(
                code=role_config["code"],
                name=role_config["name"],
                description=role_config["description"],
                is_default=1
            )
            role = role_crud.create_with_permissions(
                db, 
                obj_in=role_data, 
                permission_ids=role_config["permissions"]
            )
            created_roles.append(role)
            print(f"Created {role.name} role with {len(role_config['permissions'])} permissions")
        
        # 分配super_admin角色给管理员用户
        super_admin_role = None
        for role in created_roles:
            if role.code == "super_admin":
                super_admin_role = role
                break
        
        if super_admin_role:
            role_crud.assign_roles_to_user(db, user_id=admin.id, role_ids=[super_admin_role.id])
            print(f"Assigned super_admin role to admin user")

        # 创建一些示例资产
        sample_assets = [
            AssetCreate(
                name="测试电脑1",
                asset_number="PC001",
                type="电脑",
                status="在用",
                purchase_date=datetime.now() - timedelta(days=365),
                user="张三",
                department="IT部门",
                location="一楼",
                custodian="李四",
                description="测试用电脑1",
                company="示例公司",
                specification="ThinkPad X1 Carbon",
                inspector="王五"
            ),
            AssetCreate(
                name="测试电脑2",
                asset_number="PC002",
                type="电脑",
                status="在用",
                purchase_date=datetime.now() - timedelta(days=180),
                user="王五",
                department="财务部",
                location="二楼",
                custodian="赵六",
                description="测试用电脑2",
                company="示例公司",
                specification="MacBook Pro",
                inspector="张三"
            ),
        ]

        # 添加资产
        for asset_data in sample_assets:
            asset_crud.create(db, obj_in=asset_data)

        # 创建一个示例盘点任务
        task_data = InventoryTaskCreate(
            name="示例盘点任务",
            description="这是一个示例盘点任务",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=7),
            status="进行中",
            created_by="admin"
        )
        task = inventory_task_crud.create(db, obj_in=task_data)

        # 为所有资产创建盘点记录
        assets = asset_crud.get_multi(db)
        for asset in assets:
            inventory_record_crud.create(
                db,
                obj_in=InventoryRecordCreate(
                    task_id=task.id,
                    asset_id=asset.id,
                    status="pending"
                )
            )

        db.commit()
        print("\n数据库初始化成功!")
        print("=" * 50)

    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_db() 