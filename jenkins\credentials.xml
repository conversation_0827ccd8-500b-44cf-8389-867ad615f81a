<?xml version='1.1' encoding='UTF-8'?>
<com.cloudbees.plugins.credentials.SystemCredentialsProvider plugin="credentials@1390.vd95b_2c2a_e48">
  <domainCredentialsMap class="java-hashtable">
    <entry>
      <com.cloudbees.plugins.credentials.domains.Domain>
        <specifications/>
      </com.cloudbees.plugins.credentials.domains.Domain>
      <java.util.ArrayList>
        <com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
          <scope>GLOBAL</scope>
          <id>gitea</id>
          <description>Gitea Git仓库访问凭据</description>
          <username>your-gitea-username</username>
          <password>your-gitea-password</password>
        </com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
        <com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
          <scope>GLOBAL</scope>
          <id>dev-harbor</id>
          <description>Harbor镜像仓库访问凭据</description>
          <username>your-harbor-username</username>
          <password>your-harbor-password</password>
        </com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
        <com.cloudbees.plugins.credentials.impl.StringCredentialsImpl>
          <scope>GLOBAL</scope>
          <id>kubeconfig</id>
          <description>Kubernetes集群配置文件</description>
          <secret>your-kubeconfig-base64-encoded</secret>
        </com.cloudbees.plugins.credentials.impl.StringCredentialsImpl>
        <com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
          <scope>GLOBAL</scope>
          <id>jenkins-email</id>
          <description>Jenkins邮件通知凭据</description>
          <username><EMAIL></username>
          <password>your-email-password</password>
        </com.cloudbees.plugins.credentials.impl.UsernamePasswordCredentialsImpl>
      </java.util.ArrayList>
    </entry>
  </domainCredentialsMap>
</com.cloudbees.plugins.credentials.SystemCredentialsProvider>
