<template>
  <div class="dynamic-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :inline="inline"
      :disabled="disabled"
      @submit.prevent
    >
      <template v-for="field in sortedFields" :key="field.id">
        <DynamicField
          :field="field"
          :value="formData[field.name]"
          :error="formErrors[field.name]"
          :disabled="disabled"
          :applies-to="appliesTo"
          :entity-id="entityId"
          :mode="mode"
          :task-id="taskId"
          :asset-id="assetId"
          @update:value="handleFieldChange(field.name, $event)"
          @blur="handleFieldBlur(field.name)"
        />
      </template>
      
      <!-- 插槽：额外表单项 -->
      <slot name="extra-fields" :form-data="formData" :form-ref="formRef"></slot>
      
      <!-- 插槽：表单操作按钮 -->
      <slot name="actions" :form-data="formData" :validate="validate" :reset="reset">
        <el-form-item v-if="showActions">
          <el-button type="primary" @click="validate">提交</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </slot>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DynamicField from './DynamicField.vue'
import { customFieldApi } from '@/api/custom_field'
import type { CustomField, AppliesTo } from '@/types/custom_field'
import { generateValidationRules } from '@/types/custom_field'

interface Props {
  // 适用范围：asset、inventory_record
  appliesTo: AppliesTo | 'asset' | 'inventory_record'
  // 初始数据
  initialData?: Record<string, any>
  // 实体ID（编辑模式下的资产ID或盘点记录ID）
  entityId?: number | null
  // 操作模式：create（创建）、edit（编辑）
  mode?: 'create' | 'edit'
  // 盘点记录相关的额外参数（仅用于虚拟盘点记录）
  taskId?: number
  assetId?: number
  // 表单配置
  labelWidth?: string
  inline?: boolean
  disabled?: boolean
  showActions?: boolean
  // 只显示指定字段
  includeFields?: string[]
  // 排除指定字段
  excludeFields?: string[]
}

interface Emits {
  (e: 'submit', data: Record<string, any>): void
  (e: 'change', field: string, value: any, allData: Record<string, any>): void
  (e: 'ready', fields: CustomField[]): void
}

const props = withDefaults(defineProps<Props>(), {
  labelWidth: '120px',
  inline: false,
  disabled: false,
  showActions: true,
  mode: 'create',
  initialData: () => ({}),
  includeFields: () => [],
  excludeFields: () => []
})

const emit = defineEmits<Emits>()

// 表单引用和数据
const formRef = ref<FormInstance>()
const formData = reactive<Record<string, any>>({})
const formErrors = reactive<Record<string, string>>({})
const formRules = ref<FormRules>({})

// 字段列表
const fields = ref<CustomField[]>([])
const loading = ref(false)

// 过滤和排序后的字段
const sortedFields = computed(() => {
  let filteredFields = fields.value

  // 应用包含过滤
  if (props.includeFields.length > 0) {
    filteredFields = filteredFields.filter(field => 
      props.includeFields.includes(field.name)
    )
  }

  // 应用排除过滤
  if (props.excludeFields.length > 0) {
    filteredFields = filteredFields.filter(field => 
      !props.excludeFields.includes(field.name)
    )
  }

  // 按排序顺序和ID排序
  return filteredFields.sort((a, b) => {
    if (a.sort_order !== b.sort_order) {
      return a.sort_order - b.sort_order
    }
    return a.id - b.id
  })
})

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  Object.assign(formData, newData)
}, { immediate: true, deep: true })

// 监听字段变化，更新表单规则
watch(sortedFields, (newFields) => {
  const rules: FormRules = {}
  
  newFields.forEach(field => {
    rules[field.name] = generateValidationRules(field)
  })
  
  formRules.value = rules
}, { immediate: true })

// 获取自定义字段
const fetchCustomFields = async () => {
  try {
    loading.value = true
    const response = await customFieldApi.getActiveCustomFields({
      applies_to: props.appliesTo
    })
    fields.value = response.data
    
    // 初始化表单数据
    fields.value.forEach(field => {
      if (!(field.name in formData)) {
        formData[field.name] = field.default_value || ''
      }
    })
    
    emit('ready', fields.value)
  } catch (error) {
    console.error('获取自定义字段失败:', error)
    ElMessage.error('获取自定义字段失败')
  } finally {
    loading.value = false
  }
}

// 处理字段值变化
const handleFieldChange = (fieldName: string, value: any) => {
  formData[fieldName] = value
  
  // 清除字段错误
  if (formErrors[fieldName]) {
    delete formErrors[fieldName]
  }
  
  emit('change', fieldName, value, { ...formData })
}

// 处理字段失焦
const handleFieldBlur = (fieldName: string) => {
  // 可以在这里添加字段级别的验证
  nextTick(() => {
    formRef.value?.validateField(fieldName, (isValid, message) => {
      if (!isValid && message) {
        formErrors[fieldName] = Array.isArray(message) ? message[0].message : message
      } else {
        delete formErrors[fieldName]
      }
    })
  })
}

// 表单验证
const validate = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    emit('submit', { ...formData })
    return true
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

// 重置表单
const reset = () => {
  formRef.value?.resetFields()
  Object.keys(formErrors).forEach(key => {
    delete formErrors[key]
  })
  
  // 重置为默认值
  fields.value.forEach(field => {
    formData[field.name] = field.default_value || ''
  })
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 设置表单数据
const setFormData = (data: Record<string, any>) => {
  Object.assign(formData, data)
}

// 获取字段值
const getFieldValue = (fieldName: string) => {
  return formData[fieldName]
}

// 设置字段值
const setFieldValue = (fieldName: string, value: any) => {
  handleFieldChange(fieldName, value)
}

// 获取指定字段配置
const getField = (fieldName: string) => {
  return fields.value.find(field => field.name === fieldName)
}

// 暴露给父组件的方法
defineExpose({
  validate,
  reset,
  getFormData,
  setFormData,
  getFieldValue,
  setFieldValue,
  getField,
  formRef
})

onMounted(() => {
  fetchCustomFields()
})
</script>

<style scoped>
.dynamic-form {
  width: 100%;
}

.dynamic-form :deep(.el-form-item) {
  margin-bottom: 18px;
}

.dynamic-form :deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

.dynamic-form :deep(.el-form-item__error) {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
</style> 