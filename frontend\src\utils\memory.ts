/**
 * 内存管理工具类
 * 提供内存监控、清理和优化功能
 */

interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

class MemoryManager {
  private timers: Set<NodeJS.Timeout> = new Set()
  private intervals: Set<NodeJS.Timeout> = new Set()
  private observers: Set<ResizeObserver | IntersectionObserver | MutationObserver> = new Set()
  private eventListeners: Set<{ element: EventTarget; type: string; listener: EventListener }> = new Set()
  
  /**
   * 获取当前内存使用情况
   */
  getMemoryInfo(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }

  /**
   * 检查内存使用率是否过高
   */
  isMemoryUsageHigh(): boolean {
    const memInfo = this.getMemoryInfo()
    if (!memInfo) return false
    
    const usage = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit
    return usage > 0.8 // 超过80%认为是高使用率
  }

  /**
   * 获取内存使用率百分比
   */
  getMemoryUsagePercent(): number {
    const memInfo = this.getMemoryInfo()
    if (!memInfo) return 0
    
    return Math.round((memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100)
  }

  /**
   * 注册定时器，便于统一清理
   */
  registerTimer(timer: NodeJS.Timeout): NodeJS.Timeout {
    this.timers.add(timer)
    return timer
  }

  /**
   * 注册间隔器，便于统一清理
   */
  registerInterval(interval: NodeJS.Timeout): NodeJS.Timeout {
    this.intervals.add(interval)
    return interval
  }

  /**
   * 注册观察器，便于统一清理
   */
  registerObserver(observer: ResizeObserver | IntersectionObserver | MutationObserver): void {
    this.observers.add(observer)
  }

  /**
   * 注册事件监听器，便于统一清理
   */
  registerEventListener(element: EventTarget, type: string, listener: EventListener): void {
    element.addEventListener(type, listener)
    this.eventListeners.add({ element, type, listener })
  }

  /**
   * 清理所有注册的资源
   */
  cleanup(): void {
    // 清理定时器
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()

    // 清理间隔器
    this.intervals.forEach(interval => clearInterval(interval))
    this.intervals.clear()

    // 清理观察器
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()

    // 清理事件监听器
    this.eventListeners.forEach(({ element, type, listener }) => {
      element.removeEventListener(type, listener)
    })
    this.eventListeners.clear()
  }

  /**
   * 触发垃圾回收（仅在支持的环境中）
   */
  forceGarbageCollection(): void {
    if ('gc' in window && typeof (window as any).gc === 'function') {
      try {
        (window as any).gc()
        console.log('[MemoryManager] 手动触发垃圾回收')
      } catch (error) {
        console.warn('[MemoryManager] 垃圾回收失败:', error)
      }
    }
  }

  /**
   * 监控内存使用情况
   */
  startMemoryMonitoring(callback?: (usage: number) => void): void {
    const monitor = () => {
      const usage = this.getMemoryUsagePercent()
      
      if (import.meta.env.DEV) {
        console.log(`[MemoryManager] 当前内存使用率: ${usage}%`)
      }
      
      if (callback) {
        callback(usage)
      }
      
      // 如果内存使用率过高，发出警告
      if (usage > 80) {
        console.warn(`[MemoryManager] 内存使用率过高: ${usage}%`)
        this.forceGarbageCollection()
      }
    }

    // 每30秒监控一次
    const interval = setInterval(monitor, 30000)
    this.registerInterval(interval)
    
    // 立即执行一次
    monitor()
  }

  /**
   * 清理DOM节点引用，防止内存泄漏
   */
  cleanupDOMReferences(element: Element): void {
    // 移除所有事件监听器
    const clone = element.cloneNode(true)
    element.parentNode?.replaceChild(clone, element)
    
    console.log('[MemoryManager] 清理DOM节点引用')
  }

  /**
   * 获取性能建议
   */
  getPerformanceAdvice(): string[] {
    const advice: string[] = []
    const usage = this.getMemoryUsagePercent()
    
    if (usage > 70) {
      advice.push('内存使用率较高，建议关闭不必要的页面或刷新浏览器')
    }
    
    if (this.timers.size > 20) {
      advice.push('检测到大量定时器，可能存在内存泄漏')
    }
    
    if (this.eventListeners.size > 100) {
      advice.push('检测到大量事件监听器，建议优化事件处理')
    }
    
    if (advice.length === 0) {
      advice.push('内存使用正常')
    }
    
    return advice
  }
}

// 创建全局实例
export const memoryManager = new MemoryManager()

// 在开发环境中启动内存监控
if (import.meta.env.DEV) {
  memoryManager.startMemoryMonitoring()
}

// 页面卸载时清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    memoryManager.cleanup()
  })
}

export default MemoryManager 