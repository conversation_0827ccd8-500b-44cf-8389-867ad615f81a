"""
锁清理服务

定期清理过期的同步锁，防止锁泄漏
"""
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.services import ad_sync_lock, email_sync_lock

logger = logging.getLogger(__name__)

class LockCleanupService:
    """锁清理服务"""
    
    def __init__(self, cleanup_interval: int = 600):  # 默认10分钟清理一次
        self.cleanup_interval = cleanup_interval
        self.running = False
        self.task = None
        
    async def start(self):
        """启动锁清理服务"""
        if self.running:
            logger.warning("锁清理服务已在运行")
            return
            
        self.running = True
        self.task = asyncio.create_task(self._cleanup_loop())
        logger.info(f"锁清理服务已启动，清理间隔：{self.cleanup_interval}秒")
        
    async def stop(self):
        """停止锁清理服务"""
        if not self.running:
            return
            
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        logger.info("锁清理服务已停止")
        
    async def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                await self.cleanup_expired_locks()
                await asyncio.sleep(self.cleanup_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"锁清理循环出错: {str(e)}")
                await asyncio.sleep(60)  # 出错后等待1分钟再重试
                
    async def cleanup_expired_locks(self) -> Dict[str, Any]:
        """
        清理所有过期的锁
        
        Returns:
            清理结果统计
        """
        start_time = datetime.now(timezone.utc)
        ad_cleaned = 0
        email_cleaned = 0
        
        db = SessionLocal()
        try:
            # 清理AD同步锁
            ad_cleaned = await ad_sync_lock.cleanup_expired_locks(db)
            
            # 清理邮箱同步锁
            email_cleaned = await email_sync_lock.cleanup_expired_locks(db)
            
            total_cleaned = ad_cleaned + email_cleaned
            
            if total_cleaned > 0:
                logger.info(f"锁清理完成：AD锁 {ad_cleaned} 个，邮箱锁 {email_cleaned} 个，总计 {total_cleaned} 个")
            else:
                logger.debug("锁清理完成：无过期锁需要清理")
                
            return {
                "cleanup_time": start_time.isoformat(),
                "ad_locks_cleaned": ad_cleaned,
                "email_locks_cleaned": email_cleaned,
                "total_cleaned": total_cleaned,
                "duration_ms": int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)
            }
            
        except Exception as e:
            logger.error(f"清理过期锁时出错: {str(e)}")
            return {
                "cleanup_time": start_time.isoformat(),
                "error": str(e),
                "ad_locks_cleaned": ad_cleaned,
                "email_locks_cleaned": email_cleaned,
                "total_cleaned": ad_cleaned + email_cleaned
            }
        finally:
            db.close()
            
    async def get_locks_status(self) -> Dict[str, Any]:
        """
        获取所有锁的状态信息
        
        Returns:
            锁状态统计
        """
        db = SessionLocal()
        try:
            # 获取AD同步锁状态
            ad_locks = await ad_sync_lock.get_all_locks_status(db)
            
            # 获取邮箱同步锁状态
            email_locks = await email_sync_lock.get_all_locks_status(db)
            
            # 统计信息
            ad_active = sum(1 for lock in ad_locks if lock["is_locked"] and not lock["is_expired"])
            ad_expired = sum(1 for lock in ad_locks if lock["is_locked"] and lock["is_expired"])
            
            email_active = sum(1 for lock in email_locks if lock["is_locked"] and not lock["is_expired"])
            email_expired = sum(1 for lock in email_locks if lock["is_locked"] and lock["is_expired"])
            
            return {
                "service_running": self.running,
                "cleanup_interval": self.cleanup_interval,
                "last_cleanup": datetime.now(timezone.utc).isoformat(),
                "ad_locks": {
                    "total": len(ad_locks),
                    "active": ad_active,
                    "expired": ad_expired,
                    "locks": ad_locks
                },
                "email_locks": {
                    "total": len(email_locks),
                    "active": email_active,
                    "expired": email_expired,
                    "locks": email_locks
                },
                "summary": {
                    "total_locks": len(ad_locks) + len(email_locks),
                    "total_active": ad_active + email_active,
                    "total_expired": ad_expired + email_expired
                }
            }
            
        except Exception as e:
            logger.error(f"获取锁状态时出错: {str(e)}")
            return {
                "service_running": self.running,
                "error": str(e)
            }
        finally:
            db.close()
            
    async def force_cleanup_all(self, operator: str = "system") -> Dict[str, Any]:
        """
        强制清理所有锁（紧急情况使用）
        
        Args:
            operator: 执行操作的用户
            
        Returns:
            清理结果
        """
        start_time = datetime.now(timezone.utc)
        ad_released = 0
        email_released = 0
        
        db = SessionLocal()
        try:
            # 强制释放AD同步锁
            ad_released = await ad_sync_lock.force_release_all_locks(db, operator)
            
            # 强制释放邮箱同步锁
            email_released = await email_sync_lock.force_release_all_locks(db, operator)
            
            total_released = ad_released + email_released
            
            logger.warning(f"强制清理所有锁完成：AD锁 {ad_released} 个，邮箱锁 {email_released} 个，总计 {total_released} 个，操作者：{operator}")
            
            return {
                "cleanup_time": start_time.isoformat(),
                "operator": operator,
                "ad_locks_released": ad_released,
                "email_locks_released": email_released,
                "total_released": total_released,
                "duration_ms": int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000)
            }
            
        except Exception as e:
            logger.error(f"强制清理所有锁时出错: {str(e)}")
            return {
                "cleanup_time": start_time.isoformat(),
                "operator": operator,
                "error": str(e),
                "ad_locks_released": ad_released,
                "email_locks_released": email_released,
                "total_released": ad_released + email_released
            }
        finally:
            db.close()

# 全局锁清理服务实例
lock_cleanup_service = LockCleanupService() 