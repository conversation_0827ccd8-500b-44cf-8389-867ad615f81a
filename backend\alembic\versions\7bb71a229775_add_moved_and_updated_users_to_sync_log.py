"""add_moved_and_updated_users_to_sync_log

Revision ID: 7bb71a229775
Revises: 45b2cdbb6492
Create Date: 2025-03-20 14:24:55.640301

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7bb71a229775'
down_revision: Union[str, None] = '45b2cdbb6492'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('ad_sync_logs', sa.Column('moved_users', sa.Integer(), nullable=True, server_default='0'))
    op.add_column('ad_sync_logs', sa.Column('updated_users', sa.Integer(), nullable=True, server_default='0'))


def downgrade() -> None:
    op.drop_column('ad_sync_logs', 'updated_users')
    op.drop_column('ad_sync_logs', 'moved_users')
