#!/usr/bin/env python3
from app.database import SessionLocal
from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role

def check_admin_permissions():
    db = SessionLocal()
    try:
        # 查找admin用户
        admin_user = db.query(User).filter(User.email == '<EMAIL>').first()
        if not admin_user:
            print('❌ admin用户不存在')
            return
        
        print(f'✅ 找到admin用户: {admin_user.email} (ID: {admin_user.id})')
        print(f'   超级用户: {admin_user.is_superuser}')
        print(f'   激活状态: {admin_user.is_active}')
        
        # 查询用户角色
        print(f'\n用户角色:')
        for role in admin_user.roles:
            print(f'  - {role.name} ({role.code})')
        
        # 查询用户权限
        print(f'\n用户权限:')
        all_permissions = set()
        
        # 从角色获取权限
        for role in admin_user.roles:
            for perm in role.permissions:
                all_permissions.add(perm.code)
        
        # 注意：用户没有直接分配权限的关系，所有权限都通过角色获取
        
        # 检查是否有asset:field:manage权限
        has_field_manage = 'asset:field:manage' in all_permissions
        print(f'\n🔍 asset:field:manage权限: {"✅ 有" if has_field_manage else "❌ 没有"}')
        
        # 显示资产相关权限
        asset_permissions = [perm for perm in all_permissions if 'asset' in perm]
        print(f'\n资产相关权限({len(asset_permissions)}个):')
        for perm in sorted(asset_permissions):
            print(f'  ✓ {perm}')
            
        # 如果没有asset:field:manage权限，检查如何添加
        if not has_field_manage:
            print('\n❗ 需要添加 asset:field:manage 权限')
            # 检查权限是否存在
            field_manage_perm = db.query(Permission).filter(Permission.code == 'asset:field:manage').first()
            if field_manage_perm:
                print(f'   权限定义存在: {field_manage_perm.name}')
            else:
                print('   ❌ 权限定义不存在，需要运行初始化脚本')
            
    finally:
        db.close()

if __name__ == '__main__':
    check_admin_permissions() 