import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'
import { API_BASE_URL } from '../config/api'

interface ApiResponse<T = any> {
  data: T
  detail?: string
  message?: string
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  withCredentials: false, // 修改为false，避免CORS问题
  headers: {
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    const token = localStorage.getItem('token')

    console.log(`[Request] ${config.method?.toUpperCase()} ${config.url}`)

    if (token && config.headers) {
      // 确保token格式正确
      const cleanToken = token.trim()
      console.log(`[Auth] Token前15位: ${cleanToken.substring(0, 15)}...`)

      // 明确设置请求头
      config.headers['Authorization'] = `Bearer ${cleanToken}`

      // 打印完整的请求头，调试用
      console.log(`[Headers] ${JSON.stringify(config.headers)}`)
    } else {
      console.warn(`[Auth] 无Token或Headers`)
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error: AxiosError): Promise<AxiosError> => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    console.log(`[Response] ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`)
    console.log(`[Response Data]`, response.data)
    return response
  },
  async (error: AxiosError<ApiResponse>): Promise<AxiosError> => {
    console.error(`[ResponseError] ${error.response?.status} ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response?.data)

    // 网络错误处理
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      ElMessage.error('请求超时，请稍后重试')
      return Promise.reject(error)
    }

    if (!error.response) {
      ElMessage.error('网络连接异常，请检查网络状态')
      return Promise.reject(error)
    }

    // HTTP状态码错误处理
    if (error.response) {
      const status = error.response.status
      const errorData = error.response.data
      const errorDetail = errorData?.detail || errorData?.message || ''

      switch (status) {
        case 401:
          // 认证失败处理
          console.warn('[Auth] 认证失败，状态码401')
          localStorage.removeItem('token')
          
          // 避免在登录页重复跳转
          const currentPath = window.location.pathname
          if (!currentPath.includes('/login')) {
            if (errorDetail.includes('expired') || errorDetail.includes('过期')) {
              ElMessage.warning('登录已过期，请重新登录')
            } else {
              ElMessage.error('认证失败，请重新登录')
            }
            
            // 判断设备类型进行跳转
            const { usePlatform } = await import('@/composables/usePlatform')
            const { shouldUseMobile } = usePlatform()
            const loginPath = shouldUseMobile.value ? '/m/login' : '/login'
            router.push(loginPath)
          }
          break

        case 403:
          ElMessage.error('权限不足，无法执行此操作')
          break

        case 404:
          // 404错误通常不需要全局提示，由组件自行处理
          console.warn('[Request] 资源不存在:', error.config?.url)
          break

        case 422:
          // 数据验证错误，显示详细错误信息
          if (errorDetail) {
            ElMessage.error(`数据验证失败: ${errorDetail}`)
          } else {
            ElMessage.error('提交的数据格式有误')
          }
          break

        case 500:
          ElMessage.error('服务器内部错误，请稍后重试')
          break

        case 502:
        case 503:
        case 504:
          ElMessage.error('服务暂时不可用，请稍后重试')
          break

        default:
          // 其他错误，尝试显示服务器返回的错误信息
          const message = errorDetail || `请求失败 (状态码: ${status})`
          ElMessage.error(message)
      }
    }
    return Promise.reject(error)
  }
)

export default service