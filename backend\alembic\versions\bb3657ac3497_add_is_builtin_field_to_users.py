"""add_is_builtin_field_to_users

Revision ID: bb3657ac3497
Revises: 4c53a5efd16a
Create Date: 2025-06-23 23:13:21.919930

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bb3657ac3497'
down_revision: Union[str, None] = '4c53a5efd16a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 注意：保留asset_settings表，这是重要的业务表，不能删除
    op.drop_index(op.f('ix_ad_sync_locks_lock_name'), table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    # REMOVED: 删除asset_settings表的操作 - 这个表是必需的业务表
    # op.drop_index(op.f('ix_asset_settings_id'), table_name='asset_settings')
    # op.drop_table('asset_settings')
    op.add_column('users', sa.Column('is_builtin', sa.Boolean(), nullable=True, comment='是否为内置账号'))
    
    # 设置admin账号为内置账号
    op.execute("UPDATE users SET is_builtin = true WHERE username = 'admin'")
    
    # 设置默认值为false，然后修改字段为NOT NULL
    op.execute("UPDATE users SET is_builtin = false WHERE is_builtin IS NULL")
    op.alter_column('users', 'is_builtin', nullable=False, server_default=sa.text('false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'is_builtin')
    # REMOVED: asset_settings表的创建 - 因为upgrade中没有删除它，所以这里不需要重新创建
    # op.create_table('asset_settings',
    # sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False, comment='ID'),
    # sa.Column('company', sa.VARCHAR(length=100), autoincrement=False, nullable=False, comment='公司'),
    # sa.Column('asset_number_rule', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False, comment='资产编号规则'),
    # sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    # sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    # sa.PrimaryKeyConstraint('id', name=op.f('asset_settings_pkey')),
    # sa.UniqueConstraint('company', name=op.f('asset_settings_company_key'))
    # )
    # op.create_index(op.f('ix_asset_settings_id'), 'asset_settings', ['id'], unique=False)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('locked_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('lock_name', name=op.f('ad_sync_locks_pkey'))
    )
    op.create_index(op.f('ix_ad_sync_locks_lock_name'), 'ad_sync_locks', ['lock_name'], unique=False)
    # ### end Alembic commands ###
