"""
性能和并发测试
测试系统在大数据量和高并发情况下的性能表现
"""

import pytest
import asyncio
import time
import threading
import logging
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.database import get_db, engine
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember
from app.services.email_extid_completion import EmailExtidCompletionService
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.crud.email import email_member

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

class TestPerformance:
    """性能和并发测试类"""
    
    @pytest.fixture(scope="class")
    def db_session(self):
        """数据库会话"""
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture(scope="class")
    def large_test_data(self, db_session: Session):
        """准备大量测试数据"""
        logger.info("准备大量测试数据...")
        
        # 清理现有测试数据
        db_session.query(EcologyUser).filter(
            EcologyUser.job_number.like("PERF_%")
        ).delete(synchronize_session=False)
        
        db_session.query(EmailMember).filter(
            EmailMember.extid.like("PERF_%")
        ).delete(synchronize_session=False)
        
        # 创建大量测试用户（1000个）
        test_users = []
        test_email_members = []
        
        for i in range(1000):
            job_number = f"PERF_{i:04d}"
            
            # 创建人员信息
            user = EcologyUser(
                user_id=10000 + i,
                user_name=f"性能测试用户{i}",
                job_number=job_number,
                dept_name=f"测试部门{i % 10}",
                job_title_name=f"测试职位{i % 5}",
                mobile=f"139{i:08d}",
                status="active" if i % 10 != 9 else "inactive"  # 10%离职率
            )
            test_users.append(user)
            
            # 为80%的用户创建邮箱（模拟部分用户没有邮箱的情况）
            if i % 5 != 4:
                email_member = EmailMember(
                    extid=job_number,
                    email=f"perf{i:04d}@company.com",
                    name=f"性能测试用户{i}",
                    department_id=f"dept_{i % 10}",
                    position=f"测试职位{i % 5}",
                    mobile=f"139{i:08d}",
                    is_active=True
                )
                test_email_members.append(email_member)
        
        # 批量插入数据
        batch_size = 100
        for i in range(0, len(test_users), batch_size):
            batch = test_users[i:i + batch_size]
            for user in batch:
                db_session.merge(user)
            db_session.commit()
        
        for i in range(0, len(test_email_members), batch_size):
            batch = test_email_members[i:i + batch_size]
            for member in batch:
                db_session.merge(member)
            db_session.commit()
        
        logger.info(f"创建了 {len(test_users)} 个用户和 {len(test_email_members)} 个邮箱成员")
        
        return {
            "users": test_users,
            "email_members": test_email_members
        }
    
    def test_large_data_query_performance(self, db_session: Session, large_test_data: Dict):
        """测试大数据量查询性能"""
        logger.info("=== 测试大数据量查询性能 ===")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 测试统计信息查询性能
        start_time = time.time()
        stats = completion_service.get_completion_stats()
        stats_time = time.time() - start_time
        
        logger.info(f"统计信息查询耗时: {stats_time:.3f}秒")
        assert stats_time < 5.0, f"统计信息查询耗时过长: {stats_time:.3f}秒"
        
        # 测试分页查询性能
        start_time = time.time()
        matches = completion_service.find_name_matches(page=1, page_size=50)
        query_time = time.time() - start_time
        
        logger.info(f"分页查询耗时: {query_time:.3f}秒")
        assert query_time < 3.0, f"分页查询耗时过长: {query_time:.3f}秒"
        
        # 测试大页码查询性能
        if matches.total > 500:
            start_time = time.time()
            large_page_matches = completion_service.find_name_matches(page=10, page_size=50)
            large_page_time = time.time() - start_time
            
            logger.info(f"大页码查询耗时: {large_page_time:.3f}秒")
            assert large_page_time < 5.0, f"大页码查询耗时过长: {large_page_time:.3f}秒"
        
        logger.info("✅ 大数据量查询性能测试通过")
    
    def test_sync_performance(self, db_session: Session, large_test_data: Dict):
        """测试同步操作性能"""
        logger.info("=== 测试同步操作性能 ===")
        
        sync_service = PersonnelEmailSyncService(db_session)
        
        # 测试数据一致性检查性能
        start_time = time.time()
        consistency_result = asyncio.run(sync_service.check_data_consistency())
        consistency_time = time.time() - start_time
        
        logger.info(f"数据一致性检查耗时: {consistency_time:.3f}秒")
        logger.info(f"检查结果: 人员={consistency_result.total_personnel}, "
                   f"邮箱={consistency_result.total_email_members}, "
                   f"匹配={consistency_result.matched_count}")
        
        assert consistency_time < 10.0, f"数据一致性检查耗时过长: {consistency_time:.3f}秒"
        
        # 测试试运行同步性能
        start_time = time.time()
        dry_run_result = asyncio.run(sync_service.sync_personnel_to_email(dry_run=True))
        sync_time = time.time() - start_time
        
        logger.info(f"试运行同步耗时: {sync_time:.3f}秒")
        logger.info(f"同步结果: 处理={dry_run_result.processed_count}, "
                   f"创建申请={dry_run_result.created_requests}, "
                   f"更新={dry_run_result.updated_count}, "
                   f"禁用={dry_run_result.disabled_count}")
        
        assert sync_time < 30.0, f"试运行同步耗时过长: {sync_time:.3f}秒"
        
        logger.info("✅ 同步操作性能测试通过")
    
    def test_concurrent_read_performance(self, db_session: Session, large_test_data: Dict):
        """测试并发读取性能"""
        logger.info("=== 测试并发读取性能 ===")
        
        def query_stats():
            """查询统计信息的任务"""
            db = next(get_db())
            try:
                completion_service = EmailExtidCompletionService(db)
                start_time = time.time()
                stats = completion_service.get_completion_stats()
                end_time = time.time()
                return end_time - start_time, stats
            finally:
                db.close()
        
        def query_matches():
            """查询匹配结果的任务"""
            db = next(get_db())
            try:
                completion_service = EmailExtidCompletionService(db)
                start_time = time.time()
                matches = completion_service.find_name_matches(page=1, page_size=20)
                end_time = time.time()
                return end_time - start_time, matches
            finally:
                db.close()
        
        # 并发执行查询
        concurrent_tasks = 10
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_tasks) as executor:
            # 提交任务
            stats_futures = [executor.submit(query_stats) for _ in range(5)]
            matches_futures = [executor.submit(query_matches) for _ in range(5)]
            
            # 收集结果
            stats_times = []
            matches_times = []
            
            for future in as_completed(stats_futures):
                query_time, result = future.result()
                stats_times.append(query_time)
            
            for future in as_completed(matches_futures):
                query_time, result = future.result()
                matches_times.append(query_time)
        
        total_time = time.time() - start_time
        
        avg_stats_time = sum(stats_times) / len(stats_times)
        avg_matches_time = sum(matches_times) / len(matches_times)
        
        logger.info(f"并发查询总耗时: {total_time:.3f}秒")
        logger.info(f"统计查询平均耗时: {avg_stats_time:.3f}秒")
        logger.info(f"匹配查询平均耗时: {avg_matches_time:.3f}秒")
        
        # 验证并发性能
        assert total_time < 15.0, f"并发查询总耗时过长: {total_time:.3f}秒"
        assert avg_stats_time < 3.0, f"统计查询平均耗时过长: {avg_stats_time:.3f}秒"
        assert avg_matches_time < 3.0, f"匹配查询平均耗时过长: {avg_matches_time:.3f}秒"
        
        logger.info("✅ 并发读取性能测试通过")
    
    def test_memory_usage_performance(self, db_session: Session, large_test_data: Dict):
        """测试内存使用性能"""
        logger.info("=== 测试内存使用性能 ===")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        logger.info(f"初始内存使用: {initial_memory:.2f} MB")
        
        completion_service = EmailExtidCompletionService(db_session)
        
        # 执行多次查询操作
        for i in range(10):
            stats = completion_service.get_completion_stats()
            matches = completion_service.find_name_matches(page=i+1, page_size=100)
        
        # 记录操作后内存使用
        after_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = after_memory - initial_memory
        
        logger.info(f"操作后内存使用: {after_memory:.2f} MB")
        logger.info(f"内存增长: {memory_increase:.2f} MB")
        
        # 验证内存使用合理
        assert memory_increase < 100, f"内存增长过多: {memory_increase:.2f} MB"
        
        logger.info("✅ 内存使用性能测试通过")
    
    def test_database_connection_performance(self, large_test_data: Dict):
        """测试数据库连接性能"""
        logger.info("=== 测试数据库连接性能 ===")
        
        def test_connection():
            """测试单个数据库连接"""
            start_time = time.time()
            db = next(get_db())
            try:
                # 执行简单查询
                result = db.execute(text("SELECT COUNT(*) FROM ecology_users WHERE job_number LIKE 'PERF_%'"))
                count = result.scalar()
                return time.time() - start_time, count
            finally:
                db.close()
        
        # 测试连续连接性能
        connection_times = []
        for i in range(20):
            conn_time, count = test_connection()
            connection_times.append(conn_time)
        
        avg_connection_time = sum(connection_times) / len(connection_times)
        max_connection_time = max(connection_times)
        
        logger.info(f"平均连接时间: {avg_connection_time:.3f}秒")
        logger.info(f"最大连接时间: {max_connection_time:.3f}秒")
        
        # 验证连接性能
        assert avg_connection_time < 0.1, f"平均连接时间过长: {avg_connection_time:.3f}秒"
        assert max_connection_time < 0.5, f"最大连接时间过长: {max_connection_time:.3f}秒"
        
        logger.info("✅ 数据库连接性能测试通过")
    
    def teardown_method(self, method):
        """清理测试数据"""
        db = next(get_db())
        try:
            # 清理大量测试数据
            logger.info("清理性能测试数据...")
            
            db.query(EcologyUser).filter(
                EcologyUser.job_number.like("PERF_%")
            ).delete(synchronize_session=False)
            
            db.query(EmailMember).filter(
                EmailMember.extid.like("PERF_%")
            ).delete(synchronize_session=False)
            
            db.commit()
            logger.info("性能测试数据清理完成")
        except Exception as e:
            logger.error(f"清理测试数据失败: {e}")
            db.rollback()
        finally:
            db.close()

if __name__ == "__main__":
    # 直接运行测试
    pytest.main([__file__, "-v", "-s"])
