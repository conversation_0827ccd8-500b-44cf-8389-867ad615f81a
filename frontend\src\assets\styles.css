/* 同步结果对话框样式 */
.sync-result-dialog .el-message-box__content {
  max-height: 500px;
  overflow-y: auto;
}

.sync-result-dialog {
  width: 650px !important;
  max-width: 80vw;
}

/* ======= 分页器修复样式 (这些样式用于修复分页器显示问题) ======= */

/* 分页容器样式 */
.pagination-container, 
.pagination-wrapper, 
.pagination {
  min-width: 650px !important;
  display: flex !important;
  justify-content: flex-end !important;
  overflow: visible !important;
  padding: 10px 0 !important;
}

/* 分页器核心组件的修复 */
.el-pagination {
  white-space: normal !important;
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 5px !important;
  overflow: visible !important;
}

/* 分页器总条数修复 */
.el-pagination__total {
  flex-shrink: 0 !important;
  display: inline-block !important;
  margin-right: 10px !important;
}

/* 每页条数选择器容器修复 */
.el-pagination__sizes {
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-right: 10px !important;
}

/* 下拉选择框修复 */
.el-pagination__sizes .el-select {
  width: 110px !important;
  margin: 0 !important;
}

/* 下拉选择框输入框修复 */
.el-pagination__sizes .el-select .el-input {
  width: 110px !important;
  margin: 0 !important;
}

/* 下拉选择框输入框内部元素修复 */
.el-pagination__sizes .el-select .el-input__wrapper {
  display: inline-flex !important;
  width: 100% !important;
}

/* 下拉箭头修复 */
.el-pagination__sizes .el-select .el-input__suffix {
  display: inline-flex !important;
  align-items: center !important;
  pointer-events: auto !important;
}

/* 确保页码组件可见 */
.el-pagination__jump {
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-left: 10px !important;
}

/* 使页码输入框可见 */
.el-pagination__editor.el-input {
  width: 50px !important;
  margin: 0 5px !important;
}

/* 全局样式 */

/* 分页器容器样式 - 强制显示所有元素 */
.pagination-container, .pagination-wrapper, .pagination {
  min-width: 600px !important;
  display: flex !important;
  justify-content: flex-end !important;
  overflow: visible !important;
}

/* 强制确保分页器组件中的各元素能够正常显示 */
.el-pagination {
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  white-space: normal !important;
  overflow: visible !important;
}

.el-pagination__sizes {
  margin-right: 16px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.el-pagination__total {
  margin-right: 16px !important;
}

/* 强制显示每页条数选择器 */
.el-select.el-pagination__sizes .el-input {
  width: 110px !important;
  display: inline-flex !important;
  margin: 0 !important;
  overflow: visible !important;
}

/* 确保下拉箭头可见 */
.el-select.el-pagination__sizes .el-input__suffix {
  display: inline-flex !important;
  align-items: center !important;
}

/* 为分页器组件设置正确的宽度 */
.el-pagination > span,
.el-pagination > button,
.el-pagination > div {
  display: inline-flex !important;
  vertical-align: middle !important;
  margin: 0 2px !important;
  padding: 0 4px !important;
} 