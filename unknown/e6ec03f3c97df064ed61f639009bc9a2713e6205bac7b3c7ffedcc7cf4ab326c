# Git相关
.git
.gitignore
.gitattributes

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# 构建输出
dist/
build/
.next/
.nuxt/
.output/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage
*.lcov

# nyc测试覆盖率
.nyc_output

# 依赖目录
jspm_packages/

# TypeScript缓存
*.tsbuildinfo

# 可选npm缓存目录
.npm

# 可选eslint缓存
.eslintcache

# 可选stylelint缓存
.stylelintcache

# 微前端
.vercel

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 测试相关
coverage/
test/
tests/
*.test.js
*.spec.js

# 文档
docs/
*.md
README*

# 其他
*.bak
*.tmp
*.temp
