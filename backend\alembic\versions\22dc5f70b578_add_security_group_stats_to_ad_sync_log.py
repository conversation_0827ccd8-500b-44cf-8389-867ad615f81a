"""add_security_group_stats_to_ad_sync_log

Revision ID: 22dc5f70b578
Revises: 8f7d0e608be1
Create Date: 2025-03-21 10:19:42.253626

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '22dc5f70b578'
down_revision: Union[str, None] = '8f7d0e608be1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.add_column('ad_sync_logs', sa.Column('added_to_groups', sa.Integer(), nullable=True, comment='添加到安全组的用户数'))
    op.add_column('ad_sync_logs', sa.Column('removed_from_groups', sa.Integer(), nullable=True, comment='从安全组移除的用户数'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ad_sync_logs', 'removed_from_groups')
    op.drop_column('ad_sync_logs', 'added_to_groups')
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    # ### end Alembic commands ###
