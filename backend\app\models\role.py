from sqlalchemy import Column, Integer, String, Text, Table, ForeignKey
from sqlalchemy.orm import relationship
from app.database import Base

# 角色-权限多对多关系表
role_permission = Table(
    "role_permissions",
    Base.metadata,
    Column("role_id", Integer, Foreign<PERSON>ey("roles.id"), primary_key=True),
    Column("permission_id", Integer, ForeignKey("permissions.id"), primary_key=True)
)

# 用户-角色多对多关系表
user_role = Table(
    "user_roles",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("users.id"), primary_key=True),
    Column("role_id", Integer, ForeignKey("roles.id"), primary_key=True)
)

class Role(Base):
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(50), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_default = Column(Integer, default=0, comment="是否为默认角色")
    
    # 关联关系
    permissions = relationship("Permission", secondary=role_permission, backref="roles")
    users = relationship("User", secondary=user_role, backref="roles") 