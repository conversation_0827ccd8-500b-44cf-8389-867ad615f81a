from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.crud.base import CRUDBase
from app.models.field_value import FieldValue
from app.schemas.field_value import FieldValueCreate, FieldValueUpdate

class CRUDFieldValue(CRUDBase[FieldValue, FieldValueCreate, FieldValueUpdate]):
    def get_by_field_name(
        self, db: Session, *, field_name: str
    ) -> List[FieldValue]:
        """根据字段名称获取字段值列表"""
        return db.query(self.model).filter(
            self.model.field_name == field_name
        ).all()

    def search(
        self,
        db: Session,
        *,
        field_name: Optional[str] = None,
        keyword: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FieldValue]:
        """搜索字段值"""
        query = db.query(self.model)
        
        if field_name:
            query = query.filter(self.model.field_name == field_name)
        
        if keyword:
            query = query.filter(
                or_(
                    self.model.field_value.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )
        
        return query.offset(skip).limit(limit).all()

    def get_total(
        self,
        db: Session,
        *,
        field_name: Optional[str] = None,
        keyword: Optional[str] = None
    ) -> int:
        """获取总数"""
        query = db.query(self.model)
        
        if field_name:
            query = query.filter(self.model.field_name == field_name)
        
        if keyword:
            query = query.filter(
                or_(
                    self.model.field_value.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )
        
        return query.count()

field_value_crud = CRUDFieldValue(FieldValue) 