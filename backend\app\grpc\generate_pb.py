import os
import sys
import subprocess
import re
from pathlib import Path

def generate_proto_code():
    """
    编译终端管理的 proto 文件生成 Python 代码
    """
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent.absolute()
    proto_dir = current_dir / "terminal_proto"
    output_dir = current_dir / "terminal_pb"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建 __init__.py 文件
    init_file = output_dir / "__init__.py"
    if not init_file.exists():
        with open(init_file, "w", encoding='utf-8') as f:
            f.write("# Generated terminal gRPC modules\n")
    
    # 查找所有 proto 文件
    proto_files = list(proto_dir.glob("*.proto"))
    
    if not proto_files:
        print("No proto files found in", proto_dir)
        return
    
    # 编译每个 proto 文件
    for proto_file in proto_files:
        print(f"Compiling {proto_file.name}...")
        
        # 构建命令
        cmd = [
            sys.executable, "-m", "grpc_tools.protoc",
            f"--proto_path={proto_dir}",
            f"--python_out={output_dir}",
            f"--grpc_python_out={output_dir}",
            str(proto_file)
        ]
        
        # 执行命令
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Successfully compiled {proto_file.name}")
            
            # 修复生成的 *_grpc.py 文件中的导入问题
            proto_name = proto_file.stem
            grpc_file = output_dir / f"{proto_name}_pb2_grpc.py"
            
            if grpc_file.exists():
                with open(grpc_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换导入语句
                fixed_content = re.sub(
                    r'import\s+([a-zA-Z0-9_]+)_pb2\s+as\s+([a-zA-Z0-9_]+)',
                    r'from . import \1_pb2 as \2',
                    content
                )
                
                with open(grpc_file, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print(f"Fixed imports in {grpc_file.name}")
        else:
            print(f"Failed to compile {proto_file.name}")
            print("Error:", result.stderr)

if __name__ == "__main__":
    generate_proto_code() 