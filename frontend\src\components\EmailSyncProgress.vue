<template>
  <div class="email-sync-progress">
    <!-- 进度弹窗 -->
    <el-dialog
      v-model="visibleModel"
      title="邮箱成员同步进度"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="progress-container">
        <!-- 总体进度条 -->
        <div class="progress-section">
          <div class="progress-header">
            <h4>{{ currentStage.stage_name || '准备中...' }}</h4>
            <span class="progress-percentage">{{ progress.progress_percentage || 0 }}%</span>
          </div>
          <el-progress
            :percentage="progress.progress_percentage || 0"
            :status="progressStatus"
            :stroke-width="20"
            striped
            striped-flow
          />
          <p class="progress-message">{{ progress.message || '正在初始化...' }}</p>
        </div>

        <!-- 详细信息 -->
        <div class="details-section" v-if="progress.total_members">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-statistic title="总成员数" :value="progress.total_members || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="已处理" :value="progress.processed_count || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="成功数量" :value="progress.success_count || 0" />
            </el-col>
          </el-row>
          
          <el-row :gutter="16" class="mt-4" v-if="progress.total_batches">
            <el-col :span="8">
              <el-statistic title="总批次" :value="progress.total_batches || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="当前批次" :value="progress.current_batch || 0" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="重试次数" :value="retryCount" v-if="retryCount > 0" />
            </el-col>
          </el-row>
          
          <!-- 超时警告 -->
          <el-alert
            v-if="retryCount > 0"
            title="连接不稳定"
            :description="`同步过程中检测到网络超时，已自动重试 ${retryCount} 次`"
            type="warning"
            show-icon
            :closable="false"
            class="mt-4"
          />
          
          <el-row :gutter="16" class="mt-4" v-if="false">
            <el-col :span="8">
              <el-statistic title="更新数量" :value="progress.updated_count || 0" />
            </el-col>
          </el-row>
        </div>

        <!-- 阶段步骤指示器 -->
        <div class="stages-section">
          <el-steps :active="activeStepIndex" finish-status="success" process-status="process">
            <el-step
              v-for="(stage, index) in stageSteps"
              :key="index"
              :title="stage.title"
              :description="stage.description"
              :status="getStepStatus(stage.key)"
            />
          </el-steps>
        </div>

        <!-- 错误信息 -->
        <div class="error-section" v-if="progress.status === 'failed'">
          <el-alert
            title="同步失败"
            type="error"
            :description="progress.error || progress.message"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 实时日志 -->
        <div class="logs-section" v-if="logs.length > 0">
          <h5>同步日志</h5>
          <div class="log-container">
            <div
              v-for="(log, index) in logs"
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="progress.status === 'success' || progress.status === 'failed'"
            type="primary"
            @click="handleClose"
          >
            关闭
          </el-button>
          <el-button
            v-else
            type="danger"
            @click="handleCancel"
            :disabled="!canCancel"
          >
            取消同步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

interface SyncProgress {
  stage?: string
  stage_name?: string
  current_batch?: number
  total_batches?: number
  processed_count?: number
  success_count?: number
  updated_count?: number
  failed_count?: number
  progress_percentage?: number
  status?: 'running' | 'success' | 'failed'
  message?: string
  error?: string
  total_members?: number
  elapsed_time?: number
  permission_success_count?: number
  permission_failed_count?: number
}

interface LogItem {
  timestamp: number
  message: string
  type: 'info' | 'success' | 'error' | 'warning'
}

interface StageStep {
  key: string
  title: string
  description: string
}

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'completed': [result: any]
  'cancelled': []
}>()

// 响应式数据
const visible = ref(false)
const progress = ref<SyncProgress>({})
const logs = ref<LogItem[]>([])
const sseConnection = ref<EventSource | null>(null)
const currentSyncId = ref<string>('')
const canCancel = ref(true)
const userStore = useUserStore()

// 超时检测相关
const lastProgressTime = ref<number>(0)
const timeoutCheckInterval = ref<NodeJS.Timeout | null>(null)
const maxIdleTime = 180000 // 3分钟无响应视为超时
const retryCount = ref(0)
const maxRetries = 3

// 阶段步骤定义
const stageSteps: StageStep[] = [
  { key: 'initializing', title: '初始化', description: '准备同步环境' },
  { key: 'fetching_data', title: '获取数据', description: '从腾讯API获取成员数据' },
  { key: 'processing_members', title: '处理成员', description: '分批处理成员信息' },
  { key: 'completed', title: '完成', description: '同步完成' }
]

// 计算属性
const visibleModel = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const progressStatus = computed(() => {
  if (progress.value.status === 'failed') return 'exception'
  if (progress.value.status === 'success') return 'success'
  return undefined
})

const currentStage = computed(() => {
  return progress.value || {}
})

const activeStepIndex = computed(() => {
  const stage = progress.value.stage
  if (!stage) return 0
  
  const index = stageSteps.findIndex(step => step.key === stage)
  return index >= 0 ? index : 0
})

// 方法
const getStepStatus = (stageKey: string) => {
  const currentStageIndex = activeStepIndex.value
  const stepIndex = stageSteps.findIndex(step => step.key === stageKey)
  
  if (progress.value.status === 'failed' && stepIndex === currentStageIndex) {
    return 'error'
  }
  if (stepIndex < currentStageIndex) {
    return 'finish'
  }
  if (stepIndex === currentStageIndex) {
    return 'process'
  }
  return 'wait'
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleTimeString()
}

const addLog = (message: string, type: 'info' | 'success' | 'error' | 'warning' = 'info') => {
  logs.value.push({
    timestamp: Date.now() / 1000,
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value.splice(0, 10)
  }
}

const connectSSE = () => {
  if (!userStore.token) {
    ElMessage.error('用户未登录')
    return
  }

  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
  const sseUrl = `${baseUrl}/api/v1/sse/email-sync-stream?token=${userStore.token}`
  
  try {
    sseConnection.value = new EventSource(sseUrl)
    
    sseConnection.value.onopen = () => {
      console.log('邮箱同步SSE连接已建立')
      addLog('SSE连接已建立', 'success')
      startTimeoutCheck() // 连接成功后开始超时检测
    }
    
    sseConnection.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        console.log('收到SSE消息:', data)
        
        if (data.type === 'connection_established') {
          addLog(`SSE连接建立：${data.message}`, 'info')
        } else if (data.type === 'email_sync_progress') {
          handleProgressUpdate(data.data)
        }
      } catch (error) {
        console.error('解析SSE消息失败:', error)
      }
    }
    
    sseConnection.value.onerror = (error) => {
      console.error('SSE连接错误:', error)
      addLog('SSE连接发生错误', 'error')
      // 尝试重连
      setTimeout(() => {
        if (visible.value && progress.value.status === 'running') {
          connectSSE()
        }
      }, 3000)
    }
    
    sseConnection.value.addEventListener('close', () => {
      console.log('SSE连接已关闭')
      addLog('SSE连接已关闭', 'warning')
      stopTimeoutCheck() // 连接关闭时停止超时检测
    })
    
  } catch (error) {
    console.error('创建SSE连接失败:', error)
    ElMessage.error('建立同步进度连接失败')
  }
}

const disconnectSSE = () => {
  if (sseConnection.value) {
    sseConnection.value.close()
    sseConnection.value = null
  }
}

const handleProgressUpdate = (progressData: SyncProgress) => {
  progress.value = { ...progressData }
  lastProgressTime.value = Date.now()  // 更新最后收到进度的时间
  
  // 添加到日志
  if (progressData.message) {
    const logType = progressData.status === 'failed' ? 'error' : 
                    progressData.status === 'success' ? 'success' : 'info'
    addLog(progressData.message, logType)
  }
  
  // 如果同步完成或失败，允许关闭
  if (progressData.status === 'success' || progressData.status === 'failed') {
    canCancel.value = false
    stopTimeoutCheck()
    
    // 发送完成事件
    if (progressData.status === 'success') {
      emit('completed', progressData)
    }
  }
}

// 开始超时检测
const startTimeoutCheck = () => {
  if (timeoutCheckInterval.value) {
    clearInterval(timeoutCheckInterval.value)
  }
  
  lastProgressTime.value = Date.now()
  timeoutCheckInterval.value = setInterval(() => {
    const now = Date.now()
    const idleTime = now - lastProgressTime.value
    
    if (idleTime > maxIdleTime && progress.value.status === 'running') {
      addLog(`同步超时（${Math.floor(idleTime/1000)}秒无响应）`, 'error')
      
      if (retryCount.value < maxRetries) {
        retryCount.value++
        addLog(`尝试重新连接 (${retryCount.value}/${maxRetries})`, 'warning')
        reconnectSSE()
      } else {
        addLog('已达到最大重试次数，同步可能已失败', 'error')
        progress.value.status = 'failed'
        progress.value.message = '同步超时，请检查网络连接或稍后重试'
        canCancel.value = false
        stopTimeoutCheck()
      }
    }
  }, 30000) // 每30秒检查一次
}

// 停止超时检测
const stopTimeoutCheck = () => {
  if (timeoutCheckInterval.value) {
    clearInterval(timeoutCheckInterval.value)
    timeoutCheckInterval.value = null
  }
}

// 重新连接SSE
const reconnectSSE = () => {
  disconnectSSE()
  setTimeout(() => {
    if (visible.value && progress.value.status === 'running') {
      connectSSE()
    }
  }, 5000) // 5秒后重连
}

const handleClose = () => {
  disconnectSSE()
  stopTimeoutCheck()
  visible.value = false
  
  // 重置状态
  setTimeout(() => {
    progress.value = {}
    logs.value = []
    canCancel.value = true
    retryCount.value = 0
    lastProgressTime.value = 0
  }, 300)
}

const handleCancel = () => {
  // TODO: 实现取消同步的逻辑
  ElMessage.warning('取消同步功能暂未实现')
  emit('cancelled')
}

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    connectSSE()
    addLog('开始邮箱成员同步...', 'info')
  } else {
    disconnectSSE()
  }
})

// 生命周期
onUnmounted(() => {
  disconnectSSE()
  stopTimeoutCheck()
})
</script>

<style scoped>
.email-sync-progress {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.progress-container {
  padding: 20px 0;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 700;
  color: #409eff;
}

.progress-message {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.details-section {
  margin: 24px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stages-section {
  margin: 24px 0;
}

.error-section {
  margin: 16px 0;
}

.logs-section {
  margin: 24px 0;
}

.logs-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e4e7ed;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #909399;
  margin-right: 8px;
  white-space: nowrap;
}

.log-message {
  color: #606266;
  flex: 1;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.dialog-footer {
  text-align: center;
}

.mt-4 {
  margin-top: 16px;
}

:deep(.el-progress-bar__outer) {
  border-radius: 10px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
}

:deep(.el-step__title) {
  font-size: 14px;
}

:deep(.el-step__description) {
  font-size: 12px;
}
</style> 