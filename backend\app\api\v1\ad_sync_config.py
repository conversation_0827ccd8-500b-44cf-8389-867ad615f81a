from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ... import models, schemas
from ...schemas.ad_config import ADSyncConfigCreate
from ...schemas import ADSync<PERSON>romPersonnel
from ...database import get_db
from ...services import ad_sync_config as ad_sync_config_service
from ...services import ad as ad_service
from ...utils import get_current_user
import logging
import json
import pandas as pd
from io import BytesIO
from fastapi.responses import StreamingResponse
from ...services import ad_sync_lock as ad_sync_lock_service
from ...services.ad import validate_ou_dn
import zipfile
import urllib.parse

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/config")
async def get_ad_sync_config(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取AD同步配置"""
    try:
        config = await ad_sync_config_service.get_ad_sync_config(db)

        if config.id == 0:
            # 返回默认配置
            return {
                "id": 0,
                "enabled": False,
                "source_company_id": None,
                "source_dept_id": None,
                "target_ou_dn": "",
                "create_ou": True,
                "create_security_groups": False,
                "add_users_to_dept_groups": False,
                "change_password_next_logon": True,
                "disable_inactive_users": True,
                "move_users_with_dept": True,
                "sync_interval": 24,
                "sync_time": None,
                "last_sync_time": None,
                "next_sync_time": None
            }

        # 计算下次同步时间
        next_sync_time = await ad_sync_config_service.calculate_next_sync_time(db, config)

        # 返回所有字段
        return {
            "id": config.id,
            "enabled": config.enabled,
            "source_company_id": config.source_company_id,
            "source_dept_id": config.source_dept_id,
            "target_ou_dn": config.target_ou_dn,
            "create_ou": config.create_ou,
            "create_security_groups": config.create_security_groups,
            "add_users_to_dept_groups": config.add_users_to_dept_groups,
            "change_password_next_logon": config.change_password_next_logon,
            "disable_inactive_users": config.disable_inactive_users,
            "move_users_with_dept": config.move_users_with_dept,
            "update_user_groups_with_dept": config.update_user_groups_with_dept,
            "auto_rename_security_groups": config.auto_rename_security_groups,
            "sync_interval": config.sync_interval,
            "sync_time": config.sync_time,
            "last_sync_time": config.last_sync_time,
            "next_sync_time": next_sync_time
        }
    except Exception as e:
        logger.error(f"获取AD同步配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/config", response_model=schemas.ADSyncConfigResponse)
async def update_ad_sync_config(
    config: ADSyncConfigCreate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新AD同步配置"""
    try:
        updated_config = await ad_sync_config_service.update_ad_sync_config(db, config)

        # 返回更新后的配置
        return {
            "id": updated_config.id,
            "enabled": updated_config.enabled,
            "source_company_id": updated_config.source_company_id,
            "source_dept_id": updated_config.source_dept_id,
            "target_ou_dn": updated_config.target_ou_dn,
            "create_ou": updated_config.create_ou,
            "create_security_groups": updated_config.create_security_groups,
            "add_users_to_dept_groups": updated_config.add_users_to_dept_groups,
            "change_password_next_logon": updated_config.change_password_next_logon,
            "disable_inactive_users": updated_config.disable_inactive_users,
            "move_users_with_dept": updated_config.move_users_with_dept,
            "update_user_groups_with_dept": updated_config.update_user_groups_with_dept,
            "auto_rename_security_groups": updated_config.auto_rename_security_groups,
            "sync_interval": updated_config.sync_interval,
            "sync_time": updated_config.sync_time,
            "last_sync_time": updated_config.last_sync_time
        }
    except Exception as e:
        logger.error(f"更新AD同步配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/run-sync")
async def run_manual_sync(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """手动执行同步"""
    try:
        # 检查是否有同步正在进行
        lock_name = "ad_personnel_sync"
        is_locked = await ad_sync_lock_service.is_locked(db, lock_name)
        if is_locked:
            lock_info = await ad_sync_lock_service.get_lock_info(db, lock_name)
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"已有同步操作正在进行中，由 {lock_info.get('locked_by', '未知')} 于 {lock_info.get('locked_at', '未知时间')} 开始"
            )

        # 获取同步配置
        config = await ad_sync_config_service.get_ad_sync_config(db)

        if config.id == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未配置同步设置，请先配置同步设置"
            )

        # 尝试获取锁
        lock_success = await ad_sync_lock_service.acquire_lock(db, lock_name, current_user.username)
        if not lock_success:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="无法获取同步锁，可能有其他同步操作正在进行"
            )

        try:
            # 验证 target_ou_dn 是否存在且有效
            if not config.target_ou_dn or not await validate_ou_dn(config.target_ou_dn):
                await ad_sync_lock_service.release_lock(db, lock_name, current_user.username)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"目标OU '{config.target_ou_dn}' 无效或不存在"
                )

            # 处理依赖选项的逻辑关系
            # 如果未启用创建安全组，则相关选项强制设为False
            create_security_groups = config.create_security_groups
            add_users_to_dept_groups = config.add_users_to_dept_groups if config.add_users_to_dept_groups is not None else False
            update_user_groups_with_dept = config.update_user_groups_with_dept if config.update_user_groups_with_dept is not None else False
            auto_rename_security_groups = config.auto_rename_security_groups if config.auto_rename_security_groups is not None else False

            if not create_security_groups:
                add_users_to_dept_groups = False
                update_user_groups_with_dept = False
                auto_rename_security_groups = False
                logger.info("由于未启用创建安全组，已自动禁用相关依赖选项")

            # 创建同步数据，确保包含所有必要参数
            sync_data = ADSyncFromPersonnel(
                company_id=config.source_company_id,
                dept_id=config.source_dept_id,
                create_ou=config.create_ou,
                create_security_groups=create_security_groups,
                add_users_to_dept_groups=add_users_to_dept_groups,
                parent_ou_dn=config.target_ou_dn,
                change_password_next_logon=config.change_password_next_logon,
                disable_inactive_users=config.disable_inactive_users,
                move_users_with_dept=config.move_users_with_dept,
                update_user_groups_with_dept=update_user_groups_with_dept,
                auto_rename_security_groups=auto_rename_security_groups
            )

            # 执行同步
            result = await ad_service.sync_from_personnel(sync_data)

            # 更新最后同步时间
            await ad_sync_config_service.update_last_sync_time(db)

            # 记录同步结果到日志
            log_data = {
                "operator": current_user.username,
                "source_type": "company" if config.source_company_id else ("department" if config.source_dept_id else "all"),
                "source_id": config.source_company_id or config.source_dept_id,
                "result": result
            }
            await ad_sync_config_service.add_sync_log(db, log_data)

            return result
        finally:
            # 无论同步成功与否，都释放锁
            await ad_sync_lock_service.release_lock(db, lock_name, current_user.username)

    except HTTPException:
        # 直接抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"手动执行同步失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/logs")
async def get_sync_logs(
    limit: int = 50,
    skip: int = 0,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取同步日志"""
    try:
        logs = await ad_sync_config_service.get_sync_logs(db, skip=skip, limit=limit)
        return logs
    except Exception as e:
        logger.error(f"获取同步日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/logs/{log_id}/export-passwords")
async def export_passwords(
    log_id: int,
    format: str = "xlsx",
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """导出同步日志中新创建用户的密码信息"""
    try:
        # 获取指定的日志记录
        log = await ad_sync_config_service.get_sync_log_by_id(db, log_id)
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{log_id}的同步日志"
            )

        # 从日志详情中提取新创建用户的信息
        try:
            details = json.loads(log.details)
        except json.JSONDecodeError:
            logger.error(f"解析同步日志 {log_id} 的details字段失败，内容: {log.details[:100]}...")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法解析同步日志详细信息"
            )

        # 尝试从不同位置提取new_users信息
        new_users = []
        if "stats" in details and "new_users" in details["stats"]:
            new_users = details["stats"]["new_users"]
        elif "new_users" in details:
            new_users = details["new_users"]

        if not new_users:
            logger.warning(f"同步日志 {log_id} 中没有找到新用户信息")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="此同步日志中没有新创建的用户信息"
            )

        # 记录找到的用户数量（不记录具体密码）
        logger.info(f"从同步日志 {log_id} 中找到 {len(new_users)} 个新用户记录，准备导出密码")

        # 准备数据
        data = []
        for user in new_users:
            data.append({
                "用户名": user.get("username", ""),
                "姓名": user.get("name", ""),
                "部门": user.get("department", ""),
                "初始密码": user.get("password", "")
            })

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 根据请求的格式生成响应
        filename = f"AD_Users_Passwords_{log.sync_time.strftime('%Y%m%d%H%M%S')}"
        content_disposition = ""

        if format.lower() == "csv":
            # 导出为CSV
            output = BytesIO()
            df.to_csv(output, index=False, encoding="utf_8_sig")
            output.seek(0)

            content_disposition = f'attachment; filename="{filename}.csv"'
            media_type = "text/csv"

            return StreamingResponse(
                output,
                media_type=media_type,
                headers={"Content-Disposition": content_disposition}
            )
        else:
            # 导出为XLSX (默认)
            output = BytesIO()
            with pd.ExcelWriter(output, engine="openpyxl") as writer:
                df.to_excel(writer, index=False, sheet_name="用户密码")
            output.seek(0)

            content_disposition = f'attachment; filename="{filename}.xlsx"'
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

            return StreamingResponse(
                output,
                media_type=media_type,
                headers={"Content-Disposition": content_disposition}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出用户密码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出用户密码失败: {str(e)}"
        )

@router.get("/logs/{log_id}/export-full")
async def export_full_log(
    log_id: int,
    format: str = "xlsx",
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """导出同步日志的完整信息"""
    try:
        # 获取指定的日志记录
        log = await ad_sync_config_service.get_sync_log_by_id(db, log_id)
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{log_id}的同步日志"
            )

        # 从日志详情中提取信息
        try:
            details = json.loads(log.details) if log.details else {}
            errors = json.loads(log.errors) if log.errors else []
        except json.JSONDecodeError:
            logger.error(f"解析同步日志 {log_id} 的详细信息失败")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法解析同步日志详细信息"
            )

        # 准备基本信息数据
        basic_info = [{
            "同步ID": log.id,
            "同步时间": log.sync_time.strftime("%Y-%m-%d %H:%M:%S"),
            "操作员": log.operator,
            "同步来源": f"{log.source_type}:{log.source_id}" if log.source_id else log.source_type,
            "总人数": log.total_users,
            "新增用户": log.created_users,
            "更新用户": log.updated_users,
            "移动用户": log.moved_users,
            "跳过用户": log.skipped_users,
            "禁用账号": log.disabled_users,
            "创建OU": log.created_ous,
            "重命名OU": log.renamed_ous,
            "创建安全组": log.created_groups,
            "更新安全组": log.updated_groups,
            "添加到安全组": log.added_to_groups,
            "移除安全组": log.removed_from_groups
        }]

        # 准备错误信息数据
        error_data = [{"错误信息": error} for error in errors] if errors else []

        # 提取各种详细操作数据
        new_users_data = details.get("new_users", [])
        updated_users_data = details.get("updated_users", [])
        moved_users_data = details.get("moved_users", [])
        # 兼容不同的数据结构: 优先尝试从details直接获取，否则从stats中获取
        disabled_users_data = details.get("disabled_users_details", []) or details.get("stats", {}).get("disabled_users_details", [])
        created_ous_data = details.get("created_ous", [])
        renamed_ous_data = details.get("renamed_ous", [])
        created_groups_data = details.get("created_groups", [])
        updated_groups_data = details.get("updated_groups", [])
        added_to_groups_data = details.get("added_to_groups", [])
        removed_from_groups_data = details.get("removed_from_groups", [])

        # 创建Excel文件 (即使输出CSV也先创建Excel，再转换)
        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            # 基本信息
            df_basic = pd.DataFrame(basic_info)
            df_basic.to_excel(writer, index=False, sheet_name="基本信息")

            # 错误信息
            if error_data:
                df_errors = pd.DataFrame(error_data)
                df_errors.to_excel(writer, index=False, sheet_name="错误信息")

            # 新增用户
            if new_users_data:
                df_new = pd.DataFrame([{
                    "用户名": user.get("username", ""),
                    "姓名": user.get("name", ""),
                    "部门": user.get("department", ""),
                    "职位": user.get("title", ""),
                    "初始密码": user.get("password", ""),
                    "DN": user.get("dn", "")
                } for user in new_users_data])
                df_new.to_excel(writer, index=False, sheet_name="新增用户")

            # 更新用户
            if updated_users_data:
                df_updated = pd.DataFrame([{
                    "用户名": user.get("username", ""),
                    "姓名": user.get("name", ""),
                    "部门": user.get("department", ""),
                    "更新字段": user.get("changed_fields", ""),
                    "DN": user.get("dn", "")
                } for user in updated_users_data])
                df_updated.to_excel(writer, index=False, sheet_name="更新用户")

            # 移动用户
            if moved_users_data:
                df_moved = pd.DataFrame([{
                    "用户名": user.get("username", ""),
                    "姓名": user.get("name", ""),
                    "原部门": user.get("old_department", ""),
                    "新部门": user.get("new_department", ""),
                    "DN": user.get("dn", "")
                } for user in moved_users_data])
                df_moved.to_excel(writer, index=False, sheet_name="部门调动")

            # 禁用账号
            if disabled_users_data:
                df_disabled = pd.DataFrame([{
                    "用户名": user.get("username", ""),
                    "姓名": user.get("name", ""),
                    "部门": user.get("department", ""),
                    "DN": user.get("dn", "")
                } for user in disabled_users_data])
                df_disabled.to_excel(writer, index=False, sheet_name="禁用离职账号")

            # 创建OU
            if created_ous_data:
                df_created_ous = pd.DataFrame([{
                    "OU名称": ou.get("name", ""),
                    "DN": ou.get("dn", "")
                } for ou in created_ous_data])
                df_created_ous.to_excel(writer, index=False, sheet_name="创建OU")

            # 重命名OU
            if renamed_ous_data:
                df_renamed_ous = pd.DataFrame([{
                    "原名称": ou.get("old_name", ""),
                    "新名称": ou.get("new_name", ""),
                    "DN": ou.get("dn", "")
                } for ou in renamed_ous_data])
                df_renamed_ous.to_excel(writer, index=False, sheet_name="重命名OU")

            # 创建安全组
            if created_groups_data:
                df_created_groups = pd.DataFrame([{
                    "组名称": group.get("name", ""),
                    "DN": group.get("dn", "")
                } for group in created_groups_data])
                df_created_groups.to_excel(writer, index=False, sheet_name="创建安全组")

            # 更新安全组
            if updated_groups_data:
                df_updated_groups = pd.DataFrame([{
                    "组名称": group.get("name", ""),
                    "更新字段": group.get("changed_fields", ""),
                    "DN": group.get("dn", "")
                } for group in updated_groups_data])
                df_updated_groups.to_excel(writer, index=False, sheet_name="更新安全组")

            # 添加到安全组
            if added_to_groups_data:
                df_added_to_groups = pd.DataFrame([{
                    "用户名": item.get("username", ""),
                    "用户姓名": item.get("name", ""),
                    "安全组": item.get("group_name", "")
                } for item in added_to_groups_data])
                df_added_to_groups.to_excel(writer, index=False, sheet_name="添加到安全组")

            # 从安全组移除
            if removed_from_groups_data:
                df_removed_from_groups = pd.DataFrame([{
                    "用户名": item.get("username", ""),
                    "用户姓名": item.get("name", ""),
                    "安全组": item.get("group_name", "")
                } for item in removed_from_groups_data])
                df_removed_from_groups.to_excel(writer, index=False, sheet_name="从安全组移除")

        # 准备响应
        filename = f"AD同步日志_{log.sync_time.strftime('%Y%m%d%H%M%S')}"

        if format.lower() == "csv":
            # 如果是CSV格式，需要将上面生成的Excel重新处理为多个CSV文件打包成zip
            output.seek(0)
            excel_data = output.getvalue()

            # 创建zip内存流
            zip_output = BytesIO()
            with zipfile.ZipFile(zip_output, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 读取Excel文件中的所有表格并转换为CSV
                excel = pd.ExcelFile(excel_data)
                for sheet_name in excel.sheet_names:
                    df = pd.read_excel(excel, sheet_name)

                    # 将DataFrame转换为CSV
                    csv_data = df.to_csv(index=False, encoding="utf_8_sig")

                    # 将CSV添加到zip文件
                    zipf.writestr(f"{sheet_name}.csv", csv_data)

            zip_output.seek(0)
            response_data = zip_output.getvalue()
            # 使用RFC标准的格式支持UTF-8编码的文件名
            filename_utf8 = f"{filename}.zip"
            content_disposition = f"attachment; filename*=UTF-8''{urllib.parse.quote(filename_utf8)}"
            media_type = "application/zip"
        else:
            # Excel格式直接返回
            output.seek(0)
            response_data = output.getvalue()
            # 使用RFC标准的格式支持UTF-8编码的文件名
            filename_utf8 = f"{filename}.xlsx"
            content_disposition = f"attachment; filename*=UTF-8''{urllib.parse.quote(filename_utf8)}"
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

        return StreamingResponse(
            BytesIO(response_data),
            media_type=media_type,
            headers={"Content-Disposition": content_disposition}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出完整日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出完整日志失败: {str(e)}"
        )