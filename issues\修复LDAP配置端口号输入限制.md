# 修复LDAP配置端口号输入限制

## 问题描述
用户反映在LDAP配置页面中，端口号输入框只能选择两位数字，无法输入完整的端口号（如636、389等）。

## 问题分析
经过代码审查发现，原始实现使用了 `el-input-number` 组件：
```vue
<el-input-number v-model="form.port" :min="1" :max="65535" />
```

虽然配置了正确的最小值和最大值，但可能存在以下问题：
1. Element Plus版本兼容性问题
2. 组件宽度限制导致显示不完整
3. 浏览器兼容性问题

## 解决方案
将 `el-input-number` 替换为 `el-input` 组件，并完善表单验证：

### 1. 修改LDAP配置管理组件
**文件**: `frontend/src/views/system/components/LdapConfigManagement.vue`

**修改内容**:
- 将端口号输入改为 `el-input` 组件
- 添加 `type="number"` 属性
- 使用 `v-model.number` 确保数据类型
- 添加占位符提示 "1-65535"
- 设置完整宽度样式

**表单验证增强**:
```javascript
port: [
  { required: true, message: '请输入端口号', trigger: 'blur' },
  { 
    type: 'number', 
    min: 1, 
    max: 65535, 
    message: '端口号必须在1-65535之间', 
    trigger: 'blur' 
  }
]
```

### 2. 同步修改AD配置页面
**文件**: `frontend/src/views/ad/ADConfig.vue`

为保持一致性，同样修改了AD配置页面的端口号输入组件。

## 技术实现

### 修改前
```vue
<el-input-number v-model="form.port" :min="1" :max="65535" />
```

### 修改后
```vue
<el-input 
  v-model.number="form.port" 
  type="number" 
  placeholder="1-65535"
  style="width: 100%"
/>
```

## 测试建议

1. **功能测试**
   - 输入常用端口号（22, 80, 389, 636, 3389, 8080等）
   - 测试边界值（1, 65535）
   - 测试无效值（0, 65536, 负数）

2. **验证测试**
   - 表单验证是否正确触发
   - 错误提示信息是否准确
   - 保存功能是否正常

3. **用户体验测试**
   - 输入体验是否流畅
   - 显示是否完整
   - 不同浏览器兼容性

## 预期效果
- ✅ 端口号可以正常输入1-65535范围内的任何数值
- ✅ 表单验证能够正确校验端口号格式和范围  
- ✅ 用户界面显示完整，用户体验改善
- ✅ 保持与AD配置页面的一致性

## 影响范围
- LDAP配置管理页面
- AD配置页面
- 相关的表单验证逻辑

## 补充修复：对话框滚动条问题

### 问题描述
用户反映LDAP配置对话框内部出现了滚动条，影响用户体验。

### 问题原因
原CSS样式中为 `.ldap-form` 设置了：
```css
.ldap-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}
```

### 解决方案
1. **移除表单内滚动**：去掉表单的高度限制和overflow设置
2. **优化对话框布局**：将滚动控制移到对话框层面
3. **改善视觉体验**：确保对话框在不同屏幕尺寸下都有良好显示

### 修复后的样式
```css
.ldap-form {
  padding-right: 8px;
}

.ldap-config-dialog :deep(.el-dialog) {
  max-height: 90vh;
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
}

.ldap-config-dialog :deep(.el-dialog__body) {
  max-height: calc(90vh - 120px);
  overflow-y: auto;
  padding: 20px 20px 0;
}
```

### 效果
- ✅ 消除了表单内部的滚动条
- ✅ 对话框高度自适应内容
- ✅ 在内容过多时，整个对话框区域可滚动
- ✅ 按钮区域始终可见
- ✅ 改善了整体用户体验

## 完成时间
2024-01-XX 