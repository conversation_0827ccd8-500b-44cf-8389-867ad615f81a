#!/usr/bin/env python3
"""
测试腾讯企业邮箱成员创建功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.database import SessionLocal
from app.services.email_api import TencentEmailAPIService
from app.crud.email import email_config

async def test_create_member():
    """测试创建成员功能"""
    db = SessionLocal()
    
    try:
        # 检查是否有有效的邮箱配置
        config = email_config.get_active(db)
        if not config:
            print("❌ 未找到有效的邮箱配置")
            return
        
        print(f"✅ 找到邮箱配置: {config.corp_id}")
        
        # 创建API服务实例
        api_service = TencentEmailAPIService(db)
        
        # 测试获取访问令牌
        try:
            access_token = await api_service.get_access_token()
            print(f"✅ 成功获取访问令牌: {access_token[:20]}...")
        except Exception as e:
            print(f"❌ 获取访问令牌失败: {str(e)}")
            return
        
        # 测试创建成员
        test_member_data = {
            "userid": "<EMAIL>",  # 请替换为您的域名
            "name": "测试用户",
            "department": [1],  # 根目录部门
            "password": "TestPass123!",
            "position": "测试工程师",
            "mobile": "13800138000"
        }
        
        print("📝 测试创建成员...")
        print(f"参数: {test_member_data}")
        
        try:
            result = await api_service.create_member(test_member_data)
            print(f"📋 API返回结果:")
            print(f"   错误码: {result.errcode}")
            print(f"   错误信息: {result.errmsg}")
            print(f"   完整响应: {result.data}")
            
            if result.errcode == 0:
                print("✅ 成员创建成功！")
            else:
                print(f"❌ 成员创建失败: {result.errmsg}")
                
        except Exception as e:
            print(f"❌ 调用API异常: {str(e)}")
        
    finally:
        db.close()

if __name__ == "__main__":
    print("🧪 开始测试腾讯企业邮箱成员创建功能...")
    asyncio.run(test_create_member())
    print("🏁 测试完成") 