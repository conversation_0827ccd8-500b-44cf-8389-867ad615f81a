<template>
  <div class="inventory-task-list">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Document /></el-icon>
        <h2 class="page-title">资产盘点任务</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>资产管理</el-breadcrumb-item>
        <el-breadcrumb-item>资产盘点</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索任务名称/描述/创建人"
            class="search-input"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="action-buttons">
          <Authority permission="inventory:add">
            <el-button type="primary" @click="showCreateModal">新建盘点任务</el-button>
          </Authority>
        </div>
      </div>

      <el-table
        :data="tasks"
        v-loading="loading"
        style="width: 100%"
        :cell-style="{ padding: '8px 0' }"
        header-row-class-name="table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column prop="name" label="任务名称" min-width="200">
          <template #header>
            <div class="column-header">任务名称</div>
          </template>
          <template #default="{ row }">
            <router-link
              :to="{ name: 'inventory-task-detail', params: { id: row.id }}"
              class="task-name-link"
            >
              {{ row.name }}
            </router-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="任务描述" min-width="200" show-overflow-tooltip>
          <template #header>
            <div class="column-header">任务描述</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #header>
            <div class="column-header">状态</div>
          </template>
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="start_date" label="开始日期" width="120">
          <template #header>
            <div class="column-header">开始日期</div>
          </template>
        </el-table-column>
        <el-table-column prop="end_date" label="结束日期" width="120">
          <template #header>
            <div class="column-header">结束日期</div>
          </template>
        </el-table-column>
        <el-table-column prop="created_by" label="创建人" width="120">
          <template #header>
            <div class="column-header">创建人</div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #header>
            <div class="column-header">创建时间</div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <Authority permission="inventory:edit">
                    <el-dropdown-item v-if="row.status !== 'completed'" @click="showEditModal(row)">
                      <el-icon><Edit /></el-icon>编辑
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="inventory:edit">
                    <el-dropdown-item v-if="row.status === 'pending'" @click="handleStart(row)">
                      <el-icon><VideoPlay /></el-icon>开始盘点
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="inventory:edit">
                    <el-dropdown-item v-if="row.status === 'in_progress'" @click="handleComplete(row)">
                      <el-icon><Select /></el-icon>完成盘点
                    </el-dropdown-item>
                  </Authority>
                  <Authority permission="inventory:delete">
                    <el-dropdown-item v-if="row.status !== 'completed'" divided @click="confirmDelete(row.id)" style="color: #F56C6C;">
                      <el-icon><Delete /></el-icon>删除
                    </el-dropdown-item>
                  </Authority>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'create' ? '新建盘点任务' : '编辑盘点任务'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.prevent
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入任务名称" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="盘点日期" prop="dateRange" required>
          <el-date-picker
            v-model="form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus'
import { Search, Edit, VideoPlay, Select, Delete, ArrowDown, Document } from '@element-plus/icons-vue'

import { inventoryApi } from '@/api/inventory'
import type { InventoryTask, InventoryTaskStatus } from '@/types/inventory'

// 表格数据
const tasks = ref<InventoryTask[]>([])
const loading = ref(false)

// 搜索表单
interface SearchForm {
  keyword: string
  status: string
  start_date_start: string
  start_date_end: string
  end_date_start: string
  end_date_end: string
  sort_by: string
  sort_order: string
}

const searchForm = reactive<SearchForm>({
  keyword: '',
  status: '',
  start_date_start: '',
  start_date_end: '',
  end_date_start: '',
  end_date_end: '',
  sort_by: '',
  sort_order: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true
  try {
    const response = await inventoryApi.getInventoryTasks({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      ...searchForm
    })
    tasks.value = response.data.data
    pagination.total = response.data.total
  } catch (error: any) {
    ElMessage.error('获取盘点任务列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchTasks()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchTasks()
}

const handleCurrentChange = (val: number) => {
  pagination.current = val
  fetchTasks()
}

// 表单相关
const formRef = ref<FormInstance>()
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const submitting = ref(false)

interface TaskForm {
  id?: number
  name: string
  description: string
  dateRange: string[]
}

const form = reactive<TaskForm>({
  name: '',
  description: '',
  dateRange: []
})

const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '请选择盘点日期', trigger: 'change' }
  ]
}

// 禁用今天之前的日期
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 显示创建对话框
const showCreateModal = () => {
  formMode.value = 'create'
  form.id = undefined
  form.name = ''
  form.description = ''
  form.dateRange = []
  formVisible.value = true
}

// 显示编辑对话框
const showEditModal = (task: InventoryTask) => {
  formMode.value = 'edit'
  form.id = task.id
  form.name = task.name
  form.description = task.description || ''
  form.dateRange = [task.start_date, task.end_date]
  formVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitting.value = true
      try {
        if (formMode.value === 'create') {
          await inventoryApi.createInventoryTask({
            name: form.name,
            description: form.description || undefined,
            start_date: form.dateRange[0],
            end_date: form.dateRange[1],
            created_by: 'admin' // TODO: 从用户状态获取
          })
          ElMessage.success('创建成功')
        } else {
          await inventoryApi.updateInventoryTask(form.id!, {
            name: form.name,
            description: form.description || undefined,
            start_date: form.dateRange[0],
            end_date: form.dateRange[1]
          })
          ElMessage.success('更新成功')
        }
        formVisible.value = false
        fetchTasks()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除任务
const handleDelete = async (id: number) => {
  try {
    await inventoryApi.deleteInventoryTask(id)
    ElMessage.success('删除成功')
    fetchTasks()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '删除失败')
  }
}

const confirmDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除这个盘点任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    handleDelete(id)
  }).catch(() => {
    // 用户取消操作
  })
}

// 开始盘点
const handleStart = async (task: InventoryTask) => {
  try {
    await inventoryApi.updateInventoryTask(task.id, {
      status: 'in_progress'
    })
    ElMessage.success('已开始盘点')
    fetchTasks()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 完成盘点
const handleComplete = async (task: InventoryTask) => {
  try {
    await inventoryApi.completeInventoryTask(task.id)
    ElMessage.success('盘点任务已完成')
    fetchTasks()
  } catch (error: any) {
    ElMessage.error(error.response?.data?.detail || '操作失败')
  }
}

// 获取状态类型
const getStatusType = (status: InventoryTaskStatus): string => {
  switch (status) {
    case 'pending':
      return 'info'
    case 'in_progress':
      return 'warning'
    case 'completed':
      return 'success'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: InventoryTaskStatus): string => {
  switch (status) {
    case 'pending':
      return '待盘点'
    case 'in_progress':
      return '盘点中'
    case 'completed':
      return '已完成'
    default:
      return '未知'
  }
}

// 初始加载
onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.inventory-task-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-form {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
}

.action-buttons {
  margin-left: 16px;
}

.task-name-link {
  color: var(--el-color-primary);
  text-decoration: none;
  font-weight: 500;
}

.task-name-link:hover {
  text-decoration: underline;
}

/* 表格样式统一 */
.table-header {
  background-color: var(--el-fill-color-light) !important;
}

.table-header-cell {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 