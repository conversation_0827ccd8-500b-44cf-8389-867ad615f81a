<template>
  <div class="mobile-inventory-list">
    <!-- 导航栏 -->
    <van-nav-bar
      title="资产盘点"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-button
          type="primary"
          size="small"
          @click="showCreateDialog = true"
          icon="plus"
          class="nav-create-btn"
        >
          新建
        </van-button>
      </template>
    </van-nav-bar>

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索任务名称或描述"
        @search="handleSearch"
        @clear="handleSearch"
      />
    </div>

    <!-- 状态筛选 -->
    <div class="filter-section">
      <van-tabs v-model="activeStatus" @change="handleStatusChange">
        <van-tab title="全部" name="all"></van-tab>
        <van-tab title="待开始" name="pending"></van-tab>
        <van-tab title="进行中" name="in_progress"></van-tab>
        <van-tab title="已完成" name="completed"></van-tab>
      </van-tabs>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="task in taskList"
            :key="task.id"
            class="task-item"
            @click="handleTaskClick(task)"
          >
            <van-card>
              <template #title>
                <div class="task-header">
                  <span class="task-name">{{ task.name }}</span>
                  <van-tag :type="getStatusTagType(task.status) as any">
                    {{ getStatusText(task.status) }}
                  </van-tag>
                </div>
              </template>
              
              <template #desc>
                <div class="task-desc">
                  <p v-if="task.description" class="description">
                    {{ task.description }}
                  </p>
                  <div class="task-info">
                    <div class="info-item">
                      <van-icon name="calendar-o" />
                      <span>{{ formatDate(task.start_date) }} - {{ formatDate(task.end_date) }}</span>
                    </div>
                    <div class="info-item">
                      <van-icon name="user-o" />
                      <span>{{ task.created_by }}</span>
                    </div>
                    <div class="info-item">
                      <van-icon name="clock-o" />
                      <span>{{ formatDateTime(task.created_at) }}</span>
                    </div>
                  </div>
                </div>
              </template>

              <template #footer>
                <div class="task-actions">
                  <van-button
                    v-if="task.status === 'pending'"
                    size="small"
                    type="primary"
                    @click.stop="handleStartTask(task)"
                  >
                    开始盘点
                  </van-button>
                  <van-button
                    v-if="task.status === 'in_progress'"
                    size="small"
                    type="success"
                    @click.stop="handleViewTask(task)"
                  >
                    继续盘点
                  </van-button>
                  <van-button
                    v-if="task.status === 'completed'"
                    size="small"
                    @click.stop="handleViewTask(task)"
                  >
                    查看结果
                  </van-button>
                  <van-button
                    v-if="task.status !== 'completed'"
                    size="small"
                    type="default"
                    @click.stop="showEditTaskDialog(task)"
                  >
                    编辑
                  </van-button>
                </div>
              </template>
            </van-card>
          </div>

          <!-- 空状态 -->
          <van-empty
            v-if="!loading && taskList.length === 0"
            image="search"
            description="暂无盘点任务"
          >
            <van-button
              type="primary"
              @click="showCreateDialog = true"
            >
              创建任务
            </van-button>
          </van-empty>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 创建任务弹窗 -->
    <van-popup
      v-model:show="showCreateDialog"
      position="bottom"
      :style="{ height: 'var(--mobile-popup-medium-height, 58svh)' }"
      round
    >
      <div class="create-popup">
        <div class="popup-header">
          <h3>新建盘点任务</h3>
          <van-icon name="cross" @click="showCreateDialog = false" />
        </div>
        
        <div class="popup-content">
          <van-form @submit="handleCreateTask" ref="createFormRef">
            <van-field
              v-model="createForm.name"
              name="name"
              label="任务名称"
              placeholder="请输入任务名称"
              :rules="[{ required: true, message: '请输入任务名称' }]"
            />
            <van-field
              v-model="createForm.description"
              name="description"
              label="任务描述"
              placeholder="请输入任务描述"
              type="textarea"
              rows="3"
            />
            <van-field
              v-model="createForm.startDate"
              name="startDate"
              label="开始日期"
              placeholder="请选择开始日期"
              readonly
              is-link
              @click="showStartDatePicker = true"
              :rules="[{ required: true, message: '请选择开始日期' }]"
            />
            <van-field
              v-model="createForm.endDate"
              name="endDate"
              label="结束日期"
              placeholder="请选择结束日期"
              readonly
              is-link
              @click="showEndDatePicker = true"
              :rules="[{ required: true, message: '请选择结束日期' }]"
            />
          </van-form>
        </div>

        <MobilePopupFooter :buttons="createFooterButtons" />
      </div>
    </van-popup>

    <!-- 编辑任务弹窗 -->
    <van-popup
      v-model:show="showEditDialogVisible"
      position="bottom"
      :style="{ height: 'var(--mobile-popup-medium-height, 58svh)' }"
      round
    >
      <div class="edit-popup">
        <div class="popup-header">
          <h3>编辑盘点任务</h3>
          <van-icon name="cross" @click="showEditDialogVisible = false" />
        </div>
        
        <div class="popup-content">
          <van-form @submit="handleUpdateTask" ref="editFormRef">
            <van-field
              v-model="editForm.name"
              name="name"
              label="任务名称"
              placeholder="请输入任务名称"
              :rules="[{ required: true, message: '请输入任务名称' }]"
            />
            <van-field
              v-model="editForm.description"
              name="description"
              label="任务描述"
              placeholder="请输入任务描述"
              type="textarea"
              rows="3"
            />
            <van-field
              v-model="editForm.startDate"
              name="startDate"
              label="开始日期"
              placeholder="请选择开始日期"
              readonly
              is-link
              @click="showEditStartDatePicker = true"
              :rules="[{ required: true, message: '请选择开始日期' }]"
            />
            <van-field
              v-model="editForm.endDate"
              name="endDate"
              label="结束日期"
              placeholder="请选择结束日期"
              readonly
              is-link
              @click="showEditEndDatePicker = true"
              :rules="[{ required: true, message: '请选择结束日期' }]"
            />
          </van-form>
        </div>

        <MobilePopupFooter :buttons="editFooterButtons" />
      </div>
    </van-popup>

    <!-- 开始日期选择器 -->
    <van-popup v-model:show="showStartDatePicker" position="bottom">
      <van-date-picker
        v-model="currentStartDate"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
        title="选择开始日期"
      />
    </van-popup>

    <!-- 结束日期选择器 -->
    <van-popup v-model:show="showEndDatePicker" position="bottom">
      <van-date-picker
        v-model="currentEndDate"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
        title="选择结束日期"
      />
    </van-popup>

    <!-- 编辑开始日期选择器 -->
    <van-popup v-model:show="showEditStartDatePicker" position="bottom">
      <van-date-picker
        v-model="currentEditStartDate"
        @confirm="onEditStartDateConfirm"
        @cancel="showEditStartDatePicker = false"
        title="选择开始日期"
      />
    </van-popup>

    <!-- 编辑结束日期选择器 -->
    <van-popup v-model:show="showEditEndDatePicker" position="bottom">
      <van-date-picker
        v-model="currentEditEndDate"
        @confirm="onEditEndDateConfirm"
        @cancel="showEditEndDatePicker = false"
        title="选择结束日期"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { inventoryApi } from '@/api/inventory'
import type { InventoryTask, InventoryTaskCreate, InventoryTaskUpdate, InventoryTaskStatus } from '@/types/inventory'
import { formatDate } from '@/utils/date'
import MobilePopupFooter from '@/mobile/components/MobilePopupFooter.vue'

const router = useRouter()

// 数据状态
const taskList = ref<InventoryTask[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const searchKeyword = ref('')
const activeStatus = ref<string>('all')

// 分页状态
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 弹窗状态
const showCreateDialog = ref(false)
const showEditDialogVisible = ref(false)
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)
const showEditStartDatePicker = ref(false)
const showEditEndDatePicker = ref(false)

// 表单状态
const creating = ref(false)
const updating = ref(false)
const deleting = ref(false)
const editingTask = ref<InventoryTask | null>(null)

// 表单引用
const createFormRef = ref()
const editFormRef = ref()

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  startDate: '',
  endDate: ''
})

// 编辑表单
const editForm = reactive({
  name: '',
  description: '',
  startDate: '',
  endDate: ''
})

// 日期状态
const currentStartDate = ref<string[]>([])
const currentEndDate = ref<string[]>([])
const currentEditStartDate = ref<string[]>([])
const currentEditEndDate = ref<string[]>([])

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取状态标签类型
const getStatusTagType = (status: InventoryTaskStatus) => {
  const statusMap: Record<InventoryTaskStatus, string> = {
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success'
  }
  return statusMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: InventoryTaskStatus) => {
  const statusMap: Record<InventoryTaskStatus, string> = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载任务列表
const loadTasks = async (reset = false) => {
  try {
    if (reset) {
      pagination.current = 1
      taskList.value = []
      finished.value = false
    }

    loading.value = true

    const params = {
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize,
      keyword: searchKeyword.value || undefined,
      status: activeStatus.value === 'all' ? undefined : activeStatus.value,
      sort_by: 'created_at',
      sort_order: 'desc'
    }

    const response = await inventoryApi.getInventoryTasks(params)
    const { data, total } = response.data || response

    if (reset) {
      taskList.value = data
    } else {
      taskList.value = [...taskList.value, ...data]
    }

    pagination.total = total
    pagination.current++

    // 检查是否还有更多数据
    if (taskList.value.length >= total) {
      finished.value = true
    }

  } catch (error) {
    console.error('加载任务列表失败:', error)
    showToast('加载任务列表失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 下拉刷新
const onRefresh = () => {
  loadTasks(true)
}

// 加载更多
const onLoad = () => {
  if (!finished.value) {
    loadTasks()
  }
}

// 搜索
const handleSearch = () => {
  loadTasks(true)
}

// 状态筛选
const handleStatusChange = () => {
  loadTasks(true)
}

// 任务点击
const handleTaskClick = (task: InventoryTask) => {
  router.push(`/m/asset/inventory/task/${task.id}`)
}

// 开始任务
const handleStartTask = async (task: InventoryTask) => {
  try {
    await showConfirmDialog({
      title: '确认开始',
      message: '确定要开始这个盘点任务吗？'
    })

    await inventoryApi.updateInventoryTask(task.id, { status: 'in_progress' })
    showSuccessToast('任务已开始')
    loadTasks(true)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始任务失败:', error)
      showToast('开始任务失败')
    }
  }
}

// 查看任务
const handleViewTask = (task: InventoryTask) => {
  router.push(`/m/asset/inventory/task/${task.id}`)
}

// 显示编辑弹窗
const showEditTaskDialog = (task: InventoryTask) => {
  editingTask.value = task
  editForm.name = task.name
  editForm.description = task.description || ''
  editForm.startDate = task.start_date
  editForm.endDate = task.end_date
  
  // 设置日期选择器的初始值
  if (task.start_date) {
    const date = new Date(task.start_date)
    currentEditStartDate.value = [
      date.getFullYear().toString(),
      (date.getMonth() + 1).toString().padStart(2, '0'),
      date.getDate().toString().padStart(2, '0')
    ]
  }
  
  if (task.end_date) {
    const date = new Date(task.end_date)
    currentEditEndDate.value = [
      date.getFullYear().toString(),
      (date.getMonth() + 1).toString().padStart(2, '0'),
      date.getDate().toString().padStart(2, '0')
    ]
  }
  
  showEditDialogVisible.value = true
}

// 日期选择确认
const onStartDateConfirm = ({ selectedValues }: any) => {
  currentStartDate.value = selectedValues
  createForm.startDate = selectedValues.join('-')
  showStartDatePicker.value = false
}

const onEndDateConfirm = ({ selectedValues }: any) => {
  currentEndDate.value = selectedValues
  createForm.endDate = selectedValues.join('-')
  showEndDatePicker.value = false
}

const onEditStartDateConfirm = ({ selectedValues }: any) => {
  currentEditStartDate.value = selectedValues
  editForm.startDate = selectedValues.join('-')
  showEditStartDatePicker.value = false
}

const onEditEndDateConfirm = ({ selectedValues }: any) => {
  currentEditEndDate.value = selectedValues
  editForm.endDate = selectedValues.join('-')
  showEditEndDatePicker.value = false
}

// 创建任务
const handleCreateTask = async () => {
  try {
    await createFormRef.value.validate()
    
    if (createForm.startDate >= createForm.endDate) {
      showToast('结束日期必须晚于开始日期')
      return
    }

    creating.value = true

    const createData: InventoryTaskCreate = {
      name: createForm.name,
      description: createForm.description || undefined,
      start_date: createForm.startDate,
      end_date: createForm.endDate,
      created_by: '当前用户' // TODO: 从用户状态获取
    }

    await inventoryApi.createInventoryTask(createData)
    showSuccessToast('任务创建成功')
    
    // 重置表单
    createForm.name = ''
    createForm.description = ''
    createForm.startDate = ''
    createForm.endDate = ''
    
    showCreateDialog.value = false
    loadTasks(true)
  } catch (error: any) {
    if (error.name !== 'ValidationError') {
      console.error('创建任务失败:', error)
      showToast('创建任务失败')
    }
  } finally {
    creating.value = false
  }
}

// 更新任务
const handleUpdateTask = async () => {
  if (!editingTask.value) return

  try {
    await editFormRef.value.validate()
    
    if (editForm.startDate >= editForm.endDate) {
      showToast('结束日期必须晚于开始日期')
      return
    }

    updating.value = true

    const updateData: InventoryTaskUpdate = {
      name: editForm.name,
      description: editForm.description || undefined,
      start_date: editForm.startDate,
      end_date: editForm.endDate
    }

    await inventoryApi.updateInventoryTask(editingTask.value.id, updateData)
    showSuccessToast('任务更新成功')
    
    showEditDialogVisible.value = false
    editingTask.value = null
    loadTasks(true)
  } catch (error: any) {
    if (error.name !== 'ValidationError') {
      console.error('更新任务失败:', error)
      showToast('更新任务失败')
    }
  } finally {
    updating.value = false
  }
}

// 删除任务
const handleDeleteTask = async () => {
  if (!editingTask.value) return

  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个盘点任务吗？删除后无法恢复。'
    })

    deleting.value = true
    await inventoryApi.deleteInventoryTask(editingTask.value.id)
    
    showSuccessToast('任务删除成功')
    showEditDialogVisible.value = false
    editingTask.value = null
    loadTasks(true)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      showToast('删除任务失败')
    }
  } finally {
    deleting.value = false
  }
}

// 创建任务底部按钮配置
const createFooterButtons = computed(() => [
  {
    text: '创建任务',
    type: 'primary' as const,
    loading: creating.value,
    onClick: handleCreateTask
  }
])

// 编辑任务底部按钮配置
const editFooterButtons = computed(() => {
  const buttons = []
  
  if (editingTask.value && editingTask.value.status !== 'completed') {
    buttons.push({
      text: '删除',
      type: 'danger' as const,
      loading: deleting.value,
      onClick: handleDeleteTask
    })
  }
  
  buttons.push({
    text: '保存',
    type: 'primary' as const,
    loading: updating.value,
    onClick: handleUpdateTask
  })
  
  return buttons
})

onMounted(() => {
  loadTasks(true)
})
</script>

<style lang="scss" scoped>
.mobile-inventory-list {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.search-section {
  padding: 12px 16px;
  background: white;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #eee;
}

.task-list {
  padding: 16px;
}

.task-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 16px;
  }
  
  :deep(.van-card) {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    padding: 18px;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  :deep(.van-card__header) {
    padding: 0 0 14px 0;
  }
  
  :deep(.van-card__content) {
    padding: 0 0 14px 0;
  }
  
  :deep(.van-card__footer) {
    padding: 14px 0 0 0;
    border-top: 1px solid #f5f5f5;
    margin-top: 2px;
  }
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 4px;
  
  .task-name {
    font-weight: 600;
    font-size: 16px;
    color: #323233;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
    padding-top: 2px;
  }
  
  .van-tag {
    flex-shrink: 0;
    border-radius: 12px;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
  }
}

.task-desc {
  .description {
    margin: 0 0 16px 0;
    color: #646566;
    font-size: 14px;
    line-height: 1.6;
    padding: 8px 0;
  }
  
  .task-info {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 13px;
      color: #969799;
      line-height: 1.4;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .van-icon {
        margin-right: 6px;
        font-size: 14px;
        flex-shrink: 0;
        color: #c8c9cc;
      }
      
      span {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.task-actions {
  display: flex;
  gap: 12px;
  margin-top: 4px;
  
  .van-button {
    flex: 1;
    height: 36px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
  }
}

.create-popup,
.edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .van-icon {
      font-size: 20px;
      color: #969799;
    }
  }
  
  .popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }
  

}

:deep(.van-field) {
  padding: 12px 0;
}

:deep(.van-tabs__wrap) {
  padding: 0 16px;
}

:deep(.van-tab) {
  flex: none;
  margin-right: 24px;
}

:deep(.van-empty) {
  padding: 60px 0;
}

// 状态标签样式优化
:deep(.van-tag) {
  border-radius: 12px;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 500;
  border: none;
}

:deep(.van-tag--primary) {
  background: linear-gradient(135deg, #1989fa 0%, #1677ff 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(25, 137, 250, 0.3);
}

:deep(.van-tag--warning) {
  background: linear-gradient(135deg, #ff976a 0%, #f79346 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(255, 151, 106, 0.3);
}

:deep(.van-tag--success) {
  background: linear-gradient(135deg, #52c41a 0%, #41a817 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

// 导航栏新建按钮样式
.nav-create-btn {
  border-radius: 16px !important;
  padding: 6px 12px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  background: linear-gradient(135deg, #1989fa 0%, #1677ff 100%) !important;
  border: none !important;
  box-shadow: 0 2px 6px rgba(25, 137, 250, 0.3) !important;
  color: white !important;
  
  .van-icon {
    margin-right: 4px;
    font-size: 14px;
  }
}

// 页面按钮统一样式
:deep(.van-button) {
  border-radius: 8px;
  font-weight: 500;
  border: none;
}

:deep(.van-button--primary) {
  background: linear-gradient(135deg, #1989fa 0%, #1677ff 100%);
  box-shadow: 0 2px 4px rgba(25, 137, 250, 0.3);
}

:deep(.van-button--success) {
  background: linear-gradient(135deg, #52c41a 0%, #41a817 100%);
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

:deep(.van-button--warning) {
  background: linear-gradient(135deg, #ff976a 0%, #f79346 100%);
  box-shadow: 0 2px 4px rgba(255, 151, 106, 0.3);
}

:deep(.van-button--default) {
  background: white;
  color: #1989fa;
  border: 1px solid #1989fa;
  box-shadow: 0 1px 3px rgba(25, 137, 250, 0.2);
}

:deep(.van-button--default:active) {
  background: rgba(25, 137, 250, 0.1);
}

:deep(.van-button--small) {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 6px;
}
</style> 