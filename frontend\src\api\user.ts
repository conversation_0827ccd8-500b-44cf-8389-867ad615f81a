import request from '@/utils/request'
import type { User, UserCreate, UserUpdate } from '../types/user'

export const userApi = {
  // 获取当前用户信息
  getCurrentUser: () => {
    return request({
      url: '/system/users/me',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取用户列表
  getUsers: (params: {
    skip?: number
    limit?: number
    keyword?: string
  }) => {
    return request.get<{ data: User[], total: number }>('/api/v1/system/users', { params })
  },

  // 创建用户
  createUser: (data: UserCreate) => {
    return request.post<User>('/api/v1/system/users', data)
  },

  // 更新用户
  updateUser: (id: number, data: UserUpdate) => {
    return request.put<User>(`/api/v1/system/users/${id}`, data)
  },

  // 删除用户
  deleteUser: (id: number) => {
    return request.delete(`/api/v1/system/users/${id}`)
  },

  // 更新用户密码
  updatePassword: (oldPassword: string, newPassword: string) => {
    return request.put('/api/v1/system/users/me/password', {
      old_password: oldPassword,
      new_password: newPassword
    })
  }
} 