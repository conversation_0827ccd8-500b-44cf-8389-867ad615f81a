from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, desc, asc

from app.crud.base import CRUDBase
from app.models.custom_field import CustomField, AssetCustomFieldValue, InventoryRecordCustomFieldValue
from app.schemas.custom_field import (
    CustomFieldCreate, CustomFieldUpdate,
    AssetCustomFieldValueCreate, AssetCustomFieldValueUpdate,
    InventoryRecordCustomFieldValueCreate, InventoryRecordCustomFieldValueUpdate
)

class CRUDCustomField(CRUDBase[CustomField, CustomFieldCreate, CustomFieldUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[CustomField]:
        """根据字段名称获取自定义字段"""
        return db.query(self.model).filter(self.model.name == name).first()

    def get_active_fields(
        self, 
        db: Session, 
        *, 
        applies_to: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CustomField]:
        """获取启用的自定义字段列表"""
        query = db.query(self.model).filter(self.model.is_active == True)
        
        if applies_to:
            query = query.filter(
                or_(
                    self.model.applies_to == applies_to,
                    self.model.applies_to == "both"
                )
            )
        
        query = query.order_by(asc(self.model.sort_order), asc(self.model.id))
        return query.offset(skip).limit(limit).all()

    def search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        field_type: Optional[str] = None,
        applies_to: Optional[str] = None,
        is_active: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CustomField]:
        """搜索自定义字段"""
        query = db.query(self.model)
        
        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.label.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )
        
        if field_type:
            query = query.filter(self.model.field_type == field_type)
        
        if applies_to:
            query = query.filter(
                or_(
                    self.model.applies_to == applies_to,
                    self.model.applies_to == "both"
                )
            )
        
        if is_active is not None:
            query = query.filter(self.model.is_active == is_active)
        
        query = query.order_by(asc(self.model.sort_order), asc(self.model.id))
        return query.offset(skip).limit(limit).all()

    def get_total(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        field_type: Optional[str] = None,
        applies_to: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """获取搜索结果总数"""
        query = db.query(self.model)
        
        if keyword:
            query = query.filter(
                or_(
                    self.model.name.ilike(f"%{keyword}%"),
                    self.model.label.ilike(f"%{keyword}%"),
                    self.model.description.ilike(f"%{keyword}%")
                )
            )
        
        if field_type:
            query = query.filter(self.model.field_type == field_type)
        
        if applies_to:
            query = query.filter(
                or_(
                    self.model.applies_to == applies_to,
                    self.model.applies_to == "both"
                )
            )
        
        if is_active is not None:
            query = query.filter(self.model.is_active == is_active)
        
        return query.count()

    def update_sort_orders(self, db: Session, *, field_orders: List[Dict[str, int]]) -> bool:
        """批量更新字段排序"""
        try:
            for item in field_orders:
                field_id = item.get("id")
                sort_order = item.get("sort_order", 0)
                
                db.query(self.model).filter(
                    self.model.id == field_id
                ).update({"sort_order": sort_order})
            
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

    def create_preset_fields(self, db: Session, *, preset_type: str, applies_to: str) -> List[CustomField]:
        """创建预设字段配置"""
        preset_configs = self._get_preset_configs(preset_type, applies_to)
        created_fields = []
        
        for config in preset_configs:
            # 检查是否已存在同名字段
            existing = self.get_by_name(db, name=config["name"])
            if existing:
                continue
            
            field_create = CustomFieldCreate(**config)
            created_field = self.create(db, obj_in=field_create)
            created_fields.append(created_field)
        
        return created_fields

    def _get_preset_configs(self, preset_type: str, applies_to: str) -> List[Dict[str, Any]]:
        """获取预设字段配置"""
        configs = {
            "asset_basic": [
                {
                    "name": "photo",
                    "label": "资产照片",
                    "field_type": "file",
                    "description": "资产照片上传",
                    "is_required": False,
                    "options": {"accept": "image/*", "max_size": 5242880},  # 5MB
                    "sort_order": 1,
                    "applies_to": applies_to
                },
                {
                    "name": "condition_rating",
                    "label": "成色评级",
                    "field_type": "select",
                    "description": "资产成色评级",
                    "is_required": False,
                    "options": {
                        "choices": [
                            {"label": "全新", "value": "new"},
                            {"label": "九成新", "value": "excellent"},
                            {"label": "八成新", "value": "good"},
                            {"label": "七成新", "value": "fair"},
                            {"label": "六成新以下", "value": "poor"}
                        ]
                    },
                    "sort_order": 2,
                    "applies_to": applies_to
                }
            ],
            "inventory_basic": [
                {
                    "name": "check_photo",
                    "label": "盘点照片",
                    "field_type": "file",
                    "description": "盘点时拍摄的照片",
                    "is_required": False,
                    "options": {"accept": "image/*", "max_size": 5242880},
                    "sort_order": 1,
                    "applies_to": applies_to
                },
                {
                    "name": "check_notes",
                    "label": "盘点备注",
                    "field_type": "textarea",
                    "description": "盘点时的详细备注",
                    "is_required": False,
                    "sort_order": 2,
                    "applies_to": applies_to
                }
            ]
        }
        
        return configs.get(preset_type, [])

    def safe_delete_custom_field(self, db: Session, *, field_id: int) -> tuple[bool, str]:
        """安全删除自定义字段，先检查关联数据量"""
        from app.models.custom_field import AssetCustomFieldValue, InventoryRecordCustomFieldValue
        
        # 检查关联的资产字段值数量
        asset_value_count = db.query(AssetCustomFieldValue).filter(
            AssetCustomFieldValue.custom_field_id == field_id
        ).count()
        
        # 检查关联的盘点记录字段值数量
        inventory_value_count = db.query(InventoryRecordCustomFieldValue).filter(
            InventoryRecordCustomFieldValue.custom_field_id == field_id
        ).count()
        
        total_count = asset_value_count + inventory_value_count
        
        # 如果关联数据过多，建议手动处理
        if total_count > 1000:
            return False, f"字段关联了 {total_count} 条数据，数据量过大，请联系管理员删除"
        
        # 如果有关联数据，给出警告
        if total_count > 0:
            warning_msg = f"将删除字段及其关联的 {total_count} 条数据，此操作不可恢复"
        else:
            warning_msg = "删除成功"
        
        try:
            # 正常删除流程（会级联删除关联的字段值）
            self.remove(db, id=field_id)
            return True, warning_msg
        except Exception as e:
            return False, f"删除失败: {str(e)}"

class CRUDAssetCustomFieldValue(CRUDBase[AssetCustomFieldValue, AssetCustomFieldValueCreate, AssetCustomFieldValueUpdate]):
    def get_by_asset_and_field(
        self, 
        db: Session, 
        *, 
        asset_id: int, 
        custom_field_id: int
    ) -> Optional[AssetCustomFieldValue]:
        """根据资产ID和字段ID获取字段值"""
        return db.query(self.model).filter(
            and_(
                self.model.asset_id == asset_id,
                self.model.custom_field_id == custom_field_id
            )
        ).first()

    def get_by_asset(self, db: Session, *, asset_id: int) -> List[AssetCustomFieldValue]:
        """获取指定资产的所有自定义字段值"""
        return db.query(self.model).filter(
            self.model.asset_id == asset_id
        ).all()

    def batch_create_or_update(
        self, 
        db: Session, 
        *, 
        asset_id: int, 
        field_values: List[Dict[str, Any]]
    ) -> List[AssetCustomFieldValue]:
        """批量创建或更新资产自定义字段值"""
        results = []
        
        try:
            for item in field_values:
                custom_field_id = item.get("custom_field_id")
                value = item.get("value")
                
                if not custom_field_id:
                    continue  # 跳过无效的字段ID
                
                # 检查是否已存在
                existing = self.get_by_asset_and_field(
                    db, asset_id=asset_id, custom_field_id=custom_field_id
                )
                
                if existing:
                    # 更新
                    updated = self.update(
                        db, db_obj=existing, obj_in=AssetCustomFieldValueUpdate(value=value)
                    )
                    results.append(updated)
                else:
                    # 创建
                    created = self.create(
                        db, obj_in=AssetCustomFieldValueCreate(
                            asset_id=asset_id,
                            custom_field_id=custom_field_id,
                            value=value
                        )
                    )
                    results.append(created)
            
            # 统一提交事务
            db.commit()
            return results
            
        except Exception as e:
            # 发生错误时回滚事务
            db.rollback()
            raise e

    def delete_by_asset(self, db: Session, *, asset_id: int) -> bool:
        """删除指定资产的所有自定义字段值"""
        try:
            db.query(self.model).filter(self.model.asset_id == asset_id).delete()
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

class CRUDInventoryRecordCustomFieldValue(CRUDBase[InventoryRecordCustomFieldValue, InventoryRecordCustomFieldValueCreate, InventoryRecordCustomFieldValueUpdate]):
    def get_by_record_and_field(
        self, 
        db: Session, 
        *, 
        inventory_record_id: Optional[int] = None,
        task_id: Optional[int] = None,
        asset_id: Optional[int] = None,
        custom_field_id: int
    ) -> Optional[InventoryRecordCustomFieldValue]:
        """根据盘点记录ID和字段ID获取字段值"""
        query = db.query(self.model).filter(self.model.custom_field_id == custom_field_id)
        
        if inventory_record_id:
            query = query.filter(self.model.inventory_record_id == inventory_record_id)
        elif task_id and asset_id:
            # 虚拟记录查询
            query = query.filter(
                and_(
                    self.model.task_id == task_id,
                    self.model.asset_id == asset_id,
                    self.model.inventory_record_id.is_(None)
                )
            )
        else:
            return None
        
        return query.first()

    def get_by_record(
        self, 
        db: Session, 
        *, 
        inventory_record_id: Optional[int] = None,
        task_id: Optional[int] = None,
        asset_id: Optional[int] = None
    ) -> List[InventoryRecordCustomFieldValue]:
        """获取指定盘点记录的所有自定义字段值"""
        query = db.query(self.model)
        
        if inventory_record_id:
            query = query.filter(self.model.inventory_record_id == inventory_record_id)
        elif task_id and asset_id:
            # 虚拟记录查询
            query = query.filter(
                and_(
                    self.model.task_id == task_id,
                    self.model.asset_id == asset_id,
                    self.model.inventory_record_id.is_(None)
                )
            )
        else:
            return []
        
        return query.all()

    def batch_create_or_update(
        self, 
        db: Session, 
        *, 
        inventory_record_id: Optional[int] = None,
        task_id: Optional[int] = None,
        asset_id: Optional[int] = None,
        field_values: List[Dict[str, Any]]
    ) -> List[InventoryRecordCustomFieldValue]:
        """批量创建或更新盘点记录自定义字段值"""
        results = []
        
        try:
            for item in field_values:
                custom_field_id = item.get("custom_field_id")
                value = item.get("value")
                
                if not custom_field_id:
                    continue  # 跳过无效的字段ID
                
                # 检查是否已存在
                existing = self.get_by_record_and_field(
                    db, 
                    inventory_record_id=inventory_record_id,
                    task_id=task_id,
                    asset_id=asset_id,
                    custom_field_id=custom_field_id
                )
                
                if existing:
                    # 更新
                    updated = self.update(
                        db, db_obj=existing, obj_in=InventoryRecordCustomFieldValueUpdate(value=value)
                    )
                    results.append(updated)
                else:
                    # 创建
                    created = self.create(
                        db, obj_in=InventoryRecordCustomFieldValueCreate(
                            inventory_record_id=inventory_record_id,
                            task_id=task_id,
                            asset_id=asset_id,
                            custom_field_id=custom_field_id,
                            value=value
                        )
                    )
                    results.append(created)
            
            # 统一提交事务
            db.commit()
            return results
            
        except Exception as e:
            # 发生错误时回滚事务
            db.rollback()
            raise e

# 创建实例
custom_field_crud = CRUDCustomField(CustomField)
asset_custom_field_value_crud = CRUDAssetCustomFieldValue(AssetCustomFieldValue)
inventory_record_custom_field_value_crud = CRUDInventoryRecordCustomFieldValue(InventoryRecordCustomFieldValue) 