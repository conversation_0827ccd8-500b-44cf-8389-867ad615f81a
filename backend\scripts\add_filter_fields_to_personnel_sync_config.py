#!/usr/bin/env python3
"""
添加人员同步过滤字段到PersonnelSyncConfig表
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from sqlalchemy import text
from app.database import engine
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_filter_fields():
    """为PersonnelSyncConfig表添加过滤相关字段"""
    
    # 定义要添加的字段
    fields_to_add = [
        {
            'name': 'filter_enabled',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN filter_enabled BOOLEAN DEFAULT FALSE',
            'comment': '是否启用过滤功能'
        },
        {
            'name': 'included_companies',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN included_companies JSON',
            'comment': '包含的公司列表'
        },
        {
            'name': 'included_departments',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN included_departments JSON',
            'comment': '包含的部门列表'
        },
        {
            'name': 'included_job_titles',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN included_job_titles JSON',
            'comment': '包含的职位列表'
        },
        {
            'name': 'excluded_job_titles',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN excluded_job_titles JSON',
            'comment': '排除的职位列表'
        },
        {
            'name': 'filter_logic',
            'sql': 'ALTER TABLE personnel_sync_config ADD COLUMN filter_logic VARCHAR(10) DEFAULT "AND"',
            'comment': '过滤逻辑(AND/OR)'
        }
    ]
    
    with engine.connect() as connection:
        # 检查表是否存在
        table_exists = connection.execute(
            text("SELECT name FROM sqlite_master WHERE type='table' AND name='personnel_sync_config'")
        ).fetchone()
        
        if not table_exists:
            logger.error("表 personnel_sync_config 不存在，请先运行数据库初始化")
            return False
        
        # 获取现有列信息
        existing_columns = connection.execute(
            text("PRAGMA table_info(personnel_sync_config)")
        ).fetchall()
        
        existing_column_names = [col[1] for col in existing_columns]
        logger.info(f"现有列: {existing_column_names}")
        
        # 添加字段
        added_count = 0
        for field in fields_to_add:
            if field['name'] not in existing_column_names:
                try:
                    logger.info(f"添加字段: {field['name']} - {field['comment']}")
                    connection.execute(text(field['sql']))
                    connection.commit()
                    added_count += 1
                    logger.info(f"✓ 成功添加字段: {field['name']}")
                except Exception as e:
                    logger.error(f"✗ 添加字段 {field['name']} 失败: {str(e)}")
                    return False
            else:
                logger.info(f"字段 {field['name']} 已存在，跳过")
        
        logger.info(f"迁移完成，共添加 {added_count} 个字段")
        
        # 验证字段是否正确添加
        updated_columns = connection.execute(
            text("PRAGMA table_info(personnel_sync_config)")
        ).fetchall()
        
        updated_column_names = [col[1] for col in updated_columns]
        logger.info(f"更新后的列: {updated_column_names}")
        
        # 检查所有必需的字段是否都存在
        missing_fields = []
        for field in fields_to_add:
            if field['name'] not in updated_column_names:
                missing_fields.append(field['name'])
        
        if missing_fields:
            logger.error(f"以下字段添加失败: {missing_fields}")
            return False
        
        logger.info("所有过滤字段都已成功添加")
        return True

def main():
    """主函数"""
    logger.info("开始为PersonnelSyncConfig表添加过滤字段...")
    
    try:
        success = add_filter_fields()
        if success:
            logger.info("数据库迁移成功完成!")
        else:
            logger.error("数据库迁移失败!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 