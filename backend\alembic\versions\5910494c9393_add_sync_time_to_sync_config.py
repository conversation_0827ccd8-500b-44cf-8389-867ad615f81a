"""Add sync time to sync config

Revision ID: 5910494c9393
Revises: 4e2cb655b923
Create Date: 2025-02-28 11:23:38.870535

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite


# revision identifiers, used by Alembic.
revision: str = '5910494c9393'
down_revision: Union[str, None] = '4e2cb655b923'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加同步时间字段
    op.add_column('ecology_sync_config', sa.Column('sync_time', sa.String(), nullable=True))
    
    # 将同步间隔字段重命名为同步时间（小时）
    with op.batch_alter_table('ecology_sync_config') as batch_op:
        batch_op.alter_column('sync_interval', new_column_name='sync_hour', 
                             existing_type=sa.Integer(), nullable=True)


def downgrade() -> None:
    # 恢复同步时间字段为同步间隔
    with op.batch_alter_table('ecology_sync_config') as batch_op:
        batch_op.alter_column('sync_hour', new_column_name='sync_interval', 
                             existing_type=sa.Integer(), nullable=True)
    
    # 删除同步时间字段
    op.drop_column('ecology_sync_config', 'sync_time')
