from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from app.models.command_whitelist import CommandCategory, CommandWhitelist
from app.schemas.command_whitelist import (
    CommandCategoryCreate, CommandCategoryUpdate,
    CommandWhitelistCreate, CommandWhitelistUpdate
)
import re


# 命令分类CRUD
def get_command_categories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    is_active: Optional[bool] = None
) -> List[CommandCategory]:
    """获取命令分类列表"""
    query = db.query(CommandCategory)
    
    if is_active is not None:
        query = query.filter(CommandCategory.is_active == is_active)
    
    return query.offset(skip).limit(limit).all()


def get_command_category(db: Session, category_id: int) -> Optional[CommandCategory]:
    """根据ID获取命令分类"""
    return db.query(CommandCategory).filter(CommandCategory.id == category_id).first()


def get_command_category_by_name(db: Session, name: str) -> Optional[CommandCategory]:
    """根据名称获取命令分类"""
    return db.query(CommandCategory).filter(CommandCategory.name == name).first()


def create_command_category(db: Session, category: CommandCategoryCreate) -> CommandCategory:
    """创建命令分类"""
    db_category = CommandCategory(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category


def update_command_category(
    db: Session, 
    category_id: int, 
    category_update: CommandCategoryUpdate
) -> Optional[CommandCategory]:
    """更新命令分类"""
    db_category = get_command_category(db, category_id)
    if not db_category:
        return None
    
    update_data = category_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    return db_category


def delete_command_category(db: Session, category_id: int) -> bool:
    """删除命令分类"""
    db_category = get_command_category(db, category_id)
    if not db_category:
        return False
    
    db.delete(db_category)
    db.commit()
    return True


# 命令白名单CRUD
def get_command_whitelist(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    security_level: Optional[str] = None,
    search: Optional[str] = None
) -> List[CommandWhitelist]:
    """获取命令白名单列表"""
    query = db.query(CommandWhitelist).options(joinedload(CommandWhitelist.category))
    
    if category_id is not None:
        query = query.filter(CommandWhitelist.category_id == category_id)
    
    if is_active is not None:
        query = query.filter(CommandWhitelist.is_active == is_active)
    
    if security_level:
        query = query.filter(CommandWhitelist.security_level == security_level)
    
    if search:
        search_filter = or_(
            CommandWhitelist.name.ilike(f"%{search}%"),
            CommandWhitelist.command.ilike(f"%{search}%"),
            CommandWhitelist.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    return query.offset(skip).limit(limit).all()


def get_command_whitelist_item(db: Session, command_id: int) -> Optional[CommandWhitelist]:
    """根据ID获取命令白名单项"""
    return db.query(CommandWhitelist).options(
        joinedload(CommandWhitelist.category)
    ).filter(CommandWhitelist.id == command_id).first()


def create_command_whitelist(db: Session, command: CommandWhitelistCreate) -> CommandWhitelist:
    """创建命令白名单项"""
    db_command = CommandWhitelist(**command.dict())
    db.add(db_command)
    db.commit()
    db.refresh(db_command)
    return db_command


def update_command_whitelist(
    db: Session,
    command_id: int,
    command_update: CommandWhitelistUpdate
) -> Optional[CommandWhitelist]:
    """更新命令白名单项"""
    db_command = get_command_whitelist_item(db, command_id)
    if not db_command:
        return None
    
    update_data = command_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_command, field, value)
    
    db.commit()
    db.refresh(db_command)
    return db_command


def delete_command_whitelist(db: Session, command_id: int) -> bool:
    """删除命令白名单项"""
    db_command = get_command_whitelist_item(db, command_id)
    if not db_command:
        return False
    
    db.delete(db_command)
    db.commit()
    return True


def get_command_templates(db: Session) -> Dict[str, List[CommandWhitelist]]:
    """获取按分类组织的命令模板"""
    categories = db.query(CommandCategory).filter(CommandCategory.is_active == True).all()
    
    templates = {}
    for category in categories:
        commands = db.query(CommandWhitelist).options(
            joinedload(CommandWhitelist.category)
        ).filter(
            and_(
                CommandWhitelist.category_id == category.id,
                CommandWhitelist.is_active == True
            )
        ).all()
        
        templates[category.name] = commands
    
    return templates


def validate_command(db: Session, command: str) -> Dict[str, Any]:
    """验证命令是否在白名单中"""
    # 清理命令输入
    command = command.strip()
    
    # 基础安全检查
    dangerous_patterns = [
        r'\bformat\b',
        r'\bdel\s+',
        r'\brd\s+',
        r'\brmdir\b',
        r'\bshutdown\b',
        r'\breboot\b',
        r'\brestart\b',
        r'>\s*nul',
        r'&\s*&',
        r'\|\s*\|',
        r';\s*;',
        r'`.*`',
        r'\$\(',
        r'%.*%'
    ]
    
    for pattern in dangerous_patterns:
        if re.search(pattern, command, re.IGNORECASE):
            return {
                "is_valid": False,
                "message": f"命令包含危险操作模式: {pattern}",
                "security_level": None,
                "required_permission": None,
                "matched_command": None
            }
    
    # 长度检查
    if len(command) > 500:
        return {
            "is_valid": False,
            "message": "命令长度超过500字符限制",
            "security_level": None,
            "required_permission": None,
            "matched_command": None
        }
    
    # 检查白名单匹配
    whitelist_commands = db.query(CommandWhitelist).options(
        joinedload(CommandWhitelist.category)
    ).filter(CommandWhitelist.is_active == True).all()
    
    for wl_command in whitelist_commands:
        # 精确匹配
        if command.lower() == wl_command.command.lower():
            return {
                "is_valid": True,
                "message": "命令匹配白名单",
                "security_level": wl_command.security_level,
                "required_permission": wl_command.category.required_permission,
                "matched_command": wl_command
            }
        
        # 前缀匹配（对于带参数的命令）
        if command.lower().startswith(wl_command.command.lower() + " "):
            return {
                "is_valid": True,
                "message": "命令匹配白名单（带参数）",
                "security_level": wl_command.security_level,
                "required_permission": wl_command.category.required_permission,
                "matched_command": wl_command
            }
    
    return {
        "is_valid": False,
        "message": "命令不在白名单中",
        "security_level": None,
        "required_permission": None,
        "matched_command": None
    }


def get_commands_by_permission(db: Session, permissions: List[str]) -> List[CommandWhitelist]:
    """根据用户权限获取可用命令"""
    categories = db.query(CommandCategory).filter(
        and_(
            CommandCategory.is_active == True,
            CommandCategory.required_permission.in_(permissions)
        )
    ).all()
    
    category_ids = [cat.id for cat in categories]
    
    return db.query(CommandWhitelist).options(
        joinedload(CommandWhitelist.category)
    ).filter(
        and_(
            CommandWhitelist.category_id.in_(category_ids),
            CommandWhitelist.is_active == True
        )
    ).all() 