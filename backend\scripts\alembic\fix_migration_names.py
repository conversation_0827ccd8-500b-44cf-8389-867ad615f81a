import os
import re
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

def check_and_fix_migration_files():
    # 迁移文件目录
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    migrations_dir = os.path.join(root_dir, 'alembic', 'versions')
    print(f"检查迁移目录: {migrations_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(migrations_dir):
        print(f"错误: 迁移目录 {migrations_dir} 不存在!")
        return
    
    # 获取所有迁移文件
    migration_files = os.listdir(migrations_dir)
    
    # 迁移文件命名格式正则表达式 (例如: 12345678abcd_some_description.py)
    valid_pattern = re.compile(r'^[0-9a-f]{12}_.*\.py$')
    
    # 记录需要修复的文件
    files_to_fix = []
    
    print("检查迁移文件名...")
    for filename in migration_files:
        if filename.endswith('.py') and not valid_pattern.match(filename):
            # 从文件内容中读取revision ID
            file_path = os.path.join(migrations_dir, filename)
            if os.path.isfile(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 尝试获取revision ID
                        revision_match = re.search(r'revision\s*=\s*[\'"]([0-9a-f]+)[\'"]', content)
                        if not revision_match:
                            revision_match = re.search(r'revision: str = [\'"]([0-9a-f]+)[\'"]', content)
                            
                        # 尝试获取描述
                        description = filename.replace('.py', '')
                        if '_' in description:
                            description = description.split('_', 1)[1]
                            
                        if revision_match:
                            revision_id = revision_match.group(1)
                            files_to_fix.append((filename, revision_id, description))
                            print(f"  - 需要修复: {filename} -> {revision_id}_{description}.py")
                        else:
                            print(f"  - 警告: 无法从 {filename} 中提取revision ID")
                except Exception as e:
                    print(f"  - 错误: 处理文件 {filename} 时出现问题: {e}")
    
    # 询问是否修复文件名
    if files_to_fix:
        print("\n以下文件需要重命名:")
        for i, (old_name, revision_id, description) in enumerate(files_to_fix, 1):
            new_name = f"{revision_id}_{description}.py"
            print(f"{i}. {old_name} -> {new_name}")
        
        print("\n警告: 通常不建议直接重命名迁移文件，这可能会导致数据库迁移历史不一致。")
        print("请确保备份数据库和迁移文件后再继续。")
        
        answer = input("\n是否继续修复这些文件名? (y/N): ")
        if answer.lower() == 'y':
            for old_name, revision_id, description in files_to_fix:
                old_path = os.path.join(migrations_dir, old_name)
                new_name = f"{revision_id}_{description}.py"
                new_path = os.path.join(migrations_dir, new_name)
                
                try:
                    os.rename(old_path, new_path)
                    print(f"已重命名: {old_name} -> {new_name}")
                except Exception as e:
                    print(f"重命名 {old_name} 失败: {e}")
        else:
            print("已取消重命名操作。")
            print("您应该手动检查这些文件，并确保迁移依赖关系正确。")
    else:
        print("所有迁移文件名称格式正确。")

if __name__ == "__main__":
    check_and_fix_migration_files() 