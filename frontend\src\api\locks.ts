import request from '@/utils/request'

// 锁信息类型定义
export interface LockInfo {
  lock_name: string
  is_locked: boolean
  locked_at: string | null
  updated_at: string | null
  locked_by: string | null
  timeout_seconds: number
  is_expired: boolean
  time_remaining: number
  operation_type?: string // 邮箱锁特有
}

// 锁状态统计
export interface LockStatusSummary {
  total: number
  active: number
  expired: number
  locks: LockInfo[]
}

// 全局锁状态
export interface GlobalLockStatus {
  service_running: boolean
  cleanup_interval: number
  last_cleanup: string
  ad_locks: LockStatusSummary
  email_locks: LockStatusSummary
  summary: {
    total_locks: number
    total_active: number
    total_expired: number
  }
}

// 清理结果
export interface CleanupResult {
  cleanup_time: string
  ad_locks_cleaned?: number
  email_locks_cleaned?: number
  total_cleaned: number
  duration_ms?: number
  operator?: string
  ad_locks_released?: number
  email_locks_released?: number
  total_released?: number
}

// API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message: string
}

export const locksApi = {
  // 获取所有锁的状态信息
  getAllLocksStatus: (): Promise<ApiResponse<GlobalLockStatus>> => {
    return request({
      url: '/locks/status',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取AD同步锁状态
  getAdLocksStatus: (): Promise<ApiResponse<LockStatusSummary>> => {
    return request({
      url: '/locks/ad/status',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取邮箱同步锁状态
  getEmailLocksStatus: (): Promise<ApiResponse<LockStatusSummary>> => {
    return request({
      url: '/locks/email/status',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取指定AD锁的详细信息
  getAdLockInfo: (lockName: string): Promise<ApiResponse<LockInfo>> => {
    return request({
      url: `/locks/ad/${lockName}`,
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取指定邮箱锁的详细信息
  getEmailLockInfo: (lockName: string): Promise<ApiResponse<LockInfo>> => {
    return request({
      url: `/locks/email/${lockName}`,
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 手动清理过期锁
  cleanupExpiredLocks: (): Promise<ApiResponse<CleanupResult>> => {
    return request({
      url: '/locks/cleanup',
      method: 'post',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 强制清理所有锁（紧急情况使用）
  forceCleanupAllLocks: (): Promise<ApiResponse<CleanupResult>> => {
    return request({
      url: '/locks/force-cleanup',
      method: 'post',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 强制释放指定AD锁
  forceReleaseAdLock: (lockName: string): Promise<ApiResponse<any>> => {
    return request({
      url: `/locks/ad/${lockName}`,
      method: 'delete',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 强制释放指定邮箱锁
  forceReleaseEmailLock: (lockName: string): Promise<ApiResponse<any>> => {
    return request({
      url: `/locks/email/${lockName}`,
      method: 'delete',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  }
}

// 工具函数
export const lockUtils = {
  // 格式化时间显示
  formatTime: (timeStr: string | null): string => {
    if (!timeStr) return '-'
    try {
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return timeStr
    }
  },

  // 格式化剩余时间显示
  formatTimeRemaining: (seconds: number): string => {
    if (seconds <= 0) return '已过期'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`
    } else {
      return `${secs}秒`
    }
  },

  // 获取锁状态的显示文本和样式
  getLockStatusDisplay: (lock: LockInfo) => {
    if (!lock.is_locked) {
      return {
        text: '未锁定',
        type: 'info' as const,
        color: '#909399'
      }
    }
    
    if (lock.is_expired) {
      return {
        text: '已过期',
        type: 'danger' as const,
        color: '#F56C6C'
      }
    }
    
    return {
      text: '锁定中',
      type: 'warning' as const,
      color: '#E6A23C'
    }
  },

  // 获取操作类型的显示文本
  getOperationTypeDisplay: (operationType: string | undefined): string => {
    const typeMap: Record<string, string> = {
      'cache_sync': '缓存同步',
      'extid_completion': '工号补全',
      'personnel_sync': '人员同步',
      'consistency_check': '一致性检查',
      'full_sync': '全量同步'
    }
    return typeMap[operationType || ''] || operationType || '-'
  }
} 