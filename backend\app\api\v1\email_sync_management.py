"""
邮箱同步锁管理API接口
提供查看锁状态、强制释放锁等功能
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models.user import User
from app.api import deps
from app.services.email_sync_lock import (
    get_lock_info, force_release_all_locks, is_locked, 
    check_conflicting_operations, LOCK_TYPES
)
from app.models.email_sync_lock import EmailSyncLock

router = APIRouter()

@router.get("/locks/status")
async def get_all_locks_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取所有邮箱同步锁的状态"""
    try:
        # 查询所有锁
        locks = db.query(EmailSyncLock).all()
        
        lock_status = []
        for lock in locks:
            lock_status.append({
                "lock_name": lock.lock_name,
                "is_locked": lock.is_locked,
                "locked_by": lock.locked_by,
                "operation_type": lock.operation_type,
                "locked_at": lock.locked_at.isoformat() if lock.locked_at else None,
                "updated_at": lock.updated_at.isoformat() if lock.updated_at else None
            })
        
        return {
            "locks": lock_status,
            "total_locks": len(locks),
            "active_locks": len([l for l in locks if l.is_locked])
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取锁状态失败: {str(e)}"
        )

@router.get("/locks/{lock_name}")
async def get_lock_status(
    lock_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取指定锁的状态"""
    try:
        lock_info = await get_lock_info(db, lock_name)
        
        if not lock_info:
            return {
                "lock_name": lock_name,
                "exists": False,
                "is_locked": False
            }
        
        return {
            "exists": True,
            **lock_info
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取锁状态失败: {str(e)}"
        )

@router.post("/locks/check-conflicts")
async def check_operation_conflicts(
    operation_type: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """检查指定操作类型是否有冲突"""
    try:
        # 验证操作类型
        if operation_type not in LOCK_TYPES.values():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的操作类型: {operation_type}"
            )
        
        conflicts = await check_conflicting_operations(db, operation_type)
        
        return {
            "operation_type": operation_type,
            "has_conflicts": conflicts is not None,
            "conflicts": conflicts["conflicts"] if conflicts else []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查冲突失败: {str(e)}"
        )

@router.post("/locks/force-release-all")
async def force_release_all(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:admin"]))
):
    """强制释放所有邮箱同步锁（紧急情况使用）"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以强制释放所有锁"
        )
    
    try:
        released_count = await force_release_all_locks(db, current_user.username)
        
        return {
            "success": True,
            "message": f"已强制释放 {released_count} 个锁",
            "released_count": released_count,
            "operator": current_user.username
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"强制释放锁失败: {str(e)}"
        )

@router.get("/operations/types")
async def get_operation_types(
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取所有可用的操作类型"""
    return {
        "operation_types": [
            {
                "key": key,
                "value": value,
                "description": {
                    "CACHE_SYNC": "缓存同步（腾讯→本地）",
                    "EXTID_COMPLETION": "工号补全",
                    "PERSONNEL_SYNC": "人员同步（泛微→腾讯）",
                    "CONSISTENCY_CHECK": "数据一致性检查",
                    "FULL_SYNC": "全量同步"
                }.get(key, "")
            }
            for key, value in LOCK_TYPES.items()
        ]
    }

@router.get("/status/summary")
async def get_sync_status_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取邮箱同步状态概要"""
    try:
        # 查询所有锁
        locks = db.query(EmailSyncLock).all()
        
        # 统计各种操作类型的状态
        operation_stats = {}
        for lock in locks:
            if lock.operation_type:
                if lock.operation_type not in operation_stats:
                    operation_stats[lock.operation_type] = {
                        "total": 0,
                        "active": 0,
                        "locks": []
                    }
                
                operation_stats[lock.operation_type]["total"] += 1
                if lock.is_locked:
                    operation_stats[lock.operation_type]["active"] += 1
                    operation_stats[lock.operation_type]["locks"].append({
                        "lock_name": lock.lock_name,
                        "locked_by": lock.locked_by,
                        "locked_at": lock.locked_at.isoformat() if lock.locked_at else None
                    })
        
        return {
            "total_locks": len(locks),
            "active_locks": len([l for l in locks if l.is_locked]),
            "operation_stats": operation_stats,
            "system_status": "active" if any(l.is_locked for l in locks) else "idle"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取状态概要失败: {str(e)}"
        ) 