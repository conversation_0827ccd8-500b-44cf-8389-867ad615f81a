from typing import List, Optional
from sqlalchemy.orm import Session

from app.models.asset import AssetChangeLog
from app.schemas.asset_change_log import AssetChangeLogCreate

def create_asset_change_log(
    db: Session,
    *,
    asset_id: int,
    field: str,
    old_value: Optional[str],
    new_value: Optional[str],
    change_type: str
) -> AssetChangeLog:
    """创建资产变更记录"""
    db_obj = AssetChangeLog(
        asset_id=asset_id,
        field=field,
        old_value=old_value,
        new_value=new_value,
        change_type=change_type
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj

def get_asset_change_logs(
    db: Session,
    *,
    asset_id: int,
    skip: int = 0,
    limit: int = 100
) -> List[AssetChangeLog]:
    """获取资产变更记录列表"""
    return db.query(AssetChangeLog)\
        .filter(AssetChangeLog.asset_id == asset_id)\
        .order_by(AssetChangeLog.created_at.desc())\
        .offset(skip)\
        .limit(limit)\
        .all() 