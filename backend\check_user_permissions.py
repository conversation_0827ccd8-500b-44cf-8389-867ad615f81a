#!/usr/bin/env python3
"""检查用户权限脚本"""

from app.database import SessionLocal
from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role
from sqlalchemy.orm import selectinload

def check_user_permissions():
    db = SessionLocal()
    try:
        # 查找admin用户
        user = db.query(User).options(
            selectinload(User.roles).selectinload(Role.permissions)
        ).filter(User.email == '<EMAIL>').first()
        
        if not user:
            print("❌ 未找到admin用户")
            return
        
        print(f"👤 用户: {user.email}")
        print(f"🔐 是否超级管理员: {user.is_superuser}")
        print(f"📋 角色数量: {len(user.roles)}")
        
        # 收集所有权限
        all_perms = set()
        for role in user.roles:
            print(f"  📝 角色: {role.name}")
            for perm in role.permissions:
                all_perms.add(perm.code)
        
        print(f"\n🛡️ 总权限数量: {len(all_perms)}")
        
        # 检查命令白名单相关权限
        target_perms = [
            'terminal:command:view',
            'terminal:command:manage', 
            'terminal:command:send'
        ]
        
        print("\n🔍 命令白名单权限检查:")
        for perm in target_perms:
            status = "✓" if perm in all_perms else "✗"
            print(f"  {status} {perm}")
        
        # 检查是否有terminal模块相关权限
        terminal_perms = [p for p in all_perms if p.startswith('terminal:')]
        print(f"\n🖥️ Terminal模块权限({len(terminal_perms)}):")
        for perm in sorted(terminal_perms):
            print(f"  ✓ {perm}")
            
    finally:
        db.close()

if __name__ == "__main__":
    check_user_permissions() 