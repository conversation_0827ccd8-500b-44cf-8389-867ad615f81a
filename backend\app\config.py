from pydantic_settings import BaseSettings
from functools import lru_cache
import os

class Settings(BaseSettings):
    # 数据库配置
    # PostgreSQL配置
    POSTGRES_HOST: str = "**************"
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str = "user_mzPZG2"
    POSTGRES_PASSWORD: str = "password_8RdabK"
    POSTGRES_DB: str = "ops_platform"
    
    # 数据库URL - 默认使用PostgreSQL，可通过环境变量覆盖
    DATABASE_URL: str = "************************************************************/ops_platform"
    

    
    # JWT配置
    SECRET_KEY: str = "Hey@2024"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 240  # 从30分钟提高到4小时
    REMEMBER_TOKEN_EXPIRE_DAYS: int = 30    # 从7天提高到30天
    
    # gRPC配置
    GRPC_SERVER_ADDRESS: str = "localhost:50051"  # gRPC服务器地址
    
    # Agent系统Token配置
    AGENT_SYSTEM_TOKEN: str = os.getenv("AGENT_SYSTEM_TOKEN", "ops-agent-system-token-2024")  # Agent系统级访问Token
    AGENT_TOKEN_EXPIRE_HOURS: int = int(os.getenv("AGENT_TOKEN_EXPIRE_HOURS", "24"))  # Agent Token过期时间（小时）
    
    # Redis配置 - 优化连接性能
    REDIS_HOST: str = "**************"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = "redis_w4PNJy"
    REDIS_DB: int = 0
    REDIS_TTL: int = 300  # 默认缓存时间（5分钟），现在由分层配置管理
    REDIS_MAX_CONNECTIONS: int = 50  # 最大连接数
    REDIS_RETRY_ON_TIMEOUT: bool = True  # 超时重试
    
    # 缓存配置 - 优化缓存性能
    CACHE_ENABLED: bool = True
    CACHE_DEFAULT_TTL: int = 300  # 默认TTL：5分钟
    CACHE_MAX_TTL: int = 3600     # 最大TTL：1小时
    CACHE_MIN_TTL: int = 30       # 最小TTL：30秒
    CACHE_COMPRESSION: bool = True  # 启用缓存压缩

    class Config:
        env_file = ".env"
        extra = "ignore"

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()