"""
人员信息与邮箱同步API接口
"""

import logging
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session

from app.database import get_db
from app.utils import get_current_user
from app.models.user import User
from app.api import deps
from app.services.email_extid_completion import EmailExtidCompletionService
from app.services.data_backup import DataBackupService
from app.schemas.email_personnel_sync import (
    ExtidCompletionResult,
    ExtidCompletionStats,
    ExtidCompletionBatchRequest,
    ExtidCompletionBatchResult,
    ManualMatchRequest,
    ManualExtidRequest,
    ExtidCompletionPaginatedResponse,
    DataBackupRequest,
    DataBackupInfo,
    DataRestoreRequest,
    RecompletionStrategy,
    RecompletionRequest,
    RecompletionAnalysisResult,
    RecompletionResult
)

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/extid-completion/stats", response_model=ExtidCompletionStats)
async def get_extid_completion_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取工号补全统计信息"""
    try:
        service = EmailExtidCompletionService(db)
        stats = service.get_completion_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取工号补全统计失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

@router.get("/extid-completion/candidates")
async def get_extid_completion_candidates(
    similarity_threshold: float = 0.8,
    page: int = 1,
    page_size: int = 20,
    search: str = None,
    status_filter: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取工号补全候选者列表（分页）"""
    try:
        service = EmailExtidCompletionService(db)
        result = await service.get_completion_candidates_paginated(
            similarity_threshold=similarity_threshold,
            page=page,
            page_size=page_size,
            search=search,
            status_filter=status_filter
        )
        return result
    except Exception as e:
        logger.error(f"获取工号补全候选者失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取候选者失败: {str(e)}"
        )

@router.post("/extid-completion/auto-execute", response_model=ExtidCompletionBatchResult)
async def execute_auto_extid_completion(
    request: ExtidCompletionBatchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """执行自动工号补全"""
    # 导入锁服务
    from app.services.email_sync_lock import acquire_lock, release_lock, check_conflicting_operations, LOCK_TYPES
    
    # 检查是否有冲突的操作正在进行
    conflicts = await check_conflicting_operations(db, LOCK_TYPES["EXTID_COMPLETION"])
    if conflicts:
        conflict_info = ", ".join([f"{c['operation_type']}({c['locked_by']})" for c in conflicts["conflicts"]])
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"无法执行工号补全，有冲突的操作正在进行：{conflict_info}"
        )
    
    # 尝试获取锁
    lock_name = "email_extid_completion"
    lock_acquired = await acquire_lock(db, lock_name, LOCK_TYPES["EXTID_COMPLETION"], current_user.username)
    
    if not lock_acquired:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="工号补全操作正在进行中，请稍后再试"
        )
    
    try:
        service = EmailExtidCompletionService(db)
        result = await service.execute_auto_completion(
            similarity_threshold=request.similarity_threshold,
            auto_confirm_exact_match=request.auto_confirm_exact_match,
            dry_run=request.dry_run
        )
        return result
    except Exception as e:
        logger.error(f"执行自动工号补全失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"自动补全失败: {str(e)}"
        )
    finally:
        # 无论成功还是失败，都要释放锁
        await release_lock(db, lock_name, current_user.username)

@router.post("/extid-completion/manual-confirm")
async def manual_confirm_extid_match(
    request: ManualMatchRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """手动确认工号匹配"""
    try:
        service = EmailExtidCompletionService(db)
        success = await service.manual_match_confirm(request)
        
        if success:
            return {"success": True, "message": "工号匹配确认成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="工号匹配确认失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动确认工号匹配失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认匹配失败: {str(e)}"
        )

@router.post("/extid-completion/manual-set")
async def manual_set_extid(
    request: ManualExtidRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """手动设置工号"""
    try:
        service = EmailExtidCompletionService(db)
        success = await service.manual_set_extid(request)

        if success:
            return {"success": True, "message": "工号设置成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="工号设置失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"手动设置工号失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"设置工号失败: {str(e)}"
        )

@router.post("/extid-completion/clear-cache")
async def clear_completion_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """清除工号补全缓存"""
    try:
        service = EmailExtidCompletionService(db)
        service.clear_cache()
        return {"success": True, "message": "缓存已清除"}
    except Exception as e:
        logger.error(f"清除缓存失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除缓存失败: {str(e)}"
        )

# 重新补齐相关接口
@router.post("/extid-completion/analyze-recompletion", response_model=RecompletionAnalysisResult)
async def analyze_recompletion_candidates(
    strategy: RecompletionStrategy,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """分析需要重新补齐的候选者"""
    try:
        service = EmailExtidCompletionService(db)
        result = await service.analyze_recompletion_candidates(strategy)
        return result
    except Exception as e:
        logger.error(f"分析重新补齐候选者失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析失败: {str(e)}"
        )

@router.post("/extid-completion/execute-recompletion", response_model=RecompletionResult)
async def execute_recompletion(
    request: RecompletionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """执行重新补齐操作"""
    try:
        service = EmailExtidCompletionService(db)
        result = await service.execute_recompletion(request)
        return result
    except Exception as e:
        logger.error(f"执行重新补齐失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新补齐失败: {str(e)}"
        )

# 数据备份相关接口
@router.post("/backup/create", response_model=List[DataBackupInfo])
async def create_data_backup(
    request: DataBackupRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """创建数据备份"""
    try:
        service = DataBackupService(db)
        backup_infos = service.create_backup(request)
        return backup_infos
    except Exception as e:
        logger.error(f"创建数据备份失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建备份失败: {str(e)}"
        )

@router.get("/backup/list")
async def list_data_backups(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """列出所有数据备份"""
    try:
        service = DataBackupService(db)
        backups = service.list_backups()
        return backups
    except Exception as e:
        logger.error(f"列出数据备份失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"列出备份失败: {str(e)}"
        )

@router.get("/backup/{backup_id}")
async def get_backup_info(
    backup_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取指定备份的详细信息"""
    try:
        service = DataBackupService(db)
        backup_info = service.get_backup_info(backup_id)
        
        if not backup_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="备份不存在"
            )
        
        # 获取备份大小信息
        size_info = service.get_backup_size(backup_id)
        backup_info.update(size_info)
        
        return backup_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取备份信息失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取备份信息失败: {str(e)}"
        )

@router.post("/backup/{backup_id}/restore")
async def restore_data_backup(
    backup_id: str,
    request: DataRestoreRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """恢复数据备份"""
    try:
        # 验证备份ID匹配
        if request.backup_id != backup_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="备份ID不匹配"
            )
        
        service = DataBackupService(db)
        success = service.restore_backup(request)
        
        if success:
            return {"success": True, "message": "数据恢复成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="数据恢复失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复数据备份失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"恢复备份失败: {str(e)}"
        )

@router.delete("/backup/{backup_id}")
async def delete_data_backup(
    backup_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:edit"]))
):
    """删除数据备份"""
    try:
        service = DataBackupService(db)
        success = service.delete_backup(backup_id)
        
        if success:
            return {"success": True, "message": "备份删除成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="备份不存在或删除失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除数据备份失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除备份失败: {str(e)}"
        )
