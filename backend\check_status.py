#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查登录权限功能状态
"""

import sys
import os
import sqlite3

# 检查数据库配置
def check_db_configs():
    print("=== 检查数据库配置 ===")
    
    try:
        # 直接查询SQLite数据库
        db_path = "app.db"  # 根据实际数据库文件路径调整
        
        if not os.path.exists(db_path):
            # 尝试其他可能的路径
            possible_paths = ["./app.db", "../app.db", "../../app.db"]
            for path in possible_paths:
                if os.path.exists(path):
                    db_path = path
                    break
            else:
                print("❌ 找不到数据库文件")
                return
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询邮箱配置
        cursor.execute("SELECT id, app_name, is_active, corp_id FROM email_configs WHERE is_active = 1")
        configs = cursor.fetchall()
        
        print("活跃的邮箱配置:")
        for config in configs:
            print(f"- ID: {config[0]}, 应用: {config[1]}, Corp ID: {config[3]}")
            
        # 检查功能设置配置
        cursor.execute("SELECT id, corp_secret FROM email_configs WHERE app_name = '功能设置' AND is_active = 1")
        function_config = cursor.fetchone()
        
        if function_config:
            print(f"\n✅ 功能设置配置存在 (ID: {function_config[0]})")
            print(f"   Secret状态: {'已配置' if function_config[1] else '未配置'}")
        else:
            print("\n❌ 功能设置配置不存在或未激活")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {str(e)}")

def check_code_changes():
    print("\n=== 检查代码修改状态 ===")
    
    # 检查关键代码位置
    email_py_path = "app/api/v1/email.py"
    
    if not os.path.exists(email_py_path):
        print("❌ 找不到email.py文件")
        return
        
    with open(email_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修改点
    fixes = [
        ('get_member_login_permissions', 'app_name="功能设置"'),
        ('update_member_login_permissions', 'app_name="功能设置"'),
        ('sync_member_login_permissions', 'app_name="功能设置"'),
        ('create_member', 'function_api_service = TencentEmailAPIService(db, app_name="功能设置")'),
        ('sync_members_from_api', 'function_api_service = TencentEmailAPIService(db, app_name="功能设置"')
    ]
    
    for func_name, expected_code in fixes:
        if expected_code in content:
            print(f"✅ {func_name} - 已修复")
        else:
            print(f"❌ {func_name} - 可能未修复")

def analyze_log_message():
    print("\n=== 分析日志信息 ===")
    
    log_content = '''响应状态码: 200
响应内容: {"errcode":0,"errmsg":"ok","option":[{"type":1,"value":"0"},{"type":2,"value":"1"},{"type":3,"value":"1"},{"type":4,"value":"0"}]}'''
    
    print("根据您提供的日志:")
    print(log_content)
    print()
    print("分析结果:")
    print("✅ HTTP状态码: 200 (成功)")
    print("✅ API错误码: 0 (成功)")
    print("✅ API错误信息: 'ok' (成功)")
    print("✅ 返回了用户选项数据")
    print()
    print("用户选项解析:")
    print("- type 1 (强制安全登录): 禁用 (value=0)")
    print("- type 2 (IMAP/SMTP): 启用 (value=1)")
    print("- type 3 (POP/SMTP): 启用 (value=1)")
    print("- type 4 (安全登录): 禁用 (value=0)")
    print()
    print("🎉 结论: 登录权限管理功能已修复并正常工作!")

def check_possible_issues():
    print("\n=== 检查可能的问题 ===")
    
    print("如果您仍然遇到问题，可能的原因:")
    print("1. 浏览器缓存 - 建议清除浏览器缓存或使用隐私模式")
    print("2. 前端状态 - 前端可能需要重新加载")
    print("3. 权限显示 - API成功但前端显示可能有延迟")
    print("4. 其他API调用 - 可能还有其他地方使用了错误的配置")
    print()
    print("建议操作:")
    print("- 重新加载前端页面")
    print("- 检查浏览器开发者工具的网络请求")
    print("- 确认所有用户的登录权限都能正常查看和修改")

if __name__ == "__main__":
    print("=== 登录权限功能修复状态检查 ===\n")
    
    check_db_configs()
    check_code_changes()
    analyze_log_message()
    check_possible_issues()
    
    print("\n=== 检查完成 ===") 