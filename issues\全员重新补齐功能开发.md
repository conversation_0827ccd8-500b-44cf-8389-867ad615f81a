# 全员重新补齐功能开发文档

## 功能概述

为解决算法修复前已自动补齐的工号可能不准确的问题，开发全员重新补齐功能，支持智能分析和批量重新补齐操作。

## 功能特性

### 1. 重新补齐策略
- **全量重置** (`full_reset`): 清空所有工号重新匹配
- **智能重新补齐** (`smart_recompletion`): 只处理低可信度匹配  
- **选择性重新补齐** (`selective`): 手动选择特定记录

### 2. 安全机制
- 操作前数据备份（可选）
- 分批处理避免系统压力
- 详细操作日志记录
- 试运行模式预览结果
- 支持回滚操作

### 3. 智能分析
- 计算当前匹配可信度
- 识别需要重新补齐的候选者
- 提供推荐操作建议
- 详细的分析报告

## 技术实现

### 1. 数据模型扩展

#### RecompletionStrategy (重新补齐策略)
```python
class RecompletionStrategy(BaseModel):
    strategy_type: str  # 策略类型
    similarity_threshold: float = 0.8  # 相似度阈值
    backup_before_operation: bool = True  # 操作前备份
    batch_size: int = 50  # 批处理大小
```

#### RecompletionCandidate (重新补齐候选者)
```python
class RecompletionCandidate(BaseModel):
    email_member_id: int  # 邮箱成员ID
    email: str  # 邮箱地址
    name: str  # 成员姓名
    current_extid: str  # 当前工号
    current_match_confidence: float  # 当前匹配可信度
    reason_for_recompletion: str  # 重新补齐原因
    new_matches: List[NameMatchResult]  # 新的匹配结果
    recommended_action: str  # 推荐操作
```

### 2. 服务层实现

#### EmailExtidCompletionService 新增方法

1. **`_calculate_match_confidence`**: 计算当前匹配可信度
   - 姓名相似度计算
   - 在职状态加权 (+0.1)
   - 精确匹配加权 (+0.2)

2. **`analyze_recompletion_candidates`**: 分析重新补齐候选者
   - 获取所有已有工号的成员
   - 计算当前匹配可信度
   - 生成新的匹配结果
   - 判断是否需要重新补齐

3. **`execute_recompletion`**: 执行重新补齐操作
   - 数据备份（可选）
   - 分批处理候选者
   - 支持试运行模式
   - 详细结果统计

### 3. API端点设计

#### 分析重新补齐候选者
```
POST /api/v1/email-personnel-sync/extid-completion/analyze-recompletion
```

#### 执行重新补齐操作  
```
POST /api/v1/email-personnel-sync/extid-completion/execute-recompletion
```

## 使用流程

### 1. 智能重新补齐（推荐）

```python
# 1. 分析候选者
strategy = {
    "strategy_type": "smart_recompletion",
    "similarity_threshold": 0.8,
    "backup_before_operation": True,
    "batch_size": 50
}

analysis_result = await analyze_recompletion_candidates(strategy)

# 2. 预览结果（试运行）
request = {
    "strategy": strategy,
    "dry_run": True
}

preview_result = await execute_recompletion(request)

# 3. 正式执行
request["dry_run"] = False
final_result = await execute_recompletion(request)
```

### 2. 全量重置

```python
strategy = {
    "strategy_type": "full_reset",
    "backup_before_operation": True,
    "batch_size": 30  # 更小批次，更安全
}

# 必须先分析，再执行
analysis_result = await analyze_recompletion_candidates(strategy)
if analysis_result.candidates_for_recompletion > 0:
    result = await execute_recompletion(request)
```

### 3. 选择性重新补齐

```python
strategy = {
    "strategy_type": "selective"
}

request = {
    "strategy": strategy,
    "target_member_ids": [123, 456, 789],  # 指定成员ID
    "dry_run": False
}

result = await execute_recompletion(request)
```

## 可信度计算算法

```python
def _calculate_match_confidence(self, member, current_extid):
    # 基础相似度 (0-1)
    name_similarity = calculate_similarity(member.name, person.user_name)
    
    # 在职状态加权 (+0.1)
    status_weight = 0.1 if person.status in ACTIVE_STATUSES else 0.0
    
    # 精确匹配加权 (+0.2)  
    exact_match_weight = 0.2 if member.name == person.user_name else 0.0
    
    # 最终可信度
    confidence = name_similarity + status_weight + exact_match_weight
    return min(confidence, 1.0)
```

## 重新补齐判断逻辑

### 智能重新补齐策略
1. **低可信度匹配** (confidence < 0.7): 需要重新补齐
2. **无匹配人员**: 清除工号，需要手动处理  
3. **发现更优匹配**: 新匹配相似度比当前高0.1以上
4. **高可信度匹配**: 保持不变

### 推荐操作类型
- **keep**: 保持当前工号不变
- **update**: 更新为新的最佳匹配工号
- **manual**: 清除工号，需要手动审核

## 操作结果统计

```python
class RecompletionResult(BaseModel):
    total_processed: int  # 处理总数
    kept_unchanged: int  # 保持不变数量
    updated_extid: int  # 更新工号数量
    cleared_extid: int  # 清除工号数量  
    manual_review_required: int  # 需要手动审核数量
    errors: int  # 错误数量
    operation_duration: str  # 操作耗时
    backup_id: Optional[str]  # 备份ID
    detailed_results: List[Dict]  # 详细结果
    error_messages: List[str]  # 错误信息
```

## 安全保障

### 1. 数据备份
- 操作前自动备份关键数据
- 记录备份ID便于回滚
- 支持选择性备份

### 2. 批量处理
- 默认批次大小50个成员
- 避免长时间锁定数据库
- 支持自定义批次大小

### 3. 错误处理
- 单个成员处理失败不影响其他
- 详细错误日志记录
- 支持断点续传

### 4. 试运行模式
- 完整预览操作结果
- 不实际修改数据
- 便于评估影响范围

## 使用建议

### 1. 首次使用
1. 先用`smart_recompletion`策略分析
2. 使用`dry_run=true`预览结果
3. 确认无误后正式执行
4. 检查执行结果和错误信息

### 2. 定期维护
- 建议每月执行一次智能重新补齐
- 重点关注低可信度匹配
- 及时处理需要手动审核的成员

### 3. 紧急修复
- 使用选择性重新补齐处理特定问题
- 优先处理重要成员账号
- 保留详细的操作记录

## 兼容性说明

- 与现有工号补全功能完全兼容
- API接口保持向后兼容
- 不影响现有的手动匹配功能
- 支持现有的权限控制体系

## 性能考虑

- 大数据量建议分批执行
- 预加载人员信息减少数据库查询
- 合理设置相似度阈值平衡准确性和性能
- 异步处理避免阻塞其他操作

## 监控和日志

- 详细的操作日志记录
- 性能指标监控
- 错误率统计
- 成功率分析报告 