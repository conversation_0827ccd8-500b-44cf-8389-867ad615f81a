"""Add auto_create_departments to PersonnelSyncConfig

Revision ID: add_auto_create_departments
Revises: 732657688b92
Create Date: 2025-06-12 11:35:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_auto_create_departments'
down_revision: Union[str, None] = '52246107d9e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add auto_create_departments column to personnel_sync_config table
    op.add_column('personnel_sync_config', 
                  sa.Column('auto_create_departments', sa.<PERSON>(), 
                           nullable=True, default=True, comment='自动创建部门'))


def downgrade() -> None:
    # Remove auto_create_departments column from personnel_sync_config table
    op.drop_column('personnel_sync_config', 'auto_create_departments') 