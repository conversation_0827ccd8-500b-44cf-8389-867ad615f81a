from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_db, check_permissions
from app.crud.asset_settings import asset_settings_crud
from app.schemas.asset_settings import (
    AssetSettingsCreate,
    AssetSettingsUpdate,
    AssetSettingsResponse
)

router = APIRouter()

@router.post("/", response_model=AssetSettingsResponse)
def create_asset_settings(
    settings: AssetSettingsCreate,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:field:manage"]))
) -> AssetSettingsResponse:
    """创建资产设置"""
    return asset_settings_crud.create(db, obj_in=settings)

@router.get("/", response_model=List[AssetSettingsResponse])
def list_asset_settings(
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:view", "asset:field:manage"])),
    skip: int = 0,
    limit: int = 100,
) -> List[AssetSettingsResponse]:
    """获取资产设置列表"""
    return asset_settings_crud.get_multi(db, skip=skip, limit=limit)

@router.get("/{company}", response_model=AssetSettingsResponse)
def get_asset_settings(
    company: str,
    db: Session = Depends(get_db),
    current_user = Depends(check_permissions(["asset:view", "asset:field:manage"]))
) -> AssetSettingsResponse:
    """获取指定公司的资产设置"""
    settings = asset_settings_crud.get_by_company(db, company=company)
    if not settings:
        raise HTTPException(status_code=404, detail=f"公司 {company} 的设置不存在")
    return settings

@router.put("/{company}", response_model=AssetSettingsResponse)
def update_asset_settings(
    company: str,
    settings_update: AssetSettingsUpdate,
    db: Session = Depends(get_db),
) -> AssetSettingsResponse:
    """更新指定公司的资产设置"""
    settings = asset_settings_crud.get_by_company(db, company=company)
    if not settings:
        raise HTTPException(status_code=404, detail=f"公司 {company} 的设置不存在")
    return asset_settings_crud.update(db, db_obj=settings, obj_in=settings_update)

@router.delete("/{company}")
def delete_asset_settings(
    company: str,
    db: Session = Depends(get_db),
) -> dict:
    """删除指定公司的资产设置"""
    settings = asset_settings_crud.get_by_company(db, company=company)
    if not settings:
        raise HTTPException(status_code=404, detail=f"公司 {company} 的设置不存在")
    asset_settings_crud.remove(db, id=settings.id)
    return {"message": "设置删除成功"} 