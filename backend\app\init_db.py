from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models import Base, User
from app.utils import get_password_hash

def init_db():
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    # 检查是否已存在管理员账户
    admin = db.query(User).filter(User.username == "admin").first()
    if not admin:
        # 创建超级管理员账户
        hashed_password = get_password_hash("admin123")  # 默认密码
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            is_superuser=True
        )
        db.add(admin_user)
        db.commit()
        print("超级管理员账户创建成功！")
    else:
        print("超级管理员账户已存在。")
    
    db.close()

if __name__ == "__main__":
    print("正在初始化数据库...")
    init_db()
    print("数据库初始化完成！")
