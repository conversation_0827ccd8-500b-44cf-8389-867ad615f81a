#!/usr/bin/env python3
"""
OPS Platform 生产环境部署脚本
用于新环境的标准化数据库初始化，确保角色体系正确
"""

import sys
import logging
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def deploy_to_production():
    """部署到生产环境"""
    logger.info("🚀 开始OPS Platform生产环境部署")
    logger.info("=" * 60)
    
    try:
        # 1. 运行数据库迁移
        logger.info("📊 执行数据库迁移...")
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "alembic", "upgrade", "head"
        ], capture_output=True, text=True, cwd=current_dir)
        
        if result.returncode != 0:
            logger.error(f"数据库迁移失败: {result.stderr}")
            return False
        
        logger.info("✅ 数据库迁移完成")
        
        # 2. 初始化基础数据
        logger.info("📋 初始化基础数据...")
        from app.initial_data import init_db
        init_db()
        logger.info("✅ 基础数据初始化完成")
        
        # 3. 初始化命令白名单权限和数据
        logger.info("🔐 初始化命令白名单权限...")
        from scripts.init_command_whitelist import init_default_command_whitelist
        init_default_command_whitelist()
        logger.info("✅ 命令白名单初始化完成")
        
        # 4. 添加命令白名单权限
        logger.info("🛡️ 配置命令白名单权限...")
        from scripts.add_command_permissions import add_command_permissions
        add_command_permissions()
        logger.info("✅ 命令白名单权限配置完成")
        
        # 5. 验证部署结果
        logger.info("🔍 验证部署结果...")
        from app.database import SessionLocal
        from app.crud.role import role_crud
        from app.crud.user import user_crud
        
        db = SessionLocal()
        try:
            # 检查角色
            roles = role_crud.get_multi(db)
            logger.info(f"  👥 系统角色: {len(roles)}个")
            
            expected_roles = ["super_admin", "asset_admin", "normal_user"]
            existing_role_codes = [role.code for role in roles]
            
            for expected_role in expected_roles:
                if expected_role in existing_role_codes:
                    role = role_crud.get_by_code(db, code=expected_role)
                    perm_count = len(role.permissions) if role.permissions else 0
                    logger.info(f"    ✅ {role.name}: {perm_count}个权限")
                else:
                    logger.warning(f"    ⚠️ 缺少角色: {expected_role}")
            
            # 检查管理员用户
            admin_user = user_crud.get_by_username(db, username="admin")
            if admin_user:
                admin_roles = [role.name for role in admin_user.roles]
                logger.info(f"  👤 管理员用户: {', '.join(admin_roles)}")
                
                # 确保admin用户有super_admin角色
                has_super_admin = any(role.code == "super_admin" for role in admin_user.roles)
                if has_super_admin:
                    logger.info("  ✅ 管理员用户角色配置正确")
                else:
                    logger.error("  ❌ 管理员用户缺少super_admin角色")
                    return False
            else:
                logger.error("  ❌ 未找到管理员用户")
                return False
                
        finally:
            db.close()
        
        logger.info("=" * 60)
        logger.info("🎉 生产环境部署完成！")
        logger.info("✅ 系统已准备就绪")
        logger.info("🔑 默认管理员账号: admin / admin123")
        logger.info("📝 请及时修改默认密码")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 部署失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_environment():
    """检查部署环境"""
    logger.info("🔍 检查部署环境...")
    
    try:
        # 检查数据库连接
        from app.database import SessionLocal, engine
        from sqlalchemy import text
        
        db = SessionLocal()
        try:
            # 测试数据库连接
            result = db.execute(text("SELECT 1"))
            logger.info("  ✅ 数据库连接正常")
        finally:
            db.close()
        
        # 检查必要的目录
        required_dirs = ["app", "alembic", "logs"]
        for dir_name in required_dirs:
            dir_path = current_dir / dir_name
            if dir_path.exists():
                logger.info(f"  ✅ 目录存在: {dir_name}")
            else:
                logger.warning(f"  ⚠️ 目录不存在: {dir_name}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 环境检查失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 OPS Platform 生产环境部署工具")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，部署终止")
        sys.exit(1)
    
    # 确认部署
    confirm = input("\n是否继续执行生产环境部署？ (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("部署已取消")
        sys.exit(0)
    
    # 执行部署
    success = deploy_to_production()
    
    if success:
        print("\n✅ 部署成功！")
        print("🎉 OPS Platform 已准备就绪")
        print("🔗 请启动Web服务器开始使用")
    else:
        print("\n❌ 部署失败！")
        print("📝 请检查错误日志并修复问题")
        sys.exit(1) 