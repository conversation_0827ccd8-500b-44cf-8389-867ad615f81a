"""
同步性能优化
优化同步操作的性能，包括批量处理、缓存机制、异步处理等
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from dataclasses import dataclass

from app.database import get_db
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember, PersonnelSyncLog
from app.services.personnel_email_sync import PersonnelEmailSyncService
from app.schemas.email_personnel_sync import PersonnelSyncStats

# 配置日志
logging.basicConfig(level=logging.INFO, encoding='utf-8')
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    execution_time: float
    processed_count: int
    throughput: float  # 每秒处理数量
    memory_usage: Optional[float] = None
    cpu_usage: Optional[float] = None

class OptimizedPersonnelSyncService(PersonnelEmailSyncService):
    """优化的人员同步服务"""
    
    def __init__(self, db: Session, batch_size: int = 100, enable_cache: bool = True):
        super().__init__(db)
        self.batch_size = batch_size
        self.enable_cache = enable_cache
        self._cache = {} if enable_cache else None
        
    async def optimized_sync_personnel_to_email(
        self, 
        dry_run: bool = True,
        use_parallel: bool = True,
        batch_size: Optional[int] = None
    ) -> Tuple[PersonnelSyncStats, PerformanceMetrics]:
        """优化的人员同步方法"""
        start_time = time.time()
        batch_size = batch_size or self.batch_size
        
        logger.info(f"开始优化同步，批次大小: {batch_size}, 并行处理: {use_parallel}")
        
        # 获取所有需要处理的人员信息（使用优化的查询）
        personnel_list = self._get_personnel_optimized()
        
        # 获取所有邮箱成员（使用优化的查询）
        email_members = self._get_email_members_optimized()
        
        # 创建索引映射以提高查找效率
        email_member_map = {member.extid: member for member in email_members if member.extid}
        
        # 初始化统计信息
        stats = PersonnelSyncStats(
            processed_count=0,
            created_requests=0,
            updated_count=0,
            disabled_count=0,
            error_count=0,
            errors=[]
        )
        
        if use_parallel:
            # 并行处理
            stats = await self._process_personnel_parallel(
                personnel_list, email_member_map, batch_size, dry_run, stats
            )
        else:
            # 串行处理
            stats = await self._process_personnel_serial(
                personnel_list, email_member_map, batch_size, dry_run, stats
            )
        
        execution_time = time.time() - start_time
        throughput = stats.processed_count / execution_time if execution_time > 0 else 0
        
        metrics = PerformanceMetrics(
            operation_name="optimized_sync_personnel_to_email",
            execution_time=execution_time,
            processed_count=stats.processed_count,
            throughput=throughput
        )
        
        logger.info(f"优化同步完成，耗时: {execution_time:.3f}秒, "
                   f"处理数量: {stats.processed_count}, "
                   f"吞吐量: {throughput:.2f}条/秒")
        
        return stats, metrics
    
    def _get_personnel_optimized(self) -> List[EcologyUser]:
        """优化的人员信息查询"""
        # 使用索引优化的查询
        query = self.db.query(EcologyUser).filter(
            and_(
                EcologyUser.job_number.isnot(None),
                EcologyUser.job_number != ""
            )
        ).order_by(EcologyUser.job_number)
        
        return query.all()
    
    def _get_email_members_optimized(self) -> List[EmailMember]:
        """优化的邮箱成员查询"""
        # 使用索引优化的查询，只获取必要字段
        query = self.db.query(EmailMember).order_by(EmailMember.extid)
        
        return query.all()
    
    async def _process_personnel_parallel(
        self,
        personnel_list: List[EcologyUser],
        email_member_map: Dict[str, EmailMember],
        batch_size: int,
        dry_run: bool,
        stats: PersonnelSyncStats
    ) -> PersonnelSyncStats:
        """并行处理人员数据"""
        
        # 将人员列表分批
        batches = [
            personnel_list[i:i + batch_size] 
            for i in range(0, len(personnel_list), batch_size)
        ]
        
        # 使用线程池并行处理批次
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(
                    self._process_personnel_batch,
                    batch, email_member_map, dry_run
                )
                for batch in batches
            ]
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    batch_stats = future.result()
                    stats.processed_count += batch_stats.processed_count
                    stats.created_requests += batch_stats.created_requests
                    stats.updated_count += batch_stats.updated_count
                    stats.disabled_count += batch_stats.disabled_count
                    stats.error_count += batch_stats.error_count
                    stats.errors.extend(batch_stats.errors)
                except Exception as e:
                    logger.error(f"批次处理失败: {e}")
                    stats.error_count += 1
                    stats.errors.append(f"批次处理失败: {e}")
        
        return stats
    
    async def _process_personnel_serial(
        self,
        personnel_list: List[EcologyUser],
        email_member_map: Dict[str, EmailMember],
        batch_size: int,
        dry_run: bool,
        stats: PersonnelSyncStats
    ) -> PersonnelSyncStats:
        """串行处理人员数据"""
        
        # 分批处理
        for i in range(0, len(personnel_list), batch_size):
            batch = personnel_list[i:i + batch_size]
            
            try:
                batch_stats = self._process_personnel_batch(
                    batch, email_member_map, dry_run
                )
                
                stats.processed_count += batch_stats.processed_count
                stats.created_requests += batch_stats.created_requests
                stats.updated_count += batch_stats.updated_count
                stats.disabled_count += batch_stats.disabled_count
                stats.error_count += batch_stats.error_count
                stats.errors.extend(batch_stats.errors)
                
            except Exception as e:
                logger.error(f"批次处理失败: {e}")
                stats.error_count += 1
                stats.errors.append(f"批次处理失败: {e}")
        
        return stats
    
    def _process_personnel_batch(
        self,
        personnel_batch: List[EcologyUser],
        email_member_map: Dict[str, EmailMember],
        dry_run: bool
    ) -> PersonnelSyncStats:
        """处理单个批次的人员数据"""
        
        batch_stats = PersonnelSyncStats(
            processed_count=0,
            created_requests=0,
            updated_count=0,
            disabled_count=0,
            error_count=0,
            errors=[]
        )
        
        for person in personnel_batch:
            try:
                batch_stats.processed_count += 1
                
                # 检查是否存在对应的邮箱成员
                email_member = email_member_map.get(person.job_number)
                
                if email_member:
                    # 检查是否需要更新
                    if self._needs_update(person, email_member):
                        if not dry_run:
                            self._update_email_member(person, email_member)
                        batch_stats.updated_count += 1
                    
                    # 检查是否需要禁用
                    if person.status == "inactive" and email_member.is_active:
                        if not dry_run:
                            email_member.is_active = False
                        batch_stats.disabled_count += 1
                        
                else:
                    # 需要创建邮箱申请
                    if person.status == "active":
                        if not dry_run:
                            self._create_email_request(person)
                        batch_stats.created_requests += 1
                        
            except Exception as e:
                logger.error(f"处理人员 {person.job_number} 失败: {e}")
                batch_stats.error_count += 1
                batch_stats.errors.append(f"处理人员 {person.job_number} 失败: {e}")
        
        return batch_stats
    
    def _needs_update(self, person: EcologyUser, email_member: EmailMember) -> bool:
        """检查是否需要更新邮箱成员信息"""
        # 使用缓存避免重复计算
        cache_key = f"update_check_{person.job_number}_{email_member.id}"
        
        if self.enable_cache and cache_key in self._cache:
            return self._cache[cache_key]
        
        needs_update = (
            person.user_name != email_member.name or
            person.job_title_name != email_member.position or
            person.mobile != email_member.mobile
        )
        
        if self.enable_cache:
            self._cache[cache_key] = needs_update
        
        return needs_update
    
    def _update_email_member(self, person: EcologyUser, email_member: EmailMember):
        """更新邮箱成员信息"""
        email_member.name = person.user_name
        email_member.position = person.job_title_name
        email_member.mobile = person.mobile
        
        # 批量提交，而不是每次都提交
        # 实际提交在批次处理完成后进行
    
    def _create_email_request(self, person: EcologyUser):
        """创建邮箱申请"""
        # 这里应该创建邮箱申请记录
        # 实际实现中会调用相应的CRUD操作
        pass
    
    async def optimized_check_data_consistency(self) -> Tuple[Dict[str, Any], PerformanceMetrics]:
        """优化的数据一致性检查"""
        start_time = time.time()
        
        # 使用优化的SQL查询
        consistency_query = text("""
            SELECT 
                (SELECT COUNT(*) FROM ecology_users WHERE job_number IS NOT NULL AND job_number != '') as total_personnel,
                (SELECT COUNT(*) FROM email_members WHERE extid IS NOT NULL AND extid != '') as total_email_members,
                (SELECT COUNT(*) FROM ecology_users eu 
                 INNER JOIN email_members em ON eu.job_number = em.extid) as matched_count
        """)
        
        result = self.db.execute(consistency_query).fetchone()
        
        consistency_result = {
            'total_personnel': result.total_personnel,
            'total_email_members': result.total_email_members,
            'matched_count': result.matched_count,
            'unmatched_personnel': [],
            'unmatched_email_members': [],
            'inconsistent_data': []
        }
        
        execution_time = time.time() - start_time
        
        metrics = PerformanceMetrics(
            operation_name="optimized_check_data_consistency",
            execution_time=execution_time,
            processed_count=result.total_personnel + result.total_email_members,
            throughput=(result.total_personnel + result.total_email_members) / execution_time if execution_time > 0 else 0
        )
        
        logger.info(f"优化一致性检查完成，耗时: {execution_time:.3f}秒")
        
        return consistency_result, metrics

class SyncPerformanceOptimizer:
    """同步性能优化器"""
    
    @staticmethod
    def benchmark_sync_methods(db: Session, test_data_size: int = 100) -> Dict[str, PerformanceMetrics]:
        """基准测试不同的同步方法"""
        logger.info(f"开始基准测试，数据量: {test_data_size}")
        
        results = {}
        
        # 测试原始方法
        original_service = PersonnelEmailSyncService(db)
        start_time = time.time()
        original_result = asyncio.run(original_service.sync_personnel_to_email(dry_run=True))
        original_time = time.time() - start_time
        
        results['original'] = PerformanceMetrics(
            operation_name="original_sync",
            execution_time=original_time,
            processed_count=original_result.processed_count,
            throughput=original_result.processed_count / original_time if original_time > 0 else 0
        )
        
        # 测试优化方法（串行）
        optimized_service = OptimizedPersonnelSyncService(db, batch_size=50)
        optimized_result, optimized_metrics = asyncio.run(
            optimized_service.optimized_sync_personnel_to_email(dry_run=True, use_parallel=False)
        )
        results['optimized_serial'] = optimized_metrics
        
        # 测试优化方法（并行）
        parallel_result, parallel_metrics = asyncio.run(
            optimized_service.optimized_sync_personnel_to_email(dry_run=True, use_parallel=True)
        )
        results['optimized_parallel'] = parallel_metrics
        
        # 输出比较结果
        logger.info("=== 性能基准测试结果 ===")
        for method_name, metrics in results.items():
            logger.info(f"{method_name}: {metrics.execution_time:.3f}秒, "
                       f"吞吐量: {metrics.throughput:.2f}条/秒")
        
        return results
    
    @staticmethod
    def analyze_performance_bottlenecks(db: Session) -> Dict[str, Any]:
        """分析性能瓶颈"""
        logger.info("分析性能瓶颈...")
        
        bottlenecks = {}
        
        # 分析数据库查询性能
        start_time = time.time()
        personnel_count = db.query(EcologyUser).count()
        query_time = time.time() - start_time
        bottlenecks['personnel_query_time'] = query_time
        
        start_time = time.time()
        email_count = db.query(EmailMember).count()
        query_time = time.time() - start_time
        bottlenecks['email_query_time'] = query_time
        
        # 分析索引使用情况
        explain_query = text("EXPLAIN QUERY PLAN SELECT * FROM ecology_users WHERE job_number = 'TEST'")
        explain_result = db.execute(explain_query).fetchall()
        bottlenecks['personnel_index_usage'] = str(explain_result)
        
        explain_query = text("EXPLAIN QUERY PLAN SELECT * FROM email_members WHERE extid = 'TEST'")
        explain_result = db.execute(explain_query).fetchall()
        bottlenecks['email_index_usage'] = str(explain_result)
        
        logger.info("性能瓶颈分析完成")
        return bottlenecks

if __name__ == "__main__":
    # 运行性能优化测试
    db = next(get_db())
    try:
        optimizer = SyncPerformanceOptimizer()
        
        # 基准测试
        benchmark_results = optimizer.benchmark_sync_methods(db)
        
        # 瓶颈分析
        bottlenecks = optimizer.analyze_performance_bottlenecks(db)
        
        print("基准测试完成，详细结果请查看日志")
        
    finally:
        db.close()
