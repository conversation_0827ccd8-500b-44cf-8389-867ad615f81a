<template>
  <div class="mobile-dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <van-grid :border="false" :column-num="2" :gutter="16">
        <van-grid-item
          v-for="stat in stats"
          :key="stat.key"
          :text="stat.label"
          @click="handleStatClick(stat)"
        >
          <template #icon>
            <van-icon :name="stat.icon" size="24" :color="stat.color" />
          </template>
          <template #text>
            <div class="stat-item">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </template>
        </van-grid-item>
      </van-grid>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 统计数据
const stats = ref([
  {
    key: 'ad_users',
    label: 'AD用户',
    value: '1,234',
    icon: 'contact',
    color: '#409eff',
    path: '/m/ad'
  },
  {
    key: 'email_accounts',
    label: '邮箱账号',
    value: '856',
    icon: 'envelop-o',
    color: '#67c23a',
    path: '/m/email'
  },
  {
    key: 'assets',
    label: '资产设备',
    value: '2,108',
    icon: 'gift-card-o',
    color: '#e6a23c',
    path: '/m/asset'
  },
  {
    key: 'terminals',
    label: '在线终端',
    value: '128',
    icon: 'desktop-o',
    color: '#f56c6c',
    path: '/m/terminal'
  }
])



// 统计卡片点击处理
const handleStatClick = (stat: any) => {
  if (stat.path) {
    router.push(stat.path)
  }
}



// 加载数据
const loadData = async () => {
  try {
    // 这里可以调用API获取实际数据
    // const data = await getDashboardData()
    // stats.value = data.stats
    // recentActivities.value = data.activities
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    showToast('加载数据失败')
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
@use '@/mobile/styles/theme.scss';

.mobile-dashboard {
  padding: var(--theme-space-md);
  min-height: 100vh;
  background-color: var(--theme-bg-page);
  
  // 主题切换动画
  transition: background-color var(--theme-duration-base);
}

.stats-grid {
  margin-bottom: var(--theme-space-lg);
  
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .stat-value {
      font-size: var(--theme-font-xl);
      font-weight: bold;
      color: var(--theme-text-primary);
      margin-bottom: var(--theme-space-xs);
      transition: color var(--theme-duration-base);
    }
    
    .stat-label {
      font-size: var(--theme-font-sm);
      color: var(--theme-text-secondary);
      transition: color var(--theme-duration-base);
    }
  }
}

.van-cell-group {
  margin-bottom: var(--theme-space-lg);
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .mobile-dashboard {
    padding: var(--theme-space-sm);
  }
}

@media (min-width: 768px) {
  .mobile-dashboard {
    max-width: 600px;
    margin: 0 auto;
  }
}

// 紧凑模式适配
.compact-mode .mobile-dashboard {
  padding: var(--theme-space-sm);
  
  .stats-grid {
    margin-bottom: var(--theme-space-md);
  }
  
  .van-cell-group {
    margin-bottom: var(--theme-space-md);
  }
}
</style> 