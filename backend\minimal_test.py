#!/usr/bin/env python
"""
最小化测试 - 直接HTTP请求测试登录权限API
"""
import httpx
import asyncio
import json

async def test_login_permissions_endpoint():
    """测试登录权限端点"""
    print("=== 测试登录权限端点 ===")
    
    # 假设后端服务运行在localhost:8000
    base_url = "http://localhost:8000"
    
    # 测试参数
    userid = "<EMAIL>"
    endpoint_url = f"{base_url}/api/v1/email/members/{userid}/login-permissions"
    
    print(f"测试端点: {endpoint_url}")
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # 需要添加认证头 - 这里简化处理，实际需要有效的JWT token
            headers = {
                "Authorization": "Bearer your_jwt_token_here",  # 需要替换为实际token
                "Content-Type": "application/json"
            }
            
            print("发送GET请求...")
            response = await client.get(endpoint_url, headers=headers)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 500:
                print("\n❌ 确认存在500错误!")
                try:
                    error_data = response.json()
                    print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
                except:
                    print("响应不是有效的JSON格式")
            elif response.status_code == 401:
                print("❌ 认证失败 - 需要有效的JWT token")
            elif response.status_code == 200:
                print("✅ 请求成功")
                data = response.json()
                print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ 其他错误: {response.status_code}")
                
        except httpx.ConnectError:
            print("❌ 连接失败 - 后端服务可能没有运行")
            print("请确认后端服务已启动在localhost:8000")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")

# 测试服务状态
async def test_service_status():
    """测试服务状态"""
    print("\n=== 测试服务状态 ===")
    
    base_url = "http://localhost:8000"
    endpoints_to_test = [
        "/docs",  # FastAPI文档
        "/health", # 健康检查 (如果有)
        "/api/v1/email/configs"  # 邮箱配置端点
    ]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for endpoint in endpoints_to_test:
            url = f"{base_url}{endpoint}"
            try:
                print(f"测试: {url}")
                response = await client.get(url)
                print(f"  状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("  ✅ 可访问")
                elif response.status_code == 401:
                    print("  🔒 需要认证")
                else:
                    print(f"  ❌ 错误: {response.status_code}")
                    
            except httpx.ConnectError:
                print(f"  ❌ 连接失败")
                break
            except Exception as e:
                print(f"  ❌ 异常: {str(e)}")

# 简单的调试建议
def debug_suggestions():
    """调试建议"""
    print("\n=== 调试建议 ===")
    print("1. 确认后端服务状态:")
    print("   cd backend")
    print("   python -m uvicorn app.main:app --reload --port 8000")
    print("")
    print("2. 查看详细错误日志:")
    print("   - 检查uvicorn启动日志")
    print("   - 查看FastAPI的详细错误堆栈")
    print("   - 确认数据库连接正常")
    print("")
    print("3. 可能的问题原因:")
    print("   - 循环导入问题 (已发现)")
    print("   - 数据库连接问题")
    print("   - 依赖注入配置问题")
    print("   - 异步处理问题")
    print("")
    print("4. 建议的修复方法:")
    print("   - 重新组织import结构，避免循环导入")
    print("   - 添加更详细的错误日志记录")
    print("   - 检查每个依赖项的初始化")

if __name__ == "__main__":
    print("=== 最小化测试登录权限API ===")
    
    asyncio.run(test_service_status())
    asyncio.run(test_login_permissions_endpoint())
    debug_suggestions() 