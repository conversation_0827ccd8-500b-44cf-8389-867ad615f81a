<template>
  <div class="field-value-list">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><List /></el-icon>
        <h2 class="page-title">字段值管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>资产管理</el-breadcrumb-item>
        <el-breadcrumb-item>字段值管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <div class="toolbar">
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="字段名称">
              <el-select
                v-model="searchForm.field_name"
                placeholder="请选择字段名称"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="[key, label] in Object.entries(FIELD_NAME_LABELS)"
                  :key="key"
                  :label="label"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关键词">
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入关键词"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleAdd">新增字段值</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="fieldValues"
        border
        style="width: 100%"
        row-class-name="table-row"
        header-row-class-name="table-header"
        header-cell-class-name="table-header-cell"
      >
        <el-table-column prop="field_name" label="字段名称" min-width="120">
          <template #header>
            <div class="column-header">字段名称</div>
          </template>
          <template #default="{ row }">
            {{ FIELD_NAME_LABELS[row.field_name as FieldName] }}
          </template>
        </el-table-column>
        <el-table-column prop="field_value" label="字段值" min-width="150">
          <template #header>
            <div class="column-header">字段值</div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200">
          <template #header>
            <div class="column-header">描述</div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #header>
            <div class="column-header">创建时间</div>
          </template>
          <template #default="{ row }">
            {{ formatDateTime(row.created_at, 'datetime') }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #header>
            <div class="column-header">操作</div>
          </template>
          <template #default="{ row }">
            <el-dropdown trigger="click">
              <el-button type="primary" link>
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleEdit(row)">
                    <el-icon><Edit /></el-icon>编辑
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="confirmDelete(row)" style="color: #F56C6C;">
                    <el-icon><Delete /></el-icon>删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'create' ? '新增字段值' : '编辑字段值'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="formState"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="字段名称" prop="field_name">
          <el-select
            v-model="formState.field_name"
            placeholder="请选择字段名称"
            style="width: 100%"
          >
            <el-option
              v-for="[key, label] in Object.entries(FIELD_NAME_LABELS)"
              :key="key"
              :label="label"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字段值" prop="field_value">
          <el-input
            v-model="formState.field_value"
            placeholder="请输入字段值"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formState.description"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { fieldValueApi } from '@/api/field_value'
import type { FieldValue, FieldName } from '@/types/field_value'
import { FIELD_NAME_LABELS } from '@/types/field_value'
import { Edit, Delete, ArrowDown, List } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'

// 搜索表单
const searchForm = reactive({
  field_name: '',
  keyword: ''
})

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const loading = ref(false)
const fieldValues = ref<FieldValue[]>([])

// 表单
const formRef = ref<FormInstance>()
const formVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const formState = reactive({
  id: 0,
  field_name: '',
  field_value: '',
  description: ''
})
const formRules = {
  field_name: [{ required: true, message: '请选择字段名称' }],
  field_value: [{ required: true, message: '请输入字段值' }]
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await fieldValueApi.getFieldValues({
      field_name: searchForm.field_name,
      keyword: searchForm.keyword,
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    })
    fieldValues.value = response.data.data
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取字段值列表失败:', error)
    ElMessage.error('获取字段值列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.field_name = ''
  searchForm.keyword = ''
  handleSearch()
}

// 分页变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  fetchData()
}

const handleCurrentChange = (val: number) => {
  pagination.current = val
  fetchData()
}

// 新增
const handleAdd = () => {
  formMode.value = 'create'
  formState.id = 0
  formState.field_name = ''
  formState.field_value = ''
  formState.description = ''
  formVisible.value = true
}

// 编辑
const handleEdit = (row: FieldValue) => {
  formMode.value = 'edit'
  formState.id = row.id
  formState.field_name = row.field_name
  formState.field_value = row.field_value
  formState.description = row.description || ''
  formVisible.value = true
}

// 删除
const handleDelete = async (row: FieldValue) => {
  try {
    await fieldValueApi.deleteFieldValue(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    console.error('删除字段值失败:', error)
    ElMessage.error('删除失败')
  }
}

const confirmDelete = (row: FieldValue) => {
  ElMessageBox.confirm('确定要删除该字段值吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    handleDelete(row)
  }).catch(() => {
    // 用户取消操作
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      await fieldValueApi.createFieldValue({
        field_name: formState.field_name,
        field_value: formState.field_value,
        description: formState.description
      })
      ElMessage.success('创建成功')
    } else {
      await fieldValueApi.updateFieldValue(formState.id, {
        field_name: formState.field_name,
        field_value: formState.field_value,
        description: formState.description
      })
      ElMessage.success('更新成功')
    }
    
    formVisible.value = false
    fetchData()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('提交表单失败')
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.field-value-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-form {
  flex: 1;
}

.action-buttons {
  margin-left: 16px;
  display: flex;
  gap: 8px;
}

/* 表格样式统一 */
.table-header {
  background-color: var(--el-fill-color-light) !important;
}

.table-header-cell {
  background-color: var(--el-fill-color-light) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: bold !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 