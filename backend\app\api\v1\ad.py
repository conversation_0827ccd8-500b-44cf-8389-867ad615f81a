from fastapi import APIRouter, Depends, HTTPException, status, Query, File, Form, UploadFile, Body
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import logging
from ... import models, schemas
from ...database import get_db
from ...utils import get_current_user
from ...api.deps import check_permissions
from ...services import ad as ad_service
from ...utils.ad_client import ad_client
from pydantic import BaseModel, ValidationError
from urllib.parse import unquote, quote
from fastapi.responses import StreamingResponse
import csv
import io
from io import StringIO, BytesIO
import pandas as pd
import urllib.parse
from datetime import datetime

# 添加logger
logger = logging.getLogger(__name__)

router = APIRouter()

# 添加请求体模型
class GroupMembersRequest(BaseModel):
    members: List[str]

@router.get("/ou", response_model=List[schemas.OUResponse])
async def get_ou_tree_route(
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取OU树结构"""
    return await ad_service.get_ou_tree()

@router.post("/ou", response_model=schemas.OUResponse)
async def create_ou_route(
    ou: schemas.OUCreate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """创建新的OU"""
    return await ad_service.create_ou(ou)

@router.put("/ou/{ou_dn}", response_model=schemas.OUResponse)
async def update_ou_route(
    ou_dn: str,
    ou_data: schemas.OUUpdate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """更新OU"""
    # 解码DN
    decoded_dn = unquote(ou_dn)
    return await ad_service.update_ou(decoded_dn, ou_data.dict())

@router.delete("/ou/{ou_dn}")
async def delete_ou_route(
    ou_dn: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """删除OU"""
    # 解码两次编码的DN
    decoded_dn = unquote(unquote(ou_dn))
    return await ad_service.delete_ou(decoded_dn)

@router.get("/users")
async def get_users(
    ou_dn: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: str = None,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取用户列表(分页)"""
    return await ad_service.get_users(ou_dn, page, page_size, search)

@router.post("/users")
async def create_user_route(
    user: schemas.ADUserCreate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    return await ad_service.create_user(user)

@router.put("/users/{username}", response_model=schemas.ADUserResponse)
async def update_user(
    username: str,
    user_update: schemas.ADUserUpdate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """更新AD用户信息"""
    try:
        # 连接AD
        if not await ad_client.connect():
            logger.error("无法连接到AD服务器")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="无法连接到AD服务器"
            )

        # 获取现有用户信息
        existing_user = await ad_client.get_user(username)
        if not existing_user:
            logger.error(f"用户不存在: {username}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 准备更新的属性
        update_attrs = {}
        if user_update.name is not None:
            update_attrs['name'] = user_update.name
        if user_update.email is not None:
            update_attrs['email'] = user_update.email
        if user_update.department is not None:
            update_attrs['department'] = user_update.department
        if user_update.title is not None:
            update_attrs['title'] = user_update.title
        if user_update.password is not None:
            update_attrs['password'] = user_update.password

        # 更新用户
        try:
            await ad_client.update_user(username, update_attrs)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

        # 获取更新后的用户信息
        updated_user = await ad_client.get_user(username)
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法获取更新后的用户信息"
            )

        return updated_user

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

class ToggleUserStatusRequest(BaseModel):
    force_enable: Optional[bool] = None

@router.post("/users/{username}/toggle")
async def toggle_user_status_route(
    username: str,
    request_data: ToggleUserStatusRequest = Body(default=None),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """切换用户启用/禁用状态

    如果提供了force_enable参数，则强制设置为指定状态，否则切换当前状态
    """
    try:
        # 检查用户名是否有效
        if not username or username.strip() == "":
            logger.error("无效的用户名")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的用户名"
            )

        # 检查是否是特殊用户
        protected_users = ['Administrator', 'Guest', 'krbtgt', 'admin']
        if username.lower() in [u.lower() for u in protected_users]:
            logger.error(f"尝试修改受保护的系统账户: {username}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无法修改受保护的系统账户"
            )

        # 连接AD
        if not await ad_client.connect():
            logger.error("无法连接到AD服务器")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="无法连接到AD服务器"
            )

        force_enable = None
        if request_data:
            force_enable = request_data.force_enable

        # 切换用户状态
        success = await ad_client.toggle_user_status(username, force_enable)
        if not success:
            # 检查用户是否存在
            user_exists = await ad_client.get_user(username)
            if not user_exists:
                logger.error(f"用户不存在: {username}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"用户 {username} 不存在"
                )

            # 如果用户存在但操作失败，可能是权限问题或系统限制
            logger.error(f"切换用户状态失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"切换用户 {username} 状态失败，可能是系统保护的账户或权限不足"
            )

        # 获取更新后的用户信息
        updated_user = await ad_client.get_user(username)
        if not updated_user:
            logger.error(f"获取更新后的用户信息失败: {username}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="获取更新后的用户信息失败"
            )

        return updated_user

    except Exception as e:
        logger.error(f"切换用户状态时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/test/users/{ou_dn}", response_model=dict)
async def test_get_users(
    ou_dn: str,
    current_user: models.User = Depends(get_current_user)
):
    """测试获取指定OU下的用表"""
    try:
        # 测试连接
        if not await ad_client.connect():
            return {
                "success": False,
                "message": "AD连接失败",
                "details": None
            }

        # 测试搜索
        conn_details = {
            "bound": ad_client._conn.bound if ad_client._conn else False,
            "server": ad_client._config.get('server'),
            "username": ad_client._config.get('username'),
            "search_base": ou_dn
        }

        # 执行搜索
        search_result = await ad_client.get_users(ou_dn)

        return {
            "success": True,
            "connection": conn_details,
            "search_result": {
                "total_users": len(search_result) if search_result else 0,
                "users": search_result,
                "raw_result": str(ad_client._conn.result) if ad_client._conn else None
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "details": {
                "type": type(e).__name__,
                "args": e.args
            }
        }

@router.get("/test/groups/{ou_dn}", response_model=dict)
async def test_get_groups(
    ou_dn: str,
    current_user: models.User = Depends(get_current_user)
):
    """测试获取指定OU下的组列表"""
    try:
        # 测试连接
        if not await ad_client.connect():
            return {
                "success": False,
                "message": "AD连接失败",
                "details": None
            }

        # 测试搜索
        conn_details = {
            "bound": ad_client._conn.bound if ad_client._conn else False,
            "server": ad_client._config.get('server'),
            "username": ad_client._config.get('username'),
            "search_base": ou_dn
        }

        # 执行搜索
        search_result = await ad_client.get_groups(ou_dn)

        return {
            "success": True,
            "connection": conn_details,
            "search_result": {
                "total_groups": len(search_result) if search_result else 0,
                "groups": search_result,
                "raw_result": str(ad_client._conn.result) if ad_client._conn else None
            }
        }
    except Exception as e:
        return {
            "success": False,
            "message": str(e),
            "details": {
                "type": type(e).__name__,
                "args": e.args
            }
        }

@router.get("/groups")
async def get_groups(
    ou_dn: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: str = None,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取组列表(分页)"""
    return await ad_service.get_groups(ou_dn, page, page_size, search)

@router.post("/groups")
async def create_group_route(
    group: schemas.ADGroupCreate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    return await ad_service.create_group(group)

@router.put("/groups/{name}")
async def update_group_route(
    name: str,
    group_update: schemas.ADGroupUpdate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    return await ad_service.update_group(name, group_update)

@router.delete("/groups/{name}")
async def delete_group_route(
    name: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    return await ad_service.delete_group(name)

@router.get("/users/{username}/groups")
async def get_user_groups_route(
    username: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取用户所属的组"""
    return await ad_service.get_user_groups(username)

@router.put("/users/{username}/groups")
async def update_user_groups_route(
    username: str,
    groups: List[str],
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """更新用户所属的组"""
    return await ad_service.update_user_groups(username, groups)

@router.delete("/users/{username}")
async def delete_user(
    username: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """删除AD用户"""
    try:
        success = await ad_client.delete_user(username)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除用户失败"
            )
        return {"message": "删除成功"}
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/groups/all/{ou_dn}", response_model=List[Dict])
async def get_all_groups(
    ou_dn: str = None,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取所有组列表(不分页)"""
    try:
        groups = await ad_service.get_all_groups(None)
        return groups
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/groups/{name}/members")
async def get_group_members_route(
    name: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: str = None,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """获取组成员列表(分页)

    - name: 组名称
    - page: 页码
    - page_size: 每页数量
    - search: 搜索关键词，支持按用户名、显示名称、部门、职位搜索
    """
    return await ad_service.get_group_members(name, page, page_size, search)

@router.post("/groups/{name}/members")
async def add_group_members_route(
    name: str,
    request: GroupMembersRequest,  # 使用Pydantic模型接收请求体
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    """添加组成员"""
    return await ad_service.add_group_members(name, request.members)

@router.delete("/groups/{name}/members")
async def remove_group_members_route(
    name: str,
    request: GroupMembersRequest,  # 同样使用Pydantic模型
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    """删除组成员"""
    return await ad_service.remove_group_members(name, request.members)

@router.get("/groups/{name}/members/export")
async def export_group_members_route(
    name: str,
    format: str = Query("xlsx", regex="^(xlsx|csv)$"),
    search: str = None,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """导出组成员列表

    - name: 组名称
    - format: 导出格式，支持xlsx和csv
    - search: 搜索关键词，支持按用户名、显示名称、部门、职位搜索
    """
    try:
        # 获取所有组成员（不分页）
        members, total = await ad_client.get_group_members(name, page=1, page_size=10000, search=search)

        if not members:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到组成员数据"
            )

        # 准备导出数据
        export_data = []
        for member in members:
            export_data.append({
                "username": member.get("username", ""),
                "name": member.get("name", ""),
                "email": member.get("email", ""),
                "department": member.get("department", ""),
                "title": member.get("title", ""),
                "enabled": "启用" if member.get("enabled") else "禁用"
            })

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 使用英文文件名避免编码问题
        filename_base = f"group_members_{timestamp}"

        # 根据格式返回不同的响应
        if format == "csv":
            # 创建CSV文件
            output = StringIO()
            fieldnames = ["username", "name", "email", "department", "title", "enabled"]
            writer = csv.DictWriter(
                output,
                fieldnames=fieldnames,
                extrasaction='ignore'
            )

            # 写入CSV头（使用中文表头）
            header_mapping = {
                "username": "用户名",
                "name": "姓名",
                "email": "邮箱",
                "department": "部门",
                "title": "职位",
                "enabled": "状态"
            }
            writer.writerow(header_mapping)

            # 写入数据
            for row in export_data:
                writer.writerow(row)

            # 将指针移到开始
            output.seek(0)

            # 添加BOM头以支持Excel正确显示中文
            response_content = '\ufeff' + output.getvalue()

            # 返回StreamingResponse
            # 使用 URL 编码处理文件名
            import urllib.parse
            encoded_filename = urllib.parse.quote(f"{filename_base}.csv")
            return StreamingResponse(
                iter([response_content]),
                media_type="text/csv",
                headers={
                    "Content-Disposition": f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}',
                    "Content-Type": "text/csv; charset=utf-8-sig"
                }
            )
        else:  # xlsx format
            # 使用pandas创建Excel文件
            import pandas as pd
            import io
            import urllib.parse

            # 准备数据并添加中文列名
            df_data = []
            for item in export_data:
                df_data.append({
                    "用户名": item["username"],
                    "姓名": item["name"],
                    "邮箱": item["email"],
                    "部门": item["department"],
                    "职位": item["title"],
                    "状态": item["enabled"]
                })

            # 创建DataFrame
            df = pd.DataFrame(df_data)

            # 创建ByteIO对象
            output = io.BytesIO()

            # 将DataFrame保存为Excel
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='组成员', index=False)

                # 获取工作表
                worksheet = writer.sheets['组成员']

                # 设置列宽
                for i, col in enumerate(df.columns):
                    column_width = max(df[col].astype(str).map(len).max(), len(col) + 2)
                    # openpyxl的列宽设置方式不同
                    worksheet.column_dimensions[chr(65 + i)].width = column_width

            # 将指针移到开始
            output.seek(0)

            # 返回StreamingResponse
            # 使用 URL 编码处理文件名
            import urllib.parse
            encoded_filename = urllib.parse.quote(f"{filename_base}.xlsx")
            return StreamingResponse(
                output,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f'attachment; filename="{encoded_filename}"; filename*=UTF-8\'\'{encoded_filename}'
                }
            )

    except Exception as e:
        logger.error(f"导出组成员数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出失败: {str(e)}"
        )

@router.get("/users/search")
async def search_users(
    search: str = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """全局搜索用户"""
    try:
        users = await ad_service.search_users(search, page, page_size)
        return users
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/ou")
async def create_ou(
    ou: schemas.OUCreate,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """创建组织单位"""
    return await ad_service.create_ou(ou)

@router.post("/users/bulk_import", response_model=dict)
async def bulk_import_users(
    file: UploadFile = File(...),
    ou_dn: str = Form(...),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """批量导入用户"""
    try:
        # 读取并解析文件
        content = await file.read()

        # 根据文件类型选择解析方法
        if file.filename.endswith('.csv'):
            users = await ad_service.parse_csv_users(content)
        elif file.filename.endswith(('.xls', '.xlsx')):
            users = await ad_service.parse_excel_users(content)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型，仅支持CSV和Excel文件"
            )

        # 验证每个用户数据
        validated_users = []
        for user in users:
            try:
                # 使用 ADUserImport schema 验证数据
                validated_user = schemas.ADUserImport(**user)
                validated_users.append(validated_user.model_dump())
            except ValidationError as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"用户数据验证失败: {str(e)}"
                )

        # 执行批量导入，使用当前选中的 OU 作为默认值
        result = await ad_service.bulk_import_users(ou_dn, validated_users)

        return result

    except Exception as e:
        logger.error(f"批量导入用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/users/import_template/{file_type}")
async def get_import_template(
    file_type: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """获取用户导入模板"""
    try:
        if file_type not in ['csv', 'excel']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型，仅支持CSV和Excel文件"
            )

        return await ad_service.get_import_template(file_type)

    except Exception as e:
        logger.error(f"获取导入模板失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/users/export/{ou_dn}")
async def export_users(
    ou_dn: str,
    format: str = Query("json", regex="^(json|csv)$"),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """导出指定OU下的所有用户信息"""
    try:
        # 获取所有组的映射关系
        all_groups = await ad_client.get_all_groups(None)
        dn_to_group_name = {group['dn']: group['name'] for group in all_groups}

        # 分批处理参数
        batch_size = 500
        current_page = 1
        export_data = []
        total_processed = 0

        while True:
            # 使用只读模式分批获取用户数据
            users, total = await ad_client.get_users(
                ou_dn,
                page=current_page,
                page_size=batch_size,
                read_only=True
            )

            if not users:
                if current_page == 1:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="未找到用户数据"
                    )
                break

            # 处理当前批次的用户数据
            for user in users:
                # 获取用户的组DN
                user_group_dns = await ad_client.get_user_groups(user.get("username"))
                # 转换DN为组名
                group_names = [dn_to_group_name.get(dn, '') for dn in user_group_dns if dn in dn_to_group_name]
                # 过滤掉空字符
                group_names = [name for name in group_names if name]

                # 处理密码过期信息
                password_expiry_status = "永不过期"
                if user.get("password_never_expires"):
                    password_expiry_status = "永不过期"
                elif user.get("password_expired"):
                    password_expiry_status = "已过期"
                elif user.get("days_until_expiry") is not None:
                    days = user.get("days_until_expiry")
                    if days < 0:
                        password_expiry_status = "已过期"
                    elif days <= 7:
                        password_expiry_status = f"即将过期({days}天)"
                    else:
                        password_expiry_status = f"{days}天后过期"

                export_data.append({
                    "username": user.get("username"),
                    "name": user.get("name"),
                    "email": user.get("email"),
                    "department": user.get("department"),
                    "title": user.get("title"),
                    "groups": ";".join(group_names),
                    "enabled": "启用" if user.get("enabled") else "禁用",
                    "password_expiry_date": user.get("password_expiry_date", ""),
                    "password_expiry_status": password_expiry_status,
                    "days_until_expiry": user.get("days_until_expiry", "")
                })

            total_processed += len(users)
            if total_processed >= total:
                break

            current_page += 1

        # 根据格式返回不同的响应
        if format == "json":
            return {
                "success": True,
                "data": export_data,
                "total": total_processed
            }
        else:  # CSV format
            # 创建CSV文件
            output = StringIO()
            fieldnames = ["username", "name", "email", "department", "title", "groups", "enabled", 
                         "password_expiry_date", "password_expiry_status", "days_until_expiry"]
            writer = csv.DictWriter(
                output,
                fieldnames=fieldnames,
                extrasaction='ignore'
            )

            # 写入CSV头（使用中文表头）
            header_mapping = {
                "username": "用户名",
                "name": "姓名",
                "email": "邮箱",
                "department": "部门",
                "title": "职位",
                "groups": "所属组",
                "enabled": "状态",
                "password_expiry_date": "密码过期日期",
                "password_expiry_status": "密码过期状态",
                "days_until_expiry": "剩余天数"
            }
            writer.writerow(header_mapping)

            # 分批写入数据
            for row in export_data:
                writer.writerow(row)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ad_users_export_{timestamp}.csv"

            # 将指针移到开始
            output.seek(0)

            # 添加BOM头以支持Excel正确显示中文
            response_content = '\ufeff' + output.getvalue()

            # 返回StreamingResponse
            return StreamingResponse(
                iter([response_content]),
                media_type="text/csv",
                headers={
                    "Content-Disposition": f'attachment; filename="{filename}"',
                    "Content-Type": "text/csv; charset=utf-8-sig"
                }
            )

    except Exception as e:
        logger.error(f"导出用户数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出失败: {str(e)}"
        )

@router.post("/users/{username}/move")
async def move_user(
    username: str,
    target_ou_dn: str = Body(..., embed=True),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """移动用户到指定OU"""
    return await ad_service.move_user(username, target_ou_dn)

@router.post("/ou/{ou_dn}/move")
async def move_ou(
    ou_dn: str,
    target_ou_dn: str = Body(..., embed=True),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """移动OU到指定目标OU"""
    # 解码DN
    decoded_dn = unquote(unquote(ou_dn))
    return await ad_service.move_ou(decoded_dn, target_ou_dn)

@router.get("/metrics", response_model=dict)
async def get_ad_metrics(
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:view"]))
):
    """取AD连接性能指标"""
    try:
        metrics = await ad_client.get_metrics()
        return {
            "success": True,
            "metrics": metrics
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@router.post("/users/{username}/fix-cn")
async def fix_user_cn(
    username: str,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """修复用户的CN"""
    try:
        success = await ad_client.fix_user_cn(username)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="修复用户CN失败"
            )
        return {"message": "CN修复成功"}
    except Exception as e:
        logger.error(f"修复用户CN时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/users/fix-all-cn")
async def fix_all_users_cn(
    ou_dn: str = Body(None),
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """批量修复用户的CN"""
    try:
        results = await ad_client.fix_all_users_cn(ou_dn)
        return {
            "message": "批量修复完成",
            "results": results
        }
    except Exception as e:
        logger.error(f"批量修复用户CN时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/sync-from-personnel", response_model=Dict)
async def sync_from_personnel(
    sync_data: schemas.ADSyncFromPersonnel,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """从基础信息-人员信息同步人员信息到AD域"""
    return await ad_service.sync_from_personnel(sync_data)

@router.post("/sync-from-personnel2", response_model=Dict)
async def sync_from_personnel2(
    sync_data: schemas.ADSyncFromPersonnel,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:user:manage"]))
):
    """从基础信息-人员信息同步人员信息到AD域(备用路由)"""
    return await ad_service.sync_from_personnel(sync_data)

@router.post("/sync-organization-structure", response_model=Dict)
async def sync_organization_structure_route(
    data: schemas.ADSyncOrganizationStructure,
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:ou:manage"]))
):
    """
    从泛微系统同步组织结构到AD
    不涉及用户创建，只创建组织单位
    """
    logger.info(f"用户 {current_user.username} 请求从泛微系统同步组织结构到AD")

    try:
        result = await ad_service.sync_organization_structure(
            data.parent_ou_dn,
            company_id=data.company_id,
            dept_id=data.dept_id
        )
        return result
    except Exception as e:
        logger.error(f"同步组织结构失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步组织结构失败: {str(e)}"
        )

@router.post("/update-department-groups", response_model=Dict)
async def update_department_groups(
    departments: List[dict],
    current_user: models.User = Depends(get_current_user),
    _permissions = Depends(check_permissions(["ad:group:manage"]))
):
    """更新部门对应的安全组信息

    当部门信息（如名称、上级部门等）发生变化时，调用此API更新对应的安全组。
    """
    return await ad_service.handle_department_changes(departments)