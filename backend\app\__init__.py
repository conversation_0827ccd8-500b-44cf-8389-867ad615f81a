# -*- coding: utf-8 -*-
"""
OPS Platform Application Module

在所有其他模块加载之前设置系统编码，确保中文字符正确显示
"""

import sys
import locale
import os

# 在Windows系统上设置UTF-8编码
if sys.platform.startswith('win'):
    import codecs
    
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 重新配置标准输出流
    if hasattr(sys.stdout, 'buffer'):
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    if hasattr(sys.stderr, 'buffer'):
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
    
    # 尝试设置区域设置
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except locale.Error:
            # 如果设置失败，使用系统默认
            pass

# 设置默认编码（Python 3.x中通常已经是UTF-8）
if hasattr(sys, 'setdefaultencoding'):
    sys.setdefaultencoding('utf-8') 