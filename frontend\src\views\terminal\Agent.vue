<template>
  <div class="agent-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Setting /></el-icon>
        <h2 class="page-title">Agent管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>终端管理</el-breadcrumb-item>
        <el-breadcrumb-item>Agent管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 选项卡布局 -->
    <el-card class="box-card" shadow="hover">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 版本管理选项卡 -->
        <el-tab-pane label="版本管理" name="versions">
          <div class="toolbar">
            <div class="search-form">
              <span class="section-title">Agent版本列表</span>
            </div>
            <div class="action-buttons">
              <Authority permission="terminal:agent:manage">
                <el-button type="primary" @click="showUploadDialog">
                  <el-icon><Upload /></el-icon> 上传新版本
                </el-button>
              </Authority>
            </div>
          </div>

          <!-- 版本列表 -->
          <el-table
            v-loading="loading"
            :data="versionList"
            border
            style="width: 100%"
            row-class-name="agent-table-row"
            header-row-class-name="agent-table-header"
            header-cell-class-name="table-header-cell"
          >
            <el-table-column prop="version" label="版本号" min-width="120">
              <template #header>
                <div class="column-header">版本号</div>
              </template>
              <template #default="scope">
                <el-tag size="small" effect="plain">{{ scope.row.version }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="platform" label="平台" min-width="100">
              <template #header>
                <div class="column-header">平台</div>
              </template>
            </el-table-column>
            <el-table-column prop="file_name" label="文件名" min-width="150" show-overflow-tooltip>
              <template #header>
                <div class="column-header">文件名</div>
              </template>
            </el-table-column>
            <el-table-column prop="file_size" label="文件大小" min-width="120">
              <template #header>
                <div class="column-header">文件大小</div>
              </template>
              <template #default="{ row }">
                <el-tag size="small" type="info" effect="plain">{{ formatSize(row.file_size) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="upload_time" label="上传时间" min-width="170">
              <template #header>
                <div class="column-header">上传时间</div>
              </template>
              <template #default="{ row }">
                {{ formatTime(row.upload_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="is_current" label="当前版本" width="100">
              <template #header>
                <div class="column-header">当前版本</div>
              </template>
              <template #default="{ row }">
                <el-tag v-if="row.is_current" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="120">
              <template #header>
                <div class="column-header">操作</div>
              </template>
              <template #default="{ row }">
                <Authority permission="terminal:agent:manage">
                  <el-dropdown trigger="click">
                    <el-button type="primary" link>
                      操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          :disabled="row.is_current"
                          @click="setAsCurrent(row)"
                        >
                          <el-icon><Check /></el-icon>设为当前版本
                        </el-dropdown-item>
                        <el-dropdown-item
                          :disabled="!row.is_current"
                          @click="manualUpgrade(row)"
                        >
                          <el-icon><Upload /></el-icon>手动升级
                        </el-dropdown-item>
                        <el-dropdown-item
                          :disabled="row.is_current"
                          @click="confirmDelete(row)"
                          class="text-danger"
                        >
                          <el-icon><Delete /></el-icon>删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </Authority>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 升级监控选项卡 -->
        <el-tab-pane label="升级监控" name="monitoring">
          <div class="monitoring-container">
            <!-- 统计概览 -->
            <div class="stats-overview" v-loading="statsLoading">
              <el-row :gutter="20">
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-icon total">
                      <el-icon><TrendCharts /></el-icon>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ upgradeStats.total_upgrades || 0 }}</div>
                      <div class="stat-label">总升级次数</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-icon success">
                      <el-icon><CircleCheckFilled /></el-icon>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ upgradeStats.success_rate || 0 }}%</div>
                      <div class="stat-label">成功率</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-icon warning">
                      <el-icon><Loading /></el-icon>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ upgradeStats.active_upgrades || 0 }}</div>
                      <div class="stat-label">进行中</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="stat-item">
                    <div class="stat-icon danger">
                      <el-icon><CircleCloseFilled /></el-icon>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ upgradeStats.status_distribution?.failed || 0 }}</div>
                      <div class="stat-label">失败次数</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 筛选工具栏 -->
            <div class="monitoring-toolbar">
              <div class="filter-section">
                <el-select
                  v-model="monitoringFilters.status"
                  placeholder="状态筛选"
                  clearable
                  size="default"
                  style="width: 140px"
                  @change="fetchUpgradeStatus"
                >
                  <el-option label="等待中" value="pending" />
                  <el-option label="下载中" value="downloading" />
                  <el-option label="安装中" value="installing" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="失败" value="failed" />
                  <el-option label="已回滚" value="rolled_back" />
                </el-select>
                
                <el-input
                  v-model="monitoringFilters.terminalId"
                  placeholder="终端ID筛选"
                  size="default"
                  style="width: 140px"
                  @blur="fetchUpgradeStatus"
                />
                
                <div class="button-group">
                  <el-button type="primary" size="default" @click="fetchUpgradeStatus">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  
                  <el-button type="primary" size="default" @click="fetchUpgradeStats">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </div>
              </div>
              
              <div class="view-toggle">
                <el-radio-group v-model="currentView" @change="handleViewChange">
                  <el-radio-button label="status">实时状态</el-radio-button>
                  <el-radio-button label="history">升级历史</el-radio-button>
                </el-radio-group>
              </div>
            </div>

            <!-- 实时状态视图 -->
            <div v-if="currentView === 'status'" class="status-view">
              <el-table
                v-loading="statusLoading"
                :data="upgradeStatusList"
                border
                style="width: 100%"
              >
                <el-table-column prop="command_id" label="命令ID" width="100" />
                <el-table-column prop="terminal_id" label="终端ID" width="80" />
                <el-table-column prop="current_version" label="当前版本" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="info">{{ row.current_version }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="target_version" label="目标版本" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="primary">{{ row.target_version }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="progress" label="进度" width="150">
                  <template #default="{ row }">
                    <div class="progress-container">
                      <el-progress 
                        :percentage="row.progress >= 0 ? row.progress : 0" 
                        :status="row.progress < 0 ? 'exception' : undefined"
                        :stroke-width="8"
                        size="small"
                        :show-text="true"
                      />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="started_at" label="开始时间" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.started_at) }}
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="耗时" width="80">
                  <template #default="{ row }">
                    {{ row.duration ? formatDuration(row.duration) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="message" label="消息" min-width="200" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span :class="{ 'text-danger': row.progress < 0 }">{{ row.message || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ row }">
                    <el-button 
                      type="primary" 
                      link 
                      size="small" 
                      @click="showProgressDetail(row)"
                    >
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 升级历史视图 -->
            <div v-if="currentView === 'history'" class="history-view">
              <div class="history-filters">
                <el-date-picker
                  v-model="historyFilters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="default"
                  @change="fetchUpgradeHistory"
                />
                <el-select
                  v-model="historyFilters.status"
                  placeholder="状态筛选"
                  clearable
                  size="default"
                  style="width: 140px"
                  @change="fetchUpgradeHistory"
                >
                  <el-option label="已完成" value="completed" />
                  <el-option label="失败" value="failed" />
                  <el-option label="超时" value="timeout" />
                </el-select>
              </div>
              
              <el-table
                v-loading="historyLoading"
                :data="upgradeHistoryList"
                border
                style="width: 100%; margin-top: 10px"
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="terminal_id" label="终端ID" width="80" />
                <el-table-column prop="from_version" label="原版本" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="info">{{ row.from_version }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="to_version" label="目标版本" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="primary">{{ row.to_version }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="结果" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)" size="small">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="started_at" label="开始时间" width="150">
                  <template #default="{ row }">
                    {{ formatTime(row.started_at) }}
                  </template>
                </el-table-column>
                <el-table-column prop="completed_at" label="完成时间" width="150">
                  <template #default="{ row }">
                    {{ row.completed_at ? formatTime(row.completed_at) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="耗时" width="80">
                  <template #default="{ row }">
                    {{ row.duration ? formatDuration(row.duration) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span v-if="row.error_message" class="text-danger">{{ row.error_message }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 上传新版本对话框 -->
    <el-dialog
      v-model="uploadDialog.visible"
      title="上传新版本"
      width="500px"
      destroy-on-close
    >
      <el-form ref="uploadFormRef" :model="uploadDialog.form" label-width="100px">
        <el-form-item label="版本号" prop="version" required>
          <el-input v-model="uploadDialog.form.version" placeholder="请输入版本号，如：1.0.0" />
        </el-form-item>
        <el-form-item label="平台" prop="platform" required>
          <el-select v-model="uploadDialog.form.platform" placeholder="请选择平台">
            <el-option label="Windows" value="windows" />
            <el-option label="Linux" value="linux" />
            <el-option label="MacOS" value="macos" />
          </el-select>
        </el-form-item>
        <el-form-item label="安装包" prop="file" required>
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleUpload"
            :limit="1"
            :auto-upload="false"
            :file-list="uploadDialog.fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请选择Agent安装包文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="发布说明" prop="release_notes">
          <el-input
            v-model="uploadDialog.form.release_notes"
            type="textarea"
            placeholder="请输入版本发布说明"
            rows="4"
          />
        </el-form-item>
        <el-form-item label="设为当前版本">
          <el-switch v-model="uploadDialog.form.is_current" />
        </el-form-item>
        <el-form-item label="自动推送升级" v-if="uploadDialog.form.is_current">
          <el-switch v-model="uploadDialog.form.auto_upgrade" />
          <div class="el-form-item-tip">
            开启后，将自动向符合条件的终端推送升级命令
          </div>
        </el-form-item>
        <el-form-item label="升级策略" v-if="uploadDialog.form.is_current && uploadDialog.form.auto_upgrade">
          <el-radio-group v-model="uploadDialog.form.upgrade_strategy">
            <el-radio label="all">所有终端</el-radio>
            <el-radio label="online">仅在线终端</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadDialog.loading">
            上传
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

  <!-- 手动升级对话框 -->
  <el-dialog
    v-model="upgradeDialog.visible"
    title="手动升级Agent"
    width="600px"
    destroy-on-close
  >
    <div class="dialog-body">
      <el-alert
        type="info"
        :closable="false"
        show-icon
      >
        <p>当前版本: <el-tag type="success">{{ upgradeDialog.currentVersion?.version }}</el-tag></p>
        <p>选择要升级的终端，系统将向这些终端发送升级命令。</p>
      </el-alert>

      <div class="mt-20">
        <el-form :model="upgradeDialog.form" label-width="100px">
          <el-form-item label="终端选择" required>
            <div class="terminal-selection">
              <el-checkbox v-model="selectAllTerminals" @change="handleSelectAllChange">全选</el-checkbox>
              <div class="terminal-list">
                <el-checkbox-group v-model="upgradeDialog.selectedTerminals">
                  <el-checkbox
                    v-for="terminal in upgradeDialog.terminals"
                    :key="terminal.id"
                    :label="terminal.id"
                    :disabled="!terminal.online_status"
                  >
                    {{ terminal.hostname }}
                    <el-tag size="small" :type="terminal.online_status ? 'success' : 'danger'" effect="dark" class="ml-5">
                      {{ terminal.online_status ? '在线' : '离线' }}
                    </el-tag>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="upgradeDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmManualUpgrade" :loading="upgradeDialog.loading" :disabled="upgradeDialog.selectedTerminals.length === 0">
          <el-icon><Upload /></el-icon> 发送升级命令
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { Upload, Setting, ArrowDown, Check, Delete, Refresh, Search, TrendCharts, CircleCheckFilled, Loading, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { terminalApi } from '@/api/terminal'
import type { AgentVersion, AgentUpgradeStatus, AgentUpgradeHistory, AgentUpgradeStats } from '@/types/terminal'
import Authority from '@/components/Authority/index.vue'

// 数据加载
const loading = ref(false)
const versionList = ref<AgentVersion[]>([])

// 选项卡管理
const activeTab = ref('versions')

// 升级监控相关
const statsLoading = ref(false)
const statusLoading = ref(false)
const historyLoading = ref(false)
const currentView = ref('status')
const upgradeStats = ref<AgentUpgradeStats>({
  total_upgrades: 0,
  success_rate: 0,
  active_upgrades: 0,
  status_distribution: {},
  daily_stats: [],
  period_days: 7
})
const upgradeStatusList = ref<AgentUpgradeStatus[]>([])
const upgradeHistoryList = ref<AgentUpgradeHistory[]>([])

// 监控筛选器
const monitoringFilters = reactive({
  status: '',
  terminalId: ''
})

const historyFilters = reactive({
  dateRange: null as any,
  status: ''
})

// 上传对话框
const uploadDialog = reactive({
  visible: false,
  loading: false,
  fileList: [] as any[],
  form: {
    version: '',
    platform: 'windows',
    release_notes: '',
    is_current: false,
    auto_upgrade: false,
    upgrade_strategy: 'all'
  }
})

// 手动升级对话框
const upgradeDialog = reactive({
  visible: false,
  loading: false,
  currentVersion: null as AgentVersion | null,
  terminals: [] as any[],
  selectedTerminals: [] as string[],
  form: {
    version: '',
    download_url: ''
  }
})

// 加载版本列表
const fetchVersionList = async () => {
  loading.value = true
  try {
    const response = await terminalApi.getAgentVersions({})
    versionList.value = response.data
  } catch (error) {
    console.error('获取Agent版本列表失败:', error)
    ElMessage.error('获取Agent版本列表失败')
  } finally {
    loading.value = false
  }
}

// 显示上传对话框
const showUploadDialog = () => {
  uploadDialog.visible = true
  uploadDialog.fileList = []
  uploadDialog.form = {
    version: '',
    platform: 'windows',
    release_notes: '',
    is_current: false,
    auto_upgrade: false,
    upgrade_strategy: 'all'
  }
}

// 处理文件变化
const handleFileChange = (file: any) => {
  uploadDialog.fileList = [file]
}

// 处理文件移除
const handleFileRemove = () => {
  uploadDialog.fileList = []
}

// 处理上传
const handleUpload = () => {
  // 这个函数不会真正执行，我们使用自定义的submitUpload来处理
}

// 提交上传
const submitUpload = async () => {
  // 表单验证
  if (!uploadDialog.form.version) {
    ElMessage.warning('请输入版本号')
    return
  }

  if (!uploadDialog.form.platform) {
    ElMessage.warning('请选择平台')
    return
  }

  if (uploadDialog.fileList.length === 0) {
    ElMessage.warning('请选择安装包文件')
    return
  }

  uploadDialog.loading = true
  try {
    const formData = new FormData()
    formData.append('version', uploadDialog.form.version)
    formData.append('platform', uploadDialog.form.platform)
    formData.append('is_current', uploadDialog.form.is_current ? 'true' : 'false')

    // 如果设为当前版本并启用自动升级，添加相关参数
    if (uploadDialog.form.is_current && uploadDialog.form.auto_upgrade) {
      formData.append('auto_upgrade', 'true')
      formData.append('upgrade_strategy', uploadDialog.form.upgrade_strategy)
    }

    if (uploadDialog.form.release_notes) {
      formData.append('release_notes', uploadDialog.form.release_notes)
    }

    const file = uploadDialog.fileList[0].raw
    formData.append('file', file)

    await terminalApi.uploadAgentVersion(formData)
    ElMessage.success('上传成功')
    uploadDialog.visible = false
    fetchVersionList()
  } catch (error) {
    console.error('上传Agent版本失败:', error)
    ElMessage.error('上传Agent版本失败')
  } finally {
    uploadDialog.loading = false
  }
}

// 设置为当前版本
const setAsCurrent = async (version: AgentVersion) => {
  // 弹出确认对话框，询问是否自动推送升级
  ElMessageBox.confirm(
    '是否自动向终端推送升级命令？',
    `设置 ${version.version} 为当前版本`,
    {
      confirmButtonText: '是，自动推送',
      cancelButtonText: '否，仅设置当前版本',
      type: 'warning',
      distinguishCancelAndClose: true,
      closeOnClickModal: false
    }
  ).then(async () => {
    // 确认，自动推送升级
    try {
      // 再次确认升级策略
      const { value: upgradeStrategy } = await ElMessageBox.confirm(
        '请选择升级策略：',
        '自动推送升级策略',
        {
          confirmButtonText: '所有终端',
          cancelButtonText: '仅在线终端',
          distinguishCancelAndClose: true,
          closeOnClickModal: false,
          showClose: false
        }
      ).catch(action => {
        // 如果点击取消，返回'online'
        return { value: action === 'cancel' ? 'online' : 'all' }
      })

      // 调用API，带上自动升级参数
      await terminalApi.setCurrentAgentVersion(version.id, true, upgradeStrategy || 'all')
      ElMessage.success(`已将${version.version}设置为当前版本并推送升级命令`)
      fetchVersionList()
    } catch (error) {
      console.error('设置当前版本失败:', error)
      ElMessage.error('设置当前版本失败')
    }
  }).catch(action => {
    // 取消，仅设置当前版本
    if (action === 'cancel') {
      try {
        terminalApi.setCurrentAgentVersion(version.id)
          .then(() => {
            ElMessage.success(`已将${version.version}设置为当前版本`)
            fetchVersionList()
          })
          .catch(error => {
            console.error('设置当前版本失败:', error)
            ElMessage.error('设置当前版本失败')
          })
      } catch (error) {
        console.error('设置当前版本失败:', error)
        ElMessage.error('设置当前版本失败')
      }
    }
  })
}

// 确认删除
const confirmDelete = (version: AgentVersion) => {
  ElMessageBox.confirm(
    `确定要删除版本 ${version.version} 吗？此操作不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await terminalApi.deleteAgentVersion(version.id)
      ElMessage.success('删除成功')
      fetchVersionList()
    } catch (error) {
      console.error('删除Agent版本失败:', error)
      ElMessage.error('删除Agent版本失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 格式化文件大小
const formatSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

// 全选状态
const selectAllTerminals = ref(false)

// === 升级监控相关方法 ===

// 获取升级统计数据
const fetchUpgradeStats = async () => {
  statsLoading.value = true
  try {
    const response = await terminalApi.getAgentUpgradeStats(7)
    upgradeStats.value = response.data
  } catch (error) {
    console.error('获取升级统计失败:', error)
    ElMessage.error('获取升级统计失败')
  } finally {
    statsLoading.value = false
  }
}

// 获取升级状态列表
const fetchUpgradeStatus = async () => {
  statusLoading.value = true
  try {
    const params: any = {}
    if (monitoringFilters.status) {
      params.status = monitoringFilters.status
    }
    if (monitoringFilters.terminalId) {
      params.terminal_id = parseInt(monitoringFilters.terminalId)
    }
    
    const response = await terminalApi.getAgentUpgradeStatus(params)
    upgradeStatusList.value = response.data
  } catch (error) {
    console.error('获取升级状态失败:', error)
    ElMessage.error('获取升级状态失败')
  } finally {
    statusLoading.value = false
  }
}

// 获取升级历史记录
const fetchUpgradeHistory = async () => {
  historyLoading.value = true
  try {
    const params: any = {}
    if (historyFilters.status) {
      params.status = historyFilters.status
    }
    if (historyFilters.dateRange && historyFilters.dateRange.length === 2) {
      params.start_date = historyFilters.dateRange[0].toISOString().split('T')[0]
      params.end_date = historyFilters.dateRange[1].toISOString().split('T')[0]
    }
    
    const response = await terminalApi.getAgentUpgradeHistory(params)
    upgradeHistoryList.value = response.data
  } catch (error) {
    console.error('获取升级历史失败:', error)
    ElMessage.error('获取升级历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 重置筛选器
const resetFilters = () => {
  monitoringFilters.status = ''
  monitoringFilters.terminalId = ''
  fetchUpgradeStatus()
}

// 切换视图
const handleViewChange = (view: string) => {
  if (view === 'status') {
    fetchUpgradeStatus()
  } else if (view === 'history') {
    fetchUpgradeHistory()
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'success'
    case 'pending':
      return 'info'
    case 'downloading':
    case 'installing':
      return 'warning'
    case 'failed':
    case 'rolled_back':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return '等待中'
    case 'downloading':
      return '下载中'
    case 'installing':
      return '安装中'
    case 'completed':
      return '已完成'
    case 'failed':
      return '失败'
    case 'rolled_back':
      return '已回滚'
    default:
      return status
  }
}

// 格式化耗时
const formatDuration = (duration: number) => {
  if (duration < 60) {
    return `${duration.toFixed(1)}s`
  } else if (duration < 3600) {
    return `${(duration / 60).toFixed(1)}m`
  } else {
    return `${(duration / 3600).toFixed(1)}h`
  }
}

// 显示进度详情
const showProgressDetail = async (row: AgentUpgradeStatus) => {
  try {
    const response = await terminalApi.getAgentUpgradeProgress(row.command_id)
    const progress = response.data
    
    ElMessageBox.alert(
      `<div>
        <p><strong>命令ID:</strong> ${progress.command_id}</p>
        <p><strong>进度:</strong> ${progress.progress}%</p>
        <p><strong>状态消息:</strong> ${progress.message}</p>
        <p><strong>更新时间:</strong> ${formatTime(progress.timestamp)}</p>
        ${row.error_details ? `<p><strong>错误详情:</strong> ${row.error_details}</p>` : ''}
      </div>`,
      '升级进度详情',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    )
  } catch (error) {
    console.error('获取升级进度详情失败:', error)
    ElMessage.error('获取升级进度详情失败')
  }
}

// 手动升级功能
const manualUpgrade = async (version: AgentVersion) => {
  upgradeDialog.currentVersion = version
  upgradeDialog.form.version = version.version
  upgradeDialog.form.download_url = version.download_url
  upgradeDialog.selectedTerminals = []
  upgradeDialog.visible = true

  // 加载终端列表
  const loadingInstance = ElLoading.service({ fullscreen: true, text: '加载终端列表...' })
  try {
    const response = await terminalApi.getTerminals({})
    upgradeDialog.terminals = response.data
  } catch (error) {
    console.error('获取终端列表失败:', error)
    ElMessage.error('获取终端列表失败')
  } finally {
    loadingInstance.close()
  }
}

// 全选切换
const handleSelectAllChange = (val: boolean) => {
  if (val) {
    // 选中所有在线终端
    upgradeDialog.selectedTerminals = upgradeDialog.terminals
      .filter(terminal => terminal.online_status)
      .map(terminal => terminal.id)
  } else {
    // 清除选中
    upgradeDialog.selectedTerminals = []
  }
}

// 确认手动升级
const confirmManualUpgrade = async () => {
  if (upgradeDialog.selectedTerminals.length === 0) {
    ElMessage.warning('请选择要升级的终端')
    return
  }

  upgradeDialog.loading = true
  try {
    // 批量发送升级命令
    await terminalApi.sendBatchCommand({
      terminal_ids: upgradeDialog.selectedTerminals,
      command_type: 'UPGRADE_AGENT',
      content: JSON.stringify({
        version: upgradeDialog.form.version,
        url: upgradeDialog.form.download_url
      }),
      timeout: 600 // 10分钟超时
    })

    ElMessage.success(`已向 ${upgradeDialog.selectedTerminals.length} 个终端发送升级命令`)
    upgradeDialog.visible = false
  } catch (error) {
    console.error('发送升级命令失败:', error)
    ElMessage.error('发送升级命令失败')
  } finally {
    upgradeDialog.loading = false
  }
}

onMounted(() => {
  fetchVersionList()
  // 初始化升级监控数据
  fetchUpgradeStats()
  fetchUpgradeStatus()
})
</script>

<style scoped>
.agent-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.box-card {
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.search-form {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.agent-table-row {
  transition: all 0.3s;
  height: 56px;
}

.agent-table-row:hover {
  background-color: #f0f5ff;
  transform: translateY(-1px);
}

.agent-table-header th {
  background-color: #f2f6fc;
  color: #606266;
  font-weight: bold;
  height: 48px;
  vertical-align: middle;
  border-bottom: 2px solid #ebeef5;
}

.table-header-cell {
  padding: 0 !important;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
  width: 100%;
  font-weight: bold;
  color: #303133;
  letter-spacing: 0.5px;
}

.mb-20 {
  margin-bottom: 20px;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

.el-form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
}

.terminal-selection {
  margin-top: 10px;
}

.terminal-list {
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.terminal-list .el-checkbox {
  display: block;
  margin-right: 0;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.terminal-list .el-checkbox:hover {
  background-color: #f5f7fa;
}

.ml-5 {
  margin-left: 5px;
}

.mt-20 {
  margin-top: 20px;
}

.text-danger {
  color: #F56C6C;
}

.monitoring-container {
  padding: 20px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  background: #ffffff;
  transition: all 0.3s ease;
  gap: 18px;
  border: 1px solid #e4e7ed;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:hover {
  background: #f8fafe;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #409eff;
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 52px;
  height: 52px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon.total {
  background: linear-gradient(135deg, #5b9bd5, #409eff);
}

.stat-icon.success {
  background: linear-gradient(135deg, #70ad47, #67c23a);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #ffc000, #e6a23c);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #c65911, #f56c6c);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.monitoring-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-toggle {
  display: flex;
  align-items: center;
}

.status-view {
  margin-top: 20px;
}

.history-view {
  margin-top: 20px;
}

.history-filters {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.progress-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0 10px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .stat-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
    padding: 20px;
  }
  
  .stat-icon {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
  
  .stat-number {
    font-size: 20px;
  }
}
</style>