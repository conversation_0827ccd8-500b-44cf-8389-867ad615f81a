# 移动端首页改为APP菜单

## 任务描述
将移动端首页从仪表盘改为直接进入APP菜单页面，仪表盘作为APP菜单中的一个应用项，同时移除底部导航栏。

## 实施计划
1. 修改路由配置，将默认重定向改为apps
2. 在APP配置中添加仪表盘应用项
3. 移除底部导航栏组件和相关逻辑
4. 更新布局组件，调整返回逻辑

## 执行步骤

### 步骤1：修改路由默认重定向
- **文件**: `frontend/src/mobile/router/index.ts`
- **修改**: 将 `/m` 的默认重定向从 `dashboard` 改为 `apps`
- **状态**: ✅ 已完成

### 步骤2：添加仪表盘到APP菜单
- **文件**: `frontend/src/mobile/config/apps.ts`
- **修改**: 在 `appItems` 数组开头添加仪表盘应用项
- **配置**:
  ```typescript
  {
    id: 'dashboard',
    name: '仪表盘',
    icon: 'chart-trending-o',
    color: '#1989fa',
    path: '/m/dashboard',
    description: '系统数据概览'
  }
  ```
- **状态**: ✅ 已完成

### 步骤3：移除底部导航栏
- **文件**: `frontend/src/mobile/layout/components/MobileTabbar.vue`
- **操作**: 删除整个文件
- **状态**: ✅ 已完成

### 步骤4：更新布局组件
- **文件**: `frontend/src/mobile/layout/MobileLayout.vue`
- **修改内容**:
  - 移除 MobileTabbar 组件导入和使用
  - 移除底部导航相关的显示逻辑
  - 调整主内容区padding，移除底部空间
  - 更新返回按钮逻辑，默认返回到 `/m/apps`
  - 更新showBack逻辑，在apps页面不显示返回按钮
- **状态**: ✅ 已完成

## 预期效果
1. 用户访问 `/m` 时直接进入APP菜单页面
2. 仪表盘显示在APP菜单的第一个位置
3. 移除底部导航栏，界面更简洁
4. 保持所有功能模块的正常访问

## 测试要点
- [ ] 访问 `/m` 路径是否正确重定向到APP菜单
- [ ] APP菜单中是否显示仪表盘选项
- [ ] 点击仪表盘是否能正确跳转到仪表盘页面
- [ ] 各个功能模块的导航是否正常
- [ ] 返回按钮逻辑是否正确

### 步骤5：修复登录重定向逻辑
- **文件**: `frontend/src/mobile/views/auth/Login.vue`
- **问题**: 登录成功后硬编码重定向到 `/m/dashboard`
- **修改**: 将重定向目标改为 `/m/apps`
- **状态**: ✅ 已完成

### 步骤6：修复Loading页面重定向
- **文件**: `frontend/src/views/Loading.vue`
- **问题**: 移动端初始化完成后重定向到 `/m/dashboard`
- **修改**: 将移动端重定向目标改为 `/m/apps`
- **状态**: ✅ 已完成

### 步骤7：修复DeviceTest页面重定向
- **文件**: `frontend/src/views/DeviceTest.vue`
- **问题**: 使用了错误的路径 `/mobile/dashboard`，应该是 `/m/apps`
- **修改**: 更新移动端跳转路径为 `/m/apps`
- **状态**: ✅ 已完成

## 完成状态
已全面检查并修复所有硬编码重定向问题：
- ✅ 路由默认重定向
- ✅ APP菜单配置
- ✅ 底部导航移除
- ✅ 布局组件更新
- ✅ 登录页面重定向
- ✅ Loading页面重定向
- ✅ DeviceTest页面重定向

现在登录后会直接进入APP菜单页面，所有相关跳转都已正确配置。 