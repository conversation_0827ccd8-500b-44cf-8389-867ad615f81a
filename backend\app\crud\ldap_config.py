from typing import List, Optional
from sqlalchemy.orm import Session
from ..crud.base import CRUDBase
from ..models.ldap_config import LdapConfig
from ..schemas.ldap_config import LdapConfigCreate, LdapConfigUpdate

class CRUDLdapConfig(CRUDBase[LdapConfig, LdapConfigCreate, LdapConfigUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[LdapConfig]:
        """根据名称获取LDAP配置"""
        return db.query(LdapConfig).filter(LdapConfig.name == name).first()

    def get_active_configs(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[LdapConfig]:
        """获取所有启用的LDAP配置"""
        return db.query(LdapConfig).filter(
            LdapConfig.is_active == True
        ).offset(skip).limit(limit).all()

    def get_default_config(self, db: Session) -> Optional[LdapConfig]:
        """获取默认LDAP配置"""
        return db.query(LdapConfig).filter(
            LdapConfig.is_default == True,
            LdapConfig.is_active == True
        ).first()

    def set_default(self, db: Session, *, config_id: int) -> Optional[LdapConfig]:
        """设置默认配置"""
        # 首先取消所有配置的默认状态
        db.query(LdapConfig).update({LdapConfig.is_default: False})
        
        # 设置指定配置为默认
        config = db.query(LdapConfig).filter(LdapConfig.id == config_id).first()
        if config:
            config.is_default = True
            db.commit()
            db.refresh(config)
        
        return config

    def create(self, db: Session, *, obj_in: LdapConfigCreate) -> LdapConfig:
        """创建LDAP配置"""
        # 如果这是第一个配置，自动设为默认
        existing_configs = db.query(LdapConfig).count()
        
        db_obj = LdapConfig(
            name=obj_in.name,
            server=obj_in.server,
            port=obj_in.port,
            use_ssl=obj_in.use_ssl,
            base_dn=obj_in.base_dn,
            bind_dn=obj_in.bind_dn,
            bind_password=obj_in.bind_password,
            user_search_base=obj_in.user_search_base,
            user_search_filter=obj_in.user_search_filter,
            user_name_attr=obj_in.user_name_attr,
            user_email_attr=obj_in.user_email_attr,
            user_display_name_attr=obj_in.user_display_name_attr,
            auto_create_user=obj_in.auto_create_user,
            default_role=obj_in.default_role,
            is_active=obj_in.is_active,
            is_default=obj_in.is_default if existing_configs > 0 else True,  # 第一个配置自动设为默认
            description=obj_in.description
        )
        
        # 如果设置为默认，需要取消其他配置的默认状态
        if db_obj.is_default:
            db.query(LdapConfig).update({LdapConfig.is_default: False})
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, db_obj: LdapConfig, obj_in: LdapConfigUpdate) -> LdapConfig:
        """更新LDAP配置"""
        update_data = obj_in.dict(exclude_unset=True)
        
        # 如果设置为默认，需要取消其他配置的默认状态
        if update_data.get('is_default') is True:
            db.query(LdapConfig).filter(LdapConfig.id != db_obj.id).update({LdapConfig.is_default: False})
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj

ldap_config_crud = CRUDLdapConfig(LdapConfig) 