<template>
  <div class="mobile-inventory-task">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="task?.name || '盘点任务'"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-button
          v-if="task?.status === 'in_progress'"
          type="success"
          size="small"
          @click="handleCompleteTask"
          :loading="completing"
        >
          完成
        </van-button>
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-center" vertical>
      加载中...
    </van-loading>

    <!-- 任务内容 -->
    <div v-else-if="task" class="task-content">
      <!-- 标签页 -->
      <van-tabs v-model="activeTab" sticky>
        <!-- 任务信息标签页 -->
        <van-tab title="任务信息" name="info">
          <div class="tab-content">
            <!-- 任务概览 -->
            <van-cell-group title="任务信息" inset>
              <van-cell title="任务名称" :value="task.name" />
              <van-cell title="任务状态">
                <template #value>
                  <van-tag :type="getStatusTagType(task.status) as any">
                    {{ getStatusText(task.status) }}
                  </van-tag>
                </template>
              </van-cell>
              <van-cell title="盘点期间">
                <template #value>
                  <span class="date-range">{{ formatDate(task.start_date) }} - {{ formatDate(task.end_date) }}</span>
                </template>
              </van-cell>
              <van-cell title="创建人" :value="task.created_by" />
              <van-cell v-if="task.description" title="任务描述" :value="task.description" />
            </van-cell-group>

            <!-- 盘点统计 -->
            <van-cell-group title="盘点统计" inset>
              <van-cell title="总资产数" :value="statistics.total.toString()" />
              <van-cell title="已盘点" :value="statistics.checked.toString()" />
              <van-cell title="待盘点" :value="statistics.pending.toString()" />
              <van-cell title="正常资产" :value="statistics.normal.toString()" />
              <van-cell title="异常资产" :value="statistics.abnormal.toString()" />
              <van-cell title="缺失资产" :value="statistics.missing.toString()" />
              <van-cell title="信息变更" :value="statistics.info_changed.toString()" />
            </van-cell-group>
          </div>
        </van-tab>

        <!-- 盘点记录标签页 -->
        <van-tab title="盘点记录" name="records">
          <div class="tab-content">
            <!-- 搜索功能 -->
            <div v-if="task.status === 'in_progress'" class="search-section">
              <integrated-search
                v-model="searchValue"
                v-model:search-field="searchField"
                @search="handleSearch"
                @clear="handleSearchClear"
              />
            </div>

            <!-- 记录筛选 -->
            <div class="records-filter">
              <van-tabs v-model:active="activeRecordStatus" @change="handleRecordStatusChange" swipeable>
                <van-tab title="全部" name="all"></van-tab>
                <van-tab title="待盘点" name="pending"></van-tab>
                <van-tab title="正常" name="normal"></van-tab>
                <van-tab title="异常" name="abnormal"></van-tab>
                <van-tab title="缺失" name="missing"></van-tab>
                <van-tab title="信息变更" name="info_changed"></van-tab>
              </van-tabs>
            </div>

            <!-- 记录列表 -->
            <div class="records-list">
              <van-list
                v-model:loading="recordsLoading"
                :finished="recordsFinished"
                finished-text="没有更多了"
                @load="loadRecords"
              >
                <div
                  v-for="record in recordsList"
                  :key="record.id"
                  class="record-item"
                  @click="handleRecordClick(record)"
                >
                  <van-card>
                    <template #title>
                      <div class="record-header">
                        <span class="asset-name" v-html="highlightText(record.asset.name)"></span>
                        <van-tag :type="getRecordStatusTagType(record.status) as any">
                          {{ getRecordStatusText(record.status) }}
                        </van-tag>
                      </div>
                    </template>
                    
                    <template #desc>
                      <div class="record-desc">
                        <div class="asset-info">
                          <div class="info-item">
                            <span class="label">编号：</span>
                            <span v-html="highlightText(record.asset.asset_number)"></span>
                          </div>
                          <div class="info-item">
                            <span class="label">使用人：</span>
                            <span v-html="highlightText(record.status === 'info_changed' && record.new_user ? record.new_user : record.asset.user)"></span>
                          </div>
                          <div class="info-item">
                            <span class="label">位置：</span>
                            <span v-html="highlightText(record.status === 'info_changed' && record.new_location ? record.new_location : record.asset.location)"></span>
                          </div>
                          <div v-if="record.status === 'info_changed' && (record.new_custodian || record.new_company)" class="info-item">
                            <span class="label">{{ record.new_custodian ? '新领用人：' : '新公司：' }}</span>
                            <span>{{ record.new_custodian || record.new_company }}</span>
                          </div>
                        </div>
                        <div v-if="record.checked_by" class="check-info">
                          <div class="info-item">
                            <span class="label">盘点人：</span>
                            <span>{{ record.checked_by }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">盘点时间：</span>
                            <span>{{ formatDateTime(record.checked_at) }}</span>
                          </div>
                        </div>
                        <div v-if="record.remarks" class="remarks">
                          <span class="label">备注：</span>
                          <span>{{ record.remarks }}</span>
                        </div>
                      </div>
                    </template>

                    <template #footer>
                      <div v-if="task.status === 'in_progress'" class="record-actions">
                        <van-button
                          v-if="record.status === 'pending'"
                          size="small"
                          type="primary"
                          @click.stop="handleCheckRecord(record, 'normal')"
                        >
                          正常
                        </van-button>
                        <van-button
                          v-if="record.status === 'pending'"
                          size="small"
                          type="warning"
                          @click.stop="handleCheckRecord(record, 'abnormal')"
                        >
                          异常
                        </van-button>
                        <van-button
                          v-if="record.status === 'pending'"
                          size="small"
                          type="danger"
                          @click.stop="handleCheckRecord(record, 'missing')"
                        >
                          缺失
                        </van-button>
                        <van-button
                          v-if="record.status !== 'pending'"
                          size="small"
                          @click.stop="handleEditRecord(record)"
                        >
                          编辑
                        </van-button>
                      </div>
                    </template>
                  </van-card>
                </div>

                <!-- 空状态 -->
                <van-empty
                  v-if="!recordsLoading && recordsList.length === 0"
                  image="search"
                  :description="searchValue ? '未找到相关记录' : '暂无盘点记录'"
                />
              </van-list>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 扫码盘点弹窗 -->
    <van-dialog
      v-model:show="showScanDialog"
      title="扫码盘点"
      show-cancel-button
      @confirm="handleScanConfirm"
    >
      <div class="scan-content">
        <van-field
          v-model="scanResult"
          label="扫码结果"
          placeholder="请扫描资产二维码或条形码"
          readonly
        />
        <div class="scan-buttons">
          <van-button 
            type="primary" 
            size="small" 
            @click="startScan"
            :loading="scanning"
          >
            {{ scanning ? '扫描中...' : '开始扫描' }}
          </van-button>
          <van-button 
            size="small" 
            @click="scanResult = ''"
          >
            清空
          </van-button>
        </div>
        <div class="scan-tips">
          <p>提示：扫描资产编号后会自动定位到对应的盘点记录</p>
        </div>
      </div>
    </van-dialog>

    <!-- 手动盘点弹窗 -->
    <van-dialog
      v-model:show="showManualDialog"
      title="手动盘点"
      show-cancel-button
      @confirm="handleManualConfirm"
    >
      <div class="manual-content">
        <van-field
          v-model="manualAssetNumber"
          label="资产编号"
          placeholder="请输入资产编号"
        />
        <div class="manual-buttons">
          <van-button 
            type="primary" 
            size="small" 
            @click="searchAsset"
            :loading="searching"
          >
            {{ searching ? '搜索中...' : '搜索资产' }}
          </van-button>
        </div>
        <div v-if="foundRecord" class="found-asset">
          <p>找到资产：{{ foundRecord.asset.name }}</p>
          <p>使用人：{{ foundRecord.asset.user }}</p>
        </div>
      </div>
    </van-dialog>

    <!-- 记录详情/编辑弹窗 -->
    <van-popup
      v-model:show="showRecordDialog"
      position="bottom"
      :style="{ height: 'var(--mobile-popup-max-height, 68svh)' }"
      round
    >
      <div class="record-popup">
        <div class="popup-header">
          <h3>{{ editingRecord ? '编辑盘点记录' : '盘点记录详情' }}</h3>
          <van-icon name="cross" @click="showRecordDialog = false" />
        </div>
        
        <div v-if="editingRecord" class="popup-content">
          <van-form @submit="handleUpdateRecord" ref="recordFormRef">
            <van-cell-group title="资产信息">
              <van-cell title="资产名称" :value="editingRecord.asset.name" />
              <van-cell title="资产编号" :value="editingRecord.asset.asset_number" />
              <van-cell title="规格型号" :value="editingRecord.asset.specification" />
              <van-cell title="资产类别" :value="editingRecord.asset.category" />
              <van-cell title="生产编号" :value="editingRecord.asset.production_number" />
              <van-cell title="制造商" :value="editingRecord.asset.manufacturer" />
              <van-cell title="供应商" :value="editingRecord.asset.supplier" />
              <van-cell :title="recordForm.status === 'info_changed' ? '原领用人' : '领用人'" :value="editingRecord.asset.custodian" />
              <van-cell :title="recordForm.status === 'info_changed' ? '原使用人' : '使用人'" :value="editingRecord.asset.user" />
              <van-cell :title="recordForm.status === 'info_changed' ? '原公司' : '公司'" :value="editingRecord.asset.company" />
              <van-cell :title="recordForm.status === 'info_changed' ? '原位置' : '位置'" :value="editingRecord.asset.location" />
              <van-cell title="购入日期" :value="editingRecord.asset.purchase_date ? formatDate(editingRecord.asset.purchase_date) : ''" />
              <van-cell title="资产价格" :value="editingRecord.asset.price ? `¥${editingRecord.asset.price}` : ''" />
              <van-cell title="验收人" :value="editingRecord.asset.inspector" />
            </van-cell-group>

            <van-cell-group title="盘点状态">
              <van-field
                :model-value="getRecordStatusText(recordForm.status as InventoryRecordStatus)"
                name="status"
                label="盘点结果"
                placeholder="请选择盘点结果"
                readonly
                is-link
                @click="showRecordStatusPicker = true"
                :rules="[{ required: true, message: '请选择盘点结果' }]"
              />
              <van-field
                v-model="recordForm.remarks"
                name="remarks"
                label="备注"
                placeholder="请输入备注信息"
                type="textarea"
                rows="3"
              />
            </van-cell-group>

            <van-cell-group v-if="recordForm.status === 'info_changed'" title="更新信息">
              <!-- 新资产名称 - 使用字段值选择器 -->
              <MobileFieldValueSelector
                v-model="recordForm.newName"
                :field-name="FIELD_NAMES.NAME"
                name="newName"
                label="新资产名称"
                placeholder="请选择或输入新资产名称"
              />
              
              <!-- 新领用人 - 使用字段值选择器 -->
              <MobileFieldValueSelector
                v-model="recordForm.newCustodian"
                :field-name="FIELD_NAMES.PERSONNEL"
                name="newCustodian"
                label="新领用人"
                placeholder="请选择或输入新领用人"
              />
              
              <!-- 新使用人 - 使用字段值选择器 -->
              <MobileFieldValueSelector
                v-model="recordForm.newUser"
                :field-name="FIELD_NAMES.PERSONNEL"
                name="newUser"
                label="新使用人"
                placeholder="请选择或输入新使用人"
              />
              
              <!-- 新公司 - 使用字段值选择器 -->
              <MobileFieldValueSelector
                v-model="recordForm.newCompany"
                :field-name="FIELD_NAMES.COMPANY"
                name="newCompany"
                label="新公司"
                placeholder="请选择或输入新公司"
              />
              
              <!-- 新位置 - 使用字段值选择器 -->
              <MobileFieldValueSelector
                v-model="recordForm.newLocation"
                :field-name="FIELD_NAMES.LOCATION"
                name="newLocation"
                label="新位置"
                placeholder="请选择或输入新位置"
              />
            </van-cell-group>

            <!-- 自定义字段区域 -->
            <van-cell-group v-if="customFields.length > 0" title="自定义字段">
              <template v-for="field in customFields" :key="field.id">
                <!-- 文本类型 -->
                <van-field
                  v-if="field.field_type === 'text'"
                  v-model="customFieldData[field.name]"
                  :name="field.name"
                  :label="field.label"
                  :placeholder="`请输入${field.label}`"
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
                />
                
                <!-- 多行文本类型 -->
                <van-field
                  v-else-if="field.field_type === 'textarea'"
                  v-model="customFieldData[field.name]"
                  :name="`custom_${field.name}`"
                  :label="field.label"
                  type="textarea"
                  :placeholder="`请输入${field.label}`"
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
                  :rows="field.options?.rows || 3"
                />
                
                <!-- 数字类型 -->
                <van-field
                  v-else-if="field.field_type === 'number'"
                  v-model.number="customFieldData[field.name]"
                  :name="field.name"
                  :label="field.label"
                  :placeholder="`请输入${field.label}`"
                  type="number"
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
                />
                
                <!-- 日期类型 -->
                <van-field
                  v-else-if="field.field_type === 'date'"
                  v-model="customFieldData[field.name]"
                  :name="field.name"
                  :label="field.label"
                  :placeholder="`请选择${field.label}`"
                  readonly
                  is-link
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
                  @click="() => showDatePicker(field)"
                />
                
                <!-- 选择类型 -->
                <van-field
                  v-else-if="field.field_type === 'select'"
                  :model-value="getFieldOptionText(field, customFieldData[field.name])"
                  :name="field.name"
                  :label="field.label"
                  :placeholder="`请选择${field.label}`"
                  readonly
                  is-link
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
                  @click="() => showFieldPicker(field)"
                />
                
                <!-- 文件上传类型 -->
                <van-field
                  v-else-if="field.field_type === 'file'"
                  :name="field.name"
                  :label="field.label"
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请选择${field.label}` }] : []"
                >
                  <template #input>
                    <MobileFileUploader
                      v-model="customFieldData[field.name]"
                      :accept="field.options?.accept || 'image/*'"
                      :max-size="field.options?.max_size || 5 * 1024 * 1024"
                      :multiple="field.options?.multiple || false"
                      :max-count="field.options?.multiple ? 9 : 1"
                      :upload-text="field.options?.multiple ? '上传图片' : '上传图片'"
                      :show-camera-button="true"
                      :field-id="field.id"
                      :applies-to="'inventory_record'"
                      :entity-id="editingRecord?.id"
                      :task-id="editingRecord?.task_id"
                      :asset-id="editingRecord?.asset_id"
                      :mode="'edit'"
                    />
                  </template>
                </van-field>
                
                <!-- 其他类型默认为文本 -->
                <van-field
                  v-else
                  v-model="customFieldData[field.name]"
                  :name="field.name"
                  :label="field.label"
                  :placeholder="`请输入${field.label}`"
                  :required="field.is_required"
                  :rules="field.is_required ? [{ required: true, message: `请输入${field.label}` }] : []"
                />
              </template>
            </van-cell-group>
          </van-form>
        </div>

        <MobilePopupFooter 
          v-if="editingRecord"
          :buttons="recordFooterButtons" 
        />
      </div>
    </van-popup>

    <!-- 盘点状态选择器 -->
    <van-popup v-model:show="showRecordStatusPicker" position="bottom">
      <van-picker
        :columns="recordStatusOptions"
        @confirm="onRecordStatusConfirm"
        @cancel="showRecordStatusPicker = false"
      />
    </van-popup>

    <!-- 自定义字段日期选择器 -->
    <van-popup v-model:show="showCustomFieldDatePicker" position="bottom">
      <van-date-picker
        v-model="customFieldDateValue"
        :title="currentCustomField?.label"
        @confirm="onCustomFieldDateConfirm"
        @cancel="showCustomFieldDatePicker = false"
      />
    </van-popup>

    <!-- 自定义字段选择器 -->
    <van-popup v-model:show="showCustomFieldSelectPicker" position="bottom">
      <van-picker
        :columns="customFieldSelectOptions"
        :title="currentCustomField?.label"
        @confirm="onCustomFieldSelectConfirm"
        @cancel="showCustomFieldSelectPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'
import { useUserStore } from '@/stores/user'
import { inventoryApi } from '@/api/inventory'
import { customFieldApi } from '@/api/custom_field'
import IntegratedSearch from '@mobile/components/business/IntegratedSearch.vue'
import MobileFileUploader from '@/mobile/components/MobileFileUploader.vue'
import MobilePopupFooter from '@/mobile/components/MobilePopupFooter.vue'
import MobileFieldValueSelector from '@/mobile/components/MobileFieldValueSelector.vue'
import { FIELD_NAMES } from '@/types/field_value'
import { highlightKeyword } from '@/utils/highlight'
import type { InventoryTask, InventoryRecord, InventoryRecordUpdate, InventoryTaskStatus, InventoryRecordStatus } from '@/types/inventory'
import type { CustomField, InventoryRecordCustomFieldValue } from '@/types/custom_field'
import { formatDate } from '@/utils/date'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 任务ID
const taskId = Number(route.params.id)

// 数据状态
const task = ref<InventoryTask | null>(null)
const recordsList = ref<InventoryRecord[]>([])
const loading = ref(false)
const recordsLoading = ref(false)
const recordsFinished = ref(false)
const activeTab = ref<string>('info')
const activeRecordStatus = ref<string>('pending')

// 搜索相关
const searchValue = ref('')
const searchField = ref('asset_number')

// 分页状态
const recordsPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 弹窗状态
const showScanDialog = ref(false)
const showManualDialog = ref(false)
const showRecordDialog = ref(false)
const showRecordStatusPicker = ref(false)
const showCustomFieldDatePicker = ref(false)
const showCustomFieldSelectPicker = ref(false)

// 自定义字段选择器相关
const currentCustomField = ref<CustomField | null>(null)
const customFieldDateValue = ref<string[]>([])
const customFieldSelectOptions = ref<any[]>([])

// 操作状态
const completing = ref(false)
const scanning = ref(false)
const searching = ref(false)
const updatingRecord = ref(false)

// 扫码相关
const scanResult = ref('')

// 手动盘点相关
const manualAssetNumber = ref('')
const foundRecord = ref<InventoryRecord | null>(null)

// 记录编辑相关
const editingRecord = ref<InventoryRecord | null>(null)
const recordFormRef = ref()

// 记录表单
const recordForm = reactive({
  status: '',
  remarks: '',
  newName: '',
  newCustodian: '',
  newUser: '',
  newCompany: '',
  newLocation: ''
})

// 盘点状态选项
const recordStatusOptions = [
  { text: '正常', value: 'normal' },
  { text: '异常', value: 'abnormal' },
  { text: '缺失', value: 'missing' },
  { text: '信息变更', value: 'info_changed' }
]

// 统计数据
const statistics = ref({
  total: 0,
  checked: 0,
  pending: 0,
  normal: 0,
  abnormal: 0,
  missing: 0,
  info_changed: 0
})

// 自定义字段相关状态
const customFields = ref<CustomField[]>([])
const customFieldData = reactive<Record<string, any>>({})
const inventoryRecordCustomFieldValues = ref<InventoryRecordCustomFieldValue[]>([])
const loadingCustomFields = ref(false)

// 调试配置
const DEBUG_MODE = import.meta.env.DEV || false

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取任务状态标签类型
const getStatusTagType = (status: InventoryTaskStatus) => {
  const statusMap: Record<InventoryTaskStatus, string> = {
    'pending': 'warning',
    'in_progress': 'primary',
    'completed': 'success'
  }
  return statusMap[status] || 'default'
}

// 获取任务状态文本
const getStatusText = (status: InventoryTaskStatus) => {
  const statusMap: Record<InventoryTaskStatus, string> = {
    'pending': '待开始',
    'in_progress': '进行中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 获取记录状态标签类型
const getRecordStatusTagType = (status: InventoryRecordStatus) => {
  const statusMap: Record<InventoryRecordStatus, string> = {
    'pending': 'default',
    'normal': 'success',
    'abnormal': 'warning',
    'missing': 'danger',
    'info_changed': 'primary'
  }
  return statusMap[status] || 'default'
}

// 获取记录状态文本
const getRecordStatusText = (status: InventoryRecordStatus) => {
  const statusMap: Record<InventoryRecordStatus, string> = {
    'pending': '待盘点',
    'normal': '正常',
    'abnormal': '异常',
    'missing': '缺失',
    'info_changed': '信息变更'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateStr?: string) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 高亮搜索关键词
const highlightText = (text: string) => {
  if (!searchValue.value || !text) {
    return text
  }
  return highlightKeyword(text, searchValue.value, 'search-highlight')
}

// 加载任务详情
const loadTaskDetail = async () => {
  try {
    loading.value = true
    const response = await inventoryApi.getInventoryTask(taskId)
    task.value = response.data || response
  } catch (error) {
    console.error('加载任务详情失败:', error)
    showToast('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await inventoryApi.getInventoryTaskStatistics(taskId)
    const stats = response.data || response
    statistics.value = {
      total: stats.total,
      checked: stats.checked,
      pending: stats.pending,
      normal: stats.normal,
      abnormal: stats.abnormal,
      missing: stats.missing,
      info_changed: stats.info_changed
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    showToast('加载统计数据失败')
  }
}

// 加载盘点记录
const loadRecords = async (reset = false) => {
  try {
    if (reset) {
      recordsPagination.current = 1
      recordsList.value = []
      recordsFinished.value = false
    }

    recordsLoading.value = true

    const params = {
      skip: (recordsPagination.current - 1) * recordsPagination.pageSize,
      limit: recordsPagination.pageSize,
      status: activeRecordStatus.value === 'all' ? undefined : activeRecordStatus.value,
      keyword: searchValue.value || undefined
    }

    const response = await inventoryApi.getInventoryRecords(taskId, params)
    const { data, total } = response.data || response

    if (reset) {
      recordsList.value = data
    } else {
      recordsList.value = [...recordsList.value, ...data]
    }

    recordsPagination.total = total
    recordsPagination.current++

    // 检查是否还有更多数据
    if (recordsList.value.length >= total) {
      recordsFinished.value = true
    }

  } catch (error) {
    console.error('加载盘点记录失败:', error)
    showToast('加载盘点记录失败')
  } finally {
    recordsLoading.value = false
  }
}

// 记录状态筛选
const handleRecordStatusChange = () => {
  recordsPagination.current = 1
  recordsList.value = []
  recordsFinished.value = false
  loadRecords(true)
}

// 处理搜索
const handleSearch = async (value: string, field: string) => {
  searchValue.value = value
  searchField.value = field
  // 重置页码并刷新数据
  recordsPagination.current = 1
  await loadRecords(true)
}

// 处理搜索清空
const handleSearchClear = async () => {
  searchValue.value = ''
  searchField.value = 'asset_number' // 重置为默认搜索字段
  recordsPagination.current = 1
  await loadRecords(true)
}

// 完成任务
const handleCompleteTask = async () => {
  try {
    await showConfirmDialog({
      title: '确认完成',
      message: '确定要完成这个盘点任务吗？完成后无法继续盘点。'
    })

    completing.value = true
    
    // 先尝试直接完成任务
    const response = await inventoryApi.completeInventoryTaskWithChanges(taskId)
    const result = response.data || response
    
    // 检查是否有待处理的信息变更
    if (result.status === 'pending_changes') {
      // 询问用户是否自动应用变更
      await showConfirmDialog({
        title: '发现信息变更',
        message: `发现 ${result.changes_count} 个资产信息变更记录，是否自动应用这些变更？`
      })
      
      // 用户确认后自动应用变更并完成任务
      const finalResponse = await inventoryApi.completeInventoryTaskWithChanges(taskId, {
        auto_apply_changes: true
      })
      const finalResult = finalResponse.data || finalResponse
      
      showSuccessToast(`任务已完成，已应用 ${finalResult.applied_changes || 0} 个信息变更`)
    } else {
      showSuccessToast('任务已完成')
    }
    
    // 重新加载任务详情
    await loadTaskDetail()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成任务失败:', error)
      showToast('完成任务失败')
    }
  } finally {
    completing.value = false
  }
}

// 开始扫描
const startScan = async () => {
  try {
    scanning.value = true
    
    // 模拟扫码过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟扫码结果
    const scannedCode = `ASSET_${Date.now()}`
    
    // 将扫码结果填入搜索框
    searchValue.value = scannedCode
    
    // 查找对应的记录
    const record = recordsList.value.find(r => 
      r.asset.asset_number === scannedCode || 
      r.asset.name.includes(scannedCode)
    )
    
    if (record) {
      showSuccessToast('扫码成功，已定位到资产')
      // 可以考虑自动跳转到该记录
      handleRecordClick(record)
    } else {
      showToast('扫码成功，但未找到对应资产')
    }
  } catch (error) {
    console.error('扫码失败:', error)
    showToast('扫码失败，请重试')
  } finally {
    scanning.value = false
  }
}

// 扫码确认
const handleScanConfirm = () => {
  if (scanResult.value) {
    // 根据扫码结果查找记录
    const record = recordsList.value.find(r => r.asset.asset_number === scanResult.value)
    if (record) {
      handleRecordClick(record)
      showToast('已定位到资产记录')
    } else {
      showToast('未找到对应的资产记录')
    }
  }
  showScanDialog.value = false
}

// 搜索资产
const searchAsset = async () => {
  if (!manualAssetNumber.value) {
    showToast('请输入资产编号')
    return
  }

  try {
    searching.value = true
    
    // 在当前记录中查找
    const record = recordsList.value.find(r => r.asset.asset_number === manualAssetNumber.value)
    if (record) {
      foundRecord.value = record
      showToast('找到资产')
    } else {
      foundRecord.value = null
      showToast('未找到对应的资产')
    }
  } catch (error) {
    console.error('搜索资产失败:', error)
    showToast('搜索资产失败')
  } finally {
    searching.value = false
  }
}

// 手动盘点确认
const handleManualConfirm = () => {
  if (foundRecord.value) {
    handleRecordClick(foundRecord.value)
  }
  showManualDialog.value = false
  manualAssetNumber.value = ''
  foundRecord.value = null
}

// 加载自定义字段
const loadCustomFields = async () => {
  try {
    loadingCustomFields.value = true
    const response = await customFieldApi.getActiveCustomFields({ applies_to: 'inventory_record' })
    customFields.value = response.data
  } catch (error) {
    console.error('加载自定义字段失败:', error)
    // 自定义字段加载失败不应影响基础功能
  } finally {
    loadingCustomFields.value = false
  }
}

// 加载盘点记录的自定义字段值
const loadInventoryRecordCustomFieldValues = async (record: InventoryRecord) => {
  try {
    let response
    if (record.id != null) {
      // 实际盘点记录
      response = await customFieldApi.getInventoryRecordCustomFieldValues(record.id)
    } else {
      // 虚拟盘点记录
      response = await customFieldApi.getInventoryVirtualRecordCustomFieldValues(record.task_id, record.asset_id)
    }
    
    inventoryRecordCustomFieldValues.value = response.data
    
    // 初始化自定义字段数据
    Object.keys(customFieldData).forEach(key => delete customFieldData[key])
    
    // 预填充现有值
    inventoryRecordCustomFieldValues.value.forEach(value => {
      if (value.custom_field) {
        customFieldData[value.custom_field.name] = value.value
      }
    })
  } catch (error) {
    console.error('加载自定义字段值失败:', error)
    // 清空自定义字段数据
    Object.keys(customFieldData).forEach(key => delete customFieldData[key])
  }
}

// 保存自定义字段值
const saveCustomFieldValues = async (record: InventoryRecord) => {
  if (customFields.value.length === 0) return

  try {
    const values = customFields.value.map(field => ({
      custom_field_id: field.id,
      value: customFieldData[field.name] || ''
    }))

    if (record.id) {
      await customFieldApi.batchSetInventoryRecordCustomFieldValues(record.id, { values })
    } else {
      await customFieldApi.batchSetInventoryVirtualRecordCustomFieldValues(record.task_id, record.asset_id, { values })
    }
  } catch (error) {
    console.error('保存自定义字段值失败:', error)
    showToast('保存自定义字段值失败')
  }
}

// 获取字段选项显示文本
const getFieldOptionText = (field: CustomField, value: any): string => {
  if (field.field_type === 'select' || field.field_type === 'radio') {
    const option = field.options?.choices?.find((choice: any) => choice.value === value)
    return option ? option.label : value
  } else if (field.field_type === 'checkbox') {
    if (Array.isArray(value)) {
      const options = field.options?.choices || []
      return value.map((v: any) => {
        const option = options.find((choice: any) => choice.value === v)
        return option ? option.label : v
      }).join(', ')
    }
  }
  return value
}

// 显示自定义字段选择器
const showFieldPicker = (field: CustomField) => {
  if (field.field_type === 'select' || field.field_type === 'radio') {
    currentCustomField.value = field
    const option = field.options?.choices?.find((choice: any) => choice.value === customFieldData[field.name])
    customFieldSelectOptions.value = field.options?.choices || []
    showCustomFieldSelectPicker.value = true
  }
}

// 记录点击
const handleRecordClick = async (record: InventoryRecord) => {
  if (DEBUG_MODE) {
    console.log('打开盘点记录编辑弹窗，原始记录数据:', record)
  }
  
  editingRecord.value = record
  recordForm.status = record.status
  recordForm.remarks = record.remarks || ''
  recordForm.newName = record.new_name || ''
  recordForm.newCustodian = record.new_custodian || ''
  recordForm.newUser = record.new_user || ''
  recordForm.newCompany = record.new_company || ''
  recordForm.newLocation = record.new_location || ''
  
  if (DEBUG_MODE) {
    console.log('初始化表单数据:', {
      status: recordForm.status,
      newName: recordForm.newName,
      newCustodian: recordForm.newCustodian,
      newUser: recordForm.newUser,
      newCompany: recordForm.newCompany,
      newLocation: recordForm.newLocation
    })
  }
  
  // 加载自定义字段值
  await loadInventoryRecordCustomFieldValues(record)
  
  showRecordDialog.value = true
}

// 快速盘点记录
const handleCheckRecord = async (record: InventoryRecord, status: InventoryRecordStatus) => {
  try {
    // 获取当前用户信息
    const currentUser = userStore.userInfo?.username || userStore.userInfo?.email || '未知用户'
    
    const updateData: InventoryRecordUpdate = {
      status,
      checked_by: currentUser,
      // 不自动设置备注，让用户自己填写或保持为空
      remarks: undefined
    }

    // 如果是虚拟记录（没有ID），使用新的API接口
    if (record.id != null) {
      await inventoryApi.updateInventoryRecord(record.id as number, updateData)
    } else {
      await inventoryApi.updateInventoryRecordByAsset(record.task_id, record.asset_id, updateData)
    }
    
    showSuccessToast('盘点记录已更新')
    
    // 重新加载统计数据和记录列表
    loadStatistics()
    loadRecords(true)
  } catch (error) {
    console.error('更新盘点记录失败:', error)
    showToast('更新盘点记录失败')
  }
}

// 编辑记录
const handleEditRecord = (record: InventoryRecord) => {
  handleRecordClick(record)
}

// 盘点状态选择确认
const onRecordStatusConfirm = ({ selectedOptions }: any) => {
  const newStatus = selectedOptions[0].value
  
  if (DEBUG_MODE) {
    console.log('状态变更:', recordForm.status, '->', newStatus)
  }
  
  recordForm.status = newStatus
  showRecordStatusPicker.value = false
  
  // 如果状态从非info_changed变更为info_changed，需要重置变更字段
  if (newStatus === 'info_changed') {
    if (DEBUG_MODE) {
      console.log('状态变更为信息变更，初始化变更字段')
    }
    // 如果字段为空，使用原始资产数据作为初始值（便于用户修改）
    if (!recordForm.newName && editingRecord.value?.asset.name) {
      recordForm.newName = ''
    }
    if (!recordForm.newCustodian && editingRecord.value?.asset.custodian) {
      recordForm.newCustodian = ''
    }
    if (!recordForm.newUser && editingRecord.value?.asset.user) {
      recordForm.newUser = ''
    }
    if (!recordForm.newCompany && editingRecord.value?.asset.company) {
      recordForm.newCompany = ''
    }
    if (!recordForm.newLocation && editingRecord.value?.asset.location) {
      recordForm.newLocation = ''
    }
  }
}

// 更新盘点记录
const handleUpdateRecord = async () => {
  if (!editingRecord.value) return

  try {
    await recordFormRef.value.validate()

    if (DEBUG_MODE) {
      console.log('提交前的表单数据:', {
        status: recordForm.status,
        remarks: recordForm.remarks,
        newName: recordForm.newName,
        newCustodian: recordForm.newCustodian,
        newUser: recordForm.newUser,
        newCompany: recordForm.newCompany,
        newLocation: recordForm.newLocation
      })
    }

    // 如果是信息变更状态，检查是否至少填写了一项变更信息
    if (recordForm.status === 'info_changed') {
      const hasChanges = [
        recordForm.newName,
        recordForm.newCustodian,
        recordForm.newUser,
        recordForm.newCompany,
        recordForm.newLocation
      ].some(value => value && value.trim() !== '')

      if (DEBUG_MODE) {
        console.log('变更字段检查:', {
          hasChanges,
          fields: {
            newName: recordForm.newName,
            newCustodian: recordForm.newCustodian,
            newUser: recordForm.newUser,
            newCompany: recordForm.newCompany,
            newLocation: recordForm.newLocation
          }
        })
      }

      if (!hasChanges) {
        showToast('信息变更状态下至少需要填写一项变更信息')
        return
      }
    }

    updatingRecord.value = true

    // 获取当前用户信息
    const currentUser = userStore.userInfo?.username || userStore.userInfo?.email || '未知用户'
    
    const updateData: InventoryRecordUpdate = {
      status: recordForm.status as InventoryRecordStatus,
      remarks: recordForm.remarks,
      checked_by: currentUser,
      new_name: recordForm.newName || undefined,
      new_custodian: recordForm.newCustodian || undefined,
      new_user: recordForm.newUser || undefined,
      new_company: recordForm.newCompany || undefined,
      new_location: recordForm.newLocation || undefined
    }

    if (DEBUG_MODE) {
      console.log('即将提交的更新数据:', updateData)
    }

    // 如果是虚拟记录（没有ID），使用新的API接口
    if (editingRecord.value.id != null) {
      await inventoryApi.updateInventoryRecord(editingRecord.value.id, updateData)
    } else {
      await inventoryApi.updateInventoryRecordByAsset(editingRecord.value.task_id, editingRecord.value.asset_id, updateData)
    }
    
    // 保存自定义字段值
    await saveCustomFieldValues(editingRecord.value)
    
    if (DEBUG_MODE) {
      console.log('盘点记录更新成功')
    }
    showSuccessToast('盘点记录已更新')
    
    showRecordDialog.value = false
    editingRecord.value = null
    
    // 重新加载统计数据和记录列表
    loadStatistics()
    loadRecords(true)
  } catch (error: any) {
    if (error.name !== 'ValidationError') {
      console.error('更新盘点记录失败:', error)
      showToast('更新盘点记录失败')
    }
  } finally {
    updatingRecord.value = false
  }
}

// 显示日期选择器
const showDatePicker = (field: CustomField) => {
  currentCustomField.value = field
  const dateStr = customFieldData[field.name] || ''
  // 将日期字符串转换为DatePicker需要的数组格式
  if (dateStr) {
    customFieldDateValue.value = dateStr.split('-')
  } else {
    // 默认为当前日期
    const now = new Date()
    customFieldDateValue.value = [
      now.getFullYear().toString(),
      (now.getMonth() + 1).toString().padStart(2, '0'),
      now.getDate().toString().padStart(2, '0')
    ]
  }
  showCustomFieldDatePicker.value = true
}

// 日期选择确认
const onCustomFieldDateConfirm = ({ selectedValues }: any) => {
  if (currentCustomField.value && selectedValues) {
    // DatePicker 返回的是数组格式 ['2023', '12', '15']，需要转换为日期字符串
    const dateStr = selectedValues.join('-')
    customFieldData[currentCustomField.value.name] = dateStr
  }
  showCustomFieldDatePicker.value = false
}

// 选择器确认
const onCustomFieldSelectConfirm = ({ selectedOptions }: any) => {
  if (currentCustomField.value && selectedOptions.length > 0) {
    customFieldData[currentCustomField.value.name] = selectedOptions[0].value
  }
  showCustomFieldSelectPicker.value = false
}

// 记录详情底部按钮配置
const recordFooterButtons = computed(() => [
  {
    text: '保存记录',
    type: 'primary' as const,
    loading: updatingRecord.value,
    onClick: handleUpdateRecord
  }
])

onMounted(async () => {
  // 确保用户信息已加载
  try {
    if (!userStore.userInfo) {
      await userStore.initializeAuth()
    }
  } catch (error) {
    console.warn('初始化用户信息失败:', error)
  }
  
  loadTaskDetail()
  loadStatistics()
  loadRecords(true)
  loadCustomFields()
})
</script>

<style lang="scss" scoped>
.mobile-inventory-task {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.loading-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-content {
  padding-bottom: 20px;
}

.tab-content {
  padding-bottom: 16px;
}

.records-filter {
  background: white;
  margin-bottom: 8px;
  
  :deep(.van-tabs__wrap) {
    padding: 0 16px;
  }
  
  :deep(.van-tab) {
    flex: none;
    margin-right: 20px;
    font-size: 14px;
  }
  
  :deep(.van-tabs__line) {
    background-color: #1989fa;
  }
}

.search-section {
  padding: 12px 16px;
  background: white;
  margin-bottom: 8px;
  
  :deep(.simple-search) {
    .search-container {
      align-items: stretch;
      gap: 8px;
    }
    
    .search-field-selector {
      width: 90px;
      
      .van-dropdown-menu__item {
        min-width: 90px;
      }
      
      .van-dropdown-menu__title {
        font-size: 13px;
        padding: 0 8px;
        height: 36px;
        line-height: 36px;
      }
    }
    
    .search-input {
      .van-search {
        padding: 0;
        background: #f7f8fa;
        border-radius: 6px;
      }
      
      .van-search__content {
        background: #f7f8fa;
        border-radius: 6px;
        height: 36px;
      }
      
      .van-field__control {
        height: 36px;
        line-height: 36px;
      }
    }
  }
}

.record-summary {
  color: #1989fa;
  font-weight: 600;
}

.records-list {
  padding: 0 16px;
}

.record-item {
  margin-bottom: 12px;
  
  :deep(.van-card) {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }
  
  :deep(.van-card__header) {
    padding: 16px 16px 0;
  }
  
  :deep(.van-card__content) {
    padding: 12px 16px;
  }
  
  :deep(.van-card__footer) {
    padding: 0 16px 16px;
  }
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  .asset-name {
    font-weight: 600;
    font-size: 16px;
    color: #323233;
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
  }
  
  .van-tag {
    flex-shrink: 0;
  }
}

.record-desc {
  .asset-info,
  .check-info {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      color: #969799;
      margin-right: 8px;
      flex-shrink: 0;
      min-width: 60px;
    }
    
    span:not(.label) {
      flex: 1;
      word-break: break-all;
    }
  }
  
  .remarks {
    margin-top: 12px;
    padding: 12px;
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
    
    .label {
      color: #969799;
      margin-right: 8px;
      font-weight: 500;
    }
  }
}

.record-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  
  .van-button {
    flex: 1;
    height: 36px;
    font-size: 13px;
  }
}

.scan-content,
.manual-content {
  padding: 20px;
  
  .scan-buttons,
  .manual-buttons {
    display: flex;
    gap: 12px;
    margin: 20px 0;
    
    .van-button {
      flex: 1;
      height: 40px;
    }
  }
  
  .scan-tips,
  .found-asset {
    p {
      margin: 8px 0;
      font-size: 13px;
      color: #969799;
      text-align: center;
      line-height: 1.5;
    }
  }
  
  .found-asset {
    background: #f7f8fa;
    padding: 16px;
    border-radius: 8px;
    margin-top: 16px;
    border-left: 4px solid #1989fa;
    
    p {
      color: #323233;
      text-align: left;
      margin: 4px 0;
    }
  }
}

.record-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ebedf0;
    background: white;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }
    
    .van-icon {
      font-size: 20px;
      color: #969799;
      padding: 4px;
    }
  }
  
  .popup-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: #f7f8fa;
  }
  

}

:deep(.van-cell-group) {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  
  .van-cell-group__title {
    padding: 16px 16px 8px;
    color: #323233;
    font-weight: 600;
  }
}

:deep(.van-cell) {
  padding: 12px 16px;
  
  .van-cell__title {
    color: #646566;
    font-size: 14px;
    flex: 0 0 80px;
  }
  
  .van-cell__value {
    color: #323233;
    font-size: 14px;
    flex: 1;
    text-align: right;
    min-width: 0;
  }
}

:deep(.van-field) {
  padding: 12px 16px;
  
  .van-field__label {
    color: #646566;
    font-size: 14px;
  }
  
  .van-field__control {
    font-size: 14px;
    color: #323233;
  }
}

:deep(.van-empty) {
  padding: 80px 0;
  
  .van-empty__description {
    color: #969799;
    font-size: 14px;
  }
}

// 搜索高亮样式
:deep(.search-highlight) {
  background-color: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

// 标签样式优化
:deep(.van-tag) {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

:deep(.van-tag--primary) {
  background: linear-gradient(135deg, #1989fa 0%, #1677ff 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(25, 137, 250, 0.3);
}

:deep(.van-tag--warning) {
  background: linear-gradient(135deg, #ff976a 0%, #f79346 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(255, 151, 106, 0.3);
}

:deep(.van-tag--success) {
  background: linear-gradient(135deg, #52c41a 0%, #41a817 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

:deep(.van-tag--danger) {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff1a1a 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
}

// 按钮样式优化
:deep(.van-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.van-button--small) {
  height: 32px;
  padding: 0 12px;
  font-size: 12px;
}

.date-range {
  font-size: 14px;
  color: #646566;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 