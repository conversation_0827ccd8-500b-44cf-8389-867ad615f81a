import type { Asset } from './asset'

// 盘点任务状态
export type InventoryTaskStatus = 'pending' | 'in_progress' | 'completed'

// 盘点记录状态
export type InventoryRecordStatus = 'pending' | 'normal' | 'abnormal' | 'missing' | 'info_changed'

// 盘点任务
export interface InventoryTask {
  id: number
  name: string
  description?: string
  status: InventoryTaskStatus
  start_date: string
  end_date: string
  created_by: string
  created_at: string
  updated_at: string
  inventory_records: InventoryRecord[]
}

// 创建盘点任务
export interface InventoryTaskCreate {
  name: string
  description?: string
  start_date: string
  end_date: string
  created_by: string
}

// 更新盘点任务
export interface InventoryTaskUpdate {
  name?: string
  description?: string
  status?: InventoryTaskStatus
  start_date?: string
  end_date?: string
}

// 盘点记录
export interface InventoryRecord {
  id?: number
  task_id: number
  asset_id: number
  status: InventoryRecordStatus
  remarks?: string
  checked_by?: string
  checked_at?: string
  new_name?: string
  new_specification?: string
  new_status?: string
  new_company?: string
  new_custodian?: string
  new_custodian_department?: string
  new_user?: string
  new_user_department?: string
  new_location?: string
  new_production_number?: string
  new_price?: number
  new_supplier?: string
  new_manufacturer?: string
  new_purchaser?: string
  created_at: string
  updated_at: string
  asset: Asset
}

// 更新盘点记录
export interface InventoryRecordUpdate {
  status?: InventoryRecordStatus
  remarks?: string
  checked_by?: string
  new_name?: string
  new_specification?: string
  new_custodian?: string
  new_custodian_department?: string
  new_user?: string
  new_user_department?: string
  new_location?: string
  new_company?: string
  new_status?: string
  new_production_number?: string
  new_price?: number
  new_supplier?: string
  new_manufacturer?: string
  new_purchaser?: string
} 