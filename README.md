# OPS-Platform 现代化运维管理平台

<div align="center">
  <img src="frontend/public/logo.png" alt="OPS-Platform Logo" width="200"/>
  <p>高效 · 安全 · 集成 · 智能</p>
</div>

## 🚀 平台概述

OPS-Platform是一套面向企业级IT环境的综合运维管理平台，采用前后端分离架构，基于现代化技术栈构建。本平台致力于解决企业运维管理中的痛点问题，提供统一的资产管理、AD域服务集成、自动化运维流程和数据可视化能力。

## ✨ 平台特点

### 📊 全面的可视化监控
- 直观的仪表盘展示系统关键指标
- 资产分布与状态的实时可视化
- 多维度数据分析能力支持决策

### 🔄 深度集成与自动化
- 与Microsoft Active Directory的无缝集成
- 与泛微OA系统的数据同步与互通
- 自动化的人员信息同步与账户管理

### 🛡️ 企业级安全架构
- 基于JWT的安全认证机制
- 细粒度的权限控制系统
- 操作日志追踪与审计

### 🔧 灵活的定制与扩展
- 自定义资产属性与字段
- 可配置的工作流与审批流程
- 插件化架构便于功能扩展

### 📱 响应式设计体验
- 适配多种设备的响应式界面
- 人性化的操作交互体验
- 基于Element Plus的现代UI设计

## 🔥 核心功能模块

### 👤 用户与权限管理
- 精细的RBAC权限体系
- 多因素认证支持
- 管理员活动审计日志

### 🖥️ AD域管理
- AD用户与组批量管理
- 组织单位(OU)结构维护
- 用户权限与安全组策略配置
- 人员数据自动同步与变更处理

#### AD人员同步详细流程

系统提供了数据同步机制，通过本地数据库实现人员数据自动同步到Active Directory，支持手动触发和定时任务两种同步方式。AD同步不再直接连接泛微OA数据库，而是从本地已同步的数据进行读取，提高了系统独立性和稳定性。

```mermaid
flowchart TD
    Start([开始同步流程]) --> ConfigCheck{检查同步配置}
    ConfigCheck -->|配置有效| SourceSelect{选择数据源}
    ConfigCheck -->|配置无效| ErrorConfig[配置错误处理]
    ErrorConfig --> End([结束流程])
    
    SourceSelect -->|全公司| FetchAllData[从本地数据库获取全部人员数据]
    SourceSelect -->|指定部门| FetchDeptData[从本地数据库获取部门人员数据]
    SourceSelect -->|指定公司| FetchCompanyData[从本地数据库获取公司人员数据]
    
    FetchAllData --> ValidateData[数据校验与转换]
    FetchDeptData --> ValidateData
    FetchCompanyData --> ValidateData
    
    ValidateData --> ADConnection{连接AD服务器}
    ADConnection -->|连接成功| OrgSync{是否需要同步组织?}
    ADConnection -->|连接失败| ConnectionError[连接错误处理]
    ConnectionError --> LogError[记录错误日志]
    LogError --> End
    
    OrgSync -->|是| CreateOU[创建组织单位]
    OrgSync -->|否| ProcessUsers[处理用户数据]
    CreateOU --> ProcessUsers
    
    ProcessUsers --> BatchQuery[批量查询用户状态]
    BatchQuery --> UserProcess{用户处理}
    
    UserProcess -->|新用户| CreateUser[创建新用户]
    UserProcess -->|已存在| UpdateUser[更新用户信息]
    UserProcess -->|禁用| EnableUser[启用用户]
    
    CreateUser --> GeneratePassword[生成安全密码]
    GeneratePassword --> SetUserProps[设置用户属性]
    
    UpdateUser --> UpdateProps[更新用户属性]
    UpdateProps --> MoveCheck{需要移动OU?}
    MoveCheck -->|是| MoveUser[移动用户到新OU]
    MoveCheck -->|否| GroupCheck{需要更新安全组?}
    MoveUser --> GroupCheck
    
    SetUserProps --> GroupCheck
    EnableUser --> GroupCheck
    
    GroupCheck -->|是| ProcessGroups[处理安全组]
    GroupCheck -->|否| ProcessInactive{处理非活跃用户?}
    
    ProcessGroups --> CreateGroups[创建部门安全组]
    CreateGroups --> AddToGroups[添加用户到安全组]
    AddToGroups --> ProcessInactive
    
    ProcessInactive -->|是| DisableInactive[禁用非活跃用户]
    ProcessInactive -->|否| GenerateReport[生成同步报告]
    DisableInactive --> GenerateReport
    
    GenerateReport --> UpdateLastSync[更新最后同步时间]
    UpdateLastSync --> LogHistory[记录同步历史]
    LogHistory --> SyncComplete([同步完成])
    SyncComplete --> End
    
    subgraph "数据准备阶段"
        ConfigCheck
        SourceSelect
        FetchAllData
        FetchDeptData
        FetchCompanyData
        ValidateData
    end
    
    subgraph "组织同步阶段"
        OrgSync
        CreateOU
    end
    
    subgraph "用户处理阶段"
        BatchQuery
        UserProcess
        CreateUser
        UpdateUser
        EnableUser
        GeneratePassword
        SetUserProps
        UpdateProps
        MoveCheck
        MoveUser
    end
    
    subgraph "安全组处理阶段"
        GroupCheck
        ProcessGroups
        CreateGroups
        AddToGroups
    end
    
    subgraph "完成阶段"
        ProcessInactive
        DisableInactive
        GenerateReport
        UpdateLastSync
        LogHistory
        SyncComplete
    end
    
    style Start fill:#d4f1f9,stroke:#0077b6,stroke-width:2px
    style End fill:#d4f1f9,stroke:#0077b6,stroke-width:2px
    style SyncComplete fill:#d1e7dd,stroke:#198754,stroke-width:2px
    style ErrorConfig fill:#f8d7da,stroke:#dc3545,stroke-width:2px
    style ConnectionError fill:#f8d7da,stroke:#dc3545,stroke-width:2px
    style LogError fill:#f8d7da,stroke:#dc3545,stroke-width:2px
    style CreateUser fill:#e9ecef,stroke:#495057,stroke-width:2px
    style UpdateUser fill:#e9ecef,stroke:#495057,stroke-width:2px
    style ProcessGroups fill:#fff3cd,stroke:#ffc107,stroke-width:2px
    style DisableInactive fill:#f8d7da,stroke:#dc3545,stroke-width:2px
```

#### 数据流转架构

```mermaid
flowchart LR
    EcologyDB[(泛微OA数据库)] -->|初始导入/定时同步| LocalDB[(本地数据库)]
    LocalDB -->|读取人员/组织数据| ADSync[AD同步服务]
    ADSync -->|同步用户/组织结构| ActiveDirectory[(Active Directory)]
    
    style EcologyDB fill:#ffe0b2,stroke:#e65100,stroke-width:2px
    style LocalDB fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    style ADSync fill:#bbdefb,stroke:#1565c0,stroke-width:2px
    style ActiveDirectory fill:#e1bee7,stroke:#6a1b9a,stroke-width:2px
```

#### 人员同步策略配置说明

| 配置项 | 说明 | 默认值 |
|-------|------|-------|
| 同步数据源 | 可选全公司、指定部门或特定人员 | 全公司 |
| 同步频率 | 支持手动、每日、每周、每月定时自动同步 | 手动 |
| 目标OU | 同步用户的目标组织单位路径 | CN=Users,DC=domain,DC=com |
| 账户策略 | 包括初始密码策略、密码复杂度、下次登录修改密码等 | 随机强密码+下次登录修改 |
| 组织同步 | 是否根据部门结构自动创建OU层级 | 启用 |
| 部门映射 | 泛微部门与AD组织单位的映射规则 | 直接映射 |
| 属性映射 | 泛微用户字段与AD属性的映射配置 | 基本字段映射 |
| 安全组分配 | 基于部门、职位自动分配AD安全组 | 不自动分配 |
| 非活跃用户处理 | 处理离职、停用等非活跃用户的策略 | 禁用账户 |
| 自动创建安全组 | 是否自动为部门创建对应的安全组 | 禁用 |
| 用户加入部门组 | 是否自动将用户添加到对应部门的安全组 | 禁用 |
| 部门变更移动 | 是否在人员部门变更时自动移动用户到对应的OU | 启用 |
| 安全组更新 | 是否在人员部门变更时自动调整安全组 | 禁用 |

#### 同步功能特性

1. **智能用户管理**
   - 自动创建新用户账户
   - 更新现有用户信息
   - 移动用户到正确的组织单位
   - 处理非活跃用户状态

2. **组织结构同步**
   - 自动创建和维护OU层级结构
   - 支持公司级和部门级的组织结构
   - 智能处理组织变更

3. **安全组管理**
   - 支持部门安全组的自动创建
   - 自动将用户添加到对应部门安全组
   - 处理部门变更时的安全组调整

4. **批量处理优化**
   - 采用批量查询和处理机制
   - 支持大量用户的高效同步
   - 自动重试失败的同步操作

5. **同步日志记录**
   - 详细记录同步操作历史
   - 统计同步结果和变更数量
   - 支持导出同步报告

6. **错误处理机制**
   - 智能识别和处理同步异常
   - 详细的错误日志记录
   - 失败操作的自动重试

7. **性能优化**
   - 批量处理减少AD操作次数
   - 智能缓存减少重复查询
   - 异步处理提高响应速度

### 📦 资产管理
- 全生命周期资产跟踪
- 多维度资产分类与标签
- 资产关联关系可视化
- 资产变更历史记录

### 📋 资产盘点
- 定期盘点计划制定
- 盘点进度实时监控
- 异常资产高亮展示
- 盘点报告自动生成

### 🔄 泛微数据集成
- 人员组织架构定期同步到本地数据库
- 增量数据自动更新到本地数据库
- 数据一致性验证与同步状态记录
- 本地数据库作为中间层，提供数据独立性
- 解耦泛微系统与AD同步，增强系统稳定性

### 📲 移动端支持
- 核心功能移动端访问
- 移动端资产盘点操作
- 即时通知与提醒

## 🔌 系统架构设计

### 整体架构图

```mermaid
graph TD
    Client["客户端(浏览器/移动端)"] -->|HTTP/HTTPS| LoadBalancer["负载均衡"]
    LoadBalancer --> Frontend["前端应用集群"]
    Frontend -->|RESTful API| ApiGateway["API网关"]
    ApiGateway --> Backend["后端服务集群"]
    Backend -->|读写| Database["数据库服务"]
    Backend -->|认证| AuthService["认证服务"]
    Backend -->|集成| ExternalSystems["外部系统接口"]
    
    subgraph "外部系统"
    ExternalSystems --> AD["Active Directory"]
    ExternalSystems --> Ecology["泛微OA系统"]
    ExternalSystems --> Monitoring["监控系统"]
    end
    
    subgraph "后端服务"
    Backend --> AssetService["资产服务"]
    Backend --> ADService["AD管理服务"]
    Backend --> UserService["用户服务"]
    Backend --> InventoryService["盘点服务"]
    Backend --> SyncService["数据同步服务"]
    end
    
    subgraph "前端模块"
    Frontend --> AssetUI["资产管理界面"]
    Frontend --> ADUI["AD管理界面"]
    Frontend --> DashboardUI["仪表盘"]
    Frontend --> InventoryUI["盘点界面"]
    Frontend --> UserManagementUI["用户管理"]
    end
    
    style Client fill:#f9f9f9,stroke:#333,stroke-width:1px
    style Frontend fill:#d6e8ff,stroke:#333,stroke-width:1px
    style Backend fill:#d6ffe8,stroke:#333,stroke-width:1px
    style Database fill:#ffe8d6,stroke:#333,stroke-width:1px
    style ExternalSystems fill:#f0d6ff,stroke:#333,stroke-width:1px
```

### 技术组件关系

```mermaid
flowchart TB
    subgraph Frontend["前端应用层"]
        direction TB
        Vue3["Vue.js 3"] --> TypeScript["TypeScript"]
        Vue3 --> Pinia["Pinia状态管理"]
        Vue3 --> VueRouter["Vue Router"]
        Vue3 --> ElementPlus["Element Plus UI"]
        TypeScript --> Axios["Axios HTTP客户端"]
        Axios --> APIClient["API客户端"]
    end
    
    subgraph Backend["后端服务层"]
        direction TB
        FastAPI["FastAPI框架"] --> Pydantic["Pydantic模型"]
        FastAPI --> SQLAlchemy["SQLAlchemy ORM"]
        FastAPI --> Middleware["中间件"]
        Middleware --> Authentication["认证中间件"]
        Middleware --> ErrorHandler["错误处理"]
        Middleware --> Logging["日志记录"]
        SQLAlchemy --> Alembic["Alembic迁移"]
        SQLAlchemy --> Database2["数据库连接"]
    end
    
    subgraph Integration["集成服务层"]
        direction TB
        PythonLDAP["python-ldap"] --> ADManager["AD管理器"]
        RequestsLib["Requests库"] --> EcologyAPI["泛微API客户端"]
        Scheduler["后台任务调度"] --> SyncService["同步服务"]
    end
    
    subgraph Data["数据持久层"]
        SQLite["SQLite数据库"]
    end
    
    Frontend --> |API请求| Backend
    Backend --> |集成| Integration
    Backend --> |持久化| Data
    Integration --> |数据交换| ExternalSys["外部系统"]
    
    style Frontend fill:#bbdefb,stroke:#333,stroke-width:1px
    style Backend fill:#c8e6c9,stroke:#333,stroke-width:1px
    style Integration fill:#d1c4e9,stroke:#333,stroke-width:1px
    style Data fill:#ffecb3,stroke:#333,stroke-width:1px
```

### 核心数据流

```mermaid
sequenceDiagram
    actor User as 运维人员
    participant UI as 前端界面
    participant API as 后端API
    participant DB as 数据库
    participant AD as Active Directory
    participant Ecology as 泛微系统
    
    Note over User,Ecology: 用户认证流程
    User->>UI: 访问系统
    UI->>API: 发送登录请求
    API->>DB: 验证用户信息
    DB-->>API: 返回用户数据
    API-->>UI: 生成JWT令牌
    UI-->>User: 登录成功
    
    Note over User,Ecology: AD账户管理流程
    User->>UI: 查看/管理AD账户
    UI->>API: 请求AD数据
    API->>AD: LDAP查询
    AD-->>API: 返回AD数据
    API-->>UI: 格式化数据
    UI-->>User: 展示AD账户信息
    
    Note over User,Ecology: 人员数据同步流程
    User->>UI: 触发数据同步
    UI->>API: 同步请求
    API->>DB: 查询本地人员数据
    DB-->>API: 返回人员和组织信息
    API->>AD: 同步到AD
    AD-->>API: 操作结果
    API-->>UI: 同步状态
    UI-->>User: 显示结果
    
    Note over User,Ecology: 泛微数据初始化/更新流程
    User->>UI: 手动触发泛微数据同步
    UI->>API: 同步请求
    API->>Ecology: 获取人员数据
    Ecology-->>API: 返回人员信息
    API->>DB: 更新本地数据
    API-->>UI: 同步完成状态
    UI-->>User: 显示结果
```

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue.js 3
- **类型系统**: TypeScript 4.5+
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **路由管理**: Vue Router
- **CSS预处理**: SCSS

### 后端技术
- **语言**: Python 3.8+
- **Web框架**: FastAPI
- **ORM**: SQLAlchemy
- **数据验证**: Pydantic
- **数据库**: SQLite (可扩展至PostgreSQL)
- **迁移工具**: Alembic
- **AD集成**: python-ldap
- **认证**: PyJWT

## 📁 项目结构

```
OPS-Platform/
├── frontend/                   # 前端项目
│   ├── src/                    # 源代码
│   │   ├── api/                # API请求封装
│   │   ├── assets/             # 静态资源
│   │   ├── components/         # 可复用组件
│   │   ├── layout/             # 布局组件
│   │   ├── router/             # 路由配置
│   │   ├── stores/             # Pinia状态管理
│   │   ├── types/              # TypeScript类型定义
│   │   ├── utils/              # 工具函数
│   │   ├── views/              # 页面视图
│   │   ├── App.vue             # 应用入口组件
│   │   └── main.ts             # 应用入口文件
│   ├── public/                 # 公共资源
│   ├── index.html              # HTML模板
│   └── vite.config.ts          # Vite配置
│
├── backend/                    # 后端项目
│   ├── app/                    # 应用代码
│   │   ├── api/                # API路由和控制器
│   │   │   └── v1/             # API v1版本
│   │   ├── core/               # 核心功能
│   │   ├── crud/               # 数据库CRUD操作
│   │   ├── middleware/         # 中间件
│   │   ├── models/             # 数据模型
│   │   ├── schemas/            # 数据验证模式
│   │   ├── services/           # 业务服务
│   │   ├── sql/                # SQL查询
│   │   ├── utils/              # 工具函数
│   │   ├── config.py           # 配置文件
│   │   ├── database.py         # 数据库连接
│   │   └── main.py             # 应用入口
│   ├── alembic/                # 数据库迁移
│   └── requirements.txt        # Python依赖
│
├── docs/                       # 项目文档
├── scripts/                    # 部署和维护脚本
├── .env.example                # 环境变量示例
├── package.json                # Node.js配置
└── README.md                   # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- uv (Python包管理器)
- 网络可访问(用于下载依赖)

### 开发环境设置

1. **克隆代码库**
```bash
git clone https://github.com/your-org/OPS-Platform.git
cd OPS-Platform
```

2. **安装uv包管理器**
```bash
# Windows
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# Linux/macOS
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip
pip install uv
```

3. **安装依赖**
```bash
# 使用项目根目录的快速安装脚本
npm run setup

# 或手动安装
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖 (使用uv，速度更快)
cd ../backend
uv sync --index-url https://pypi.tuna.tsinghua.edu.cn/simple
```

3. **配置环境变量**
```bash
# 后端目录下创建.env文件
cd backend
cp .env.example .env
# 编辑.env文件，填入必要配置
```

4. **启动开发服务**
```bash
# 使用项目根目录的开发脚本 (推荐)
npm run dev

# 或手动启动
# 启动前端
cd frontend
npm run dev

# 启动后端 (新终端，使用uv)
cd backend
uv run python run.py
```

5. **访问应用**
   - 前端: http://localhost:5173
   - 后端API: http://localhost:8000
   - API文档: http://localhost:8000/docs

### 生产环境部署

1. **构建前端**
```bash
cd frontend
npm run build
```

2. **配置后端**
```bash
cd backend
# 配置生产环境变量
```

3. **启动服务**
```bash
# 使用Gunicorn运行后端
gunicorn -k uvicorn.workers.UvicornWorker -w 4 app.main:app

# 使用Nginx托管前端构建内容并反向代理后端API
```

## 📦 功能预览

### 登录界面
![登录界面](docs/images/login.png)

### 资产管理
![资产管理](docs/images/assets.png)

### AD用户管理
![AD用户管理](docs/images/ad-management.png)

### 系统仪表盘
![系统仪表盘](docs/images/dashboard.png)

## 🛣️ 路线图

- [x] 用户认证与权限管理
- [x] AD用户与组管理
- [x] 基础资产管理功能
- [x] 泛微人员数据同步
- [ ] 移动端适配增强
- [ ] 多租户支持
- [ ] 高级报表与分析
- [ ] API网关与服务发现
- [ ] 分布式部署支持

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。在提交之前，请确保：

1. 代码符合项目规范与风格
2. 添加必要的单元测试和文档
3. 确保所有测试通过
4. 遵循语义化版本规范

## 📜 许可证

本项目采用 [MIT 许可证](LICENSE) 进行许可。

## 🔗 相关链接

- [详细文档](https://your-doc-url.com)
- [更新日志](CHANGELOG.md)
- [贡献指南](CONTRIBUTING.md)

---

<div align="center">
  <p>© 2023-2024 OPS-Platform Team. All Rights Reserved.</p>
  <p>用科技改变运维，让IT管理更简单</p>
</div>
