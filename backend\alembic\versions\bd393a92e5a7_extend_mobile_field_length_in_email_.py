"""extend_mobile_field_length_in_email_members

Revision ID: bd393a92e5a7
Revises: fix_permissions_table_only
Create Date: 2025-06-18 15:15:57.572114

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bd393a92e5a7'
down_revision: Union[str, None] = 'fix_permissions_table_only'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 扩展 email_members 表中的 mobile 字段长度从 20 增加到 50
    op.alter_column('email_members', 'mobile',
                    existing_type=sa.VARCHAR(length=20),
                    type_=sa.String(length=50),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 回滚: 将 mobile 字段长度从 50 减少到 20
    # 注意：这可能导致数据截断，请谨慎使用
    op.alter_column('email_members', 'mobile',
                    existing_type=sa.VARCHAR(length=50),
                    type_=sa.String(length=20),
                    existing_nullable=True)
    # ### end Alembic commands ###
