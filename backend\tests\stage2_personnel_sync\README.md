# 人员邮箱同步功能测试

本目录包含人员邮箱同步功能的完整测试套件，涵盖阶段二和阶段三的所有测试内容。

## 测试文件说明

### 1. test_personnel_sync.py
**基础功能测试**
- 测试同步配置管理
- 测试同步状态查询
- 测试数据一致性检查
- 测试同步日志管理
- 测试试运行同步功能

**使用方法：**
```bash
cd backend
python tests/stage2_personnel_sync/test_personnel_sync.py
```

### 2. test_dry_run.py
**试运行功能专项测试**
- 专门测试试运行同步功能
- 验证不会实际创建/修改/删除数据
- 测试同步逻辑的正确性

**使用方法：**
```bash
cd backend
python tests/stage2_personnel_sync/test_dry_run.py
```

### 3. test_new_sync_logic.py
**新同步逻辑综合测试**
- 测试新的同步逻辑（不自动创建账号）
- 测试邮箱创建申请功能
- 测试申请统计信息
- 测试实际同步（创建申请记录）
- 验证完整的申请-审批流程

**使用方法：**
```bash
cd backend
python tests/stage2_personnel_sync/test_new_sync_logic.py
```

## 测试前提条件

1. **服务运行**：确保后端服务正在运行
   ```bash
   cd backend
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **数据库准备**：确保数据库包含必要的表结构
   - personnel_sync_config
   - personnel_sync_logs
   - email_creation_requests

3. **权限配置**：确保测试用户（admin）具有相应权限
   - email:personnel:view
   - email:personnel:sync
   - email:personnel:config

## 测试覆盖的功能

### ✅ 核心同步逻辑
- [x] 不自动创建新邮箱账号
- [x] 自动禁用离职员工邮箱
- [x] 自动更新在职员工信息
- [x] 创建邮箱申请记录

### ✅ 申请管理功能
- [x] 邮箱创建申请列表
- [x] 申请统计信息
- [x] 邮箱名额监控（1000个限制）

### ✅ 同步配置管理
- [x] 同步配置查询和更新
- [x] 同步状态监控
- [x] 同步日志记录

### ✅ 数据一致性检查
- [x] 人员信息与邮箱数据对比
- [x] 离职员工邮箱检查
- [x] 数据不一致项识别

## 测试结果示例

### 成功的测试输出
```
=== 测试新的同步逻辑（试运行） ===
状态码: 200
同步统计:
  处理数量: 500
  创建申请: 450
  更新数量: 30
  禁用数量: 20
  错误数量: 0

=== 测试邮箱创建申请列表 ===
状态码: 200
申请数量: 450

=== 测试申请统计信息 ===
状态码: 200
邮箱使用情况:
  当前活跃邮箱: 873
  邮箱配额: 1000
  可用配额: 127
```

## 注意事项

1. **测试环境**：建议在测试环境中运行，避免影响生产数据
2. **API限流**：测试过程中会调用多个API，注意避免触发限流
3. **数据备份**：运行实际同步测试前，建议备份数据库
4. **权限验证**：确保测试用户具有足够的权限

## 故障排除

### 常见问题

1. **连接拒绝错误**
   - 检查后端服务是否正在运行
   - 确认端口8000是否可访问

2. **权限不足错误**
   - 检查测试用户权限配置
   - 确认用户是否为超级管理员

3. **数据库错误**
   - 检查数据库表结构是否完整
   - 运行数据库迁移脚本

4. **API超时**
   - 检查网络连接
   - 适当增加超时时间

## 相关文档

- [人员信息与腾讯企业邮箱同步方案](../../../docs/email/人员信息与腾讯企业邮箱同步方案.md)
- [阶段二开发总结](../../../docs/email/阶段二开发总结.md)
