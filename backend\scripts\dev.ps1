# OPS Platform Backend - UV Development Script
# 使用uv管理的Python环境启动开发服务器

Write-Host "启动OPS Platform Backend (使用uv环境)..." -ForegroundColor Green

# 确保在backend目录
Set-Location $PSScriptRoot\..

# 检查uv是否安装
if (-not (Get-Command uv -ErrorAction SilentlyContinue)) {
    Write-Host "错误: uv未安装，请先安装uv" -ForegroundColor Red
    exit 1
}

# 检查pyproject.toml是否存在
if (-not (Test-Path "pyproject.toml")) {
    Write-Host "错误: 未找到pyproject.toml文件" -ForegroundColor Red
    exit 1
}

# 配置环境变量
$env:UV_LINK_MODE = "copy"

# 同步依赖
Write-Host "同步依赖..." -ForegroundColor Yellow
uv sync

# 启动开发服务器
Write-Host "启动开发服务器..." -ForegroundColor Yellow
uv run python run.py 