"""add_category_field_to_assets

Revision ID: 3cd0d15e8c93
Revises: 4c140c306766
Create Date: 2025-06-28 23:26:13.474924

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3cd0d15e8c93'
down_revision: Union[str, None] = '4c140c306766'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('category', sa.String(length=50), nullable=True, comment='资产类别'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assets', 'category')
    # ### end Alembic commands ###
