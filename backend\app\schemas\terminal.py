from pydantic import BaseModel, Field, field_serializer, model_validator
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta


# 辅助函数 - 将UTC时间转换为中国时区时间
def convert_utc_to_cn_time(utc_time: Optional[datetime]) -> Optional[str]:
    if utc_time is None:
        return None
    # 将UTC时间转换为东八区时间
    tz_cn = timezone(timedelta(hours=8))
    try:
        # 如果时间已有时区信息，直接转换
        if utc_time.tzinfo is not None:
            local_time = utc_time.astimezone(tz_cn)
        else:
            # 假设输入是UTC时间但没有时区信息
            local_time = utc_time.replace(tzinfo=timezone.utc).astimezone(tz_cn)
        return local_time.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        print(f"时间转换错误: {e}, 原始时间: {utc_time}")
        return str(utc_time)  # 出错时返回原始字符串


class TerminalBase(BaseModel):
    """终端基础模型"""
    hostname: str
    mac_address: str
    ip_address: Optional[str] = None
    agent_version: Optional[str] = None


class TerminalCreate(TerminalBase):
    """创建终端请求模型"""
    unique_id: str


class TerminalUpdate(BaseModel):
    """更新终端请求模型"""
    hostname: Optional[str] = None
    mac_address: Optional[str] = None
    ip_address: Optional[str] = None
    agent_version: Optional[str] = None
    online_status: Optional[bool] = None
    heartbeat_interval: Optional[int] = None
    collection_interval: Optional[int] = None


class DiskInfoResponse(BaseModel):
    """磁盘信息响应模型"""
    name: str
    total_space: int  # KB
    free_space: int   # KB
    filesystem: Optional[str] = None
    mount_point: Optional[str] = None

    class Config:
        from_attributes = True


class HardwareInfoResponse(BaseModel):
    """硬件信息响应模型"""
    cpu_model: Optional[str] = None
    cpu_cores: Optional[int] = None
    memory_total: Optional[int] = None  # KB
    serial_number: Optional[str] = None
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    disks: List[DiskInfoResponse] = []
    update_time: Optional[datetime] = None

    @field_serializer('update_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class SecurityInfoResponse(BaseModel):
    """安全信息响应模型"""
    firewall_enabled: Optional[bool] = None
    antivirus: Optional[str] = None
    antivirus_enabled: Optional[bool] = None

    class Config:
        from_attributes = True


class OSInfoResponse(BaseModel):
    """操作系统信息响应模型"""
    name: Optional[str] = None
    version: Optional[str] = None
    build: Optional[str] = None
    architecture: Optional[str] = None
    install_date: Optional[str] = None
    installed_updates: List[str] = []
    security_info: Optional[SecurityInfoResponse] = None
    update_time: Optional[datetime] = None

    @field_serializer('update_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class SoftwareResponse(BaseModel):
    """软件信息响应模型"""
    name: str
    version: Optional[str] = None
    publisher: Optional[str] = None
    install_date: Optional[str] = None
    size: Optional[int] = None  # KB
    install_location: Optional[str] = None
    is_compliant: Optional[bool] = True
    usage_notes: Optional[str] = None

    class Config:
        from_attributes = True


class NetworkInterfaceResponse(BaseModel):
    """网络接口响应模型"""
    name: str
    mac_address: str
    ip_address: Optional[str] = None
    subnet_mask: Optional[str] = None
    dhcp_enabled: Optional[bool] = None
    is_connected: Optional[bool] = None

    class Config:
        from_attributes = True


class NetworkInfoResponse(BaseModel):
    """网络信息响应模型"""
    hostname: Optional[str] = None
    domain: Optional[str] = None
    dns_servers: List[str] = []
    default_gateway: Optional[str] = None
    interfaces: List[NetworkInterfaceResponse] = []
    update_time: Optional[datetime] = None

    @field_serializer('update_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class UserLoginInfoResponse(BaseModel):
    """用户登录信息响应模型"""
    username: str
    full_name: Optional[str] = None
    login_time: Optional[str] = None
    domain: Optional[str] = None
    update_time: Optional[datetime] = None

    @field_serializer('update_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class TerminalCommandResponse(BaseModel):
    """终端命令响应模型"""
    id: int  # 适配现有数据库的integer类型
    type: str
    content: str
    create_time: datetime
    sent_time: Optional[datetime] = None
    execute_time: Optional[datetime] = None
    timeout: int
    status: str
    result: Optional[str] = None
    error: Optional[str] = None
    execution_duration: Optional[int] = None

    @field_serializer('create_time', 'sent_time', 'execute_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class TerminalCommandCreate(BaseModel):
    """创建终端命令请求模型"""
    terminal_id: int  # 适配现有数据库的integer类型
    type: str = Field(..., description="命令类型：COLLECT_INFO, UPGRADE_AGENT, CUSTOM_COMMAND, UNINSTALL_SOFTWARE")
    content: str
    timeout: Optional[int] = 3600  # 默认1小时


class BatchCommandCreate(BaseModel):
    """批量创建命令请求模型"""
    terminal_ids: List[int]  # 适配现有数据库的integer类型
    command_type: str = Field(..., description="命令类型：COLLECT_INFO, UPGRADE_AGENT, CUSTOM_COMMAND, UNINSTALL_SOFTWARE")
    content: str
    timeout: Optional[int] = 3600  # 默认1小时


class TerminalResponse(TerminalBase):
    """终端响应模型"""
    id: int  # 适配现有数据库的integer类型
    unique_id: Optional[str] = None  # 改为可选，从属性计算
    online_status: Optional[bool] = None  # 改为可选，从status字段计算
    last_online_time: Optional[datetime] = None
    last_offline_time: Optional[datetime] = None
    last_heartbeat_time: Optional[datetime] = None
    registration_time: Optional[datetime] = None  # 改为可选，从created_at映射
    heartbeat_interval: Optional[int] = 300  # 提供默认值
    collection_interval: Optional[int] = 86400  # 提供默认值
    hardware_info: Optional[HardwareInfoResponse] = None
    os_info: Optional[OSInfoResponse] = None
    network_info: Optional[NetworkInfoResponse] = None
    last_login_user: Optional[UserLoginInfoResponse] = None

    @model_validator(mode='before')
    @classmethod
    def map_fields(cls, data):
        """映射数据库字段到响应字段"""
        if hasattr(data, '__dict__'):
            # SQLAlchemy 对象
            result = {}
            for key, value in data.__dict__.items():
                if not key.startswith('_'):
                    result[key] = value
            
            # 映射计算属性
            result['unique_id'] = getattr(data, 'unique_id', f"terminal_{data.id}")
            result['online_status'] = getattr(data, 'online_status', data.status == 'online')
            result['last_heartbeat_time'] = getattr(data, 'last_heartbeat_time', data.last_heartbeat)
            result['registration_time'] = getattr(data, 'registration_time', data.created_at)
            result['heartbeat_interval'] = getattr(data, 'heartbeat_interval', 300)
            result['collection_interval'] = getattr(data, 'collection_interval', 86400)
            
            # 映射关联表字段到前端期望的字段名
            if hasattr(data, 'os_info_detail') and data.os_info_detail:
                result['os_info'] = data.os_info_detail
            if hasattr(data, 'network_info_detail') and data.network_info_detail:
                result['network_info'] = data.network_info_detail
            
            return result
        elif isinstance(data, dict):
            # 字典数据
            if 'unique_id' not in data and 'id' in data:
                data['unique_id'] = f"terminal_{data['id']}"
            if 'online_status' not in data and 'status' in data:
                data['online_status'] = data['status'] == 'online'
            if 'last_heartbeat_time' not in data and 'last_heartbeat' in data:
                data['last_heartbeat_time'] = data['last_heartbeat']
            if 'registration_time' not in data and 'created_at' in data:
                data['registration_time'] = data['created_at']
            if 'heartbeat_interval' not in data:
                data['heartbeat_interval'] = 300
            if 'collection_interval' not in data:
                data['collection_interval'] = 86400
            
            # 映射关联表字段
            if 'os_info_detail' in data and data['os_info_detail']:
                data['os_info'] = data['os_info_detail']
            if 'network_info_detail' in data and data['network_info_detail']:
                data['network_info'] = data['network_info_detail']
                
            return data
        return data

    @field_serializer('last_online_time', 'last_offline_time', 'last_heartbeat_time', 'registration_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class TerminalSummary(BaseModel):
    """终端摘要响应模型，用于列表展示"""
    id: int  # 适配现有数据库的integer类型
    hostname: str
    unique_id: str
    mac_address: str
    ip_address: Optional[str] = None
    online_status: bool
    last_heartbeat_time: Optional[datetime] = None
    os_name: Optional[str] = None
    os_version: Optional[str] = None

    @field_serializer('last_heartbeat_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class TerminalStats(BaseModel):
    """终端统计信息"""
    total: int
    online: int
    offline: int
    windows_count: int
    windows_versions: Dict[str, int]
    hardware_stats: Dict[str, Any]


class TerminalCommandStats(BaseModel):
    """命令统计信息"""
    total: int
    pending: int
    sent: int
    executed: int
    failed: int
    timeout: int


class AgentVersionBase(BaseModel):
    """Agent版本基础模型"""
    version: str
    platform: str
    file_name: str
    file_size: int
    download_url: str
    release_notes: Optional[str] = None


class AgentVersionCreate(AgentVersionBase):
    """创建Agent版本请求模型"""
    pass


class AgentVersionResponse(AgentVersionBase):
    """Agent版本响应模型"""
    id: int  # 适配现有数据库的integer类型
    upload_time: datetime
    is_current: bool

    class Config:
        from_attributes = True


class SoftwareSummary(BaseModel):
    """软件汇总信息，用于软件管理页面展示"""
    name: str
    version: Optional[str] = None
    terminal_count: int
    is_compliant: Optional[bool] = True
    usage_notes: Optional[str] = None

    class Config:
        from_attributes = True


class SoftwareDetail(BaseModel):
    """单个软件的详细信息，包含安装此软件的终端列表"""
    name: str
    version: Optional[str] = None
    terminals: List[TerminalSummary] = []
    is_compliant: Optional[bool] = True
    usage_notes: Optional[str] = None

    class Config:
        from_attributes = True


class SoftwareUpdate(BaseModel):
    """更新软件信息的请求模型"""
    is_compliant: bool = True
    usage_notes: Optional[str] = None


class SoftwareUninstall(BaseModel):
    """卸载软件请求模型"""
    name: str
    version: Optional[str] = None


class TerminalCommandStats(BaseModel):
    """命令统计信息"""
    total: int
    pending: int
    sent: int
    executed: int
    failed: int
    timeout: int


class AgentVersionBase(BaseModel):
    """Agent版本基础模型"""
    version: str
    platform: str
    file_name: str
    file_size: int
    download_url: str
    release_notes: Optional[str] = None


class AgentVersionCreate(AgentVersionBase):
    """创建Agent版本请求模型"""
    is_current: bool = False
    auto_upgrade: bool = False
    upgrade_strategy: str = "all"


class AgentVersionResponse(AgentVersionBase):
    """Agent版本响应模型"""
    id: int  # 适配现有数据库的integer类型
    upload_time: datetime
    is_current: bool

    # 上报升级命令结果（仅在设置当前版本时返回）
    upgrade_command_sent: Optional[bool] = None
    upgrade_command_success: Optional[int] = None
    upgrade_command_failed: Optional[int] = None

    @field_serializer('upload_time')
    def serialize_datetime(self, value: Optional[datetime], _info) -> Optional[str]:
        return convert_utc_to_cn_time(value)

    class Config:
        from_attributes = True


class AgentUpgradeStatus(BaseModel):
    """Agent升级状态模型"""
    command_id: str
    terminal_id: int
    target_version: str
    current_version: str
    status: str  # pending, downloading, installing, completed, failed, rolled_back
    progress: int  # 0-100
    message: Optional[str] = None
    error_details: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None  # 升级耗时（秒）


class AgentUpgradeProgress(BaseModel):
    """Agent升级进度模型"""
    command_id: str
    progress: int  # 0-100, -1表示失败, -2表示回滚
    message: str
    timestamp: datetime


class AgentUpgradeHistory(BaseModel):
    """Agent升级历史记录"""
    id: int
    terminal_id: int
    from_version: str
    to_version: str
    status: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True