import request from '@/utils/request'

export const systemApi = {
  // 获取权限模块列表
  getPermissionModules: () => {
    return request({
      url: '/system/permissions/modules',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取权限列表
  getPermissions: () => {
    return request({
      url: '/system/permissions',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取角色列表
  getRoles: () => {
    return request({
      url: '/system/roles',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 获取角色详情
  getRoleById: (id: number) => {
    return request({
      url: `/system/roles/${id}`,
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 分配角色权限
  assignPermissions: (roleId: number, permissionIds: number[]) => {
    return request({
      url: '/system/roles/assign-permissions',
      method: 'post',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: {
        role_id: roleId,
        permission_ids: permissionIds
      }
    })
  },

  // 获取用户列表
  getUsers: () => {
    return request({
      url: '/system/users/',
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 创建新用户
  createUser: (userData: {
    username: string,
    email: string,
    password: string,
    is_active: boolean,
    is_superuser: boolean
  }) => {
    return request({
      url: '/system/users/',
      method: 'post',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: userData
    })
  },

  // 更新用户信息
  updateUser: (userId: number, userData: {
    username: string,
    email: string,
    password?: string,
    is_active: boolean,
    is_superuser: boolean
  }) => {
    return request({
      url: `/system/users/${userId}`,
      method: 'put',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: userData
    })
  },

  // 更新用户状态（启用/禁用）
  updateUserActive: (userId: number, isActive: boolean) => {
    return request({
      url: `/system/users/${userId}/active`,
      method: 'patch',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: {
        is_active: isActive
      }
    })
  },

  // 为用户分配角色
  assignRoles: (userId: number, roleIds: number[]) => {
    return request({
      url: '/system/roles/assign-roles',
      method: 'post',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: {
        user_id: userId,
        role_ids: roleIds
      }
    })
  },

  // 更新用户超级管理员状态
  updateUserSuperuser: (userId: number, isSuperuser: boolean) => {
    return request({
      url: `/system/users/${userId}/superuser`,
      method: 'patch',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      },
      data: {
        is_superuser: isSuperuser
      }
    })
  },

  // 获取用户详细信息
  getUserById: (userId: number) => {
    return request({
      url: `/system/users/${userId}`,
      method: 'get',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  },

  // 删除用户
  deleteUser: (userId: number) => {
    return request({
      url: `/system/users/${userId}`,
      method: 'delete',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')?.trim() || ''}`
      }
    })
  }
} 