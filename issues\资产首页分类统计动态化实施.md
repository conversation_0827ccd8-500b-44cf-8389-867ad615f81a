# 资产首页分类统计动态化实施

## 任务背景
用户希望资产管理首页的分类统计能够基于字段值管理进行动态配置，而不是硬编码的分类。

## 实施计划

### 已完成的修改

#### 0. 添加资产类别字段到数据模型
- **文件**: `backend/app/models/asset.py`
- **修改内容**: 
  - 在Asset模型中添加 `category: Mapped[Optional[str]]` 字段
  - 字段类型为可选的字符串，长度限制50字符
- **数据库迁移**: 创建并执行了 `3cd0d15e8c93_add_category_field_to_assets.py` 迁移

#### 1. 更新后端Schema和前端类型定义
- **文件**: 
  - `backend/app/schemas/asset.py` - 在所有资产Schema中添加category字段
  - `frontend/src/types/asset.ts` - 在Asset相关接口中添加category字段
  - `frontend/src/types/field_value.ts` - 将"设备分类"改名为"资产类别"

#### 2. 修改资产首页统计逻辑
- **文件**: `frontend/src/mobile/views/asset/index.vue`
- **修改内容**:
  - 导入字段值相关的API和类型
  - 将硬编码的分类统计结构改为动态的 `Record<string, string>` 类型
  - 修改模板，支持动态渲染分类统计项
  - 重构分类匹配函数，直接基于资产的 `category` 字段进行匹配
  - 新增 `getCategoryConfigs()` 函数获取分类配置
  - 重写分类统计计算逻辑，优先使用字段值配置，否则统计现有的资产类别

## 功能特性

### 1. 标准化资产类别字段
- 资产模型包含标准的 `category` 字段
- 用户可直接为每个资产设置类别
- 支持通过字段值管理配置可选的类别选项

### 2. 精确分类统计
- 直接基于资产的 `category` 字段进行统计
- 无需关键词匹配，统计结果更准确
- 支持动态显示所有现有的资产类别

### 3. 灵活配置
- 可在字段值管理中预设常用的资产类别选项
- 当有字段值配置时，优先显示配置的分类统计
- 当无配置时，自动统计现有资产的所有类别

### 4. 实时更新
- 字段值管理中的分类配置变更会立即反映到首页统计
- 支持下拉刷新更新统计数据

## 使用方法

### 配置资产类别
1. 进入"字段值管理"页面
2. 点击"新增字段值"
3. 选择字段名称为"资产类别"
4. 输入类别名称（如"台式机"、"笔记本电脑"、"服务器"等）
5. 可在描述中添加类别说明

### 为资产设置类别
1. 在资产添加/编辑页面
2. 选择或输入资产类别
3. 保存后首页统计会自动更新

## 技术实现

### 核心函数
```typescript
// 获取设备分类配置
const getCategoryConfigs = async (): Promise<FieldValue[]> => {
  const response = await fieldValueApi.getFieldValues({
    field_name: FIELD_NAMES.CATEGORY,
    limit: 100
  })
  return response.data?.data || []
}

// 分类匹配函数
const isCategoryMatch = (asset: Asset, categoryValue: string): boolean => {
  return asset.category === categoryValue
}
```

### 数据流程
1. 页面加载时调用 `getCategoryConfigs()` 获取分类配置
2. 遍历每个分类配置，使用 `isCategoryMatch()` 匹配资产
3. 统计每个分类的资产数量
4. 动态更新页面显示

## 预期效果
- ✅ 支持用户自定义设备分类
- ✅ 分类名称和匹配规则可配置
- ✅ 实时反映字段值管理的变更
- ✅ 保持向后兼容性
- ✅ 提供良好的用户体验

## 后续优化建议
1. 考虑在字段值管理中增加专门的分类配置界面
2. 支持更复杂的匹配规则（正则表达式等）
3. 增加分类统计的排序功能
4. 考虑缓存分类配置以提升性能 