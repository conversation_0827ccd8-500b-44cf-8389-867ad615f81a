# 盘点任务导出CSV编码问题修复

## 问题描述
在导出盘点任务为CSV格式时，出现编码错误：
```
'latin-1' codec can't encode characters in position 22-25: ordinal not in range(256)
```

## 问题分析
1. **根本原因**：pandas的`to_csv`方法在某些情况下会使用`latin-1`编码处理中文字符
2. **触发条件**：导出包含中文字符的数据时，编码转换失败
3. **影响范围**：CSV格式导出功能，Excel导出未受影响

## 修复方案

### 第一步：修复CSV导出编码
**文件**：`backend/app/api/v1/inventory.py`
**位置**：第370行左右的CSV导出逻辑

**修改前**：
```python
if format == 'csv':
    # 导出CSV
    df.to_csv(output, index=False, encoding='utf-8-sig')
    media_type = 'text/csv'
```

**修改后**：
```python
if format == 'csv':
    # 导出CSV - 直接写入BytesIO，避免编码问题
    csv_content = df.to_csv(index=False)
    output.write(csv_content.encode('utf-8-sig'))
    media_type = 'text/csv; charset=utf-8'
```

### 第二步：优化文件名编码处理
**问题**：中文文件名在某些浏览器下可能显示异常

**修改前**：
```python
# 编码文件名
encoded_filename = quote(filename_display)

return StreamingResponse(
    output,
    media_type=media_type,
    headers={
        'Content-Disposition': f'attachment; filename="{filename_display}"; filename*=utf-8\'\'{encoded_filename}',
        'Access-Control-Expose-Headers': 'Content-Disposition'
    }
)
```

**修改后**：
```python
# 编码文件名，确保中文文件名正确处理
encoded_filename = quote(filename_display, safe='')

return StreamingResponse(
    io.BytesIO(output.getvalue()),
    media_type=media_type,
    headers={
        'Content-Disposition': f'attachment; filename*=utf-8\'\'{encoded_filename}',
        'Access-Control-Expose-Headers': 'Content-Disposition'
    }
)
```

## 技术要点

### 1. 编码处理策略
- **分离编码步骤**：先生成CSV内容，再手动编码为字节流
- **使用UTF-8-BOM**：确保Excel等工具能正确识别中文字符
- **字符集声明**：在media_type中明确指定字符集

### 2. 文件名处理
- **URL编码**：使用`quote(filename, safe='')`确保所有特殊字符被编码
- **移除双重filename**：只使用`filename*=utf-8''`格式，避免编码冲突
- **BytesIO重新封装**：确保StreamingResponse获得正确的字节流

### 3. 兼容性考虑
- **浏览器兼容**：filename*格式被现代浏览器广泛支持
- **Excel兼容**：UTF-8-BOM确保Excel能正确显示中文
- **系统兼容**：编码方案在Windows/Linux/macOS上均有效

## 测试验证

### 预期结果
1. **CSV导出成功**：包含中文字符的数据能正常导出
2. **文件名正确**：下载的文件名显示中文字符
3. **内容可读**：Excel打开CSV文件时中文字符正确显示
4. **Excel导出不受影响**：原有Excel导出功能正常

### 测试用例
- [x] 导出包含中文资产名称的盘点任务
- [x] 导出包含中文自定义字段的盘点任务  
- [x] 验证文件名中文显示
- [x] 验证Excel打开CSV文件的中文显示

## 状态
✅ **修复完成** - CSV导出编码问题已解决，功能恢复正常 