"""
注册表管理API路由
提供注册表操作、搜索、备份、还原等功能的RESTful接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from app.database import get_db
from app.schemas import registry as schemas
from app.crud import registry_operation as crud
from app.crud import terminal as terminal_crud
from app.api import deps
import logging
import uuid
import time
import json
from datetime import datetime
from app.models import Terminal

router = APIRouter()
logger = logging.getLogger(__name__)


# === 权限验证装饰器 ===

def check_registry_permissions(permissions: List[str]):
    """检查注册表相关权限"""
    return deps.check_permissions([f"terminal:registry:{perm}" for perm in permissions])


# === 终端验证函数 ===

def validate_terminal_exists(terminal_id: int, db: Session) -> Terminal:
    """验证终端是否存在并返回终端对象"""
    terminal = terminal_crud.get_terminal(db, terminal_id)
    if not terminal:
        raise HTTPException(
            status_code=404,
            detail=f"终端 {terminal_id} 不存在"
        )
    return terminal


# === gRPC客户端工具函数 ===

async def send_registry_command_to_terminal(terminal_id: str, operation_data: dict) -> dict:
    """发送注册表命令到终端Agent并等待响应"""
    try:
        # 导入gRPC客户端
        from app.grpc.terminal_pb import terminal_pb2_grpc, terminal_pb2
        import grpc
        
        # 创建gRPC连接
        channel = grpc.insecure_channel('localhost:50051')
        stub = terminal_pb2_grpc.TerminalManagementStub(channel)
        
        # 构建注册表操作请求
        request = terminal_pb2.RegistryOperationRequest(
            command_id=operation_data.get("command_id", str(uuid.uuid4())),
            terminal_id=terminal_id,
            operation=getattr(terminal_pb2.RegistryOperationType, operation_data.get("operation", "REGISTRY_READ")),
            root_key=getattr(terminal_pb2.RegistryRootKey, operation_data.get("root_key", "HKEY_LOCAL_MACHINE")),
            sub_key_path=operation_data.get("key_path", ""),
            value_name=operation_data.get("value_name", ""),
            value_type=getattr(terminal_pb2.RegistryValueType, operation_data.get("value_type", "REG_SZ")) if operation_data.get("value_type") else terminal_pb2.RegistryValueType.REG_SZ,
            value_data=operation_data.get("value_data", ""),
            create_backup=operation_data.get("create_backup", False),
            backup_reason=operation_data.get("backup_reason", ""),
            # 分页参数
            page=operation_data.get("page", 1),
            page_size=operation_data.get("page_size", 1000),
            search_filter=operation_data.get("search_filter", "")
        )
        
        # 发送请求并等待响应
        response = stub.PerformRegistryOperation(request, timeout=30)
        
        # 关闭连接
        channel.close()
        
        # 转换响应为字典格式
        result = {
            "success": response.success,
            "message": response.message,
            "error": response.error,
            "backup_id": response.backup_id,
            "operation_time": response.operation_time
        }
        
        # 添加键数据（如果有）
        if response.key_data:
            result["key_data"] = {
                "name": response.key_data.name,
                "full_path": response.key_data.full_path,
                "sub_keys": list(response.key_data.sub_keys),
                "values": [
                    {
                        "name": value.name,
                        "type": terminal_pb2.RegistryValueType.Name(value.type),
                        "data": value.data,
                        "size": value.size
                    } for value in response.key_data.values
                ],
                "last_modified": response.key_data.last_modified,
                "sub_key_count": response.key_data.sub_key_count,
                "value_count": response.key_data.value_count,
                # 分页信息
                "current_page": response.key_data.current_page,
                "page_size": response.key_data.page_size,
                "total_sub_keys": response.key_data.total_sub_keys,
                "has_more_sub_keys": response.key_data.has_more_sub_keys
            }
        
        # 添加值数据（如果有）
        if response.value_data:
            result["value_data"] = {
                "name": response.value_data.name,
                "type": terminal_pb2.RegistryValueType.Name(response.value_data.type),
                "data": response.value_data.data,
                "size": response.value_data.size
            }
        
        return result
        
    except Exception as e:
        logger.error(f"发送注册表命令到终端失败: {str(e)}")
        return {
            "success": False,
            "error": f"终端通信失败: {str(e)}"
        }


async def send_registry_search_to_terminal(terminal_id: str, search_data: dict) -> dict:
    """发送注册表搜索命令到终端Agent"""
    try:
        from app.grpc.terminal_pb import terminal_pb2_grpc, terminal_pb2
        import grpc
        
        channel = grpc.insecure_channel('localhost:50051')
        stub = terminal_pb2_grpc.TerminalManagementStub(channel)
        
        request = terminal_pb2.RegistrySearchRequest(
            command_id=search_data.get("command_id", str(uuid.uuid4())),
            terminal_id=terminal_id,
            root_key=getattr(terminal_pb2.RegistryRootKey, search_data.get("root_key", "HKEY_LOCAL_MACHINE")),
            start_path=search_data.get("start_path", ""),
            search_pattern=search_data.get("search_pattern", ""),
            search_keys=search_data.get("search_keys", True),
            search_values=search_data.get("search_values", True),
            search_data=search_data.get("search_data", True),
            max_depth=search_data.get("max_depth", 10),
            max_results=search_data.get("max_results", 100)
        )
        
        response = stub.SearchRegistry(request, timeout=60)
        channel.close()
        
        result = {
            "success": response.success,
            "message": response.message,
            "total_results": len(response.results),
            "results": [
                {
                    "path": result.path,
                    "match_type": result.match_type,
                    "match_text": result.match_text,
                    "value": {
                        "name": result.value.name,
                        "type": terminal_pb2.RegistryValueType.Name(result.value.type),
                        "data": result.value.data,
                        "size": result.value.size
                    } if result.value else None
                } for result in response.results
            ]
        }
        
        return result
        
    except Exception as e:
        logger.error(f"发送注册表搜索到终端失败: {str(e)}")
        return {
            "success": False,
            "error": f"搜索操作失败: {str(e)}",
            "total_results": 0,
            "results": []
        }


async def send_registry_backup_to_terminal(terminal_id: str, backup_data: dict) -> dict:
    """发送注册表备份命令到终端Agent"""
    try:
        from app.grpc.terminal_pb import terminal_pb2_grpc, terminal_pb2
        import grpc
        
        channel = grpc.insecure_channel('localhost:50051')
        stub = terminal_pb2_grpc.TerminalManagementStub(channel)
        
        # 构建备份操作数据
        operation_data = {
            "command_id": backup_data.get("command_id"),
            "operation": "BACKUP",
            "root_key": backup_data.get("root_key"),
            "key_path": backup_data.get("key_path", ""),
            "create_backup": True,
            "backup_reason": backup_data.get("reason", "Manual backup")
        }
        
        request = terminal_pb2.RegistryOperationRequest(
            command_id=operation_data["command_id"],
            terminal_id=terminal_id,
            operation=terminal_pb2.RegistryOperationType.REGISTRY_BACKUP,
            root_key=getattr(terminal_pb2.RegistryRootKey, operation_data["root_key"]),
            sub_key_path=operation_data["key_path"],
            create_backup=True,
            backup_reason=operation_data["backup_reason"]
        )
        
        response = stub.PerformRegistryOperation(request, timeout=120)  # 备份可能需要更长时间
        channel.close()
        
        result = {
            "success": response.success,
            "message": response.message,
            "error": response.error,
            "backup_id": response.backup_id,
            "file_path": f"C:\\Registry_Backups\\{response.backup_id}.reg",  # 模拟文件路径
            "file_size": 1024,  # Agent应该返回实际文件大小
            "file_hash": "calculated_hash"  # Agent应该返回文件哈希
        }
        
        return result
        
    except Exception as e:
        logger.error(f"发送注册表备份到终端失败: {str(e)}")
        return {
            "success": False,
            "error": f"备份操作失败: {str(e)}"
        }


async def send_registry_restore_to_terminal(terminal_id: str, restore_data: dict) -> dict:
    """发送注册表还原命令到终端Agent"""
    try:
        from app.grpc.terminal_pb import terminal_pb2_grpc, terminal_pb2
        import grpc
        
        channel = grpc.insecure_channel('localhost:50051')
        stub = terminal_pb2_grpc.TerminalManagementStub(channel)
        
        # 构建还原操作数据
        operation_data = {
            "command_id": restore_data.get("command_id"),
            "operation": "RESTORE",
            "backup_file_path": restore_data.get("backup_file_path"),
            "target_root_key": restore_data.get("target_root_key"),
            "create_backup": restore_data.get("create_backup_before_restore", True),
            "backup_reason": "Pre-restore backup"
        }
        
        request = terminal_pb2.RegistryOperationRequest(
            command_id=operation_data["command_id"],
            terminal_id=terminal_id,
            operation=terminal_pb2.RegistryOperationType.REGISTRY_BACKUP,  # 还原前先备份
            value_data=operation_data["backup_file_path"],  # 使用value_data传递备份文件路径
            create_backup=operation_data["create_backup"],
            backup_reason=operation_data["backup_reason"]
        )
        
        response = stub.PerformRegistryOperation(request, timeout=180)  # 还原可能需要更长时间
        channel.close()
        
        result = {
            "success": response.success,
            "message": response.message,
            "error": response.error,
            "backup_id": response.backup_id,  # 还原前的备份ID
            "operation_time": response.operation_time
        }
        
        return result
        
    except Exception as e:
        logger.error(f"发送注册表还原到终端失败: {str(e)}")
        return {
            "success": False,
            "error": f"还原操作失败: {str(e)}"
        }


# === 注册表基本操作接口 ===

@router.post("/{terminal_id}/registry/operations", response_model=schemas.RegistryOperationResponse)
async def perform_registry_operation(
    terminal_id: int,
    operation_request: schemas.RegistryOperationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["read", "write"]))
):
    """
    执行注册表操作（读取、写入、删除等）
    支持自动备份和操作审计
    """
    # 验证终端是否存在且在线
    terminal = validate_terminal_exists(terminal_id, db)
    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端离线，无法执行操作")
    
    # 更新请求中的终端ID
    operation_request.terminal_id = terminal_id
    
    # 权限检查 - 根据操作类型检查不同权限
    required_permissions = []
    if operation_request.operation in [schemas.RegistryOperationType.READ]:
        required_permissions = ["read"]
    elif operation_request.operation in [schemas.RegistryOperationType.WRITE]:
        required_permissions = ["write"]
    elif operation_request.operation in [schemas.RegistryOperationType.DELETE, schemas.RegistryOperationType.DELETE_KEY]:
        required_permissions = ["delete"]
    elif operation_request.operation in [schemas.RegistryOperationType.CREATE_KEY]:
        required_permissions = ["create"]
    
    # 检查危险操作权限
    dangerous_keys = [
        "SYSTEM\\CurrentControlSet",
        "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
        "SECURITY",
        "SAM"
    ]
    
    if any(dangerous_key in operation_request.key_path.upper() for dangerous_key in dangerous_keys):
        # 需要管理员权限
        if not current_user.is_superuser:
            raise HTTPException(status_code=403, detail="此操作需要管理员权限")
    
    try:
        # 生成命令ID
        command_id = str(uuid.uuid4())
        start_time = time.time()
        
        # 创建操作记录
        operation_record = crud.create_registry_operation(
            db=db,
            command_id=command_id,
            terminal_id=str(operation_request.terminal_id),
            operation_type=operation_request.operation.value,
            root_key=operation_request.root_key.value,
            key_path=operation_request.key_path,
            value_name=operation_request.value_name,
            value_type=operation_request.value_type.value if operation_request.value_type else None,
            new_value_data=operation_request.value_data,
            backup_reason=operation_request.backup_reason
        )
        
        # 构建操作数据
        operation_data = {
            "command_id": command_id,
            "operation": operation_request.operation.value,
            "root_key": operation_request.root_key.value,
            "key_path": operation_request.key_path,
            "value_name": operation_request.value_name,
            "value_type": operation_request.value_type.value if operation_request.value_type else None,
            "value_data": operation_request.value_data,
            "create_backup": operation_request.create_backup,
            "backup_reason": operation_request.backup_reason
        }
        
        # 发送命令到终端Agent
        result = await send_registry_command_to_terminal(str(operation_request.terminal_id), operation_data)
        
        execution_duration = int((time.time() - start_time) * 1000)
        
        # 更新操作记录
        operation_record.success = result.get("success", False)
        operation_record.response_data = result
        operation_record.error_message = result.get("error")
        operation_record.execution_duration = execution_duration
        db.commit()
        
        # 构建响应
        response = schemas.RegistryOperationResponse(
            success=result.get("success", False),
            operation_id=operation_record.id,
            command_id=command_id,
            error=result.get("error"),
            backup_id=result.get("backup_id"),
            execution_duration=execution_duration
        )
        
        # 如果是查询或枚举操作，添加数据
        if operation_request.operation in [schemas.RegistryOperationType.READ, schemas.RegistryOperationType.ENUMERATE]:
            # 添加调试日志
            logger.info(f"gRPC响应结果: success={result.get('success')}, key_data存在={bool(result.get('key_data'))}")
            if result.get('key_data'):
                key_data = result['key_data']
                logger.info(f"键数据详情: sub_keys={len(key_data.get('sub_keys', []))}, values={len(key_data.get('values', []))}")
            
            if result.get("key_data"):
                key_data = result["key_data"]
                response.key_data = schemas.RegistryKey(
                    name=key_data.get("name", ""),
                    full_path=key_data.get("full_path", ""),
                    sub_keys=key_data.get("sub_keys", []),
                    values=[
                        schemas.RegistryValue(
                            name=v.get("name", ""),
                            type=v.get("type", "REG_SZ"),
                            data=v.get("data", ""),
                            size=v.get("size", 0)
                        ) for v in key_data.get("values", [])
                    ],
                    last_modified=key_data.get("last_modified"),
                    sub_key_count=key_data.get("sub_key_count", 0),
                    value_count=key_data.get("value_count", 0)
                )
            
            if result.get("value_data"):
                value_data = result["value_data"]
                response.value_data = schemas.RegistryValue(
                    name=value_data.get("name", ""),
                    type=value_data.get("type", "REG_SZ"),
                    data=value_data.get("data", ""),
                    size=value_data.get("size", 0)
                )
        
        return response
        
    except Exception as e:
        logger.error(f"注册表操作失败: {str(e)}")
        
        # 更新失败记录
        if 'operation_record' in locals():
            operation_record.success = False
            operation_record.error_message = str(e)
            operation_record.execution_duration = int((time.time() - start_time) * 1000)
            db.commit()
        
        raise HTTPException(status_code=500, detail=f"注册表操作失败: {str(e)}")


@router.post("/{terminal_id}/registry/batch-operations", response_model=schemas.RegistryBatchOperationResponse)
async def perform_batch_registry_operations(
    terminal_id: int,
    batch_request: schemas.RegistryBatchOperationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["batch"]))
):
    """
    执行批量注册表操作
    支持批量备份和失败回滚
    """
    # 验证终端
    terminal = validate_terminal_exists(terminal_id, db)
    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端离线，无法执行批量操作")
    
    # 更新请求中的终端ID
    batch_request.terminal_id = terminal_id
    
    results = []
    successful_count = 0
    failed_count = 0
    
    try:
        # 如果需要批量备份，先创建批量备份
        batch_backup_id = None
        if batch_request.create_backup:
            # 如果需要批量备份，先创建批量备份
            if batch_request.create_backup:
                batch_backup_data = {
                    "command_id": str(uuid.uuid4()),
                    "root_key": "HKEY_LOCAL_MACHINE",  # 默认根键
                    "key_path": "",  # 完整备份
                    "reason": f"批量操作前备份 - {batch_request.batch_reason}"
                }
                backup_result = await send_registry_backup_to_terminal(str(batch_request.terminal_id), batch_backup_data)
                if backup_result.get("success"):
                    batch_backup_id = backup_result.get("backup_id")
        
        # 逐个执行操作
        for operation in batch_request.operations:
            try:
                # 重用单个操作接口
                operation.terminal_id = batch_request.terminal_id
                result = await perform_registry_operation(operation, background_tasks, db, current_user)
                results.append(result)
                if result.success:
                    successful_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"批量操作中的单个操作失败: {str(e)}")
                failed_result = schemas.RegistryOperationResponse(
                    success=False,
                    error=str(e)
                )
                results.append(failed_result)
                failed_count += 1
        
        return schemas.RegistryBatchOperationResponse(
            success=failed_count == 0,
            total_operations=len(batch_request.operations),
            successful_operations=successful_count,
            failed_operations=failed_count,
            operation_results=results,
            batch_backup_id=batch_backup_id
        )
        
    except Exception as e:
        logger.error(f"批量注册表操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量操作失败: {str(e)}")


# === 注册表搜索接口 ===

@router.post("/{terminal_id}/registry/search", response_model=schemas.RegistrySearchResponse)
async def search_registry(
    terminal_id: int,
    search_request: schemas.RegistrySearchRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["read"]))
):
    """
    搜索注册表
    支持键名、值名、值数据的模糊匹配
    """
    # 验证终端
    terminal = validate_terminal_exists(terminal_id, db)
    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端离线，无法执行搜索")
    
    # 更新请求中的终端ID
    search_request.terminal_id = terminal_id
    
    try:
        # 生成搜索ID
        search_id = str(uuid.uuid4())
        start_time = time.time()
        
        # 构建搜索数据
        search_data = {
            "command_id": search_id,
            "root_key": search_request.root_key.value,
            "start_path": search_request.start_path,
            "search_pattern": search_request.search_pattern,
            "search_keys": search_request.search_keys,
            "search_values": search_request.search_values,
            "search_data": search_request.search_data,
            "max_depth": search_request.max_depth,
            "max_results": search_request.max_results
        }
        
        # 发送搜索命令到终端
        result = await send_registry_search_to_terminal(str(search_request.terminal_id), search_data)
        
        execution_duration = int((time.time() - start_time) * 1000)
        
        # 记录搜索日志
        search_log = crud.create_registry_search_log(
            db=db,
            search_id=search_id,
            terminal_id=str(search_request.terminal_id),
            root_key=search_request.root_key.value,
            start_path=search_request.start_path,
            search_pattern=search_request.search_pattern,
            search_keys=search_request.search_keys,
            search_values=search_request.search_values,
            search_data=search_request.search_data,
            max_depth=search_request.max_depth,
            max_results=search_request.max_results,
            total_results=result.get("total_results", 0),
            results_data=result,
            success=result.get("success", False),
            error_message=result.get("error"),
            execution_duration=execution_duration
        )
        
        # 构建响应
        response = schemas.RegistrySearchResponse(
            success=result.get("success", False),
            search_id=search_id,
            total_results=result.get("total_results", 0),
            execution_duration=execution_duration,
            error=result.get("error")
        )
        
        if result.get("success", False) and result.get("results"):
            response.results = [
                schemas.RegistrySearchResult(
                    path=item.get("path", ""),
                    match_type=item.get("match_type", ""),
                    match_text=item.get("match_text", ""),
                    value=schemas.RegistryValue(
                        name=item["value"].get("name", ""),
                        type=item["value"].get("type", "REG_SZ"),
                        data=item["value"].get("data", ""),
                        size=item["value"].get("size", 0)
                    ) if item.get("value") else None
                ) for item in result["results"]
            ]
        
        return response
        
    except Exception as e:
        logger.error(f"注册表搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索操作失败: {str(e)}")


# === 注册表备份接口 ===

@router.post("/{terminal_id}/registry/backups", response_model=schemas.RegistryBackupResponse)
async def create_registry_backup(
    terminal_id: int,
    backup_request: schemas.RegistryBackupRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["backup"]))
):
    """
    创建注册表备份
    支持指定键范围的备份和自定义备份名称
    """
    # 验证终端
    terminal = validate_terminal_exists(terminal_id, db)
    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端离线，无法创建备份")
    
    # 更新请求中的终端ID
    backup_request.terminal_id = terminal_id
    
    try:
        backup_id = str(uuid.uuid4())
        
        # 生成备份名称
        if backup_request.backup_name:
            backup_name = backup_request.backup_name
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            key_name = backup_request.key_path.replace("\\", "_") if backup_request.key_path else "ROOT"
            backup_name = f"{backup_request.root_key}_{key_name}_{timestamp}"
        
        # 构建备份数据
        backup_data = {
            "command_id": backup_id,
            "root_key": backup_request.root_key.value,
            "key_path": backup_request.key_path,
            "reason": backup_request.reason or "Manual backup"
        }
        
        # 发送备份命令到终端Agent
        result = await send_registry_backup_to_terminal(str(backup_request.terminal_id), backup_data)
        
        if result.get("success", False):
            # 记录备份信息到数据库
            tags_str = ",".join(backup_request.tags) if backup_request.tags else None
            
            backup_record = crud.create_registry_backup(
                db=db,
                backup_id=backup_id,
                backup_name=backup_name,
                terminal_id=str(backup_request.terminal_id),
                root_key=backup_request.root_key.value,
                key_path=backup_request.key_path,
                file_path=result.get("file_path", ""),
                file_size=result.get("file_size"),
                file_hash=result.get("file_hash"),
                reason=backup_request.reason,
                description=backup_request.description,
                tags=tags_str
            )
            
            return schemas.RegistryBackupResponse(
                success=True,
                backup_id=backup_id,
                backup_name=backup_name,
                file_path=result.get("file_path"),
                file_size=result.get("file_size"),
                file_hash=result.get("file_hash"),
                created_at=backup_record.created_at
            )
        else:
            return schemas.RegistryBackupResponse(
                success=False,
                backup_id=backup_id,
                backup_name=backup_name,
                error=result.get("error", "备份失败")
            )
        
    except Exception as e:
        logger.error(f"创建注册表备份失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"备份失败: {str(e)}")


@router.post("/{terminal_id}/registry/backups/{backup_id}/restore", response_model=schemas.RegistryOperationResponse)
async def restore_registry_backup(
    terminal_id: int,
    backup_id: str,
    restore_request: schemas.RegistryRestoreRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["restore"]))
):
    """
    从备份还原注册表
    支持还原到不同位置和还原前备份
    """
    # 验证备份是否存在
    backup_record = crud.get_registry_backup(db, backup_id)
    if not backup_record:
        raise HTTPException(status_code=404, detail="备份不存在")
    
    # 验证终端
    terminal = validate_terminal_exists(terminal_id, db)
    if not terminal.online_status:
        raise HTTPException(status_code=400, detail="终端离线，无法执行还原")
    
    # 更新请求中的终端ID
    restore_request.terminal_id = terminal_id
    
    # 管理员权限检查
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="注册表还原需要管理员权限")
    
    try:
        command_id = str(uuid.uuid4())
        
        # 构建还原数据
        restore_data = {
            "command_id": command_id,
            "backup_file_path": backup_record.file_path,
            "target_root_key": restore_request.target_root_key.value if restore_request.target_root_key else backup_record.root_key,
            "create_backup_before_restore": restore_request.create_backup_before_restore
        }
        
        # 发送还原命令到终端Agent
        result = await send_registry_restore_to_terminal(str(restore_request.terminal_id), restore_data)
        
        # 记录还原操作
        restore_record = crud.create_registry_operation(
            db=db,
            command_id=command_id,
            terminal_id=str(restore_request.terminal_id),
            operation_type="REGISTRY_RESTORE",
            root_key=backup_record.root_key,
            key_path=backup_record.key_path,
            success=result.get("success", False),
            error_message=result.get("error"),
            backup_id=backup_id,
            backup_reason=f"还原备份: {restore_request.restore_reason}" if restore_request.restore_reason else "注册表还原操作"
        )
        
        return schemas.RegistryOperationResponse(
            success=result.get("success", False),
            operation_id=restore_record.id,
            command_id=command_id,
            error=result.get("error"),
            backup_id=result.get("new_backup_id") if restore_request.create_backup_before_restore else None
        )
        
    except Exception as e:
        logger.error(f"注册表还原失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"还原失败: {str(e)}")


# === 历史记录和查询接口 ===

@router.get("/{terminal_id}/registry/operations", response_model=List[schemas.RegistryOperationHistory])
def get_registry_operations(
    terminal_id: int,
    operation_type: Optional[str] = Query(None, description="操作类型"),
    root_key: Optional[str] = Query(None, description="根键"),
    success: Optional[bool] = Query(None, description="操作是否成功"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="限制记录数"),
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["view_history"]))
):
    """
    获取指定终端的注册表操作历史记录
    支持多条件过滤和分页
    """
    # 验证终端存在
    validate_terminal_exists(terminal_id, db)
    
    try:
        if search_term:
            operations = crud.search_registry_operations(
                db=db,
                search_term=search_term,
                terminal_id=str(terminal_id),
                skip=skip,
                limit=limit
            )
        else:
            operations = crud.get_registry_operations(
                db=db,
                terminal_id=str(terminal_id),
                operation_type=operation_type,
                root_key=root_key,
                success=success,
                skip=skip,
                limit=limit
            )
        
        return [
            schemas.RegistryOperationHistory(
                id=op.id,
                command_id=op.command_id,
                terminal_id=op.terminal_id,
                operation_type=op.operation_type,
                root_key=op.root_key,
                key_path=op.key_path,
                value_name=op.value_name,
                success=op.success,
                error_message=op.error_message,
                backup_id=op.backup_id,
                execution_duration=op.execution_duration,
                created_at=op.created_at
            ) for op in operations
        ]
        
    except Exception as e:
        logger.error(f"获取注册表操作历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")


@router.get("/{terminal_id}/registry/backups", response_model=List[schemas.RegistryBackupHistory])
def get_registry_backups(
    terminal_id: int,
    root_key: Optional[str] = Query(None, description="根键"),
    status: str = Query("active", description="备份状态"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(100, description="限制记录数"),
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["view_backups"]))
):
    """
    获取指定终端的注册表备份历史记录
    支持多条件过滤和分页
    """
    # 验证终端存在
    validate_terminal_exists(terminal_id, db)
    
    try:
        if search_term:
            backups = crud.search_registry_backups(
                db=db,
                search_term=search_term,
                terminal_id=str(terminal_id),
                skip=skip,
                limit=limit
            )
        else:
            backups = crud.get_registry_backups(
                db=db,
                terminal_id=str(terminal_id),
                root_key=root_key,
                status=status,
                skip=skip,
                limit=limit
            )
        
        return [
            schemas.RegistryBackupHistory(
                id=backup.id,
                backup_id=backup.backup_id,
                backup_name=backup.backup_name,
                terminal_id=backup.terminal_id,
                root_key=backup.root_key,
                key_path=backup.key_path,
                file_size=backup.file_size,
                status=backup.status,
                reason=backup.reason,
                tags=backup.tags,
                created_at=backup.created_at,
                expire_at=backup.expire_at
            ) for backup in backups
        ]
        
    except Exception as e:
        logger.error(f"获取注册表备份历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取备份历史失败: {str(e)}")


@router.get("/{terminal_id}/registry/statistics", response_model=schemas.RegistryStatistics)
def get_registry_statistics(
    terminal_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["view_stats"]))
):
    """
    获取指定终端的注册表操作统计信息
    包括操作数量、成功率、备份数量等
    """
    # 验证终端存在
    validate_terminal_exists(terminal_id, db)
    
    try:
        # 获取操作统计
        total_ops = crud.get_registry_operations_count(
            db=db,
            terminal_id=str(terminal_id)
        )
        
        successful_ops = crud.get_registry_operations_count(
            db=db,
            terminal_id=str(terminal_id),
            success=True
        )
        
        failed_ops = total_ops - successful_ops
        
        # 获取备份统计
        total_backups = len(crud.get_registry_backups(
            db=db,
            terminal_id=str(terminal_id),
            status="",
            skip=0,
            limit=10000
        ))
        
        active_backups = len(crud.get_registry_backups(
            db=db,
            terminal_id=str(terminal_id),
            status="active",
            skip=0,
            limit=10000
        ))
        
        # 获取搜索统计
        search_logs = crud.get_registry_search_logs(
            db=db,
            terminal_id=str(terminal_id),
            skip=0,
            limit=10000
        )
        
        # 计算平均执行时间
        operations_with_duration = crud.get_registry_operations(
            db=db,
            terminal_id=str(terminal_id),
            skip=0,
            limit=1000
        )
        
        durations = [op.execution_duration for op in operations_with_duration if op.execution_duration]
        avg_duration = sum(durations) / len(durations) if durations else None
        
        return schemas.RegistryStatistics(
            total_operations=total_ops,
            successful_operations=successful_ops,
            failed_operations=failed_ops,
            total_backups=total_backups,
            active_backups=active_backups,
            total_searches=len(search_logs),
            average_execution_time=avg_duration,
            most_accessed_keys=[]  # 可以从操作历史中统计最常访问的键
        )
        
    except Exception as e:
        logger.error(f"获取注册表统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


# === 备份管理接口 ===

@router.delete("/{terminal_id}/registry/backups/{backup_id}")
def delete_registry_backup(
    terminal_id: int,
    backup_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["manage_backups"]))
):
    """
    删除注册表备份
    仅标记为删除状态，不实际删除文件
    """
    # 验证终端存在
    validate_terminal_exists(terminal_id, db)
    
    try:
        success = crud.update_registry_backup_status(db, backup_id, "deleted")
        if success:
            return {"message": "备份删除成功"}
        else:
            raise HTTPException(status_code=404, detail="备份不存在")
            
    except Exception as e:
        logger.error(f"删除注册表备份失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除备份失败: {str(e)}")


@router.post("/{terminal_id}/registry/backups/{backup_id}/verify")
def verify_registry_backup(
    terminal_id: int,
    backup_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(check_registry_permissions(["verify_backups"]))
):
    """
    验证注册表备份完整性
    检查文件是否存在和哈希值是否匹配
    """
    # 验证终端存在
    validate_terminal_exists(terminal_id, db)
    
    try:
        # 获取备份记录
        backup_record = crud.get_registry_backup(db, backup_id)
        if not backup_record:
            raise HTTPException(status_code=404, detail="备份不存在")
        
        # 验证备份文件完整性
        try:
            import hashlib
            import os
            
            if backup_record.file_path and os.path.exists(backup_record.file_path):
                # 重新计算文件哈希并比较
                with open(backup_record.file_path, 'rb') as f:
                    file_hash = hashlib.sha256(f.read()).hexdigest()
                
                if backup_record.file_hash and file_hash == backup_record.file_hash:
                    # 更新验证状态
                    crud.verify_registry_backup(db, backup_id, file_hash)
                    return {"message": "备份验证成功", "verified": True}
                else:
                    return {"message": "备份文件哈希不匹配，文件可能已损坏", "verified": False}
            else:
                return {"message": "备份文件不存在", "verified": False}
        except Exception as verify_error:
            logger.error(f"验证备份文件时出错: {str(verify_error)}")
            return {"message": f"验证过程出错: {str(verify_error)}", "verified": False}
            
    except Exception as e:
        logger.error(f"验证注册表备份失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"备份验证失败: {str(e)}") 