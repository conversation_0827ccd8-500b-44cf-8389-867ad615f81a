<template>
  <div class="field-value-management">
    <!-- 导航栏 -->
    <van-nav-bar
      title="字段值管理"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索字段值或描述"
        @search="handleSearch"
        @clear="handleSearch"
        show-action
        @cancel="handleSearch"
      />
      
      <!-- 字段名称筛选 -->
      <van-field
        v-model="selectedFieldName"
        is-link
        readonly
        label="字段类型"
        placeholder="选择字段类型"
        @click="showFieldPicker = true"
      />
    </div>

    <!-- 下拉刷新 + 列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多数据了"
        @load="onLoad"
      >
        <div v-if="fieldValues.length === 0 && !loading" class="empty-state">
          <van-empty description="暂无字段值数据" />
        </div>
        
        <van-swipe-cell
          v-for="item in fieldValues"
          :key="item.id"
          :before-close="(params: any) => beforeClose(params, item)"
        >
          <van-cell
            :title="item.field_value"
            :label="item.description || '无描述'"
            @click="handleEdit(item)"
          >
            <template #value>
              <div class="field-info">
                <van-tag type="primary">
                  {{ FIELD_NAME_LABELS[item.field_name as FieldName] }}
                </van-tag>
                <div class="create-time">
                  {{ formatDateTime(item.created_at) }}
                </div>
              </div>
            </template>
          </van-cell>
          
          <template #right>
            <van-button 
              square 
              type="danger" 
              text="删除"
              @click="confirmDelete(item)"
            />
          </template>
        </van-swipe-cell>
      </van-list>
    </van-pull-refresh>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      @click="handleAdd"
    />

    <!-- 字段名称选择器 -->
    <van-popup v-model:show="showFieldPicker" position="bottom">
      <van-picker
        :columns="fieldNameOptions"
        @confirm="onFieldNameConfirm"
        @cancel="showFieldPicker = false"
      />
    </van-popup>

    <!-- 编辑表单弹窗 -->
    <van-popup 
      v-model:show="showForm" 
      position="bottom" 
      :style="{ height: 'var(--mobile-popup-small-height, 48svh)' }"
      round
    >
      <div class="form-container">
        <van-nav-bar
          :title="formMode === 'create' ? '新增字段值' : '编辑字段值'"
          left-text="取消"
          @click-left="showForm = false"
        />
        
        <van-form ref="formRef" @submit="handleSubmit">
          <van-cell-group inset>
            <van-field
              v-model="formData.field_name_display"
              is-link
              readonly
              name="field_name"
              label="字段名称"
              placeholder="请选择字段名称"
              :rules="[{ required: true, message: '请选择字段名称' }]"
              @click="showFormFieldPicker = true"
            />
            
            <van-field
              v-model="formData.field_value"
              name="field_value"
              label="字段值"
              placeholder="请输入字段值"
              :rules="[{ required: true, message: '请输入字段值' }]"
            />
            
            <van-field
              v-model="formData.description"
              name="description"
              label="描述"
              type="textarea"
              placeholder="请输入描述（可选）"
              :rows="3"
              autosize
              show-word-limit
              :maxlength="200"
            />
          </van-cell-group>
        </van-form>
        
        <MobilePopupFooter :buttons="formFooterButtons" />
      </div>
    </van-popup>

    <!-- 表单内字段名称选择器 -->
    <van-popup v-model:show="showFormFieldPicker" position="bottom">
      <van-picker
        :columns="fieldNameOptions"
        @confirm="onFormFieldNameConfirm"
        @cancel="showFormFieldPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { showToast, showConfirmDialog, type FormInstance } from 'vant'
import { fieldValueApi } from '@/api/field_value'
import type { FieldValue, FieldName } from '@/types/field_value'
import { FIELD_NAME_LABELS, FIELD_NAMES } from '@/types/field_value'
import { formatDateTime } from '@/utils/format'
import MobilePopupFooter from '@/mobile/components/MobilePopupFooter.vue'

// 搜索和筛选
const searchKeyword = ref('')
const selectedFieldName = ref('')
const showFieldPicker = ref(false)

// 列表数据
const fieldValues = ref<FieldValue[]>([])
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const page = ref(1)
const pageSize = 20

// 表单相关
const showForm = ref(false)
const showFormFieldPicker = ref(false)
const formRef = ref<FormInstance>()
const formMode = ref<'create' | 'edit'>('create')
const formData = reactive({
  id: 0,
  field_name: '',
  field_name_display: '',
  field_value: '',
  description: ''
})

// 字段名称选项
const fieldNameOptions = computed(() => {
  return [
    { text: '全部', value: '' },
    ...Object.entries(FIELD_NAME_LABELS).map(([key, label]) => ({
      text: label,
      value: key
    }))
  ]
})

// 获取数据
const fetchData = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
  }

  try {
    const response = await fieldValueApi.getFieldValues({
      field_name: selectedFieldName.value,
      keyword: searchKeyword.value,
      skip: (page.value - 1) * pageSize,
      limit: pageSize
    })

    const newData = response.data.data || []
    
    if (isRefresh) {
      fieldValues.value = newData
    } else {
      fieldValues.value.push(...newData)
    }

    // 判断是否还有更多数据
    if (newData.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('获取字段值列表失败:', error)
    showToast('获取数据失败')
  }
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  await fetchData(true)
  refreshing.value = false
}

// 上拉加载
const onLoad = async () => {
  if (refreshing.value) return
  loading.value = true
  await fetchData()
  loading.value = false
}

// 搜索
const handleSearch = () => {
  fetchData(true)
}

// 字段名称筛选确认
const onFieldNameConfirm = ({ selectedOptions }: any) => {
  selectedFieldName.value = selectedOptions[0].value
  showFieldPicker.value = false
  fetchData(true)
}

// 新增
const handleAdd = () => {
  formMode.value = 'create'
  resetForm()
  showForm.value = true
}

// 编辑
const handleEdit = (item: FieldValue) => {
  formMode.value = 'edit'
  formData.id = item.id
  formData.field_name = item.field_name
  formData.field_name_display = FIELD_NAME_LABELS[item.field_name as FieldName]
  formData.field_value = item.field_value
  formData.description = item.description || ''
  showForm.value = true
}

// 表单内字段名称选择确认
const onFormFieldNameConfirm = ({ selectedOptions }: any) => {
  const selected = selectedOptions[0]
  if (selected.value) {
    formData.field_name = selected.value
    formData.field_name_display = selected.text
  }
  showFormFieldPicker.value = false
}

// 重置表单
const resetForm = () => {
  formData.id = 0
  formData.field_name = ''
  formData.field_name_display = ''
  formData.field_value = ''
  formData.description = ''
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    if (!formData.field_name) {
      showToast('请选择字段名称')
      return
    }

    if (formMode.value === 'create') {
      await fieldValueApi.createFieldValue({
        field_name: formData.field_name,
        field_value: formData.field_value,
        description: formData.description
      })
      showToast('创建成功')
    } else {
      await fieldValueApi.updateFieldValue(formData.id, {
        field_name: formData.field_name,
        field_value: formData.field_value,
        description: formData.description
      })
      showToast('更新成功')
    }
    
    showForm.value = false
    fetchData(true)
  } catch (error) {
    console.error('提交失败:', error)
    showToast('提交失败')
  }
}

// 删除确认
const confirmDelete = (item: FieldValue) => {
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除字段值"${item.field_value}"吗？`
  }).then(() => {
    handleDelete(item)
  }).catch(() => {
    // 用户取消
  })
}

// 删除
const handleDelete = async (item: FieldValue) => {
  try {
    await fieldValueApi.deleteFieldValue(item.id)
    showToast('删除成功')
    fetchData(true)
  } catch (error) {
    console.error('删除失败:', error)
    showToast('删除失败')
  }
}

// 滑动删除前确认
const beforeClose = (params: any, item: FieldValue): boolean | Promise<boolean> => {
  const { position } = params
  if (position === 'right') {
    return new Promise<boolean>((resolve) => {
      showConfirmDialog({
        title: '确认删除',
        message: `确定要删除字段值"${item.field_value}"吗？`
      })
        .then(() => {
          handleDelete(item)
          resolve(true)
        })
        .catch(() => resolve(false))
    })
  }
  return true
}

// 表单底部按钮配置
const formFooterButtons = computed(() => [
  {
    text: '保存',
    type: 'primary' as const,
    onClick: handleSubmit
  }
])

onMounted(() => {
  fetchData(true)
})
</script>

<style scoped>
.field-value-management {
  height: 100vh;
  background-color: #f5f5f5;
}

.search-section {
  background: white;
  padding-bottom: 8px;
}

.field-info {
  text-align: right;
}

.create-time {
  font-size: 12px;
  color: #969799;
  margin-top: 4px;
}

.empty-state {
  padding: 40px 20px;
}

.form-container {
  height: 100%;
  background: #f5f5f5;
}

.form-container .van-cell-group {
  margin: 16px;
}
</style>