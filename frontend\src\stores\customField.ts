import { defineStore } from 'pinia'
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { customFieldApi } from '@/api/custom_field'
import type {
  CustomField,
  CustomFieldCreate,
  CustomFieldUpdate,
  FieldPresetRequest,
  FieldSortItem,
  AppliesTo
} from '@/types/custom_field'

interface SearchForm {
  keyword: string
  field_type: string
  applies_to: string
  is_active: boolean | undefined
}

interface Pagination {
  page: number
  limit: number
  total: number
}

export const useCustomFieldStore = defineStore('customField', () => {
  // ============ 状态定义 ============
  const fieldList = ref<CustomField[]>([])
  const loading = ref(false)
  const searchForm = reactive<SearchForm>({
    keyword: '',
    field_type: '',
    applies_to: '',
    is_active: undefined
  })
  
  const pagination = reactive<Pagination>({
    page: 1,
    limit: 20,
    total: 0
  })

  // 对话框状态
  const fieldDialogVisible = ref(false)
  const fieldDialogMode = ref<'create' | 'edit'>('create')
  const currentField = ref<CustomField | null>(null)
  
  const presetDialogVisible = ref(false)
  const presetLoading = ref(false)
  const presetForm = reactive({
    preset_type: '',
    applies_to: 'asset' as AppliesTo
  })

  // ============ 计算属性 ============
  const hasFields = computed(() => fieldList.value.length > 0)
  const isLoading = computed(() => loading.value)
  
  // ============ Actions ============
  
  // 获取字段列表
  const fetchFields = async (resetPage = false) => {
    if (loading.value) return // 防止重复请求
    
    try {
      loading.value = true
      
      if (resetPage) {
        pagination.page = 1
      }
      
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        keyword: searchForm.keyword || undefined,
        field_type: searchForm.field_type || undefined,
        applies_to: searchForm.applies_to || undefined,
        is_active: searchForm.is_active
      }
      
      const response = await customFieldApi.getCustomFields(params)
      
      // response.data 是 CustomFieldListResponse 类型
      if (response && response.data) {
        fieldList.value = response.data.data || []
        pagination.total = response.data.total || 0
      } else {
        fieldList.value = []
        pagination.total = 0
      }
      
      console.log('获取字段列表成功:', {
        count: fieldList.value.length,
        total: pagination.total
      })
    } catch (error) {
      console.error('获取字段列表失败:', error)
      ElMessage.error('获取字段列表失败')
      fieldList.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // 搜索字段
  const searchFields = () => {
    fetchFields(true)
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.keyword = ''
    searchForm.field_type = ''
    searchForm.applies_to = ''
    searchForm.is_active = undefined
    fetchFields(true)
  }

  // 创建字段
  const createField = async (data: CustomFieldCreate) => {
    try {
      await customFieldApi.createCustomField(data)
      ElMessage.success('字段创建成功')
      closeFieldDialog()
      fetchFields()
    } catch (error) {
      console.error('创建字段失败:', error)
      ElMessage.error('创建字段失败')
      throw error
    }
  }

  // 更新字段
  const updateField = async (id: number, data: CustomFieldUpdate) => {
    try {
      await customFieldApi.updateCustomField(id, data)
      ElMessage.success('字段更新成功')
      closeFieldDialog()
      fetchFields()
    } catch (error) {
      console.error('更新字段失败:', error)
      ElMessage.error('更新字段失败')
      throw error
    }
  }

  // 删除字段
  const deleteField = async (field: CustomField) => {
    try {
      await customFieldApi.deleteCustomField(field.id)
      ElMessage.success('字段删除成功')
      fetchFields()
    } catch (error) {
      console.error('删除字段失败:', error)
      ElMessage.error('删除字段失败')
    }
  }

  // 切换字段状态
  const toggleFieldStatus = async (field: CustomField) => {
    const originalStatus = field.is_active
    
    try {
      await customFieldApi.updateCustomField(field.id, {
        is_active: field.is_active
      })
      ElMessage.success(`字段已${field.is_active ? '启用' : '禁用'}`)
    } catch (error) {
      console.error('更新字段状态失败:', error)
      ElMessage.error('更新字段状态失败')
      // 回滚状态
      field.is_active = originalStatus
    }
  }

  // 移动字段排序
  const moveField = async (field: CustomField, direction: 'up' | 'down') => {
    const currentIndex = fieldList.value.findIndex(f => f.id === field.id)
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    
    if (targetIndex < 0 || targetIndex >= fieldList.value.length) return
    
    const currentField = fieldList.value[currentIndex]
    const targetField = fieldList.value[targetIndex]
    
    const updates: FieldSortItem[] = [
      { id: currentField.id, sort_order: targetField.sort_order },
      { id: targetField.id, sort_order: currentField.sort_order }
    ]
    
    try {
      await customFieldApi.batchUpdateSortOrders(updates)
      
      // 更新本地数据
      const temp = currentField.sort_order
      currentField.sort_order = targetField.sort_order
      targetField.sort_order = temp
      
      // 重新排序列表
      fieldList.value.sort((a, b) => a.sort_order - b.sort_order)
      
      ElMessage.success('排序更新成功')
    } catch (error) {
      console.error('更新排序失败:', error)
      ElMessage.error('更新排序失败')
    }
  }

  // 创建预设字段
  const createPresetFields = async () => {
    try {
      presetLoading.value = true
      const response = await customFieldApi.createPresetFields({
        preset_type: presetForm.preset_type,
        applies_to: presetForm.applies_to
      })
      
      ElMessage.success(`成功创建 ${response.data?.length || 0} 个预设字段`)
      closePresetDialog()
      fetchFields()
    } catch (error) {
      console.error('创建预设字段失败:', error)
      ElMessage.error('创建预设字段失败')
    } finally {
      presetLoading.value = false
    }
  }

  // ============ 对话框管理 ============
  
  // 打开字段对话框
  const openFieldDialog = (field?: CustomField) => {
    fieldDialogMode.value = field ? 'edit' : 'create'
    currentField.value = field || null
    fieldDialogVisible.value = true
  }

  // 关闭字段对话框
  const closeFieldDialog = () => {
    fieldDialogVisible.value = false
    currentField.value = null
  }

  // 打开预设字段对话框
  const openPresetDialog = () => {
    presetForm.preset_type = ''
    presetForm.applies_to = 'asset' as AppliesTo
    presetDialogVisible.value = true
  }

  // 关闭预设字段对话框
  const closePresetDialog = () => {
    presetDialogVisible.value = false
  }

  // ============ 分页管理 ============
  
  // 改变页码
  const changePage = (page: number) => {
    pagination.page = page
    fetchFields()
  }

  // 改变每页大小
  const changePageSize = (size: number) => {
    pagination.limit = size
    pagination.page = 1
    fetchFields()
  }

  // ============ 初始化 ============
  const init = () => {
    fetchFields()
  }

  return {
    // 状态
    fieldList,
    loading,
    searchForm,
    pagination,
    fieldDialogVisible,
    fieldDialogMode,
    currentField,
    presetDialogVisible,
    presetLoading,
    presetForm,
    
    // 计算属性
    hasFields,
    isLoading,
    
    // Actions
    fetchFields,
    searchFields,
    resetSearch,
    createField,
    updateField,
    deleteField,
    toggleFieldStatus,
    moveField,
    createPresetFields,
    openFieldDialog,
    closeFieldDialog,
    openPresetDialog,
    closePresetDialog,
    changePage,
    changePageSize,
    init
  }
}) 