"""
第三阶段快速测试脚本
用于快速验证第三阶段开发的核心功能
"""

import sys
import os
import logging
import time
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.database import get_db
from app.models.ecology_user import EcologyUser
from app.models.email import EmailMember
from app.services.email_extid_completion import EmailExtidCompletionService
from app.services.personnel_email_sync import PersonnelEmailSyncService
from tests.stage3_testing_optimization.optimization.sync_performance import OptimizedPersonnelSyncService
from tests.stage3_testing_optimization.monitoring.sync_status_monitor import SyncStatusMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

class QuickTester:
    """快速测试器"""
    
    def __init__(self):
        self.db = None
        self.test_results = {}
        
    def setup(self):
        """设置测试环境"""
        try:
            self.db = next(get_db())
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def cleanup(self):
        """清理测试环境"""
        if self.db:
            self.db.close()
            logger.info("✅ 数据库连接已关闭")
    
    def test_basic_functionality(self):
        """测试基础功能"""
        logger.info("=== 测试基础功能 ===")
        
        try:
            # 测试工号补全服务
            completion_service = EmailExtidCompletionService(self.db)
            stats = completion_service.get_completion_statistics()

            logger.info(f"工号补全统计: 总成员={stats.total_members}, "
                       f"有工号={stats.has_extid}, "
                       f"无工号={stats.missing_extid}")

            # 测试分页查询
            matches = asyncio.run(completion_service.get_completion_candidates_paginated(page=1, page_size=5))
            logger.info(f"姓名匹配查询: 找到{matches.total}个匹配项")
            
            self.test_results['basic_functionality'] = 'passed'
            logger.info("✅ 基础功能测试通过")
            
        except Exception as e:
            logger.error(f"❌ 基础功能测试失败: {e}")
            self.test_results['basic_functionality'] = 'failed'
    
    def test_sync_functionality(self):
        """测试同步功能"""
        logger.info("=== 测试同步功能 ===")
        
        try:
            # 测试人员同步服务
            sync_service = PersonnelEmailSyncService(self.db)
            
            # 执行数据一致性检查
            start_time = time.time()
            consistency_result = asyncio.run(sync_service.check_data_consistency())
            check_time = time.time() - start_time

            logger.info(f"数据一致性检查: 人员={consistency_result['stats']['total_personnel']}, "
                       f"邮箱={consistency_result['stats']['total_email_members']}, "
                       f"不一致项={consistency_result['total_inconsistencies']}, "
                       f"耗时={check_time:.3f}秒")

            # 执行试运行同步
            start_time = time.time()
            sync_result = asyncio.run(sync_service.sync_personnel_to_email(dry_run=True))
            sync_time = time.time() - start_time

            logger.info(f"试运行同步: 处理={sync_result.processed_count}, "
                       f"创建申请={sync_result.created_requests}, "
                       f"更新={sync_result.updated_count}, "
                       f"禁用={sync_result.disabled_count}, "
                       f"耗时={sync_time:.3f}秒")
            
            self.test_results['sync_functionality'] = 'passed'
            logger.info("✅ 同步功能测试通过")
            
        except Exception as e:
            logger.error(f"❌ 同步功能测试失败: {e}")
            self.test_results['sync_functionality'] = 'failed'
    
    def test_performance_optimization(self):
        """测试性能优化"""
        logger.info("=== 测试性能优化 ===")
        
        try:
            # 测试原始同步服务
            original_service = PersonnelEmailSyncService(self.db)
            start_time = time.time()
            original_result = asyncio.run(original_service.sync_personnel_to_email(dry_run=True))
            original_time = time.time() - start_time

            # 测试优化后的同步服务
            optimized_service = OptimizedPersonnelSyncService(self.db, batch_size=50)
            optimized_result, metrics = asyncio.run(optimized_service.optimized_sync_personnel_to_email(
                dry_run=True, use_parallel=False
            ))
            
            # 比较性能
            improvement = ((original_time - metrics.execution_time) / original_time * 100) if original_time > 0 else 0
            
            logger.info(f"性能对比:")
            logger.info(f"  原始方法: {original_time:.3f}秒, 处理{original_result.processed_count}条")
            logger.info(f"  优化方法: {metrics.execution_time:.3f}秒, 处理{optimized_result.processed_count}条")
            logger.info(f"  性能改进: {improvement:.1f}%")
            logger.info(f"  吞吐量: {metrics.throughput:.2f}条/秒")
            
            self.test_results['performance_optimization'] = 'passed'
            logger.info("✅ 性能优化测试通过")
            
        except Exception as e:
            logger.error(f"❌ 性能优化测试失败: {e}")
            self.test_results['performance_optimization'] = 'failed'
    
    def test_monitoring_system(self):
        """测试监控系统"""
        logger.info("=== 测试监控系统 ===")
        
        try:
            # 测试监控服务
            monitor = SyncStatusMonitor(self.db)
            
            # 获取当前状态
            current_status = monitor.get_current_status()
            logger.info(f"当前同步状态: {current_status['status']}")
            
            # 获取同步指标
            try:
                metrics = monitor.get_sync_metrics()
                logger.info(f"同步指标: 人员={metrics['total_personnel']}, "
                           f"邮箱={metrics['total_email_members']}, "
                           f"匹配={metrics['matched_count']}")
            except Exception as e:
                logger.warning(f"获取同步指标失败: {e}")
                # 创建模拟指标用于测试
                metrics = {
                    'total_personnel': 0,
                    'total_email_members': 0,
                    'matched_count': 0
                }
            
            # 检查健康状态
            health_status = monitor.check_health_status()
            logger.info(f"系统健康状态: {health_status['overall_status']}")
            
            if health_status['issues']:
                logger.warning(f"发现问题: {', '.join(health_status['issues'])}")
            
            self.test_results['monitoring_system'] = 'passed'
            logger.info("✅ 监控系统测试通过")
            
        except Exception as e:
            logger.error(f"❌ 监控系统测试失败: {e}")
            self.test_results['monitoring_system'] = 'failed'
    
    def test_error_handling(self):
        """测试错误处理"""
        logger.info("=== 测试错误处理 ===")
        
        try:
            completion_service = EmailExtidCompletionService(self.db)
            
            # 测试无效参数处理
            try:
                matches = completion_service.find_name_matches(page=0, page_size=10)
                if matches.page >= 1:
                    logger.info("✅ 无效页码被正确处理")
                else:
                    logger.warning("⚠️ 无效页码处理可能有问题")
            except Exception as e:
                logger.info(f"✅ 无效页码正确抛出异常: {type(e).__name__}")
            
            # 测试过大页面大小处理
            try:
                matches = completion_service.find_name_matches(page=1, page_size=10000)
                if matches.page_size <= 1000:
                    logger.info("✅ 过大页面大小被正确限制")
                else:
                    logger.warning("⚠️ 过大页面大小处理可能有问题")
            except Exception as e:
                logger.info(f"✅ 过大页面大小正确抛出异常: {type(e).__name__}")
            
            self.test_results['error_handling'] = 'passed'
            logger.info("✅ 错误处理测试通过")
            
        except Exception as e:
            logger.error(f"❌ 错误处理测试失败: {e}")
            self.test_results['error_handling'] = 'failed'
    
    def run_all_tests(self):
        """运行所有快速测试"""
        logger.info("开始第三阶段快速测试")
        logger.info("=" * 50)
        
        if not self.setup():
            return False
        
        try:
            # 运行各项测试
            self.test_basic_functionality()
            self.test_sync_functionality()
            self.test_performance_optimization()
            self.test_monitoring_system()
            self.test_error_handling()
            
            # 输出测试结果摘要
            self.print_summary()
            
            return all(result == 'passed' for result in self.test_results.values())
            
        finally:
            self.cleanup()
    
    def print_summary(self):
        """打印测试摘要"""
        logger.info("\n" + "=" * 50)
        logger.info("快速测试结果摘要")
        logger.info("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result == 'passed')
        failed_tests = total_tests - passed_tests
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过: {passed_tests}")
        logger.info(f"失败: {failed_tests}")
        
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == 'passed' else "❌"
            logger.info(f"{status_icon} {test_name}: {result}")
        
        if failed_tests == 0:
            logger.info("\n🎉 所有快速测试通过！第三阶段功能正常")
        else:
            logger.warning(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查相关功能")

def main():
    """主函数"""
    tester = QuickTester()
    
    try:
        success = tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
