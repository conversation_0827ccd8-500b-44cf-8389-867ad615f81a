// 部门类型定义
export interface Department {
  dept_id: string
  name: string
  parent_id?: string
  order?: number
  children?: Department[]
  created_at?: string
  updated_at?: string
}

// 成员类型定义
export interface Member {
  extid: string
  email: string
  name: string
  department_id: string
  department_name?: string
  position?: string
  mobile?: string
  tel?: string
  is_active?: boolean
  pop_smtp_enabled?: boolean
  imap_smtp_enabled?: boolean
  secure_login_enabled?: boolean
  force_secure_login?: boolean
  created_at?: string
  updated_at?: string
}

// 邮箱申请类型定义
export interface EmailCreationRequest {
  id: number
  job_number: string
  user_name: string
  dept_name?: string
  job_title_name?: string
  mobile?: string
  status: 'pending' | 'approved' | 'rejected' | 'created'
  reason?: string
  requested_by: string
  approved_by?: string
  approved_at?: string
  created_email?: string
  notes?: string
  created_at: string
  updated_at: string
}

// 申请统计信息类型
export interface RequestStats {
  pending_count: number
  approved_count: number
  rejected_count: number
  created_count: number
  total_requests: number
  active_email_count: number
  email_quota: number
  available_quota: number
}

// 审批操作类型
export interface ApprovalAction {
  action: 'approve' | 'reject'
  notes?: string
}

// 批量审批类型
export interface BatchApprovalAction {
  request_ids: number[]
  action: 'approve' | 'reject'
  notes?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success?: boolean
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 部门表单类型
export interface DepartmentForm {
  dept_id: string
  name: string
  parent_id?: string
  order?: number
}

// 成员表单类型
export interface MemberForm {
  extid: string
  email: string
  name: string
  password?: string
  department_id: string
  position?: string
  mobile?: string
  tel?: string
  pop_smtp_enabled?: boolean
  imap_smtp_enabled?: boolean
  secure_login_enabled?: boolean
  force_secure_login?: boolean
}

// 成员权限类型（前端界面使用布尔值）
export interface MemberPermissions {
  pop_smtp_enabled: boolean
  imap_smtp_enabled: boolean
  secure_login_enabled: boolean
  force_secure_login: boolean
}

// API权限类型（后端API使用数字）
export interface MemberPermissionsAPI {
  pop_smtp_enabled: number
  imap_smtp_enabled: number
  secure_login_enabled: number
  force_secure_login: number
}

// 搜索参数类型
export interface MemberSearchParams {
  page?: number
  size?: number
  department_id?: string
  search?: string
}

export interface DepartmentSearchParams {
  page?: number
  size?: number
  search?: string
}

// 邮箱配置类型定义
export interface EmailConfig {
  id?: number
  corp_id: string
  corp_secret: string
  app_name?: string
  app_key?: string
  api_base_url?: string
  is_active: boolean
  created_at?: string
  updated_at?: string
}

// 群组类型定义
export interface EmailGroup {
  groupid: string
  groupname: string
  groupdesc?: string
  userlist?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface EmailGroupForm {
  groupid: string
  groupname: string
  groupdesc?: string
  userlist: string
  is_active: boolean
}

// 标签类型定义
export interface EmailTag {
  tagid: number
  tagname: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface EmailTagForm {
  tagid: number | null
  tagname: string
  is_active: boolean
}

// 群组和标签API参数类型
export interface GroupSearchParams {
  page?: number
  size?: number
  skip?: number
  limit?: number
  search?: string
}

export interface TagSearchParams {
  page?: number
  size?: number
  skip?: number
  limit?: number
} 