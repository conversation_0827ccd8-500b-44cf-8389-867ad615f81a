import type { CustomField } from '@/types/custom_field'

/**
 * 自定义字段数据处理工具函数
 */

/**
 * 准备自定义字段值数据用于API提交
 * @param fields 自定义字段定义列表
 * @param fieldData 字段值数据对象
 * @returns 格式化后的字段值数组
 */
export const prepareCustomFieldValues = (
  fields: CustomField[], 
  fieldData: Record<string, any>
): Array<{ custom_field_id: number; value: string }> => {
  return fields.map(field => ({
    custom_field_id: field.id,
    value: fieldData[field.name] || ''
  }))
}

/**
 * 将API返回的字段值转换为表单数据
 * @param fieldValues API返回的字段值数组
 * @returns 字段值对象，以字段名为key
 */
export const convertApiFieldValuesToFormData = (
  fieldValues: Array<{ custom_field?: CustomField; value?: string }>
): Record<string, any> => {
  const formData: Record<string, any> = {}
  
  fieldValues.forEach(value => {
    if (value.custom_field) {
      formData[value.custom_field.name] = value.value || ''
    }
  })
  
  return formData
}

/**
 * 验证自定义字段值
 * @param field 字段定义
 * @param value 字段值
 * @returns 验证结果 { valid: boolean; message?: string }
 */
export const validateCustomFieldValue = (
  field: CustomField, 
  value: any
): { valid: boolean; message?: string } => {
  // 必填验证
  if (field.is_required && (!value || value === '')) {
    return {
      valid: false,
      message: `${field.label}为必填项`
    }
  }
  
  // 类型特定验证
  if (value && field.validation_rules) {
    const rules = field.validation_rules
    
    // 长度验证
    if (rules.min_length !== undefined && value.length < rules.min_length) {
      return {
        valid: false,
        message: `${field.label}长度不能少于${rules.min_length}个字符`
      }
    }
    
    if (rules.max_length !== undefined && value.length > rules.max_length) {
      return {
        valid: false,
        message: `${field.label}长度不能超过${rules.max_length}个字符`
      }
    }
    
    // 数值验证
    if (field.field_type === 'number') {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        return {
          valid: false,
          message: `${field.label}必须是有效数字`
        }
      }
      
      if (rules.min_value !== undefined && numValue < rules.min_value) {
        return {
          valid: false,
          message: `${field.label}不能小于${rules.min_value}`
        }
      }
      
      if (rules.max_value !== undefined && numValue > rules.max_value) {
        return {
          valid: false,
          message: `${field.label}不能大于${rules.max_value}`
        }
      }
    }
    
    // 正则验证
    if (rules.pattern) {
      const regex = new RegExp(rules.pattern)
      if (!regex.test(value)) {
        return {
          valid: false,
          message: rules.custom_message || `${field.label}格式不正确`
        }
      }
    }
  }
  
  return { valid: true }
}

/**
 * 批量验证自定义字段值
 * @param fields 字段定义列表
 * @param fieldData 字段值数据
 * @returns 验证结果 { valid: boolean; errors: Record<string, string> }
 */
export const validateAllCustomFields = (
  fields: CustomField[], 
  fieldData: Record<string, any>
): { valid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {}
  
  fields.forEach(field => {
    const value = fieldData[field.name]
    const result = validateCustomFieldValue(field, value)
    
    if (!result.valid && result.message) {
      errors[field.name] = result.message
    }
  })
  
  return {
    valid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 处理移动端文件字段值，确保格式正确
 * @param field 字段定义
 * @param value 字段值
 * @returns 处理后的值
 */
export const processMobileFileFieldValue = (field: CustomField, value: any): string => {
  if (!value) return ''
  
  // 如果是文件类型字段
  if (field.field_type === 'file') {
    // 如果已经是字符串，直接返回
    if (typeof value === 'string') {
      return value
    }
    
    // 如果是数组，转换为JSON字符串
    if (Array.isArray(value)) {
      return JSON.stringify(value)
    }
    
    // 如果是对象，转换为JSON字符串
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }
  }
  
  return String(value)
}

/**
 * 获取文件字段的预览URL
 * @param value 字段值
 * @returns 预览URL数组
 */
export const getFileFieldPreviewUrls = (value: any): string[] => {
  if (!value) return []
  
  try {
    if (typeof value === 'string') {
      if (value.startsWith('[') || value.startsWith('{')) {
        const fileData = JSON.parse(value)
        if (Array.isArray(fileData)) {
          return fileData.map(f => f.url || f.path || '').filter(Boolean)
        } else {
          return [fileData.url || fileData.path || ''].filter(Boolean)
        }
      } else {
        // 直接URL
        return [value]
      }
    }
  } catch {
    // 解析失败，返回空数组
  }
  
  return []
}

/**
 * 格式化字段值用于显示
 * @param field 字段定义
 * @param value 字段值
 * @returns 格式化后的显示值
 */
export const formatCustomFieldValue = (field: CustomField, value: any): string => {
  if (!value || value === '') {
    return '-'
  }
  
  switch (field.field_type) {
    case 'select':
    case 'radio':
      // 从选项中查找对应的标签
      const option = field.options?.choices?.find(choice => choice.value === value)
      return option?.label || value
      
    case 'checkbox':
      // 多选值可能是数组
      if (Array.isArray(value)) {
        const labels = value.map(v => {
          const option = field.options?.choices?.find(choice => choice.value === v)
          return option?.label || v
        })
        return labels.join(', ')
      }
      return value
      
    case 'date':
      // 格式化日期
      try {
        return new Date(value).toLocaleDateString('zh-CN')
      } catch {
        return value
      }
      
    case 'datetime':
      // 格式化日期时间
      try {
        return new Date(value).toLocaleString('zh-CN')
      } catch {
        return value
      }
      
    case 'file':
      // 文件类型显示文件名或URL
      try {
        if (typeof value === 'string') {
          // 尝试解析JSON
          if (value.startsWith('[') || value.startsWith('{')) {
            const fileData = JSON.parse(value)
            if (Array.isArray(fileData)) {
              // 多文件显示
              return fileData.map(f => f.filename || f.name || '文件').join(', ')
            } else {
              // 单文件
              return fileData.filename || fileData.name || '文件'
            }
          } else {
            // 直接URL，提取文件名
            return value.split('/').pop() || '文件'
          }
        }
        return '文件'
      } catch {
        return value || '文件'
      }
      
    default:
      return String(value)
  }
} 