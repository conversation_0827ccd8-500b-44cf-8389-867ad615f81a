from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

# 邮箱配置相关模式
class EmailConfigBase(BaseModel):
    corp_id: str = Field(..., description="企业ID")
    corp_secret: str = Field(..., description="企业密钥")
    app_name: Optional[str] = Field(default="", description="应用名称")
    app_key: Optional[str] = Field(default="", description="应用标识")
    api_base_url: str = Field(default="https://api.exmail.qq.com/cgi-bin", description="API基础URL")
    is_active: bool = Field(default=True, description="是否启用")

class EmailConfigCreate(EmailConfigBase):
    pass

class EmailConfigUpdate(BaseModel):
    corp_id: Optional[str] = None
    corp_secret: Optional[str] = None
    app_name: Optional[str] = None
    app_key: Optional[str] = None
    api_base_url: Optional[str] = None
    is_active: Optional[bool] = None

class EmailConfig(EmailConfigBase):
    id: Optional[int] = None
    access_token: Optional[str] = None
    token_expires_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 部门相关模式
class EmailDepartmentBase(BaseModel):
    dept_id: str = Field(..., description="部门ID")
    name: str = Field(..., description="部门名称")
    parent_id: Optional[str] = Field(None, description="父部门ID")
    order: int = Field(default=0, description="排序")
    is_active: bool = Field(default=True, description="是否启用")

class EmailDepartmentCreate(EmailDepartmentBase):
    pass

class EmailDepartmentUpdate(BaseModel):
    name: Optional[str] = None
    parent_id: Optional[str] = None
    order: Optional[int] = None
    is_active: Optional[bool] = None

class EmailDepartment(EmailDepartmentBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class EmailDepartmentWithMembers(EmailDepartment):
    members: List["EmailMember"] = []

# 成员相关模式
class EmailMemberBase(BaseModel):
    extid: Optional[str] = Field(None, description="工号")
    email: str = Field(..., description="邮箱地址（对应腾讯企业邮箱API的userid字段）")
    name: str = Field(..., description="姓名")
    department_id: str = Field(..., description="部门ID")
    position: Optional[str] = Field(None, description="职位")
    mobile: Optional[str] = Field(None, description="手机号")
    tel: Optional[str] = Field(None, description="固定电话")
    cpwd_login: int = Field(default=1, description="是否允许使用密码登录")
    force_secure_login: int = Field(default=0, description="强制启用安全登录 (0:关闭, 1:开启)")
    imap_smtp_enabled: int = Field(default=1, description="IMAP/SMTP服务 (0:关闭, 1:开启)")
    pop_smtp_enabled: int = Field(default=1, description="POP/SMTP服务 (0:关闭, 1:开启)")
    secure_login_enabled: int = Field(default=0, description="是否启用安全登录 (0:关闭, 1:开启)")
    extattr: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    is_active: bool = Field(default=True, description="是否启用")

class EmailMemberCreate(EmailMemberBase):
    password: str = Field(..., description="用户密码，创建成员时必需")

class EmailMemberUpdate(BaseModel):
    extid: Optional[str] = Field(None, description="工号")
    email: Optional[str] = Field(None, description="邮箱地址（对应腾讯企业邮箱API的userid字段）")
    name: Optional[str] = Field(None, description="姓名")
    department_id: Optional[str] = Field(None, description="部门ID")
    position: Optional[str] = Field(None, description="职位")
    mobile: Optional[str] = Field(None, description="手机号")
    tel: Optional[str] = Field(None, description="固定电话")
    password: Optional[str] = Field(None, description="用户密码")
    cpwd_login: Optional[int] = Field(None, description="是否允许使用密码登录")
    force_secure_login: Optional[int] = Field(None, description="强制启用安全登录 (0:关闭, 1:开启)")
    imap_smtp_enabled: Optional[int] = Field(None, description="IMAP/SMTP服务 (0:关闭, 1:开启)")
    pop_smtp_enabled: Optional[int] = Field(None, description="POP/SMTP服务 (0:关闭, 1:开启)")
    secure_login_enabled: Optional[int] = Field(None, description="是否启用安全登录 (0:关闭, 1:开启)")
    extattr: Optional[Dict[str, Any]] = Field(None, description="扩展属性")
    is_active: Optional[bool] = Field(None, description="是否启用")

# 成员状态更新（启用/禁用）
class EmailMemberActiveUpdate(BaseModel):
    is_active: bool = Field(..., description="是否启用")

class EmailMember(EmailMemberBase):
    id: int
    created_at: datetime
    updated_at: datetime

    @property
    def userid(self) -> str:
        """
        返回腾讯企业邮箱API所需的userid值
        
        注意：腾讯企业邮箱API中的userid字段值为邮箱地址格式
        """
        return self.email

    class Config:
        from_attributes = True

class EmailMemberWithDepartment(EmailMember):
    department: Optional[EmailDepartment] = None

# 标签相关模式
class EmailTagBase(BaseModel):
    tagid: int = Field(..., description="标签ID")
    tagname: str = Field(..., description="标签名称")
    is_active: bool = Field(default=True, description="是否启用")

class EmailTagCreate(EmailTagBase):
    pass

class EmailTagUpdate(BaseModel):
    tagname: Optional[str] = None
    is_active: Optional[bool] = None

class EmailTag(EmailTagBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 邮件群组相关模式
class EmailGroupBase(BaseModel):
    groupid: str = Field(..., description="群组ID")
    groupname: str = Field(..., description="群组名称")
    userlist: Optional[str] = Field(None, description="成员列表")
    groupdesc: Optional[str] = Field(None, description="群组描述")
    is_active: bool = Field(default=True, description="是否启用")

class EmailGroupCreate(EmailGroupBase):
    pass

class EmailGroupUpdate(BaseModel):
    groupname: Optional[str] = None
    userlist: Optional[str] = None
    groupdesc: Optional[str] = None
    is_active: Optional[bool] = None

class EmailGroup(EmailGroupBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# API响应模式
class EmailAPIResponse(BaseModel):
    errcode: int
    errmsg: str
    data: Optional[Dict[str, Any]] = None

# 分页模式
class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int

# 邮箱成员分页响应模式
class EmailMemberPaginatedResponse(BaseModel):
    items: List[Dict[str, Any]]
    total: int
    page: int
    size: int
    pages: int

# 登录权限管理相关模式
class EmailLoginPermissionBase(BaseModel):
    force_secure_login: int = Field(default=0, description="强制启用安全登录 (0:关闭, 1:开启)")
    imap_smtp_enabled: int = Field(default=1, description="IMAP/SMTP服务 (0:关闭, 1:开启)")
    pop_smtp_enabled: int = Field(default=1, description="POP/SMTP服务 (0:关闭, 1:开启)")
    secure_login_enabled: int = Field(default=0, description="是否启用安全登录 (0:关闭, 1:开启)")

class EmailLoginPermissionUpdate(BaseModel):
    force_secure_login: Optional[int] = Field(None, description="强制启用安全登录 (0:关闭, 1:开启)")
    imap_smtp_enabled: Optional[int] = Field(None, description="IMAP/SMTP服务 (0:关闭, 1:开启)")
    pop_smtp_enabled: Optional[int] = Field(None, description="POP/SMTP服务 (0:关闭, 1:开启)")
    secure_login_enabled: Optional[int] = Field(None, description="是否启用安全登录 (0:关闭, 1:开启)")

class EmailUserOptionRequest(BaseModel):
    userid: str = Field(..., description="成员邮箱地址")
    type: List[int] = Field(..., description="功能设置属性类型: 1-强制启用安全登录, 2-IMAP/SMTP服务, 3-POP/SMTP服务, 4-是否启用安全登录")

class EmailUserOptionResponse(BaseModel):
    errcode: int = Field(..., description="返回码")
    errmsg: str = Field(..., description="返回消息")
    option: Optional[List[Dict[str, Union[int, str]]]] = Field(None, description="功能设置属性列表，type为int，value为str")

class EmailUserOptionSetRequest(BaseModel):
    userid: str = Field(..., description="成员邮箱地址")
    option: List[Dict[str, Union[int, str]]] = Field(..., description="功能设置属性: [{type: 1, value: '0'}, {type: 2, value: '1'}]") 