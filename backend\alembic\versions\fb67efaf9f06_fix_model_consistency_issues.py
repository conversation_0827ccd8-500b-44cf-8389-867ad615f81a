"""fix_model_consistency_issues

Revision ID: fb67efaf9f06
Revises: 7bdbca0b6716
Create Date: 2025-06-24 09:28:54.986425

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fb67efaf9f06'
down_revision: Union[str, None] = '7bdbca0b6716'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('asset_settings', 'id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('ldap_config', 'priority',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_comment='匹配优先级，数字越小优先级越高',
               existing_server_default=sa.text('1'))
    op.alter_column('ldap_config', 'auto_select_enabled',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_comment='是否启用基于IP的自动选择',
               existing_server_default=sa.text('true'))
    op.alter_column('users', 'is_builtin',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_comment='是否为内置账号',
               existing_server_default=sa.text('false'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'is_builtin',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_comment='是否为内置账号',
               existing_server_default=sa.text('false'))
    op.alter_column('ldap_config', 'auto_select_enabled',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_comment='是否启用基于IP的自动选择',
               existing_server_default=sa.text('true'))
    op.alter_column('ldap_config', 'priority',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_comment='匹配优先级，数字越小优先级越高',
               existing_server_default=sa.text('1'))
    op.alter_column('asset_settings', 'id',
               existing_type=sa.INTEGER(),
               comment='ID',
               existing_nullable=False,
               autoincrement=True)
    # ### end Alembic commands ###
