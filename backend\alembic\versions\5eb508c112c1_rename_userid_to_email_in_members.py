"""rename_userid_to_email_in_members

Revision ID: 5eb508c112c1
Revises: 03c4f1f63bbf
Create Date: 2025-05-24 12:52:49.129736

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '5eb508c112c1'
down_revision: Union[str, None] = '03c4f1f63bbf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_asset_settings_id', table_name='asset_settings')
    op.drop_table('asset_settings')
    op.drop_index('ix_ad_sync_locks_lock_name', table_name='ad_sync_locks')
    op.drop_table('ad_sync_locks')
    op.drop_index('ix_email_members_userid', table_name='email_members')
    op.create_index(op.f('ix_email_members_email'), 'email_members', ['email'], unique=True)
    op.create_index(op.f('ix_email_members_extid'), 'email_members', ['extid'], unique=True)
    op.drop_column('email_members', 'userid')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('email_members', sa.Column('userid', sa.VARCHAR(length=50), nullable=True))
    op.drop_index(op.f('ix_email_members_extid'), table_name='email_members')
    op.drop_index(op.f('ix_email_members_email'), table_name='email_members')
    op.create_index('ix_email_members_userid', 'email_members', ['userid'], unique=1)
    op.create_table('ad_sync_locks',
    sa.Column('lock_name', sa.VARCHAR(), nullable=False),
    sa.Column('is_locked', sa.BOOLEAN(), nullable=True),
    sa.Column('locked_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('locked_by', sa.VARCHAR(), nullable=True),
    sa.PrimaryKeyConstraint('lock_name')
    )
    op.create_index('ix_ad_sync_locks_lock_name', 'ad_sync_locks', ['lock_name'], unique=False)
    op.create_table('asset_settings',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('company', sa.VARCHAR(length=100), nullable=False),
    sa.Column('asset_number_rule', sqlite.JSON(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.Column('updated_at', sa.DATETIME(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('company')
    )
    op.create_index('ix_asset_settings_id', 'asset_settings', ['id'], unique=False)
    # ### end Alembic commands ###
