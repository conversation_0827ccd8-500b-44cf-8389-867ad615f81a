<template>
  <component 
    v-if="isDevelopment && StagewiseComponent" 
    :is="StagewiseComponent" 
    :config="config" 
  />
</template>

<script setup lang="ts">
import { ref, onMounted, shallowRef } from 'vue'
import { stagewiseConfig } from '../stagewise.config'

const isDevelopment = ref(false)
const config = ref(stagewiseConfig)
const StagewiseComponent = shallowRef<any>(null) // 使用shallowRef避免响应式警告

onMounted(async () => {
  // 只在开发模式下加载
  if (import.meta.env.DEV) {
    isDevelopment.value = true
    try {
      // 动态导入stagewise工具栏
      const { StagewiseToolbar } = await import('@stagewise/toolbar-vue')
      StagewiseComponent.value = StagewiseToolbar
    } catch (error) {
      console.warn('Failed to load stagewise toolbar:', error)
    }
  }
})
</script> 