# 邮箱成员mobile字段长度错误修复

## 问题描述

在邮箱成员同步过程中出现数据库字段长度限制错误：
```
StringDataRightTruncation: value too long for type character varying(20)
```

错误发生在尝试插入mobile字段值时，具体错误数据：
- mobile值：`<EMAIL>` (24个字符)
- 数据库字段限制：`VARCHAR(20)` (20个字符)

## 根本原因

1. 腾讯企业邮箱API返回的mobile字段值有时是邮箱地址格式
2. 数据库模型中`EmailMember.mobile`字段定义为`String(20)`，只能存储20个字符
3. 当API返回的mobile值超过20个字符时，PostgreSQL抛出截断错误

## 解决方案

采用扩展数据库字段长度的方案，具体步骤：

### 1. 创建数据库迁移脚本
- 文件：`alembic/versions/bd393a92e5a7_extend_mobile_field_length_in_email_.py`
- 操作：将`email_members.mobile`字段从`VARCHAR(20)`扩展到`VARCHAR(50)`

### 2. 更新模型定义
- 文件：`app/models/email.py`
- 修改：`EmailMember.mobile = Column(String(50), ...)`

### 3. 应用数据库迁移
- 命令：`alembic upgrade head`
- 结果：成功应用迁移 `bd393a92e5a7`

## 修复结果

- ✅ 数据库字段长度从20扩展到50个字符
- ✅ 模型定义已更新
- ✅ 迁移成功应用
- ✅ 可以存储邮箱地址格式的mobile值

## 测试建议

1. 重新运行邮箱成员同步功能
2. 验证包含长mobile值的用户能够正常同步
3. 确认原有数据未受影响

## 相关文件

- `backend/app/models/email.py` - 数据库模型
- `backend/alembic/versions/bd393a92e5a7_extend_mobile_field_length_in_email_.py` - 迁移脚本
- `backend/app/api/v1/email.py` - 邮箱同步API

## 预防措施

建议在未来处理字符串字段时：
1. 根据API数据格式合理设置字段长度
2. 在同步代码中添加数据验证和长度检查
3. 考虑使用Text类型存储可变长度字符串 