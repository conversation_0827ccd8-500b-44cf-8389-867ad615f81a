# Agent安装脚本文件复制优化修复

## 问题描述

Agent安装脚本在复制文件时使用了过于宽泛的通配符命令，导致复制了不必要的系统文件。同时在实际安装时出现文件找不到的问题。

### 具体现象
1. **原始问题**：安装过程中出现复制以下文件的日志：
```
正在复制文件...
C:07409496-a423-4a3e-b620-2cfb01a9318d_HyperV-ComputeNetwork.dll
C:69fe178f-26e7-43a9-aa7d-2b616b672dde_eventlogservice.dll
C:6bea57fb-8dfb-4177-9ae8-42e8b3529933_RuntimeDeviceInstall.dll
C:@AdvancedKeySettingsNotification.png
C:@AppHelpToast.png
C:@AudioToastIcon.png
```

2. **修复后暴露的问题**：精确复制后出现文件找不到错误：
```
正在复制文件...
系统找不到指定的文件。
系统找不到指定的文件。
```

## 根本原因

### 第一层问题（已修复）
在 `backend/agent/build_installer.py` 的 `create_install_script` 函数中，安装脚本使用了：
```batch
xcopy /y /e *.* "%INSTALL_DIR%\\"
```

### 第二层问题（本次修复）
修改为精确复制后，文件路径没有使用脚本所在目录的绝对路径：
```batch
copy "TerminalAgent.exe" "%INSTALL_DIR%\\TerminalAgent.exe"
copy "default_config.json" "%INSTALL_DIR%\\default_config.json"
```

导致安装脚本运行时在错误的工作目录中查找文件。

## 解决方案

### 第一阶段修复：精确文件复制
替换通配符复制为具体文件复制。

### 第二阶段修复：路径问题解决
使用`%~dp0`获取脚本所在目录，确保文件路径正确：

```batch
:: 复制文件
echo 正在复制文件...
:: 获取当前脚本所在目录
set SCRIPT_DIR=%~dp0

:: 只复制必要的Agent文件，避免复制系统DLL和其他不必要文件
if exist "%SCRIPT_DIR%TerminalAgent.exe" (
    copy "%SCRIPT_DIR%TerminalAgent.exe" "%INSTALL_DIR%\\TerminalAgent.exe"
    echo 已复制 TerminalAgent.exe
) else (
    echo 错误：找不到 TerminalAgent.exe 文件
    pause
    exit /b 1
)

if exist "%SCRIPT_DIR%default_config.json" (
    copy "%SCRIPT_DIR%default_config.json" "%INSTALL_DIR%\\default_config.json"
    echo 已复制 default_config.json
) else (
    echo 错误：找不到 default_config.json 文件
    pause
    exit /b 1
)

if exist "%SCRIPT_DIR%README.txt" copy "%SCRIPT_DIR%README.txt" "%INSTALL_DIR%\\README.txt"
echo 文件复制完成
```

## 修复效果

1. **避免复制系统文件**：不再包含HyperV-ComputeNetwork.dll等系统文件
2. **路径问题解决**：使用脚本所在目录的绝对路径
3. **错误处理改进**：添加文件存在性检查和错误提示
4. **安装过程清晰**：每个复制步骤都有明确的成功提示
5. **故障快速定位**：文件不存在时立即停止并提示错误

## 验证方法

1. 重新构建Agent安装包
2. 检查安装包目录结构：
   ```
   TerminalAgent-1.0.1/
   ├── TerminalAgent.exe
   ├── default_config.json
   ├── install.bat
   ├── uninstall.bat
   └── README.txt
   ```
3. 运行安装脚本，确认：
   - 显示"已复制 TerminalAgent.exe"
   - 显示"已复制 default_config.json"
   - 服务注册成功
   - 服务启动成功

## 相关文件

- `backend/agent/build_installer.py` - 主要修改文件
- `backend/agent/create_installer.py` - 无需修改（已使用精确复制）

## 完成时间

2024年12月19日

## 修复状态

✅ 已完成 