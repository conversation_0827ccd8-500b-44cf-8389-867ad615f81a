"""add_inventory_tables

Revision ID: e5b079347583
Revises: 5269a5600429
Create Date: 2024-12-18 16:35:02.372621

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e5b079347583'
down_revision: Union[str, None] = '5269a5600429'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ad_config',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('server', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=False),
    sa.Column('password', sa.String(), nullable=False),
    sa.Column('search_base', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ad_config_id'), 'ad_config', ['id'], unique=False)
    op.create_table('assets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('company', sa.String(length=100), nullable=False, comment='公司'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='资产名称'),
    sa.Column('asset_number', sa.String(length=50), nullable=False, comment='资产编号'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='资产状态'),
    sa.Column('specification', sa.String(length=200), nullable=False, comment='资产规格'),
    sa.Column('purchase_date', sa.DateTime(), nullable=False, comment='入账日期'),
    sa.Column('retirement_date', sa.DateTime(), nullable=True, comment='销账日期'),
    sa.Column('custodian', sa.String(length=50), nullable=False, comment='领用人'),
    sa.Column('user', sa.String(length=50), nullable=False, comment='使用人'),
    sa.Column('inspector', sa.String(length=50), nullable=False, comment='验收人'),
    sa.Column('remarks', sa.String(length=500), nullable=True, comment='备注'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('asset_number')
    )
    op.create_index(op.f('ix_assets_id'), 'assets', ['id'], unique=False)
    op.create_table('inventory_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='盘点任务名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='任务描述'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='任务状态'),
    sa.Column('start_date', sa.DateTime(), nullable=False, comment='开始日期'),
    sa.Column('end_date', sa.DateTime(), nullable=False, comment='结束日期'),
    sa.Column('created_by', sa.String(length=50), nullable=False, comment='创建人'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_inventory_tasks_id'), 'inventory_tasks', ['id'], unique=False)
    op.create_table('organizational_units',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('dn', sa.String(), nullable=True),
    sa.Column('parent_dn', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('dn')
    )
    op.create_index(op.f('ix_organizational_units_dn'), 'organizational_units', ['dn'], unique=True)
    # 添加外键约束（在创建唯一约束后）
    op.create_foreign_key(None, 'organizational_units', 'organizational_units', ['parent_dn'], ['dn'])
    op.create_index(op.f('ix_organizational_units_id'), 'organizational_units', ['id'], unique=False)
    op.create_index(op.f('ix_organizational_units_name'), 'organizational_units', ['name'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('ad_users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('dn', sa.String(), nullable=True),
    sa.Column('hashed_password', sa.String(), nullable=True),
    sa.Column('enabled', sa.Boolean(), nullable=True),
    sa.Column('ou_dn', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['ou_dn'], ['organizational_units.dn'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('dn')
    )
    op.create_index(op.f('ix_ad_users_id'), 'ad_users', ['id'], unique=False)
    op.create_index(op.f('ix_ad_users_username'), 'ad_users', ['username'], unique=True)
    op.create_table('inventory_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False, comment='盘点任务ID'),
    sa.Column('asset_id', sa.Integer(), nullable=False, comment='资产ID'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='盘点状态'),
    sa.Column('new_custodian', sa.String(length=50), nullable=True, comment='新领用人'),
    sa.Column('new_user', sa.String(length=50), nullable=True, comment='新使用人'),
    sa.Column('remarks', sa.String(length=500), nullable=True, comment='备注'),
    sa.Column('checked_by', sa.String(length=50), nullable=True, comment='盘点人'),
    sa.Column('checked_at', sa.DateTime(), nullable=True, comment='盘点时间'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], ),
    sa.ForeignKeyConstraint(['task_id'], ['inventory_tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_inventory_records_id'), 'inventory_records', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_inventory_records_id'), table_name='inventory_records')
    op.drop_table('inventory_records')
    op.drop_index(op.f('ix_ad_users_username'), table_name='ad_users')
    op.drop_index(op.f('ix_ad_users_id'), table_name='ad_users')
    op.drop_table('ad_users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_organizational_units_name'), table_name='organizational_units')
    op.drop_index(op.f('ix_organizational_units_id'), table_name='organizational_units')
    op.drop_index(op.f('ix_organizational_units_dn'), table_name='organizational_units')
    op.drop_table('organizational_units')
    op.drop_index(op.f('ix_inventory_tasks_id'), table_name='inventory_tasks')
    op.drop_table('inventory_tasks')
    op.drop_index(op.f('ix_assets_id'), table_name='assets')
    op.drop_table('assets')
    op.drop_index(op.f('ix_ad_config_id'), table_name='ad_config')
    op.drop_table('ad_config')
    # ### end Alembic commands ###
