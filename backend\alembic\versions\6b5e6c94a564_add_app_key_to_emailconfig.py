"""add_app_key_to_emailconfig

Revision ID: 6b5e6c94a564
Revises: 7da725d812da
Create Date: 2025-05-26 16:11:46.257700

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6b5e6c94a564'
down_revision: Union[str, None] = '7da725d812da'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加app_key字段
    op.add_column('email_configs', sa.Column('app_key', sa.String(50), nullable=True, comment="应用标识"))
    
    # 更新现有记录，根据app_name设置app_key
    conn = op.get_bind()
    
    # 预设的应用列表及对应的key
    app_keys = {
        "通讯录管理": "contact_manager",
        "功能设置": "function_settings",
        "单点登录": "sso",
        "新邮件提醒": "mail_notification",
        "日志查询": "log_query"
    }
    
    # 更新现有记录
    for app_name, app_key in app_keys.items():
        conn.execute(
            sa.text(
                f"UPDATE email_configs SET app_key = '{app_key}' WHERE app_name = '{app_name}'"
            )
        )


def downgrade() -> None:
    # 删除app_key字段
    op.drop_column('email_configs', 'app_key')
