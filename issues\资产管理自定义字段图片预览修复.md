# 资产管理自定义字段图片预览修复

## 问题描述
资产管理模块中，自定义字段的图片类型在上传完图片后无法进行预览的问题。
- 图片上传成功后界面显示默认的图片样式，而不是预览的图片
- 点击预览出现默认的黑色图片
- 浏览器控制台没有错误信息

## 问题分析
经过代码分析，发现问题的根源：

1. **静态文件路径不匹配**：
   - 后端上传API返回的URL是 `/uploads/custom_fields/{filename}`
   - 但是后端静态文件服务只挂载了 `/downloads` 路径映射到 `uploads` 目录
   - 前端无法正确访问 `/uploads/` 路径下的图片

2. **前端URL拼接配置混乱**：
   - 部分组件使用 `import.meta.env.VITE_API_BASE_URL`（未定义的环境变量）
   - 部分组件使用 `@/config/api` 配置文件
   - 导致URL拼接不一致

## 解决方案

### 1. 修复后端静态文件挂载
**文件**: `backend/app/main.py`

```python
# 修改前：只有/downloads路径
app.mount("/downloads", StaticFiles(directory="uploads"), name="downloads")

# 修改后：同时支持/downloads和/uploads路径
app.mount("/downloads", StaticFiles(directory="uploads"), name="downloads")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
```

### 2. 统一前端URL处理逻辑
**文件**: `frontend/src/components/DynamicForm/DynamicField.vue`

```typescript
// 修改前：使用未定义的环境变量
return import.meta.env.VITE_API_BASE_URL + url

// 修改后：使用配置文件中的API_BASE_URL
import { API_BASE_URL } from '@/config/api'

const getFullUrl = (url: string): string => {
  if (url.startsWith('http')) {
    return url
  }
  // 使用配置文件中的API_BASE_URL，去掉'/api/v1'后缀
  const baseUrl = API_BASE_URL.replace('/api/v1', '')
  return baseUrl + url
}
```

**文件**: `frontend/src/mobile/components/MobileFileUploader.vue`

```typescript
// 修改前：使用未定义的环境变量
return import.meta.env.VITE_API_BASE_URL + file.url

// 修改后：使用配置文件
import { API_BASE_URL } from '@/config/api'

if (file.url.startsWith('/')) {
  const baseUrl = API_BASE_URL.replace('/api/v1', '')
  return baseUrl + file.url
}
```

### 3. 修复API工具函数
**文件**: `frontend/src/api/custom_field.ts`

```typescript
// 修改前：使用未定义的环境变量
return import.meta.env.VITE_API_BASE_URL + fileUrl

// 修改后：使用配置文件
const { API_BASE_URL } = require('@/config/api')
const baseUrl = API_BASE_URL.replace('/api/v1', '')
return baseUrl + fileUrl
```

## 修复效果

修复后，图片上传和预览功能应该正常工作：

1. **图片上传成功后**：
   - 显示实际的图片预览缩略图
   - 点击可以看到高清大图

2. **URL访问正确**：
   - 后端API返回：`/uploads/custom_fields/xxx.jpg`
   - 前端拼接为：`http://localhost:8000/uploads/custom_fields/xxx.jpg`
   - 后端静态文件服务可以正确响应

3. **桌面端和移动端**：
   - 统一使用相同的URL拼接逻辑
   - 保证兼容性

## 测试验证

### 1. 功能测试
- [ ] 资产管理 -> 自定义字段 -> 图片上传
- [ ] 上传后查看缩略图预览
- [ ] 点击查看大图功能
- [ ] 移动端图片上传和预览

### 2. 技术验证
- [ ] 检查网络请求中的图片URL
- [ ] 验证静态文件服务响应状态
- [ ] 确认不同浏览器兼容性

## 相关文件清单

### 后端文件
- `backend/app/main.py` - 静态文件挂载配置
- `backend/app/api/v1/custom_fields.py` - 图片上传API

### 前端文件
- `frontend/src/components/DynamicForm/DynamicField.vue` - 桌面端文件上传组件
- `frontend/src/mobile/components/MobileFileUploader.vue` - 移动端文件上传组件
- `frontend/src/api/custom_field.ts` - 自定义字段API
- `frontend/src/config/api.ts` - API配置文件

## 注意事项

1. **开发环境配置**：确保 `frontend/src/config/api.ts` 中的服务器IP配置正确
2. **生产环境部署**：需要确保静态文件服务正确配置
3. **缓存清理**：修复后可能需要清理浏览器缓存 