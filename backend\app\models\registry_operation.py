"""
注册表操作记录数据模型
"""

from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import Integer, String, Text, Boolean, DateTime, JSON
from datetime import datetime
from app.database import Base


class RegistryOperation(Base):
    """注册表操作记录表"""
    __tablename__ = "registry_operations"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # 操作基本信息
    command_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment="命令ID")
    terminal_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment="终端ID")
    operation_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True, comment="操作类型")
    
    # 注册表路径信息
    root_key: Mapped[str] = mapped_column(String(50), nullable=False, comment="根键")
    key_path: Mapped[str] = mapped_column(Text, nullable=False, comment="键路径")
    value_name: Mapped[str] = mapped_column(String(255), nullable=True, comment="值名称")
    
    # 操作数据
    value_type: Mapped[str] = mapped_column(String(50), nullable=True, comment="值类型")
    old_value_data: Mapped[str] = mapped_column(Text, nullable=True, comment="原始值数据")
    new_value_data: Mapped[str] = mapped_column(Text, nullable=True, comment="新值数据")
    
    # 操作结果
    success: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, comment="操作是否成功")
    error_message: Mapped[str] = mapped_column(Text, nullable=True, comment="错误信息")
    
    # 备份信息
    backup_id: Mapped[str] = mapped_column(String(255), nullable=True, index=True, comment="备份ID")
    backup_reason: Mapped[str] = mapped_column(String(500), nullable=True, comment="备份原因")
    
    # 操作上下文
    operation_data: Mapped[dict] = mapped_column(JSON, nullable=True, comment="完整操作数据")
    execution_duration: Mapped[int] = mapped_column(Integer, nullable=True, comment="执行耗时(毫秒)")
    
    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    def __repr__(self):
        return f"<RegistryOperation(id={self.id}, operation_type='{self.operation_type}', root_key='{self.root_key}', key_path='{self.key_path}')>"


class RegistryBackup(Base):
    """注册表备份记录表"""
    __tablename__ = "registry_backups"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # 备份标识
    backup_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True, comment="备份ID")
    backup_name: Mapped[str] = mapped_column(String(500), nullable=False, comment="备份名称")
    
    # 终端信息
    terminal_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment="终端ID")
    
    # 备份范围
    root_key: Mapped[str] = mapped_column(String(50), nullable=False, comment="根键")
    key_path: Mapped[str] = mapped_column(Text, nullable=False, comment="键路径")
    
    # 备份文件信息
    file_path: Mapped[str] = mapped_column(Text, nullable=False, comment="备份文件路径")
    file_size: Mapped[int] = mapped_column(Integer, nullable=True, comment="文件大小(字节)")
    file_hash: Mapped[str] = mapped_column(String(255), nullable=True, comment="文件哈希值")
    
    # 备份元信息
    reason: Mapped[str] = mapped_column(String(500), nullable=True, comment="备份原因")
    description: Mapped[str] = mapped_column(Text, nullable=True, comment="备份描述")
    tags: Mapped[str] = mapped_column(String(500), nullable=True, comment="标签(逗号分隔)")
    
    # 状态信息
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="active", index=True, comment="备份状态(active/deleted/expired)")
    is_verified: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, comment="是否已验证")
    
    # 关联操作
    related_operation_id: Mapped[int] = mapped_column(Integer, nullable=True, comment="关联的操作记录ID")
    
    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    expire_at: Mapped[datetime] = mapped_column(DateTime, nullable=True, comment="过期时间")

    def __repr__(self):
        return f"<RegistryBackup(id={self.id}, backup_id='{self.backup_id}', root_key='{self.root_key}', key_path='{self.key_path}')>"


class RegistrySearchLog(Base):
    """注册表搜索日志表"""
    __tablename__ = "registry_search_logs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    
    # 搜索标识
    search_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment="搜索ID")
    terminal_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True, comment="终端ID")
    
    # 搜索参数
    root_key: Mapped[str] = mapped_column(String(50), nullable=False, comment="搜索根键")
    start_path: Mapped[str] = mapped_column(Text, nullable=False, comment="开始路径")
    search_pattern: Mapped[str] = mapped_column(String(500), nullable=False, comment="搜索模式")
    search_keys: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True, comment="是否搜索键名")
    search_values: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True, comment="是否搜索值名")
    search_data: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True, comment="是否搜索值数据")
    max_depth: Mapped[int] = mapped_column(Integer, nullable=False, default=10, comment="最大搜索深度")
    max_results: Mapped[int] = mapped_column(Integer, nullable=False, default=100, comment="最大结果数")
    
    # 搜索结果
    total_results: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="搜索结果总数")
    results_data: Mapped[dict] = mapped_column(JSON, nullable=True, comment="搜索结果数据")
    
    # 执行信息
    success: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, comment="搜索是否成功")
    error_message: Mapped[str] = mapped_column(Text, nullable=True, comment="错误信息")
    execution_duration: Mapped[int] = mapped_column(Integer, nullable=True, comment="执行耗时(毫秒)")
    
    # 时间信息
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, comment="创建时间")

    def __repr__(self):
        return f"<RegistrySearchLog(id={self.id}, search_id='{self.search_id}', pattern='{self.search_pattern}', results={self.total_results})>" 