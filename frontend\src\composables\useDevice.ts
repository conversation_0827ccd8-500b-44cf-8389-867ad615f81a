import { ref, computed, onMounted, onUnmounted } from 'vue'

// 全局单例状态
let deviceInstance: ReturnType<typeof createDeviceDetection> | null = null

// 创建设备检测实例
function createDeviceDetection() {
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)
  
  // 基于屏幕宽度的简单设备类型判断
  const isMobile = computed(() => {
    return screenWidth.value <= 768
  })
  
  const isTablet = computed(() => {
    return screenWidth.value > 768 && screenWidth.value <= 1024
  })
  
  const isDesktop = computed(() => {
    return screenWidth.value > 1024
  })
  
  const updateSize = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
  }
  
  // 初始化标志
  let isInitialized = false
  
  const initialize = () => {
    if (isInitialized) return
    
    window.addEventListener('resize', updateSize)
    isInitialized = true
  }
  
  const cleanup = () => {
    if (isInitialized) {
      window.removeEventListener('resize', updateSize)
      isInitialized = false
    }
  }
  
  return {
    screenWidth,
    screenHeight,
    isMobile,
    isTablet,
    isDesktop,
    initialize,
    cleanup
  }
}

export const useDevice = () => {
  // 单例模式：只创建一次设备检测实例
  if (!deviceInstance) {
    deviceInstance = createDeviceDetection()
    // 立即初始化
    deviceInstance.initialize()
  }
  
  // 在组件挂载时确保已初始化
  onMounted(() => {
    deviceInstance!.initialize()
  })
  
  onUnmounted(() => {
    // 保持全局状态，不清理
  })
  
  return {
    screenWidth: deviceInstance.screenWidth,
    screenHeight: deviceInstance.screenHeight,
    isMobile: deviceInstance.isMobile,
    isTablet: deviceInstance.isTablet,
    isDesktop: deviceInstance.isDesktop
  }
} 