<template>
  <div class="email-management">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Message /></el-icon>
        <h2 class="page-title">企业邮箱管理</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>邮箱管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 统计信息卡片 -->
    <el-card class="stats-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><DataBoard /></el-icon>
            统计信息
          </span>
          <el-button type="primary" size="small" @click="loadStats" :loading="statsLoading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon departments">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.departments }}</div>
              <div class="stat-label">部门数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon members">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.members }}</div>
              <div class="stat-label">成员数量</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon groups">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.groups }}</div>
              <div class="stat-label">邮件群组</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-icon tags">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.tags }}</div>
              <div class="stat-label">标签数量</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 功能模块管理 -->
    <el-card class="management-card" shadow="hover" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span class="header-title">
            <el-icon><Grid /></el-icon>
            功能模块
          </span>
        </div>
      </template>
      
      <div class="management-cards">
        <el-row :gutter="20">
          <el-col :span="12">
            <Authority permission="email:config:view">
              <el-card class="module-card" shadow="hover" @click="navigateTo('/email/config')">
                <div class="card-content">
                  <el-icon class="card-icon config"><Setting /></el-icon>
                  <h3 class="card-title">邮箱配置</h3>
                  <p class="card-description">配置企业邮箱API连接信息和基础设置</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain>
                      <el-icon><Right /></el-icon>
                      进入配置
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>
          
          <el-col :span="12">
            <Authority permission="email:department:view">
              <el-card class="module-card" shadow="hover" @click="navigateTo('/email/departments-members')">
                <div class="card-content">
                  <el-icon class="card-icon departments"><OfficeBuilding /></el-icon>
                  <h3 class="card-title">部门与成员管理</h3>
                  <p class="card-description">管理企业邮箱部门结构和成员账号信息</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain>
                      <el-icon><Right /></el-icon>
                      立即管理
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <Authority permission="email:group:view">
              <el-card class="module-card" shadow="hover" @click="navigateTo('/email/groups')">
                <div class="card-content">
                  <el-icon class="card-icon groups"><User /></el-icon>
                  <h3 class="card-title">邮件群组</h3>
                  <p class="card-description">创建和管理企业内部邮件群组</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain>
                      <el-icon><Right /></el-icon>
                      管理群组
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>
          
          <el-col :span="12">
            <Authority permission="email:tag:view">
              <el-card class="module-card" shadow="hover" @click="navigateTo('/email/tags')">
                <div class="card-content">
                  <el-icon class="card-icon tags"><Collection /></el-icon>
                  <h3 class="card-title">标签管理</h3>
                  <p class="card-description">管理企业邮箱标签分类和标识</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain>
                      <el-icon><Right /></el-icon>
                      管理标签
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="12">
            <Authority permission="email:member:edit">
              <el-card class="module-card" shadow="hover" @click="handlePersonnelSyncClick">
                <div class="card-content">
                  <el-icon class="card-icon personnel-sync"><Connection /></el-icon>
                  <h3 class="card-title">同步管理</h3>
                  <p class="card-description">数据同步、人员信息同步和邮箱账号管理</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain @click.stop="handlePersonnelSyncClick">
                      <el-icon><Right /></el-icon>
                      管理同步
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>

          <el-col :span="12">
            <Authority permission="email:request:view">
              <el-card class="module-card" shadow="hover" @click="navigateTo('/email/creation-requests')">
                <div class="card-content">
                  <el-icon class="card-icon requests"><DocumentChecked /></el-icon>
                  <h3 class="card-title">邮箱申请管理</h3>
                  <p class="card-description">审批和管理邮箱创建申请</p>
                  <div class="card-footer">
                    <el-button type="primary" size="small" plain>
                      <el-icon><Right /></el-icon>
                      管理申请
                    </el-button>
                  </div>
                </div>
              </el-card>
            </Authority>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">


        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Message,
  Setting,
  OfficeBuilding,
  UserFilled,
  User,
  Collection,
  Refresh,
  DataBoard,
  Grid,
  Right,
  Connection,
  DocumentChecked
} from '@element-plus/icons-vue'
import { getDepartmentTree } from '@/api/email/department'
import { getMembers } from '@/api/email/member'
import { getGroups } from '@/api/email/group'
import { getTags } from '@/api/email/tag'
import Authority from '@/components/Authority/index.vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const statsLoading = ref(false)
const userStore = useUserStore()

const stats = ref({
  departments: 0,
  members: 0,
  groups: 0,
  tags: 0
})

const navigateTo = (path: string) => {
  router.push(path)
}

const loadStats = async () => {
  try {
    statsLoading.value = true
    // 加载统计数据
    const [deptRes, memberRes, groupRes, tagRes] = await Promise.all([
      getDepartmentTree(),
      getMembers({ page: 1, size: 1 }),
      getGroups({ page: 1, size: 1 }),
      getTags({ page: 1, size: 1 })
    ])
    
    // 计算部门数量
    const countTreeNodes = (tree: any[]): number => {
      let count = 0
      for (const node of tree) {
        count++
        if (node.children && node.children.length > 0) {
          count += countTreeNodes(node.children)
        }
      }
      return count
    }
    
    stats.value = {
      departments: deptRes.data?.tree ? countTreeNodes(deptRes.data.tree) : 0,
      members: memberRes.data?.total || 0,
      groups: groupRes.data?.total || 0,
      tags: tagRes.data?.total || 0
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.warning('加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

const handlePersonnelSyncClick = () => {
  router.push('/email/sync')
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.email-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 10px;
  color: var(--el-color-primary);
}

.page-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

/* 统计卡片样式 */
.stats-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 700;
  color: #303133;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  background: #ffffff;
  transition: all 0.3s ease;
  gap: 18px;
  border: 1px solid #e4e7ed;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:hover {
  background: #f8fafe;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: #409eff;
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 52px;
  height: 52px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon.departments {
  background: linear-gradient(135deg, #5b9bd5, #409eff);
}

.stat-icon.members {
  background: linear-gradient(135deg, #70ad47, #67c23a);
}

.stat-icon.groups {
  background: linear-gradient(135deg, #ffc000, #e6a23c);
}

.stat-icon.tags {
  background: linear-gradient(135deg, #c65911, #f56c6c);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 功能模块卡片样式 */
.management-card {
  border-radius: 8px;
}

.management-cards {
  margin-top: 16px;
}

.module-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 240px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  position: relative;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.module-card:hover::before {
  opacity: 1;
}

.card-content {
  text-align: center;
  padding: 20px 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #ffffff;
}

.card-icon {
  font-size: 42px;
  margin-bottom: 12px;
  padding: 10px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: inline-block;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.card-icon.config {
  color: #409eff;
  background: linear-gradient(135deg, #e3f2fd, #ffffff);
}

.card-icon.departments {
  color: #67c23a;
  background: linear-gradient(135deg, #e8f5e8, #ffffff);
}

.card-icon.groups {
  color: #e6a23c;
  background: linear-gradient(135deg, #fff8e1, #ffffff);
}

.card-icon.tags {
  color: #f56c6c;
  background: linear-gradient(135deg, #ffebee, #ffffff);
}

.card-icon.sync {
  color: #909399;
  background: linear-gradient(135deg, #f5f5f5, #ffffff);
}

.card-icon.personnel-sync {
  color: #8e44ad;
  background: linear-gradient(135deg, #f3e5f5, #ffffff);
}

.card-icon.requests {
  color: #17a2b8;
  background: linear-gradient(135deg, #e0f7fa, #ffffff);
}

.card-title {
  font-size: 16px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 8px 0;
  letter-spacing: 0.5px;
}

.card-description {
  font-size: 14px;
  color: #666666;
  margin: 0 0 16px 0;
  line-height: 1.5;
  flex: 1;
  min-height: 42px;
}

.card-footer {
  display: flex;
  justify-content: center;
}

.card-footer .el-button {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 160px;
  border: 2px solid #409eff;
  background: #409eff;
  color: #ffffff;
  font-size: 14px;
  padding: 8px 16px;
}

.card-footer .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
  background: #337ecc;
  border-color: #337ecc;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .email-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .management-cards .el-col {
    margin-bottom: 20px;
  }
}

@media (max-width: 576px) {
  .module-card {
    height: auto;
    min-height: 240px;
  }
  
  .card-content {
    padding: 16px 12px;
  }
  
  .card-footer .el-button {
    max-width: none;
    width: 100%;
  }
}
</style>